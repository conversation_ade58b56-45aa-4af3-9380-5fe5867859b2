/**
 * WebOTR Message Interceptor
 * 
 * Provides transparent message encryption/decryption:
 * - Intercepts outgoing messages for encryption
 * - Intercepts incoming messages for decryption
 * - Platform-agnostic message handling
 * - Real-time encryption status indicators
 */

class MessageInterceptor {
  constructor(platformDetector) {
    this.platformDetector = platformDetector;
    this.platform = null;
    this.config = null;
    this.isActive = false;
    
    this.state = {
      initialized: false,
      intercepting: false,
      messagesProcessed: 0,
      encryptionEnabled: true
    };
    
    this.messageQueue = [];
    this.processingQueue = false;
    this.observers = [];
    
    // Message processing metrics
    this.metrics = {
      encryptionTimes: [],
      decryptionTimes: [],
      totalMessages: 0,
      encryptedMessages: 0
    };
  }

  /**
   * Initialize message interceptor
   */
  async initialize() {
    try {
      // Wait for platform detection
      if (!this.platformDetector.state.ready) {
        await this.waitForPlatformReady();
      }
      
      this.platform = this.platformDetector.platform;
      this.config = this.platformDetector.config;
      
      // Set up message interception based on platform
      await this.setupMessageInterception();
      
      // Set up message queue processing
      this.startMessageQueueProcessing();
      
      // Set up real-time message monitoring
      this.setupMessageMonitoring();
      
      this.state.initialized = true;
      this.isActive = true;
      
      console.log(`WebOTR: Message interceptor initialized for ${this.platform}`);
      
      return {
        platform: this.platform,
        initialized: this.state.initialized,
        intercepting: this.state.intercepting
      };
      
    } catch (error) {
      console.error('Message interceptor initialization failed:', error);
      throw error;
    }
  }

  /**
   * Wait for platform to be ready
   */
  async waitForPlatformReady() {
    return new Promise((resolve) => {
      const checkReady = () => {
        if (this.platformDetector.state.ready) {
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
    });
  }

  /**
   * Setup platform-specific message interception
   */
  async setupMessageInterception() {
    switch (this.platform) {
      case 'discord':
        await this.setupDiscordInterception();
        break;
      case 'slack':
        await this.setupSlackInterception();
        break;
      case 'teams':
        await this.setupTeamsInterception();
        break;
      case 'whatsapp':
        await this.setupWhatsAppInterception();
        break;
      case 'telegram':
        await this.setupTelegramInterception();
        break;
      case 'element':
        await this.setupElementInterception();
        break;
      default:
        throw new Error(`Unsupported platform: ${this.platform}`);
    }
    
    this.state.intercepting = true;
  }

  /**
   * Setup Discord message interception
   */
  async setupDiscordInterception() {
    // Intercept message sending
    this.interceptMessageSending('discord');
    
    // Monitor incoming messages
    this.monitorIncomingMessages('discord');
    
    // Hook into Discord's message API
    this.hookDiscordAPI();
  }

  /**
   * Setup Slack message interception
   */
  async setupSlackInterception() {
    // Intercept message sending
    this.interceptMessageSending('slack');
    
    // Monitor incoming messages
    this.monitorIncomingMessages('slack');
    
    // Hook into Slack's message API
    this.hookSlackAPI();
  }

  /**
   * Setup Teams message interception
   */
  async setupTeamsInterception() {
    // Intercept message sending
    this.interceptMessageSending('teams');
    
    // Monitor incoming messages
    this.monitorIncomingMessages('teams');
    
    // Hook into Teams' message API
    this.hookTeamsAPI();
  }

  /**
   * Setup WhatsApp message interception
   */
  async setupWhatsAppInterception() {
    // Intercept message sending
    this.interceptMessageSending('whatsapp');
    
    // Monitor incoming messages
    this.monitorIncomingMessages('whatsapp');
    
    // Hook into WhatsApp's message API
    this.hookWhatsAppAPI();
  }

  /**
   * Setup Telegram message interception
   */
  async setupTelegramInterception() {
    // Intercept message sending
    this.interceptMessageSending('telegram');
    
    // Monitor incoming messages
    this.monitorIncomingMessages('telegram');
    
    // Hook into Telegram's message API
    this.hookTelegramAPI();
  }

  /**
   * Setup Element message interception
   */
  async setupElementInterception() {
    // Intercept message sending
    this.interceptMessageSending('element');
    
    // Monitor incoming messages
    this.monitorIncomingMessages('element');
    
    // Hook into Element's message API
    this.hookElementAPI();
  }

  /**
   * Generic message sending interception
   */
  interceptMessageSending(platform) {
    const messageInput = this.platformDetector.getElement('messageInput');
    const sendButton = this.platformDetector.getElement('sendButton');
    
    if (!messageInput) {
      console.warn(`WebOTR: Message input not found for ${platform}`);
      return;
    }
    
    // Intercept Enter key and send button clicks
    this.interceptKeyboardEvents(messageInput);
    
    if (sendButton) {
      this.interceptSendButton(sendButton);
    }
    
    // Monitor input changes for real-time encryption preview
    this.monitorInputChanges(messageInput);
  }

  /**
   * Intercept keyboard events for message sending
   */
  interceptKeyboardEvents(messageInput) {
    const keydownHandler = async (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        // Check if encryption is enabled
        if (this.state.encryptionEnabled) {
          event.preventDefault();
          event.stopPropagation();
          
          const messageText = this.extractMessageText(messageInput);
          if (messageText.trim()) {
            await this.processOutgoingMessage(messageText, messageInput);
          }
        }
      }
    };
    
    messageInput.addEventListener('keydown', keydownHandler, true);
    
    // Store reference for cleanup
    this.observers.push({
      element: messageInput,
      event: 'keydown',
      handler: keydownHandler
    });
  }

  /**
   * Intercept send button clicks
   */
  interceptSendButton(sendButton) {
    const clickHandler = async (event) => {
      if (this.state.encryptionEnabled) {
        event.preventDefault();
        event.stopPropagation();
        
        const messageInput = this.platformDetector.getElement('messageInput');
        const messageText = this.extractMessageText(messageInput);
        
        if (messageText.trim()) {
          await this.processOutgoingMessage(messageText, messageInput);
        }
      }
    };
    
    sendButton.addEventListener('click', clickHandler, true);
    
    this.observers.push({
      element: sendButton,
      event: 'click',
      handler: clickHandler
    });
  }

  /**
   * Monitor input changes for real-time encryption preview
   */
  monitorInputChanges(messageInput) {
    const inputHandler = (event) => {
      if (this.state.encryptionEnabled) {
        const messageText = this.extractMessageText(messageInput);
        this.showEncryptionPreview(messageText);
      }
    };
    
    messageInput.addEventListener('input', inputHandler);
    
    this.observers.push({
      element: messageInput,
      event: 'input',
      handler: inputHandler
    });
  }

  /**
   * Monitor incoming messages for decryption
   */
  monitorIncomingMessages(platform) {
    const messageContainer = this.platformDetector.getElement('messageContainer');
    
    if (!messageContainer) {
      console.warn(`WebOTR: Message container not found for ${platform}`);
      return;
    }
    
    // Monitor for new messages
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            this.checkForEncryptedMessages(node);
          }
        });
      });
    });
    
    observer.observe(messageContainer, {
      childList: true,
      subtree: true
    });
    
    this.observers.push({ observer, type: 'mutation' });
    
    // Check existing messages
    this.checkExistingMessages(messageContainer);
  }

  /**
   * Extract message text from input element
   */
  extractMessageText(messageInput) {
    // Handle different input types
    if (messageInput.contentEditable === 'true') {
      return messageInput.textContent || messageInput.innerText || '';
    } else {
      return messageInput.value || '';
    }
  }

  /**
   * Process outgoing message for encryption
   */
  async processOutgoingMessage(messageText, messageInput) {
    try {
      const startTime = performance.now();
      
      // Add to processing queue
      const messageData = {
        id: this.generateMessageId(),
        text: messageText,
        timestamp: Date.now(),
        direction: 'outgoing',
        platform: this.platform
      };
      
      this.messageQueue.push(messageData);
      
      // Clear input immediately for better UX
      this.clearMessageInput(messageInput);
      
      // Show processing indicator
      this.showProcessingIndicator(messageData.id);
      
      // Process encryption (will be handled by queue processor)
      console.log(`WebOTR: Queued outgoing message for encryption: ${messageData.id}`);
      
      const encryptionTime = performance.now() - startTime;
      this.metrics.encryptionTimes.push(encryptionTime);
      
    } catch (error) {
      console.error('Failed to process outgoing message:', error);
      this.showErrorIndicator('Failed to encrypt message');
    }
  }

  /**
   * Check for encrypted messages in incoming content
   */
  checkForEncryptedMessages(element) {
    // Look for WebOTR encrypted message markers
    const encryptedMarkers = [
      '-----BEGIN WEBOTTR MESSAGE-----',
      'WEBOTTR:',
      '🔒 WebOTR:'
    ];
    
    const textContent = element.textContent || element.innerText || '';
    
    for (const marker of encryptedMarkers) {
      if (textContent.includes(marker)) {
        this.processIncomingEncryptedMessage(element, textContent);
        break;
      }
    }
  }

  /**
   * Check existing messages for encrypted content
   */
  checkExistingMessages(container) {
    const messageElements = this.platformDetector.getElements('messageItem');
    
    messageElements.forEach(element => {
      this.checkForEncryptedMessages(element);
    });
  }

  /**
   * Process incoming encrypted message for decryption
   */
  async processIncomingEncryptedMessage(element, encryptedText) {
    try {
      const startTime = performance.now();
      
      const messageData = {
        id: this.generateMessageId(),
        text: encryptedText,
        element: element,
        timestamp: Date.now(),
        direction: 'incoming',
        platform: this.platform
      };
      
      this.messageQueue.push(messageData);
      
      // Show decryption indicator
      this.showDecryptionIndicator(element);
      
      console.log(`WebOTR: Queued incoming message for decryption: ${messageData.id}`);
      
      const decryptionTime = performance.now() - startTime;
      this.metrics.decryptionTimes.push(decryptionTime);
      
    } catch (error) {
      console.error('Failed to process incoming encrypted message:', error);
      this.showDecryptionError(element);
    }
  }

  /**
   * Start message queue processing
   */
  startMessageQueueProcessing() {
    if (this.processingQueue) {
      return;
    }
    
    this.processingQueue = true;
    
    const processQueue = async () => {
      while (this.messageQueue.length > 0 && this.isActive) {
        const message = this.messageQueue.shift();
        
        try {
          if (message.direction === 'outgoing') {
            await this.encryptAndSendMessage(message);
          } else {
            await this.decryptAndDisplayMessage(message);
          }
          
          this.state.messagesProcessed++;
          this.metrics.totalMessages++;
          
        } catch (error) {
          console.error(`Failed to process message ${message.id}:`, error);
        }
      }
      
      // Continue processing
      if (this.isActive) {
        setTimeout(processQueue, 100);
      }
    };
    
    processQueue();
  }

  /**
   * Encrypt and send message
   */
  async encryptAndSendMessage(messageData) {
    // This will integrate with the WebOTR encryption system
    // For now, simulate encryption
    const encryptedMessage = `🔒 WebOTR: ${btoa(messageData.text)}`;
    
    // Send the encrypted message through the platform
    await this.sendMessageToPlatform(encryptedMessage);
    
    this.metrics.encryptedMessages++;
    this.hideProcessingIndicator(messageData.id);
    
    console.log(`WebOTR: Encrypted and sent message: ${messageData.id}`);
  }

  /**
   * Decrypt and display message
   */
  async decryptAndDisplayMessage(messageData) {
    try {
      // This will integrate with the WebOTR decryption system
      // For now, simulate decryption
      const encryptedContent = messageData.text.replace('🔒 WebOTR: ', '');
      const decryptedMessage = atob(encryptedContent);
      
      // Replace encrypted content with decrypted content
      this.replaceMessageContent(messageData.element, decryptedMessage);
      
      this.hideDecryptionIndicator(messageData.element);
      
      console.log(`WebOTR: Decrypted message: ${messageData.id}`);
      
    } catch (error) {
      console.error(`Failed to decrypt message ${messageData.id}:`, error);
      this.showDecryptionError(messageData.element);
    }
  }

  /**
   * Send message through platform's native API
   */
  async sendMessageToPlatform(messageText) {
    const messageInput = this.platformDetector.getElement('messageInput');
    
    if (!messageInput) {
      throw new Error('Message input not found');
    }
    
    // Set the encrypted message in the input
    this.setMessageInput(messageInput, messageText);
    
    // Trigger the platform's send mechanism
    await this.triggerPlatformSend();
  }

  /**
   * Set message in input element
   */
  setMessageInput(messageInput, text) {
    if (messageInput.contentEditable === 'true') {
      messageInput.textContent = text;
      
      // Trigger input event
      const event = new Event('input', { bubbles: true });
      messageInput.dispatchEvent(event);
    } else {
      messageInput.value = text;
      
      // Trigger input and change events
      const inputEvent = new Event('input', { bubbles: true });
      const changeEvent = new Event('change', { bubbles: true });
      messageInput.dispatchEvent(inputEvent);
      messageInput.dispatchEvent(changeEvent);
    }
  }

  /**
   * Clear message input
   */
  clearMessageInput(messageInput) {
    this.setMessageInput(messageInput, '');
  }

  /**
   * Trigger platform's native send mechanism
   */
  async triggerPlatformSend() {
    const sendButton = this.platformDetector.getElement('sendButton');
    
    if (sendButton) {
      // Temporarily disable our interception
      this.state.encryptionEnabled = false;
      
      // Click the send button
      sendButton.click();
      
      // Re-enable interception after a short delay
      setTimeout(() => {
        this.state.encryptionEnabled = true;
      }, 100);
    } else {
      // Fallback: trigger Enter key
      const messageInput = this.platformDetector.getElement('messageInput');
      if (messageInput) {
        this.state.encryptionEnabled = false;
        
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          bubbles: true
        });
        messageInput.dispatchEvent(enterEvent);
        
        setTimeout(() => {
          this.state.encryptionEnabled = true;
        }, 100);
      }
    }
  }

  /**
   * Replace message content with decrypted text
   */
  replaceMessageContent(element, decryptedText) {
    const messageContentElement = element.querySelector(this.config.selectors.messageContent) || element;
    
    if (messageContentElement) {
      // Create decrypted message container
      const decryptedContainer = document.createElement('div');
      decryptedContainer.className = 'webottr-decrypted-message';
      decryptedContainer.textContent = decryptedText;
      
      // Add WebOTR indicator
      const indicator = document.createElement('span');
      indicator.className = 'webottr-decrypted-indicator';
      indicator.textContent = '🔓';
      indicator.title = 'Decrypted by WebOTR';
      
      decryptedContainer.appendChild(indicator);
      
      // Replace content
      messageContentElement.innerHTML = '';
      messageContentElement.appendChild(decryptedContainer);
    }
  }

  /**
   * Show encryption preview
   */
  showEncryptionPreview(messageText) {
    // This will show a preview of how the message will be encrypted
    // Implementation depends on UI integration
  }

  /**
   * Show processing indicator
   */
  showProcessingIndicator(messageId) {
    // Show that message is being encrypted
    console.log(`WebOTR: Processing message ${messageId}...`);
  }

  /**
   * Hide processing indicator
   */
  hideProcessingIndicator(messageId) {
    // Hide processing indicator
    console.log(`WebOTR: Finished processing message ${messageId}`);
  }

  /**
   * Show decryption indicator
   */
  showDecryptionIndicator(element) {
    // Add decryption indicator to message
    const indicator = document.createElement('div');
    indicator.className = 'webottr-decrypting';
    indicator.textContent = '🔄 Decrypting...';
    element.appendChild(indicator);
  }

  /**
   * Hide decryption indicator
   */
  hideDecryptionIndicator(element) {
    const indicator = element.querySelector('.webottr-decrypting');
    if (indicator) {
      indicator.remove();
    }
  }

  /**
   * Show decryption error
   */
  showDecryptionError(element) {
    const errorIndicator = document.createElement('div');
    errorIndicator.className = 'webottr-decryption-error';
    errorIndicator.textContent = '❌ Decryption failed';
    element.appendChild(errorIndicator);
  }

  /**
   * Show error indicator
   */
  showErrorIndicator(message) {
    console.error(`WebOTR: ${message}`);
  }

  /**
   * Generate unique message ID
   */
  generateMessageId() {
    return `webottr-msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Hook into platform-specific APIs
   */
  hookDiscordAPI() {
    // Discord-specific API hooks will be implemented here
  }

  hookSlackAPI() {
    // Slack-specific API hooks will be implemented here
  }

  hookTeamsAPI() {
    // Teams-specific API hooks will be implemented here
  }

  hookWhatsAppAPI() {
    // WhatsApp-specific API hooks will be implemented here
  }

  hookTelegramAPI() {
    // Telegram-specific API hooks will be implemented here
  }

  hookElementAPI() {
    // Element-specific API hooks will be implemented here
  }

  /**
   * Setup message monitoring for platform changes
   */
  setupMessageMonitoring() {
    // Listen for platform changes
    document.addEventListener('webottr-platform-change', (event) => {
      if (event.detail.type === 'reinitialized') {
        this.reinitialize();
      }
    });
  }

  /**
   * Reinitialize message interceptor
   */
  async reinitialize() {
    console.log('WebOTR: Reinitializing message interceptor...');
    
    // Cleanup existing observers
    this.cleanup();
    
    // Reinitialize
    await this.initialize();
  }

  /**
   * Get interceptor status
   */
  getStatus() {
    return {
      platform: this.platform,
      initialized: this.state.initialized,
      intercepting: this.state.intercepting,
      encryptionEnabled: this.state.encryptionEnabled,
      messagesProcessed: this.state.messagesProcessed,
      queueLength: this.messageQueue.length,
      metrics: {
        totalMessages: this.metrics.totalMessages,
        encryptedMessages: this.metrics.encryptedMessages,
        averageEncryptionTime: this.metrics.encryptionTimes.length > 0
          ? this.metrics.encryptionTimes.reduce((a, b) => a + b) / this.metrics.encryptionTimes.length
          : 0,
        averageDecryptionTime: this.metrics.decryptionTimes.length > 0
          ? this.metrics.decryptionTimes.reduce((a, b) => a + b) / this.metrics.decryptionTimes.length
          : 0
      }
    };
  }

  /**
   * Toggle encryption
   */
  toggleEncryption() {
    this.state.encryptionEnabled = !this.state.encryptionEnabled;
    console.log(`WebOTR: Encryption ${this.state.encryptionEnabled ? 'enabled' : 'disabled'}`);
    return this.state.encryptionEnabled;
  }

  /**
   * Cleanup message interceptor
   */
  cleanup() {
    this.isActive = false;
    
    // Remove event listeners
    this.observers.forEach(observer => {
      if (observer.observer) {
        observer.observer.disconnect();
      } else if (observer.element && observer.event && observer.handler) {
        observer.element.removeEventListener(observer.event, observer.handler, true);
      }
    });
    
    this.observers = [];
    this.messageQueue = [];
    this.processingQueue = false;
    
    this.state.initialized = false;
    this.state.intercepting = false;
  }
}

// Export for use by other components
window.webottrMessageInterceptor = MessageInterceptor;
