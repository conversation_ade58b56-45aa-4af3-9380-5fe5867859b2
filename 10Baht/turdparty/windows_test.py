#!/usr/bin/env python3
"""
Test script for connecting to Vagrant gRPC service and loading VM templates
"""
import grpc
import sys
import json
import requests
import subprocess
import time

# Configuration
GRPC_PORT = 40000
GRPC_HOST = "localhost"

def test_grpc_connection():
    """Test basic connectivity to the Vagrant gRPC service"""
    try:
        # Create an insecure channel to the gRPC server
        channel = grpc.insecure_channel(f"{GRPC_HOST}:{GRPC_PORT}")
        
        # Try a simple connection (no actual RPCs yet)
        state = channel.get_state()
        print(f"Successfully connected to gRPC server on port {GRPC_PORT}")
        print(f"Channel state: {state}")
        
        return True
    except Exception as e:
        print(f"Error connecting to gRPC server: {e}")
        return False

def check_vagrant_executable():
    """Check if Vagrant is installed and get its version"""
    try:
        result = subprocess.run(["vagrant", "--version"], 
                               capture_output=True, text=True, check=True)
        print(f"Vagrant installed: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Vagrant command failed: {e}")
        print(f"Error output: {e.stderr}")
        return False
    except FileNotFoundError:
        print("Vagrant executable not found. Please ensure Vagrant is installed.")
        return False

def list_boxes():
    """List available Vagrant boxes"""
    try:
        result = subprocess.run(["vagrant", "box", "list"], 
                               capture_output=True, text=True, check=True)
        print("Available Vagrant boxes:")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to list Vagrant boxes: {e}")
        print(f"Error output: {e.stderr}")
        return False

def direct_vagrant_status():
    """Run vagrant status command directly"""
    try:
        result = subprocess.run(["vagrant", "status"], 
                               capture_output=True, text=True, check=True)
        print("Vagrant status:")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Vagrant status command failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def test_api_endpoints():
    """Test local API endpoints for VM templates"""
    api_endpoints = [
        "http://localhost:3050/api/v1/vagrant_vm/templates",
        "http://localhost:3050/api/v1/vagrant_vm/"
    ]
    
    for endpoint in api_endpoints:
        try:
            print(f"Testing API endpoint: {endpoint}")
            response = requests.get(endpoint)
            print(f"Status code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Data: {json.dumps(data, indent=2)}")
            else:
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"Error accessing {endpoint}: {e}")

def create_windows_vm():
    """Create a Windows VM using Vagrant"""
    print("\nAttempting to create a Windows VM using Vagrant...")
    
    try:
        # Run vagrant up with output streaming
        process = subprocess.Popen(
            ["vagrant", "up"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1
        )
        
        # Stream the output
        for line in iter(process.stdout.readline, ""):
            print(line, end="")
            sys.stdout.flush()
        
        # Wait for process to complete
        return_code = process.wait()
        
        if return_code == 0:
            print("Successfully created Windows VM!")
            return True
        else:
            print(f"Failed to create Windows VM. Return code: {return_code}")
            return False
    except Exception as e:
        print(f"Error creating Windows VM: {e}")
        return False

def main():
    """Main test function"""
    print("======= Vagrant gRPC Service Test =======")
    
    # Basic checks
    print("\n--- Checking Vagrant Installation ---")
    check_vagrant_executable()
    
    print("\n--- Testing gRPC Connection ---")
    if test_grpc_connection():
        print("gRPC connection successful")
    else:
        print("gRPC connection failed")
    
    print("\n--- Listing Available Vagrant Boxes ---")
    list_boxes()
    
    print("\n--- Checking Vagrant Status ---")
    direct_vagrant_status()
    
    print("\n--- Testing API Endpoints ---")
    test_api_endpoints()
    
    # Prompt user before creating VM
    choice = input("\nDo you want to attempt creating a Windows VM? (y/n): ")
    if choice.lower() == 'y':
        create_windows_vm()

if __name__ == "__main__":
    main() 