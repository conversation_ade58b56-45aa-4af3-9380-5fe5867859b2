#!/usr/bin/env python
"""
Fix the database startup issues
"""
import os
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    # Import the necessary SQLAlchemy modules
    from sqlalchemy import create_engine, text, MetaData, inspect
    
    # Get the database URL from the environment
    database_url = os.environ.get("DATABASE_URL")
    if not database_url:
        logger.error("DATABASE_URL environment variable is not set")
        sys.exit(1)
    
    logger.info(f"Connecting to database: {database_url}")
    
    # Create the engine
    engine = create_engine(database_url)
    
    # First, drop the problematic index if it exists
    with engine.connect() as conn:
        logger.info("Dropping problematic index if it exists")
        conn.execute(text("DROP INDEX IF EXISTS ix_test_cases_id;"))
        
        # Create device_info table if needed
        logger.info("Creating device_info table if it doesn't exist")
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS device_info (
            id UUID PRIMARY KEY,
            device_type VARCHAR(255),
            device_name VARCHAR(255),
            os_name VARCHAR(255),
            os_version VARCHAR(255),
            browser_name VARCHAR(255),
            browser_version VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """))
        
        # Commit the changes
        conn.commit()
    
    # Import the Base class and models from the application
    from api.database import Base
    
    # Attempt to import all models to register with Base.metadata
    try:
        logger.info("Importing models...")
        # Import all models that we know exist
        import importlib
        from api.db.models.item import Item
        from api.db.models.user import User
        from api.db.models.file_upload import FileUpload
        from api.db.models.session import Session
        from api.db.models.hash_report import HashReport
        from api.db.models.vagrant_vm import Vagrant_vm
        
        logger.info("Models imported successfully")
    except ImportError as e:
        logger.warning(f"Could not import some models: {e}")
    
    # Create tables one by one, skipping the problematic ones
    logger.info("Creating tables one by one")
    with engine.connect() as conn:
        # Get the list of tables defined in the metadata
        tables = list(Base.metadata.tables.values())
        
        for table in tables:
            # Skip problematic tables
            if table.name == "test_cases":
                logger.info(f"Skipping problematic table: {table.name}")
                continue
            
            # Skip tables that have foreign keys to non-existent tables
            has_problematic_fk = False
            for fk in table.foreign_keys:
                if "device_info" in str(fk) and table.name == "user_sessions":
                    has_problematic_fk = True
                    break
            
            if has_problematic_fk:
                logger.info(f"Skipping table with problematic FK: {table.name}")
                continue
            
            try:
                logger.info(f"Creating table: {table.name}")
                table.create(bind=conn, checkfirst=True)
                logger.info(f"Table {table.name} created successfully")
            except Exception as e:
                logger.warning(f"Error creating table {table.name}: {e}")
    
    logger.info("Database initialization completed successfully")
    
except Exception as e:
    logger.error(f"Error initializing database: {e}")
    sys.exit(1) 