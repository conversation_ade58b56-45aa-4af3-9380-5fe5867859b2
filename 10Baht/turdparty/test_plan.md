# TurdParty API Test Plan

## System Architecture Overview

The TurdParty API system consists of the following components:

1. **Docker Container**: Runs the FastAPI application
2. **Host System**: Runs MinIO server and Vagrant VMs
3. **SSH Tunneling**: Used for communication between the Docker container and MinIO server
4. **gRPC/SSH**: Used for communication between the Docker container and Vagrant VMs

## Test Scope

This test plan covers:

1. Authentication and authorization
2. MinIO storage integration
3. Vagrant VM management
4. Error handling and resilience
5. Performance and load testing
6. File upload functionality
7. UI accessibility and usability
8. Security testing

## Test Environment Setup

### Prerequisites

- Docker container running the TurdParty API (`TurdParty-container-test`)
- MinIO server running on the host
- Vagrant VMs accessible from the host
- SSH keys configured for MinIO and Vagrant access
- Test data for storage operations

### Environment Variables

```
# Authentication
TEST_MODE=true

# SSH Configuration
SSH_KEY_PATH=~/.ssh/replit
VAGRANT_SSH_USER=vagrant
DEFAULT_VAGRANT_SERVER=localhost

# MinIO Configuration
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
```

### Special Considerations

1. **Authentication in Container**: When running tests inside the Docker container, authentication with JWT tokens may cause 500 Internal Server Errors. Tests should be designed to handle this case by:
   - Using test mode authentication
   - Implementing retry logic with appropriate error handling
   - Logging detailed error information for debugging

2. **Host Access from Container**: The Docker container needs to access services running on the host. This requires:
   - Proper network configuration
   - Using the host's IP address or network alias instead of localhost
   - Port mapping between container and host

## Test Categories

### 1. Authentication Tests

#### 1.1 Test Token Generation

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| AUTH-01 | Generate test token | 1. Send POST request to `/api/auth/test-token` | 200 OK with valid JWT token |
| AUTH-02 | Access protected endpoint with test token | 1. Get test token<br>2. Access `/api/auth/me` with token | 200 OK with user details |
| AUTH-03 | Access protected endpoint without token | 1. Access `/api/auth/me` without token | 401 Unauthorized |

#### 1.2 Role-Based Authorization

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| AUTH-04 | Access admin endpoint with regular user | 1. Generate regular user token<br>2. Access admin endpoint | 403 Forbidden |
| AUTH-05 | Access admin endpoint with admin user | 1. Generate admin token<br>2. Access admin endpoint | 200 OK |

### 2. MinIO Storage Tests

#### 2.1 SSH Tunnel Management

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| MINIO-01 | Establish SSH tunnel to MinIO | 1. Call endpoint that requires MinIO access | Tunnel established successfully |
| MINIO-02 | Close SSH tunnel | 1. Call `/api/storage/tunnel/close` | Tunnel closed successfully |
| MINIO-03 | Reconnect after tunnel closure | 1. Close tunnel<br>2. Call endpoint that requires MinIO access | New tunnel established successfully |

#### 2.2 Bucket Operations

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| MINIO-04 | List buckets | 1. Call GET `/api/storage/buckets` | 200 OK with list of buckets |
| MINIO-05 | Create bucket | 1. Call POST `/api/storage/buckets` with new bucket name | 200 OK, bucket created |
| MINIO-06 | Delete bucket | 1. Create bucket<br>2. Call DELETE `/api/storage/buckets/{bucket_name}` | 200 OK, bucket deleted |
| MINIO-07 | Create bucket with invalid name | 1. Call POST `/api/storage/buckets` with invalid name | 400 Bad Request |

#### 2.3 Object Operations

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| MINIO-08 | Upload file | 1. Create bucket<br>2. Call POST `/api/storage/buckets/{bucket}/objects` with file | 200 OK, file uploaded |
| MINIO-09 | Download file | 1. Upload file<br>2. Call GET `/api/storage/buckets/{bucket}/objects/{key}` | 200 OK, file downloaded |
| MINIO-10 | List objects | 1. Upload multiple files<br>2. Call GET `/api/storage/buckets/{bucket}/objects` | 200 OK with list of objects |
| MINIO-11 | Delete object | 1. Upload file<br>2. Call DELETE `/api/storage/buckets/{bucket}/objects/{key}` | 200 OK, object deleted |
| MINIO-12 | Get object metadata | 1. Upload file<br>2. Call GET `/api/storage/buckets/{bucket}/objects/{key}/metadata` | 200 OK with metadata |

### 3. Vagrant VM Management Tests

#### 3.1 Connection Management

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| VM-01 | Check connection status | 1. Call GET `/api/vagrant/connection/status` | 200 OK with connection status |
| VM-02 | Verify connection | 1. Call POST `/api/vagrant/connection/check` | 200 OK with successful connection |

#### 3.2 VM Status Operations

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| VM-03 | Get VM status | 1. Call GET `/api/vagrant/status/{vm_id}` | 200 OK with VM status |
| VM-04 | Get remote server status | 1. Call GET `/api/vagrant/remote/{hostname}/status` | 200 OK with server status |
| VM-05 | Get VM info | 1. Call GET `/api/vagrant/info/{vm_id}` | 200 OK with VM info |

#### 3.3 VM Lifecycle Operations

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| VM-06 | Start VM | 1. Call POST `/api/vagrant/up/{vm_id}` | 200 OK, VM started |
| VM-07 | Stop VM | 1. Start VM<br>2. Call POST `/api/vagrant/halt/{vm_id}` | 200 OK, VM stopped |
| VM-08 | Suspend VM | 1. Start VM<br>2. Call POST `/api/vagrant/vms/{vm_id}/suspend` | 200 OK, VM suspended |
| VM-09 | Destroy VM | 1. Start VM<br>2. Call POST `/api/vagrant/destroy/{vm_id}` | 200 OK, VM destroyed |

#### 3.4 VM Command Execution

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| VM-10 | Execute command | 1. Start VM<br>2. Call POST `/api/vagrant/execute/{vm_id}` with command | 200 OK with command output |
| VM-11 | Execute sudo command | 1. Start VM<br>2. Call POST `/api/vagrant/execute/{vm_id}` with sudo=true | 200 OK with command output |

### 4. Error Handling and Resilience Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| ERR-01 | Handle MinIO server down | 1. Stop MinIO server<br>2. Call MinIO endpoint | 503 Service Unavailable with clear error message |
| ERR-02 | Handle Vagrant server down | 1. Stop Vagrant service<br>2. Call Vagrant endpoint | 503 Service Unavailable with clear error message |
| ERR-03 | Handle SSH tunnel failure | 1. Block SSH port<br>2. Call MinIO endpoint | 500 Internal Server Error with clear error message |
| ERR-04 | Recover from SSH tunnel failure | 1. Cause SSH tunnel failure<br>2. Restore SSH access<br>3. Call MinIO endpoint | New tunnel established, 200 OK |

### 5. Performance and Load Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| PERF-01 | Concurrent MinIO operations | 1. Send 10 concurrent file upload requests | All requests complete successfully |
| PERF-02 | Large file upload | 1. Upload 100MB file to MinIO | File uploads successfully within timeout |
| PERF-03 | Multiple VM operations | 1. Send commands to multiple VMs concurrently | All commands execute successfully |

## File Upload Testing

### Test Categories

1. **Basic Functionality Tests**
   - Single file upload
   - Multiple file upload
   - File download
   - File deletion
   - File listing

2. **Edge Case Tests**
   - Empty files
   - Large files (10MB+)
   - Files with special characters in names
   - Duplicate file uploads
   - Upload cancellation

3. **Accessibility Tests**
   - Keyboard navigation
   - Screen reader compatibility
   - Focus management
   - Visual indicators
   - Responsive design

4. **Performance Tests**
   - Upload speed for different file sizes
   - Concurrent uploads
   - UI responsiveness during uploads
   - File listing performance

5. **Security Tests**
   - File type restrictions
   - File size limits
   - Protection against malicious files
   - Path traversal prevention
   - XSS prevention in file names

### Test Execution

Tests are organized into separate Playwright test files:
- `file-upload.spec.ts` - Basic functionality tests
- `file-upload-edge-cases.spec.ts` - Edge case tests
- `file-upload-accessibility.spec.ts` - Accessibility tests
- `file-upload-performance.spec.ts` - Performance tests
- `file-upload-security.spec.ts` - Security tests
- `dev-server-startup.spec.ts` - Tests with development server

Run all tests using the provided script:
```bash
./scripts/run-tests.sh
```

To include development server tests:
```bash
./scripts/run-tests.sh --with-dev-server
```

## Test Implementation

### Test Tools

1. **Pytest**: Primary testing framework
2. **Requests**: HTTP client for API testing
3. **Docker**: Container management
4. **MinIO Client**: For verifying MinIO operations
5. **Vagrant CLI**: For verifying VM states

### Test Structure

```
api/tests/
├── test_auth/
│   ├── test_token.py
│   └── test_permissions.py
├── test_minio/
│   ├── test_tunnel.py
│   ├── test_buckets.py
│   └── test_objects.py
├── test_vagrant/
│   ├── test_connection.py
│   ├── test_status.py
│   ├── test_lifecycle.py
│   └── test_commands.py
├── test_error_handling.py
└── test_performance.py
```

### Test Fixtures

1. **Authentication Token**: Generate and provide test tokens
2. **MinIO Test Bucket**: Create and clean up test buckets
3. **Test Files**: Generate test files of various sizes
4. **Vagrant VM**: Ensure test VM is available

## Test Execution

### Running Tests

```bash
# Run all tests
./run_tests_in_docker.py

# Run specific test category
./run_tests_in_docker.py api/tests/test_minio

# Run with coverage report
./run_tests_in_docker.py --coverage

# Run integration tests
./run_tests_in_docker.py --integration
```

### Test Reports

1. **Console Output**: Basic pass/fail information
2. **HTML Report**: Detailed test results with coverage information
3. **Log Files**: Detailed logs for debugging

## Test Maintenance

1. **Regular Updates**: Update tests when API changes
2. **Environment Checks**: Verify test environment before running tests
3. **Cleanup**: Ensure all tests clean up resources after execution

## Continuous Integration

1. **Pre-commit Hooks**: Run basic tests before commit
2. **CI Pipeline**: Run full test suite on push
3. **Scheduled Tests**: Run performance tests on schedule

## Conclusion

This test plan provides a comprehensive approach to testing the TurdParty API system, with a focus on the integration with MinIO and Vagrant services running on the host. By following this plan, we can ensure that the system functions correctly, handles errors gracefully, and performs well under load. 