// Standalone file upload test with <PERSON>wright
// This test focuses only on the file upload functionality
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const assert = require('assert');

// Configuration
const config = {
  // URLs - use host.docker.internal for Docker container to access host
  baseUrl: process.env.BASE_URL || 'http://host.docker.internal:3100',
  uploadUrl: process.env.UPLOAD_URL || 'http://host.docker.internal:3100/upload',
  filesUrl: process.env.FILES_URL || 'http://host.docker.internal:3100/files',
  
  // Test file
  testFile: {
    path: process.env.TEST_FILE_PATH || '/app/test-upload.txt',
    content: `Test file for upload functionality testing
Created at: ${new Date().toISOString()}
Random ID: ${Math.random().toString(36).substring(2, 15)}`,
  },
  
  // Screenshot directory
  screenshotDir: process.env.SCREENSHOT_DIR || '/app/test_screenshots',
  
  // Browser options
  browserOptions: {
    headless: true, // Always headless in Docker
    slowMo: 50,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage'
    ]
  },
  
  // Timeouts
  timeouts: {
    navigation: 30000,
    assertion: 10000
  }
};

// Log configuration
console.log(`Using base URL: ${config.baseUrl}`);
console.log(`Test file path: ${config.testFile.path}`);
console.log(`Screenshot directory: ${config.screenshotDir}`);

// Create screenshot directory if it doesn't exist
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true });
}

// Create test file if it doesn't exist
if (!fs.existsSync(config.testFile.path)) {
  fs.writeFileSync(config.testFile.path, config.testFile.content);
  console.log(`Created test file at ${config.testFile.path}`);
} else {
  console.log(`Using existing test file at ${config.testFile.path}`);
}

// Helper function to take screenshots
async function takeScreenshot(page, name) {
  const screenshotPath = path.join(config.screenshotDir, `${name}.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  console.log(`Screenshot saved: ${screenshotPath}`);
  return screenshotPath;
}

// Main test function
async function runFileUploadTest() {
  const browser = await chromium.launch(config.browserOptions);
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    console.log('Starting file upload test');
    
    // Step 1: Navigate to the upload page
    console.log('Step 1: Navigating to the upload page');
    await page.goto(config.uploadUrl);
    await page.waitForLoadState('networkidle');
    await takeScreenshot(page, '01-upload-page');
    
    // Step 2: Fill upload form and upload file
    console.log('Step 2: Filling upload form');
    
    // Fill description
    const description = `Upload test - ${new Date().toISOString()}`;
    await page.locator('textarea[placeholder*="description"]').fill(description);
    
    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(config.testFile.path);
    await takeScreenshot(page, '02-file-selected');
    
    // Click upload button
    console.log('Step 3: Submitting the upload');
    const uploadButton = page.locator('button:has-text("Upload")');
    await uploadButton.click();
    
    // Wait for upload to complete
    try {
      await page.waitForSelector('.ant-message-success, .upload-success', { timeout: config.timeouts.navigation });
      console.log('Upload completed successfully');
      await takeScreenshot(page, '03-upload-success');
    } catch (error) {
      console.error('Upload failed:', error);
      await takeScreenshot(page, '03-upload-error');
      throw new Error('Upload failed');
    }
    
    // Step 4: Verify file appears in file list
    console.log('Step 4: Verifying file in file list');
    await page.goto(config.filesUrl);
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.ant-table-row', { timeout: config.timeouts.assertion });
    await takeScreenshot(page, '04-file-list');
    
    // Find our file in the list by description
    const fileRow = page.locator(`.ant-table-row:has-text("${description}")`).first();
    await fileRow.waitFor({ state: 'visible', timeout: config.timeouts.assertion });
    
    console.log('✅ File upload test completed successfully!');
    return true;
  } catch (error) {
    console.error('File upload test failed:', error);
    // Take final screenshot on failure
    await takeScreenshot(page, 'error-state');
    return false;
  } finally {
    // Clean up
    console.log('Cleaning up...');
    await browser.close();
  }
}

// Run the test
console.log('Starting standalone file upload test...');
runFileUploadTest()
  .then(success => {
    if (success) {
      console.log('✅ All tests passed!');
      process.exit(0);
    } else {
      console.error('❌ Test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  }); 