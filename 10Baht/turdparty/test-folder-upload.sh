#!/bin/bash

# Test script for folder upload functionality

# Set color variables
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing Folder Upload Functionality${NC}"
echo "======================================"

# Test file paths
TEST_DIR="./test_upload_dir"
API_URL="http://localhost:3050"

# Create test directory and files if they don't exist
if [ ! -d "$TEST_DIR" ]; then
    mkdir -p "$TEST_DIR"
    echo "This is file 1" > "$TEST_DIR/file1.txt"
    echo "This is file 2" > "$TEST_DIR/file2.txt"
    mkdir -p "$TEST_DIR/subdir"
    echo "This is a subdir file" > "$TEST_DIR/subdir/file3.txt"
    echo -e "${GRE<PERSON>}Created test directory and files: $TEST_DIR${NC}"
fi

# Test API health
echo -e "\n${YELLOW}Testing API Health${NC}"
echo "----------------------"
HEALTH_RESPONSE=$(curl -s "$API_URL/api/v1/health/")
if [[ "$HEALTH_RESPONSE" == *"ok"* ]]; then
    echo -e "${GREEN}API is healthy.${NC}"
else
    echo -e "${RED}API health check failed. Response: $HEALTH_RESPONSE${NC}"
    # Continue anyway since we know the API is running
    echo -e "${YELLOW}Continuing anyway since we know the API is running...${NC}"
fi

# Try to test token (this might fail if authentication is required)
echo -e "\n${YELLOW}Getting API Token${NC}"
echo "-------------------"
TOKEN_RESPONSE=$(curl -s -X POST "$API_URL/api/v1/auth/test-token" -H "Content-Type: application/json" -d '{"token": "test"}')
TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | sed 's/"access_token":"//')
echo "Token received: ${TOKEN:0:15}..."

# Prepare files for folder upload
echo -e "\n${YELLOW}Preparing files for folder upload${NC}"
echo "--------------------------------"

# Create a temp file with the files data
TMP_FILES=$(mktemp)
ls -1 $TEST_DIR/* $TEST_DIR/subdir/* > $TMP_FILES
echo "Files to upload:"
cat $TMP_FILES

# Curl command to upload folder
echo -e "\n${YELLOW}Uploading folder${NC}"
echo "----------------"

CURL_CMD="curl -s -o response.txt -w "%{http_code}" -X POST "$API_URL/api/v1/file_upload/folder" \
     -H "Content-Type: multipart/form-data" \
     -H "Authorization: Bearer $TOKEN""

# Add each file with its path
FILE_COUNT=0
while read -r file; do
    if [ -f "$file" ]; then
        REL_PATH=${file#$TEST_DIR/}
        CURL_CMD+=" -F \"files=@$file\""
        CURL_CMD+=" -F \"paths=$REL_PATH\""
        ((FILE_COUNT++))
    fi
done < $TMP_FILES

# Add description
CURL_CMD+=" -F \"description=Test folder upload\""

# Execute the curl command
echo "Uploading $FILE_COUNT files..."
RESPONSE=$(eval $CURL_CMD)

echo "Status code: $RESPONSE"

if [[ "$RESPONSE" == 2* ]]; then
    echo -e "${GREEN}Folder upload successful!${NC}"
    echo "Response:"
    cat response.txt
    echo -e "\n${GREEN}=== Folder upload test succeeded! ===${NC}"
    rm $TMP_FILES
    exit 0
else
    echo -e "${RED}Folder upload failed.${NC}"
    echo "Response:"
    cat response.txt
    rm $TMP_FILES
    echo -e "\n${RED}=== Folder upload test failed. ===${NC}"
    exit 1
fi 