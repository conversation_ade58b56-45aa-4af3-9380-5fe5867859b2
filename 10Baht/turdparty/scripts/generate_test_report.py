#!/usr/bin/env python3
"""
Test Report Generator

This script generates comprehensive HTML test reports from JUnit XML files.
It supports aggregating results from multiple test suites and provides statistics
and visualizations of test performance and coverage.
"""

import argparse
import glob
import json
import os
import sys
import xml.etree.ElementTree as ET
from collections import defaultdict
from datetime import datetime

# Basic HTML template for the report
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty Test Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .summary {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stats {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .stat-box {
            padding: 15px;
            border-radius: 5px;
            flex: 1;
            min-width: 150px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .passed {
            background-color: #d4edda;
            color: #155724;
        }
        .failed {
            background-color: #f8d7da;
            color: #721c24;
        }
        .skipped {
            background-color: #fff3cd;
            color: #856404;
        }
        .total {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .coverage {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .test-details {
            cursor: pointer;
        }
        .test-details:hover {
            background-color: #f1f1f1;
        }
        .test-output {
            display: none;
            white-space: pre-wrap;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            border: 1px solid #ddd;
        }
        .chart-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        .chart {
            flex: 1;
            min-width: 300px;
            height: 300px;
            background-color: #fff;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .failures {
            color: #721c24;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <h1>TurdParty Test Report</h1>
        <p class="timestamp">Generated on: {timestamp}</p>

        {summary_section}
        
        {stats_section}
        
        {charts_section}
        
        {failures_section}
        
        {results_section}
        
        <script>
            // Toggle test output visibility
            document.addEventListener('DOMContentLoaded', function() {
                const testRows = document.querySelectorAll('.test-details');
                testRows.forEach(row => {
                    row.addEventListener('click', function() {
                        const outputId = this.getAttribute('data-output-id');
                        const output = document.getElementById(outputId);
                        if (output.style.display === 'block') {
                            output.style.display = 'none';
                        } else {
                            output.style.display = 'block';
                        }
                    });
                });
            });
            
            // Create charts
            {chart_js}
        </script>
    </div>
</body>
</html>
"""

def parse_junit_xml(file_path):
    """Parse a JUnit XML file and return test results."""
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        if root.tag in ('testsuites', 'testsuite'):
            return parse_element(root, os.path.basename(file_path))
        else:
            print(f"Warning: Unexpected XML format in {file_path}")
            return None
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return None

def parse_element(element, file_name):
    """Parse a testsuite or testsuites element."""
    results = {
        'name': file_name,
        'timestamp': datetime.now().isoformat(),
        'tests': 0,
        'failures': 0,
        'errors': 0,
        'skipped': 0,
        'time': 0,
        'suites': [],
        'test_cases': []
    }
    
    if element.tag == 'testsuites':
        for testsuite in element.findall('./testsuite'):
            suite_result = parse_testsuite(testsuite)
            results['suites'].append(suite_result)
            results['tests'] += suite_result['tests']
            results['failures'] += suite_result['failures']
            results['errors'] += suite_result['errors']
            results['skipped'] += suite_result['skipped']
            results['time'] += float(suite_result['time']) if suite_result['time'] else 0
            results['test_cases'].extend(suite_result['test_cases'])
    elif element.tag == 'testsuite':
        suite_result = parse_testsuite(element)
        results['suites'].append(suite_result)
        results['tests'] += suite_result['tests']
        results['failures'] += suite_result['failures']
        results['errors'] += suite_result['errors']
        results['skipped'] += suite_result['skipped']
        results['time'] += float(suite_result['time']) if suite_result['time'] else 0
        results['test_cases'].extend(suite_result['test_cases'])
    
    return results

def parse_testsuite(testsuite):
    """Parse a testsuite element."""
    suite = {
        'name': testsuite.get('name', 'Unknown Suite'),
        'tests': int(testsuite.get('tests', 0)),
        'failures': int(testsuite.get('failures', 0)),
        'errors': int(testsuite.get('errors', 0)),
        'skipped': int(testsuite.get('skipped', 0)),
        'time': testsuite.get('time', '0'),
        'timestamp': testsuite.get('timestamp', ''),
        'test_cases': []
    }
    
    for testcase in testsuite.findall('./testcase'):
        case = parse_testcase(testcase)
        suite['test_cases'].append(case)
    
    return suite

def parse_testcase(testcase):
    """Parse a testcase element."""
    case = {
        'name': testcase.get('name', 'Unknown Test'),
        'classname': testcase.get('classname', ''),
        'time': testcase.get('time', '0'),
        'status': 'passed',
        'message': '',
        'output': ''
    }
    
    failure = testcase.find('./failure')
    error = testcase.find('./error')
    skipped = testcase.find('./skipped')
    system_out = testcase.find('./system-out')
    
    if failure is not None:
        case['status'] = 'failed'
        case['message'] = failure.get('message', '')
        case['output'] = failure.text or ''
    elif error is not None:
        case['status'] = 'error'
        case['message'] = error.get('message', '')
        case['output'] = error.text or ''
    elif skipped is not None:
        case['status'] = 'skipped'
        case['message'] = skipped.get('message', '')
    
    if system_out is not None:
        case['output'] += '\n' + (system_out.text or '')
    
    return case

def aggregate_results(results_list):
    """Aggregate results from multiple test files."""
    aggregated = {
        'name': 'Aggregated Test Results',
        'timestamp': datetime.now().isoformat(),
        'tests': 0,
        'failures': 0,
        'errors': 0,
        'skipped': 0,
        'time': 0,
        'suites': [],
        'test_cases': [],
        'by_suite': defaultdict(lambda: {
            'tests': 0,
            'failures': 0,
            'errors': 0,
            'skipped': 0,
            'time': 0
        }),
        'by_status': {
            'passed': 0,
            'failed': 0,
            'error': 0,
            'skipped': 0
        },
        'by_file': {}
    }
    
    for result in results_list:
        if not result:
            continue
            
        aggregated['tests'] += result['tests']
        aggregated['failures'] += result['failures']
        aggregated['errors'] += result['errors']
        aggregated['skipped'] += result['skipped']
        aggregated['time'] += result['time']
        aggregated['suites'].extend(result['suites'])
        aggregated['test_cases'].extend(result['test_cases'])
        
        aggregated['by_file'][result['name']] = {
            'tests': result['tests'],
            'failures': result['failures'],
            'errors': result['errors'],
            'skipped': result['skipped'],
            'time': result['time']
        }
        
        for suite in result['suites']:
            suite_name = suite['name']
            aggregated['by_suite'][suite_name]['tests'] += suite['tests']
            aggregated['by_suite'][suite_name]['failures'] += suite['failures']
            aggregated['by_suite'][suite_name]['errors'] += suite['errors']
            aggregated['by_suite'][suite_name]['skipped'] += suite['skipped']
            aggregated['by_suite'][suite_name]['time'] += float(suite['time']) if suite['time'] else 0
    
    for case in aggregated['test_cases']:
        aggregated['by_status'][case['status']] += 1
    
    return aggregated

def generate_summary_section(aggregated):
    """Generate the summary section of the report."""
    passed = aggregated['tests'] - aggregated['failures'] - aggregated['errors'] - aggregated['skipped']
    pass_rate = (passed / aggregated['tests'] * 100) if aggregated['tests'] > 0 else 0
    
    return f"""
    <div class="summary">
        <h2>Summary</h2>
        <p>
            <strong>Total Test Files:</strong> {len(aggregated['by_file'])}<br>
            <strong>Total Test Suites:</strong> {len(aggregated['by_suite'])}<br>
            <strong>Total Tests:</strong> {aggregated['tests']}<br>
            <strong>Passed:</strong> {passed} ({pass_rate:.2f}%)<br>
            <strong>Failed:</strong> {aggregated['failures']}<br>
            <strong>Errors:</strong> {aggregated['errors']}<br>
            <strong>Skipped:</strong> {aggregated['skipped']}<br>
            <strong>Total Duration:</strong> {aggregated['time']:.3f} seconds
        </p>
    </div>
    """

def generate_stats_section(aggregated):
    """Generate the statistics section of the report."""
    passed = aggregated['tests'] - aggregated['failures'] - aggregated['errors'] - aggregated['skipped']
    pass_rate = (passed / aggregated['tests'] * 100) if aggregated['tests'] > 0 else 0
    
    return f"""
    <div class="stats">
        <div class="stat-box total">
            <h3>Total Tests</h3>
            <p>{aggregated['tests']}</p>
        </div>
        <div class="stat-box passed">
            <h3>Passed</h3>
            <p>{passed}</p>
        </div>
        <div class="stat-box failed">
            <h3>Failed</h3>
            <p>{aggregated['failures'] + aggregated['errors']}</p>
        </div>
        <div class="stat-box skipped">
            <h3>Skipped</h3>
            <p>{aggregated['skipped']}</p>
        </div>
        <div class="stat-box coverage">
            <h3>Pass Rate</h3>
            <p>{pass_rate:.2f}%</p>
        </div>
    </div>
    """

def generate_charts_section(aggregated):
    """Generate the charts section of the report."""
    # Prepare data for charts
    by_status_data = {
        'labels': ['Passed', 'Failed', 'Errors', 'Skipped'],
        'values': [
            aggregated['by_status']['passed'],
            aggregated['by_status']['failed'],
            aggregated['by_status']['error'],
            aggregated['by_status']['skipped']
        ]
    }
    
    by_suite_data = {
        'labels': list(aggregated['by_suite'].keys())[:10],  # Limit to top 10 suites
        'passed': [],
        'failed': [],
        'skipped': []
    }
    
    for suite_name in by_suite_data['labels']:
        suite = aggregated['by_suite'][suite_name]
        passed = suite['tests'] - suite['failures'] - suite['errors'] - suite['skipped']
        by_suite_data['passed'].append(passed)
        by_suite_data['failed'].append(suite['failures'] + suite['errors'])
        by_suite_data['skipped'].append(suite['skipped'])
    
    # Chart JavaScript
    chart_js = f"""
        // Test Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {{
            type: 'pie',
            data: {{
                labels: {json.dumps(by_status_data['labels'])},
                datasets: [{{
                    data: {json.dumps(by_status_data['values'])},
                    backgroundColor: [
                        '#28a745',  // Passed - Green
                        '#dc3545',  // Failed - Red
                        '#dc3545',  // Errors - Also Red
                        '#ffc107'   // Skipped - Yellow
                    ]
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'right',
                    }},
                    title: {{
                        display: true,
                        text: 'Test Status Distribution'
                    }}
                }}
            }}
        }});
        
        // Test Suite Chart
        const suiteCtx = document.getElementById('suiteChart').getContext('2d');
        new Chart(suiteCtx, {{
            type: 'bar',
            data: {{
                labels: {json.dumps(by_suite_data['labels'])},
                datasets: [
                    {{
                        label: 'Passed',
                        data: {json.dumps(by_suite_data['passed'])},
                        backgroundColor: '#28a745'
                    }},
                    {{
                        label: 'Failed',
                        data: {json.dumps(by_suite_data['failed'])},
                        backgroundColor: '#dc3545'
                    }},
                    {{
                        label: 'Skipped',
                        data: {json.dumps(by_suite_data['skipped'])},
                        backgroundColor: '#ffc107'
                    }}
                ]
            }},
            options: {{
                responsive: true,
                scales: {{
                    x: {{
                        stacked: true,
                        title: {{
                            display: true,
                            text: 'Test Suites'
                        }}
                    }},
                    y: {{
                        stacked: true,
                        title: {{
                            display: true,
                            text: 'Number of Tests'
                        }}
                    }}
                }},
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Test Results by Suite'
                    }}
                }}
            }}
        }});
    """
    
    return f"""
    <h2>Test Visualizations</h2>
    <div class="chart-container">
        <div class="chart">
            <canvas id="statusChart"></canvas>
        </div>
        <div class="chart">
            <canvas id="suiteChart"></canvas>
        </div>
    </div>
    """, chart_js

def generate_failures_section(aggregated):
    """Generate the failures section of the report."""
    failures = []
    
    for case in aggregated['test_cases']:
        if case['status'] in ('failed', 'error'):
            failures.append(case)
    
    if not failures:
        return "<h2>Failures</h2><p>No test failures! 🎉</p>"
    
    failures_html = "<h2>Failures</h2>"
    failures_html += "<div class='failures'>"
    failures_html += f"<p><strong>Total Failures:</strong> {len(failures)}</p>"
    failures_html += "<table>"
    failures_html += "<tr><th>Test</th><th>Class</th><th>Message</th></tr>"
    
    for i, case in enumerate(failures):
        failures_html += f"""
        <tr class="test-details" data-output-id="failure-{i}">
            <td>{case['name']}</td>
            <td>{case['classname']}</td>
            <td>{case['message']}</td>
        </tr>
        <tr>
            <td colspan="3">
                <div id="failure-{i}" class="test-output">{case['output']}</div>
            </td>
        </tr>
        """
    
    failures_html += "</table></div>"
    return failures_html

def generate_results_section(aggregated):
    """Generate the results section of the report."""
    results_html = "<h2>Test Results</h2>"
    
    # Results by file
    results_html += "<h3>Results by File</h3>"
    results_html += "<table>"
    results_html += "<tr><th>File</th><th>Tests</th><th>Passed</th><th>Failed</th><th>Skipped</th><th>Time (s)</th></tr>"
    
    for file_name, file_data in aggregated['by_file'].items():
        passed = file_data['tests'] - file_data['failures'] - file_data['errors'] - file_data['skipped']
        results_html += f"""
        <tr>
            <td>{file_name}</td>
            <td>{file_data['tests']}</td>
            <td>{passed}</td>
            <td>{file_data['failures'] + file_data['errors']}</td>
            <td>{file_data['skipped']}</td>
            <td>{file_data['time']:.3f}</td>
        </tr>
        """
    
    results_html += "</table>"
    
    # Results by suite
    results_html += "<h3>Results by Suite</h3>"
    results_html += "<table>"
    results_html += "<tr><th>Suite</th><th>Tests</th><th>Passed</th><th>Failed</th><th>Skipped</th><th>Time (s)</th></tr>"
    
    for suite_name, suite_data in aggregated['by_suite'].items():
        passed = suite_data['tests'] - suite_data['failures'] - suite_data['errors'] - suite_data['skipped']
        results_html += f"""
        <tr>
            <td>{suite_name}</td>
            <td>{suite_data['tests']}</td>
            <td>{passed}</td>
            <td>{suite_data['failures'] + suite_data['errors']}</td>
            <td>{suite_data['skipped']}</td>
            <td>{suite_data['time']:.3f}</td>
        </tr>
        """
    
    results_html += "</table>"
    
    # Detailed test results
    results_html += "<h3>All Test Cases</h3>"
    results_html += "<table>"
    results_html += "<tr><th>Test</th><th>Class</th><th>Status</th><th>Time (s)</th></tr>"
    
    for i, case in enumerate(aggregated['test_cases']):
        status_class = 'passed' if case['status'] == 'passed' else ('skipped' if case['status'] == 'skipped' else 'failed')
        results_html += f"""
        <tr class="test-details {status_class}" data-output-id="output-{i}">
            <td>{case['name']}</td>
            <td>{case['classname']}</td>
            <td>{case['status'].capitalize()}</td>
            <td>{float(case['time']):.3f}</td>
        </tr>
        """
        
        if case['output']:
            results_html += f"""
            <tr>
                <td colspan="4">
                    <div id="output-{i}" class="test-output">{case['output']}</div>
                </td>
            </tr>
            """
    
    results_html += "</table>"
    
    return results_html

def generate_report(xml_files, output_file):
    """Generate a comprehensive HTML test report from JUnit XML files."""
    results_list = []
    
    for xml_file in xml_files:
        result = parse_junit_xml(xml_file)
        if result:
            results_list.append(result)
    
    if not results_list:
        print("No valid test results found.")
        return False
    
    aggregated = aggregate_results(results_list)
    
    summary_section = generate_summary_section(aggregated)
    stats_section = generate_stats_section(aggregated)
    charts_section, chart_js = generate_charts_section(aggregated)
    failures_section = generate_failures_section(aggregated)
    results_section = generate_results_section(aggregated)
    
    html = HTML_TEMPLATE.format(
        timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        summary_section=summary_section,
        stats_section=stats_section,
        charts_section=charts_section,
        failures_section=failures_section,
        results_section=results_section,
        chart_js=chart_js
    )
    
    with open(output_file, 'w') as f:
        f.write(html)
    
    print(f"Report generated: {output_file}")
    return True

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description='Generate HTML test reports from JUnit XML files.')
    parser.add_argument('--input', '-i', nargs='+', help='Input XML file(s)', required=False)
    parser.add_argument('--directory', '-d', help='Directory containing XML files', required=False)
    parser.add_argument('--pattern', '-p', default='*.xml', help='File pattern to match in directory')
    parser.add_argument('--output', '-o', default='test-report.html', help='Output HTML file')
    
    args = parser.parse_args()
    
    xml_files = []
    
    if args.input:
        xml_files.extend(args.input)
    
    if args.directory:
        pattern = os.path.join(args.directory, args.pattern)
        xml_files.extend(glob.glob(pattern))
    
    if not xml_files:
        print("No input files specified. Use --input or --directory.")
        return 1
    
    success = generate_report(xml_files, args.output)
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main()) 