#!/usr/bin/env python
"""
Database initialization script.
"""
import sys
import os
import logging
from typing import List

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import create_engine, inspect, MetaData
from api.core.config import settings
from api.db.base_model import Base, model_history, metadata
from api.core.logging_config import setup_logging
from api.core.dependencies import check_dependencies
from sqlalchemy.sql import text

# Import all models to ensure they are registered with Base.metadata
from api.db.models.item import Item
from api.db.models.user import User
from api.db.models.file_upload import FileUpload
from api.db.models.file_selection import FileSelection
from api.db.models.vagrant_vm import VagrantVM
from api.db.models.vm_injection import VMInjection

# Set up logging
logger = setup_logging(log_level=logging.INFO)

# Check dependencies
dependency_check = check_dependencies({
    "sqlalchemy": ">=2.0.0", 
    "psycopg2-binary": ">=2.9.0"
})
if dependency_check["failure"]:
    logger.warning(f"Some database dependencies have issues: {dependency_check['failure']}")

def init_db() -> None:
    """Initialize the database."""
    logger.info("Database initialization script started")
    
    try:
        # Create engine for default database
        default_engine = create_engine("********************************************/postgres")
        
        # Drop and recreate database
        logger.info("Dropping and recreating database...")
        with default_engine.connect() as conn:
            # Disable autocommit to run commands outside transaction
            conn.execution_options(isolation_level="AUTOCOMMIT")
            
            # Terminate all connections to the database
            conn.execute(text("""
                SELECT pg_terminate_backend(pg_stat_activity.pid)
                FROM pg_stat_activity
                WHERE pg_stat_activity.datname = 'app'
                AND pid <> pg_backend_pid();
            """))
            
            # Drop and recreate database
            conn.execute(text("DROP DATABASE IF EXISTS app"))
            conn.execute(text("CREATE DATABASE app"))
        
        # Create engine for app database
        engine = create_engine(settings.DATABASE_URL)
        
        # Create tables
        logger.info("Creating tables...")
        
        # Create model_history table first
        model_history.create(bind=engine)
        
        # Create all other tables
        Base.metadata.create_all(bind=engine)
        
        # Verify tables were created
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        logger.info(f"Created tables: {tables}")
        
        logger.info("Database initialization completed successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

if __name__ == "__main__":
    init_db()
