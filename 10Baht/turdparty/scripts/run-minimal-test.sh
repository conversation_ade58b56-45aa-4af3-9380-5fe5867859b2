#!/bin/bash

# Run the minimal upload test
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running minimal file upload test${NC}"

# Ensure test directories exist
mkdir -p test_screenshots

# Get host IP
HOST_IP=$(ip -4 addr show docker0 | grep -oP '(?<=inet\s)\d+(\.\d+){3}' || echo "**********")
echo -e "${YELLOW}Using host IP: ${HOST_IP}${NC}"

# Run the test using docker-compose
echo -e "${YELLOW}Running test with docker-compose${NC}"
cd "$(dirname "$0")/.." # Make sure we're in the project root
docker-compose -f .dockerwrapper/docker-compose.playwright.yml run -e HOST_IP=${HOST_IP} --rm playwright npx playwright test tests/minimal-upload.spec.js --reporter=list

# Check result
if [ $? -eq 0 ]; then
  echo -e "${GREEN}Test passed!${NC}"
  echo -e "${YELLOW}Screenshots saved to test_screenshots/${NC}"
  exit 0
else
  echo -e "${RED}Test failed. Check the logs above for details.${NC}"
  exit 1
fi 