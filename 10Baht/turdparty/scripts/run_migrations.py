#!/usr/bin/env python3
"""
Script to execute database migrations for the TurdParty application.

This script follows the migration strategy outlined in migration_strategy.md
and performs the following steps:
1. Validates the current database state
2. Backs up the database (if possible)
3. Executes migrations in phased sequence
4. Validates post-migration state
5. Reports any issues

Usage:
    python scripts/run_migrations.py [--dry-run] [--rollback-to REVISION]
"""
import os
import sys
import argparse
import subprocess
import logging
import datetime
import re
from pathlib import Path

# Setup logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)
log_file = log_dir / f"migration_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("migration")

# Define migration phases and their corresponding revisions
MIGRATIONS = [
    {"id": "20250328_152652_enhance_base_model", "name": "Enhance Base Model", "phase": 1},
    {"id": "20250328_152717_table_structure_changes", "name": "Table Structure Changes", "phase": 1},
    {"id": "20250328_152750_column_additions_and_type_mods", "name": "Column Additions and Type Modifications", "phase": 2},
    {"id": "20250328_152825_data_migrations", "name": "Data Migrations", "phase": 2},
    {"id": "20250328_152858_add_constraints", "name": "Add Constraints", "phase": 3},
]


def run_command(command, shell=False, check=True, capture_output=True):
    """Run a command and return its output."""
    logger.info(f"Running command: {command}")
    try:
        result = subprocess.run(
            command,
            shell=shell,
            check=check,
            capture_output=capture_output,
            text=True
        )
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with exit code {e.returncode}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        raise


def get_current_revision():
    """Get the current Alembic revision."""
    try:
        result = run_command(["alembic", "current"])
        match = re.search(r"([a-f0-9_]+)(?:\s+\(head\))?", result.stdout)
        if match:
            return match.group(1)
        return None
    except Exception as e:
        logger.error(f"Failed to get current revision: {e}")
        return None


def backup_database():
    """Create a backup of the database."""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"db_backup_{timestamp}.sql"
    
    try:
        # This is a simplified example. In practice, you'd need to configure
        # the connection parameters based on your environment.
        pg_dump_cmd = f"pg_dump -h localhost -U turdparty turdparty_db > {backup_file}"
        run_command(pg_dump_cmd, shell=True)
        logger.info(f"Database backed up to {backup_file}")
        return backup_file
    except Exception as e:
        logger.error(f"Failed to backup database: {e}")
        logger.warning("Proceeding without backup")
        return None


def validate_schema(phase):
    """Validate the database schema matches expectations for the given phase."""
    logger.info(f"Validating schema for phase {phase}")
    
    # Run validation SQL queries appropriate for the migration phase
    # This would be customized based on your specific validation needs
    validation_queries = {
        "pre": [
            "SELECT COUNT(*) FROM users",
            "SELECT COUNT(*) FROM vagrant_vm",
            "SELECT COUNT(*) FROM vm_injection"
        ],
        "post": [
            "SELECT COUNT(*) FROM users",
            "SELECT COUNT(*) FROM vagrant_vms",
            "SELECT COUNT(*) FROM vm_injections",
            "SELECT COUNT(*) FROM model_history"
        ]
    }
    
    # Execute validation queries and log results
    # (Implementation would connect to your database and run these queries)
    logger.info("Schema validation complete")
    return True


def apply_migration(migration_id, dry_run=False):
    """Apply a specific migration."""
    logger.info(f"Applying migration: {migration_id}")
    
    if dry_run:
        # Generate SQL but don't execute
        result = run_command(["alembic", "upgrade", migration_id, "--sql"])
        logger.info(f"SQL for migration {migration_id}:\n{result.stdout}")
        return True
    
    try:
        result = run_command(["alembic", "upgrade", migration_id])
        logger.info(f"Migration {migration_id} applied successfully")
        logger.debug(result.stdout)
        return True
    except Exception as e:
        logger.error(f"Failed to apply migration {migration_id}: {e}")
        return False


def rollback_to(revision):
    """Rollback to a specific revision."""
    logger.info(f"Rolling back to revision: {revision}")
    
    try:
        result = run_command(["alembic", "downgrade", revision])
        logger.info(f"Rollback to {revision} successful")
        logger.debug(result.stdout)
        return True
    except Exception as e:
        logger.error(f"Failed to rollback to {revision}: {e}")
        return False


def run_tests():
    """Run tests to validate the migrated system."""
    logger.info("Running validation tests")
    
    try:
        # Run database-specific tests
        result = run_command(["pytest", "api/tests/db", "-v"])
        if result.returncode != 0:
            logger.error("Database tests failed")
            return False
        
        # Run schema validation tests
        result = run_command(["pytest", "api/tests/schemas", "-v"])
        if result.returncode != 0:
            logger.error("Schema validation tests failed")
            return False
        
        logger.info("All tests passed")
        return True
    except Exception as e:
        logger.error(f"Error running tests: {e}")
        return False


def main():
    """Execute the migration process."""
    parser = argparse.ArgumentParser(description="Database migration script")
    parser.add_argument("--dry-run", action="store_true", help="Generate SQL but don't execute")
    parser.add_argument("--rollback-to", help="Rollback to specific revision")
    parser.add_argument("--skip-backup", action="store_true", help="Skip database backup")
    parser.add_argument("--skip-tests", action="store_true", help="Skip post-migration tests")
    args = parser.parse_args()
    
    logger.info("Starting database migration process")
    
    if args.rollback_to:
        success = rollback_to(args.rollback_to)
        sys.exit(0 if success else 1)
    
    # Get current revision
    current_revision = get_current_revision()
    logger.info(f"Current database revision: {current_revision}")
    
    # Validate pre-migration schema
    if not validate_schema("pre"):
        logger.error("Pre-migration schema validation failed")
        sys.exit(1)
    
    # Backup database
    if not args.skip_backup and not args.dry_run:
        backup_file = backup_database()
        if not backup_file:
            logger.warning("Failed to create database backup")
    
    # Apply migrations in sequence
    failed_migrations = []
    successful_migrations = []
    
    for migration in MIGRATIONS:
        logger.info(f"Phase {migration['phase']}: {migration['name']}")
        success = apply_migration(migration["id"], args.dry_run)
        
        if success:
            successful_migrations.append(migration["id"])
        else:
            failed_migrations.append(migration["id"])
            logger.error(f"Migration {migration['id']} failed")
            break
    
    # Report status
    if failed_migrations:
        logger.error("Migration process failed")
        logger.error(f"Failed migrations: {', '.join(failed_migrations)}")
        logger.error(f"Successful migrations: {', '.join(successful_migrations)}")
        
        # If any migration failed, offer to rollback
        if not args.dry_run and successful_migrations:
            rollback_point = current_revision if current_revision else "base"
            logger.info(f"Rolling back to {rollback_point}")
            rollback_to(rollback_point)
        
        sys.exit(1)
    
    # Validate post-migration schema
    if not args.dry_run:
        if not validate_schema("post"):
            logger.error("Post-migration schema validation failed")
            sys.exit(1)
        
        # Run tests
        if not args.skip_tests:
            if not run_tests():
                logger.error("Post-migration tests failed")
                sys.exit(1)
    
    logger.info("Migration process completed successfully")


if __name__ == "__main__":
    main() 