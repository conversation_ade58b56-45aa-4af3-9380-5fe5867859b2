#!/usr/bin/env bash
# Script to run the file upload to VM E2E tests in Docker and collect video evidence

# Set script to exit on any error
set -e

# Create directories for outputs
mkdir -p ./test_output/videos
mkdir -p ./test_output/screenshots

echo "===== Running File Upload to VM E2E Tests in Docker ====="

# Get the React container ID
REACT_CONTAINER=$(docker ps | grep dockerwrapper-react | awk '{print $1}')

if [ -z "$REACT_CONTAINER" ]; then
  echo "Error: React container not found!"
  exit 1
fi

echo "Using React container: $REACT_CONTAINER"

# Copy the test files to the container
echo "Copying test files to container..."
docker cp tests/playwright/file-upload-to-vm.spec.ts $REACT_CONTAINER:/app/tests/
docker cp tests/playwright/direct-api-file-to-vm.spec.ts $REACT_CONTAINER:/app/tests/
docker cp tests/playwright/playwright.config.js $REACT_CONTAINER:/app/playwright.config.js

# Run the test inside the container
echo "Running tests in container..."
docker exec -t $REACT_CONTAINER sh -c "cd /app && mkdir -p test_screenshots && npx playwright install chromium && PLAYWRIGHT_BROWSERS_PATH=/app/node_modules/playwright npx playwright test tests/file-upload-to-vm.spec.ts tests/direct-api-file-to-vm.spec.ts --config=playwright.config.js"

# Copy the videos and screenshots from the container
echo "Retrieving test results..."
docker cp $REACT_CONTAINER:/app/test-results ./test_output/
docker cp $REACT_CONTAINER:/app/test_screenshots ./test_output/

# Process the videos
echo "Processing videos..."
find ./test_output/test-results -name "*.webm" -exec cp {} ./test_output/videos/ \;

echo "===== Tests Completed ====="
echo "Results:"
echo "- Videos: ./test_output/videos"
echo "- Screenshots: ./test_output/screenshots"
echo "- Full results: ./test_output/test-results"

# Show the evidence
echo "Video evidence captured successfully!"
echo "Video files:"
ls -la ./test_output/videos/

echo "Screenshot files:"
ls -la ./test_output/screenshots/

echo "===== Done =====" 