# Test Scripts

This directory contains utility scripts for the TurdParty project.

## File Upload Testing Scripts

### run-enhanced-upload-test.sh

This script runs enhanced file upload tests against multiple targets using <PERSON><PERSON>.

```bash
# Run the enhanced file upload tests
./scripts/run-enhanced-upload-test.sh
```

The script:
- Automatically detects Docker containers (frontend, API, Playwright)
- Configures test environments with appropriate container IP addresses
- Runs tests across multiple targets (frontend, containerized frontend, API)
- Captures detailed screenshots and diagnostic information
- Consolidates results from all test runs

### curl-upload-test.sh

This script tests file uploads using curl directly from various containers.

```bash
# Test file uploads using curl
./scripts/curl-upload-test.sh
```

### debug-container-network.sh

Debug Docker container network connectivity issues.

```bash
# Debug container network connectivity
./scripts/debug-container-network.sh
```

### simple-frontend-check.sh

Verify basic frontend accessibility and functionality.

```bash
# Verify frontend is accessible
./scripts/simple-frontend-check.sh
```

## Test Coverage Scripts

### generate_test_coverage.py

This script generates test coverage data for the API endpoints.

```
Usage: python generate_test_coverage.py [options]

Options:
  --modules MODULE [MODULE ...]   Modules to test, e.g. api/tests/test_health_endpoints.py
  --output-dir OUTPUT_DIR         Output directory for reports (default: test-results/coverage)
  --no-html                       Skip HTML report generation
  --no-xml                        Skip XML report generation
  --clean                         Clean old reports
  --max-age MAX_AGE               Maximum age of reports to keep in days (default: 30)
  --sample                        Generate sample test results (for testing)
```

### run_tests_and_report.py

This script runs tests with coverage and reports the results to the API endpoint.

```
Usage: python run_tests_and_report.py [options]

Options:
  --modules MODULE [MODULE ...]   Specific test modules to run
  --no-report                     Skip sending results to API
  --verbose                       Show detailed output
  --api-url API_URL               Base URL for the API (default: http://localhost:3050)
  --sample                        Generate sample test data
```

## How to Use

### Generate Test Coverage

To generate test coverage data:

```bash
python scripts/generate_test_coverage.py
```

This will run all tests and generate coverage reports in the default locations.

### Generate Sample Test Data

To generate sample test data for testing:

```bash
python scripts/generate_test_coverage.py --sample
```

This creates sample test result files without running actual tests.

### Run Tests and Report Results

To run tests and report the results to the API:

```bash
python scripts/run_tests_and_report.py
```

The script will:
1. Run tests with coverage
2. Get the coverage data
3. Send the results to the API endpoint
4. Provide a summary report

### Run with Sample Data

To test the reporting functionality with sample data:

```bash
python scripts/run_tests_and_report.py --sample
```

## API Endpoint

The test results are reported to the `/api/v1/health/test-runs` endpoint, which:

- Requires authentication (automatically handled by the script)
- Returns a JSON response with:
  - Latest coverage data
  - Test result summaries
  - Optional historical coverage data

The endpoint can be viewed in a browser at:
http://localhost:3050/api/v1/health/test-runs (authentication required) 