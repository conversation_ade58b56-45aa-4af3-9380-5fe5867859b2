#!/usr/bin/env python
"""
Test runner script with coverage reporting.
"""
import sys
import os
import subprocess
import argparse
import logging
import importlib.util

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from api.core.logging_config import setup_logging
from api.core.dependencies import check_dependencies

# Set up logging
logger = setup_logging(log_level=logging.INFO)

def check_test_dependencies():
    """Check if required testing packages are installed."""
    test_deps = {
        "pytest": ">=7.0.0",
        "coverage": ">=7.0.0"
    }

    result = check_dependencies(test_deps)

    if result["failure"]:
        logger.error(f"Missing required packages: {', '.join(result['failure'])}")
        logger.error("Please install required packages via pyproject.toml")
        logger.info("Your pyproject.toml already has these packages in [tool.poetry.group.dev.dependencies]")
        logger.info("Try running 'poetry install --with dev' in the shell to install them")
        return False

    return True

def run_tests(test_category=None, with_coverage=False):
    """
    Run tests with optional coverage reporting.

    Args:
        test_category: The category of tests to run (unit, integration, e2e, or None for all)
        with_coverage: Whether to enable coverage reporting

    Returns:
        The exit code from pytest
    """
    logger.info(f"Running tests: category={test_category or 'all'}, coverage={with_coverage}")

    # Determine test paths based on category
    test_paths = []
    if test_category == "unit":
        test_paths = [
            "api/tests/test_models.py",
            "api/tests/test_repositories.py",
            "api/tests/test_services.py",
            "api/tests/test_vagrant_client.py"
        ]
    elif test_category == "integration":
        test_paths = [
            "api/tests/test_api_endpoints.py",
            "api/tests/test_error_handling.py", 
            "api/tests/test_vagrant_endpoints.py"
        ]
    elif test_category == "e2e":
        test_paths = ["api/tests/test_e2e.py"]
    else:
        # Run all tests
        test_paths = ["api/tests"]

    # Build command
    cmd = ["pytest", "-v"]

    # Add coverage if requested
    if with_coverage:
        # Use Python's coverage module with pytest
        cmd = ["coverage", "run", "-m", "pytest"] + cmd[1:]
        # Don't add HTML and JSON arguments to pytest; they'll be handled separately

    # Add test paths
    cmd.extend(test_paths)

    logger.info(f"Running command: {' '.join(cmd)}")

    try:
        # Check dependencies first
        if not check_test_dependencies():
            return 1

        # Run the command
        result = subprocess.run(cmd, check=False, capture_output=True, text=True)

        # Log output
        logger.info(result.stdout)
        if result.stderr:
            logger.error(result.stderr)

        # Generate coverage reports if coverage was run
        if with_coverage:
            logger.info("Generating coverage reports...")
            try:
                # Generate coverage reports in different formats
                report_process = subprocess.run(
                    ["coverage", "report"], 
                    check=False,
                    capture_output=True, 
                    text=True
                )
                logger.info(report_process.stdout)

                html_process = subprocess.run(
                    ["coverage", "html"], 
                    check=False,
                    capture_output=True, 
                    text=True
                )

                json_process = subprocess.run(
                    ["coverage", "json"], 
                    check=False,
                    capture_output=True, 
                    text=True
                )

                if report_process.returncode == 0 and html_process.returncode == 0 and json_process.returncode == 0:
                    logger.info("Coverage reports generated successfully")
                else:
                    logger.warning("Some coverage reports may not have been generated correctly")
                    if report_process.stderr:
                        logger.warning(f"Report error: {report_process.stderr}")
                    if html_process.stderr:
                        logger.warning(f"HTML error: {html_process.stderr}")
                    if json_process.stderr:
                        logger.warning(f"JSON error: {json_process.stderr}")
            except Exception as e:
                logger.error(f"Error generating coverage reports: {str(e)}")

        return result.returncode
    except Exception as e:
        logger.error(f"Error running tests: {str(e)}")
        return 1

def main():
    """Run tests with optional coverage reporting."""
    parser = argparse.ArgumentParser(description="Run tests with optional coverage reporting")
    parser.add_argument(
        "--category",
        choices=["unit", "integration", "e2e", "all"],
        default="all",
        help="Category of tests to run"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Generate coverage reports"
    )
    parser.add_argument(
        "--file",
        help="Specific test file to run"
    )

    args = parser.parse_args()

    if args.file:
        # Run a specific test file
        cmd = ["pytest", "-v", args.file]
        if args.coverage:
            cmd.extend(["--cov=api", "--cov-report=term", "--cov-report=html", "--cov-report=json"])

        result = subprocess.run(cmd)
        sys.exit(result.returncode)
    else:
        # Run by category
        category = None if args.category == "all" else args.category
        sys.exit(run_tests(test_category=category, with_coverage=args.coverage))

if __name__ == "__main__":
    sys.exit(main())