#!/bin/bash

# Script to run Playwright tests in a Docker container with proper permissions

# Set variables
CONTAINER_NAME="turdparty-playwright"
IMAGE_NAME="turdparty-playwright-image"
SCREENSHOTS_DIR="docs/screenshots/playwright-tests"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="${SCREENSHOTS_DIR}/${TIMESTAMP}"
ABSOLUTE_OUTPUT_DIR="$(pwd)/${OUTPUT_DIR}"

# Ensure the screenshots directory exists
mkdir -p ${OUTPUT_DIR}
echo "Screenshots will be saved to: ${OUTPUT_DIR}"

# Build the Playwright Docker image if it doesn't exist
if [[ "$(docker images -q ${IMAGE_NAME} 2> /dev/null)" == "" ]]; then
  echo "Building Playwright Docker image..."
  docker build -t ${IMAGE_NAME} -f .dockerwrapper/Dockerfile.playwright .
fi

# Check if the container is already running
if [[ "$(docker ps -q -f name=${CONTAINER_NAME} 2> /dev/null)" == "" ]]; then
  # Container is not running, start it
  echo "Starting Playwright Docker container..."
  docker run -d --name ${CONTAINER_NAME} \
    -v "$(pwd):/app" \
    -v "${ABSOLUTE_OUTPUT_DIR}:/app/test-results" \
    -v "${HOME}/.cache/ms-playwright:/home/<USER>/.cache/ms-playwright" \
    -p 9323:9323 \
    --network="host" \
    ${IMAGE_NAME}
else
  echo "Playwright Docker container is already running."
fi

# Create a temporary Playwright config in the container
echo "Creating temporary Playwright config..."
docker exec -u playwright-user ${CONTAINER_NAME} bash -c "cat > /app/turdparty-app/temp-playwright.config.ts << 'EOF'
import { defineConfig, devices } from '@playwright/test';
import path from 'path';

export default defineConfig({
  testDir: './tests/playwright',
  timeout: 30000,
  fullyParallel: false,
  forbidOnly: false,
  retries: 0,
  workers: 1,
  reporter: 'html',
  outputDir: path.resolve(__dirname, '../test-results'),
  use: {
    baseURL: 'http://localhost:8000',
    trace: 'on',
    screenshot: 'on',
    video: 'on',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
  webServer: {
    command: 'echo \"Using existing server at http://localhost:8000\"',
    url: 'http://localhost:8000',
    reuseExistingServer: true,
    timeout: 5000,
  },
});
EOF"

# Run the authentication setup first
echo "Setting up authentication..."
docker exec -u playwright-user ${CONTAINER_NAME} bash -c "cd /app/turdparty-app && npx playwright test tests/playwright/auth.setup.ts"

# Run the Playwright tests with screenshots enabled
echo "Running Playwright tests with screenshots..."
docker exec -u playwright-user ${CONTAINER_NAME} bash -c "cd /app/turdparty-app && npx playwright test --config=temp-playwright.config.ts"

# Clean up the temporary config
docker exec -u playwright-user ${CONTAINER_NAME} bash -c "rm /app/turdparty-app/temp-playwright.config.ts"

# Create a symlink to the latest screenshots
echo "Creating symlink to latest screenshots..."
rm -f ${SCREENSHOTS_DIR}/latest
ln -s ${TIMESTAMP} ${SCREENSHOTS_DIR}/latest

echo "Tests completed. Screenshots have been saved to ${OUTPUT_DIR}"
echo "You can view the test report by opening ${OUTPUT_DIR}/report/index.html" 