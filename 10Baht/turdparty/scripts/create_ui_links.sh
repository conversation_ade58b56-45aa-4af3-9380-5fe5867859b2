#!/bin/bash

# Script to generate HTML files with links to the UI

# Ensure the screenshots directory exists
mkdir -p ../docs/screenshots/ui-links

# Create a timestamp for this run
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LINKS_DIR="../docs/screenshots/ui-links/$TIMESTAMP"
mkdir -p "$LINKS_DIR"

# Create an index file
cat > "$LINKS_DIR/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty UI Links</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .timestamp {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        ul {
            list-style-type: none;
            padding: 0;
        }
        li {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #2196F3;
        }
        a {
            color: #2196F3;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
        .note {
            margin-top: 20px;
            padding: 10px;
            background-color: #fffde7;
            border-left: 4px solid #ffd600;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TurdParty UI Links</h1>
        <div class="timestamp">Generated on: $(date)</div>
        <ul>
            <li><a href="http://localhost:3000/" target="_blank">Home Page</a></li>
            <li><a href="http://localhost:3000/vm-status" target="_blank">VM Status Page</a></li>
            <li><a href="http://localhost:3000/files" target="_blank">Files Page</a></li>
            <li><a href="http://localhost:3000/vms" target="_blank">VMs Page</a></li>
            <li><a href="http://localhost:3000/injections" target="_blank">Injections Page</a></li>
            <li><a href="http://localhost:3000/docs" target="_blank">Documentation Page</a></li>
        </ul>
        <div class="note">
            Note: These links point to the local development server. Make sure the server is running before clicking on the links.
        </div>
    </div>
</body>
</html>
EOF

# Create a symlink to the latest links
if [ -L "../docs/screenshots/ui-links/latest" ]; then
    rm ../docs/screenshots/ui-links/latest
fi
ln -s "$TIMESTAMP" ../docs/screenshots/ui-links/latest

# Create a root index file
cat > "../docs/screenshots/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty Screenshots</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .timestamp {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        ul {
            list-style-type: none;
            padding: 0;
        }
        li {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #2196F3;
        }
        a {
            color: #2196F3;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
        .note {
            margin-top: 20px;
            padding: 10px;
            background-color: #fffde7;
            border-left: 4px solid #ffd600;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TurdParty Screenshots</h1>
        <div class="timestamp">Generated on: $(date)</div>
        
        <h2>UI Links</h2>
        <ul>
            <li><a href="ui-links/latest/index.html">UI Links</a></li>
        </ul>
        
        <h2>Playwright Tests</h2>
        <ul>
            <li><a href="playwright-tests/">Playwright Tests</a></li>
        </ul>
        
        <div class="note">
            Note: These screenshots and links are excluded from git via .gitignore to avoid bloating the repository with binary files.
        </div>
    </div>
</body>
</html>
EOF

echo "UI links have been generated at $LINKS_DIR/index.html"
echo "You can view the links by opening ../docs/screenshots/index.html in a browser"
echo "Note: This directory is excluded from git via .gitignore" 