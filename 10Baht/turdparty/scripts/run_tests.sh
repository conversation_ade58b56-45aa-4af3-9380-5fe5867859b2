#!/bin/bash
# <PERSON><PERSON>t to run the validation tests using the existing dockerwrapper setup

set -e  # Exit on error

echo "Running tests using the dockerwrapper API container..."

# Check if API container is running
if ! docker ps | grep -q "dockerwrapper_api_1"; then
    echo "Starting dockerwrapper services..."
    cd .dockerwrapper && docker compose up -d api
    cd ..
    echo "Waiting for services to start..."
    sleep 10
fi

# Copy our schema test files to the container
echo "Copying test files to container..."
docker cp api/tests/schemas dockerwrapper_api_1:/app/api/tests/
docker cp scripts/test_model_integration.py dockerwrapper_api_1:/app/scripts/

# Run the tests in the container
echo "Running schema validation tests..."
docker exec dockerwrapper_api_1 bash -c "
    cd /app
    python -m pytest api/tests/schemas/ -v
"

# Run the integration test
echo "Running model integration test..."
docker exec -it dockerwrapper_api_1 bash -c "
    cd /app
    python scripts/test_model_integration.py || echo 'Integration test requires database setup'
"

echo "Tests completed!" 