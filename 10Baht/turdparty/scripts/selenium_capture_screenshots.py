#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to capture screenshots using Selenium.

This script navigates to different pages of the application and captures
screenshots using Selenium WebDriver.
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Check if selenium is installed
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
except ImportError:
    print("Selenium not installed. Installing now...")
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "selenium", "--break-system-packages"])
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

# Define paths
PROJECT_ROOT = Path(__file__).parent.parent
SCREENSHOTS_DIR = PROJECT_ROOT / "docs" / "screenshots" / "selenium-tests"

# Create the screenshots directory
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

# Create a timestamp for this test run
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
run_dir = SCREENSHOTS_DIR / timestamp
os.makedirs(run_dir, exist_ok=True)

# Create a symlink to the latest screenshots
latest_link = SCREENSHOTS_DIR / "latest"
try:
    if os.path.exists(latest_link):
        os.remove(latest_link)
    os.symlink(run_dir, latest_link, target_is_directory=True)
    print(f"Created symlink: {latest_link} -> {run_dir}")
except Exception as e:
    print(f"Failed to create symlink: {e}")

# Define the base URL
BASE_URL = "http://localhost:3000"  # Frontend app
API_URL = "http://localhost:8000"   # Backend API

def capture_screenshots():
    """Capture screenshots of the main functionality."""
    # Set up Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Initialize the WebDriver
    try:
        driver = webdriver.Chrome(options=chrome_options)
    except Exception as e:
        print(f"Failed to initialize Chrome WebDriver: {e}")
        print("Trying Firefox WebDriver...")
        try:
            driver = webdriver.Firefox()
        except Exception as e:
            print(f"Failed to initialize Firefox WebDriver: {e}")
            print("Exiting...")
            return
    
    try:
        # Home page
        print("Capturing Home page...")
        driver.get(f"{BASE_URL}/")
        time.sleep(2)  # Wait for page to load
        driver.save_screenshot(str(run_dir / "01-home-page.png"))
        print("✅ Home page captured")
        
        # VM Status page
        print("Capturing VM Status page...")
        driver.get(f"{BASE_URL}/vm-status")
        time.sleep(2)  # Wait for page to load
        driver.save_screenshot(str(run_dir / "02-vm-status.png"))
        print("✅ VM Status page captured")
        
        # Try to capture VM details if available
        try:
            vm_items = driver.find_elements(By.CSS_SELECTOR, ".ant-list-item")
            if vm_items:
                vm_items[0].click()
                time.sleep(2)  # Wait for details to load
                driver.save_screenshot(str(run_dir / "03-vm-details.png"))
                print("✅ VM Details captured")
                
                # Capture different tabs
                tabs = driver.find_elements(By.CSS_SELECTOR, 'div[role="tab"]')
                for tab in tabs:
                    if "Injections" in tab.text:
                        tab.click()
                        time.sleep(1)
                        driver.save_screenshot(str(run_dir / "04-vm-injections.png"))
                        print("✅ VM Injections tab captured")
                    elif "Logs" in tab.text:
                        tab.click()
                        time.sleep(1)
                        driver.save_screenshot(str(run_dir / "05-vm-logs.png"))
                        print("✅ VM Logs tab captured")
        except Exception as e:
            print(f"Failed to capture VM details: {e}")
        
        # Files page
        print("Capturing Files page...")
        driver.get(f"{BASE_URL}/files")
        time.sleep(2)  # Wait for page to load
        driver.save_screenshot(str(run_dir / "06-files.png"))
        print("✅ Files page captured")
        
        # VMs page
        print("Capturing VMs page...")
        driver.get(f"{BASE_URL}/vms")
        time.sleep(2)  # Wait for page to load
        driver.save_screenshot(str(run_dir / "07-vms.png"))
        print("✅ VMs page captured")
        
        # Injections page
        print("Capturing Injections page...")
        driver.get(f"{BASE_URL}/injections")
        time.sleep(2)  # Wait for page to load
        driver.save_screenshot(str(run_dir / "08-injections.png"))
        print("✅ Injections page captured")
        
        # Documentation page
        print("Capturing Documentation page...")
        driver.get(f"{BASE_URL}/docs")
        time.sleep(2)  # Wait for page to load
        driver.save_screenshot(str(run_dir / "09-docs.png"))
        print("✅ Documentation page captured")
        
        # Language switcher functionality
        print("Capturing Language switcher functionality...")
        driver.get(f"{BASE_URL}/")
        time.sleep(2)  # Wait for page to load
        
        # Find and click the language switcher
        try:
            language_switchers = driver.find_elements(By.CSS_SELECTOR, ".ant-select")
            for switcher in language_switchers:
                if "English" in switcher.text:
                    switcher.click()
                    time.sleep(1)
                    driver.save_screenshot(str(run_dir / "10-language-switcher-open.png"))
                    print("✅ Language switcher open captured")
                    
                    # Select German
                    options = driver.find_elements(By.CSS_SELECTOR, ".ant-select-item-option")
                    for option in options:
                        if "Deutsch" in option.text:
                            option.click()
                            time.sleep(2)
                            driver.save_screenshot(str(run_dir / "11-german-language.png"))
                            print("✅ German language UI captured")
                            
                            # Go to VM Status page to see translations
                            driver.get(f"{BASE_URL}/vm-status")
                            time.sleep(2)
                            driver.save_screenshot(str(run_dir / "12-vm-status-german.png"))
                            print("✅ VM Status page in German captured")
                            break
                    break
        except Exception as e:
            print(f"Failed to capture language switcher functionality: {e}")
        
        print(f"\nScreenshots have been saved to {run_dir}")
        print("Note: This directory is excluded from git via .gitignore")
    
    finally:
        # Close the browser
        driver.quit()

if __name__ == "__main__":
    capture_screenshots() 