# TurdParty API

## Running Tests Without Authentication

This repository includes a solution to run the API testsuite without the auth-middleware requiring a token. This is useful for testing endpoints that would normally require authentication.

### How It Works

1. **Test Mode Configuration**
   - Modified `api/core/test_config.py` to enable test mode by default
   - Set `is_test_mode: bool = True` in the `TestSettings` class

2. **Authentication Middleware**
   - Modified `api/middleware/auth_middleware.py` to properly handle test mode
   - Updated the `get_current_user` function to return a valid test user when test mode is enabled
   - Ensured the test user has all required fields (id as UUID, created_on, modified_on, etc.)

3. **Test Token Endpoint**
   - Added a test token endpoint in `api/routes/auth.py`
   - Created a `/api/auth/test-token` endpoint that returns a valid token when test mode is enabled
   - Modified the endpoint to use the correct parameters for `create_access_token`

4. **Docker Container with TEST_MODE**
   - Created a new container with `TEST_MODE=true` to enable the test token endpoint
   - This allows the test token endpoint to work properly

### Running Tests

To run tests with authentication bypassed, use the following command:

```bash
# Run all tests
docker exec -it TurdParty-container-test python -m pytest api/tests

# Run specific tests
docker exec -it TurdParty-container-test python -m pytest api/tests/test_specific_file.py
```

### Verifying Authentication Bypass

To verify that authentication is properly bypassed, use the following command:

```bash
./override_dependencies.py
```

This script:
1. Requests a test token from the `/api/auth/test-token` endpoint
2. Uses the token to access a protected endpoint (`/api/auth/me`)
3. Verifies that the request is successful (not 401 Unauthorized)

### Troubleshooting

If you encounter authentication issues:
1. Verify that test mode is enabled by checking `test_settings.is_testing()`
2. Check that the middleware is correctly bypassing authentication
3. Ensure that the User model is correctly instantiated in test mode
4. Verify that the `TEST_MODE` environment variable is set to `true` in the container

## Current Challenges and Solutions

### File Upload Functionality

The file upload functionality is currently experiencing issues that need to be addressed:

1. **404 Errors**: The file upload endpoint is returning 404 errors in some cases, likely due to inconsistent API path handling.
2. **Path Inconsistencies**: The frontend is using the new versioned API paths (`/api/v1/file_upload`), but some backend routes may still be using the old paths.
3. **Multiple File Upload**: The folder upload functionality needs additional testing and debugging.

#### Debugging and Testing

To help diagnose and fix these issues, we've implemented:

1. **Enhanced Test Suite**: A new debugging-focused test suite in `turdparty-app/tests/playwright/file-upload-debug.spec.ts` that includes:
   - Detailed network request/response logging
   - Step-by-step verification of the upload process
   - API endpoint URL debugging
   - Mock API response testing
   - Screenshots on test failures

2. **API Path Consistency**: Updated the application to use a consistent `/api/v1/` prefix for all API endpoints:
   - Created a centralized API URL management utility in `turdparty-app/src/utils/apiConfig.ts`
   - Updated the main router in `api/application.py` to use a consistent prefix
   - Fixed duplicate prefixes in nested routers

3. **Error Logging**: Implemented comprehensive error logging for both frontend and backend:
   - UI errors are now logged to the backend via `/api/v1/logs/ui-error`
   - Error boundaries catch and report React component errors
   - Network errors are logged with detailed context information

### Running the Debug Tests

To run the new debugging tests for the file uploader:

```bash
# Run the debug tests
cd turdparty-app
npx playwright test tests/playwright/file-upload-debug.spec.ts --headed

# Run with verbose logging
npx playwright test tests/playwright/file-upload-debug.spec.ts --headed --debug
```

The test results and logs will help identify the specific issues with the file upload functionality.

# Project Milestones

## Milestone 1: Foundation Setup
- ✅ Project structure established with FastAPI backend
- ✅ Database configurations and connection handling implemented
- ✅ Dependency checking system implemented to ensure all required packages are installed
- ✅ Base soft-delete model implemented to support logical deletion
- ✅ Logging system configured with file rotation and console output

## Milestone 2: UI Framework Integration
- ✅ Service connector pattern for API communication
- ✅ Item service implementation with full CRUD operations
- ✅ Component architecture for UI development
- ✅ Test suite for UI services

## Milestone 3: Advanced Services Integration
- ✅ MinIO object storage integration via SSH tunneling
- ✅ Vagrant VM management via SSH communication
- ✅ Authentication system with JWT token support
- ✅ Health monitoring and status reporting

## Milestone 4: Error Handling and Monitoring (Current)
- ✅ Error boundary implementation for React components
- ✅ UI error logging to backend API
- ✅ Performance monitoring and visualization
- ✅ API versioning with consistent `/api/v1/` prefix
- 🔄 File upload functionality debugging and fixes
- 🔄 Enhanced test suite for debugging and verification

## Next Steps
- Complete file upload functionality fixes
- Extend test coverage for critical workflows
- User management enhancements
- UI improvements
- Multilingual documentation expansion
- Static analysis service integration

## Fathom SSH Integration

The project has been refactored to use SSH for Vagrant communication via Fathom:

- **Direct SSH Communication**: Connect to Vagrant VMs using standard SSH protocol
- **Environment Variable Configuration**: Configure SSH settings using environment variables
- **Automatic Key Setup**: Scripts to set up and validate SSH keys automatically
- **Secure Key Handling**: Support for both key path and direct key content via environment variables
- **Validation**: Key validation before connection attempts to ensure proper formatting

### SSH Configuration

- **VAGRANT_SSH_KEY**: Direct SSH key content as a string (ED25519 format, no newlines)
- **SSH_KEY_PATH**: Path to SSH key file (default: "~/.ssh/replit")
- **DEFAULT_VAGRANT_SSH_USER**: Username for SSH connections (default: "vagrant")
- **DEFAULT_VAGRANT_SERVER**: Default Vagrant server hostname/IP

See [Vagrant SSH Documentation](docs/vagrant_ssh.md) for more details.

## MinIO Storage

The project now includes MinIO object storage integration:

- **SSH Tunneling**: Secure connection to MinIO server through SSH
- **S3-Compatible API**: Full support for bucket and object operations
- **Health Monitoring**: Status checks and statistics for the MinIO server
- **Comprehensive Testing**: Extensive test suite for MinIO operations

See [MinIO Storage Documentation](docs/minio_storage.md) for more details.

## Authentication System

The project implements a JWT (JSON Web Token) based authentication system:

- **Token-Based Authentication**: Users authenticate using JWT tokens
- **Test Token Generation**: For development purposes, a test token generator script is available
- **Role-Based Authorization**: Support for regular users and superusers
- **Token Expiration**: Configurable token expiration times
- **Test Mode**: Authentication bypass option for development and testing

### Using the Test Token Generator

For development and testing purposes, you can generate test authentication tokens:

```bash
# Generate a regular user token
python scripts/generate_test_token.py

# Generate an admin/superuser token
python scripts/generate_test_token.py --admin

# Generate a token for a specific username
python scripts/generate_test_token.py --username john
```

The token can be used to authenticate API requests by including it in the Authorization header:
```
Authorization: Bearer your_token_here
```

### Test Mode for Authentication Bypass

During development and testing, you can enable test mode to bypass authentication requirements:

```bash
# Enable test mode (bypass authentication)
python scripts/toggle_test_mode.py --enable

# Disable test mode (enforce authentication)
python scripts/toggle_test_mode.py --disable

# Check current test mode status
python scripts/toggle_test_mode.py --status
```

See [Test Mode Documentation](docs/test_mode.md) for more details.

## Error Handling

This project implements a comprehensive error handling system:

- **Centralized Error Classes**: API errors are handled with custom exception classes
- **Structured Error Responses**: All API errors return consistent JSON structures
- **Validation Errors**: Pydantic validation errors are formatted consistently
- **Database Errors**: SQLAlchemy errors are properly caught and translated to user-friendly messages
- **Request IDs**: Each request has a unique ID for tracing errors through logs
- **UI Error Logging**: Frontend errors are logged to the backend for centralized monitoring
- **Error Boundaries**: React components are wrapped in error boundaries to prevent cascading failures

## Logging

The project implements a robust logging system:

- **Structured JSON Logs**: All logs are formatted as JSON for easier parsing and analysis
- **Log Rotation**: Log files are automatically rotated to prevent them from growing too large
- **Request Logging**: All HTTP requests are logged with timing information
- **Error Logging**: Errors are logged with full stack traces and context information
- **Database Logging**: Database operations can be logged for debugging
- **Correlation IDs**: Requests can be traced through the system using correlation IDs
- **UI Error Logging**: Frontend errors are logged to `/api/v1/logs/ui-error` endpoint
- **Performance Logging**: API response times and UI performance metrics are logged and visualized

## Documentation Translations

Documentation for this project is available in multiple languages:

- [Afrikaans (af)](./lang/af/)
- [English (UK) (en_GB)](./lang/en_GB/)
- [German (de)](./lang/de/)
- [Romanian (ro)](./lang/ro/)
- [isiZulu (zu)](./lang/zu/)

For more information about the translation process, see [Translation Documentation](./lang/README.md).

## UI Screenshots

The project includes a Playwright-based screenshot capture system to document the UI:

- **Automated Screenshots**: Capture screenshots of all main UI components
- **Internationalization Support**: Screenshots in multiple languages
- **Documentation Ready**: Images suitable for inclusion in documentation
- **Excluded from Git**: Screenshots directory is excluded from Git to avoid repository bloat

### Generating Screenshots

To generate screenshots of the main UI components, you can use one of the following methods:

#### Using Python with Playwright (Recommended)

```bash
# Run the Python screenshot script
python scripts/capture_screenshots.py
```

This script will:
1. Install Playwright if it's not already installed
2. Navigate through the main pages of the application
3. Capture screenshots of each page with timestamps
4. Save them to the `docs/screenshots/[timestamp]` directory
5. Create a symlink to the latest screenshots at `docs/screenshots/latest`

#### Using Docker Container

If you're running the application in a Docker container:

```bash
# Run the Docker screenshot script
./scripts/docker_capture_screenshots.sh
```

#### Using .dockerwrapper Container

If you're using the .dockerwrapper environment:

```bash
# Run the .dockerwrapper screenshot script
./scripts/dockerwrapper_capture_screenshots.sh
```

Screenshots will be saved to the `docs/screenshots/` directory. This directory is excluded from Git via `.gitignore`.

For more information about the screenshots, see [Screenshots Documentation](./docs/screenshots/README.md).

## Architecture Patterns

### Service Connector Pattern

This project implements a service connector pattern for API communication:

- **Generic Type Support**: Type-safe API communication with generic type parameters
- **Consistent Error Handling**: Standardized error handling for all API requests
- **Request Logging**: Comprehensive logging of all API interactions
- **Response Deserialization**: Automatic conversion of JSON responses to typed objects

The implementation consists of:

- **ServiceConnector[T]**: Generic base connector class supporting GET, POST, PUT, DELETE
- **ItemService**: Concrete implementation for item-related API endpoints
- **Test Suite**: Comprehensive unit tests with mocked responses

### Dependency Injection

This project implements a dependency injection container to manage service dependencies:

- **Container**: Centralized registry for application services and repositories
- **Service Resolution**: Automatic resolution of dependencies when needed
- **Testing Support**: Easy mocking of dependencies for unit testing

### Repository Pattern

This project uses a repository pattern for data access:

### BaseRepository
The `BaseRepository` provides a generic implementation for all CRUD operations:
- `get()`: Retrieve a record by ID
- `get_all()`: Get all records with pagination
- `create()`: Create a new record
- `update()`: Update an existing record
- `delete()`: Hard delete a record
- `soft_delete()`: Soft delete a record (setting deleted_on timestamp)
- `restore()`: Restore a soft-deleted record
- `count()`: Count total non-deleted records
- `exists()`: Check if a record exists

### Specific Repositories
Each entity has its own repository that extends `BaseRepository` and implements entity-specific methods:
- `ItemRepository`: Handles CRUD operations for items with methods like `get_by_title()` and `get_active_items()`
- `UserRepository`: Manages user data with authentication-related methods

### Services Layer
Services use repositories to implement business logic and handle data operations, providing a clean separation of concerns.

## UI Component Architecture

The UI is built with a reusable component architecture:

- **BaseComponent**: Abstract base class for UI components with lifecycle methods
- **CardComponent**: Reusable card UI element
- **FormComponent**: Form handling with validation support
- **StatusComponent**: Status indicators and notifications

## Development Guidelines
- Follow PEP-8, PEP-257, and PEP-484 for Python code
- Implement test suite with high coverage 
- API-first development approach
- Maintain comprehensive logging
- Exception handling for resilient operations

## Dependencies

### Python Dependencies

The project relies on the following key Python packages:

#### Core Dependencies
- FastAPI (>=0.95.0): Web framework for building APIs
- Pydantic (>=2.0.0): Data validation and settings management
- SQLAlchemy (>=2.0.0): ORM for database operations
- Uvicorn (>=0.17.0): ASGI server for running the application
- Alembic (>=1.8.0): Database migration tool

#### Database Dependencies
- psycopg2-binary (>=2.9.0): PostgreSQL adapter
- asyncpg (>=0.27.0): Asynchronous PostgreSQL driver

#### Authentication & Security
- PyJWT (>=2.10.1): JWT token handling
- passlib (>=1.7.4): Password hashing utilities
- bcrypt (>=4.0.1): Password hashing algorithm

#### Storage & Remote Access
- paramiko (>=3.0.0): SSH communication library
- boto3 (>=1.34.0): AWS SDK for Python, used for S3/MinIO operations
- grpcio (>=1.59.0): gRPC framework for high-performance RPC

#### Testing Dependencies
- pytest (>=7.4.3): Testing framework
- pytest-cov (>=4.1.0): Coverage plugin for pytest
- coverage (>=7.3.2): Code coverage measurement

### System Dependencies

The following system packages are required:

- openssh: For SSH connections to remote servers
- postgresql: PostgreSQL database client and server
- libxcrypt: Extended crypt library

### Environment Variables and Secrets

The following environment variables/secrets are used:

- `DEFAULT_VAGRANT_SSH_USER`: The username for SSH connections to Vagrant hosts (secret)
- `DEFAULT_VAGRANT_SERVER`: The hostname/IP address of the Vagrant server (secret)
- `DEFAULT_VAGRANT_SSH_KEY`: The ed25519 SSH key with no linebreaks for authentication (secret)
- `SSH_KEY_PATH`: Path to SSH key file (default: "~/.ssh/replit")
- `MINIO_ACCESS_KEY`: MinIO access key (default: "minioadmin")
- `MINIO_SECRET_KEY`: MinIO secret key (default: "minioadmin")

### Checking Dependencies

You can check your installed dependencies using the provided script:

```bash
python scripts/check_dependencies.py
```

To install missing Python dependencies:

```bash
python -m poetry install --with dev
```

The system dependencies are already defined in `replit.nix` for Replit environments.

## Roadmap
- [ ] Dockerwrapper functional and easy to use
- [ ] Translate pages with Deepl API
- [ ] Add user management UI
- [ ] Implement multi-factor authentication
- [ ] Expand MinIO integration with more advanced features
- [ ] Add data visualization components

## Docker Dashboard

The project includes a Docker Dashboard CLI tool to monitor and manage Docker containers for the TurdParty application.

### Features

- **Container Management**: Start, stop, and restart containers
- **Container Monitoring**: View container logs and stats
- **Container Listing**: List all containers with their status, image, ports, restart count, and runtime
- **Interactive Mode**: Start an interactive dashboard UI (coming soon)

### Installation

The Docker Dashboard is automatically installed when you set up the project using Docker Compose. The dashboard runs in its own container and has access to the Docker socket to manage other containers.

### Usage

You can use the Docker Dashboard with the following commands:

```bash
# List all containers with status, restart count, runtime, and ports
docker exec -it dashboard python /app/dashboard.py list

# View logs for a specific container
docker exec -it dashboard python /app/dashboard.py logs <container_name> --lines <number_of_lines>

# View stats for a specific container
docker exec -it dashboard python /app/dashboard.py stats <container_name>

# Start a specific container
docker exec -it dashboard python /app/dashboard.py restart <container_name>

# Start all containers
docker exec -it dashboard python /app/dashboard.py up

# Stop all containers
docker exec -it dashboard python /app/dashboard.py down

# View configuration
docker exec -it dashboard python /app/dashboard.py config
```

### Wrapper Script

For convenience, you can use the `docker-dashboard` wrapper script:

```bash
# List all containers
./docker-dashboard list

# View logs for a specific container
./docker-dashboard logs <container_name> --lines <number_of_lines>

# View stats for a specific container
./docker-dashboard stats <container_name>

# Start a specific container
./docker-dashboard restart <container_name>

# Start all containers
./docker-dashboard up

# Stop all containers
./docker-dashboard down

# View configuration
./docker-dashboard config

# View help
./docker-dashboard --help
```

## Development Environment with Live Editing

This project includes a development environment that allows for live editing of code without needing to rebuild containers. Changes to the code are immediately reflected in the running application.

### Features

- **Live Code Reloading**: Edit code locally and see changes immediately in the running containers
- **Persistent Volumes**: Node modules and Python virtual environments are stored in Docker volumes
- **Development-Optimized**: All services configured for development with debugging enabled

### Getting Started

1. **Start the Development Environment**

   ```bash
   ./.dockerwrapper/dev.sh start
   ```

2. **Access the Application**

   - API: http://localhost:8000
   - Frontend: http://localhost:3000

3. **Managing the Environment**

   ```bash
   # View logs from all containers
   ./.dockerwrapper/dev.sh logs

   # Stop the development environment
   ./.dockerwrapper/dev.sh stop

   # Restart the development environment
   ./.dockerwrapper/dev.sh restart

   # Check status of containers
   ./.dockerwrapper/dev.sh status
   ```

4. **Testing Live Editing**

   The repository includes a script to test live editing functionality:

   ```bash
   ./.dockerwrapper/test-live-editing.sh
   ```

   This script creates test files for both API and frontend that you can modify to verify live editing is working.

### How It Works

The development environment uses Docker volumes to map your local code into the containers:

1. **API Container**: 
   - Maps the entire project directory into `/app` in the container
   - Uses `uvicorn` with the `--reload` flag to automatically reload on code changes
   - Python dependencies are installed in a persistent volume

2. **Frontend Container**:
   - Maps the `turdparty-app` directory into the container
   - Node modules are stored in a persistent volume for better performance
   - Uses React's hot reloading capabilities

For more detailed information, see the [Development Environment README](./.dockerwrapper/DEV_README.md).

# VM Management and AppImage Deployment

A system for managing Vagrant virtual machines and deploying/executing AppImage files on them.

## Features

- **VM Management**: Create, start, stop, restart, and destroy Vagrant VMs
- **Command Execution**: Execute arbitrary commands on VMs
- **AppImage Deployment**: Upload and execute AppImages on VMs
- **Multiple Execution Methods**: 
  - Direct SSH connection
  - Vagrant CLI fallback
  - Simulation mode for testing
- **Robust Fallback Mechanism**: Gracefully handles environments with varying levels of VM access

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Python 3.8+
- Vagrant (optional, for real VM operations)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/replit-10baht-TurdParty-simplified.git
   cd replit-10baht-TurdParty-simplified
   ```

2. Start the Docker environment:
   ```
   cd .dockerwrapper
   docker-compose up -d
   ```

### Usage

#### VM Management

The system provides a REST API for VM management:

- `GET /api/v1/vagrant_vm/` - List all VMs
- `POST /api/v1/vagrant_vm/` - Create a new VM
- `GET /api/v1/vagrant_vm/{id}` - Get VM details
- `POST /api/v1/vagrant_vm/{id}/action` - Perform VM action (start, stop, restart, destroy)
- `POST /api/v1/vagrant_vm/{id}/exec` - Execute command on VM
- `DELETE /api/v1/vagrant_vm/{id}` - Delete VM

#### Command Execution Test Script

The repository includes a test script for executing commands on VMs:

```bash
# Execute a simple command
./vm_exec_test.py --command "echo Hello from VM && hostname"

# Execute a command with a specific method
./vm_exec_test.py --command "echo Hello from VM && hostname" --method vagrant_cli

# Upload and execute an AppImage
./vm_exec_test.py --file "/path/to/your/appimage.AppImage"

# Interactive VM selection
./vm_exec_test.py --command "echo Hello" --interactive
```

#### Execution Methods

The system supports three execution methods:

1. **SSH** (`--method ssh`): Directly connects to the VM using SSH
2. **Vagrant CLI** (`--method vagrant_cli`): Uses the host's Vagrant CLI to execute commands
3. **Simulation** (`--method simulation`): Simulates command execution without a real VM

## Architecture

The system consists of:

- FastAPI backend for the REST API
- PostgreSQL database for VM metadata
- Docker environment for development
- Host-side script for Vagrant CLI integration

## Testing

For testing without actual Vagrant VMs, the system provides a simulation mode that mimics VM responses:

```bash
# Enable test mode in docker-compose.yml
VAGRANT_TEST_MODE=1

# Or run the test script with simulation mode
./vm_exec_test.py --command "echo Hello" --method simulation
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## API Testing

The project includes comprehensive API testing capabilities with test coverage reporting. The following files are available for testing:

- `test_api_coverage.py`: Contains test cases for various API endpoints including health checks, file uploads, MinIO status, authentication, and documentation.
- `run_api_coverage.py`: Script to run tests with coverage reporting.
- `.coveragerc`: Configuration file for coverage settings.

### New Test Coverage Reporting

We've added a new test coverage reporting system with the following features:

- `/api/v1/health/test-runs` API endpoint that provides detailed test coverage information
- Scripts for generating and analyzing test coverage data
- Automated reporting of test results and coverage metrics

The new scripts are located in the `scripts` directory:

- `generate_test_coverage.py`: Generates test coverage data for API endpoints
- `run_tests_and_report.py`: Runs tests and reports results to the API endpoint

### Running API Tests

To run the API tests with coverage reporting:

```bash
# Activate the virtual environment
python3 -m venv /tmp/api-test-venv
source /tmp/api-test-venv/bin/activate

# Install required packages
pip install requests pytest pytest-cov

# Run the tests
python run_api_coverage.py
```

### Using the New Test Scripts

To generate test coverage data and report it to the API:

```bash
# Generate test coverage
python scripts/generate_test_coverage.py

# Run tests and report results
python scripts/run_tests_and_report.py
```

To view the test results via the API endpoint:

1. Get a test token:
   ```bash
   curl -s -X POST http://localhost:3050/api/v1/auth/test-token
   ```

2. Use the token to access the test-runs endpoint:
   ```bash
   curl -s -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3050/api/v1/health/test-runs | python -m json.tool
   ```

For more details, see the [scripts README](scripts/README.md).

### Coverage Reports

Coverage reports are generated in the following formats:

- HTML reports in the `coverage_reports/html` directory
- XML report in `coverage_reports/coverage.xml`
- Terminal report showing coverage statistics
- JSON report in `coverage.json` (used by the API endpoint)

To view the HTML coverage report, you can serve it using Python's built-in HTTP server:

```bash
python3 -m http.server 8000 --directory coverage_reports/html
```

Then open http://localhost:8000 in your browser.

# TurdParty Template Selection Fix

## Issue Description

There was an issue with template selection not being correctly transferred to the configuration page during the workflow process. The problem occurred when a user selected a template in step 2 and proceeded to step 3 (Configure Target), where the selected template was not displayed.

## Fix Implementation

We identified and fixed several issues in the `MainPage/index.tsx` component:

1. Added a dedicated `handleTemplateSelect` function that:
   - Properly logs template selection
   - Updates state with selected template and template name
   - Stores template selection in localStorage for persistence

2. Added a robust `useEffect` hook that:
   - Triggers when navigating to step 2 (Configure)
   - Retrieves template selection from localStorage
   - Updates component state with the selected template
   - Includes comprehensive logging for debugging

3. Implemented a fallback mechanism in the Configure step that:
   - Double-checks localStorage for template data
   - Ensures the template is always displayed correctly
   - Provides helpful warnings when template data is missing

## Test Scripts

Several test scripts have been created to verify and debug the template selection functionality:

### 1. UI Workflow Test (Playwright)

An end-to-end Playwright test that automates the entire workflow:
- Navigates to the application
- Simulates file upload
- Selects a template
- Proceeds to the configuration step
- Verifies template selection persistence
- Captures screenshots at each step

### 2. Browser Debug Utility (`scripts/browser-debug.js`)

A comprehensive browser console utility that:
- Provides an interactive debugging experience
- Includes diagnostic functions to identify issues
- Offers one-click fixes for common problems
- Monitors and reports on template selection state
- Produces detailed reports on localStorage and UI state

To use the browser debug utility:
1. Open the file `scripts/browser-debug.js` in a text editor
2. Copy the entire contents
3. Open your browser to http://localhost:3100/
4. Open the browser's developer tools console (F12 or Ctrl+Shift+I)
5. Paste the code and press Enter
6. Use the available functions:
   - `debugTurdParty.diagnose()` - Run full diagnostic
   - `debugTurdParty.quickFix()` - Apply automatic fixes
   - `debugTurdParty.help()` - Show all available commands

### 3. Dependency Installer (`scripts/install_test_deps.sh`)

A helper script that:
- Creates package.json if needed
- Installs Playwright and its dependencies
- Sets up test configurations
- Makes the test scripts executable
- Provides instructions for running tests

## How to Test the Fix

### Automated Testing with Playwright

1. Install dependencies:
   ```bash
   ./scripts/install_test_deps.sh
   ```

2. Run the UI workflow test:
   ```bash
   npm run test:ui
   ```

3. Run with debugging enabled:
   ```bash
   npm run debug:template
   ```

Test reports are generated in the `test-reports` directory, and screenshots are saved to `test_screenshots/`.

## Advanced End-to-End Testing

We've created comprehensive end-to-end tests that cover the complete application workflow:

### 1. Complete Workflow Test (`scripts/playwright-tests/full-workflow.spec.js`)

Tests the entire user journey from file upload to VM creation:
- File upload functionality
- Template selection and persistence
- Configuration page with target path
- VM creation button verification
- Comprehensive error handling

Run with:
```bash
npx playwright test scripts/playwright-tests/full-workflow.spec.js
```

### 2. VM Management Test (`scripts/playwright-tests/vm-management.spec.js`)

Focuses on VM-specific operations:
- VM creation UI verification
- VM status monitoring 
- VM error state handling
- Simulated VM lifecycle testing

Run with:
```bash
npx playwright test scripts/playwright-tests/vm-management.spec.js
```

### 3. API Integration Test (`scripts/playwright-tests/api-interaction.spec.js`)

Tests backend API endpoints:
- File upload API functionality
- Templates retrieval API
- VM creation API
- VM status API
- Error handling and validation

Run with:
```bash
npx playwright test scripts/playwright-tests/api-interaction.spec.js
```

### Run All Tests

To run all tests at once:

```bash
npx playwright test
```

Test results and screenshots are saved in the `test-reports` and `test_screenshots` directories.

## Common Issues and Solutions

If template selection still doesn't appear:

1. Check browser console for errors
2. Verify localStorage values with:
   ```javascript
   console.log(localStorage.getItem('selectedTemplate'));
   console.log(localStorage.getItem('selectedTemplateName'));
   ```
3. Manually set values with:
   ```javascript
   localStorage.setItem('selectedTemplate', 'windows_10');
   localStorage.setItem('selectedTemplateName', 'Windows 10');
   window.location.reload();
   ```

## Test Screenshots

The UI test script saves screenshots to the `test_screenshots/` directory:
- `1_initial_state.png` - Initial application state
- `2_after_upload.png` - After file upload
- `3_template_selected.png` - After template selection
- `4_configuration_step.png` - Configure step with template info
- `5_final_state.png` - Final application state

# Windows VM Templates

TurdParty now supports Windows VM templates for creating Windows-based virtual machines in your environment.

### Supported Windows Templates

- **Windows 10** - Modern Windows desktop OS
- **Windows Server 2019** - Windows server OS with enhanced security and management

### Using Windows Templates

To create a Windows VM through the API:

```bash
curl -X POST "http://localhost:3050/api/v1/vagrant_vm/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-windows-vm",
    "template": "windows_10",
    "memory_mb": 4096,
    "cpus": 2
  }'
```

### Windows VM Requirements

- **Memory**: Minimum 4GB recommended (4096 MB)
- **CPUs**: At least 2 CPU cores recommended
- **Disk**: Minimum 40GB recommended
- **Network**: Private network with DHCP
- **Communication**: Uses WinRM for communicating with the VM

### Troubleshooting Windows VMs

Common issues and solutions:

1. **WinRM Connectivity Issues**
   - Ensure the VM is fully booted (can take longer than Linux VMs)
   - Verify WinRM service is running in the VM
   - Default credentials are username: `vagrant`, password: `vagrant`

2. **Performance Issues**
   - Increase memory allocation (min 4GB recommended)
   - Increase CPU allocation (min 2 cores recommended)
   - Enable virtualization in BIOS/UEFI
