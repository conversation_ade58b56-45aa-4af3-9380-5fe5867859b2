services:
  test_runner:
    build:
      context: ...
      dockerfile: docker/Dockerfile.testing
    volumes:
      - ./:/app
      - test_deps:/usr/local/lib/python3.10/site-packages/
      - test_cache:/root/.cache/
    environment:
      - PYTHONPATH=/app
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - DOCKER_NETWORK=true
      - TESTING=true
      - TEST_MODE=true
      - DATABASE_URL=********************************************/test_app
      - SQLALCHEMY_DATABASE_URI=********************************************/test_app
    depends_on:
      postgres:
        condition: service_healthy
      minio:
        condition: service_started
    networks:
      - test_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: tail -f /dev/null
    container_name: turdparty_test_runner_test
      
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=test_app
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - test_network
    container_name: turdparty_postgres_test
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
      
  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - test_network
    container_name: turdparty_minio_test

networks:
  test_network:
    name: turdparty_test_network
    driver: bridge

volumes:
  postgres_data:
    name: turdparty_postgres_data_test
  minio_data:
    name: turdparty_minio_data_test
  test_deps:
    name: turdparty_test_deps_test
  test_cache:
    name: turdparty_test_cache_test
