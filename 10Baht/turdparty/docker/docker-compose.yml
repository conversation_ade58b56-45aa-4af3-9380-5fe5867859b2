version: '3'

services:
  api:
    build:
      context: ...
      dockerfile: docker/Dockerfile.api
    environment:
      - DATABASE_URL=********************************************/app
      - PYTHONPATH=/app
      - MINIO_DIRECT=true
      - MINIO_HOST=minio
    volumes:
      - ../api:/app/api
      - ../scripts:/app/scripts
    ports:
      - "3055:8000"
    depends_on:
      - postgres
      - minio
    networks:
      default:
        aliases:
          - turdparty-api-1

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=app
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5435:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      default:
        aliases:
          - turdparty-postgres-1

  minio:
    image: minio/minio:latest
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "9500:9000"
      - "9501:9001"
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      default:
        aliases:
          - turdparty-minio-1

  minio-ssh:
    build:
      context: ...
      dockerfile: .dockerwrapper/Dockerfile.minio
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "2225:22"
    depends_on:
      - minio
    networks:
      default:
        aliases:
          - turdparty-minio-ssh-1

volumes:
  postgres_data:
  minio_data:

networks:
  default:
    name: turdparty_default 