"""
Services package for API communication.
"""
from ui.services.connector import ServiceConnector
from ui.services.item_service import ItemService
from ui.services.config import SERVICE_CONFIG
from ui.services.static_analysis_service import StaticAnalysisService

# Create a pre-configured item service instance
item_service = ItemService(SERVICE_CONFIG["api_url"])
# Create service instances
static_analysis_service = StaticAnalysisService(base_url=SERVICE_CONFIG["api_url"])

try:
    # Attempt to import state_manager
    from ui.services.state_manager import state_manager
    has_state_manager = True
except ImportError:
    # If streamlit is not available, create a basic state manager
    has_state_manager = False

__all__ = [
    "ServiceConnector", 
    "ItemService", 
    "item_service", 
    "SERVICE_CONFIG", 
    "static_analysis_service"
]

# Only add state_manager to __all__ if it was successfully imported
if has_state_manager:
    __all__.append("state_manager")