"""
Service connector pattern implementation for API communication.
"""
import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type, Union, List
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError

# Set up logging
logger = logging.getLogger(__name__)

T = TypeVar('T')

class ServiceConnector(Generic[T]):
    """
    Generic service connector for API communication.
    
    This class provides a standardized way to communicate with backend APIs.
    """
    
    def __init__(self, base_url: str, model_class: Type[T], timeout: int = 30):
        """
        Initialize the service connector.
        
        Args:
            base_url: Base URL for API endpoints
            model_class: Class to use for response data
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.model_class = model_class
        self.timeout = timeout
    
    def _build_url(self, endpoint: str) -> str:
        """
        Build the full URL for an endpoint.
        
        Args:
            endpoint: API endpoint path
            
        Returns:
            Full URL
        """
        endpoint = endpoint.lstrip('/')
        return f"{self.base_url}/{endpoint}"
    
    def _handle_response(self, response: requests.Response) -> Union[T, List[T], Dict[str, Any]]:
        """
        Handle API response.
        
        Args:
            response: Response object from requests
            
        Returns:
            Response data as model instance, list of model instances, or dictionary
            
        Raises:
            ValueError: If response is not successful
        """
        if response.status_code >= 400:
            logger.error(f"API error: {response.status_code} - {response.text}")
            try:
                error_data = response.json()
                error_message = error_data.get('detail', str(error_data))
            except Exception:
                error_message = response.text or f"HTTP {response.status_code}"
                
            raise ValueError(f"API error: {error_message}")
        
        # Handle empty responses
        if not response.content or response.status_code == 204:
            return {"message": "success"}
            
        # Parse JSON response
        try:
            data = response.json()
            
            # Handle list response
            if isinstance(data, list):
                try:
                    return [self.model_class(**item) for item in data]
                except TypeError as e:
                    logger.warning(f"Could not convert list items to model class: {str(e)}")
                    return data
            
            # Handle dictionary response
            elif isinstance(data, dict):
                # Check if it's likely a model instance
                if any(key in data for key in ['id', 'name']):
                    try:
                        return self.model_class(**data)
                    except TypeError as e:
                        logger.warning(f"Could not convert dictionary to model class: {str(e)}")
                        return data
                return data  # Other dictionary response
            
            # Handle other data types
            return data
            
        except ValueError as e:
            logger.warning(f"Could not parse JSON response: {str(e)}")
            return {"message": "success" if not response.text else response.text}
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Union[T, List[T], Dict[str, Any]]:
        """
        Send GET request to API.
        
        Args:
            endpoint: API endpoint path
            params: Query parameters
            
        Returns:
            Response data as model instance, list of model instances, or dictionary
            
        Raises:
            ValueError: If request fails
        """
        url = self._build_url(endpoint)
        logger.debug(f"GET {url}")
        
        try:
            response = requests.get(url, params=params, timeout=self.timeout)
            return self._handle_response(response)
        except RequestException as e:
            logger.error(f"Request error: {str(e)}")
            raise ValueError(f"API request failed: {str(e)}")
    
    def post(self, endpoint: str, json: Optional[Dict[str, Any]] = None) -> Union[T, Dict[str, Any]]:
        """
        Send POST request to API.
        
        Args:
            endpoint: API endpoint path
            json: Request data
            
        Returns:
            Response data as model instance or dictionary
            
        Raises:
            ValueError: If request fails
        """
        url = self._build_url(endpoint)
        logger.debug(f"POST {url}")
        
        try:
            response = requests.post(url, json=json, timeout=self.timeout)
            return self._handle_response(response)
        except RequestException as e:
            logger.error(f"Request error: {str(e)}")
            raise ValueError(f"API request failed: {str(e)}")
    
    def post_file(self, endpoint: str, files: Dict[str, Any], data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send POST request with file upload to API.
        
        Args:
            endpoint: API endpoint path
            files: Files to upload
            data: Additional form data
            
        Returns:
            Response data as dictionary
            
        Raises:
            ValueError: If request fails
        """
        url = self._build_url(endpoint)
        logger.debug(f"POST (file) {url}")
        
        try:
            response = requests.post(url, files=files, data=data, timeout=self.timeout)
            return self._handle_response(response)
        except RequestException as e:
            logger.error(f"Request error: {str(e)}")
            raise ValueError(f"API request failed: {str(e)}")
    
    def put(self, endpoint: str, json: Optional[Dict[str, Any]] = None) -> Union[T, Dict[str, Any]]:
        """
        Send PUT request to API.
        
        Args:
            endpoint: API endpoint path
            json: Request data
            
        Returns:
            Response data as model instance or dictionary
            
        Raises:
            ValueError: If request fails
        """
        url = self._build_url(endpoint)
        logger.debug(f"PUT {url}")
        
        try:
            response = requests.put(url, json=json, timeout=self.timeout)
            return self._handle_response(response)
        except RequestException as e:
            logger.error(f"Request error: {str(e)}")
            raise ValueError(f"API request failed: {str(e)}")
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """
        Send DELETE request to API.
        
        Args:
            endpoint: API endpoint path
            
        Returns:
            Response data as dictionary
            
        Raises:
            ValueError: If request fails
        """
        url = self._build_url(endpoint)
        logger.debug(f"DELETE {url}")
        
        try:
            response = requests.delete(url, timeout=self.timeout)
            return self._handle_response(response)
        except RequestException as e:
            logger.error(f"Request error: {str(e)}")
            raise ValueError(f"API request failed: {str(e)}")
