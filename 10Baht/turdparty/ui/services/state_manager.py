"""
State management service.
Adapted for Flask instead of Streamlit.
"""
import logging
from typing import Dict, Any, Optional, List, Callable
from threading import Lock

logger = logging.getLogger(__name__)

class SessionState:
    """
    Simple session state implementation.
    """
    def __init__(self):
        """Initialize an empty session state."""
        self._data = {}
        self._lock = Lock()
    
    def __getitem__(self, key):
        """Get a value by key."""
        with self._lock:
            return self._data.get(key)
    
    def __setitem__(self, key, value):
        """Set a value by key."""
        with self._lock:
            self._data[key] = value
    
    def __contains__(self, key):
        """Check if key exists."""
        with self._lock:
            return key in self._data
    
    def get(self, key, default=None):
        """Get a value with a default."""
        with self._lock:
            return self._data.get(key, default)
    
    def items(self):
        """Get all items."""
        with self._lock:
            return self._data.items()


class StateManager:
    """
    State manager for complex UI interactions.

    This class provides a centralized way to manage application state,
    handle complex interactions, and implement features like undo/redo.
    """

    def __init__(self):
        """Initialize the state manager."""
        # Initialize the session state
        self.session_state = SessionState()
        self.session_state["history"] = []
        self.session_state["history_index"] = -1
        self.session_state["callbacks"] = {}

    def set_state(self, key: str, value: Any) -> None:
        """
        Set a state value and record it in history.

        Args:
            key: The state key
            value: The state value
        """
        try:
            # Record state change in history
            new_state = {k: v for k, v in self.session_state.items() 
                        if k not in ["history", "history_index", "callbacks"]}

            # If we're not at the end of the history, truncate it
            if self.session_state["history_index"] < len(self.session_state["history"]) - 1:
                self.session_state["history"] = self.session_state["history"][:self.session_state["history_index"] + 1]

            self.session_state["history"].append(new_state)
            self.session_state["history_index"] = len(self.session_state["history"]) - 1

            # Set the actual state
            self.session_state[key] = value

            # Trigger callbacks
            self._trigger_callbacks(key, value)

            logger.debug(f"State updated: {key}={value}")
        except Exception as e:
            logger.error(f"Error setting state {key}: {str(e)}", exc_info=True)

    def get_state(self, key: str, default: Any = None) -> Any:
        """
        Get a state value.

        Args:
            key: The state key
            default: Default value if key doesn't exist

        Returns:
            The state value or default
        """
        try:
            return self.session_state.get(key, default)
        except Exception as e:
            logger.error(f"Error getting state {key}: {str(e)}", exc_info=True)
            return default

    def register_callback(self, key: str, callback: Callable[[str, Any], None]) -> None:
        """
        Register a callback for state changes.

        Args:
            key: The state key to watch
            callback: Function to call when state changes
        """
        try:
            callbacks = self.session_state.get("callbacks", {})
            if key not in callbacks:
                callbacks[key] = []
            callbacks[key].append(callback)
            self.session_state["callbacks"] = callbacks
            logger.debug(f"Callback registered for {key}")
        except Exception as e:
            logger.error(f"Error registering callback for {key}: {str(e)}", exc_info=True)

    def _trigger_callbacks(self, key: str, value: Any) -> None:
        """
        Trigger callbacks for a state change.

        Args:
            key: The state key that changed
            value: The new state value
        """
        try:
            callbacks = self.session_state.get("callbacks", {}).get(key, [])
            for callback in callbacks:
                callback(key, value)
        except Exception as e:
            logger.error(f"Error triggering callbacks for {key}: {str(e)}", exc_info=True)

    def undo(self) -> bool:
        """
        Undo the last state change.

        Returns:
            True if undo was successful, False otherwise
        """
        try:
            if self.session_state["history_index"] > 0:
                self.session_state["history_index"] -= 1
                state = self.session_state["history"][self.session_state["history_index"]]

                # Update the current state
                for k, v in state.items():
                    self.session_state[k] = v

                logger.debug("Undo operation performed")
                return True
            return False
        except Exception as e:
            logger.error(f"Error performing undo: {str(e)}", exc_info=True)
            return False

    def redo(self) -> bool:
        """
        Redo the last undone state change.

        Returns:
            True if redo was successful, False otherwise
        """
        try:
            if self.session_state["history_index"] < len(self.session_state["history"]) - 1:
                self.session_state["history_index"] += 1
                state = self.session_state["history"][self.session_state["history_index"]]

                # Update the current state
                for k, v in state.items():
                    self.session_state[k] = v

                logger.debug("Redo operation performed")
                return True
            return False
        except Exception as e:
            logger.error(f"Error performing redo: {str(e)}", exc_info=True)
            return False

    def clear_history(self) -> None:
        """Clear the state history."""
        try:
            self.session_state["history"] = []
            self.session_state["history_index"] = -1
            logger.debug("State history cleared")
        except Exception as e:
            logger.error(f"Error clearing history: {str(e)}", exc_info=True)

    def is_authenticated(self) -> bool:
        """
        Check if user is authenticated.

        Returns:
            True if authenticated, False otherwise
        """
        if self.get_token() is None:
            return False

        # Check if MFA is required but not verified
        user_data = self.get_user_data()
        if user_data and user_data.get("mfa_enabled", False) and not self.get_mfa_verified():
            return False

        return True

    def get_mfa_verified(self) -> bool:
        """
        Check if MFA has been verified for this session.

        Returns:
            True if MFA verified, False otherwise
        """
        return self.session_state.get("mfa_verified", False)

    def set_mfa_verified(self, verified: bool) -> None:
        """
        Set MFA verification status.

        Args:
            verified: Whether MFA has been verified
        """
        self.session_state["mfa_verified"] = verified

    def get_token(self):
        """Placeholder for token retrieval"""
        return self.session_state.get("token")


    def get_user_data(self):
        """Placeholder for user data retrieval"""
        return self.session_state.get("user_data")


# Create a singleton instance for easy import
state_manager = StateManager()