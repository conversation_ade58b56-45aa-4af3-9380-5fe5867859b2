"""
Configuration for services.
"""
import os
from typing import Dict, Any

# Default API configuration
DEFAULT_API_URL = "http://localhost:8000"
DEFAULT_TIMEOUT = 10  # seconds

# Load configuration from environment variables if available
def get_from_env(key: str, default: Any) -> Any:
    """Get a configuration value from environment variables with a fallback default."""
    value = os.environ.get(key)
    if value is None or value.strip() == '':
        return default
    return value

# Parse timeout as integer, but gracefully handle invalid values
def parse_timeout(value: str, default: int) -> int:
    """Parse timeout value, falling back to default if invalid."""
    if not value or not value.strip():
        return default
    try:
        timeout = int(value)
        return timeout if timeout > 0 else default
    except (ValueError, TypeError):
        return default

# Get API URL from environment or use default
API_URL = get_from_env("API_URL", DEFAULT_API_URL)

# Get timeout from environment or use default
REQUEST_TIMEOUT_STR = get_from_env("REQUEST_TIMEOUT", str(DEFAULT_TIMEOUT))
REQUEST_TIMEOUT = parse_timeout(REQUEST_TIMEOUT_STR, DEFAULT_TIMEOUT)

# Service configuration dictionary
SERVICE_CONFIG: Dict[str, Any] = {
    "api_url": API_URL,
    "timeout": REQUEST_TIMEOUT,
}
