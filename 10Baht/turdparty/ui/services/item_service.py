"""
Item service connector for API communication.
"""
from typing import List, Dict, Any, Optional
import logging
from dataclasses import dataclass
from datetime import datetime

from ui.services.connector import ServiceConnector

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class Item:
    """Data class for item objects."""
    id: int
    name: str
    description: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    deleted_at: Optional[str] = None


class ItemService(ServiceConnector[Item]):
    """
    Service connector for item-related API endpoints.
    """
    
    def __init__(self, base_url: str):
        """
        Initialize the item service connector.
        
        Args:
            base_url: Base URL for the API
        """
        super().__init__(base_url, Item)
        self.endpoint = "api/v1/items"
    
    def get_items(self, skip: int = 0, limit: int = 100, name: Optional[str] = None) -> List[Item]:
        """
        Get a list of items with optional filtering.
        
        Args:
            skip: Number of items to skip
            limit: Maximum number of items to return
            name: Optional filter by item name
            
        Returns:
            List of items
        """
        params = {"skip": skip, "limit": limit}
        if name:
            params["name"] = name
            
        try:
            return self.get(self.endpoint, params=params)
        except Exception as e:
            logger.error(f"Error getting items: {str(e)}")
            return []
    
    def get_item(self, item_id: int) -> Optional[Item]:
        """
        Get a specific item by ID.
        
        Args:
            item_id: ID of the item
            
        Returns:
            Item object or None if not found
        """
        try:
            return self.get(f"{self.endpoint}/{item_id}")
        except Exception as e:
            logger.error(f"Error getting item {item_id}: {str(e)}")
            return None
    
    def create_item(self, name: str, description: Optional[str] = None) -> Optional[Item]:
        """
        Create a new item.
        
        Args:
            name: Name of the item
            description: Optional description
            
        Returns:
            Created item or None if creation failed
        """
        data = {"name": name}
        if description:
            data["description"] = description
            
        try:
            return self.post(self.endpoint, json=data)
        except Exception as e:
            logger.error(f"Error creating item: {str(e)}")
            return None
    
    def update_item(self, item_id: int, name: Optional[str] = None, description: Optional[str] = None) -> Optional[Item]:
        """
        Update an existing item.
        
        Args:
            item_id: ID of the item to update
            name: New name (optional)
            description: New description (optional)
            
        Returns:
            Updated item or None if update failed
        """
        data = {}
        if name:
            data["name"] = name
        if description is not None:  # Allow empty string
            data["description"] = description
            
        try:
            return self.put(f"{self.endpoint}/{item_id}", json=data)
        except Exception as e:
            logger.error(f"Error updating item {item_id}: {str(e)}")
            return None
    
    def delete_item(self, item_id: int) -> bool:
        """
        Delete an item.
        
        Args:
            item_id: ID of the item to delete
            
        Returns:
            True if deletion succeeded, False otherwise
        """
        try:
            self.delete(f"{self.endpoint}/{item_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting item {item_id}: {str(e)}")
            return False
