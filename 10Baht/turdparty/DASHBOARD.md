# TurdParty Docker Dashboard

A CLI dashboard for monitoring and managing Docker containers for the TurdParty application.

![Dashboard Screenshot](dashboard-screenshot.png)

## Features

- Real-time monitoring of Docker containers
- Container status display with color coding
- Container logs viewer
- CPU and memory usage statistics
- Network traffic monitoring
- One-click container restart
- Start all containers with a single command

## Requirements

- Node.js (v14 or higher)
- npm
- Docker

## Installation

The dashboard comes with a wrapper script that will automatically install all required dependencies.

1. Make sure you have Node.js and npm installed:
   ```bash
   node --version
   npm --version
   ```

2. Make sure the wrapper script is executable:
   ```bash
   chmod +x turdparty-dashboard.sh
   ```

## Usage

Simply run the wrapper script:

```bash
./turdparty-dashboard.sh
```

### Keyboard Controls

- **Arrow keys**: Navigate the container list
- **Enter**: Select a container to view its logs
- **R**: Restart the selected container
- **X**: Stop the selected container
- **I**: View detailed information about the selected container
- **S**: Start all containers
- **H**: Show help screen
- **Q** or **Esc** or **Ctrl+C**: Quit the dashboard

## Dashboard Sections

### Containers Table

Displays all Docker containers with the following information:
- Name
- Status (color-coded: green for running, red for exited, blue for created)
- Uptime
- Ports
- Image

### Container Logs

Shows the logs of the selected container. The logs are updated when you select a different container.

### System Stats

Displays system-wide statistics:
- CPU Usage: Shows the total CPU usage of all running containers
- Memory Usage: Shows the total memory usage of all running containers
- Network Traffic: Shows the network traffic (RX/TX) of all running containers

### Status Bar

Shows the current status of the dashboard and any operations being performed.

## Troubleshooting

If you encounter any issues:

1. Make sure Docker is running:
   ```bash
   docker info
   ```

2. Check if you have permission to access the Docker socket:
   ```bash
   ls -l /var/run/docker.sock
   ```
   
   If needed, add your user to the docker group:
   ```bash
   sudo usermod -aG docker $USER
   ```
   (You'll need to log out and back in for this to take effect)

3. Check if the required Node.js modules are installed:
   ```bash
   npm list --depth=0
   ```

## License

MIT 