# Database Schema Documentation

## Overview

The TurdParty application uses PostgreSQL 13 as its primary database. This document outlines the database schema, relationships, and data models.

## Database Configuration

- **Host**: localhost
- **Port**: 5430
- **Database Name**: turdparty
- **User**: postgres
- **Password**: postgres

## Tables

### Users
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    mfa_secret VARCHAR(32),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Items
```sql
CREATE TABLE items (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Files
```sql
CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    content_type VARCHAR(100),
    size BIGINT NOT NULL,
    bucket_name VARCHAR(255) NOT NULL,
    object_name VARCHAR(255) NOT NULL,
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### VMs
```sql
CREATE TABLE vms (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    template VARCHAR(255) NOT NULL,
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### VM_Logs
```sql
CREATE TABLE vm_logs (
    id SERIAL PRIMARY KEY,
    vm_id INTEGER REFERENCES vms(id),
    log_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Relationships

### One-to-Many Relationships
- User → Items
- User → Files
- User → VMs
- VM → VM_Logs

### Foreign Key Constraints
- `items.user_id` → `users.id`
- `files.user_id` → `users.id`
- `vms.user_id` → `users.id`
- `vm_logs.vm_id` → `vms.id`

## Indexes

### Primary Keys
- `users.id`
- `items.id`
- `files.id`
- `vms.id`
- `vm_logs.id`

### Unique Constraints
- `users.username`
- `users.email`

### Performance Indexes
- `items.user_id`
- `files.user_id`
- `vms.user_id`
- `vm_logs.vm_id`

## Triggers

### Updated At Trigger
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_items_updated_at
    BEFORE UPDATE ON items
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_files_updated_at
    BEFORE UPDATE ON files
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vms_updated_at
    BEFORE UPDATE ON vms
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

## Data Types

### Common Types
- `SERIAL`: Auto-incrementing integer
- `VARCHAR`: Variable-length string
- `TEXT`: Unlimited length text
- `BOOLEAN`: True/false value
- `TIMESTAMP WITH TIME ZONE`: Date and time with timezone

### Custom Types
- `VM_STATUS`: Enum for VM states
  ```sql
  CREATE TYPE vm_status AS ENUM (
      'created',
      'starting',
      'running',
      'stopped',
      'error'
  );
  ```

## Migrations

Database migrations are managed using Alembic. Migration files are located in:
- `api/migrations/versions/`

### Running Migrations
```bash
# Create a new migration
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head

# Rollback migrations
alembic downgrade -1
```

## Backup and Recovery

### Backup
```bash
pg_dump -h localhost -p 5430 -U postgres -d turdparty > backup.sql
```

### Recovery
```bash
psql -h localhost -p 5430 -U postgres -d turdparty < backup.sql
```

## Related Documentation

- [Data Models](../schema/models.md)
- [Migration History](../schema/migrations.md)
- [API Endpoints](../api/endpoints.md)
- [Database Operations](../workflows/operations.md)

## Database (COMPLETED - March 28, 2025)

Our system will use PostgreSQL as the primary database. The following components need to be implemented:

1. Set up PostgreSQL database server 
2. Create database schemas
3. Create database enum types for VM statuses
4. Set up triggers for updated_at timestamps
5. Configure performance indexes on foreign keys
6. Configure Alembic for database migrations
7. Implement database backup and recovery scripts
8. Implement database connection module
9. Create database models documentation
10. Implement database migration documentation generator

All database components have been successfully implemented, including the relationship between User and Item models. 