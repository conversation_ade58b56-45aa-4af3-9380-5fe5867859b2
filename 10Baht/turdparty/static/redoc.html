<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty API Documentation</title>
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }

        #redoc-container {
            background-color: #1a1a1a;
        }

        /* Main container */
        .sc-htpNat, .menu-content, .api-info, .api-content {
            background-color: #1a1a1a !important;
            color: #e0e0e0 !important;
        }

        /* Menu and sidebar */
        .menu-content {
            background-color: #222 !important;
            border-right: 1px solid #444 !important;
        }

        /* Menu items */
        .menu-item-title, .menu-item-text, .menu-item-depth-1 > .menu-item-text {
            color: #e0e0e0 !important;
        }

        /* Active menu item */
        li.active > label, .menu-item.active, .menu-item-text.active {
            background-color: #333 !important;
            color: #fff !important;
        }

        /* Menu item hover */
        .menu-item:hover {
            background-color: #333 !important;
        }

        /* Search box */
        .search-input {
            background-color: #333 !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }

        /* API info section */
        .api-info h1, .api-info p {
            color: #e0e0e0 !important;
        }

        /* Operation badges */
        .operation-type {
            color: #fff !important;
        }

        /* HTTP method badges */
        span[type="get"] {
            background-color: #0085a1 !important;
        }

        span[type="post"] {
            background-color: #00a14b !important;
        }

        span[type="put"] {
            background-color: #a18d00 !important;
        }

        span[type="delete"] {
            background-color: #a10000 !important;
        }

        span[type="patch"] {
            background-color: #9000a1 !important;
        }

        /* Response section */
        .response-title, .response {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
        }

        /* Schema section */
        .schema {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }

        /* Code blocks */
        .redoc-json code, pre, code {
            background-color: #333 !important;
            color: #e0e0e0 !important;
        }

        /* Property names and types */
        .property-name, .property-type, .param-name, .param-type {
            color: #e0e0e0 !important;
        }

        /* Property required marker */
        .property-name .required {
            color: #ff7070 !important;
        }

        /* Tabs */
        .tab-list .tab-item {
            color: #e0e0e0 !important;
        }

        .tab-list .tab-item.active {
            background-color: #333 !important;
            color: #fff !important;
        }

        /* Links */
        a, a:visited {
            color: #4da3ff !important;
        }

        a:hover {
            color: #66b0ff !important;
            text-decoration: underline !important;
        }

        /* All text elements */
        [data-section-id] h1,
        [data-section-id] h2,
        [data-section-id] h3,
        [data-section-id] h4,
        [data-section-id] h5,
        [data-section-id] p,
        [data-section-id] a,
        [data-section-id] li,
        [data-section-id] td,
        [data-section-id] th,
        h1, h2, h3, h4, h5, p, a, li, td, th,
        div, span, button {
            color: #e0e0e0 !important;
        }

        /* JSON syntax highlighting */
        .token.property {
            color: #9cdcfe !important;
        }

        .token.string {
            color: #ce9178 !important;
        }

        .token.number {
            color: #b5cea8 !important;
        }

        .token.boolean {
            color: #569cd6 !important;
        }

        .token.null {
            color: #569cd6 !important;
        }

        /* Scrollbars */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        ::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        ::-webkit-scrollbar-thumb {
            background: #444;
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Toggle button */
        #dark-mode-toggle {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            padding: 8px 16px;
            background-color: #333;
            color: #fff;
            border: 1px solid #444;
            border-radius: 4px;
            cursor: pointer;
        }

        /* Force dark mode for all elements */
        * {
            color-scheme: dark;
        }
    </style>
</head>
<body>
    <button id="dark-mode-toggle">Toggle Light/Dark Mode</button>
    <div id="redoc-container"></div>
    <script src="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js"></script>
    <script>
        // Initialize ReDoc
        Redoc.init('/api/v1/openapi.json', {
            scrollYOffset: 50,
            hideDownloadButton: false,
            expandResponses: 'all',
            theme: {
                colors: {
                    primary: {
                        main: '#4da3ff'
                    }
                },
                typography: {
                    fontSize: '16px',
                    headings: {
                        fontWeight: '600'
                    }
                },
                sidebar: {
                    backgroundColor: '#222',
                    textColor: '#e0e0e0'
                },
                rightPanel: {
                    backgroundColor: '#2d2d2d',
                    textColor: '#e0e0e0'
                }
            }
        }, document.getElementById('redoc-container'));

        // Toggle button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButton = document.getElementById('dark-mode-toggle');
            if (toggleButton) {
                toggleButton.addEventListener('click', function() {
                    const body = document.body;
                    if (body.style.backgroundColor === 'white') {
                        body.style.backgroundColor = '#1a1a1a';
                        body.style.color = '#e0e0e0';
                    } else {
                        body.style.backgroundColor = 'white';
                        body.style.color = '#333';
                    }
                });
            }
        });
    </script>
</body>
</html>
