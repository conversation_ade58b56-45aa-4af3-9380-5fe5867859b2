<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>TurdParty API Documentation</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #e0e0e0;
      background-color: #121212;
      margin: 0;
      padding: 20px;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    h1, h2, h3, h4 {
      color: #4da3ff;
      margin-top: 30px;
    }
    
    h1 {
      border-bottom: 1px solid #444;
      padding-bottom: 10px;
    }
    
    .endpoint {
      background-color: #1e1e1e;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      border: 1px solid #333;
    }
    
    .endpoint-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .method {
      padding: 5px 10px;
      border-radius: 4px;
      font-weight: bold;
      margin-right: 15px;
      min-width: 60px;
      text-align: center;
    }
    
    .get {
      background-color: #61affe;
      color: #fff;
    }
    
    .post {
      background-color: #49cc90;
      color: #fff;
    }
    
    .put {
      background-color: #fca130;
      color: #fff;
    }
    
    .delete {
      background-color: #f93e3e;
      color: #fff;
    }
    
    .path {
      font-family: monospace;
      font-size: 16px;
    }
    
    .description {
      color: #a0a0a0;
      margin-bottom: 15px;
    }
    
    .section {
      margin-top: 15px;
    }
    
    .section-title {
      font-weight: bold;
      margin-bottom: 10px;
      color: #4da3ff;
    }
    
    pre {
      background-color: #2d2d2d;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
    }
    
    code {
      font-family: 'Courier New', Courier, monospace;
    }
    
    .tag {
      background-color: #333;
      color: #a0a0a0;
      padding: 3px 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-right: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>TurdParty API Documentation</h1>
    <p>This is the API documentation for the TurdParty application.</p>
    
    <h2>Health</h2>
    
    <div class="endpoint">
      <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="path">/api/v1/health/</span>
      </div>
      <div class="description">Check the health status of the API.</div>
      <div class="section">
        <div class="section-title">Response</div>
        <pre><code>{
  "status": "ok"
}</code></pre>
      </div>
    </div>
    
    <h2>Authentication</h2>
    
    <div class="endpoint">
      <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="path">/api/v1/auth/login</span>
      </div>
      <div class="description">Login to the API and get an access token.</div>
      <div class="section">
        <div class="section-title">Request Body</div>
        <pre><code>{
  "username": "string",
  "password": "string"
}</code></pre>
      </div>
      <div class="section">
        <div class="section-title">Response</div>
        <pre><code>{
  "access_token": "string",
  "token_type": "bearer"
}</code></pre>
      </div>
    </div>
    
    <div class="endpoint">
      <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="path">/api/v1/auth/session</span>
      </div>
      <div class="description">Get information about the current session.</div>
      <div class="section">
        <div class="section-title">Response</div>
        <pre><code>{
  "authenticated": true,
  "user": {
    "username": "string",
    "email": "string"
  }
}</code></pre>
      </div>
    </div>
    
    <h2>Async Tasks</h2>
    
    <div class="endpoint">
      <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="path">/api/v1/async/tasks</span>
      </div>
      <div class="description">List all async tasks.</div>
      <div class="section">
        <div class="section-title">Response</div>
        <pre><code>[
  {
    "id": "string",
    "status": "string",
    "result": {}
  }
]</code></pre>
      </div>
    </div>
    
    <div class="endpoint">
      <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="path">/api/v1/async/workers</span>
      </div>
      <div class="description">List all Celery workers.</div>
      <div class="section">
        <div class="section-title">Response</div>
        <pre><code>[
  {
    "name": "string",
    "status": "string",
    "queues": ["string"]
  }
]</code></pre>
      </div>
    </div>
    
    <h2>Virtual Machines</h2>
    
    <div class="endpoint">
      <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="path">/api/v1/virtual-machines</span>
      </div>
      <div class="description">List all virtual machines.</div>
      <div class="section">
        <div class="section-title">Response</div>
        <pre><code>[
  {
    "id": "string",
    "name": "string",
    "status": "string"
  }
]</code></pre>
      </div>
    </div>
  </div>
</body>
</html>
