const { chromium } = require("playwright"); (async () => { const browser = await chromium.launch({ headless: true }); const context = await browser.newContext(); const page = await context.newPage(); console.log("Testing UI components..."); try { await page.goto("http://turdparty_test_frontend:3000/"); console.log("Page loaded with title:", await page.title()); const mainContent = await page.$("div#root > div"); console.log("Main content present:", !!mainContent); const buttons = await page.$$eval("button", btns => btns.length); console.log("Number of buttons found:", buttons); const headings = await page.$$eval("h1, h2, h3", headings => headings.map(h => h.textContent)); console.log("Headings found:", headings); console.log("UI TEST PASSED: Interface is working properly"); } catch (error) { console.error("TEST FAILED:", error); } finally { await browser.close(); } })();
