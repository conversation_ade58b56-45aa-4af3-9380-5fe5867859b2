<!DOCTYPE html><html lang="en"><head><style data-rc-order="prepend" rc-util-key="@ant-design-icons">
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="w81tjp" data-token-hash="v4zkj0" data-cache-path="v4zkj0|Shared|ant">a:where(.css-dev-only-do-not-override-tjsggz){color:#1677ff;text-decoration:none;background-color:transparent;outline:none;cursor:pointer;transition:color 0.3s;-webkit-text-decoration-skip:objects;}a:where(.css-dev-only-do-not-override-tjsggz):hover{color:#69b1ff;}a:where(.css-dev-only-do-not-override-tjsggz):active{color:#0958d9;}a:where(.css-dev-only-do-not-override-tjsggz):active,a:where(.css-dev-only-do-not-override-tjsggz):hover{text-decoration:none;outline:0;}a:where(.css-dev-only-do-not-override-tjsggz):focus{text-decoration:none;outline:0;}a:where(.css-dev-only-do-not-override-tjsggz)[disabled]{color:rgba(0,0,0,0.25);cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz) a{color:#1677ff;text-decoration:none;background-color:transparent;outline:none;cursor:pointer;transition:color 0.3s;-webkit-text-decoration-skip:objects;}:where(.css-dev-only-do-not-override-tjsggz) a:hover{color:#69b1ff;}:where(.css-dev-only-do-not-override-tjsggz) a:active{color:#0958d9;}:where(.css-dev-only-do-not-override-tjsggz) a:active,:where(.css-dev-only-do-not-override-tjsggz) a:hover{text-decoration:none;outline:0;}:where(.css-dev-only-do-not-override-tjsggz) a:focus{text-decoration:none;outline:0;}:where(.css-dev-only-do-not-override-tjsggz) a[disabled]{color:rgba(0,0,0,0.25);cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).anticon{display:inline-flex;align-items:center;color:inherit;font-style:normal;line-height:0;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}:where(.css-dev-only-do-not-override-tjsggz).anticon >*{line-height:1;}:where(.css-dev-only-do-not-override-tjsggz).anticon svg{display:inline-block;}:where(.css-dev-only-do-not-override-tjsggz).anticon .anticon .anticon-icon{display:block;}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="1xevswb" data-token-hash="v4zkj0" data-cache-path="v4zkj0|Select-Select|ant-select|anticon">:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"],:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"]{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"]::before,:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"]::before,:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"]::after,:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"] [class^="ant-select"],:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"] [class^="ant-select"],:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"] [class*=" ant-select"],:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"] [class*=" ant-select"]{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"] [class^="ant-select"]::before,:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"] [class^="ant-select"]::before,:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"] [class*=" ant-select"]::before,:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"] [class*=" ant-select"]::before,:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"] [class^="ant-select"]::after,:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"] [class^="ant-select"]::after,:where(.css-dev-only-do-not-override-tjsggz)[class^="ant-select"] [class*=" ant-select"]::after,:where(.css-dev-only-do-not-override-tjsggz)[class*=" ant-select"] [class*=" ant-select"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-select.ant-select-in-form-item{width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select{box-sizing:border-box;margin:0;padding:0;color:rgba(0,0,0,0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';position:relative;display:inline-flex;cursor:pointer;}:where(.css-dev-only-do-not-override-tjsggz).ant-select:not(.ant-select-customize-input) .ant-select-selector{position:relative;transition:all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-tjsggz).ant-select:not(.ant-select-customize-input) .ant-select-selector input{cursor:pointer;}.ant-select-show-search:where(.css-dev-only-do-not-override-tjsggz).ant-select:not(.ant-select-customize-input) .ant-select-selector{cursor:text;}.ant-select-show-search:where(.css-dev-only-do-not-override-tjsggz).ant-select:not(.ant-select-customize-input) .ant-select-selector input{cursor:auto;color:inherit;height:100%;}.ant-select-disabled:where(.css-dev-only-do-not-override-tjsggz).ant-select:not(.ant-select-customize-input) .ant-select-selector{cursor:not-allowed;}.ant-select-disabled:where(.css-dev-only-do-not-override-tjsggz).ant-select:not(.ant-select-customize-input) .ant-select-selector input{cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input{margin:0;padding:0;background:transparent;border:none;outline:none;appearance:none;font-family:inherit;}:where(.css-dev-only-do-not-override-tjsggz).ant-select:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input::-webkit-search-cancel-button{display:none;-webkit-appearance:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-selection-item{flex:1;font-weight:normal;position:relative;user-select:none;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-selection-item >.ant-typography{display:inline;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-selection-placeholder{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;flex:1;color:rgba(0,0,0,0.25);pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-arrow{display:flex;align-items:center;color:rgba(0,0,0,0.25);font-style:normal;line-height:1;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;position:absolute;top:50%;inset-inline-start:auto;inset-inline-end:11px;height:12px;margin-top:-6px;font-size:12px;pointer-events:none;transition:opacity 0.3s ease;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-arrow >*{line-height:1;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-arrow svg{display:inline-block;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-arrow .anticon{vertical-align:top;transition:transform 0.3s;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-arrow .anticon >svg{vertical-align:top;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-arrow .anticon:not(.ant-select-suffix){pointer-events:auto;}.ant-select-disabled :where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-arrow{cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-arrow >*:not(:last-child){margin-inline-end:8px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-selection-wrap{display:flex;width:100%;position:relative;min-width:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-selection-wrap:after{content:"\a0";width:0;overflow:hidden;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-prefix{flex:none;margin-inline-end:4px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-clear{position:absolute;top:50%;inset-inline-start:auto;inset-inline-end:11px;z-index:1;display:inline-block;width:12px;height:12px;margin-top:-6px;color:rgba(0,0,0,0.25);font-size:12px;font-style:normal;line-height:1;text-align:center;text-transform:none;cursor:pointer;opacity:0;transition:color 0.2s ease,opacity 0.3s ease;text-rendering:auto;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-clear:before{display:block;}:where(.css-dev-only-do-not-override-tjsggz).ant-select .ant-select-clear:hover{color:rgba(0,0,0,0.45);}:where(.css-dev-only-do-not-override-tjsggz).ant-select:hover .ant-select-clear{opacity:1;background:#fff;border-radius:50%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-status-error.ant-select-has-feedback .ant-select-clear,:where(.css-dev-only-do-not-override-tjsggz).ant-select-status-warning.ant-select-has-feedback .ant-select-clear,:where(.css-dev-only-do-not-override-tjsggz).ant-select-status-success.ant-select-has-feedback .ant-select-clear,:where(.css-dev-only-do-not-override-tjsggz).ant-select-status-validating.ant-select-has-feedback .ant-select-clear{inset-inline-end:33px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single{font-size:14px;height:32px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector{box-sizing:border-box;margin:0;padding:0;color:rgba(0,0,0,0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:inherit;display:flex;border-radius:6px;flex:1 1 auto;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector .ant-select-selection-wrap:after{line-height:30px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector .ant-select-selection-search{position:absolute;inset:0;width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector .ant-select-selection-search-input{width:100%;-webkit-appearance:textfield;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector .ant-select-selection-item,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector .ant-select-selection-placeholder{display:block;padding:0;line-height:30px;transition:all 0.3s,visibility 0s;align-self:center;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector .ant-select-selection-placeholder{transition:none;pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector:after,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector .ant-select-selection-item:empty:after,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single .ant-select-selector .ant-select-selection-placeholder:empty:after{display:inline-block;width:0;visibility:hidden;content:"\a0";}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-show-arrow .ant-select-selection-item,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-show-arrow .ant-select-selection-search,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder{padding-inline-end:18px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-open .ant-select-selection-item{color:rgba(0,0,0,0.25);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single:not(.ant-select-customize-input) .ant-select-selector{width:100%;height:100%;align-items:center;padding:0 11px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input{height:30px;font-size:14px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single:not(.ant-select-customize-input) .ant-select-selector:after{line-height:30px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-customize-input .ant-select-selector:after{display:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-customize-input .ant-select-selector .ant-select-selection-search{position:static;width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-customize-input .ant-select-selector .ant-select-selection-placeholder{position:absolute;inset-inline-start:0;inset-inline-end:0;padding:0 11px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-customize-input .ant-select-selector .ant-select-selection-placeholder:after{display:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm{font-size:14px;height:24px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector{box-sizing:border-box;margin:0;padding:0;color:rgba(0,0,0,0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:inherit;display:flex;border-radius:4px;flex:1 1 auto;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-wrap:after{line-height:22px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-search{position:absolute;inset:0;width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-search-input{width:100%;-webkit-appearance:textfield;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-item,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-placeholder{display:block;padding:0;line-height:22px;transition:all 0.3s,visibility 0s;align-self:center;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-placeholder{transition:none;pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector:after,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-item:empty:after,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-placeholder:empty:after{display:inline-block;width:0;visibility:hidden;content:"\a0";}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm.ant-select-show-arrow .ant-select-selection-item,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm.ant-select-show-arrow .ant-select-selection-search,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm.ant-select-show-arrow .ant-select-selection-placeholder{padding-inline-end:18px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm.ant-select-open .ant-select-selection-item{color:rgba(0,0,0,0.25);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector{width:100%;height:100%;align-items:center;padding:0 11px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input{height:22px;font-size:14px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector:after{line-height:22px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm.ant-select-customize-input .ant-select-selector:after{display:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm.ant-select-customize-input .ant-select-selector .ant-select-selection-search{position:static;width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm.ant-select-customize-input .ant-select-selector .ant-select-selection-placeholder{position:absolute;inset-inline-start:0;inset-inline-end:0;padding:0 11px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm.ant-select-customize-input .ant-select-selector .ant-select-selection-placeholder:after{display:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector{padding:0 7px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-search{inset-inline-end:28px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-item,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-placeholder{padding-inline-end:21px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg{font-size:16px;height:40px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector{box-sizing:border-box;margin:0;padding:0;color:rgba(0,0,0,0.88);font-size:16px;line-height:1.5714285714285714;list-style:none;font-family:inherit;display:flex;border-radius:8px;flex:1 1 auto;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector .ant-select-selection-wrap:after{line-height:38px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector .ant-select-selection-search{position:absolute;inset:0;width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector .ant-select-selection-search-input{width:100%;-webkit-appearance:textfield;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector .ant-select-selection-item,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector .ant-select-selection-placeholder{display:block;padding:0;line-height:38px;transition:all 0.3s,visibility 0s;align-self:center;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector .ant-select-selection-placeholder{transition:none;pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector:after,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector .ant-select-selection-item:empty:after,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg .ant-select-selector .ant-select-selection-placeholder:empty:after{display:inline-block;width:0;visibility:hidden;content:"\a0";}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg.ant-select-show-arrow .ant-select-selection-item,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg.ant-select-show-arrow .ant-select-selection-search,:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg.ant-select-show-arrow .ant-select-selection-placeholder{padding-inline-end:18px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg.ant-select-open .ant-select-selection-item{color:rgba(0,0,0,0.25);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector{width:100%;height:100%;align-items:center;padding:0 11px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input{height:38px;font-size:16px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector:after{line-height:38px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg.ant-select-customize-input .ant-select-selector:after{display:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg.ant-select-customize-input .ant-select-selector .ant-select-selection-search{position:static;width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg.ant-select-customize-input .ant-select-selector .ant-select-selection-placeholder{position:absolute;inset-inline-start:0;inset-inline-end:0;padding:0 11px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-single.ant-select-lg.ant-select-customize-input .ant-select-selector .ant-select-selection-placeholder:after{display:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow{position:relative;display:flex;flex:auto;flex-wrap:wrap;max-width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow-item{flex:none;align-self:center;max-width:100%;display:inline-flex;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item{display:flex;align-self:center;flex:none;box-sizing:border-box;max-width:100%;margin-block:2px;border-radius:4px;cursor:default;transition:font-size 0.3s,line-height 0.3s,height 0.3s;margin-inline-end:4px;padding-inline-start:8px;padding-inline-end:4px;}.ant-select-disabled:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item{color:rgba(0,0,0,0.25);border-color:transparent;cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item-content{display:inline-block;margin-inline-end:4px;overflow:hidden;white-space:pre;text-overflow:ellipsis;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item-remove{display:inline-flex;align-items:center;color:rgba(0,0,0,0.45);font-style:normal;line-height:inherit;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-weight:bold;font-size:10px;cursor:pointer;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item-remove >*{line-height:1;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item-remove svg{display:inline-block;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item-remove >.anticon{vertical-align:-0.2em;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item-remove:hover{color:rgba(0,0,0,0.88);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selector{display:flex;align-items:center;width:100%;height:100%;padding-inline:3px;padding-block:1px;border-radius:6px;}.ant-select-disabled:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selector{background:rgba(0,0,0,0.04);cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selector:after{display:inline-block;width:0;margin:2px 0;line-height:24px;visibility:hidden;content:"\a0";}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-item{height:24px;line-height:22px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-wrap{align-self:flex-start;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-wrap:after{line-height:24px;margin-block:2px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-prefix{margin-inline-start:8px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow-item+.ant-select-selection-overflow-item .ant-select-selection-search,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-prefix+.ant-select-selection-wrap .ant-select-selection-search{margin-inline-start:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow-item+.ant-select-selection-overflow-item .ant-select-selection-placeholder,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-prefix+.ant-select-selection-wrap .ant-select-selection-placeholder{inset-inline-start:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-overflow-item-suffix{min-height:24px;margin-block:2px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-search{display:inline-flex;position:relative;max-width:100%;margin-inline-start:8px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-search-input,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-search-mirror{height:24px;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';line-height:24px;transition:all 0.3s;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-search-input{width:100%;min-width:4.1px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-search-mirror{position:absolute;top:0;inset-inline-start:0;inset-inline-end:auto;z-index:999;white-space:pre;visibility:hidden;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selection-placeholder{position:absolute;top:50%;inset-inline-start:8px;inset-inline-end:11px;transform:translateY(-50%);transition:all 0.3s;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple{font-size:14px;}.ant-select-show-search:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple .ant-select-selector{cursor:text;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-show-arrow .ant-select-selector,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-allow-clear .ant-select-selector{padding-inline-end:24px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow{position:relative;display:flex;flex:auto;flex-wrap:wrap;max-width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow-item{flex:none;align-self:center;max-width:100%;display:inline-flex;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item{display:flex;align-self:center;flex:none;box-sizing:border-box;max-width:100%;margin-block:2px;border-radius:2px;cursor:default;transition:font-size 0.3s,line-height 0.3s,height 0.3s;margin-inline-end:4px;padding-inline-start:8px;padding-inline-end:4px;}.ant-select-disabled:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item{color:rgba(0,0,0,0.25);border-color:transparent;cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item-content{display:inline-block;margin-inline-end:4px;overflow:hidden;white-space:pre;text-overflow:ellipsis;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item-remove{display:inline-flex;align-items:center;color:rgba(0,0,0,0.45);font-style:normal;line-height:inherit;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-weight:bold;font-size:10px;cursor:pointer;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item-remove >*{line-height:1;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item-remove svg{display:inline-block;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item-remove >.anticon{vertical-align:-0.2em;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item-remove:hover{color:rgba(0,0,0,0.88);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selector{display:flex;align-items:center;width:100%;height:100%;padding-inline:3px;padding-block:1px;border-radius:4px;}.ant-select-disabled:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selector{background:rgba(0,0,0,0.04);cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selector:after{display:inline-block;width:0;margin:2px 0;line-height:16px;visibility:hidden;content:"\a0";}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-item{height:16px;line-height:14px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-wrap{align-self:flex-start;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-wrap:after{line-height:16px;margin-block:2px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-prefix{margin-inline-start:8px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow-item+.ant-select-selection-overflow-item .ant-select-selection-search,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-prefix+.ant-select-selection-wrap .ant-select-selection-search{margin-inline-start:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow-item+.ant-select-selection-overflow-item .ant-select-selection-placeholder,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-prefix+.ant-select-selection-wrap .ant-select-selection-placeholder{inset-inline-start:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-overflow-item-suffix{min-height:16px;margin-block:2px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-search{display:inline-flex;position:relative;max-width:100%;margin-inline-start:8px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-search-input,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-search-mirror{height:16px;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';line-height:16px;transition:all 0.3s;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-search-input{width:100%;min-width:4.1px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-search-mirror{position:absolute;top:0;inset-inline-start:0;inset-inline-end:auto;z-index:999;white-space:pre;visibility:hidden;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-placeholder{position:absolute;top:50%;inset-inline-start:8px;inset-inline-end:11px;transform:translateY(-50%);transition:all 0.3s;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm{font-size:14px;}.ant-select-show-search:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selector{cursor:text;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm.ant-select-show-arrow .ant-select-selector,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm.ant-select-allow-clear .ant-select-selector{padding-inline-end:24px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-placeholder{inset-inline:7px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-sm .ant-select-selection-search{margin-inline-start:2px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow{position:relative;display:flex;flex:auto;flex-wrap:wrap;max-width:100%;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow-item{flex:none;align-self:center;max-width:100%;display:inline-flex;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow .ant-select-selection-item{display:flex;align-self:center;flex:none;box-sizing:border-box;max-width:100%;margin-block:2px;border-radius:6px;cursor:default;transition:font-size 0.3s,line-height 0.3s,height 0.3s;margin-inline-end:4px;padding-inline-start:8px;padding-inline-end:4px;}.ant-select-disabled:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow .ant-select-selection-item{color:rgba(0,0,0,0.25);border-color:transparent;cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow .ant-select-selection-item-content{display:inline-block;margin-inline-end:4px;overflow:hidden;white-space:pre;text-overflow:ellipsis;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow .ant-select-selection-item-remove{display:inline-flex;align-items:center;color:rgba(0,0,0,0.45);font-style:normal;line-height:inherit;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-weight:bold;font-size:10px;cursor:pointer;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow .ant-select-selection-item-remove >*{line-height:1;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow .ant-select-selection-item-remove svg{display:inline-block;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow .ant-select-selection-item-remove >.anticon{vertical-align:-0.2em;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow .ant-select-selection-item-remove:hover{color:rgba(0,0,0,0.88);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selector{display:flex;align-items:center;width:100%;height:100%;padding-inline:3px;padding-block:1px;border-radius:8px;}.ant-select-disabled:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selector{background:rgba(0,0,0,0.04);cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selector:after{display:inline-block;width:0;margin:2px 0;line-height:32px;visibility:hidden;content:"\a0";}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-item{height:32px;line-height:30px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-wrap{align-self:flex-start;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-wrap:after{line-height:32px;margin-block:2px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-prefix{margin-inline-start:8px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow-item+.ant-select-selection-overflow-item .ant-select-selection-search,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-prefix+.ant-select-selection-wrap .ant-select-selection-search{margin-inline-start:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow-item+.ant-select-selection-overflow-item .ant-select-selection-placeholder,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-prefix+.ant-select-selection-wrap .ant-select-selection-placeholder{inset-inline-start:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-overflow-item-suffix{min-height:32px;margin-block:2px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-search{display:inline-flex;position:relative;max-width:100%;margin-inline-start:8px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-search-input,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-search-mirror{height:32px;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';line-height:32px;transition:all 0.3s;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-search-input{width:100%;min-width:4.1px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-search-mirror{position:absolute;top:0;inset-inline-start:0;inset-inline-end:auto;z-index:999;white-space:pre;visibility:hidden;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selection-placeholder{position:absolute;top:50%;inset-inline-start:8px;inset-inline-end:11px;transform:translateY(-50%);transition:all 0.3s;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg{font-size:16px;}.ant-select-show-search:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg .ant-select-selector{cursor:text;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg.ant-select-show-arrow .ant-select-selector,:where(.css-dev-only-do-not-override-tjsggz).ant-select-multiple.ant-select-lg.ant-select-allow-clear .ant-select-selector{padding-inline-end:24px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown{box-sizing:border-box;margin:0;padding:4px;color:rgba(0,0,0,0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';position:absolute;top:-9999px;z-index:1050;overflow:hidden;font-variant:initial;background-color:#ffffff;border-radius:8px;outline:none;box-shadow:0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-enter.ant-slide-up-enter-active.ant-select-dropdown-placement-bottomLeft,:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-appear.ant-slide-up-appear-active.ant-select-dropdown-placement-bottomLeft{animation-name:css-dev-only-do-not-override-tjsggz-antSlideUpIn;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-enter.ant-slide-up-enter-active.ant-select-dropdown-placement-topLeft,:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-appear.ant-slide-up-appear-active.ant-select-dropdown-placement-topLeft,:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-enter.ant-slide-up-enter-active.ant-select-dropdown-placement-topRight,:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-appear.ant-slide-up-appear-active.ant-select-dropdown-placement-topRight{animation-name:css-dev-only-do-not-override-tjsggz-antSlideDownIn;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-leave.ant-slide-up-leave-active.ant-select-dropdown-placement-bottomLeft{animation-name:css-dev-only-do-not-override-tjsggz-antSlideUpOut;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-leave.ant-slide-up-leave-active.ant-select-dropdown-placement-topLeft,:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown.ant-slide-up-leave.ant-slide-up-leave-active.ant-select-dropdown-placement-topRight{animation-name:css-dev-only-do-not-override-tjsggz-antSlideDownOut;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown-hidden{display:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item{position:relative;display:block;min-height:32px;padding:5px 12px;color:rgba(0,0,0,0.88);font-weight:normal;font-size:14px;line-height:1.5714285714285714;box-sizing:border-box;cursor:pointer;transition:background 0.3s ease;border-radius:4px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-group{color:rgba(0,0,0,0.45);font-size:12px;cursor:default;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option{display:flex;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-content{flex:auto;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-state{flex:none;display:flex;align-items:center;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-active:not(.ant-select-item-option-disabled){background-color:rgba(0,0,0,0.04);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled){color:rgba(0,0,0,0.88);font-weight:600;background-color:#e6f4ff;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) .ant-select-item-option-state{color:#1677ff;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-disabled{color:rgba(0,0,0,0.25);cursor:not-allowed;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-disabled.ant-select-item-option-selected{background-color:rgba(0,0,0,0.04);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-grouped{padding-inline-start:24px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-empty{position:relative;display:block;min-height:32px;padding:5px 12px;color:rgba(0,0,0,0.25);font-weight:normal;font-size:14px;line-height:1.5714285714285714;box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-selected:has(+ .ant-select-item-option-selected){border-end-start-radius:0;border-end-end-radius:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown .ant-select-item-option-selected:has(+ .ant-select-item-option-selected)+.ant-select-item-option-selected{border-start-start-radius:0;border-start-end-radius:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-dropdown-rtl{direction:rtl;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-enter,:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-enter.ant-slide-up-enter-active,:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-appear.ant-slide-up-appear-active{animation-name:css-dev-only-do-not-override-tjsggz-antSlideUpIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-leave.ant-slide-up-leave-active{animation-name:css-dev-only-do-not-override-tjsggz-antSlideUpOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-enter,:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-appear{transform:scale(0);transform-origin:0% 0%;opacity:0;animation-timing-function:cubic-bezier(0.23, 1, 0.32, 1);}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-enter-prepare,:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-appear-prepare{transform:scale(1);}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-up-leave{animation-timing-function:cubic-bezier(0.755, 0.05, 0.855, 0.06);}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-enter,:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-enter.ant-slide-down-enter-active,:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-appear.ant-slide-down-appear-active{animation-name:css-dev-only-do-not-override-tjsggz-antSlideDownIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-leave.ant-slide-down-leave-active{animation-name:css-dev-only-do-not-override-tjsggz-antSlideDownOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-enter,:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-appear{transform:scale(0);transform-origin:0% 0%;opacity:0;animation-timing-function:cubic-bezier(0.23, 1, 0.32, 1);}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-enter-prepare,:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-appear-prepare{transform:scale(1);}:where(.css-dev-only-do-not-override-tjsggz).ant-slide-down-leave{animation-timing-function:cubic-bezier(0.755, 0.05, 0.855, 0.06);}:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-enter,:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-enter.ant-move-up-enter-active,:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-appear.ant-move-up-appear-active{animation-name:css-dev-only-do-not-override-tjsggz-antMoveUpIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-leave.ant-move-up-leave-active{animation-name:css-dev-only-do-not-override-tjsggz-antMoveUpOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-enter,:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-appear{opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-dev-only-do-not-override-tjsggz).ant-move-up-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-enter,:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-enter.ant-move-down-enter-active,:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-appear.ant-move-down-appear-active{animation-name:css-dev-only-do-not-override-tjsggz-antMoveDownIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-leave.ant-move-down-leave-active{animation-name:css-dev-only-do-not-override-tjsggz-antMoveDownOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-enter,:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-appear{opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-dev-only-do-not-override-tjsggz).ant-move-down-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-rtl{direction:rtl;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:not(.ant-select-compact-last-item){margin-inline-end:-1px;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:hover>*,:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:active>*{z-index:2;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item.ant-select-focused{z-index:2;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item[disabled]>*{z-index:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:not(.ant-select-compact-first-item):not(.ant-select-compact-last-item)>.ant-select-selector{border-radius:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:not(.ant-select-compact-last-item).ant-select-compact-first-item>.ant-select-selector,:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:not(.ant-select-compact-last-item).ant-select-compact-first-item.ant-select-sm>.ant-select-selector,:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:not(.ant-select-compact-last-item).ant-select-compact-first-item.ant-select-lg>.ant-select-selector{border-start-end-radius:0;border-end-end-radius:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:not(.ant-select-compact-first-item).ant-select-compact-last-item>.ant-select-selector,:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:not(.ant-select-compact-first-item).ant-select-compact-last-item.ant-select-sm>.ant-select-selector,:where(.css-dev-only-do-not-override-tjsggz).ant-select-compact-item:not(.ant-select-compact-first-item).ant-select-compact-last-item.ant-select-lg>.ant-select-selector{border-start-start-radius:0;border-end-start-radius:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector{border:1px solid #d9d9d9;background:#ffffff;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{border-color:#4096ff;}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{border-color:#1677ff;box-shadow:0 0 0 2px rgba(5,145,255,0.1);outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-prefix{color:rgba(0,0,0,0.88);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-status-error:not(.ant-select-customize-input) .ant-select-selector{border:1px solid #ff4d4f;background:#ffffff;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{border-color:#ff7875;}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{border-color:#ff4d4f;box-shadow:0 0 0 2px rgba(255,38,5,0.06);outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-prefix{color:#ff4d4f;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-status-warning:not(.ant-select-customize-input) .ant-select-selector{border:1px solid #faad14;background:#ffffff;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-status-warning:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{border-color:#ffd666;}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-status-warning:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{border-color:#faad14;box-shadow:0 0 0 2px rgba(255,215,5,0.1);outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-status-warning:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-prefix{color:#faad14;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-disabled:not(.ant-select-customize-input) .ant-select-selector{background:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-outlined.ant-select-multiple .ant-select-selection-item{background:rgba(0,0,0,0.06);border:1px solid transparent;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled:not(.ant-select-customize-input) .ant-select-selector{background:rgba(0,0,0,0.04);border:1px solid transparent;color:rgba(0,0,0,0.88);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{background:rgba(0,0,0,0.06);}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{background:#ffffff;border-color:#1677ff;outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled.ant-select-status-error:not(.ant-select-customize-input) .ant-select-selector{background:#fff2f0;border:1px solid transparent;color:#ff4d4f;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled.ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{background:#fff1f0;}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled.ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{background:#ffffff;border-color:#ff4d4f;outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled.ant-select-status-warning:not(.ant-select-customize-input) .ant-select-selector{background:#fffbe6;border:1px solid transparent;color:#faad14;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled.ant-select-status-warning:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{background:#fff1b8;}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled.ant-select-status-warning:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{background:#ffffff;border-color:#faad14;outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled.ant-select-disabled:not(.ant-select-customize-input) .ant-select-selector{border-color:#d9d9d9;background:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-filled.ant-select-multiple .ant-select-selection-item{background:#ffffff;border:1px solid rgba(5,5,5,0.06);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-borderless .ant-select-selector{background:transparent;border:1px solid transparent;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-borderless.ant-select-disabled:not(.ant-select-customize-input) .ant-select-selector{color:rgba(0,0,0,0.25);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-borderless.ant-select-multiple .ant-select-selection-item{background:rgba(0,0,0,0.06);border:1px solid transparent;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-borderless.ant-select-status-error .ant-select-prefix,:where(.css-dev-only-do-not-override-tjsggz).ant-select-borderless.ant-select-status-error .ant-select-selection-item{color:#ff4d4f;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-borderless.ant-select-status-warning .ant-select-prefix,:where(.css-dev-only-do-not-override-tjsggz).ant-select-borderless.ant-select-status-warning .ant-select-selection-item{color:#faad14;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined:not(.ant-select-customize-input) .ant-select-selector{border-width:0 0 1px 0;border-style:none none solid none;border-color:#d9d9d9;background:#ffffff;border-radius:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{border-color:#4096ff;}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{border-color:#1677ff;outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-prefix{color:rgba(0,0,0,0.88);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-status-error:not(.ant-select-customize-input) .ant-select-selector{border-width:0 0 1px 0;border-style:none none solid none;border-color:#ff4d4f;background:#ffffff;border-radius:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{border-color:#ff7875;}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{border-color:#ff4d4f;outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-prefix{color:#ff4d4f;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-status-warning:not(.ant-select-customize-input) .ant-select-selector{border-width:0 0 1px 0;border-style:none none solid none;border-color:#faad14;background:#ffffff;border-radius:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-status-warning:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector{border-color:#ffd666;}.ant-select-focused:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-status-warning:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{border-color:#faad14;outline:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-status-warning:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-prefix{color:#faad14;}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-disabled:not(.ant-select-customize-input) .ant-select-selector{color:rgba(0,0,0,0.25);}:where(.css-dev-only-do-not-override-tjsggz).ant-select-underlined.ant-select-multiple .ant-select-selection-item{background:rgba(0,0,0,0.06);border:1px solid transparent;}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antSlideUpIn">@keyframes css-dev-only-do-not-override-tjsggz-antSlideUpIn{0%{transform:scaleY(0.8);transform-origin:0% 0%;opacity:0;}100%{transform:scaleY(1);transform-origin:0% 0%;opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antSlideDownIn">@keyframes css-dev-only-do-not-override-tjsggz-antSlideDownIn{0%{transform:scaleY(0.8);transform-origin:100% 100%;opacity:0;}100%{transform:scaleY(1);transform-origin:100% 100%;opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antSlideUpOut">@keyframes css-dev-only-do-not-override-tjsggz-antSlideUpOut{0%{transform:scaleY(1);transform-origin:0% 0%;opacity:1;}100%{transform:scaleY(0.8);transform-origin:0% 0%;opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antSlideDownOut">@keyframes css-dev-only-do-not-override-tjsggz-antSlideDownOut{0%{transform:scaleY(1);transform-origin:100% 100%;opacity:1;}100%{transform:scaleY(0.8);transform-origin:100% 100%;opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antMoveUpIn">@keyframes css-dev-only-do-not-override-tjsggz-antMoveUpIn{0%{transform:translate3d(0, -100%, 0);transform-origin:0 0;opacity:0;}100%{transform:translate3d(0, 0, 0);transform-origin:0 0;opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antMoveUpOut">@keyframes css-dev-only-do-not-override-tjsggz-antMoveUpOut{0%{transform:translate3d(0, 0, 0);transform-origin:0 0;opacity:1;}100%{transform:translate3d(0, -100%, 0);transform-origin:0 0;opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antMoveDownIn">@keyframes css-dev-only-do-not-override-tjsggz-antMoveDownIn{0%{transform:translate3d(0, 100%, 0);transform-origin:0 0;opacity:0;}100%{transform:translate3d(0, 0, 0);transform-origin:0 0;opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antMoveDownOut">@keyframes css-dev-only-do-not-override-tjsggz-antMoveDownOut{0%{transform:translate3d(0, 0, 0);transform-origin:0 0;opacity:1;}100%{transform:translate3d(0, 100%, 0);transform-origin:0 0;opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="1dax3bm" data-token-hash="v4zkj0" data-cache-path="v4zkj0|Layout-Layout|ant-layout|anticon">:where(.css-dev-only-do-not-override-tjsggz).ant-layout{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout::before,:where(.css-dev-only-do-not-override-tjsggz).ant-layout::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout [class^="ant-layout"],:where(.css-dev-only-do-not-override-tjsggz).ant-layout [class*=" ant-layout"]{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout [class^="ant-layout"]::before,:where(.css-dev-only-do-not-override-tjsggz).ant-layout [class*=" ant-layout"]::before,:where(.css-dev-only-do-not-override-tjsggz).ant-layout [class^="ant-layout"]::after,:where(.css-dev-only-do-not-override-tjsggz).ant-layout [class*=" ant-layout"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout{display:flex;flex:auto;flex-direction:column;min-height:0;background:#f5f5f5;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout,:where(.css-dev-only-do-not-override-tjsggz).ant-layout *{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout.ant-layout-has-sider{flex-direction:row;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout.ant-layout-has-sider >.ant-layout,:where(.css-dev-only-do-not-override-tjsggz).ant-layout.ant-layout-has-sider >.ant-layout-content{width:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout .ant-layout-header,:where(.css-dev-only-do-not-override-tjsggz).ant-layout.ant-layout-footer{flex:0 0 auto;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout-rtl{direction:rtl;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout-header{height:64px;padding:0 50px;color:rgba(0,0,0,0.88);line-height:64px;background:#001529;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout-header .ant-menu{line-height:inherit;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout-footer{padding:24px 50px;color:rgba(0,0,0,0.88);font-size:14px;background:#f5f5f5;}:where(.css-dev-only-do-not-override-tjsggz).ant-layout-content{flex:auto;color:rgba(0,0,0,0.88);min-height:0;}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="1outx1" data-token-hash="v4zkj0" data-cache-path="v4zkj0|Spin-Spin|ant-spin|anticon">:where(.css-dev-only-do-not-override-tjsggz).ant-spin{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin::before,:where(.css-dev-only-do-not-override-tjsggz).ant-spin::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin [class^="ant-spin"],:where(.css-dev-only-do-not-override-tjsggz).ant-spin [class*=" ant-spin"]{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin [class^="ant-spin"]::before,:where(.css-dev-only-do-not-override-tjsggz).ant-spin [class*=" ant-spin"]::before,:where(.css-dev-only-do-not-override-tjsggz).ant-spin [class^="ant-spin"]::after,:where(.css-dev-only-do-not-override-tjsggz).ant-spin [class*=" ant-spin"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin{box-sizing:border-box;margin:0;padding:0;color:#1677ff;font-size:0;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';position:absolute;display:none;text-align:center;vertical-align:middle;opacity:0;transition:transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-spinning{position:relative;display:inline-block;opacity:1;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-text{font-size:14px;padding-top:5px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-fullscreen{position:fixed;width:100vw;height:100vh;background-color:rgba(0,0,0,0.45);z-index:1000;inset:0;display:flex;align-items:center;flex-direction:column;justify-content:center;opacity:0;visibility:hidden;transition:all 0.2s;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-fullscreen-show{opacity:1;visibility:visible;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-fullscreen .ant-spin .ant-spin-dot-holder{color:#fff;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-fullscreen .ant-spin .ant-spin-text{color:#fff;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading{position:relative;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin{position:absolute;top:0;inset-inline-start:0;z-index:4;display:block;width:100%;height:100%;max-height:400px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin .ant-spin-dot{position:absolute;top:50%;inset-inline-start:50%;margin:-10px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin .ant-spin-text{position:absolute;top:50%;width:100%;text-shadow:0 1px 2px #ffffff;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin.ant-spin-show-text .ant-spin-dot{margin-top:-20px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin-sm .ant-spin-dot{margin:-7px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin-sm .ant-spin-text{padding-top:2px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin-sm.ant-spin-show-text .ant-spin-dot{margin-top:-17px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin-lg .ant-spin-dot{margin:-16px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin-lg .ant-spin-text{padding-top:11px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading >div>.ant-spin-lg.ant-spin-show-text .ant-spin-dot{margin-top:-26px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading .ant-spin-container{position:relative;transition:opacity 0.3s;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading .ant-spin-container::after{position:absolute;top:0;inset-inline-end:0;bottom:0;inset-inline-start:0;z-index:10;width:100%;height:100%;background:#ffffff;opacity:0;transition:all 0.3s;content:"";pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading .ant-spin-blur{clear:both;opacity:0.5;user-select:none;pointer-events:none;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-nested-loading .ant-spin-blur::after{opacity:0.4;pointer-events:auto;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-tip{color:rgba(0,0,0,0.45);}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-holder{width:1em;height:1em;font-size:20px;display:inline-block;transition:transform 0.3s ease,opacity 0.3s ease;transform-origin:50% 50%;line-height:1;color:#1677ff;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-holder-hidden{transform:scale(0.3);opacity:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-progress{position:absolute;inset:0;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot{position:relative;display:inline-block;font-size:20px;width:1em;height:1em;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-item{position:absolute;display:block;width:9px;height:9px;background:currentColor;border-radius:100%;transform:scale(0.75);transform-origin:50% 50%;opacity:0.3;animation-name:css-dev-only-do-not-override-tjsggz-antSpinMove;animation-duration:1s;animation-iteration-count:infinite;animation-timing-function:linear;animation-direction:alternate;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-item:nth-child(1){top:0;inset-inline-start:0;animation-delay:0s;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-item:nth-child(2){top:0;inset-inline-end:0;animation-delay:0.4s;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-item:nth-child(3){inset-inline-end:0;bottom:0;animation-delay:0.8s;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-item:nth-child(4){bottom:0;inset-inline-start:0;animation-delay:1.2s;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-spin{transform:rotate(45deg);animation-name:css-dev-only-do-not-override-tjsggz-antRotate;animation-duration:1.2s;animation-iteration-count:infinite;animation-timing-function:linear;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-circle{stroke-linecap:round;transition:stroke-dashoffset 0.3s ease,stroke-dasharray 0.3s ease,stroke 0.3s ease,stroke-width 0.3s ease,opacity 0.3s ease;fill-opacity:0;stroke:currentcolor;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin .ant-spin-dot-circle-bg{stroke:rgba(0,0,0,0.06);}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-sm .ant-spin-dot,:where(.css-dev-only-do-not-override-tjsggz).ant-spin-sm .ant-spin-dot-holder{font-size:14px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-sm .ant-spin-dot-holder i{width:6px;height:6px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-lg .ant-spin-dot,:where(.css-dev-only-do-not-override-tjsggz).ant-spin-lg .ant-spin-dot-holder{font-size:32px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin-lg .ant-spin-dot-holder i{width:14px;height:14px;}:where(.css-dev-only-do-not-override-tjsggz).ant-spin.ant-spin-show-text .ant-spin-text{display:block;}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antSpinMove">@keyframes css-dev-only-do-not-override-tjsggz-antSpinMove{to{opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-tjsggz-antRotate">@keyframes css-dev-only-do-not-override-tjsggz-antRotate{to{transform:rotate(405deg);}}</style><style data-rc-order="prependQueue" data-css-hash="154c9a2" data-token-hash="v4zkj0" data-cache-path="v4zkj0|ant-design-icons|anticon">.anticon{display:inline-flex;align-items:center;color:inherit;font-style:normal;line-height:0;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}.anticon >*{line-height:1;}.anticon svg{display:inline-block;}.anticon .anticon .anticon-icon{display:block;}</style>
    <meta charset="utf-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="theme-color" content="#000000">
    <meta name="description" content="TurdParty - File Upload, VM Management, and Injection">
    <link rel="apple-touch-icon" href="/logo192.png">
    <link rel="manifest" href="/manifest.json">
    <title>TurdParty</title>
  <script defer="" src="/static/js/bundle.js"></script><style>.main-page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.main-page-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.main-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.main-page-header h2 {
  margin-bottom: 0;
}

.main-page-steps {
  margin-bottom: 30px;
}

.main-page-alert {
  margin-bottom: 20px;
}

.main-page-content {
  padding: 10px 0;
}

.step-card {
  margin-bottom: 20px;
}

.completion-card {
  text-align: center;
  padding: 20px;
}

.completion-icon {
  font-size: 64px;
  color: #52c41a;
  margin-bottom: 20px;
}

/* Upload component styling */
.ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.3s;
  padding: 20px;
}

.ant-upload-drag:hover {
  border-color: #1890ff;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

.ant-upload-text {
  font-size: 16px;
  margin: 8px 0;
}

.ant-upload-hint {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

/* Form styling */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-select-selection-item {
  font-weight: normal;
}

/* Steps styling */
.ant-steps-item-title {
  font-weight: 500;
}

.ant-steps-item-description {
  font-size: 12px;
}

/* Dark mode for docs */
.dark-mode {
  background-color: #1f1f1f;
  color: #f0f0f0;
}

.dark-mode pre {
  background-color: #2d2d2d;
  border: 1px solid #444;
}

.dark-mode code {
  background-color: #2d2d2d;
  color: #e6e6e6;
}

.dark-mode a {
  color: #1890ff;
}

.dark-mode h1, .dark-mode h2, .dark-mode h3, .dark-mode h4, .dark-mode h5, .dark-mode h6 {
  color: #f0f0f0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .main-page-header h2 {
    margin-bottom: 16px;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
}

/* Template grid and cards */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border: 2px solid #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.template-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.template-card-content h4 {
  margin-bottom: 8px;
}

.template-card-content .ant-typography-secondary {
  flex-grow: 1;
  margin-bottom: 12px;
}

/* File upload area */
.ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: border-color 0.3s;
}

.ant-upload-drag:hover {
  border-color: #1890ff;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

/* Radio buttons */
.ant-radio-group {
  margin-bottom: 16px;
}

/* Dark mode template cards */
body.dark-mode .template-card {
  background-color: #2a2a2a;
  border-color: #555;
}

body.dark-mode .template-card.selected {
  border-color: #1890ff;
}

body.dark-mode .ant-tag {
  border-color: transparent;
}

/* Folder upload styles */
.folder-upload-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f6f6f6;
  border-radius: 4px;
}

body.dark-mode .folder-upload-info {
  background-color: #3a3a3a;
}

.file-list-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

body.dark-mode .file-list-item {
  border-bottom-color: #555;
}

.file-list-item-icon {
  margin-right: 8px;
  font-size: 16px;
}

.file-list-item-name {
  flex-grow: 1;
}

.file-list-item-size {
  color: #888;
  font-size: 12px;
}

body.dark-mode .file-list-item-size {
  color: #aaa;
} 
/*# sourceMappingURL=data:application/json;base64,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 */</style><style>.file-upload-component {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.file-upload-component .ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

/* Add dark mode styles for upload area */
:root[theme-mode="dark"] .file-upload-component .ant-upload-drag,
.ant-app[class*="-dark"] .file-upload-component .ant-upload-drag,
.ant-app-dark .file-upload-component .ant-upload-drag {
  border-color: #434343;
  background: #1f1f1f;
}

.file-upload-component .ant-upload-drag:hover {
  border-color: #1890ff;
  background: #f0f7ff;
}

/* Add dark mode hover state */
:root[theme-mode="dark"] .file-upload-component .ant-upload-drag:hover,
.ant-app[class*="-dark"] .file-upload-component .ant-upload-drag:hover,
.ant-app-dark .file-upload-component .ant-upload-drag:hover {
  border-color: #177ddc;
  background: #111a2c;
}

.file-upload-component .ant-upload-drag-icon {
  margin-bottom: 16px;
  color: #1890ff;
}

.file-upload-component .ant-upload-drag-icon .anticon {
  font-size: 48px;
}

.file-upload-component .ant-upload-text {
  margin: 0 0 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: 500;
}

/* Add dark mode text color */
:root[theme-mode="dark"] .file-upload-component .ant-upload-text,
.ant-app[class*="-dark"] .file-upload-component .ant-upload-text,
.ant-app-dark .file-upload-component .ant-upload-text {
  color: rgba(255, 255, 255, 0.85);
}

.file-upload-component .ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  line-height: 1.5;
}

/* Add dark mode hint color */
:root[theme-mode="dark"] .file-upload-component .ant-upload-hint,
.ant-app[class*="-dark"] .file-upload-component .ant-upload-hint,
.ant-app-dark .file-upload-component .ant-upload-hint {
  color: rgba(255, 255, 255, 0.45);
}

.file-upload-component .upload-error-alert {
  margin-bottom: 16px;
}

.file-upload-component .ant-progress {
  margin: 16px 0;
}

.file-upload-component .upload-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  height: 40px;
  padding: 0 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-component .upload-button:hover:not(:disabled) {
  background-color: #40a9ff;
}

.file-upload-component .upload-button:active:not(:disabled) {
  background-color: #096dd9;
}

.file-upload-component .upload-button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.file-upload-component .upload-button.uploading {
  background-color: #1890ff;
  opacity: 0.8;
  cursor: wait;
}

/* More comprehensive dark mode styles */
body.dark-mode .file-upload-component .ant-upload-drag,
[data-theme="dark"] .file-upload-component .ant-upload-drag {
  border-color: #434343;
  background: #1f1f1f;
}

body.dark-mode .file-upload-component .ant-upload-drag:hover,
[data-theme="dark"] .file-upload-component .ant-upload-drag:hover {
  border-color: #177ddc;
  background: #111a2c;
}

body.dark-mode .file-upload-component .ant-upload-text,
[data-theme="dark"] .file-upload-component .ant-upload-text {
  color: rgba(255, 255, 255, 0.85);
}

body.dark-mode .file-upload-component .ant-upload-hint,
[data-theme="dark"] .file-upload-component .ant-upload-hint {
  color: rgba(255, 255, 255, 0.45);
}

/* Target the component's own dark mode class */
.file-upload-component.dark-mode .ant-upload-drag {
  border-color: #434343;
  background: #1f1f1f;
}

.file-upload-component.dark-mode .ant-upload-drag:hover {
  border-color: #177ddc;
  background: #111a2c;
}

.file-upload-component.dark-mode .ant-upload-text {
  color: rgba(255, 255, 255, 0.85);
}

.file-upload-component.dark-mode .ant-upload-hint {
  color: rgba(255, 255, 255, 0.45);
}

.file-upload-component.dark-mode .upload-button {
  background-color: #177ddc;
}

.file-upload-component.dark-mode .upload-button:hover:not(:disabled) {
  background-color: #2b8dec;
}

.file-upload-component.dark-mode .upload-button:active:not(:disabled) {
  background-color: #1068bf;
}

.file-upload-component.dark-mode .upload-button:disabled {
  background-color: #303030;
}

.file-upload-component.dark-mode .ant-upload-drag-icon {
  color: #177ddc;  /* Darker blue for dark mode */
}

.file-upload-component.dark-mode .ant-input,
.file-upload-component.dark-mode .ant-input-textarea {
  background-color: #141414;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

.file-upload-component.dark-mode .ant-input:hover,
.file-upload-component.dark-mode .ant-input-textarea:hover {
  border-color: #165996;
}

.file-upload-component.dark-mode .ant-input:focus,
.file-upload-component.dark-mode .ant-input-textarea:focus {
  border-color: #177ddc;
  box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
}

.file-upload-component.dark-mode .ant-form-item-label > label {
  color: rgba(255, 255, 255, 0.85);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .file-upload-component {
    max-width: 100%;
    padding: 0 16px;
  }
  
  .file-upload-component .ant-upload-drag-icon .anticon {
    font-size: 36px;
  }
  
  .file-upload-component .ant-upload-text {
    font-size: 14px;
  }
  
  .file-upload-component .ant-upload-hint {
    font-size: 12px;
  }
} 
/*# sourceMappingURL=data:application/json;base64,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 */</style><style>.file-upload-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.file-upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.file-upload-table {
  margin-top: 20px;
}

.file-upload-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

/* Customize the upload dragger */
.ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.ant-upload-drag:hover {
  border-color: #1890ff;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

.ant-upload-text {
  margin: 0 0 4px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
}

.ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

/* Table styles */
.file-upload-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.file-upload-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.file-upload-table .ant-space {
  gap: 8px;
}

/* File size column alignment */
.file-upload-table .ant-table-cell:nth-child(2) {
  text-align: right;
}

/* Status tag styling */
.file-upload-table .ant-tag {
  min-width: 60px;
  text-align: center;
}

/*# sourceMappingURL=data:application/json;base64,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 */</style><style>.file-selection-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.file-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.file-selection-table {
  margin-top: 20px;
}

.file-selection-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

/* Table styles */
.file-selection-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.file-selection-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.file-selection-table .ant-space {
  gap: 8px;
}

/* File path styling */
.file-selection-table .ant-table-cell:nth-child(3) {
  font-family: monospace;
  color: #0066cc;
}

/* Permissions styling */
.file-selection-table code {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}

/* Form styles */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-select-selection-item {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Target path input styling */
.ant-input-group-addon {
  background-color: #f5f5f5;
  color: #666;
  font-weight: 500;
}

/*# sourceMappingURL=data:application/json;base64,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 */</style><style>.vagrant-vm-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.vagrant-vm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.vagrant-vm-table {
  margin-top: 20px;
}

.vagrant-vm-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

/* Table styles */
.vagrant-vm-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.vagrant-vm-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.vagrant-vm-table .ant-space {
  gap: 8px;
}

/* Resource inputs in form */
.resource-inputs {
  display: flex;
  gap: 16px;
}

.resource-inputs .ant-form-item {
  flex: 1 1;
}

/* VM Details Modal */
.vm-details {
  margin-top: 16px;
}

.vm-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.vm-status-card {
  margin-bottom: 16px;
}

.vm-status-card .ant-statistic-title {
  font-size: 14px;
}

.vm-status-card .ant-statistic-content {
  font-size: 24px;
}

.last-action {
  margin-top: 8px;
  font-size: 12px;
}

.vm-description {
  margin-top: 16px;
}

.vm-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Status tag styling */
.ant-tag .anticon {
  margin-right: 4px;
}

/* Form styles */
.ant-form-item-label > label {
  font-weight: 500;
}

/* Descriptions styling */
.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #f5f5f5;
  font-weight: 500;
}

/* Tab styling */
.ant-tabs-tab {
  font-weight: 500;
}

/* Card styling */
.ant-card-head {
  background-color: #f5f5f5;
}

/* Button styling in action cards */
.vm-actions .ant-card-body .ant-space {
  flex-wrap: wrap;
}

/* Template option styles */
.template-option {
  display: flex;
  flex-direction: column;
}

.template-name {
  font-weight: bold;
}

.template-description {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/*# sourceMappingURL=data:application/json;base64,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 */</style><style>.vm-injection-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.vm-injection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.vm-injection-table {
  margin-top: 20px;
}

.vm-injection-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

/* Table styles */
.vm-injection-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.vm-injection-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.vm-injection-table .ant-space {
  gap: 8px;
}

/* Injection Details Modal */
.injection-details {
  margin-top: 16px;
}

.injection-status-card {
  margin-bottom: 16px;
}

.injection-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24px;
}

.injection-status .ant-tag {
  font-size: 14px;
  padding: 4px 8px;
}

.injection-description {
  margin-top: 16px;
}

/* Status tag styling */
.ant-tag .anticon {
  margin-right: 4px;
}

/* Form styles */
.ant-form-item-label > label {
  font-weight: 500;
}

/* Descriptions styling */
.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #f5f5f5;
  font-weight: 500;
}

/* Steps styling */
.ant-steps-item-title {
  font-weight: 500;
}

.ant-steps-item-description {
  font-size: 12px;
}

/* Command code styling */
.injection-details code {
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
  display: block;
  white-space: pre-wrap;
}

/* Select dropdown styling */
.ant-select-selection-item {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9wYWdlcy9WbV9pbmplY3Rpb24vc3R5bGVzLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQWE7RUFDYixpQkFBaUI7RUFDakIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGFBQWE7RUFDYiw4QkFBOEI7RUFDOUIsbUJBQW1CO0VBQ25CLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixZQUFZO0FBQ2Q7O0FBRUEsaUJBQWlCO0FBQ2pCO0VBQ0UseUJBQXlCO0VBQ3pCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLHlCQUF5QjtBQUMzQjs7QUFFQSxvQ0FBb0M7QUFDcEM7RUFDRSxRQUFRO0FBQ1Y7O0FBRUEsNEJBQTRCO0FBQzVCO0VBQ0UsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQiw4QkFBOEI7RUFDOUIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsZUFBZTtFQUNmLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQSx1QkFBdUI7QUFDdkI7RUFDRSxpQkFBaUI7QUFDbkI7O0FBRUEsZ0JBQWdCO0FBQ2hCO0VBQ0UsZ0JBQWdCO0FBQ2xCOztBQUVBLHlCQUF5QjtBQUN6QjtFQUNFLHlCQUF5QjtFQUN6QixnQkFBZ0I7QUFDbEI7O0FBRUEsa0JBQWtCO0FBQ2xCO0VBQ0UsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsZUFBZTtBQUNqQjs7QUFFQSx5QkFBeUI7QUFDekI7RUFDRSx5QkFBeUI7RUFDekIsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtFQUNsQixzQkFBc0I7RUFDdEIsY0FBYztFQUNkLHFCQUFxQjtBQUN2Qjs7QUFFQSw0QkFBNEI7QUFDNUI7RUFDRSxlQUFlO0VBQ2YsZ0JBQWdCO0VBQ2hCLHVCQUF1QjtBQUN6QiIsInNvdXJjZXNDb250ZW50IjpbIi52bS1pbmplY3Rpb24tY29udGFpbmVyIHtcbiAgcGFkZGluZzogMjBweDtcbiAgbWF4LXdpZHRoOiAxMjAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xufVxuXG4udm0taW5qZWN0aW9uLWhlYWRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbn1cblxuLnZtLWluamVjdGlvbi10YWJsZSB7XG4gIG1hcmdpbi10b3A6IDIwcHg7XG59XG5cbi52bS1pbmplY3Rpb24tYWxlcnQge1xuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xufVxuXG4ubG9hZGluZy1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgaGVpZ2h0OiA1MHZoO1xufVxuXG4vKiBUYWJsZSBzdHlsZXMgKi9cbi52bS1pbmplY3Rpb24tdGFibGUgLmFudC10YWJsZS10aGVhZCA+IHRyID4gdGgge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4udm0taW5qZWN0aW9uLXRhYmxlIC5hbnQtdGFibGUtdGJvZHkgPiB0cjpob3ZlciA+IHRkIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjdmZjtcbn1cblxuLyogQnV0dG9uIHNwYWNpbmcgaW4gYWN0aW9uIGNvbHVtbiAqL1xuLnZtLWluamVjdGlvbi10YWJsZSAuYW50LXNwYWNlIHtcbiAgZ2FwOiA4cHg7XG59XG5cbi8qIEluamVjdGlvbiBEZXRhaWxzIE1vZGFsICovXG4uaW5qZWN0aW9uLWRldGFpbHMge1xuICBtYXJnaW4tdG9wOiAxNnB4O1xufVxuXG4uaW5qZWN0aW9uLXN0YXR1cy1jYXJkIHtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cblxuLmluamVjdGlvbi1zdGF0dXMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIG1hcmdpbi10b3A6IDI0cHg7XG59XG5cbi5pbmplY3Rpb24tc3RhdHVzIC5hbnQtdGFnIHtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBwYWRkaW5nOiA0cHggOHB4O1xufVxuXG4uaW5qZWN0aW9uLWRlc2NyaXB0aW9uIHtcbiAgbWFyZ2luLXRvcDogMTZweDtcbn1cblxuLyogU3RhdHVzIHRhZyBzdHlsaW5nICovXG4uYW50LXRhZyAuYW50aWNvbiB7XG4gIG1hcmdpbi1yaWdodDogNHB4O1xufVxuXG4vKiBGb3JtIHN0eWxlcyAqL1xuLmFudC1mb3JtLWl0ZW0tbGFiZWwgPiBsYWJlbCB7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG5cbi8qIERlc2NyaXB0aW9ucyBzdHlsaW5nICovXG4uYW50LWRlc2NyaXB0aW9ucy1ib3JkZXJlZCAuYW50LWRlc2NyaXB0aW9ucy1pdGVtLWxhYmVsIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbn1cblxuLyogU3RlcHMgc3R5bGluZyAqL1xuLmFudC1zdGVwcy1pdGVtLXRpdGxlIHtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbn1cblxuLmFudC1zdGVwcy1pdGVtLWRlc2NyaXB0aW9uIHtcbiAgZm9udC1zaXplOiAxMnB4O1xufVxuXG4vKiBDb21tYW5kIGNvZGUgc3R5bGluZyAqL1xuLmluamVjdGlvbi1kZXRhaWxzIGNvZGUge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xuICBwYWRkaW5nOiA0cHggOHB4O1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIGZvbnQtZmFtaWx5OiBtb25vc3BhY2U7XG4gIGRpc3BsYXk6IGJsb2NrO1xuICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7XG59XG5cbi8qIFNlbGVjdCBkcm9wZG93biBzdHlsaW5nICovXG4uYW50LXNlbGVjdC1zZWxlY3Rpb24taXRlbSB7XG4gIG1heC13aWR0aDogMTAwJTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */</style><style>.vm-status-container {
  padding: 24px;
  width: 100%;
}

.vm-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.vm-status-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.vm-status-content {
  display: flex;
  gap: 24px;
}

.vm-status-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.vm-status-details {
  flex-grow: 1;
}

.vm-list-card {
  height: 100%;
}

.vm-list {
  max-height: 500px;
  overflow-y: auto;
}

.vm-list-item {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.vm-list-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.vm-list-item.selected {
  background-color: rgba(24, 144, 255, 0.1);
}

.vm-list-item-name {
  margin-left: 8px;
  font-weight: 500;
}

.vm-details-card {
  width: 100%;
}

.vm-status-title {
  display: flex;
  align-items: center;
}

.vm-status-overview,
.vm-status-injections,
.vm-status-logs {
  margin-top: 16px;
}

.vm-status-card {
  margin-bottom: 16px;
}

.resource-usage-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.resource-usage-item {
  flex: 1 1;
  min-width: 200px;
}

.log-entry {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
}

.log-error {
  background-color: rgba(255, 77, 79, 0.1);
}

.log-warning {
  background-color: rgba(250, 173, 20, 0.1);
}

.log-info {
  background-color: rgba(24, 144, 255, 0.1);
}

/* Responsive styles */
@media (max-width: 768px) {
  .vm-status-content {
    flex-direction: column;
  }
  
  .vm-status-sidebar {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .resource-usage-item {
    min-width: 100%;
  }
} 
/*# sourceMappingURL=data:application/json;base64,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 */</style><style>.docs-page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.docs-page-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.docs-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.docs-frame-container {
  width: 100%;
  height: calc(100vh - 200px);
  min-height: 600px;
  border-radius: 4px;
  overflow: hidden;
}

.docs-frame {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .docs-page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .docs-page-header > .ant-space:last-child {
    margin-top: 16px;
  }
  
  .docs-frame-container {
    height: calc(100vh - 250px);
  }
} 
/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9wYWdlcy9Eb2NzUGFnZS9zdHlsZXMuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBYTtFQUNiLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UseUNBQXlDO0VBQ3pDLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGFBQWE7RUFDYiw4QkFBOEI7RUFDOUIsbUJBQW1CO0VBQ25CLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCwyQkFBMkI7RUFDM0IsaUJBQWlCO0VBQ2pCLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLFlBQVk7RUFDWixrQkFBa0I7QUFDcEI7O0FBRUEsMkJBQTJCO0FBQzNCO0VBQ0U7SUFDRSxzQkFBc0I7SUFDdEIsdUJBQXVCO0VBQ3pCOztFQUVBO0lBQ0UsZ0JBQWdCO0VBQ2xCOztFQUVBO0lBQ0UsMkJBQTJCO0VBQzdCO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuZG9jcy1wYWdlLWNvbnRhaW5lciB7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIG1heC13aWR0aDogMTIwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbn1cblxuLmRvY3MtcGFnZS1jYXJkIHtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbn1cblxuLmRvY3MtcGFnZS1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XG59XG5cbi5kb2NzLWZyYW1lLWNvbnRhaW5lciB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAyMDBweCk7XG4gIG1pbi1oZWlnaHQ6IDYwMHB4O1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi5kb2NzLWZyYW1lIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgYm9yZGVyOiBub25lO1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG59XG5cbi8qIFJlc3BvbnNpdmUgYWRqdXN0bWVudHMgKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuZG9jcy1wYWdlLWhlYWRlciB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgfVxuICBcbiAgLmRvY3MtcGFnZS1oZWFkZXIgPiAuYW50LXNwYWNlOmxhc3QtY2hpbGQge1xuICAgIG1hcmdpbi10b3A6IDE2cHg7XG4gIH1cbiAgXG4gIC5kb2NzLWZyYW1lLWNvbnRhaW5lciB7XG4gICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMjUwcHgpO1xuICB9XG59ICJdLCJzb3VyY2VSb290IjoiIn0= */</style><style>.status-page-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
}

.status-page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.status-overview-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.status-badge-container {
  display: flex;
  align-items: center;
  height: 100%;
}

.component-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transition: all 0.3s;
}

.component-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.status-cards-container {
  margin-bottom: 24px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .status-overview-card,
  .component-card {
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.05);
  }
  
  .component-card:hover {
    box-shadow: 0 4px 16px rgba(255, 255, 255, 0.08);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .status-page-container {
    padding: 16px;
  }
  
  .status-page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .status-page-header button {
    align-self: flex-start;
  }
} 
/*# sourceMappingURL=data:application/json;base64,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 */</style><style>.test-run-viewer {
  margin-bottom: 20px;
}

.test-run-viewer .summary-card {
  margin-bottom: 16px;
  background-color: #fafafa;
}

.test-run-viewer .dark-mode .summary-card {
  background-color: #1f1f1f;
}

.test-run-viewer .coverage-summary {
  margin-bottom: 16px;
}

.test-run-viewer ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.test-run-viewer .ant-table-small {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.test-run-viewer .dark-mode .ant-table-small {
  border-color: #303030;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .test-run-viewer .ant-card-head-title {
    font-size: 16px;
  }
  
  .test-run-viewer .ant-tabs-nav-list {
    flex-wrap: wrap;
  }
} 
/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9jb21wb25lbnRzL1Rlc3RSdW5WaWV3ZXIvc3R5bGVzLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSx5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQSwyQkFBMkI7QUFDM0I7RUFDRTtJQUNFLGVBQWU7RUFDakI7O0VBRUE7SUFDRSxlQUFlO0VBQ2pCO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIudGVzdC1ydW4tdmlld2VyIHtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbn1cblxuLnRlc3QtcnVuLXZpZXdlciAuc3VtbWFyeS1jYXJkIHtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTtcbn1cblxuLnRlc3QtcnVuLXZpZXdlciAuZGFyay1tb2RlIC5zdW1tYXJ5LWNhcmQge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWYxZjFmO1xufVxuXG4udGVzdC1ydW4tdmlld2VyIC5jb3ZlcmFnZS1zdW1tYXJ5IHtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cblxuLnRlc3QtcnVuLXZpZXdlciB1bCB7XG4gIHBhZGRpbmctbGVmdDogMjBweDtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cblxuLnRlc3QtcnVuLXZpZXdlciAuYW50LXRhYmxlLXNtYWxsIHtcbiAgYm9yZGVyOiAxcHggc29saWQgI2YwZjBmMDtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xufVxuXG4udGVzdC1ydW4tdmlld2VyIC5kYXJrLW1vZGUgLmFudC10YWJsZS1zbWFsbCB7XG4gIGJvcmRlci1jb2xvcjogIzMwMzAzMDtcbn1cblxuLyogUmVzcG9uc2l2ZSBhZGp1c3RtZW50cyAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC50ZXN0LXJ1bi12aWV3ZXIgLmFudC1jYXJkLWhlYWQtdGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgfVxuICBcbiAgLnRlc3QtcnVuLXZpZXdlciAuYW50LXRhYnMtbmF2LWxpc3Qge1xuICAgIGZsZXgtd3JhcDogd3JhcDtcbiAgfVxufSAiXSwic291cmNlUm9vdCI6IiJ9 */</style><style>/* Global styles */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

/* Ensure full height for app container */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Common utility classes */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 8px;
}

.mb-2 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 24px;
}

.mt-1 {
  margin-top: 8px;
}

.mt-2 {
  margin-top: 16px;
}

.mt-3 {
  margin-top: 24px;
}

.p-1 {
  padding: 8px;
}

.p-2 {
  padding: 16px;
}

.p-3 {
  padding: 24px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }
}

@media (min-width: 769px) {
  .show-on-mobile {
    display: none;
  }
} 
/*# sourceMappingURL=data:application/json;base64,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 */</style></head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"><div class="ant-layout css-dev-only-do-not-override-tjsggz"><header class="ant-layout-header css-dev-only-do-not-override-tjsggz" style="display: flex; justify-content: flex-end; padding: 0px 16px;"><div class="ant-select ant-select-outlined css-dev-only-do-not-override-tjsggz ant-select-single ant-select-show-arrow" style="width: 150px;"><div class="ant-select-selector"><span class="ant-select-selection-wrap"><span class="ant-select-selection-search"><input type="search" autocomplete="off" class="ant-select-selection-search-input" role="combobox" aria-expanded="false" aria-haspopup="listbox" aria-owns="rc_select_1_list" aria-autocomplete="list" aria-controls="rc_select_1_list" readonly="" unselectable="on" value="" id="rc_select_1" style="opacity: 0;"></span><span class="ant-select-selection-item" title="en-US">en-US</span></span></div><span class="ant-select-arrow" unselectable="on" aria-hidden="true" style="user-select: none;"><span role="img" aria-label="down" class="anticon anticon-down ant-select-suffix"><svg viewBox="64 64 896 896" focusable="false" data-icon="down" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"></path></svg></span></span></div></header><div class="loading-container"><div class="ant-spin ant-spin-lg ant-spin-spinning css-dev-only-do-not-override-tjsggz" aria-live="polite" aria-busy="true"><span class="ant-spin-dot-holder"><span class="ant-spin-dot ant-spin-dot-spin"><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i></span></span></div><p>Loading file upload data...</p></div></div></div>
  
 </body></html>