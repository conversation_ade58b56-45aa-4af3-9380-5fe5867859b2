{"thresholds": {"confidence": 0.7, "clarity": 0.6, "coverage": 0.8, "max_ambiguous_high": 0, "max_missing_high": 0}, "batch_processing": {"default_workers": 4, "prd_pattern": "*.md", "tasks_pattern": "*.json", "auto_pair_files": true}, "ci_integration": {"fail_on_threshold": true, "include_improvement_suggestions": true, "max_issues_to_report": 5}, "logging": {"log_file": "confidence_scoring.log", "log_level": "INFO", "include_timestamp": true}, "scoring_weights": {"clarity": 0.3, "coverage": 0.5, "complexity": 0.2}, "review_system": {"max_clarifications": 10, "max_improvements": 10, "priority_order": ["high", "medium", "low"]}}