# 🔥 Focus Forge 🛠️

> **Turn chaos into clarity** - Your productivity companion in the terminal

<p align="center">
  <img src="https://img.shields.io/badge/Python-3.10+-blue.svg" alt="Python 3.10+"/>
  <img src="https://img.shields.io/badge/License-MIT-green.svg" alt="MIT License"/>
  <img src="https://img.shields.io/badge/Status-Active-brightgreen.svg" alt="Status Active"/>
</p>

## 🌟 What is Focus Forge?

Focus Forge is a powerful, single-file Python CLI task management system designed for developers who want to **stay in the flow** without leaving the terminal. It helps you organize tasks, track dependencies, and leverage AI to break down complex work.

## ⚡ Key Features

- 📋 **Task Management** - Create, organize, and track your tasks 
- 🔄 **Dependencies** - Define task relationships and validate them
- 📊 **Dependency Graphs** - Visualize and analyze task dependencies with topological sorting
- 📈 **Sequence Analysis** - Determine the optimal sequence for implementing tasks with critical path detection
- 🚀 **Constraint-Based Ordering** - Apply various constraints to optimize task sequencing
- 🛣️ **Critical Path Analysis** - Identify the critical path for efficient project planning
- 🧠 **AI-Powered** - Auto-generate tasks from PRDs and expand complex tasks
- 🖥️ **Terminal UI** - Rich colors and formatting in your terminal
- 🧩 **Task Generation** - Generate individual task files from your task list
- 🏗️ **Nix Integration** - Seamless setup with nix-shell configuration

## 🔄 Workflow

```mermaid
graph TD
    A[Initialize Project] -->|Creates config & task files| B[Parse PRD]
    B -->|Generates initial tasks| C[List Tasks]
    C --> D{Choose Next Action}
    D -->|View details| E[Show Task]
    D -->|Get recommendation| F[Next Task]
    D -->|Change status| G[Update Status]
    D -->|Break down task| H[Expand Task]
    D -->|Manage connections| I[Manage Dependencies]
    D -->|Update based on changes| J[Update Tasks]
    D -->|Generate files| K[Generate Task Files]
    D -->|Sequence Analysis| L[Optimize Implementation Order]
    
    %% New PRD Integration nodes
    B -->|Advanced parsing| M[Parse PRD Advanced]
    D -->|Integrate PRD changes| N[Integrate PRD]
    D -->|Validate against PRD| O[Validate PRD Tasks]
    
    M --> C
    N --> C
    O --> D
    
    G --> C
    H --> C
    I --> C
    J --> C
    L --> C
    E --> D
    F --> D
    K --> D
```

## 🚀 Installation

### 🐳 Using DevContainer (Recommended for Development)

```bash
# 1. Install Docker and VS Code with Remote-Containers extension
# 2. Open the project in VS Code
# 3. When prompted, click "Reopen in Container"
# 4. 🎉 You're ready to go!
```

### 🧙‍♂️ Using nix-shell

```bash
# Generate nix-shell configuration
./focus_forge.py nix-config

# Enter nix-shell environment
nix-shell

# Use the tool with magic ✨
focus-forge --help
```

### 🛠️ Manual Installation

```bash
# Install dependencies
pip install click rich requests

# Make the script executable
chmod +x focus_forge.py

# Run the tool
./focus_forge.py --help
```

### Using nix-shell (recommended)

```bash
# Clone the repository
git clone https://github.com/yourusername/focus_forge.git
cd focus_forge

# Start nix-shell
nix-shell

# Now you can use the 'focus-forge' command
focus-forge --help
```

### Direct Usage

```bash
# Clone the repository
git clone https://github.com/yourusername/focus_forge.git
cd focus_forge

# Install dependencies
pip install -r requirements.txt

# Run directly
./focus_forge.py --help
```

### Using as a Git Submodule

For projects that want to use Focus Forge while keeping it separate from their main codebase, we provide a guide for integrating it as a Git submodule:

[Integration Guide: Using Focus Forge as a Submodule](docs/integration_guide.md)

This approach allows you to:
- Keep Focus Forge updated independently
- Store task data in your main project directory
- Easily integrate with your existing workflows

## 📋 Usage Guide

### 🌱 Initialize a Project

```bash
./focus_forge.py init
```

### 📝 Parse a PRD and Generate Tasks

```bash
./focus_forge.py parse-prd --input=prd.txt --tasks=10
```

### 📊 View Your Tasks

```bash
# List all tasks
./focus_forge.py list

# Show specific task details
./focus_forge.py show <task_id>

# Show next recommended task
./focus_forge.py next
```

### 🔄 Update Task Status

```bash
./focus_forge.py set-status --id=<task_id> --status=<status>
# Status options: 🟡 pending, 🟢 in-progress, ✅ done, ⏸️ deferred
```

### 🧩 Break Down a Task with AI

```bash
./focus_forge.py expand --id=<task_id> --num=<num_subtasks>
```

### 🔗 Dependency Management

```bash
# Add dependency
./focus_forge.py add-dependency --id=<task_id> --depends-on=<dependency_id>

# Remove dependency
./focus_forge.py remove-dependency --id=<task_id> --depends-on=<dependency_id>

# Validate dependencies
./focus_forge.py validate-dependencies

# Fix dependency issues
./focus_forge.py fix-dependencies

# Visualize dependency graph (requires Graphviz)
./focus_forge.py dependency-graph --output=graph.dot

# Generate PNG from DOT file
dot -Tpng -o graph.png graph.dot
```

### 🔄 Update Tasks with New Context

```bash
./focus_forge.py update --from=<task_id> --prompt="New context or changes"
```

### 📄 Generate Task Files

```bash
./focus_forge.py generate
```

### 📊 Sequence Analysis and Optimization (T04)

```bash
# Generate optimal implementation sequence
./focus_forge.py sequence

# Export sequence to a file
./focus_forge.py sequence --output=sequence.json

# Identify tasks that can be implemented in parallel
./focus_forge.py parallel-tasks

# Show the critical path for the project
./focus_forge.py critical-path

# Apply custom constraints to task ordering
./focus_forge.py sequence --constraints=technical,foundation,team

# Detect task bottlenecks
./focus_forge.py bottlenecks
```

## ⚙️ Configuration

Focus Forge can be configured through environment variables or a `.focus_forge_config` file:

| Variable | Description | Default |
|----------|-------------|---------|
| `ANTHROPIC_API_KEY` | Your Anthropic API key for Claude | - |
| `MODEL` | AI model to use | "claude-3-7-sonnet-20250219" |
| `MAX_TOKENS` | Maximum tokens for responses | 4000 |
| `TEMPERATURE` | Temperature for responses | 0.7 |
| `PROJECT_NAME` | Default project name | - |
| `PROJECT_VERSION` | Default project version | - |
| `DEFAULT_SUBTASKS` | Default number of subtasks | 3 |
| `DEFAULT_PRIORITY` | Default priority | medium |

## 🧪 Development

### 🧬 Testing

```bash
# Install dev dependencies
pip install -e ".[dev]"

# Run all tests
pytest

# Run sequence analysis tests (T04)
nix-shell default.nix --run "python3 tests/test_sequence_analysis_extended.py && python3 tests/test_sequence_constraints.py && python3 tests/test_critical_path.py"
```

### 🧹 Code Quality

```bash
# Install pre-commit
pip install pre-commit

# Set up pre-commit hooks
pre-commit install
```

## 📈 T04: Sequence Analysis

The Sequence Analysis module (T04) is a powerful feature that helps you determine the optimal order for implementing tasks. It considers:

- **Dependency Relationships**: Ensures dependent tasks are completed in the correct order
- **Priority Levels**: Prioritizes high-priority tasks when dependencies allow
- **Technical Constraints**: Handles technical prerequisites between tasks
- **Team Assignments**: Optimizes for team workflows with minimal context switching
- **Foundation First**: Identifies and prioritizes foundation systems that should be built early
- **Critical Path Analysis**: Identifies the sequence of tasks that determines the minimum project timeline
- **Parallel Development**: Detects tasks that can be worked on concurrently

The implementation includes comprehensive testing with:

- Basic sequence verification
- Constraint handling and prioritization
- Weighted path analysis
- Edge case handling (cycles, disconnected components)

## 📜 License

MIT

## 💡 Tips & Tricks

- 💻 Use `focus-forge next` to always know what to work on next
- 🔄 Run `validate-dependencies` periodically to catch circular dependencies
- 📊 Visualize your task dependencies to better understand your project structure
- 📈 Use the critical path analysis to identify project bottlenecks
- 🧩 Apply custom constraints to optimize task ordering for your specific needs
- 🌙 Dark mode terminal works best with the rich text formatting
- ⚡ Define keyboard shortcuts for common commands to boost productivity

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🙏 Acknowledgements

- The [Rich](https://github.com/Textualize/rich) library for beautiful terminal output
- [Click](https://click.palletsprojects.com/) for making CLI development a breeze
- [Anthropic Claude](https://www.anthropic.com/claude) for powering the AI features

## Task Generation and Validation

Focus Forge includes a powerful task generation and validation system that can:

1. **Generate tasks from PRD text**: Convert Product Requirements Documents into structured tasks with proper dependencies and priorities.
   ```bash
   ./focus_forge.py parse-prd --input=prd.txt
   ```

2. **Validate tasks for correctness**: Check that tasks meet schema requirements, have valid dependencies, and properly cover the PRD requirements.
   ```bash
   python test_task_validation.py --file tasks.json --prd prd.txt
   ```

3. **Generate custom task templates**: Define your own task templates in `focus_forge/templates/task_templates.json`.

### Task Generation Features

- Automatic component detection from PRD text
- Dependency detection between components
- Priority inference based on textual indicators
- Generation of well-structured tasks with subtasks
- Template-based generation for consistent task format

### Task Validation Features

- Schema validation of tasks and subtasks
- Dependency consistency checking
- Circular dependency detection
- PRD coverage analysis
- Quality scoring and recommendations

### Usage Examples

Generate tasks from a PRD:
```bash
python test_task_generation_prd.py
```

Validate generated tasks:
```bash
python test_task_validation.py --file generated_tasks.json
```

Run all validation tests:
```bash
python test_task_validation.py --all
```

## PRD Integration System

Focus Forge includes advanced PRD integration features that allow you to:

1. **Parse PRDs with advanced features**: Use enhanced PRD parsing capabilities to generate high-quality tasks.
   ```bash
   ./focus_forge.py parse-prd-advanced --input=prd.md --advanced
   ```

2. **Integrate PRD with existing tasks**: Merge newly generated tasks with your existing task management system.
   ```bash
   ./focus_forge.py integrate-prd --input=prd.md
   ```

3. **Validate tasks against PRD requirements**: Ensure your tasks properly cover and align with PRD requirements.
   ```bash
   ./focus_forge.py validate-prd-tasks --prd=prd.md
   ```

### Integration Features

- Seamless merging of new tasks with existing ones
- Preservation of task completion status during updates
- Intelligent task ID conflict resolution
- Confidence scoring for task-PRD alignment
- Detailed validation reports for quality assurance
- Comprehensive error handling

### Advanced Usage

Parse PRD with custom configuration:
```bash
./focus_forge.py parse-prd-advanced --input=prd.md --advanced --config-file=config.json
```

Get detailed integration report:
```bash
./focus_forge.py integrate-prd --input=prd.md --verbose
```

Save validation report to file:
```bash
./focus_forge.py validate-prd-tasks --prd=prd.md --output=validation_report.json
```

Fail CI/CD pipeline on validation issues:
```bash
./focus_forge.py validate-prd-tasks --prd=prd.md --fail
```

## Task Format Upgrader System

The upgrader system ensures backward compatibility as Focus Forge evolves, allowing projects to seamlessly upgrade to newer versions:

- **Version Detection**: Automatically identifies task data structure versions
- **Schema Validation**: Validates task data against version-specific schemas
- **Migration Pipeline**: Step-by-step upgrades between consecutive versions
- **Backup Management**: Creates and manages backups before upgrading
- **Upgrade History**: Records all version changes for auditing
- **Configurable Behavior**: Options to prompt, auto-upgrade, or never upgrade

See the [Task Upgrader Documentation](focus_forge/README_task_upgrader.md) for detailed usage.
