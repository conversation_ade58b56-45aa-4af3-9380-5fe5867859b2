{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    python310
    python310Packages.click
    python310Packages.rich
    python310Packages.requests
    python310Packages.pytest
    python310Packages.pytest-cov
    python310Packages.mypy
    pre-commit
  ];
  
  shellHook = ''
    alias focus-forge="python3 ${./focus_forge.py}"
    alias run-tests="python3 -m pytest -v tests/"
    alias run-task-validation-tests="python3 ${./test_task_validation.py}"
    alias run-confidence-scoring-tests="python3 ${./test_confidence_scoring.py}"
    alias confidence-scorer="python3 ${./confidence_scoring_cli.py}"
    
    # Function to close a task and show the next one
    close-task() {
      if [ -z "$1" ]; then
        echo "Error: Task ID is required"
        echo "Usage: close-task <task_id>"
        return 1
      fi
      
      echo "Marking task $1 as done..."
      focus-forge set-status --id="$1" --status=done && echo "Task $1 completed!" && echo "" && echo "Finding next task..." && focus-forge next
    }
    
    echo ""
    echo "Focus Forge nix-shell environment activated!"
    echo "Commands:"
    echo "  focus-forge               Run the Focus Forge CLI tool"
    echo "  run-tests                 Run test suite"
    echo "  run-task-validation-tests Run task validation tests"
    echo "  run-confidence-scoring-tests Run confidence scoring tests"
    echo "  confidence-scorer         Run confidence scoring CLI"
    echo "  close-task <id>           Close the task and show the next one"
    echo "  pytest -v tests/          Run tests directly with pytest"
    echo "  pytest --cov=focus_forge tests/  Run tests with coverage"
    echo ""
  '';
} 