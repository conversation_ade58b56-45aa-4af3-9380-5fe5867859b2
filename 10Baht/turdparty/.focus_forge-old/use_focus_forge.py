#!/usr/bin/env python3
"""
Helper script to run focus_forge.py with the API key from .env
"""
import os
import sys
import subprocess
from pathlib import Path

# Load API key from .env file
env_file = Path(__file__).parent / '.env'
if env_file.exists():
    with open(env_file, 'r') as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value
                print(f"Loaded {key} from .env file")

# Ensure the API key is set
if 'ANTHROPIC_API_KEY' not in os.environ:
    print("Error: ANTHROPIC_API_KEY not found in .env file or environment variables")
    sys.exit(1)

# Construct the command to run focus_forge.py
focus_forge_path = Path(__file__).parent / 'focus_forge.py'
cmd = [
    'python3', 
    str(focus_forge_path),
    'parse-prd',
    '--input=../prd.md'
]

print("Running focus_forge.py with API key from .env file...")
subprocess.run(cmd) 