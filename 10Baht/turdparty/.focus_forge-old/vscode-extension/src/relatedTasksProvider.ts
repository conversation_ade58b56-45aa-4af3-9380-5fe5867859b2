import * as vscode from 'vscode';
import { Task, TaskProvider } from './taskProvider';
import { TaskTreeItem } from './taskTreeItem';

export class RelatedTasksProvider implements vscode.TreeDataProvider<TaskTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<TaskTreeItem | undefined | null | void> = 
        new vscode.EventEmitter<TaskTreeItem | undefined | null | void>();
    
    readonly onDidChangeTreeData: vscode.Event<TaskTreeItem | undefined | null | void> = 
        this._onDidChangeTreeData.event;

    constructor(private taskProvider: TaskProvider) {
        this.taskProvider.onDidChangeTaskData(() => this.refresh());
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: TaskTreeItem): vscode.TreeItem {
        return element;
    }

    async getChildren(element?: TaskTreeItem): Promise<TaskTreeItem[]> {
        if (element) {
            return []; // No child items for task items
        }

        const currentFileTasks = await this.getCurrentFileTasks();
        if (currentFileTasks.length === 0) {
            return [];
        }

        const allTasks = await this.taskProvider.getAllTasks();
        const relatedTaskIds = new Set<string>();
        const currentTaskIds = new Set(currentFileTasks.map(task => task.id));

        // Find tasks that are dependencies of the current file tasks
        // Or tasks that depend on the current file tasks
        for (const task of allTasks) {
            // If this task depends on any of the current file tasks
            const isDependentOnCurrentTask = task.dependencies.some(depId => 
                currentTaskIds.has(depId)
            );
            
            if (isDependentOnCurrentTask && !currentTaskIds.has(task.id)) {
                relatedTaskIds.add(task.id);
                continue;
            }
            
            // If any current file task depends on this task
            const isRequiredByCurrentTask = currentFileTasks.some(currentTask => 
                currentTask.dependencies.includes(task.id)
            );
            
            if (isRequiredByCurrentTask && !currentTaskIds.has(task.id)) {
                relatedTaskIds.add(task.id);
            }
        }

        // Convert IDs to tasks
        const relatedTasks = allTasks.filter(task => relatedTaskIds.has(task.id));

        // Sort tasks by status priority
        relatedTasks.sort((a, b) => {
            const statusOrder = {
                'in-progress': 0,
                'pending': 1,
                'deferred': 2,
                'done': 3
            };
            
            return statusOrder[a.status] - statusOrder[b.status];
        });

        // Apply showCompleted filter
        const showCompleted = vscode.workspace.getConfiguration('focus-forge').get<boolean>('showCompletedTasks') || false;
        const displayTasks = showCompleted 
            ? relatedTasks
            : relatedTasks.filter(task => task.status !== 'done');

        return displayTasks.map(task => {
            // For related tasks, the relevance score is based on dependency relationship
            // We arbitrarily assign 0.5 as it's not directly file-based relevance
            return new TaskTreeItem(
                task,
                0.5,
                vscode.TreeItemCollapsibleState.None
            );
        });
    }

    private async getCurrentFileTasks(): Promise<Task[]> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return [];
        }

        const currentFilePath = editor.document.uri.fsPath;
        const tasks = await this.taskProvider.getAllTasks();
        const relevanceThreshold = this.getRelevanceThreshold();
        const currentFileTasks: Task[] = [];

        for (const task of tasks) {
            const relevance = await this.taskProvider.calculateTaskRelevance(task, currentFilePath);
            if (relevance >= relevanceThreshold) {
                currentFileTasks.push(task);
            }
        }

        return currentFileTasks;
    }
    
    private getRelevanceThreshold(): number {
        const config = vscode.workspace.getConfiguration('focus-forge');
        return config.get<number>('relevanceThreshold') || 0.3;
    }
} 