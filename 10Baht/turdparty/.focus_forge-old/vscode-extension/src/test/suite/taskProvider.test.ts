import * as assert from 'assert';
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import { TaskProvider, Task } from '../../taskProvider';

suite('TaskProvider Test Suite', () => {
    let context: vscode.ExtensionContext;
    let tempDir: string;
    let tasksFilePath: string;
    
    setup(async () => {
        // Create a temporary directory for test tasks.json
        tempDir = path.join(os.tmpdir(), 'focus-forge-tests-' + Math.random().toString(36).substring(2, 15));
        fs.mkdirSync(tempDir, { recursive: true });
        
        // Create a mock tasks.json file
        tasksFilePath = path.join(tempDir, 'tasks.json');
        const mockTasks: Task[] = [
            {
                id: 'T-123456',
                title: 'Test Task 1',
                description: 'A test task',
                status: 'pending',
                priority: 'medium',
                dependencies: [],
                codeReferences: [
                    {
                        filePath: '/path/to/file1.ts',
                        selection: {
                            startLine: 10,
                            endLine: 20
                        }
                    }
                ]
            }
        ];
        
        fs.writeFileSync(tasksFilePath, JSON.stringify(mockTasks, null, 2));
        
        // Create a partial mock for the extension context
        context = {
            subscriptions: [],
            extensionPath: '',
            asAbsolutePath: (relativePath: string) => relativePath,
        } as any;
        
        // Override workspace folders for tests
        const originalWorkspaceFolder = vscode.workspace.workspaceFolders;
        (vscode.workspace as any).workspaceFolders = [
            {
                uri: vscode.Uri.file(tempDir),
                name: 'test',
                index: 0
            }
        ];
        
        // Override configuration for tests
        const originalGetConfiguration = vscode.workspace.getConfiguration;
        (vscode.workspace as any).getConfiguration = (section?: string) => {
            return {
                get: <T>(key: string) => {
                    if (key === 'tasksFilePath') {
                        return 'tasks.json';
                    }
                    return undefined;
                }
            };
        };
        
        // Cleanup function to restore original workspace folders
        teardown(() => {
            (vscode.workspace as any).workspaceFolders = originalWorkspaceFolder;
            (vscode.workspace as any).getConfiguration = originalGetConfiguration;
            try {
                fs.rmSync(tempDir, { recursive: true, force: true });
            } catch (err) {
                console.error('Error cleaning up test directory:', err);
            }
        });
    });
    
    test('TaskProvider loads tasks from file', async () => {
        const taskProvider = new TaskProvider(context);
        await new Promise(resolve => setTimeout(resolve, 100)); // Wait for tasks to load
        
        const tasks = await taskProvider.getAllTasks();
        assert.strictEqual(tasks.length, 1);
        assert.strictEqual(tasks[0].id, 'T-123456');
        assert.strictEqual(tasks[0].title, 'Test Task 1');
    });
    
    test('TaskProvider creates a new task', async () => {
        const taskProvider = new TaskProvider(context);
        await new Promise(resolve => setTimeout(resolve, 100)); // Wait for tasks to load
        
        const newTask = await taskProvider.createTask('New Test Task', '/path/to/newfile.ts', {
            startLine: 5,
            endLine: 10
        });
        
        assert.strictEqual(newTask.title, 'New Test Task');
        assert.strictEqual(newTask.status, 'pending');
        assert.strictEqual(newTask.priority, 'medium');
        assert.deepStrictEqual(newTask.dependencies, []);
        assert.strictEqual(newTask.codeReferences.length, 1);
        assert.strictEqual(newTask.codeReferences[0].filePath, '/path/to/newfile.ts');
        
        // Verify task was added to the list
        const tasks = await taskProvider.getAllTasks();
        assert.strictEqual(tasks.length, 2);
    });
    
    test('TaskProvider updates task status', async () => {
        const taskProvider = new TaskProvider(context);
        await new Promise(resolve => setTimeout(resolve, 100)); // Wait for tasks to load
        
        await taskProvider.updateTaskStatus('T-123456', 'in-progress');
        
        const tasks = await taskProvider.getAllTasks();
        assert.strictEqual(tasks.length, 1);
        assert.strictEqual(tasks[0].status, 'in-progress');
    });
    
    test('TaskProvider calculates task relevance correctly', async () => {
        const taskProvider = new TaskProvider(context);
        await new Promise(resolve => setTimeout(resolve, 100)); // Wait for tasks to load
        
        const tasks = await taskProvider.getAllTasks();
        const task = tasks[0];
        
        // Direct match
        const directRelevance = await taskProvider.calculateTaskRelevance(task, '/path/to/file1.ts');
        assert.strictEqual(directRelevance, 1.0);
        
        // Same directory match
        const dirRelevance = await taskProvider.calculateTaskRelevance(task, '/path/to/otherfile.ts');
        assert.strictEqual(dirRelevance, 0.7);
        
        // Similar filename match
        const similarRelevance = await taskProvider.calculateTaskRelevance(task, '/different/path/file1.ext');
        assert.strictEqual(similarRelevance, 0.5);
        
        // Low relevance for unrelated file
        const lowRelevance = await taskProvider.calculateTaskRelevance(task, '/unrelated/path/unrelated.ts');
        assert.strictEqual(lowRelevance, 0.1);
    });
}); 