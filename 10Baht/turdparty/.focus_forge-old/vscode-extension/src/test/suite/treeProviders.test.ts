import * as assert from 'assert';
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import { TaskProvider, Task } from '../../taskProvider';
import { CurrentFileTasksProvider } from '../../currentFileTasksProvider';
import { ModuleTasksProvider } from '../../moduleTasksProvider';
import { RelatedTasksProvider } from '../../relatedTasksProvider';

suite('Tree Providers Test Suite', () => {
    let context: vscode.ExtensionContext;
    let tempDir: string;
    let tasksFilePath: string;
    let taskProvider: TaskProvider;
    
    setup(async () => {
        // Create a temporary directory for test tasks.json
        tempDir = path.join(os.tmpdir(), 'focus-forge-tests-' + Math.random().toString(36).substring(2, 15));
        fs.mkdirSync(tempDir, { recursive: true });
        
        // Create test files for different scenarios
        const testFilePath = path.join(tempDir, 'testFile.ts');
        fs.writeFileSync(testFilePath, 'console.log("test");');
        
        const moduleFilePath = path.join(tempDir, 'moduleFile.ts');
        fs.writeFileSync(moduleFilePath, 'console.log("module test");');
        
        const subDirPath = path.join(tempDir, 'subdir');
        fs.mkdirSync(subDirPath, { recursive: true });
        const subDirFilePath = path.join(subDirPath, 'subFile.ts');
        fs.writeFileSync(subDirFilePath, 'console.log("subdir test");');
        
        // Create a mock tasks.json file with various types of tasks
        tasksFilePath = path.join(tempDir, 'tasks.json');
        const mockTasks: Task[] = [
            {
                id: 'T-123456',
                title: 'Current File Task',
                description: 'A task for the current file',
                status: 'pending',
                priority: 'medium',
                dependencies: ['T-345678'],
                codeReferences: [
                    {
                        filePath: testFilePath,
                        selection: {
                            startLine: 0,
                            endLine: 1
                        }
                    }
                ]
            },
            {
                id: 'T-234567',
                title: 'Module Task',
                description: 'A task for a file in the same directory',
                status: 'in-progress',
                priority: 'high',
                dependencies: [],
                codeReferences: [
                    {
                        filePath: moduleFilePath,
                        position: {
                            line: 0,
                            character: 5
                        }
                    }
                ]
            },
            {
                id: 'T-345678',
                title: 'Dependency Task',
                description: 'A task that is a dependency for other tasks',
                status: 'done',
                priority: 'low',
                dependencies: [],
                codeReferences: [
                    {
                        filePath: subDirFilePath,
                        selection: {
                            startLine: 0,
                            endLine: 1
                        }
                    }
                ]
            },
            {
                id: 'T-456789',
                title: 'Dependent Task',
                description: 'A task that depends on the current file task',
                status: 'pending',
                priority: 'medium',
                dependencies: ['T-123456'],
                codeReferences: [
                    {
                        filePath: '/unrelated/path/file.ts',
                        selection: {
                            startLine: 10,
                            endLine: 20
                        }
                    }
                ]
            }
        ];
        
        fs.writeFileSync(tasksFilePath, JSON.stringify(mockTasks, null, 2));
        
        // Create a partial mock for the extension context
        context = {
            subscriptions: [],
            extensionPath: '',
            asAbsolutePath: (relativePath: string) => relativePath,
        } as any;
        
        // Override workspace folders for tests
        const originalWorkspaceFolder = vscode.workspace.workspaceFolders;
        (vscode.workspace as any).workspaceFolders = [
            {
                uri: vscode.Uri.file(tempDir),
                name: 'test',
                index: 0
            }
        ];
        
        // Override configuration for tests
        const originalGetConfiguration = vscode.workspace.getConfiguration;
        (vscode.workspace as any).getConfiguration = (section?: string) => {
            return {
                get: <T>(key: string) => {
                    if (key === 'tasksFilePath') {
                        return 'tasks.json';
                    } else if (key === 'relevanceThreshold') {
                        return 0.3;
                    } else if (key === 'showCompletedTasks') {
                        return true; // Show all tasks including completed ones for tests
                    }
                    return undefined;
                }
            };
        };
        
        // Mock the active text editor
        const originalActiveTextEditor = vscode.window.activeTextEditor;
        (vscode.window as any).activeTextEditor = {
            document: {
                uri: {
                    fsPath: testFilePath
                },
                getText: () => 'console.log("test");'
            },
            selection: new vscode.Selection(0, 0, 0, 0)
        };
        
        // Initialize task provider
        taskProvider = new TaskProvider(context);
        await new Promise(resolve => setTimeout(resolve, 100)); // Wait for tasks to load
        
        // Cleanup function
        teardown(() => {
            (vscode.workspace as any).workspaceFolders = originalWorkspaceFolder;
            (vscode.workspace as any).getConfiguration = originalGetConfiguration;
            (vscode.window as any).activeTextEditor = originalActiveTextEditor;
            try {
                fs.rmSync(tempDir, { recursive: true, force: true });
            } catch (err) {
                console.error('Error cleaning up test directory:', err);
            }
        });
    });
    
    test('CurrentFileTasksProvider returns tasks for current file', async () => {
        const currentFileTasksProvider = new CurrentFileTasksProvider(taskProvider);
        const items = await currentFileTasksProvider.getChildren();
        
        assert.strictEqual(items.length, 1);
        assert.strictEqual(items[0].task.id, 'T-123456');
        assert.strictEqual(items[0].task.title, 'Current File Task');
    });
    
    test('ModuleTasksProvider returns tasks in the same module', async () => {
        const moduleTasksProvider = new ModuleTasksProvider(taskProvider);
        const items = await moduleTasksProvider.getChildren();
        
        // Should include the module task but not the current file task (which is shown in current file view)
        assert.strictEqual(items.length, 1);
        assert.strictEqual(items[0].task.id, 'T-234567');
        assert.strictEqual(items[0].task.title, 'Module Task');
    });
    
    test('RelatedTasksProvider returns related tasks', async () => {
        const relatedTasksProvider = new RelatedTasksProvider(taskProvider);
        const items = await relatedTasksProvider.getChildren();
        
        // Should find tasks related by dependencies (both ways)
        assert.strictEqual(items.length, 2);
        
        // Check if it contains both the dependency and dependent tasks
        const taskIds = items.map(item => item.task.id);
        assert.ok(taskIds.includes('T-345678')); // Dependency of the current file task
        assert.ok(taskIds.includes('T-456789')); // Depends on the current file task
    });
}); 