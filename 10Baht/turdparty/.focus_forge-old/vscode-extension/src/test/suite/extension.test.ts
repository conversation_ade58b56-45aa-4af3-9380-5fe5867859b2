import * as assert from 'assert';
import * as vscode from 'vscode';

suite('Extension Test Suite', () => {
    test('Extension should be present', () => {
        const extension = vscode.extensions.getExtension('focus-forge.focus-forge');
        assert.notStrictEqual(extension, undefined);
    });
    
    test('Extension commands should be registered', async () => {
        const commands = await vscode.commands.getCommands();
        
        // Check for our extension commands
        const extensionCommands = [
            'focus-forge.refreshTasks',
            'focus-forge.createTask',
            'focus-forge.linkTaskToCode',
            'focus-forge.setTaskStatus'
        ];
        
        extensionCommands.forEach(command => {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        });
    });
    
    test('Extension views should be registered', () => {
        // Check for the sidebar view container
        const viewContainers = vscode.window.registerTreeDataProvider;
        assert.notStrictEqual(viewContainers, undefined);
        
        // This is a limited test as there's no direct API to check for view containers
        // A more comprehensive test would interact with the views
    });
}); 