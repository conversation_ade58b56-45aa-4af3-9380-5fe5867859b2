#!/usr/bin/env python3
"""
Task Generation System for focus_forge.

This module implements Task T7: Develop Task Generation System.
It provides functionality to generate actionable development tasks from
analyzed PRD data, dependency graphs, and priority information.
"""
from enum import Enum
from typing import Dict, List, Set, Optional, Tuple, Any, TypeVar, Generic, Callable, Union, Literal
import json
import os
import logging
import re
from pathlib import Path
from dataclasses import dataclass, field

# Import related components
from .dependency_graph import DependencyGraph, Dependency, DependencyType
from .sequence_analysis import Sequence<PERSON><PERSON>yzer, SequenceConstraint, SequenceConstraintType
from .priority_inference import PriorityInferenceEngine, PriorityLevel, ComplexityLevel
from .nlp import RelationshipDetector
from .component_detection import ComponentDetector

# Define task status type
TaskStatus = Literal["pending", "in-progress", "done", "deferred"]

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class TaskGenerationConfig:
    """Configuration for task generation system."""
    default_status: TaskStatus = "pending"
    default_priority: PriorityLevel = "medium"
    use_sequence_constraints: bool = True
    generate_subtasks: bool = True
    max_subtasks_per_task: int = 3
    id_prefix: str = "T"
    detailed_descriptions: bool = True
    include_test_strategy: bool = True
    format_version: str = "1.0"


@dataclass
class TaskTemplate:
    """Template structure for generating tasks."""
    title_template: str
    description_template: str
    details_template: str
    test_strategy_template: str
    subtask_templates: List[Dict[str, str]] = field(default_factory=list)


class TaskType(Enum):
    """Types of tasks that can be generated."""
    FOUNDATION = "foundation"  # Core infrastructure or architectural tasks
    FEATURE = "feature"        # User-facing feature implementation
    INTEGRATION = "integration" # Integration between components
    TESTING = "testing"        # Testing-specific tasks
    DOCUMENTATION = "documentation" # Documentation tasks
    OPTIMIZATION = "optimization" # Performance optimization tasks
    UI = "ui"                  # User interface tasks
    API = "api"                # API implementation tasks
    DATABASE = "database"      # Database-related tasks
    DEPLOYMENT = "deployment"  # Deployment and infrastructure tasks


class TaskGenerator:
    """
    Task generation system that converts PRD analysis, dependency graphs,
    and priorities into concrete development tasks.
    """
    
    def __init__(self, config: Optional[TaskGenerationConfig] = None):
        """
        Initialize the task generator.
        
        Args:
            config: Optional configuration for task generation
        """
        self.config = config or TaskGenerationConfig()
        self.priority_engine = PriorityInferenceEngine()
        
        # Try to instantiate the RelationshipDetector, but provide a fallback if it doesn't work
        try:
            self.relationship_detector = RelationshipDetector()
        except (ImportError, AttributeError, TypeError) as e:
            logger.warning(f"Could not instantiate RelationshipDetector: {e}")
            # Create a simple replacement that extracts relationships based on keywords
            self.relationship_detector = self._create_simple_relationship_detector()
        
        # Try to instantiate the ComponentDetector, but provide a fallback if it doesn't work
        try:
            self.component_detector = ComponentDetector()
        except (ImportError, AttributeError, TypeError) as e:
            logger.warning(f"Could not instantiate ComponentDetector: {e}")
            # Create a simple replacement that extracts components based on headings
            self.component_detector = self._create_simple_component_detector()
        
        self.task_id_counter = 1
        self.templates = self._load_templates()
    
    def _create_simple_relationship_detector(self):
        """
        Create a simple relationship detector to use as fallback.
        
        Returns:
            An object with a detect_relationships method
        """
        class SimpleRelationshipDetector:
            def __init__(self):
                # Simple patterns to detect relationships between components
                self.dependency_patterns = [
                    (r'(\w+[\w\s]+)\s+depends\s+on\s+(\w+[\w\s]+)', "depends_on"),
                    (r'(\w+[\w\s]+)\s+requires\s+(\w+[\w\s]+)', "requires"),
                    (r'(\w+[\w\s]+)\s+needs\s+(\w+[\w\s]+)', "depends_on"),
                    (r'before\s+(\w+[\w\s]+),\s+(\w+[\w\s]+)\s+must', "depends_on"),
                    (r'after\s+(\w+[\w\s]+),\s+(\w+[\w\s]+)', "depends_on")
                ]
            
            def detect_relationships(self, text: str) -> List[Tuple[str, str, str]]:
                """Simple relationship detection based on regex patterns."""
                relationships = []
                
                for pattern, rel_type in self.dependency_patterns:
                    for match in re.finditer(pattern, text, re.IGNORECASE):
                        if rel_type in ["depends_on", "requires"]:
                            # Normal direction
                            source, target = match.group(1).strip(), match.group(2).strip()
                            relationships.append((source, rel_type, target))
                        elif "before" in pattern:
                            # Reversed for "before" patterns
                            source, target = match.group(2).strip(), match.group(1).strip()
                            relationships.append((source, "depends_on", target))
                
                return relationships
        
        return SimpleRelationshipDetector()
    
    def _create_simple_component_detector(self):
        """
        Create a simple component detector to use as fallback.
        
        Returns:
            An object with a detect_components method
        """
        class SimpleComponentDetector:
            def detect_components(self, text: str) -> List[Dict[str, Any]]:
                """Simple component detection based on headings."""
                components = []
                # Look for heading patterns like "## Component Name" or "### Feature"
                heading_pattern = r'(?:^|\n)#+\s+(.+?)(?:\n|$)'
                for match in re.finditer(heading_pattern, text):
                    heading = match.group(1).strip()
                    if len(heading) > 3:  # Ignore very short headings
                        # Try to find the paragraph following the heading
                        start_pos = match.end()
                        next_heading = re.search(heading_pattern, text[start_pos:])
                        if next_heading:
                            description = text[start_pos:start_pos + next_heading.start()].strip()
                        else:
                            description = text[start_pos:].strip()
                        
                        components.append({
                            "name": heading,
                            "description": description
                        })
                return components
        
        return SimpleComponentDetector()
    
    def _load_templates(self) -> Dict[TaskType, TaskTemplate]:
        """
        Load task templates for different task types.
        
        Returns:
            Dict mapping TaskType to TaskTemplate
        """
        # Default templates
        templates = {
            TaskType.FOUNDATION: TaskTemplate(
                title_template="Implement {component} Foundation",
                description_template="Create the core foundation for {component}.",
                details_template="Design and implement the foundation architecture for {component}. "
                                "This includes {details}.",
                test_strategy_template="Test the core functionality with unit tests. "
                                     "Verify integration with dependent components."
            ),
            TaskType.FEATURE: TaskTemplate(
                title_template="Implement {feature_name} Feature",
                description_template="Develop the {feature_name} feature as specified in the PRD.",
                details_template="Create the {feature_name} feature with the following capabilities: {details}. "
                                "Ensure proper error handling and user feedback.",
                test_strategy_template="Create unit tests for all functionality. "
                                     "Develop integration tests to verify behavior with other features."
            ),
            TaskType.INTEGRATION: TaskTemplate(
                title_template="Integrate {component_a} with {component_b}",
                description_template="Create integration between {component_a} and {component_b}.",
                details_template="Implement the necessary communication and data flow between "
                                "{component_a} and {component_b}. This includes {details}.",
                test_strategy_template="Test data exchange between components. "
                                     "Verify behavior when either component fails."
            ),
            # Additional templates can be defined for other task types
        }
        
        # Try to load custom templates if available
        try:
            custom_templates_path = Path(__file__).parent / "templates" / "task_templates.json"
            if custom_templates_path.exists():
                with open(custom_templates_path, 'r') as f:
                    custom_data = json.load(f)
                
                for task_type_str, template_data in custom_data.items():
                    try:
                        task_type = TaskType[task_type_str.upper()]
                        templates[task_type] = TaskTemplate(
                            title_template=template_data.get("title", ""),
                            description_template=template_data.get("description", ""),
                            details_template=template_data.get("details", ""),
                            test_strategy_template=template_data.get("test_strategy", ""),
                            subtask_templates=template_data.get("subtasks", [])
                        )
                    except (KeyError, ValueError) as e:
                        logger.warning(f"Invalid task type in templates: {task_type_str}, error: {str(e)}")
        except Exception as e:
            logger.warning(f"Error loading custom templates: {str(e)}")
            
        return templates
    
    def _generate_task_id(self) -> str:
        """
        Generate a unique task ID.
        
        Returns:
            str: Task ID in the format "T1", "T2", etc.
        """
        task_id = f"{self.config.id_prefix}{self.task_id_counter}"
        self.task_id_counter += 1
        return task_id

    def _estimate_task_complexity(self, component_data: Dict[str, Any]) -> ComplexityLevel:
        """
        Estimate the complexity of a task based on component data.
        
        Args:
            component_data: Data about the component
            
        Returns:
            ComplexityLevel: Estimated complexity (high/medium/low)
        """
        complexity_signals = {
            "high": [
                "complex", "challenging", "difficult", "sophisticated", "advanced",
                "extensive", "comprehensive", "intricate", "large-scale", "major"
            ],
            "low": [
                "simple", "straightforward", "basic", "minor", "easy", "small", 
                "minimal", "trivial", "quick", "lightweight"
            ]
        }
        
        # Extract text to analyze
        text = component_data.get("description", "") 
        if "details" in component_data:
            text += " " + component_data["details"]
        
        text = text.lower()
        
        # Count complexity signals
        high_count = sum(1 for term in complexity_signals["high"] if term in text)
        low_count = sum(1 for term in complexity_signals["low"] if term in text)
        
        # Compare and determine complexity
        if high_count > low_count:
            return "high"
        elif low_count > high_count:
            return "low"
        
        # Default to medium if unclear
        return "medium"
    
    def generate_tasks_from_components(
        self, 
        components: List[Dict[str, Any]], 
        dependency_graph: Optional[DependencyGraph] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate tasks from component analysis data.
        
        Args:
            components: List of component data
            dependency_graph: Optional dependency graph to use
            
        Returns:
            List[Dict[str, Any]]: Generated tasks
        """
        tasks = []
        
        # Create a new dependency graph if none was provided
        graph = dependency_graph or DependencyGraph()
        
        # First pass: Create tasks for each component
        for component in components:
            task_id = self._generate_task_id()
            
            # Determine task type based on component data
            task_type = self._determine_task_type(component)
            
            # Use the appropriate template
            template = self.templates.get(task_type, self.templates[TaskType.FEATURE])
            
            # Generate task data
            task = {
                "id": task_id,
                "title": self._fill_template(template.title_template, component),
                "description": self._fill_template(template.description_template, component),
                "status": self.config.default_status,
                "priority": self._determine_priority(component),
                "dependencies": [],  # Will be filled in second pass
                "details": self._fill_template(template.details_template, component),
                "test_strategy": self._fill_template(template.test_strategy_template, component) if self.config.include_test_strategy else None,
                "subtasks": [],  # Will be filled later if needed
                "component_id": component.get("id", ""),
                "task_type": task_type.value
            }
            
            # Clean up any unfilled template placeholders
            task["title"] = self._clean_template_output(task["title"])
            task["description"] = self._clean_template_output(task["description"])
            task["details"] = self._clean_template_output(task["details"])
            if task["test_strategy"]:
                task["test_strategy"] = self._clean_template_output(task["test_strategy"])
            
            tasks.append(task)
            graph.add_node(task_id)
            
            # Store task ID in component for dependency resolution
            component["task_id"] = task_id
        
        # Second pass: Resolve dependencies
        for i, component in enumerate(components):
            task = tasks[i]
            
            # Add dependencies based on component dependencies
            for dep_id in component.get("dependencies", []):
                # Find the task associated with the dependency component
                for comp in components:
                    if comp.get("id") == dep_id and "task_id" in comp:
                        # Add to task dependencies
                        if comp["task_id"] not in task["dependencies"]:
                            task["dependencies"].append(comp["task_id"])
                        
                        # Add to dependency graph
                        graph.add_dependency(Dependency(
                            source_id=task["id"],
                            target_id=comp["task_id"],
                            dep_type=DependencyType.DEPENDS_ON
                        ))
        
        # Generate subtasks if configured to do so
        if self.config.generate_subtasks:
            for task in tasks:
                task["subtasks"] = self._generate_subtasks(task, components)
        
        # Use sequence analysis to optimize task order if needed
        if self.config.use_sequence_constraints:
            sequence_analyzer = SequenceAnalyzer(
                tasks=tasks,
                task_id_fn=lambda t: t["id"],
                priority_fn=lambda t: {"high": 0, "medium": 1, "low": 2}[t["priority"]]
            )
            
            # Add technical constraints
            for task in tasks:
                if task["task_type"] == TaskType.FOUNDATION.value:
                    sequence_analyzer.add_constraint(SequenceConstraint(
                        constraint_type=SequenceConstraintType.FOUNDATION_FIRST,
                        task_ids=[task["id"]],
                        description=f"Foundation task {task['id']} should be prioritized",
                        weight=1.5
                    ))
            
            # Get optimized sequence
            optimized_tasks = sequence_analyzer.get_implementation_sequence()
            
            # Reorder tasks based on sequence analysis
            tasks = optimized_tasks
        
        return tasks
    
    def _clean_template_output(self, text: str) -> str:
        """
        Clean up any unfilled template placeholders from the output.
        
        Args:
            text: Text with possible unfilled placeholders
            
        Returns:
            str: Cleaned text
        """
        # Replace unfilled placeholders like {name} with empty string
        cleaned = re.sub(r'\{[^}]+\}', '', text)
        
        # Fix double spaces
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # Fix punctuation issues from template replacement
        cleaned = re.sub(r'\s+\.', '.', cleaned)
        cleaned = re.sub(r'\s+,', ',', cleaned)
        cleaned = re.sub(r'\s+:', ':', cleaned)
        
        return cleaned.strip()
    
    def _determine_task_type(self, component: Dict[str, Any]) -> TaskType:
        """
        Determine the task type based on component data.
        
        Args:
            component: Component data
            
        Returns:
            TaskType: Determined task type
        """
        # This is a simplified implementation
        component_type = component.get("type", "").lower()
        
        if "foundation" in component_type or "core" in component_type:
            return TaskType.FOUNDATION
        elif "ui" in component_type or "interface" in component_type:
            return TaskType.UI
        elif "api" in component_type:
            return TaskType.API
        elif "database" in component_type or "data" in component_type:
            return TaskType.DATABASE
        elif "integration" in component_type:
            return TaskType.INTEGRATION
        elif "test" in component_type:
            return TaskType.TESTING
        elif "document" in component_type:
            return TaskType.DOCUMENTATION
        elif "optimize" in component_type or "performance" in component_type:
            return TaskType.OPTIMIZATION
        elif "deploy" in component_type or "infra" in component_type:
            return TaskType.DEPLOYMENT
        else:
            return TaskType.FEATURE
    
    def _determine_priority(self, component: Dict[str, Any]) -> PriorityLevel:
        """
        Determine task priority based on component data.
        
        Args:
            component: Component data
            
        Returns:
            PriorityLevel: Determined priority level
        """
        # Use priority inference engine if a description is available
        if "description" in component:
            priority, _ = self.priority_engine.infer_priority(component["description"])
            return priority
        
        # Fallback to default priority
        return self.config.default_priority
    
    def _fill_template(self, template: str, data: Dict[str, Any]) -> str:
        """
        Fill a template string with data from a component.
        
        Args:
            template: Template string with placeholders
            data: Component data
            
        Returns:
            str: Filled template
        """
        result = template
        
        # Simple placeholder replacement
        for key, value in data.items():
            if isinstance(value, str):
                placeholder = f"{{{key}}}"
                result = result.replace(placeholder, value)
        
        return result
    
    def _generate_subtasks(self, task: Dict[str, Any], components: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate subtasks for a given task.
        
        Args:
            task: Parent task
            components: List of all components for context
            
        Returns:
            List[Dict[str, Any]]: Generated subtasks
        """
        subtasks = []
        subtask_count = min(self.config.max_subtasks_per_task, 3)  # Default to 3 if not specified
        
        # Get appropriate template
        task_type = TaskType(task["task_type"]) if task["task_type"] in [t.value for t in TaskType] else TaskType.FEATURE
        template = self.templates.get(task_type, self.templates[TaskType.FEATURE])
        
        # Check if we have specific subtask templates for this task type
        if template.subtask_templates and len(template.subtask_templates) > 0:
            # Use predefined subtask templates
            for i, subtask_template in enumerate(template.subtask_templates[:subtask_count]):
                subtask_id = f"{task['id']}.{i+1}"
                
                # Create a combined data dictionary for template filling
                combined_data = {**task}
                if "name" not in combined_data and "title" in task:
                    # Extract name from title for template usage
                    title_parts = task["title"].split(" ")
                    if len(title_parts) > 1:
                        combined_data["name"] = " ".join(title_parts[1:])  # Skip "Implement" or similar prefix
                
                subtask = {
                    "id": subtask_id,
                    "title": self._fill_template(subtask_template.get("title", "Subtask {i}"), {
                        **combined_data, "i": str(i+1), "parent_title": task["title"]
                    }),
                    "description": self._fill_template(subtask_template.get("description", ""), {
                        **combined_data, "i": str(i+1), "parent_title": task["title"]
                    }),
                    "status": self.config.default_status
                }
                
                # Clean up any unfilled template placeholders
                subtask["title"] = self._clean_template_output(subtask["title"])
                subtask["description"] = self._clean_template_output(subtask["description"])
                
                subtasks.append(subtask)
        else:
            # Generate generic subtasks
            phases = ["Design", "Implement", "Test"]
            
            for i in range(subtask_count):
                phase = phases[i] if i < len(phases) else f"Phase {i+1}"
                
                # Extract title without the task type prefix for more natural subtask titles
                title_parts = task["title"].split(" ", 1)
                base_title = title_parts[1] if len(title_parts) > 1 else task["title"]
                
                subtask = {
                    "id": f"{task['id']}.{i+1}",
                    "title": f"{phase} {base_title}",
                    "description": f"{phase.lower()} the {base_title.lower()}.",
                    "status": self.config.default_status
                }
                
                subtasks.append(subtask)
        
        return subtasks
    
    def analyze_prd_text(self, prd_text: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Analyze PRD text to extract components, priorities, and relationships.
        
        This method integrates the component detection, relationship detection,
        and priority inference to provide a comprehensive analysis of the PRD.
        
        Args:
            prd_text: The PRD text to analyze
        
        Returns:
            Dictionary with categorized components and their relationships
        """
        # Split the PRD into sections for more focused analysis
        sections = self._split_prd_into_sections(prd_text)
        
        # Initialize result structure
        result = {
            "components": [],
            "features": [],
            "integrations": [],
            "requirements": []
        }
        
        # Define component name mapping to track components across sections
        component_map = {}
        
        # Process each section
        for section_title, section_text in sections.items():
            # Skip empty sections
            if not section_text.strip():
                continue
            
            # Detect components in this section
            detected_components = self.component_detector.detect_components(section_text)
            
            # If no components were detected, create one from the section itself
            if not detected_components:
                detected_components = [{
                    "name": section_title,
                    "description": section_text[:200] + "..." if len(section_text) > 200 else section_text
                }]
            
            # Extract relationships
            relationships = self.relationship_detector.detect_relationships(section_text)
            
            # Determine section type based on content and title
            section_type = self._determine_section_type(section_title, section_text)
            
            # Process components and add to results
            for component in detected_components:
                # Ensure component has a description
                if "description" not in component or not component["description"]:
                    component["description"] = section_text[:200] + "..." if len(section_text) > 200 else section_text
                
                # Generate a unique ID
                comp_id = component_map.get(component["name"], f"C{len(component_map) + 1}")
                component_map[component["name"]] = comp_id
                
                # Set component type based on section
                component["type"] = section_type
                component["id"] = comp_id
                
                # Analyze priority and complexity
                priority, _ = self.priority_engine.infer_priority(component["description"])
                complexity = self._estimate_task_complexity(component)
                
                component["priority"] = priority
                component["complexity"] = complexity
                
                # Add to the appropriate category
                result[section_type + "s"].append(component)
        
        # Process relationships and link components
        self._process_relationships(result, relationships)
        
        return result
    
    def _split_prd_into_sections(self, prd_text: str) -> Dict[str, str]:
        """
        Split a PRD document into logical sections for analysis.
        
        Args:
            prd_text: Full PRD text
            
        Returns:
            Dictionary mapping section titles to section content
        """
        # Simple section detection based on Markdown-style headers
        section_pattern = r'(?:^|\n)#+\s+(.+?)(?:\n|$)'
        sections = {}
        current_section = "introduction"
        current_text = []
        
        # Split text into lines for processing
        lines = prd_text.split('\n')
        
        for line in lines:
            # Check if line is a header
            header_match = re.match(r'^#+\s+(.+)$', line)
            if header_match:
                # Save previous section
                if current_text:
                    sections[current_section] = '\n'.join(current_text)
                
                # Start new section
                current_section = header_match.group(1).lower().strip()
                current_text = []
            else:
                # Add line to current section
                current_text.append(line)
        
        # Save the last section
        if current_text:
            sections[current_section] = '\n'.join(current_text)
        
        # If no sections were found, treat the entire document as one section
        if len(sections) <= 1 and "introduction" in sections:
            # Try to split based on blank lines into pseudo-sections
            paragraphs = [p.strip() for p in prd_text.split('\n\n') if p.strip()]
            
            if paragraphs:
                sections = {}
                for i, para in enumerate(paragraphs):
                    # Use first few words as pseudo-section name
                    words = para.split()
                    section_name = " ".join(words[:3]).lower() if len(words) > 2 else f"section_{i+1}"
                    sections[section_name] = para
        
        return sections
    
    def _determine_section_type(self, section_title: str, section_text: str) -> str:
        """
        Determine the type of section based on its title and content.
        
        Args:
            section_title: Title of the section
            section_text: Text content of the section
            
        Returns:
            String indicating section type ("component", "feature", "integration", or "requirement")
        """
        # Convert to lowercase for case-insensitive matching
        title = section_title.lower()
        text = section_text.lower()
        
        # Check for section type indicators in the title
        if any(kw in title for kw in ["architecture", "foundation", "core", "infrastructure", "framework"]):
            return "component"
        elif any(kw in title for kw in ["feature", "functionality", "capability"]):
            return "feature"
        elif any(kw in title for kw in ["integration", "interface", "compatibility", "interact"]):
            return "integration"
        elif any(kw in title for kw in ["requirement", "constraint", "specification", "must have"]):
            return "requirement"
        
        # If title doesn't indicate type, check the content
        if any(kw in text for kw in ["architecture", "foundation", "component", "service", "module"]):
            return "component"
        elif any(kw in text for kw in ["feature", "user story", "user experience", "ux"]):
            return "feature"
        elif any(kw in text for kw in ["integration", "interface", "connect", "interact with"]):
            return "integration"
        
        # Default to requirement if nothing else matches
        return "requirement"
    
    def _process_relationships(self, result: Dict[str, List[Dict[str, Any]]], relationships: List[Tuple[str, str, str]]) -> None:
        """
        Process detected relationships and link components.
        
        Args:
            result: Analysis result dictionary with components
            relationships: List of (source, type, target) relationship tuples
        """
        # Build a map of component names to their IDs for quick lookup
        component_name_map = {}
        for section_key in ["components", "features", "integrations", "requirements"]:
            for component in result[section_key]:
                component_name_map[component["name"].lower()] = component["id"]
                # Initialize relationships list if not present
                if "relationships" not in component:
                    component["relationships"] = []
                # Initialize dependencies list if not present
                if "dependencies" not in component:
                    component["dependencies"] = []
        
        # Process each relationship
        for source_name, rel_type, target_name in relationships:
            # Normalize names for matching
            source_norm = source_name.lower()
            target_norm = target_name.lower()
            
            # Find best matching component names in our map
            source_id = self._find_best_match(source_norm, component_name_map)
            target_id = self._find_best_match(target_norm, component_name_map)
            
            if source_id and target_id:
                # Find the component objects
                source_component = None
                target_component = None
                
                for section_key in ["components", "features", "integrations", "requirements"]:
                    for component in result[section_key]:
                        if component["id"] == source_id:
                            source_component = component
                        if component["id"] == target_id:
                            target_component = component
                
                if source_component and target_component:
                    # Add relationship to source component
                    source_component["relationships"].append({
                        "type": rel_type,
                        "target": target_component["name"],
                        "target_id": target_id
                    })
                    
                    # If it's a dependency relationship, add to dependencies list
                    if rel_type in ["depends_on", "requires", "prerequisite"]:
                        if target_id not in source_component["dependencies"]:
                            source_component["dependencies"].append(target_id)
    
    def _find_best_match(self, name: str, component_map: Dict[str, str]) -> Optional[str]:
        """
        Find the best matching component ID for a given name.
        
        Args:
            name: Component name to match
            component_map: Map of component names to IDs
            
        Returns:
            Component ID or None if no match found
        """
        # First try exact match
        if name in component_map:
            return component_map[name]
        
        # Then try contained match (e.g., "authentication service" matches "authentication")
        for comp_name, comp_id in component_map.items():
            if name in comp_name or comp_name in name:
                return comp_id
        
        # Try word-level matching
        name_words = name.split()
        for comp_name, comp_id in component_map.items():
            comp_words = comp_name.split()
            if any(word in comp_words for word in name_words if len(word) > 3):
                return comp_id
        
        return None
    
    def generate_tasks_from_prd(self, prd_text: str) -> List[Dict[str, Any]]:
        """
        Generate tasks directly from PRD text.
        
        This is a convenience method that:
        1. Uses our custom PRD analysis functionality
        2. Builds a dependency graph
        3. Generates tasks using the components
        
        Args:
            prd_text: Product Requirements Document text
            
        Returns:
            List[Dict[str, Any]]: Generated tasks
        """
        # Use our comprehensive analysis method
        analysis_result = self.analyze_prd_text(prd_text)
        
        # Convert analysis to components
        components = []
        
        # First add core components (they'll be first in dependency chain)
        for component in analysis_result.get("components", []):
            components.append(component)
        
        # Then add features
        for feature in analysis_result.get("features", []):
            components.append(feature)
        
        # Then add integrations (they typically depend on components and features)
        for integration in analysis_result.get("integrations", []):
            components.append(integration)
        
        # Finally add requirements (they might be standalone or depend on anything)
        for requirement in analysis_result.get("requirements", []):
            components.append(requirement)
        
        # Create dependency graph
        dependency_graph = DependencyGraph()
        
        # Add nodes and dependencies to graph
        for component in components:
            dependency_graph.add_node(component["id"])
            
            # Add dependencies to graph
            for dep_id in component.get("dependencies", []):
                dependency_graph.add_dependency(Dependency(
                    source_id=component["id"],
                    target_id=dep_id,
                    dep_type=DependencyType.DEPENDS_ON
                ))
        
        # Generate tasks from these components
        return self.generate_tasks_from_components(components, dependency_graph)


def convert_prd_to_tasks(prd_text: str, config: Optional[TaskGenerationConfig] = None) -> List[Dict[str, Any]]:
    """
    Convenience function to convert PRD text to tasks.
    
    Args:
        prd_text: Product Requirements Document text
        config: Optional task generation configuration
        
    Returns:
        List[Dict[str, Any]]: Generated tasks
    """
    generator = TaskGenerator(config)
    return generator.generate_tasks_from_prd(prd_text) 