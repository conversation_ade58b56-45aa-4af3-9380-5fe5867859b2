"""Document discovery module for Focus Forge.

This module provides functions to discover and analyze documentation files
in a project directory, with support for various documentation structures.
"""

import os
import re
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple, Set, Any
import hashlib

# Configure logging
logger = logging.getLogger(__name__)

# Constants
DEFAULT_SCAN_DEPTH = 3
DOCS_CACHE_DIR = ".focus_forge"
DOCS_CACHE_FILE = "docs_cache.json"
LEARNING_PROFILES_DIR = "learning"
AI_FOLDER = ".ai"
CLAUDE_FOLDER = ".claude"
STANDARD_DOCS = [
    "README.md", 
    "CHANGELOG.md", 
    "CONTRIBUTING.md",
    "ARCHITECTURE.md",
    "DESIGN.md",
]
PRD_PATTERNS = [
    r"PRD.*\.md",
    r".*[Rr]equirements.*\.md",
    r".*[Ss]pecification.*\.md",
    r".*[Dd]esign.*[Dd]oc.*\.md",
]

class DocumentType:
    """Enumeration of document types."""
    UNKNOWN = "unknown"
    README = "readme"
    CHANGELOG = "changelog"
    REQUIREMENTS = "requirements"
    ARCHITECTURE = "architecture"
    DESIGN = "design"
    CONTRIBUTING = "contributing"
    API = "api"
    GUIDE = "guide"
    TUTORIAL = "tutorial"


class GuidanceSystemType:
    """Enumeration of AI guidance system types."""
    UNKNOWN = "unknown"
    BUTTERBOT = "butterbot"
    CLAUDE = "claude"
    CUSTOM = "custom"
    STANDARD = "standard"


class Document:
    """Represents a documentation file."""
    
    def __init__(self, path: Union[str, Path], 
                 doc_type: str = DocumentType.UNKNOWN,
                 confidence: float = 0.0):
        self.path = Path(path)
        self.doc_type = doc_type
        self.confidence = confidence
        self.title = ""
        self.summary = ""
        self.modified = None
        self.size = 0
        self.content = None
        self.related_docs = []
        self.metadata = {}
        
        # Initialize with basic file information
        self._init_file_info()
    
    def _init_file_info(self):
        """Initialize with basic file information."""
        if self.path.exists():
            self.size = self.path.stat().st_size
            self.modified = datetime.fromtimestamp(self.path.stat().st_mtime)
            
            # Try to extract a title from the first heading
            try:
                with open(self.path, 'r', encoding='utf-8') as f:
                    first_lines = [next(f) for _ in range(10)]
                    for line in first_lines:
                        if line.startswith('# '):
                            self.title = line[2:].strip()
                            break
            except Exception as e:
                logger.warning(f"Could not read file {self.path}: {e}")
    
    def read_content(self) -> str:
        """Read the content of the document."""
        if self.content is None:
            try:
                with open(self.path, 'r', encoding='utf-8') as f:
                    self.content = f.read()
            except Exception as e:
                logger.warning(f"Could not read file {self.path}: {e}")
                self.content = ""
        return self.content
    
    def extract_summary(self, max_chars: int = 200) -> str:
        """Extract a summary from the document content."""
        if not self.content:
            self.read_content()
        
        if self.content:
            # Try to find the first paragraph after the title
            lines = self.content.split('\n')
            in_paragraph = False
            paragraph = []
            
            for line in lines:
                if line.startswith('#'):
                    # Skip headings
                    in_paragraph = False
                    continue
                
                # Skip empty lines unless we're in a paragraph
                if not line.strip():
                    in_paragraph = False
                    if paragraph:
                        break
                    continue
                
                # Start or continue a paragraph
                in_paragraph = True
                paragraph.append(line.strip())
            
            if paragraph:
                summary = ' '.join(paragraph)
                self.summary = summary[:max_chars] + ('...' if len(summary) > max_chars else '')
            else:
                # Fallback to first non-empty lines
                non_empty = [l.strip() for l in lines if l.strip() and not l.startswith('#')]
                if non_empty:
                    summary = ' '.join(non_empty[:3])
                    self.summary = summary[:max_chars] + ('...' if len(summary) > max_chars else '')
        
        return self.summary
    
    def to_dict(self, include_content: bool = False) -> Dict[str, Any]:
        """Convert the document to a dictionary."""
        result = {
            "path": str(self.path),
            "type": self.doc_type,
            "confidence": self.confidence,
            "title": self.title,
            "summary": self.summary,
            "modified": self.modified.isoformat() if self.modified else None,
            "size": self.size,
            "metadata": self.metadata
        }
        
        if include_content and self.content:
            result["content"] = self.content
        
        if self.related_docs:
            result["related_docs"] = self.related_docs
            
        return result


class GuidanceSystem:
    """Represents an AI guidance system."""
    
    def __init__(self, path: Union[str, Path] = None, 
                 system_type: str = GuidanceSystemType.UNKNOWN,
                 confidence: float = 0.0):
        self.path = Path(path) if path else None
        self.system_type = system_type
        self.confidence = confidence
        self.completeness = 0.0
        self.documents = []
        self.markers = []
        self.missing = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the guidance system to a dictionary."""
        return {
            "path": str(self.path) if self.path else None,
            "type": self.system_type,
            "confidence": self.confidence,
            "completeness": self.completeness,
            "markers": self.markers,
            "missing": self.missing
        }


class DocumentCache:
    """Cache for document discovery results."""
    
    def __init__(self, project_dir: Union[str, Path] = None):
        self.project_dir = Path(project_dir or os.getcwd())
        self.cache_dir = self.project_dir / DOCS_CACHE_DIR
        self.cache_file = self.cache_dir / DOCS_CACHE_FILE
        self.document_data = []
        self.guidance_data = {}
        self.relationships_data = []
        self.timestamp = None
        self.version = VERSION
    
    def add_document(self, document: Document) -> None:
        """Add a document to the cache."""
        self.document_data.append(document.to_dict())
    
    def set_guidance_system(self, guidance_system: GuidanceSystem) -> None:
        """Set the guidance system data."""
        self.guidance_data = guidance_system.to_dict()
    
    def save(self, include_content: bool = False) -> None:
        """Save cache to file."""
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Update document data if needed
        if not self.document_data:
            logger.warning("No document data to save to cache")
            return
        
        # Update timestamp
        self.timestamp = datetime.now().isoformat()
        
        # Prepare cache data
        cache_data = {
            "metadata": {
                "version": self.version,
                "timestamp": self.timestamp,
                "project_dir": str(self.project_dir),
                "include_content": include_content
            },
            "guidance_system": self.guidance_data,
            "documents": self.document_data,
            "relationships": self.relationships_data
        }
        
        # Save to file
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2)
            logger.info(f"Cache saved to {self.cache_file}")
        except Exception as e:
            logger.error(f"Failed to save cache: {e}")
    
    def load(self) -> bool:
        """Load cache from file.
        
        Returns:
            bool: True if cache was loaded successfully, False otherwise
        """
        if not self.cache_file.exists():
            logger.debug(f"Cache file {self.cache_file} does not exist")
            return False
        
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # Check cache version
            metadata = cache_data.get("metadata", {})
            version = metadata.get("version")
            if version != self.version:
                logger.warning(f"Cache version mismatch: {version} != {self.version}")
                return False
            
            # Load cache data
            self.timestamp = metadata.get("timestamp")
            self.document_data = cache_data.get("documents", [])
            self.guidance_data = cache_data.get("guidance_system", {})
            self.relationships_data = cache_data.get("relationships", [])
            
            logger.info(f"Loaded cache from {self.cache_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to load cache: {e}")
            return False
    
    def is_valid(self, max_age_hours: int = 24) -> bool:
        """Check if cache is valid.
        
        Args:
            max_age_hours: Maximum age of cache in hours
            
        Returns:
            bool: True if cache is valid, False otherwise
        """
        if not self.timestamp:
            return False
        
        # Check cache age
        cache_time = datetime.fromisoformat(self.timestamp)
        age = datetime.now() - cache_time
        return age.total_seconds() < max_age_hours * 3600


class DocDiscovery:
    """Handles discovery and analysis of documentation files."""
    
    def __init__(self, project_dir: Union[str, Path] = None):
        self.project_dir = Path(project_dir or os.getcwd())
        self.documents = []
        self.guidance_system = GuidanceSystem()
        self.cache = DocumentCache(self.project_dir)
        self.active_profile = None
        self.document_relationships = []
    
    def discover_documents(self, 
                      max_depth: int = DEFAULT_SCAN_DEPTH,
                      use_cache: bool = True,
                      include_content: bool = False,
                      use_learning: bool = False,
                      profile_name: Optional[str] = None) -> List[Document]:
        """Discover and analyze documentation files in the project."""
        # Check if we can use cache
        if use_cache and self.cache.load() and self.cache.is_valid():
            logger.info("Using cached document information")
            # Build documents from cache
            self.documents = []
            for doc_data in self.cache.document_data:
                doc = Document(doc_data["path"])
                doc.doc_type = doc_data["type"]
                doc.confidence = doc_data["confidence"]
                doc.title = doc_data.get("title", "")
                doc.summary = doc_data.get("summary", "")
                if doc_data.get("modified"):
                    doc.modified = datetime.fromisoformat(doc_data["modified"])
                doc.size = doc_data.get("size", 0)
                if include_content and "content" in doc_data:
                    doc.content = doc_data["content"]
                self.documents.append(doc)
            
            # Build guidance system from cache
            if self.cache.guidance_data:
                self.guidance_system = GuidanceSystem(
                    path=self.cache.guidance_data.get("path", ""),
                    system_type=self.cache.guidance_data.get("type", GuidanceSystemType.UNKNOWN),
                    confidence=self.cache.guidance_data.get("confidence", 0.0)
                )
            
            # Build relationships from cache
            if hasattr(self.cache, "relationships_data") and self.cache.relationships_data:
                self.document_relationships = self.cache.relationships_data
            
            return self.documents
        
        # If not using cache or cache is invalid, perform discovery
        logger.info(f"Performing document discovery with max depth {max_depth}")
        self.documents = []
        
        # Load active learning profile if requested
        if use_learning and profile_name:
            self._load_learning_profile(profile_name)
        
        # First, check for AI guidance folders
        self._discover_ai_guidance()
        
        # Second, check for standard docs in the project root
        self._discover_standard_docs()
        
        # Third, scan for markdown files
        self._discover_markdown_files(max_depth)
        
        # Analyze documents to classify and extract metadata
        self._analyze_documents(include_content)
        
        # Find relationships between documents
        self._find_relationships()
        
        # Update cache
        self.cache.document_data = [doc.to_dict(include_content) for doc in self.documents]
        self.cache.guidance_data = self.guidance_system.to_dict()
        self.cache.relationships_data = self.document_relationships
        self.cache.save(include_content)
        
        # Learn from documents if using learning
        if use_learning and self.active_profile:
            self.active_profile.learn_from_documents(self.documents)
            self.active_profile.save()
        
        return self.documents

    def _load_learning_profile(self, profile_name: str) -> bool:
        """Load a learning profile by name."""
        profiles_dir = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
        if not os.path.exists(profiles_dir):
            logger.warning(f"Learning profiles directory {profiles_dir} does not exist")
            return False
        
        # Try exact match first
        for filename in os.listdir(profiles_dir):
            if filename.startswith(profile_name.lower().replace(' ', '_')) and filename.endswith('.json'):
                try:
                    self.active_profile = LearningProfile.load(os.path.join(profiles_dir, filename))
                    logger.info(f"Loaded learning profile: {self.active_profile.name}")
                    return True
                except Exception as e:
                    logger.error(f"Failed to load learning profile {filename}: {e}")
                    return False
        
        # If no exact match, try fuzzy match
        for filename in os.listdir(profiles_dir):
            if filename.endswith('.json'):
                try:
                    profile = LearningProfile.load(os.path.join(profiles_dir, filename))
                    if profile_name.lower() in profile.name.lower():
                        self.active_profile = profile
                        logger.info(f"Loaded learning profile (fuzzy match): {self.active_profile.name}")
                        return True
                except Exception:
                    pass
        
        logger.warning(f"No learning profile found matching {profile_name}")
        return False

    def _discover_ai_guidance(self) -> None:
        """Discover AI guidance folders and their contents."""
        ai_path = self.project_dir / AI_FOLDER
        claude_path = self.project_dir / CLAUDE_FOLDER
        
        # Check for AI guidance folders
        has_ai = ai_path.exists() and ai_path.is_dir()
        has_claude = claude_path.exists() and claude_path.is_dir()
        
        # Initialize guidance system
        self.guidance_system = GuidanceSystem()
        
        if has_ai:
            logger.info(f"Found AI guidance folder: {ai_path}")
            self.guidance_system.path = ai_path
            self._analyze_butterbot_guidance(ai_path)
            
        elif has_claude:
            logger.info(f"Found Claude guidance folder: {claude_path}")
            self.guidance_system.path = claude_path
            self._analyze_claude_guidance(claude_path)
    
    def _analyze_butterbot_guidance(self, path: Path) -> None:
        """Analyze a Butterbot guidance folder."""
        # Check for key Butterbot markers
        structure_file = path / "STRUCTURE.md"
        has_structure = structure_file.exists() and structure_file.is_file()
        
        # Check for standard numbered directories
        standard_dirs = [
            "1-context",
            "2-technical-design", 
            "3-development",
            "4-acceptance"
        ]
        
        found_dirs = []
        for dir_name in standard_dirs:
            full_path = path / "docs" / dir_name
            if full_path.exists() and full_path.is_dir():
                found_dirs.append(dir_name)
                
                # Scan directory for documents
                self._discover_in_directory(full_path, max_depth=2)
        
        # Calculate confidence based on markers
        markers = []
        if has_structure:
            markers.append("STRUCTURE.md")
            # Add the structure file as a document
            doc = Document(structure_file, doc_type="structure", confidence=1.0)
            self.documents.append(doc)
        
        for dir_name in found_dirs:
            markers.append(f"Directory: {dir_name}")
        
        # Set guidance system properties
        self.guidance_system.system_type = GuidanceSystemType.BUTTERBOT
        self.guidance_system.markers = markers
        self.guidance_system.missing = [d for d in standard_dirs if d not in found_dirs]
        
        # Calculate confidence score
        confidence = 0.0
        if has_structure:
            confidence += 0.5  # Structure file is a strong indicator
        
        # Add points for each found directory
        confidence += 0.1 * len(found_dirs)
        
        # Normalize to 0-1 range
        self.guidance_system.confidence = min(confidence, 1.0)
        
        # Calculate completeness (ratio of found dirs to standard dirs)
        self.guidance_system.completeness = len(found_dirs) / len(standard_dirs)
    
    def _analyze_claude_guidance(self, path: Path) -> None:
        """Analyze a Claude guidance folder."""
        # TODO: Implement Claude-specific detection
        # For now, just scan for docs
        self._discover_in_directory(path, max_depth=2)
        
        # Set basic guidance system info
        self.guidance_system.system_type = GuidanceSystemType.CLAUDE
        self.guidance_system.confidence = 0.7  # Default confidence for Claude folder
    
    def _discover_standard_docs(self) -> None:
        """Discover standard documentation files in project root."""
        for doc_name in STANDARD_DOCS:
            doc_path = self.project_dir / doc_name
            if doc_path.exists() and doc_path.is_file():
                logger.info(f"Found standard doc: {doc_path}")
                doc = Document(doc_path)
                self.documents.append(doc)
        
        # Look for PRD files using patterns
        for pattern in PRD_PATTERNS:
            for file_path in self.project_dir.glob(pattern):
                if file_path.is_file():
                    logger.info(f"Found PRD doc: {file_path}")
                    doc = Document(file_path)
                    self.documents.append(doc)
    
    def _discover_in_directory(self, directory: Path, max_depth: int = 1, 
                              current_depth: int = 0) -> None:
        """Recursively discover documentation files in a directory."""
        if current_depth > max_depth:
            return
        
        try:
            for item in directory.iterdir():
                if item.is_file() and item.suffix.lower() in ('.md', '.txt', '.rst'):
                    logger.debug(f"Found document: {item}")
                    doc = Document(item)
                    self.documents.append(doc)
                elif item.is_dir() and not item.name.startswith('.'):
                    self._discover_in_directory(
                        item, max_depth, current_depth + 1
                    )
        except PermissionError:
            logger.warning(f"Permission denied: {directory}")
    
    def _discover_markdown_files(self, max_depth: int = 1) -> None:
        """Discover markdown files in the project directory."""
        for file_path in self.project_dir.glob('**/*.md'):
            # Skip files in .git and other dot directories
            if any(part.startswith('.') for part in file_path.parts):
                continue
                
            # Skip files in already processed directories
            if (self.project_dir / 'docs' in file_path.parents or 
                self.project_dir / AI_FOLDER in file_path.parents or
                self.project_dir / CLAUDE_FOLDER in file_path.parents):
                continue
            
            # Skip already discovered files
            if any(doc.path == file_path for doc in self.documents):
                continue
                
            # Check depth
            rel_path = file_path.relative_to(self.project_dir)
            if len(rel_path.parts) > max_depth:
                continue
                
            # Add the document
            logger.debug(f"Found markdown file: {file_path}")
            doc = Document(file_path)
            self.documents.append(doc)
    
    def _analyze_documents(self, include_content: bool = False) -> None:
        """Analyze discovered documents to determine their types."""
        for doc in self.documents:
            # Read content if needed
            if include_content:
                doc.read_content()
            
            # Extract summary
            doc.extract_summary()
            
            # Determine document type and confidence
            self._classify_document(doc)
    
    def _classify_document(self, doc: Document) -> None:
        """Classify a document based on its filename, location, and content."""
        # Start with unknown type and no confidence
        doc_type = DocumentType.UNKNOWN
        confidence = 0.0
        
        # First, try to classify by filename
        filename = doc.path.name.lower()
        
        # Check standard document patterns
        if filename == "readme.md":
            doc_type = DocumentType.README
            confidence = 0.9
        elif filename == "changelog.md":
            doc_type = DocumentType.CHANGELOG
            confidence = 0.9
        elif filename == "contributing.md":
            doc_type = DocumentType.CONTRIBUTING
            confidence = 0.9
        elif filename == "architecture.md":
            doc_type = DocumentType.ARCHITECTURE
            confidence = 0.9
        elif filename == "design.md":
            doc_type = DocumentType.DESIGN
            confidence = 0.9
        elif any(re.match(pattern, filename, re.IGNORECASE) for pattern in PRD_PATTERNS):
            doc_type = DocumentType.REQUIREMENTS
            confidence = 0.8
        
        # If we have a learning profile, try to apply it
        if confidence < 0.7 and self.active_profile:
            learned_type, learned_confidence = self.active_profile.apply_to_document(doc)
            if learned_confidence > confidence:
                doc_type = learned_type
                confidence = learned_confidence
                logger.debug(f"Classified {doc.path} as {doc_type} using learning profile with confidence {confidence:.2f}")
        
        # If still low confidence, try content-based classification
        if confidence < 0.6:
            self._classify_by_content(doc)
            # If content classification gave a result, use it
            if doc.doc_type != DocumentType.UNKNOWN and doc.confidence > confidence:
                return
        
        # Update the document with our classification
        doc.doc_type = doc_type
        doc.confidence = confidence
    
    def _classify_by_content(self, doc: Document) -> None:
        """Classify a document based on its content."""
        content = doc.content.lower()
        
        # Simple keyword-based classification
        # In a real implementation, this would be more sophisticated
        keywords = {
            DocumentType.REQUIREMENTS: [
                "requirements", "specification", "product requirements", 
                "user stories", "acceptance criteria"
            ],
            DocumentType.ARCHITECTURE: [
                "architecture", "system design", "high-level design", 
                "components", "modules", "services"
            ],
            DocumentType.API: [
                "api reference", "endpoints", "interface", "rest", 
                "http", "request", "response"
            ],
            DocumentType.GUIDE: [
                "guide", "how to", "usage", "instructions", "reference"
            ],
            DocumentType.TUTORIAL: [
                "tutorial", "walkthrough", "step by step", "example"
            ]
        }
        
        # Count occurrences of keywords
        type_scores = {}
        for doc_type, words in keywords.items():
            score = sum(content.count(word) for word in words)
            if score > 0:
                type_scores[doc_type] = score
        
        if type_scores:
            # Choose the type with the highest score
            best_type = max(type_scores.items(), key=lambda x: x[1])
            doc.doc_type = best_type[0]
            
            # Convert score to a confidence value between 0 and 1
            max_possible = 20  # Arbitrary scaling factor
            doc.confidence = min(best_type[1] / max_possible, 0.9)  # Cap at 0.9 for content-based
        else:
            doc.doc_type = DocumentType.UNKNOWN
            doc.confidence = 0.0
    
    def _find_relationships(self) -> None:
        """Find relationships between documents based on links and content."""
        self.document_relationships = []
        
        # Create a map of documents by path for quick lookup
        doc_map = {str(doc.path): doc for doc in self.documents}
        
        # Check each document for links to other documents
        for source_doc in self.documents:
            content = source_doc.read_content()
            if not content:
                continue
                
            # Look for markdown links
            md_links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
            for link_text, link_target in md_links:
                # Normalize the link target path
                target_path = link_target
                if not os.path.isabs(target_path):
                    # Resolve relative to the source document
                    target_path = os.path.normpath(os.path.join(
                        os.path.dirname(str(source_doc.path)), 
                        target_path
                    ))
                
                # Check if the target is a known document
                if target_path in doc_map:
                    self.document_relationships.append({
                        "source": str(source_doc.path),
                        "target": target_path,
                        "type": "link",
                        "text": link_text
                    })
            
            # Look for indirect references (mentions of other document titles)
            for target_doc in self.documents:
                if target_doc == source_doc or not target_doc.title:
                    continue
                    
                # Check if the source mentions the target's title
                if target_doc.title in content:
                    # Count occurrences
                    count = content.count(target_doc.title)
                    if count > 0:
                        self.document_relationships.append({
                            "source": str(source_doc.path),
                            "target": str(target_doc.path),
                            "type": "reference",
                            "count": count
                        })
        
        # Add relationship to guidance structure if applicable
        if self.guidance_system.path:
            for doc in self.documents:
                relative_path = os.path.relpath(str(doc.path), str(self.project_dir))
                if relative_path.startswith(self.guidance_system.path):
                    self.document_relationships.append({
                        "source": self.guidance_system.path,
                        "target": str(doc.path),
                        "type": "contains",
                    })


# CLI command functions
def discover_docs(project_dir: Union[str, Path] = None,
                 max_depth: int = DEFAULT_SCAN_DEPTH,
                 use_cache: bool = False,
                 include_content: bool = False,
                 use_learning: bool = False,
                 profile_name: Optional[str] = None,
                 output_format: str = "text") -> Union[str, Dict[str, Any]]:
    """
    Discover documentation files in a project.
    
    Args:
        project_dir: Project directory to scan
        max_depth: Maximum directory depth to search
        use_cache: Whether to use cached results if available
        include_content: Whether to include document content in results
        use_learning: Whether to use learning profiles for document classification
        profile_name: Name of the learning profile to use
        output_format: Output format ("text" or "json")
        
    Returns:
        Discovery results as text or JSON object
    """
    if project_dir is None:
        project_dir = os.getcwd()
        
    # Create document discovery instance
    discovery = DocDiscovery(project_dir)
    
    # Discover documents
    docs = discovery.discover_documents(
        max_depth=max_depth,
        use_cache=use_cache,
        include_content=include_content,
        use_learning=use_learning,
        profile_name=profile_name
    )
    
    # Build result
    if output_format == "json":
        result = {
            "project_dir": str(discovery.project_dir),
            "documents": [doc.to_dict(include_content) for doc in docs],
            "guidance_system": discovery.guidance_system.to_dict() if discovery.guidance_system else None,
            "relationships": discovery.document_relationships
        }
        
        # Convert to JSON string or return dict
        if isinstance(output_format, str) and output_format.lower() == "json":
            return json.dumps(result, indent=2, default=str)
        return result
    
    # Text output
    lines = []
    lines.append(f"Project directory: {discovery.project_dir}")
    lines.append("")
    
    # Add guidance system info
    if discovery.guidance_system and discovery.guidance_system.system_type != GuidanceSystemType.UNKNOWN:
        lines.append(f"AI Guidance System: {discovery.guidance_system.system_type}")
        lines.append(f"Confidence: {discovery.guidance_system.confidence:.2f}")
        lines.append(f"Path: {discovery.guidance_system.path}")
        lines.append("")
    
    # Add document counts
    doc_counts = {}
    for doc in docs:
        if doc.doc_type not in doc_counts:
            doc_counts[doc.doc_type] = 0
        doc_counts[doc.doc_type] += 1
    
    if doc_counts:
        lines.append("Document Counts:")
        for doc_type, count in doc_counts.items():
            lines.append(f"  {doc_type}: {count}")
        lines.append("")
    
    # Add documents
    if docs:
        lines.append("Documents:")
        for doc in docs:
            lines.append(f"  {doc.path}")
            lines.append(f"    Type: {doc.doc_type}")
            lines.append(f"    Confidence: {doc.confidence:.2f}")
            if doc.title:
                lines.append(f"    Title: {doc.title}")
            if doc.modified:
                lines.append(f"    Modified: {doc.modified.isoformat()}")
            if doc.summary:
                lines.append(f"    Summary: {doc.summary}")
            lines.append("")
    
    # Add relationships
    if discovery.document_relationships:
        lines.append("Document Relationships:")
        for rel in discovery.document_relationships:
            rel_type = rel.get("type", "unknown")
            source = os.path.basename(rel["source"])
            target = os.path.basename(rel["target"])
            
            if rel_type == "link":
                lines.append(f"  {source} → {target} (link: {rel.get('text', '')})")
            elif rel_type == "reference":
                count = rel.get("count", 1)
                lines.append(f"  {source} → {target} ({count} references)")
            elif rel_type == "contains":
                lines.append(f"  {source} contains {target}")
            else:
                lines.append(f"  {source} → {target} ({rel_type})")
    
    return "\n".join(lines)


def analyze_guidance(project_dir: Union[str, Path] = None,
                    path: Union[str, Path] = None,
                    verbose: bool = False) -> Dict[str, Any]:
    """
    Analyze AI guidance system in a project.
    
    Args:
        project_dir: Project directory
        path: Specific path to analyze
        verbose: Whether to include detailed output
        
    Returns:
        Analysis results as a dictionary
    """
    discovery = DocDiscovery(project_dir)
    
    # If specific path provided, only analyze that
    if path:
        path = Path(path)
        if path.exists() and path.is_dir():
            if path.name == AI_FOLDER:
                discovery.guidance_system = GuidanceSystem(path=path)
                discovery._analyze_butterbot_guidance(path)
            elif path.name == CLAUDE_FOLDER:
                discovery.guidance_system = GuidanceSystem(path=path)
                discovery._analyze_claude_guidance(path)
            else:
                # Treat as custom documentation
                discovery.guidance_system = GuidanceSystem(
                    path=path,
                    system_type=GuidanceSystemType.CUSTOM
                )
                discovery._discover_in_directory(path, max_depth=2)
    else:
        # Otherwise, perform regular discovery
        discovery.discover_documents(use_cache=False)
    
    if discovery.guidance_system:
        result = discovery.guidance_system.to_dict()
        
        # Add document counts
        doc_counts = {}
        for doc in discovery.documents:
            if doc.doc_type not in doc_counts:
                doc_counts[doc.doc_type] = 0
            doc_counts[doc.doc_type] += 1
        
        result["document_counts"] = doc_counts
        
        if verbose:
            result["documents"] = [doc.to_dict() for doc in discovery.documents]
        
        return result
    else:
        return {
            "path": None,
            "type": GuidanceSystemType.UNKNOWN,
            "confidence": 0.0,
            "completeness": 0.0,
            "markers": [],
            "missing": ["No guidance system found"]
        }


def cache_docs(project_dir: Union[str, Path] = None,
              include_content: bool = False,
              force: bool = False,
              output: Union[str, Path] = None) -> str:
    """
    Generate and save a cache of documentation files.
    
    Args:
        project_dir: Project directory
        include_content: Whether to include document content in the cache
        force: Whether to force regeneration of the cache
        output: Custom output path
        
    Returns:
        Path to the cache file
    """
    discovery = DocDiscovery(project_dir)
    
    # If custom output path specified, set it in the cache
    if output:
        discovery.cache.cache_path = Path(output)
    
    # Discover documents
    discovery.discover_documents(
        use_cache=not force,
        include_content=include_content
    )
    
    # Save cache
    discovery.cache.save(include_content=include_content)
    
    return str(discovery.cache.cache_path)


def query_cache(project_dir: Union[str, Path] = None,
               doc_type: str = None,
               format: str = "text") -> Union[str, Dict[str, Any]]:
    """
    Query the document cache.
    
    Args:
        project_dir: Project directory
        doc_type: Document type to filter by
        format: Output format ("text", "json", "dict")
        
    Returns:
        Query results in the requested format
    """
    discovery = DocDiscovery(project_dir)
    
    # Load cache
    if not discovery.cache.load():
        if format == "dict":
            return {"error": "No cache available"}
        elif format == "json":
            return json.dumps({"error": "No cache available"})
        else:
            return "No document cache available. Run 'cache-docs' first."
    
    # Filter documents by type if specified
    if doc_type:
        docs = [doc for doc in discovery.cache.documents if doc.doc_type == doc_type]
    else:
        docs = discovery.cache.documents
    
    if format == "dict":
        return {
            "guidance_system": discovery.cache.guidance_system.to_dict() 
                              if discovery.cache.guidance_system else None,
            "documents": [doc.to_dict() for doc in docs]
        }
    elif format == "json":
        result = {
            "guidance_system": discovery.cache.guidance_system.to_dict() 
                              if discovery.cache.guidance_system else None,
            "documents": [doc.to_dict() for doc in docs]
        }
        return json.dumps(result, indent=2)
    else:
        lines = []
        cache_age = ""
        if discovery.cache.last_updated:
            age = datetime.now() - discovery.cache.last_updated
            cache_age = f" (cached {age.days}d {age.seconds//3600}h ago)"
        
        lines.append(f"Document Cache{cache_age}:")
        lines.append(f"Found {len(docs)} documents")
        
        if discovery.cache.guidance_system:
            gs = discovery.cache.guidance_system
            lines.append(f"\nAI Guidance: {gs.system_type} (confidence: {gs.confidence:.2f})")
        
        # Group by type
        docs_by_type = {}
        for doc in docs:
            if doc.doc_type not in docs_by_type:
                docs_by_type[doc.doc_type] = []
            docs_by_type[doc.doc_type].append(doc)
        
        for doc_type, type_docs in docs_by_type.items():
            lines.append(f"\n{doc_type.capitalize()} documents ({len(type_docs)}):")
            for doc in type_docs:
                lines.append(f"- {doc.path}")
                if doc.title:
                    lines.append(f"  Title: {doc.title}")
                if doc.summary:
                    lines.append(f"  Summary: {doc.summary}")
        
        return "\n".join(lines)


class LearningProfile:
    """Represents a learning profile for document structure detection."""
    
    def __init__(self, name: str, project_id: Optional[str] = None):
        self.name = name
        self.project_id = project_id or self._generate_project_id()
        self.created = datetime.now()
        self.last_updated = self.created
        self.document_patterns = {}
        self.filename_patterns = {}
        self.directory_patterns = {}
        self.keyword_patterns = {}
        self.confidence_thresholds = {}
        self.custom_document_types = []
        
    def _generate_project_id(self) -> str:
        """Generate a unique project ID based on current directory structure."""
        try:
            # Use the current directory name and some metadata to create a unique ID
            current_dir = os.path.basename(os.getcwd())
            timestamp = datetime.now().isoformat()
            unique_string = f"{current_dir}_{timestamp}"
            return hashlib.md5(unique_string.encode()).hexdigest()[:10]
        except Exception as e:
            logger.error(f"Failed to generate project ID: {e}")
            return f"project_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def learn_from_documents(self, documents: List[Document]) -> None:
        """Learn patterns from a list of classified documents."""
        if not documents:
            return
            
        self.last_updated = datetime.now()
        
        # Learn filename patterns
        for doc in documents:
            if doc.doc_type != DocumentType.UNKNOWN and doc.confidence > 0.7:
                # Extract filename without extension
                filename = doc.path.name
                stem = doc.path.stem
                
                # Store as pattern for this document type
                if doc.doc_type not in self.filename_patterns:
                    self.filename_patterns[doc.doc_type] = []
                
                # Add regex pattern based on the filename
                if filename.startswith(stem.upper()):
                    # For uppercase filenames (like README.md), create a pattern
                    pattern = f"^{stem.upper()}\\.(md|txt|rst)$"
                    if pattern not in self.filename_patterns[doc.doc_type]:
                        self.filename_patterns[doc.doc_type].append(pattern)
                else:
                    # For other naming patterns, create a more specific regex
                    # For example, convert "project-requirements.md" to ".*requirements\\.(md|txt|rst)$"
                    words = re.findall(r'[a-zA-Z]+', stem)
                    if len(words) > 1:
                        for word in words:
                            if len(word) > 3:  # Only use meaningful words
                                pattern = f".*{word.lower()}\\.(md|txt|rst)$"
                                if pattern not in self.filename_patterns[doc.doc_type]:
                                    self.filename_patterns[doc.doc_type].append(pattern)
        
        # Learn directory patterns
        for doc in documents:
            if doc.doc_type != DocumentType.UNKNOWN and doc.confidence > 0.7:
                parent_dir = str(doc.path.parent.name).lower()
                if parent_dir and parent_dir not in [".", ""]:
                    if doc.doc_type not in self.directory_patterns:
                        self.directory_patterns[doc.doc_type] = []
                    
                    if parent_dir not in self.directory_patterns[doc.doc_type]:
                        self.directory_patterns[doc.doc_type].append(parent_dir)
        
        # Learn keyword patterns from document content
        for doc in documents:
            if doc.doc_type != DocumentType.UNKNOWN and doc.confidence > 0.8:
                content = doc.read_content()
                if content:
                    if doc.doc_type not in self.keyword_patterns:
                        self.keyword_patterns[doc.doc_type] = {}
                    
                    # Extract key phrases based on document type
                    if doc.doc_type == DocumentType.REQUIREMENTS:
                        keywords = ["requirement", "feature", "user story", "acceptance criteria", "product", "milestone"]
                    elif doc.doc_type == DocumentType.ARCHITECTURE:
                        keywords = ["architecture", "component", "service", "interface", "design", "system", "diagram"]
                    elif doc.doc_type == DocumentType.API:
                        keywords = ["api", "endpoint", "request", "response", "parameter", "authentication"]
                    else:
                        keywords = []
                    
                    # Count occurrences of each keyword
                    for keyword in keywords:
                        count = len(re.findall(rf'\b{keyword}\b', content.lower()))
                        if count > 0:
                            self.keyword_patterns[doc.doc_type][keyword] = self.keyword_patterns[doc.doc_type].get(keyword, 0) + count
    
    def apply_to_document(self, document: Document) -> Tuple[str, float]:
        """Apply the learning profile to classify a document.
        
        Returns:
            Tuple[str, float]: (document_type, confidence)
        """
        if document.doc_type != DocumentType.UNKNOWN and document.confidence > 0.8:
            # Document already has high confidence classification
            return document.doc_type, document.confidence
        
        best_type = DocumentType.UNKNOWN
        best_confidence = 0.0
        
        # Check filename patterns
        filename = document.path.name
        for doc_type, patterns in self.filename_patterns.items():
            for pattern in patterns:
                if re.match(pattern, filename, re.IGNORECASE):
                    confidence = 0.7  # Base confidence for filename match
                    if confidence > best_confidence:
                        best_type = doc_type
                        best_confidence = confidence
        
        # Check directory patterns
        parent_dir = str(document.path.parent.name).lower()
        for doc_type, directories in self.directory_patterns.items():
            if parent_dir in directories:
                confidence = 0.5  # Base confidence for directory match
                if confidence > best_confidence:
                    best_type = doc_type
                    best_confidence = confidence
        
        # Check content patterns if we haven't got a high confidence yet
        if best_confidence < 0.8 and document.path.suffix.lower() in ['.md', '.txt', '.rst']:
            try:
                content = document.read_content().lower()
                
                # Check for keyword matches
                for doc_type, keywords in self.keyword_patterns.items():
                    matches = 0
                    total_keywords = 0
                    
                    for keyword, expected_count in keywords.items():
                        count = len(re.findall(rf'\b{keyword}\b', content))
                        if count > 0:
                            matches += min(count, expected_count)
                        total_keywords += expected_count
                    
                    if total_keywords > 0:
                        confidence = 0.4 + (0.4 * (matches / total_keywords))
                        if confidence > best_confidence:
                            best_type = doc_type
                            best_confidence = confidence
            except Exception as e:
                logger.warning(f"Failed to analyze content for {document.path}: {e}")
        
        return best_type, best_confidence
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert learning profile to dictionary."""
        return {
            "name": self.name,
            "project_id": self.project_id,
            "created": self.created.isoformat(),
            "last_updated": self.last_updated.isoformat(),
            "filename_patterns": self.filename_patterns,
            "directory_patterns": self.directory_patterns,
            "keyword_patterns": self.keyword_patterns,
            "confidence_thresholds": self.confidence_thresholds,
            "custom_document_types": self.custom_document_types
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LearningProfile':
        """Create a learning profile from dictionary."""
        profile = cls(data["name"], data.get("project_id"))
        profile.created = datetime.fromisoformat(data["created"])
        profile.last_updated = datetime.fromisoformat(data["last_updated"])
        profile.filename_patterns = data["filename_patterns"]
        profile.directory_patterns = data["directory_patterns"]
        profile.keyword_patterns = data["keyword_patterns"]
        profile.confidence_thresholds = data.get("confidence_thresholds", {})
        profile.custom_document_types = data.get("custom_document_types", [])
        return profile
    
    def save(self, directory: Optional[Union[str, Path]] = None) -> str:
        """Save the learning profile to a file.
        
        Args:
            directory: Directory to save the profile in (defaults to .focus_forge/learning)
            
        Returns:
            str: Path to the saved profile file
        """
        if directory is None:
            directory = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
        
        os.makedirs(directory, exist_ok=True)
        
        filename = f"{self.name.lower().replace(' ', '_')}_{self.project_id}.json"
        filepath = os.path.join(directory, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2)
        
        return filepath
    
    @classmethod
    def load(cls, filepath: Union[str, Path]) -> 'LearningProfile':
        """Load a learning profile from a file."""
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return cls.from_dict(data)
    
    @classmethod
    def list_profiles(cls, directory: Optional[Union[str, Path]] = None) -> List[str]:
        """List available learning profiles."""
        if directory is None:
            directory = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
        
        if not os.path.exists(directory):
            return []
        
        return [f for f in os.listdir(directory) if f.endswith('.json')] 