#!/usr/bin/env python3
"""
Command-line utility for analyzing PRD files for priority and complexity.

This script uses the Priority and Complexity Inference Engine to analyze
PRD files and generate reports on task priorities and complexities.
"""
import os
import sys
import argparse
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the priority inference engine
try:
    from priority_inference import PriorityInferenceEngine, analyze_text_file
except ImportError:
    # If running from the same directory, adjust the path
    sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
    from priority_inference import PriorityInferenceEngine, analyze_text_file


def analyze_prd_file(filepath: str, output_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Analyze a PRD file and generate a priority/complexity report.
    
    Args:
        filepath: Path to the PRD file
        output_file: Path to save the report (optional)
        
    Returns:
        Dictionary containing analysis results
    """
    try:
        # Check if file exists
        if not os.path.isfile(filepath):
            logger.error(f"File not found: {filepath}")
            return {"error": "File not found"}
        
        # Read the file content
        with open(filepath, 'r') as f:
            prd_text = f.read()
        
        # Create the inference engine
        engine = PriorityInferenceEngine()
        
        # Analyze the PRD text
        results = engine.analyze_prd_text(prd_text)
        
        # Add summary statistics
        summary = {
            "high_priority_count": len(results["high_priority"]),
            "medium_priority_count": len(results["medium_priority"]),
            "low_priority_count": len(results["low_priority"]),
            "total_sections": (
                len(results["high_priority"]) + 
                len(results["medium_priority"]) + 
                len(results["low_priority"])
            )
        }
        
        # Add complexity breakdown
        complexity_counts = {"high": 0, "medium": 0, "low": 0}
        
        for priority_level in ["high_priority", "medium_priority", "low_priority"]:
            for item in results[priority_level]:
                complexity = item["analysis"]["complexity"]
                complexity_counts[complexity] += 1
        
        summary["complexity_counts"] = complexity_counts
        
        # Add the summary to the results
        results["summary"] = summary
        
        # Save to output file if specified
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Analysis saved to {output_file}")
        
        return results
    
    except Exception as e:
        logger.error(f"Error analyzing PRD file: {str(e)}")
        return {"error": str(e)}


def generate_text_report(results: Dict[str, Any], include_sections: bool = True) -> str:
    """
    Generate a text report from analysis results.
    
    Args:
        results: Analysis results dictionary
        include_sections: Whether to include section details
        
    Returns:
        Formatted text report
    """
    if "error" in results:
        return f"Error: {results['error']}"
    
    summary = results.get("summary", {})
    
    # Build the report
    report = [
        "=== PRD Priority and Complexity Analysis ===",
        "",
        "Priority Summary:",
        f"  High Priority: {summary.get('high_priority_count', 0)} section(s)",
        f"  Medium Priority: {summary.get('medium_priority_count', 0)} section(s)",
        f"  Low Priority: {summary.get('low_priority_count', 0)} section(s)",
        f"  Total Sections: {summary.get('total_sections', 0)}",
        "",
        "Complexity Breakdown:",
    ]
    
    complexity_counts = summary.get("complexity_counts", {})
    report.extend([
        f"  High Complexity: {complexity_counts.get('high', 0)} section(s)",
        f"  Medium Complexity: {complexity_counts.get('medium', 0)} section(s)",
        f"  Low Complexity: {complexity_counts.get('low', 0)} section(s)",
        ""
    ])
    
    if include_sections:
        # Add high priority sections
        if results.get("high_priority"):
            report.append("HIGH PRIORITY SECTIONS:")
            report.append("----------------------")
            for i, item in enumerate(results["high_priority"], 1):
                text = item["text"]
                complexity = item["analysis"]["complexity"]
                # Truncate long sections
                if len(text) > 100:
                    text = text[:97] + "..."
                report.append(f"{i}. [{complexity.upper()}] {text}")
            report.append("")
        
        # Add medium priority sections
        if results.get("medium_priority"):
            report.append("MEDIUM PRIORITY SECTIONS:")
            report.append("------------------------")
            for i, item in enumerate(results["medium_priority"], 1):
                text = item["text"]
                complexity = item["analysis"]["complexity"]
                # Truncate long sections
                if len(text) > 100:
                    text = text[:97] + "..."
                report.append(f"{i}. [{complexity.upper()}] {text}")
            report.append("")
        
        # Add low priority sections if there are any
        if results.get("low_priority"):
            report.append("LOW PRIORITY SECTIONS:")
            report.append("---------------------")
            for i, item in enumerate(results["low_priority"], 1):
                text = item["text"]
                complexity = item["analysis"]["complexity"]
                # Truncate long sections
                if len(text) > 100:
                    text = text[:97] + "..."
                report.append(f"{i}. [{complexity.upper()}] {text}")
    
    return "\n".join(report)


def main():
    """Main function to parse arguments and run the analysis."""
    parser = argparse.ArgumentParser(
        description="Analyze PRD files for priority and complexity."
    )
    parser.add_argument(
        "input_file",
        help="Path to the PRD file to analyze"
    )
    parser.add_argument(
        "-o", "--output",
        help="Path to save JSON output file (optional)"
    )
    parser.add_argument(
        "-r", "--report",
        help="Path to save text report (optional)"
    )
    parser.add_argument(
        "-s", "--summary",
        action="store_true",
        help="Only show summary statistics, not individual sections"
    )
    
    args = parser.parse_args()
    
    # Analyze the PRD file
    results = analyze_prd_file(args.input_file, args.output)
    
    # Generate text report
    report = generate_text_report(results, not args.summary)
    
    # Save report to file if specified
    if args.report:
        with open(args.report, 'w') as f:
            f.write(report)
        print(f"Report saved to {args.report}")
    else:
        # Print report to console
        print(report)


if __name__ == "__main__":
    main() 