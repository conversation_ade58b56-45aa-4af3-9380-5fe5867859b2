# Task Format Upgrader System

The Task Format Upgrader System provides functionality to ensure backward compatibility as Focus Forge evolves. It allows seamless upgrading of task data between different format versions.

## Architecture Overview

```mermaid
flowchart TB
    subgraph Registry["Version Registry"]
        SV["Schema Versions"] 
        V["Validators"]
        U["Upgraders"]
    end
    
    subgraph TaskUpgrader["TaskUpgrader Class"]
        DV["detect_version()"]
        VAL["validate()"]
        UP["upgrade()"]
        BU["backup_tasks_file()"]
        REC["record_upgrade()"]
        RES["restore_from_backup()"]
    end
    
    subgraph CLI["CLI Interface"]
        CUP["upgrade-tasks"]
        CAUP["auto-upgrade-tasks"]
        CVAL["validate-tasks"]
        CLIST["list-versions"]
        CPTH["check-upgrade-path"]
        CHIST["upgrade-history"]
        CBAK["backup-tasks"]
    end
    
    TaskUpgrader -- uses --> Registry
    CLI -- calls --> TaskUpgrader
    
    USER[fa:fa-user User] -- interacts --> CLI
    APP[fa:fa-cog Application] -- calls --> TaskUpgrader
```

## Features

- **Version Detection**: Automatically identifies the version of task data structures
- **Validation**: Validates task data against version-specific schemas
- **Step-by-Step Migration**: Applies sequential migrations between consecutive versions
- **Automatic Backups**: Creates backups before upgrading task data
- **CLI Interface**: Provides command-line tools for upgrading, validating, and backing up tasks
- **Auto-Upgrade Prompting**: Detects version mismatches and offers to upgrade automatically
- **Upgrade History**: Records all version changes for tracking and auditing
- **Backup Management**: Manages backup files with options to restore if needed
- **Configurable Upgrade Behavior**: Configure to prompt, automatically upgrade, or never upgrade

## Upgrade Workflow

```mermaid
flowchart TD
    A[Load Tasks] --> B{Detect Version}
    B -->|Current Version Detected| C{Version Match?}
    C -->|Yes| D[Use Tasks As-Is]
    C -->|No| E{Upgrade Policy?}
    
    E -->|prompt| F[Show Prompt]
    F -->|User Accepts| G[Create Backup]
    F -->|User Declines| D
    
    E -->|auto| G
    E -->|never| D
    
    G --> H[Determine Upgrade Path]
    H --> I[Apply Upgraders Sequentially]
    I --> J[Validate Result]
    J -->|Valid| K[Save Upgraded Tasks]
    J -->|Invalid| L[Log Error, Use Original]
    
    K --> M[Record in History]
    L --> D
    M --> D
```

## Version Management

```mermaid
graph LR
    subgraph Versions
        V070["0.7.0"] --> V080["0.8.0"]
        V080 --> V090["0.9.0 (Future)"]
    end
    
    subgraph Validators
        VAL070["validate_v0_7_0()"]
        VAL080["validate_v0_8_0()"]
        VAL090["validate_v0_9_0()"]
    end
    
    subgraph Upgraders
        UPG070to080["upgrade_v0_7_0_to_v0_8_0()"]
        UPG080to090["upgrade_v0_8_0_to_v0_9_0()"]
    end
    
    V070 --- VAL070
    V080 --- VAL080
    V090 --- VAL090
    
    V070 --- UPG070to080 --- V080
    V080 --- UPG080to090 --- V090
```

## Usage

### Configuration

The upgrader system behavior can be configured using the `upgrader_behavior` setting in your configuration:

- `prompt` (default): Prompt the user when a version mismatch is detected
- `auto`: Automatically upgrade without prompting
- `never`: Never automatically upgrade

```bash
# Set to auto-upgrade silently
export UPGRADER_BEHAVIOR=auto

# Set to never automatically upgrade
export UPGRADER_BEHAVIOR=never
```

### Command Line Interface

The task upgrader provides several CLI commands:

```bash
# Upgrade tasks to the latest version (with prompt)
./focus_forge.py upgrade-tasks

# Upgrade to a specific version
./focus_forge.py upgrade-tasks --target-version=0.8.0

# Skip creating a backup when upgrading
./focus_forge.py upgrade-tasks --skip-backup

# Silently upgrade without prompting
./focus_forge.py auto-upgrade-tasks

# Validate tasks against their current version
./focus_forge.py validate-tasks

# Validate against a specific version
./focus_forge.py validate-tasks --version=0.7.0

# List all registered schema versions
./focus_forge.py list-versions

# Check the upgrade path between versions
./focus_forge.py check-upgrade-path 0.7.0 --to-version=0.8.0

# Show schema differences between versions
./focus_forge.py show-schema-diff 0.7.0 0.8.0

# Create a backup of the tasks file
./focus_forge.py backup-tasks

# Detect the version of the tasks file
./focus_forge.py detect-version

# View upgrade history
./focus_forge.py upgrade-history

# View upgrade history in JSON format
./focus_forge.py upgrade-history --json

# List available backup files
./focus_forge.py list-backups

# Restore from a backup file
./focus_forge.py restore-backup <backup_path>

# Restore from a backup by its ID in the upgrade history
./focus_forge.py restore-by-id <backup_id>
```

### Backup Management

The system automatically creates backups before upgrading:

```
$ ./focus_forge.py list-backups
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Filename                   ┃ Timestamp           ┃ Version ┃ Path                                    ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ tasks.20250325_115846.back │ 2025-03-25 11:58:46 │ 0.7.0   │ /home/<USER>/focus_forge/tasks.20250325_ │
│ up                         │                     │         │ 115846.backup                           │
└─────────────────────────────┴─────────────────────┴─────────┴────────────────────────────────────────┘
```

If you need to restore from a backup:

```bash
# Restore from a specific backup file
./focus_forge.py restore-backup tasks.20250325_115846.backup

# Skip confirmation with -y
./focus_forge.py restore-backup tasks.20250325_115846.backup -y
```

### Upgrade History

The system keeps a record of all upgrades performed:

```
$ ./focus_forge.py upgrade-history
┏━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ ID     ┃ Timestamp           ┃ From   ┃ To    ┃ Backup Path                                       ┃
┡━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ 7ab4c23f │ 2025-03-25 11:58:46 │ 0.7.0  │ 0.8.0 │ /home/<USER>/focus_forge/tasks.20250325_115846.backup │
└──────────┴─────────────────────┴────────┴───────┴───────────────────────────────────────────────────┘
```

You can use the upgrade ID to restore to that specific backup:

```bash
./focus_forge.py restore-by-id 7ab4c23f
```

### Automatic Upgrades

When loading tasks, Focus Forge automatically detects if the tasks file version differs from the application version. Depending on your configuration:

1. **Prompt Mode** (default): You'll be asked whether to upgrade
   ```
   Notice: Tasks file version (0.7.0) is older than app version (0.8.0).
   Would you like to upgrade the tasks file? [y/N]
   ```

2. **Auto Mode**: Automatically upgrades without prompting
   ```
   Notice: Auto-upgrading tasks from version 0.7.0 to 0.8.0
   Success: Successfully upgraded from version 0.7.0 to 0.8.0
   ```

3. **Never Mode**: No upgrades will be performed automatically

## Extending the System

### Adding a New Version

To add support for a new version:

1. Register the schema version:
   ```python
   register_schema_version("0.9.0")
   ```

2. Create a validator for the new version:
   ```python
   def validate_v0_9_0(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
       # Implement validation logic
       return True, []
   
   register_validator("0.9.0", validate_v0_9_0)
   ```

3. Create upgraders from previous versions:
   ```python
   def upgrade_v0_8_0_to_v0_9_0(data: Dict[str, Any]) -> Dict[str, Any]:
       # Implement upgrade logic
       return upgraded_data
   
   register_upgrader("0.8.0", "0.9.0", upgrade_v0_8_0_to_v0_9_0)
   ```

## Architecture

The upgrader system consists of several key components:

- **Registry System**: Maintains lists of versions, validators, and upgraders
- **TaskUpgrader Class**: Core logic for detecting versions and performing upgrades
- **CLI Interface**: User-friendly commands for managing task data
- **Auto-Upgrade Integration**: Seamlessly integrates with task loading
- **Backup Management**: Tracks and manages backup files for safe rollbacks
- **History Tracking**: Records all version changes for auditing purposes

## Error Handling

The system includes robust error handling to prevent data loss:

- Backups are created before any upgrade operation
- Original data is preserved if validation fails
- Detailed error messages help diagnose issues
- Graceful fallback to original data if auto-upgrade fails
- Ability to restore from backups if anything goes wrong

## Version History

- **0.7.0**: Initial version with basic task structure
- **0.8.0**: Added task format upgrader system with version tracking and backup management 