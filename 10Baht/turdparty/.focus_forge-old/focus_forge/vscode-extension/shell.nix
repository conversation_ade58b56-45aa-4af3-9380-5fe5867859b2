{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    nodejs
    nodePackages.npm
    nodePackages.typescript
    nodePackages.vsce
    xvfb-run
  ];
  
  shellHook = ''
    echo "Focus Forge VS Code Extension Development Environment"
    echo "Run 'npm install' to install dependencies"
    echo "Run 'npm test' to run tests"
    echo "Run 'npm run compile' to compile the extension"
  '';
} 