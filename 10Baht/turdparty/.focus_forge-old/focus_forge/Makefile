.PHONY: test lint format coverage install dev-install clean help

help:
	@echo "Focus Forge Development Commands"
	@echo "--------------------------------"
	@echo "make test          Run all tests"
	@echo "make lint          Run linting checks"
	@echo "make format        Format code with ruff"
	@echo "make coverage      Run tests with coverage report"
	@echo "make install       Install focus_forge"
	@echo "make dev-install   Install focus_forge with development dependencies"
	@echo "make clean         Clean up build artifacts"

test:
	python -m pytest

lint:
	ruff check .

format:
	ruff format .

coverage:
	python -m pytest --cov=focus_forge --cov-report=term-missing --cov-report=html

install:
	pip install .

dev-install:
	pip install -e ".[dev]"

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf coverage.xml
	find . -name "__pycache__" -exec rm -rf {} +
	find . -name "*.pyc" -delete 