{"name": "Focus Forge Development", "dockerComposeFile": "docker-compose.yml", "service": "app", "workspaceFolder": "/workspace", "customizations": {"vscode": {"settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.ruffEnabled": true, "python.formatting.provider": "none", "python.formatting.blackEnabled": false, "python.formatting.ruffEnabled": true, "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "python.testing.pytestArgs": ["tests"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true, "source.fixAll": true}}}, "extensions": ["ms-python.python", "ms-python.vscode-pylance", "charliermarsh.ruff", "njpwerner.autodocstring"]}}, "postCreateCommand": "pip install --no-cache-dir -e '.[dev]' && pre-commit install", "remoteUser": "vscode"}