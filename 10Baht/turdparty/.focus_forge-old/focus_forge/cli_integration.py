#!/usr/bin/env python3
"""
Command-line interface for integrating PRD parsing with the task management system.

This module provides commands for:
1. Parsing PRDs and generating tasks
2. Integrating with existing tasks
3. Validating tasks against PRDs
"""
import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

# Import integration module
try:
    from focus_forge.task_management_integration import (
        parse_prd_to_tasks,
        integrate_prd_with_existing,
        validate_tasks_with_prd
    )
except ImportError:
    # When running directly from source directory
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from focus_forge.task_management_integration import (
        parse_prd_to_tasks,
        integrate_prd_with_existing,
        validate_tasks_with_prd
    )

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("integration.log")
    ]
)
logger = logging.getLogger(__name__)


def load_config(config_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Load configuration from a file.
    
    Args:
        config_file: Path to config file (optional)
        
    Returns:
        Dict[str, Any]: Configuration dictionary
    """
    default_config = {
        "task_generation": {
            "default_priority": "medium",
            "generate_subtasks": True,
            "max_subtasks_per_task": 3
        },
        "task_validation": {
            "validate_dependencies": True,
            "validate_coverage": True,
            "require_test_strategy": True
        },
        "confidence_scoring": {
            "scoring_weights": {
                "clarity": 0.3,
                "coverage": 0.5,
                "complexity": 0.2
            }
        }
    }
    
    if not config_file:
        return default_config
    
    try:
        with open(config_file, 'r') as f:
            user_config = json.load(f)
        
        # Merge with default config
        for section in default_config:
            if section in user_config:
                default_config[section].update(user_config[section])
        
        # Add any new sections
        for section in user_config:
            if section not in default_config:
                default_config[section] = user_config[section]
        
        return default_config
    
    except Exception as e:
        logger.error(f"Error loading config file {config_file}: {str(e)}")
        return default_config


def print_report(report: Dict[str, Any], verbose: bool = False) -> None:
    """
    Print a formatted report to the console.
    
    Args:
        report: Report dictionary
        verbose: Whether to print verbose details
    """
    if report.get("status") == "error":
        print(f"ERROR: {report.get('error_message', 'Unknown error')}")
        return
    
    # Print basic info
    print("\n======= Integration Report =======")
    print(f"Status: {report.get('status', 'unknown')}")
    
    # Print task counts if available
    if "new_tasks" in report:
        print(f"New tasks: {report['new_tasks']}")
    if "existing_tasks" in report:
        print(f"Existing tasks: {report['existing_tasks']}")
    if "merged_tasks" in report:
        print(f"Total merged tasks: {report['merged_tasks']}")
    
    # Print output file if available
    if report.get("output_file"):
        print(f"Output saved to: {report['output_file']}")
    
    # Print validation info if available
    validation = report.get("validation", {})
    if validation:
        print("\n--- Validation Results ---")
        print(f"Valid tasks: {validation.get('tasks_valid', False)}")
        
        if "confidence_score" in validation:
            print(f"Confidence score: {validation['confidence_score']:.2f}")
        if "clarity_score" in validation:
            print(f"Clarity score: {validation['clarity_score']:.2f}")
        if "coverage_score" in validation:
            print(f"Coverage score: {validation['coverage_score']:.2f}")
        
        # Print issues if verbose
        if verbose:
            ambiguous = validation.get("ambiguous_requirements", {}).get("items", [])
            if ambiguous:
                print(f"\nAmbiguous requirements: {len(ambiguous)}")
                for i, req in enumerate(ambiguous[:3], 1):  # Show at most 3
                    print(f"  {i}. {req.get('requirement', '')}")
                if len(ambiguous) > 3:
                    print(f"  ... and {len(ambiguous) - 3} more")
            
            missing = validation.get("missing_requirements", {}).get("items", [])
            if missing:
                print(f"\nPotentially missing requirements: {len(missing)}")
                for i, req in enumerate(missing[:3], 1):  # Show at most 3
                    print(f"  {i}. {req.get('requirement', '')}")
                if len(missing) > 3:
                    print(f"  ... and {len(missing) - 3} more")
    
    print("\n==================================")


def cmd_parse(args: argparse.Namespace) -> None:
    """
    Handle the parse command.
    
    Args:
        args: Parsed command-line arguments
    """
    try:
        # Load config
        config = load_config(args.config)
        
        # Parse PRD to tasks
        tasks = parse_prd_to_tasks(args.prd_file, config)
        
        # Save to output file
        output_data = {
            "version": "1.0",
            "prd_file": args.prd_file,
            "generated_at": str(Path(args.prd_file).stat().st_mtime),
            "tasks": tasks
        }
        
        with open(args.output, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"Successfully parsed PRD and generated {len(tasks)} tasks.")
        print(f"Tasks saved to {args.output}")
        
    except Exception as e:
        logger.error(f"Error in parse command: {str(e)}")
        print(f"ERROR: {str(e)}")


def cmd_integrate(args: argparse.Namespace) -> None:
    """
    Handle the integrate command.
    
    Args:
        args: Parsed command-line arguments
    """
    try:
        # Load config
        config = load_config(args.config)
        
        # Integrate PRD with existing tasks
        report = integrate_prd_with_existing(
            args.prd_file,
            args.tasks_file,
            args.output,
            config
        )
        
        # Print report
        print_report(report, args.verbose)
        
    except Exception as e:
        logger.error(f"Error in integrate command: {str(e)}")
        print(f"ERROR: {str(e)}")


def cmd_validate(args: argparse.Namespace) -> None:
    """
    Handle the validate command.
    
    Args:
        args: Parsed command-line arguments
    """
    try:
        # Load config
        config = load_config(args.config)
        
        # Validate tasks against PRD
        report = validate_tasks_with_prd(
            args.tasks_file,
            args.prd_file,
            config
        )
        
        # Print report
        print_report(report, args.verbose)
        
        # Save report to file if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"Validation report saved to {args.output}")
        
        # Exit with error code if validation failed and --fail flag is set
        if args.fail and (not report.get("tasks_valid", False) or report.get("status") == "error"):
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"Error in validate command: {str(e)}")
        print(f"ERROR: {str(e)}")
        if args.fail:
            sys.exit(1)


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Integration tools for PRD parsing and task management"
    )
    
    # Global arguments
    parser.add_argument(
        "--config",
        help="Path to configuration file"
    )
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(
        dest="command",
        help="Command to execute"
    )
    subparsers.required = True
    
    # Parse command
    parse_parser = subparsers.add_parser(
        "parse",
        help="Parse a PRD and generate tasks"
    )
    parse_parser.add_argument(
        "prd_file",
        help="Path to PRD file"
    )
    parse_parser.add_argument(
        "-o", "--output",
        default="generated_tasks.json",
        help="Path to save generated tasks (default: generated_tasks.json)"
    )
    parse_parser.set_defaults(func=cmd_parse)
    
    # Integrate command
    integrate_parser = subparsers.add_parser(
        "integrate",
        help="Integrate a PRD with existing tasks"
    )
    integrate_parser.add_argument(
        "prd_file",
        help="Path to PRD file"
    )
    integrate_parser.add_argument(
        "tasks_file",
        help="Path to existing tasks file"
    )
    integrate_parser.add_argument(
        "-o", "--output",
        help="Path to save merged tasks (optional)"
    )
    integrate_parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Print verbose output"
    )
    integrate_parser.set_defaults(func=cmd_integrate)
    
    # Validate command
    validate_parser = subparsers.add_parser(
        "validate",
        help="Validate tasks against a PRD"
    )
    validate_parser.add_argument(
        "tasks_file",
        help="Path to tasks file"
    )
    validate_parser.add_argument(
        "prd_file",
        help="Path to PRD file"
    )
    validate_parser.add_argument(
        "-o", "--output",
        help="Path to save validation report (optional)"
    )
    validate_parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Print verbose output"
    )
    validate_parser.add_argument(
        "--fail",
        action="store_true",
        help="Exit with error code if validation fails"
    )
    validate_parser.set_defaults(func=cmd_validate)
    
    return parser.parse_args()


def main() -> None:
    """Main entry point for the CLI."""
    args = parse_args()
    args.func(args)


if __name__ == "__main__":
    main() 