{"description": "Test data for priority and complexity inference engine evaluation", "version": "1.0", "examples": [{"id": "1", "text": "Implement secure authentication system with multi-factor authentication. This is a critical security feature that must be implemented immediately.", "priority": "high", "complexity": "high"}, {"id": "2", "text": "Fix critical security vulnerability in user authentication that exposes user data.", "priority": "high", "complexity": "medium"}, {"id": "3", "text": "Optimize database queries to improve performance by 50%. This is a high impact change affecting all users.", "priority": "high", "complexity": "medium"}, {"id": "4", "text": "Update the UI components to match the new brand guidelines. This change is important but not critical.", "priority": "medium", "complexity": "medium"}, {"id": "5", "text": "Add data export functionality to the admin dashboard. This feature was requested by several customers.", "priority": "medium", "complexity": "medium"}, {"id": "6", "text": "Document the API endpoints for external developers. This should be completed in the next few weeks.", "priority": "medium", "complexity": "low"}, {"id": "7", "text": "Add a dark mode theme option to the UI. This is a nice to have feature that could be implemented when possible.", "priority": "low", "complexity": "low"}, {"id": "8", "text": "Refactor the internal logging system. This is a low priority task with minimal user impact.", "priority": "low", "complexity": "medium"}, {"id": "9", "text": "Build a comprehensive analytics dashboard with integration to multiple data sources, real-time updates, and custom visualization options.", "priority": "medium", "complexity": "high"}, {"id": "10", "text": "Replace the current payment processor with a new service provider. This is urgent as the current provider will cease operations next month.", "priority": "high", "complexity": "high"}, {"id": "11", "text": "Fix minor typos in the documentation. This can be done whenever time permits.", "priority": "low", "complexity": "low"}, {"id": "12", "text": "Implement the shopping cart functionality according to the specifications. This is an important core feature.", "priority": "medium", "complexity": "medium"}, {"id": "13", "text": "This is a critical blocker with severe consequences if not addressed immediately. The bug affects all users and causes data loss.", "priority": "high", "complexity": "medium"}, {"id": "14", "text": "This feature would be nice to have but is not essential. It adds some convenience for power users.", "priority": "low", "complexity": "medium"}, {"id": "15", "text": "This complex integration requires coordination with multiple external systems and significant architectural changes.", "priority": "medium", "complexity": "high"}, {"id": "16", "text": "The system must comply with new regulations by the end of the month. Non-compliance will result in significant penalties.", "priority": "high", "complexity": "medium"}, {"id": "17", "text": "The user profile page needs a refresh to match the new design system. This is a standard update with moderate importance.", "priority": "medium", "complexity": "low"}, {"id": "18", "text": "A simple cosmetic change to the footer links. This has minimal impact on user experience.", "priority": "low", "complexity": "low"}, {"id": "19", "text": "Improve error messages across the application to be more user-friendly. This should be done within the next quarter.", "priority": "medium", "complexity": "medium"}, {"id": "20", "text": "Critical performance issue affecting checkout process. Users are abandoning purchases due to slow response times.", "priority": "high", "complexity": "medium"}, {"id": "21", "text": "Implement a completely new machine learning-based recommendation engine. This requires extensive research, new infrastructure, and specialized knowledge.", "priority": "medium", "complexity": "high"}, {"id": "22", "text": "Update the copyright year in the website footer. This is a trivial change with no functional impact.", "priority": "low", "complexity": "low"}, {"id": "23", "text": "Urgent security patch needed for recently discovered vulnerability. Exploit is being actively used in the wild.", "priority": "high", "complexity": "medium"}, {"id": "24", "text": "Add internationalization support for five new languages. This is important for expanding into new markets next year.", "priority": "medium", "complexity": "high"}, {"id": "25", "text": "Implement a comprehensive logging and monitoring solution with real-time alerts and visualization dashboards.", "priority": "medium", "complexity": "high"}, {"id": "26", "text": "Extremely critical security vulnerability affecting all customer data. Must be patched immediately.", "priority": "high", "complexity": "high"}, {"id": "27", "text": "Minor enhancement to the search functionality to improve result relevance slightly.", "priority": "low", "complexity": "medium"}, {"id": "28", "text": "Add a simple contact form to the website. This is a basic feature with minimal complexity.", "priority": "medium", "complexity": "low"}, {"id": "29", "text": "This is a somewhat important feature with moderate complexity that should be implemented in the next few months.", "priority": "medium", "complexity": "medium"}, {"id": "30", "text": "This task is a critical blocker for the upcoming release and must be completed immediately, but it's technically straightforward.", "priority": "high", "complexity": "low"}]}