# Focus Forge Test Suite

This directory contains the test suite for the Focus Forge project. It includes unit tests, integration tests, and tests for edge cases and error handling.

## Test Structure

- `conftest.py`: Contains pytest fixtures used across all tests
- `test_utils.py`: Unit tests for utility functions
- `test_cli.py`: Tests for CLI commands
- `test_api.py`: Tests for API integration
- `test_integration.py`: End-to-end tests for complete workflows
- `test_edge_cases.py`: Tests for edge cases and error handling

## Running Tests

To run the tests, use the pytest command from the project root directory:

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_utils.py

# Run tests with a specific marker
pytest -m unit
pytest -m integration
pytest -m api
pytest -m cli

# Run tests with coverage report
pytest --cov=focus_forge --cov-report=term-missing
```

## Test Markers

- `unit`: Unit tests
- `integration`: Integration tests
- `cli`: Command-line interface tests
- `api`: API integration tests

## Adding New Tests

When adding new tests:

1. Place them in the appropriate file based on what they test
2. Add proper docstrings explaining what each test does
3. Use fixtures from `conftest.py` where appropriate
4. Add new fixtures to `conftest.py` if needed for multiple tests
5. Apply appropriate markers to categorize tests

## Mocking External Dependencies

For tests that involve external dependencies (e.g., API calls), use the `unittest.mock` library to mock responses:

```python
from unittest.mock import patch

def test_with_mock():
    with patch('focus_forge.call_claude_api', return_value='mocked response'):
        # Test code that calls the API
        # ...
```

## Test Coverage

The test suite aims to achieve high code coverage, testing:

- Basic functionality
- Edge cases
- Error handling
- Complete workflows

The coverage report can be generated using pytest-cov:

```bash
pytest --cov=focus_forge --cov-report=html
```

This will generate an HTML report in the `htmlcov` directory. 