"""CLI extensions for Focus Forge.

This module provides additional CLI commands for the Focus Forge tool
related to document discovery and AI guidance analysis.
"""

import os
import json
import logging
from pathlib import Path
from typing import Optional, List

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.markdown import Markdown

from .doc_discovery import (
    DocumentType,
    GuidanceSystemType,
    Document,
    LearningProfile,
    discover_docs,
    analyze_guidance,
    cache_docs,
    query_cache,
    DOCS_CACHE_DIR,
    LEARNING_PROFILES_DIR
)

# Configure logging
logger = logging.getLogger(__name__)

# Initialize rich console
console = Console()


@click.command("discover-docs")
@click.option("--depth", type=int, default=3, help="Maximum directory depth to search.")
@click.option("--use-cache/--no-cache", default=False, help="Use cached results if available.")
@click.option("--include-content", is_flag=True, help="Include document content in results.")
@click.option("--output", type=click.Path(), help="Output file for results (JSON format).")
def discover_docs_cmd(depth: int, use_cache: bool, include_content: bool, output: Optional[str]) -> None:
    """Discover documentation files in the project."""
    try:
        console.print("[bold green]Discovering documentation files...[/]")
        
        result = discover_docs(
            max_depth=depth,
            use_cache=use_cache,
            include_content=include_content,
            output_format="text" if not output else "json"
        )
        
        if output:
            # Save results to file
            with open(output, 'w', encoding='utf-8') as f:
                f.write(result)
            console.print(f"[green]Results saved to {output}[/]")
        else:
            # Print results to console
            if result:
                console.print(Panel(result, title="Document Discovery Results"))
            else:
                console.print("[yellow]No documentation files found.[/]")
    
    except Exception as e:
        console.print(f"[bold red]Error discovering documentation:[/] {str(e)}")


@click.command("analyze-guidance")
@click.option("--path", type=click.Path(exists=True), help="Specific path to analyze.")
@click.option("--verbose", is_flag=True, help="Include detailed information in output.")
@click.option("--output", type=click.Path(), help="Output file for results (JSON format).")
def analyze_guidance_cmd(path: Optional[str], verbose: bool, output: Optional[str]) -> None:
    """Analyze AI guidance system in the project."""
    try:
        console.print("[bold green]Analyzing AI guidance system...[/]")
        
        result = analyze_guidance(
            path=path,
            verbose=verbose
        )
        
        if output:
            # Save results to file
            with open(output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2)
            console.print(f"[green]Results saved to {output}[/]")
        else:
            # Print results to console in a nice format
            panel_title = "AI Guidance Analysis"
            
            if result["type"] == GuidanceSystemType.UNKNOWN:
                console.print(Panel(
                    "[yellow]No AI guidance system detected in this project.[/]",
                    title=panel_title
                ))
                return
            
            guidance_type = result["type"].upper()
            confidence = result["confidence"]
            completeness = result["completeness"]
            
            # Determine confidence level color
            conf_color = "green" if confidence >= 0.8 else "yellow" if confidence >= 0.5 else "red"
            
            # Create table for markers and missing items
            table = Table(show_header=True, header_style="bold")
            table.add_column("Detected Markers")
            table.add_column("Missing Elements")
            
            # Fill the table
            max_rows = max(len(result["markers"]), len(result["missing"]))
            for i in range(max_rows):
                marker = result["markers"][i] if i < len(result["markers"]) else ""
                missing = result["missing"][i] if i < len(result["missing"]) else ""
                table.add_row(
                    f"✅ {marker}" if marker else "",
                    f"❌ {missing}" if missing else ""
                )
            
            # Create document counts table if available
            doc_table = None
            if "document_counts" in result:
                doc_table = Table(show_header=True, header_style="bold")
                doc_table.add_column("Document Type")
                doc_table.add_column("Count")
                
                for doc_type, count in result["document_counts"].items():
                    doc_table.add_row(doc_type.capitalize(), str(count))
            
            # Build output
            content = [
                f"[bold]Guidance System:[/] [bold {conf_color}]{guidance_type}[/]",
                f"[bold]Confidence:[/] [bold {conf_color}]{confidence:.2f}[/]",
                f"[bold]Completeness:[/] {completeness:.2f}",
                f"[bold]Path:[/] {result['path']}",
                "",
                table
            ]
            
            if doc_table:
                content.append("")
                content.append("[bold]Document Counts:[/]")
                content.append(doc_table)
            
            console.print(Panel.fit(
                "\n".join(str(item) for item in content),
                title=panel_title
            ))
    
    except Exception as e:
        console.print(f"[bold red]Error analyzing guidance:[/] {str(e)}")


@click.command("cache-docs")
@click.option("--include-content", is_flag=True, help="Include document content in cache.")
@click.option("--force", is_flag=True, help="Force regeneration of cache.")
@click.option("--output", type=click.Path(), help="Custom output path for cache file.")
def cache_docs_cmd(include_content: bool, force: bool, output: Optional[str]) -> None:
    """Cache documentation files for faster access."""
    try:
        console.print("[bold green]Caching documentation files...[/]")
        
        cache_path = cache_docs(
            include_content=include_content,
            force=force,
            output=output
        )
        
        console.print(f"[green]Documentation cache saved to {cache_path}[/]")
    
    except Exception as e:
        console.print(f"[bold red]Error caching documentation:[/] {str(e)}")


@click.command("query-cache")
@click.option("--type", "doc_type", help="Document type to filter by.")
@click.option("--format", type=click.Choice(["text", "json"]), default="text",
              help="Output format (text or JSON).")
@click.option("--output", type=click.Path(), help="Output file for results.")
def query_cache_cmd(doc_type: Optional[str], format: str, output: Optional[str]) -> None:
    """Query the document cache."""
    try:
        result = query_cache(
            doc_type=doc_type,
            format=format
        )
        
        if output:
            # Save results to file
            with open(output, 'w', encoding='utf-8') as f:
                f.write(result if format == "json" else str(result))
            console.print(f"[green]Results saved to {output}[/]")
        else:
            # Print results to console
            if format == "json":
                console.print(result)
            else:
                console.print(Panel(result, title="Document Cache Query Results"))
    
    except Exception as e:
        console.print(f"[bold red]Error querying cache:[/] {str(e)}")


@click.command("ai-guidance-status")
@click.option("--analyze", is_flag=True, help="Perform detailed analysis.")
@click.option("--include-standard-docs", is_flag=True, help="Include standard docs in report.")
def ai_guidance_status_cmd(analyze: bool, include_standard_docs: bool) -> None:
    """Show status of AI guidance integration."""
    try:
        # Get the basic guidance status
        if analyze:
            # Perform detailed analysis
            result = analyze_guidance(verbose=include_standard_docs)
            
            guidance_type = result["type"]
            confidence = result["confidence"]
            
            # Determine status text and color
            if guidance_type == GuidanceSystemType.UNKNOWN:
                status_text = "NOT DETECTED"
                status_color = "red"
            elif confidence < 0.5:
                status_text = "LOW CONFIDENCE"
                status_color = "yellow"
            elif confidence < 0.8:
                status_text = "MEDIUM CONFIDENCE"
                status_color = "blue"
            else:
                status_text = "HIGH CONFIDENCE"
                status_color = "green"
            
            # Build the panel content
            content = [
                f"[bold]AI Guidance Status:[/] [bold {status_color}]{status_text}[/]",
                "",
                f"[bold]Guidance System:[/] {guidance_type.upper()}",
                f"[bold]Confidence:[/] {confidence:.2f}",
                f"[bold]Completeness:[/] {result['completeness']:.2f}",
            ]
            
            if result["path"]:
                content.append(f"[bold]Path:[/] {result['path']}")
            
            if result["markers"]:
                content.append("")
                content.append("[bold]Detected Markers:[/]")
                for marker in result["markers"]:
                    content.append(f"✅ {marker}")
            
            if result["missing"]:
                content.append("")
                content.append("[bold]Missing Elements:[/]")
                for missing in result["missing"]:
                    content.append(f"❌ {missing}")
            
            if include_standard_docs and "document_counts" in result:
                content.append("")
                content.append("[bold]Document Counts:[/]")
                for doc_type, count in result["document_counts"].items():
                    content.append(f"- {doc_type.capitalize()}: {count}")
            
            console.print(Panel(
                "\n".join(content),
                title="Focus Forge AI Guidance",
                border_style="blue"
            ))
            
        else:
            # Just show basic status
            from focus_forge import check_ai_guidance_folders, has_butterbot_integration
            
            has_ai, has_claude = check_ai_guidance_folders()
            has_butterbot = has_butterbot_integration()
            
            content = [
                f".ai folder: {'[green]Found[/]' if has_ai else '[red]Not found[/]'}",
                f".claude folder: {'[green]Found[/]' if has_claude else '[red]Not found[/]'}",
                f"Butterbot integration: {'[green]Available[/]' if has_butterbot else '[red]Not available[/]'}"
            ]
            
            console.print(Panel(
                "\n".join(content),
                title="Focus Forge AI Guidance",
                border_style="blue"
            ))
    
    except Exception as e:
        console.print(f"[bold red]Error getting AI guidance status:[/] {str(e)}")


@click.command("extract-doc")
@click.option("--path", required=True, type=click.Path(exists=True), help="Path to document.")
@click.option("--type", "doc_type", help="Document type (auto-detect if not specified).")
@click.option("--format", type=click.Choice(["text", "markdown", "json"]), default="markdown",
              help="Output format.")
def extract_doc_cmd(path: str, doc_type: Optional[str], format: str) -> None:
    """Extract and format a document."""
    try:
        from .doc_discovery import Document
        
        doc = Document(path, doc_type=doc_type or DocumentType.UNKNOWN)
        
        # Read content
        content = doc.read_content()
        
        # If no doc_type specified, try to classify
        if not doc_type:
            from .doc_discovery import DocDiscovery
            discovery = DocDiscovery()
            discovery._classify_document(doc)
        
        if format == "json":
            # Return as JSON
            result = doc.to_dict(include_content=True)
            console.print(json.dumps(result, indent=2))
        elif format == "markdown":
            # Format as markdown
            md_content = content
            
            # Add title if available
            if doc.title:
                title = doc.title
            else:
                title = Path(path).stem.replace("_", " ").replace("-", " ").title()
            
            console.print(Markdown(md_content))
        else:
            # Plain text
            console.print(content)
    
    except Exception as e:
        console.print(f"[bold red]Error extracting document:[/] {str(e)}")


@click.command("learn-structure")
@click.option("--from", "source_path", type=click.Path(exists=True), 
              help="Directory or file to learn from.")
@click.option("--name", required=True, help="Name for the learning profile.")
@click.option("--force", is_flag=True, help="Force overwrite if profile exists.")
def learn_structure_cmd(source_path: Optional[str], name: str, force: bool) -> None:
    """Learn document structure patterns from existing documents."""
    try:
        console.print("[bold green]Learning document structure patterns...[/]")
        
        # First discover documents to learn from
        result = discover_docs(
            project_dir=source_path,
            max_depth=5,
            use_cache=True,
            include_content=True,
            output_format="json"
        )
        
        if isinstance(result, str):
            result = json.loads(result)
        
        documents = []
        for doc_data in result.get("documents", []):
            doc = Document(doc_data["path"])
            doc.doc_type = doc_data["type"]
            doc.confidence = doc_data["confidence"]
            doc.content = doc_data.get("content", "")
            documents.append(doc)
        
        if not documents:
            console.print("[yellow]No documents found to learn from.[/]")
            return
        
        # Create learning profile directory if it doesn't exist
        profile_dir = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
        os.makedirs(profile_dir, exist_ok=True)
        
        # Check if profile already exists
        profile_files = [f for f in os.listdir(profile_dir) 
                         if f.startswith(name.lower().replace(' ', '_')) and f.endswith('.json')]
        
        if profile_files and not force:
            console.print(f"[yellow]Profile named '{name}' already exists. Use --force to overwrite.[/]")
            return
        
        # Create and learn from documents
        profile = LearningProfile(name)
        profile.learn_from_documents(documents)
        
        # Save profile
        profile_path = profile.save()
        
        # Report on what was learned
        console.print(f"[green]Learning profile saved to {profile_path}[/]")
        console.print("\n[bold]Learned patterns:[/]")
        
        # Show filename patterns
        if profile.filename_patterns:
            console.print("\n[blue]Filename patterns:[/]")
            for doc_type, patterns in profile.filename_patterns.items():
                console.print(f"  [green]{doc_type}[/]: {', '.join(patterns)}")
        
        # Show directory patterns
        if profile.directory_patterns:
            console.print("\n[blue]Directory patterns:[/]")
            for doc_type, directories in profile.directory_patterns.items():
                console.print(f"  [green]{doc_type}[/]: {', '.join(directories)}")
        
        # Show keyword patterns
        if profile.keyword_patterns:
            console.print("\n[blue]Keyword patterns:[/]")
            for doc_type, keywords in profile.keyword_patterns.items():
                keyword_info = [f"{k} ({v})" for k, v in keywords.items()]
                console.print(f"  [green]{doc_type}[/]: {', '.join(keyword_info)}")
        
    except Exception as e:
        console.print(f"[bold red]Error learning document structure:[/] {str(e)}")


@click.command("apply-learning")
@click.option("--profile", required=True, help="Name of the learning profile to apply.")
@click.option("--path", type=click.Path(exists=True), help="Directory to apply profile to.")
@click.option("--output", type=click.Path(), help="Output file for results (JSON format).")
def apply_learning_cmd(profile: str, path: Optional[str], output: Optional[str]) -> None:
    """Apply a learning profile to classify documents."""
    try:
        console.print(f"[bold green]Applying learning profile '{profile}'...[/]")
        
        # Discover documents with learning
        result = discover_docs(
            project_dir=path,
            max_depth=5,
            use_cache=False,  # Don't use cache when applying learning
            include_content=True,
            use_learning=True,
            profile_name=profile,
            output_format="json"
        )
        
        if isinstance(result, str):
            result = json.loads(result)
        
        # Display results
        if not result.get("documents"):
            console.print("[yellow]No documents found to classify.[/]")
            return
        
        # Create table of classified documents
        table = Table(title="Document Classification Results")
        table.add_column("Document", style="blue")
        table.add_column("Type", style="green")
        table.add_column("Confidence", style="yellow")
        
        for doc in result.get("documents", []):
            confidence = doc.get("confidence", 0)
            confidence_str = f"{confidence:.2f}"
            if confidence >= 0.8:
                confidence_str = f"[green]{confidence_str}[/]"
            elif confidence >= 0.6:
                confidence_str = f"[yellow]{confidence_str}[/]"
            else:
                confidence_str = f"[red]{confidence_str}[/]"
                
            table.add_row(
                doc.get("path"),
                doc.get("type", "unknown"),
                confidence_str
            )
        
        console.print(table)
        
        # Save output if requested
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2)
            console.print(f"[green]Results saved to {output}[/]")
        
    except Exception as e:
        console.print(f"[bold red]Error applying learning profile:[/] {str(e)}")


@click.command("export-learning")
@click.option("--profile", required=True, help="Name of the learning profile to export.")
@click.option("--output", required=True, type=click.Path(), help="Output file path.")
def export_learning_cmd(profile: str, output: str) -> None:
    """Export a learning profile to a file."""
    try:
        console.print(f"[bold green]Exporting learning profile '{profile}'...[/]")
        
        # Find profile
        profile_dir = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
        profile_files = [f for f in os.listdir(profile_dir) 
                         if f.startswith(profile.lower().replace(' ', '_')) and f.endswith('.json')]
        
        if not profile_files:
            console.print(f"[yellow]No learning profile found matching '{profile}'.[/]")
            return
        
        profile_path = os.path.join(profile_dir, profile_files[0])
        
        # Load profile
        with open(profile_path, 'r', encoding='utf-8') as f:
            profile_data = json.load(f)
        
        # Export to output file
        with open(output, 'w', encoding='utf-8') as f:
            json.dump(profile_data, f, indent=2)
        
        console.print(f"[green]Learning profile exported to {output}[/]")
        
    except Exception as e:
        console.print(f"[bold red]Error exporting learning profile:[/] {str(e)}")


@click.command("import-learning")
@click.option("--input", "input_file", required=True, type=click.Path(exists=True), 
              help="Input file path.")
@click.option("--name", help="Name for the imported profile (uses original name if not specified).")
@click.option("--force", is_flag=True, help="Force overwrite if profile exists.")
def import_learning_cmd(input_file: str, name: Optional[str], force: bool) -> None:
    """Import a learning profile from a file."""
    try:
        console.print(f"[bold green]Importing learning profile from {input_file}...[/]")
        
        # Load profile data
        with open(input_file, 'r', encoding='utf-8') as f:
            profile_data = json.load(f)
        
        # Create profile
        original_name = profile_data.get("name", "Imported Profile")
        profile_name = name or original_name
        
        # Create learning profile directory if it doesn't exist
        profile_dir = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
        os.makedirs(profile_dir, exist_ok=True)
        
        # Check if profile already exists
        profile_files = [f for f in os.listdir(profile_dir) 
                         if f.startswith(profile_name.lower().replace(' ', '_')) and f.endswith('.json')]
        
        if profile_files and not force:
            console.print(f"[yellow]Profile named '{profile_name}' already exists. Use --force to overwrite.[/]")
            return
        
        # Create and save profile
        profile = LearningProfile.from_dict(profile_data)
        if name:
            profile.name = name
        
        profile_path = profile.save()
        
        console.print(f"[green]Learning profile imported and saved to {profile_path}[/]")
        
    except Exception as e:
        console.print(f"[bold red]Error importing learning profile:[/] {str(e)}")


@click.command("list-profiles")
def list_profiles_cmd() -> None:
    """List available learning profiles."""
    try:
        console.print("[bold green]Available learning profiles:[/]")
        
        profile_dir = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
        if not os.path.exists(profile_dir):
            console.print("[yellow]No learning profiles found.[/]")
            return
        
        profiles = []
        for filename in os.listdir(profile_dir):
            if filename.endswith('.json'):
                try:
                    profile_path = os.path.join(profile_dir, filename)
                    profile = LearningProfile.load(profile_path)
                    profiles.append({
                        "name": profile.name,
                        "project_id": profile.project_id,
                        "created": profile.created,
                        "updated": profile.last_updated,
                        "filename_patterns": len(profile.filename_patterns),
                        "directory_patterns": len(profile.directory_patterns),
                        "keyword_patterns": len(profile.keyword_patterns),
                    })
                except Exception as e:
                    console.print(f"[red]Error loading profile {filename}: {e}[/]")
        
        if not profiles:
            console.print("[yellow]No valid learning profiles found.[/]")
            return
        
        # Create table of profiles
        table = Table(title="Learning Profiles")
        table.add_column("Name", style="blue")
        table.add_column("Created", style="green")
        table.add_column("Last Updated", style="yellow")
        table.add_column("Patterns", style="cyan")
        
        for profile in profiles:
            table.add_row(
                profile["name"],
                profile["created"].strftime("%Y-%m-%d %H:%M"),
                profile["updated"].strftime("%Y-%m-%d %H:%M"),
                f"F:{profile['filename_patterns']} D:{profile['directory_patterns']} K:{profile['keyword_patterns']}"
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[bold red]Error listing learning profiles:[/] {str(e)}")


@click.command("feedback")
@click.option("--doc", required=True, type=click.Path(exists=True), 
              help="Path to document to provide feedback for.")
@click.option("--type", "doc_type", required=True, 
              help="Correct document type.")
@click.option("--profile", required=True, 
              help="Learning profile to update.")
def feedback_cmd(doc: str, doc_type: str, profile: str) -> None:
    """Provide feedback to improve learning profiles."""
    try:
        console.print(f"[bold green]Processing feedback for document {doc}...[/]")
        
        # Validate document type
        valid_types = vars(DocumentType)
        valid_types = [t for t in valid_types if not t.startswith('_')]
        
        if doc_type.upper() not in [t.upper() for t in valid_types]:
            console.print(f"[yellow]Warning: '{doc_type}' is not a standard document type.[/]")
            console.print(f"[blue]Standard types: {', '.join(valid_types)}[/]")
        
        # Load or create the learning profile
        profile_dir = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
        os.makedirs(profile_dir, exist_ok=True)
        
        # Find profile
        profile_files = [f for f in os.listdir(profile_dir) 
                         if f.startswith(profile.lower().replace(' ', '_')) and f.endswith('.json')]
        
        if not profile_files:
            console.print(f"[yellow]No learning profile found matching '{profile}'. Creating new profile.[/]")
            learning_profile = LearningProfile(profile)
        else:
            profile_path = os.path.join(profile_dir, profile_files[0])
            learning_profile = LearningProfile.load(profile_path)
        
        # Create document from the specified path
        document = Document(doc)
        document.doc_type = doc_type
        document.confidence = 1.0  # High confidence since this is user feedback
        
        # Read content for learning
        document.read_content()
        
        # Update learning profile with this document
        learning_profile.learn_from_documents([document])
        
        # Save updated profile
        profile_path = learning_profile.save()
        
        console.print(f"[green]Learning profile updated and saved to {profile_path}[/]")
        
    except Exception as e:
        console.print(f"[bold red]Error processing feedback:[/] {str(e)}")


def register_commands(cli_group):
    """Register all commands with the CLI group."""
    cli_group.add_command(discover_docs_cmd)
    cli_group.add_command(analyze_guidance_cmd)
    cli_group.add_command(cache_docs_cmd)
    cli_group.add_command(query_cache_cmd)
    cli_group.add_command(extract_doc_cmd)
    
    # Add learning system commands
    cli_group.add_command(learn_structure_cmd)
    cli_group.add_command(apply_learning_cmd)
    cli_group.add_command(export_learning_cmd)
    cli_group.add_command(import_learning_cmd)
    cli_group.add_command(list_profiles_cmd)
    cli_group.add_command(feedback_cmd)
    
    # Replace existing ai-guidance-status with enhanced version
    for cmd in list(cli_group.commands.keys()):
        if cmd == "ai-guidance-status":
            cli_group.commands.pop(cmd)
            break
    cli_group.add_command(ai_guidance_status_cmd) 