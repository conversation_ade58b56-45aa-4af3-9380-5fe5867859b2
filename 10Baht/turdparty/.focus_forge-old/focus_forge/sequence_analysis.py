#!/usr/bin/env python3
"""
Sequence Analysis module for focus_forge.

This module implements Task T4: Develop Development Sequence Analysis Algorithm.
It provides functionality to determine the optimal sequence for task implementation
based on dependencies, priorities, and other technical constraints.
"""
from enum import Enum
from typing import List, Dict, Set, Optional, Tuple, Any, TypeVar, Generic, Callable
import heapq

# Import dependency graph functionality
from .dependency_graph import DependencyGraph, Dependency, DependencyType

# Type definition for task objects
T = TypeVar('T')


class SequenceConstraintType(Enum):
    """Types of constraints that can affect sequence ordering."""
    TECHNICAL_PREREQUISITE = "technical_prerequisite"  # Technical requirements (e.g., API before UI)
    FOUNDATION_FIRST = "foundation_first"              # Foundation systems that should be built early
    TEAM_ASSIGNMENT = "team_assignment"                # Tasks assigned to the same team should be grouped
    COMPLEXITY_BASED = "complexity_based"              # Complex tasks should be started earlier
    USER_DEFINED = "user_defined"                      # Manually defined ordering constraint


class SequenceConstraint:
    """Represents a constraint for sequence analysis."""
    
    def __init__(
        self,
        constraint_type: SequenceConstraintType,
        task_ids: List[str],
        description: Optional[str] = None,
        weight: float = 1.0
    ):
        """
        Initialize a sequence constraint.
        
        Args:
            constraint_type: Type of constraint
            task_ids: List of task IDs affected by this constraint
            description: Optional description of the constraint
            weight: Weight of this constraint (higher = more important)
        """
        self.constraint_type = constraint_type
        self.task_ids = task_ids
        self.description = description
        self.weight = weight
    
    def __str__(self) -> str:
        """String representation of the constraint."""
        return f"{self.constraint_type.value}: {', '.join(self.task_ids)} ({self.weight})"


class SequenceAnalyzer(Generic[T]):
    """
    Analyzes task dependencies and constraints to determine optimal implementation sequence.
    
    This class uses both topological sorting (for hard dependencies) and heuristic analysis
    (for soft constraints) to generate an implementation sequence that respects dependencies
    while optimizing for efficiency and other constraints.
    """
    
    def __init__(self, tasks: List[T], task_id_fn: Callable[[T], str], priority_fn: Optional[Callable[[T], int]] = None):
        """
        Initialize the sequence analyzer.
        
        Args:
            tasks: List of tasks to analyze
            task_id_fn: Function to extract ID from a task
            priority_fn: Optional function to extract priority value from a task (lower = higher priority)
        """
        self.tasks = tasks
        self.task_id_fn = task_id_fn
        self.priority_fn = priority_fn
        self.constraints: List[SequenceConstraint] = []
        self.dependency_graph = DependencyGraph()
        
        # Build task ID map for quick lookups
        self.task_map: Dict[str, T] = {}
        for task in tasks:
            task_id = task_id_fn(task)
            self.task_map[task_id] = task
            self.dependency_graph.add_node(task_id)
    
    def add_dependency(self, source_id: str, target_id: str, dep_type: DependencyType = DependencyType.DEPENDS_ON, description: Optional[str] = None) -> bool:
        """
        Add a dependency between tasks.
        
        Args:
            source_id: ID of the source task
            target_id: ID of the target task (dependency)
            dep_type: Type of dependency
            description: Optional description
            
        Returns:
            bool: True if added successfully, False if would create a cycle
        """
        if source_id not in self.task_map or target_id not in self.task_map:
            return False
        
        dep = Dependency(source_id, target_id, dep_type, description)
        return self.dependency_graph.add_dependency(dep)
    
    def add_constraint(self, constraint: SequenceConstraint) -> None:
        """
        Add a sequence constraint.
        
        Args:
            constraint: The constraint to add
        """
        self.constraints.append(constraint)
    
    def _get_foundation_tasks(self) -> Set[str]:
        """
        Identify foundation tasks based on constraints and graph structure.
        
        Foundation tasks are those that should be built first as they provide
        core infrastructure or capabilities needed by many other tasks.
        
        Returns:
            Set of task IDs identified as foundation tasks
        """
        foundation_tasks = set()
        
        # First, check explicit foundation constraints
        for constraint in self.constraints:
            if constraint.constraint_type == SequenceConstraintType.FOUNDATION_FIRST:
                for task_id in constraint.task_ids:
                    if task_id in self.task_map:
                        foundation_tasks.add(task_id)
        
        # Then, analyze graph structure to find highly depended-upon tasks
        if not foundation_tasks:
            # Look for tasks with many dependents
            dependent_counts = {}
            for node in self.dependency_graph.nodes:
                dependents = self.dependency_graph.get_dependents(node)
                dependent_counts[node] = len(dependents)
            
            # Tasks with dependents > average are considered foundation
            if dependent_counts:
                avg_dependents = sum(dependent_counts.values()) / len(dependent_counts)
                threshold = max(2, avg_dependents * 1.5)  # At least 2 dependents or 1.5x average
                
                for node, count in dependent_counts.items():
                    if count >= threshold:
                        foundation_tasks.add(node)
        
        return foundation_tasks
    
    def _apply_technical_constraints(self, sequence: List[str]) -> List[str]:
        """
        Apply technical constraints to the sequence.
        
        This adjusts the sequence to respect technical prerequisites and other
        constraints while maintaining topological validity.
        
        Args:
            sequence: Current sequence of task IDs
            
        Returns:
            Adjusted sequence
        """
        technical_constraints = [c for c in self.constraints 
                              if c.constraint_type == SequenceConstraintType.TECHNICAL_PREREQUISITE]
        
        if not technical_constraints:
            return sequence
        
        # Create a position map for the current sequence
        position_map = {task_id: i for i, task_id in enumerate(sequence)}
        
        # Apply each technical constraint
        for constraint in technical_constraints:
            # For technical prerequisites, tasks should be in the specified order
            for i in range(1, len(constraint.task_ids)):
                before_id = constraint.task_ids[i-1]
                after_id = constraint.task_ids[i]
                
                if before_id in position_map and after_id in position_map:
                    if position_map[before_id] > position_map[after_id]:
                        # Swap tasks while maintaining topological validity
                        before_pos = position_map[before_id]
                        after_pos = position_map[after_id]
                        
                        # Check if swapping would violate dependencies
                        can_swap = True
                        for pos in range(after_pos, before_pos):
                            task_id = sequence[pos]
                            if self.dependency_graph.has_path(task_id, before_id):
                                can_swap = False
                                break
                        
                        if can_swap:
                            # Adjust the sequence by moving after_id after before_id
                            sequence.pop(after_pos)
                            sequence.insert(before_pos, after_id)
                            
                            # Update position map
                            for i, task_id in enumerate(sequence):
                                position_map[task_id] = i
        
        return sequence
    
    def _calculate_task_priority_score(self, task_id: str) -> float:
        """
        Calculate priority score for a task based on constraints and inherent priority.
        
        Higher score = higher priority for sequencing.
        
        Args:
            task_id: Task ID to calculate score for
            
        Returns:
            Priority score (higher = higher priority)
        """
        score = 0.0
        task = self.task_map.get(task_id)
        
        if task and self.priority_fn:
            # Base score from task's inherent priority (inverted as lower priority values mean higher priority)
            base_priority = self.priority_fn(task)
            score += 1000.0 / (base_priority + 1)  # Prevent division by zero
        
        # Add points for being a foundation task
        if task_id in self._get_foundation_tasks():
            score += 500.0
        
        # Add constraint-based points
        for constraint in self.constraints:
            if task_id in constraint.task_ids:
                if constraint.constraint_type == SequenceConstraintType.FOUNDATION_FIRST:
                    score += 300.0 * constraint.weight
                elif constraint.constraint_type == SequenceConstraintType.COMPLEXITY_BASED:
                    score += 200.0 * constraint.weight
                elif constraint.constraint_type == SequenceConstraintType.USER_DEFINED:
                    score += 400.0 * constraint.weight
        
        # Add points based on dependency structure
        dependents = self.dependency_graph.get_dependents(task_id)
        score += len(dependents) * 50.0  # More dependents = higher priority
        
        return score
    
    def get_implementation_sequence(self) -> List[T]:
        """
        Generate the optimal implementation sequence for tasks.
        
        This uses a combination of topological sorting and constraint-based scoring
        to determine the best order for implementing tasks.
        
        Returns:
            List of tasks in optimal implementation order
        """
        # First, get topological sort as the base sequence
        topo_sort = self.dependency_graph.topological_sort()
        if topo_sort is None:
            # If there are cycles, we can't determine a valid sequence
            return []
        
        # Apply foundation task prioritization
        foundation_tasks = self._get_foundation_tasks()
        if foundation_tasks:
            # Move foundation tasks earlier in the sequence when possible
            foundation_indices = [i for i, task_id in enumerate(topo_sort) if task_id in foundation_tasks]
            
            for idx in sorted(foundation_indices, reverse=True):
                task_id = topo_sort[idx]
                
                # Find earliest possible position for this foundation task
                earliest_pos = 0
                for i in range(idx):
                    if self.dependency_graph.has_path(topo_sort[i], task_id):
                        earliest_pos = i + 1
                
                # Move the task to the earliest possible position
                if earliest_pos < idx:
                    topo_sort.pop(idx)
                    topo_sort.insert(earliest_pos, task_id)
        
        # Apply technical constraints
        topo_sort = self._apply_technical_constraints(topo_sort)
        
        # Apply priority-based sorting within each "layer" of the dependency graph
        # A layer consists of tasks that can be started at the same time
        result = []
        visited = set()
        
        while len(visited) < len(topo_sort):
            # Find tasks that can be started now (all dependencies satisfied)
            available_tasks = []
            
            for task_id in topo_sort:
                if task_id in visited:
                    continue
                
                dependencies = self.dependency_graph.get_dependencies(task_id)
                if all(dep.target_id in visited for dep in dependencies):
                    # Task can be started - calculate its priority score
                    score = self._calculate_task_priority_score(task_id)
                    available_tasks.append((score, task_id))
            
            # Sort available tasks by score (descending)
            available_tasks.sort(reverse=True)
            
            # Add them to the result
            for _, task_id in available_tasks:
                result.append(task_id)
                visited.add(task_id)
        
        # Convert back to the original task objects
        return [self.task_map[task_id] for task_id in result]
    
    def get_critical_path(self) -> List[str]:
        """
        Find the critical path in the task dependency graph.
        
        The critical path is the longest sequence of dependent tasks, which
        determines the minimum time needed to complete the entire project.
        
        Returns:
            List of task IDs forming the critical path
        """
        # Calculate the earliest possible start time for each task
        earliest_start: Dict[str, int] = {}
        
        # Topological sort to process tasks in dependency order
        topo_sort = self.dependency_graph.topological_sort()
        if topo_sort is None:
            return []  # Cycles detected, can't determine critical path
        
        # Initialize start times
        for task_id in topo_sort:
            earliest_start[task_id] = 0
        
        # Calculate earliest start times
        for task_id in topo_sort:
            dependencies = self.dependency_graph.get_dependencies(task_id)
            for dep in dependencies:
                dep_task = dep.target_id
                if dep_task in earliest_start:
                    # Task can't start until its dependency is done
                    # For simplicity, assume each task takes 1 time unit
                    earliest_start[task_id] = max(earliest_start[task_id], earliest_start[dep_task] + 1)
        
        # Find the latest finishing task
        if not earliest_start:
            return []
            
        latest_finish = max(earliest_start.values())
        end_tasks = [t for t, time in earliest_start.items() if time == latest_finish]
        
        if not end_tasks:
            return []
            
        # Pick the task with the most dependencies as the end of critical path
        end_task = max(end_tasks, key=lambda t: len(self.dependency_graph.get_dependencies(t)))
        
        # Trace backwards to find the critical path
        path = [end_task]
        current = end_task
        
        while earliest_start[current] > 0:
            dependencies = self.dependency_graph.get_dependencies(current)
            if not dependencies:
                break
                
            # Find the dependency with the latest finish time
            critical_dep = max(
                dependencies, 
                key=lambda d: earliest_start.get(d.target_id, 0)
            )
            
            if critical_dep.target_id not in earliest_start:
                break
                
            current = critical_dep.target_id
            path.append(current)
        
        # Return the path in the correct order (start to finish)
        return list(reversed(path))
    
    def identify_parallel_tasks(self) -> List[List[str]]:
        """
        Identify groups of tasks that can be implemented in parallel.
        
        This is useful for optimizing resource allocation and team assignments.
        
        Returns:
            List of lists, where each inner list contains task IDs that can be worked on in parallel
        """
        # Get the topological sorting first
        topo_sort = self.dependency_graph.topological_sort()
        if topo_sort is None:
            return []  # Cycles detected
        
        # Group tasks by their depth in the dependency graph
        depths: Dict[str, int] = {}
        
        # Calculate depths (longest path to reach this task)
        for task_id in topo_sort:
            dependencies = self.dependency_graph.get_dependencies(task_id)
            depth = 0
            for dep in dependencies:
                dep_task = dep.target_id
                if dep_task in depths:
                    depth = max(depth, depths[dep_task] + 1)
            depths[task_id] = depth
        
        # Group tasks by depth
        parallel_groups: Dict[int, List[str]] = {}
        for task_id, depth in depths.items():
            if depth not in parallel_groups:
                parallel_groups[depth] = []
            parallel_groups[depth].append(task_id)
        
        # Convert to list of lists and sort by depth
        return [tasks for _, tasks in sorted(parallel_groups.items())]
    
    def estimate_implementation_time(self, task_time_fn: Callable[[T], float]) -> float:
        """
        Estimate the total implementation time based on the critical path.
        
        Args:
            task_time_fn: Function to extract time estimate from a task
            
        Returns:
            Estimated total implementation time
        """
        critical_path = self.get_critical_path()
        if not critical_path:
            return 0.0
        
        total_time = 0.0
        for task_id in critical_path:
            if task_id in self.task_map:
                task = self.task_map[task_id]
                total_time += task_time_fn(task)
        
        return total_time


def create_sequence_analyzer_from_tasks(tasks: List[Dict[str, Any]]) -> SequenceAnalyzer:
    """
    Create a sequence analyzer from a list of focus_forge tasks.
    
    Args:
        tasks: List of focus_forge task dictionaries
        
    Returns:
        Configured SequenceAnalyzer instance
    """
    # Define priority mapping function (convert string priorities to numeric values)
    def get_priority_value(task: Dict[str, Any]) -> int:
        priority_map = {"high": 0, "medium": 1, "low": 2}
        return priority_map.get(task.get("priority", "medium"), 1)
    
    # Create the analyzer
    analyzer = SequenceAnalyzer(
        tasks=tasks,
        task_id_fn=lambda t: t["id"],
        priority_fn=get_priority_value
    )
    
    # Add dependencies from the task data
    for task in tasks:
        for dep_id in task.get("dependencies", []):
            analyzer.add_dependency(task["id"], dep_id)
    
    # Identify and add foundation task constraints
    foundation_tasks = []
    for task in tasks:
        # Look for indication of foundation nature in title or description
        title_lower = task.get("title", "").lower()
        desc_lower = task.get("description", "").lower()
        
        foundation_indicators = ["foundation", "core", "infrastructure", "base", "framework"]
        if any(indicator in title_lower or indicator in desc_lower for indicator in foundation_indicators):
            foundation_tasks.append(task["id"])
    
    if foundation_tasks:
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.FOUNDATION_FIRST,
            task_ids=foundation_tasks,
            description="Identified foundation tasks from descriptions"
        ))
    
    # Add technical constraints for standard patterns
    backend_tasks = []
    frontend_tasks = []
    
    for task in tasks:
        title_lower = task.get("title", "").lower()
        desc_lower = task.get("description", "").lower()
        
        if any(term in title_lower or term in desc_lower 
              for term in ["backend", "api", "database", "server"]):
            backend_tasks.append(task["id"])
        
        if any(term in title_lower or term in desc_lower 
              for term in ["frontend", "ui", "interface", "client"]):
            frontend_tasks.append(task["id"])
    
    # Backend should come before frontend
    if backend_tasks and frontend_tasks:
        for backend_id in backend_tasks:
            for frontend_id in frontend_tasks:
                # Only if there's no explicit dependency already
                if backend_id in tasks and frontend_id in tasks:
                    deps = tasks[tasks.index(next(t for t in tasks if t["id"] == frontend_id))].get("dependencies", [])
                    if backend_id not in deps:
                        analyzer.add_constraint(SequenceConstraint(
                            constraint_type=SequenceConstraintType.TECHNICAL_PREREQUISITE,
                            task_ids=[backend_id, frontend_id],
                            description="Backend should be implemented before frontend"
                        ))
    
    return analyzer


def get_optimized_sequence(tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Get an optimized implementation sequence based on dependencies and heuristics.
    
    Args:
        tasks: List of focus_forge task dictionaries
        
    Returns:
        List of tasks in optimal implementation order
    """
    analyzer = create_sequence_analyzer_from_tasks(tasks)
    return analyzer.get_implementation_sequence()


def get_parallel_task_groups(tasks: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
    """
    Group tasks that can be implemented in parallel.
    
    Args:
        tasks: List of focus_forge task dictionaries
        
    Returns:
        List of task groups that can be implemented in parallel
    """
    analyzer = create_sequence_analyzer_from_tasks(tasks)
    task_groups = analyzer.identify_parallel_tasks()
    
    # Convert task IDs back to task dictionaries
    task_dict = {task["id"]: task for task in tasks}
    return [[task_dict[task_id] for task_id in group if task_id in task_dict] 
            for group in task_groups]


def get_critical_path(tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Get the critical path of tasks that determine the project timeline.
    
    Args:
        tasks: List of focus_forge task dictionaries
        
    Returns:
        List of tasks on the critical path
    """
    analyzer = create_sequence_analyzer_from_tasks(tasks)
    path = analyzer.get_critical_path()
    
    # Convert task IDs back to task dictionaries
    task_dict = {task["id"]: task for task in tasks}
    return [task_dict[task_id] for task_id in path if task_id in task_dict]


if __name__ == "__main__":
    import sys
    import json
    
    # Example usage when run directly
    if len(sys.argv) > 1:
        try:
            with open(sys.argv[1], 'r') as f:
                data = json.load(f)
                tasks = data.get("tasks", [])
                
                if not tasks:
                    print("No tasks found in the input file")
                    sys.exit(1)
                    
                print(f"Analyzing {len(tasks)} tasks...")
                
                # Get optimal sequence
                sequence = get_optimized_sequence(tasks)
                print("\nOptimal implementation sequence:")
                for i, task in enumerate(sequence):
                    print(f"{i+1}. {task['id']}: {task.get('title', 'Unknown')} (Priority: {task.get('priority', 'medium')})")
                
                # Get parallel tasks
                parallel_groups = get_parallel_task_groups(tasks)
                print("\nParallel task groups:")
                for i, group in enumerate(parallel_groups):
                    print(f"Group {i+1}:")
                    for task in group:
                        print(f"  - {task['id']}: {task.get('title', 'Unknown')}")
                
                # Get critical path
                critical_path = get_critical_path(tasks)
                print("\nCritical path:")
                for i, task in enumerate(critical_path):
                    print(f"{i+1}. {task['id']}: {task.get('title', 'Unknown')}")
        except Exception as e:
            print(f"Error analyzing tasks: {str(e)}")
            sys.exit(1)
    else:
        print("Usage: python sequence_analysis.py tasks.json")
        sys.exit(1) 