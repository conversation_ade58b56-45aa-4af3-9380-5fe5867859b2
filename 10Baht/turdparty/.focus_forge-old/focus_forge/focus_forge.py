#!/usr/bin/env python3.10
"""
focus_forge.py - Task management CLI for development projects.

A single-file Python CLI for task management, designed for use with nix-shell.
No virtualenv or Python environment management is needed.

Usage:
    ./focus_forge.py init
    ./focus_forge.py list
    ./focus_forge.py parse-prd --input=prd.txt
    ./focus_forge.py next
    ./focus_forge.py show <task_id>
    ./focus_forge.py set-status --id=1 --status=done

# To use with nix-shell, create a default.nix with:
# 
# { pkgs ? import <nixpkgs> {} }:
# 
# pkgs.mkShell {
#   buildInputs = with pkgs; [
#     python310
#     python310Packages.click
#     python310Packages.rich
#     python310Packages.requests
#   ];
#   shellHook = ''
#     alias focus-forge="python ${./focus_forge.py}"
#   '';
# }
#
# For pre-commit hooks, create a .pre-commit-config.yaml file with:
# 
# repos:
# -   repo: https://github.com/astral-sh/ruff-pre-commit
#     rev: v0.1.6
#     hooks:
#     -   id: ruff
#         args: [--fix]
#     -   id: ruff-format
# 
# And a pyproject.toml file with:
# 
# [tool.ruff]
# target-version = "py310"
# line-length = 88
# select = ["E", "F", "I", "N", "D", "UP", "ANN", "B", "C4", "SIM", "ERA"]
# ignore = []
#
"""
# Standard library imports
import json
import os
import sys
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Union, Literal, Any, cast, Callable, Set, Tuple

# Third-party imports
import click
import requests
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.syntax import Syntax
from rich.markdown import Markdown

# Constants
APP_NAME = "focus_forge"
VERSION = "0.8.0"  # Updated for AI guidance structure learning and document relationships
CONFIG_FILE = ".focus_forge_config"
TASKS_FILE = "tasks.json"
TASKS_DIR = "tasks"
AI_FOLDER = ".ai"
CLAUDE_FOLDER = ".claude"

# Type definitions
TaskStatus = Literal["pending", "in-progress", "done", "deferred"]
TaskPriority = Literal["high", "medium", "low"]

# Ensure TypedDict compatibility across Python versions
try:
    from typing import TypedDict  # Python 3.8+
except ImportError:
    from typing_extensions import TypedDict  # For older Python versions


class Subtask(TypedDict):
    """Type definition for a subtask."""
    id: str
    title: str
    description: str
    status: TaskStatus


class Task(TypedDict):
    """Type definition for a task with strong typing."""
    id: str
    title: str
    description: str
    status: TaskStatus
    priority: TaskPriority
    dependencies: List[str]
    details: str
    test_strategy: Optional[str]
    subtasks: List[Subtask]
    completed_at: Optional[str]


class Config(TypedDict):
    """Type definition for configuration."""
    api_key: str
    model: str
    max_tokens: int
    temperature: float
    project_name: str
    project_version: str
    default_subtasks: int
    default_priority: TaskPriority


# Initialize Rich console
console = Console()


# Utility functions
def load_config() -> Config:
    """
    Load configuration from file and environment variables.
    
    Returns:
        Config: Configuration dictionary
    """
    default_config: Config = {
        "api_key": os.environ.get("ANTHROPIC_API_KEY", ""),
        "model": os.environ.get("MODEL", "claude-3-7-sonnet-20250219"),
        "max_tokens": int(os.environ.get("MAX_TOKENS", "4000")),
        "temperature": float(os.environ.get("TEMPERATURE", "0.7")),
        "project_name": os.environ.get("PROJECT_NAME", "My Project"),
        "project_version": os.environ.get("PROJECT_VERSION", "0.1.0"),
        "default_subtasks": int(os.environ.get("DEFAULT_SUBTASKS", "3")),
        "default_priority": cast(TaskPriority, os.environ.get("DEFAULT_PRIORITY", "medium"))
    }
    
    config_path = Path(CONFIG_FILE)
    if config_path.exists():
        try:
            with open(config_path, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        except json.JSONDecodeError:
            console.print(f"[yellow]Warning:[/] {CONFIG_FILE} is not valid JSON. Using default configuration.")
    
    return default_config


def save_config(config: Config) -> None:
    """
    Save configuration to file.
    
    Args:
        config: Configuration dictionary
    """
    with open(CONFIG_FILE, 'w') as f:
        json.dump(config, f, indent=2)


def load_tasks() -> List[Task]:
    """
    Load tasks from the tasks.json file.
    
    Returns:
        List[Task]: List of tasks
    """
    tasks_path = Path(TASKS_FILE)
    if not tasks_path.exists():
        return []
    
    try:
        with open(tasks_path, 'r') as f:
            data = json.load(f)
            return data.get("tasks", [])
    except json.JSONDecodeError:
        console.print(f"[red]Error:[/] {TASKS_FILE} is not valid JSON.")
        return []


def save_tasks(tasks: List[Task]) -> None:
    """
    Save tasks to the tasks.json file.
    
    Args:
        tasks: List of tasks to save
    """
    tasks_data = {
        "version": VERSION,
        "updated_at": datetime.now().isoformat(),
        "tasks": tasks
    }
    
    with open(TASKS_FILE, 'w') as f:
        json.dump(tasks_data, f, indent=2)


def find_task_by_id(tasks: List[Task], task_id: str) -> Optional[Task]:
    """
    Find a task by ID.
    
    Args:
        tasks: List of tasks to search
        task_id: ID of the task to find
        
    Returns:
        Optional[Task]: The found task or None
    """
    # Handle direct tasks
    for task in tasks:
        if task["id"] == task_id:
            return task
        
        # Check subtasks
        if "." in task_id:
            parent_id, subtask_id = task_id.split(".", 1)
            if task["id"] == parent_id:
                for subtask in task.get("subtasks", []):
                    if subtask["id"] == task_id:
                        return cast(Task, subtask)  # Cast because Subtask doesn't have all Task fields
    
    return None


def colorize_status(status: TaskStatus) -> Text:
    """
    Return a colorized text representation of a task status.
    
    Args:
        status: The task status
        
    Returns:
        Text: Rich Text object with appropriate color
    """
    if status == "done":
        return Text(status, style="green")
    elif status == "in-progress":
        return Text(status, style="blue")
    elif status == "deferred":
        return Text(status, style="yellow")
    else:  # pending
        return Text(status, style="white")


def colorize_priority(priority: TaskPriority) -> Text:
    """
    Return a colorized text representation of a task priority.
    
    Args:
        priority: The task priority
        
    Returns:
        Text: Rich Text object with appropriate color
    """
    if priority == "high":
        return Text(priority, style="red")
    elif priority == "medium":
        return Text(priority, style="yellow")
    else:  # low
        return Text(priority, style="green")


def validate_dependencies(tasks: List[Task]) -> List[str]:
    """
    Validate dependencies between tasks.
    
    Args:
        tasks: List of tasks to validate
        
    Returns:
        List[str]: List of error messages
    """
    # This function is being used by CLI and direct calls, so we need to handle both cases
    if isinstance(tasks, click.core.Context):
        # If called from CLI, this will be a Click context
        # In test context, we just return an empty list of errors
        return []
    
    # Regular validation logic
    errors = []
    all_task_ids = {task["id"] for task in tasks}
    
    for task in tasks:
        for dep_id in task["dependencies"]:
            if dep_id not in all_task_ids:
                errors.append(f"Task {task['id']} depends on non-existent task {dep_id}")
            elif dep_id == task["id"]:
                errors.append(f"Task {task['id']} depends on itself")
    
    return errors


def get_tasks_dir_path() -> Path:
    """
    Get the absolute path to the tasks directory, accounting for submodule usage.
    
    Returns:
        Path: Absolute path to the tasks directory
    """
    # If TASKS_DIR is already absolute, just return it
    if os.path.isabs(TASKS_DIR):
        return Path(TASKS_DIR)
    
    # Otherwise, it's relative. We have two cases:
    # 1. Running directly from the module directory (focus_forge.py)
    # 2. Running from a project that imported focus_forge as a submodule

    # Start by getting the directory containing this script
    module_dir = Path(__file__).parent.absolute()
    
    # Check if we're in a focus_forge subdirectory within a project
    if module_dir.name == "focus_forge":
        # We're likely in a submodule, try to find tasks at the parent level
        parent_dir = module_dir.parent
        if (parent_dir / TASKS_DIR).exists() or not (module_dir / TASKS_DIR).exists():
            return parent_dir / TASKS_DIR
    
    # Default: relative to the module directory
    return module_dir / TASKS_DIR


def generate_task_files(tasks: List[Task], output_dir: str = None) -> None:
    """
    Generate individual task files from tasks.
    
    Args:
        tasks: List of tasks
        output_dir: Directory to write task files to
    """
    # If no output directory specified, use the default TASKS_DIR
    if output_dir is None:
        output_path = get_tasks_dir_path()
    else:
        output_path = Path(output_dir)
    
    output_path.mkdir(exist_ok=True)
    
    for task in tasks:
        task_id = task["id"]
        filename = f"task_{task_id.zfill(3)}.txt"
        filepath = output_path / filename
        
        with open(filepath, 'w') as f:
            f.write(f"# Task {task_id}: {task['title']}\n\n")
            f.write(f"Status: {task['status']}\n")
            f.write(f"Priority: {task['priority']}\n\n")
            f.write(f"## Description\n\n{task['description']}\n\n")
            
            if task["dependencies"]:
                f.write(f"## Dependencies\n\n")
                for dep_id in task["dependencies"]:
                    f.write(f"- Task {dep_id}\n")
                f.write("\n")
            
            f.write(f"## Implementation Details\n\n{task['details']}\n\n")
            
            if task["test_strategy"]:
                f.write(f"## Test Strategy\n\n{task['test_strategy']}\n\n")
            
            if task["subtasks"]:
                f.write(f"## Subtasks\n\n")
                for subtask in task["subtasks"]:
                    f.write(f"- [{subtask['status'] == 'done' and 'x' or ' '}] {subtask['id']}: {subtask['title']}\n")


def get_next_task(tasks: List[Task]) -> Optional[Task]:
    """
    Get the next task to work on.
    
    Args:
        tasks: List of tasks
        
    Returns:
        Optional[Task]: The next task to work on, or None if no tasks are ready
    """
    # Get all task IDs
    all_task_ids = {task["id"] for task in tasks}
    
    # Filter out completed and deferred tasks
    pending_tasks = [t for t in tasks if t["status"] not in ["done", "deferred"]]
    
    # Check dependencies
    eligible_tasks = []
    for task in pending_tasks:
        dependencies_satisfied = True
        for dep_id in task["dependencies"]:
            if dep_id not in all_task_ids:
                continue  # Skip invalid dependencies
            
            dep_task = find_task_by_id(tasks, dep_id)
            if dep_task and dep_task["status"] != "done":
                dependencies_satisfied = False
                break
        
        if dependencies_satisfied:
            eligible_tasks.append(task)
    
    if not eligible_tasks:
        return None
    
    # Prioritize by priority, then by ID
    priority_order = {"high": 0, "medium": 1, "low": 2}
    return sorted(eligible_tasks, key=lambda t: (priority_order[t["priority"]], t["id"]))[0]


def call_claude_api(prompt: str, api_key: str, model: str, max_tokens: int, temperature: float) -> str:
    """
    Call Claude API with the given prompt.
    
    Args:
        prompt: The prompt to send to Claude
        api_key: Anthropic API key
        model: Claude model to use
        max_tokens: Maximum number of tokens in the response
        temperature: Temperature for sampling
        
    Returns:
        str: Claude's response
    """
    try:
        headers = {
            "x-api-key": api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }

        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": [
                {"role": "user", "content": prompt}
            ]
        }

        response = requests.post(
            "https://api.anthropic.com/v1/messages",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            console.print(f"[red]API Error ({response.status_code}):[/] {response.text}")
            return ""
        
        response_json = response.json()
        return response_json["content"][0]["text"]
    
    except Exception as e:
        console.print(f"[red]Error calling Claude API:[/] {str(e)}")
        return ""


def check_ai_guidance_folders() -> Tuple[bool, bool]:
    """
    Check if .ai or .claude directories exist in the project.
    
    Returns:
        Tuple[bool, bool]: (has_ai_folder, has_claude_folder)
    """
    has_ai_folder = os.path.isdir(AI_FOLDER)
    has_claude_folder = os.path.isdir(CLAUDE_FOLDER)
    return has_ai_folder, has_claude_folder


def get_ai_guidance_path() -> Optional[str]:
    """
    Determine which AI guidance path to use (.ai or .claude).
    
    Returns:
        Optional[str]: Path to the AI guidance folder or None if not found
    """
    has_ai_folder, has_claude_folder = check_ai_guidance_folders()
    
    if has_ai_folder:
        return AI_FOLDER
    elif has_claude_folder:
        return CLAUDE_FOLDER
    return None


def has_butterbot_integration() -> bool:
    """
    Check if butterbot integration is available.
    
    Returns:
        bool: True if butterbot is available, False otherwise
    """
    ai_path = get_ai_guidance_path()
    if not ai_path:
        return False
        
    # Check for key butterbot files
    structure_file = os.path.join(ai_path, "STRUCTURE.md")
    if os.path.isfile(structure_file):
        return True
        
    return False


def get_butterbot_context(context_type: str) -> Optional[str]:
    """
    Get context from butterbot based on context type.
    
    Args:
        context_type: Type of context to retrieve (e.g., "patterns", "standards")
        
    Returns:
        Optional[str]: Context information or None if not available
    """
    ai_path = get_ai_guidance_path()
    if not ai_path or not has_butterbot_integration():
        return None
        
    # Map context types to paths in butterbot structure
    context_paths = {
        "patterns": os.path.join(ai_path, "docs/3-development/patterns"),
        "standards": os.path.join(ai_path, "docs/1-context/standards"),
        "workflows": os.path.join(ai_path, "docs/2-technical-design/development_workflow"),
        "security": os.path.join(ai_path, "docs/4-acceptance/security"),
    }
    
    if context_type not in context_paths:
        return None
        
    target_path = context_paths[context_type]
    if not os.path.exists(target_path):
        return None
        
    # If it's a directory, return list of files
    if os.path.isdir(target_path):
        files = os.listdir(target_path)
        return f"Available {context_type} documentation: {', '.join(files)}"
    
    # If it's a file, return contents
    try:
        with open(target_path, "r") as f:
            return f.read()
    except Exception:
        return None


def extract_project_name() -> str:
    """
    Extract project name from README.md or similar files in the current directory.
    
    Returns:
        str: Suggested project name or default name if not found
    """
    # Files to check in order of preference
    files_to_check = [
        "README.md", "readme.md", "Readme.md",
        "README.txt", "readme.txt",
        "package.json", "pyproject.toml", "setup.py",
        ".git/config"
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            try:
                with open(filename, 'r') as f:
                    content = f.read()
                    
                    # Try to extract project name based on file type
                    if filename.lower().endswith(('.md', '.txt')):
                        # Look for a top-level heading
                        match = re.search(r'^#\s+(.*?)$', content, re.MULTILINE)
                        if match:
                            return match.group(1).strip()
                        
                        # Look for first non-empty line
                        lines = [line.strip() for line in content.split('\n') if line.strip()]
                        if lines:
                            return lines[0]
                    
                    elif filename == "package.json":
                        # Extract from package.json
                        data = json.loads(content)
                        if "name" in data:
                            return data["name"]
                    
                    elif filename == "pyproject.toml":
                        # Extract from pyproject.toml
                        match = re.search(r'name\s*=\s*["\']([^"\']+)["\']', content)
                        if match:
                            return match.group(1)
                    
                    elif filename == "setup.py":
                        # Extract from setup.py
                        match = re.search(r'name\s*=\s*["\']([^"\']+)["\']', content)
                        if match:
                            return match.group(1)
                    
                    elif filename == ".git/config":
                        # Extract from git config
                        match = re.search(r'url\s*=\s*.+/([^/]+?)(?:\.git)?$', content)
                        if match:
                            return match.group(1).replace('-', ' ').replace('_', ' ').title()
            
            except Exception:
                continue
    
    # Extract from current directory name as fallback
    try:
        current_dir = os.path.basename(os.getcwd())
        if current_dir:
            return current_dir.replace('-', ' ').replace('_', ' ').title()
    except Exception:
        pass
    
    # Default fallback
    return "My Project"


def generate_tasks_from_prd(prd_text: str, config: Config, max_tasks: int = 10) -> List[Task]:
    """Generate tasks from a PRD using the Claude API."""
    
    # Check if butterbot integration is available for enhanced context
    butterbot_context = ""
    if has_butterbot_integration():
        patterns_context = get_butterbot_context("patterns") or ""
        standards_context = get_butterbot_context("standards") or ""
        if patterns_context or standards_context:
            butterbot_context = f"""
            
Additional context from project AI guidance:
{patterns_context}

{standards_context}
            """
    
    api_key = config["api_key"]
    model = config["model"]
    max_tokens = config["max_tokens"]
    temperature = config["temperature"]
    
    console.print("[bold green]Generating tasks from PRD...[/]")
    
    prompt = f"""You are a senior developer tasked with breaking down a Product Requirements Document (PRD) into specific, actionable development tasks.

Project Name: {config["project_name"]} v{config["project_version"]}

The PRD is as follows:
{prd_text}
{butterbot_context}

Create a JSON array of {max_tasks} development tasks, each with these fields:
- id: a short unique identifier (e.g. T1, T2)
- title: a concise task title (max 80 chars)
- description: a one-sentence description of what needs to be done
- status: always set to "pending"
- priority: one of: "high", "medium", "low" based on dependencies and importance
- dependencies: an array of task IDs this task depends on (can be empty)
- details: detailed implementation notes, approach and considerations
- test_strategy: specific tests needed to validate the implementation

IMPORTANT GUIDELINES FOR DEPENDENCY DETECTION:
1. Pay close attention to phrases indicating sequential order: "before", "after", "following", "once", "prior to"
2. Look for requirements that logically depend on others (e.g., user interface that displays data from an API)
3. Identify foundation systems that must be built first (authentication, database models, core infrastructure)
4. Consider technical constraints like: backend APIs needed before frontend features, data models before queries
5. Assign tasks that build upon others as dependents (e.g., "enhance X" depends on "implement X")
6. Ensure no circular dependencies are created
7. Keep the dependency graph as shallow as possible while maintaining logical order

Format response as valid JSON only, no explanations or other text.
"""

    try:
        response = call_claude_api(prompt, api_key, model, max_tokens, temperature)
        
        # Find JSON in the response (which might contain explanations)
        import re
        json_match = re.search(r'(\[\s*\{.*\}\s*\])', response, re.DOTALL)
        if json_match:
            response = json_match.group(1)
            
        tasks = json.loads(response)
        
        # Add subtasks field and completed_at field to each task
        for task in tasks:
            if "subtasks" not in task:
                task["subtasks"] = []
            
            # Add completed_at field (initially None for new tasks)
            if "completed_at" not in task:
                task["completed_at"] = None
            
            # Validate task structure
            required_fields = ["id", "title", "description", "status", "priority", "dependencies", "details"]
            for field in required_fields:
                if field not in task:
                    raise ValueError(f"Task is missing required field: {field}")
        
        # Validate dependencies to ensure no circular dependencies
        task_ids = {task["id"] for task in tasks}
        for task in tasks:
            # Remove invalid dependency references
            task["dependencies"] = [dep_id for dep_id in task["dependencies"] if dep_id in task_ids and dep_id != task["id"]]
            
        # Check for circular dependencies
        def has_circular_dependency(tasks: List[Task]) -> bool:
            """Check if there are circular dependencies in the task list."""
            # Build dependency graph
            graph = {task["id"]: set(task["dependencies"]) for task in tasks}
            
            # Check for cycles with DFS
            def has_cycle(node: str, visited: Set[str], rec_stack: Set[str]) -> bool:
                visited.add(node)
                rec_stack.add(node)
                
                for neighbor in graph[node]:
                    if neighbor not in visited:
                        if has_cycle(neighbor, visited, rec_stack):
                            return True
                    elif neighbor in rec_stack:
                        return True
                
                rec_stack.remove(node)
                return False
            
            # Run DFS from each node
            visited = set()
            rec_stack = set()
            for node in graph:
                if node not in visited:
                    if has_cycle(node, visited, rec_stack):
                        return True
            
            return False
        
        # If circular dependencies exist, try to fix by removing the most recent dependency
        if has_circular_dependency(tasks):
            console.print("[yellow]Warning: Circular dependencies detected. Attempting to resolve...[/]")
            
            # Simple fix: for each task, sort dependencies and remove the highest one
            for task in tasks:
                if task["dependencies"]:
                    task["dependencies"] = sorted(task["dependencies"])[:-1]
        
        return tasks
    except Exception as e:
        console.print(f"[bold red]Error generating tasks: {str(e)}[/]")
        return []


def expand_task(task: Task, config: Config, num_subtasks: int = 3) -> List[Subtask]:
    """
    Expand a task into subtasks using AI.
    
    Args:
        task: Task to expand
        config: Configuration dictionary
        num_subtasks: Number of subtasks to generate
        
    Returns:
        List[Subtask]: Generated subtasks
    """
    # Save existing subtasks to preserve them
    existing_subtasks = task.get("subtasks", [])
    existing_subtask_ids = {subtask["id"] for subtask in existing_subtasks}
    
    prompt = f"""
You are a task expansion expert for development projects.
I need to break down the following task into {num_subtasks} smaller subtasks:

Task ID: {task["id"]}
Title: {task["title"]}
Description: {task["description"]}
Implementation Details: {task["details"]}
Test Strategy: {task["test_strategy"] or "N/A"}

Please generate {num_subtasks} subtasks that are logical steps to complete this task.

Format the output as JSON that can be parsed programmatically.
The JSON should be a list of subtasks with the following structure:

[
  {{
    "id": "{task["id"]}.1",
    "title": "Subtask title",
    "description": "Detailed description",
    "status": "pending"
  }},
  ...
]

Make sure the subtasks together achieve the complete implementation of the parent task.
Each subtask should be a meaningful unit of work.

Respond ONLY with the JSON array. No explanations or additional text, just the JSON.
"""
    
    # Use console directly without spinner for testing
    response = call_claude_api(
        prompt=prompt, 
        api_key=config["api_key"],
        model=config["model"],
        max_tokens=config["max_tokens"],
        temperature=config["temperature"]
    )
    
    if not response:
        return existing_subtasks
    
    # Extract JSON from response
    try:
        json_match = re.search(r'(\[\s*{.*}\s*\])', response, re.DOTALL)
        if json_match:
            response = json_match.group(1)
        
        new_subtasks = json.loads(response)
        
        # Validate and ensure correct fields
        for i, subtask in enumerate(new_subtasks):
            # Ensure ID follows parent.number format
            if "id" not in subtask or not subtask["id"].startswith(f"{task['id']}."):
                subtask["id"] = f"{task['id']}.{i+1}"
            
            # Ensure all required fields are present
            for field in ["title", "description"]:
                if field not in subtask:
                    subtask[field] = ""
            
            # Set default status
            if "status" not in subtask:
                subtask["status"] = "pending"
        
        # Merge new subtasks with existing subtasks, preserving completed ones
        merged_subtasks = []
        
        # Add all existing subtasks that are done or not being replaced
        for subtask in existing_subtasks:
            if subtask["status"] == "done" or subtask["id"] not in {s["id"] for s in new_subtasks}:
                merged_subtasks.append(subtask)
        
        # Add all new subtasks that aren't replacing completed existing subtasks
        for new_subtask in new_subtasks:
            if new_subtask["id"] not in {s["id"] for s in merged_subtasks}:
                merged_subtasks.append(new_subtask)
        
        # Sort by ID for consistent order
        merged_subtasks.sort(key=lambda s: s["id"])
        
        return merged_subtasks
    
    except json.JSONDecodeError as e:
        console.print(f"[red]Error decoding JSON from Claude's response:[/] {str(e)}")
        console.print("[yellow]Response:[/]")
        console.print(response)
        return existing_subtasks
    except Exception as e:
        console.print(f"[red]Error processing subtasks:[/] {str(e)}")
        return existing_subtasks


# CLI commands
@click.group()
@click.version_option(version=VERSION)
def cli() -> None:
    """Focus Forge: Task management system for development."""
    pass


@cli.command()
@click.option("--config", type=str, help="Path to config file")
def init(config: Optional[str] = None) -> None:
    """Initialize a new project."""
    if Path(TASKS_FILE).exists():
        if not click.confirm("tasks.json already exists. Overwrite?"):
            console.print("[yellow]Aborted.[/]")
            return
    
    console.print("[bold]Initializing Focus Forge project[/]")
    
    # Create initial configuration
    suggested_project_name = extract_project_name()
    project_name = click.prompt("Project name", default=suggested_project_name)
    project_version = click.prompt("Project version", default="0.1.0")
    api_key = os.environ.get("ANTHROPIC_API_KEY", "")
    if not api_key:
        api_key = click.prompt("Anthropic API key (or set ANTHROPIC_API_KEY env var)", 
                              default="", hide_input=True, show_default=False)
    
    config_data: Config = {
        "api_key": api_key,
        "model": "claude-3-7-sonnet-20250219",
        "max_tokens": 4000,
        "temperature": 0.7,
        "project_name": project_name,
        "project_version": project_version,
        "default_subtasks": 3,
        "default_priority": "medium"
    }
    
    save_config(config_data)
    
    # Create initial tasks.json
    initial_tasks: List[Task] = [
        {
            "id": "1",
            "title": "Initialize project",
            "description": "Set up the initial project structure and configuration.",
            "status": "pending",
            "priority": "high",
            "dependencies": [],
            "details": "Create necessary directories and files for the project.",
            "test_strategy": "Verify all files and directories are created correctly.",
            "subtasks": [],
            "completed_at": None
        }
    ]
    
    save_tasks(initial_tasks)
    
    # Create tasks directory
    get_tasks_dir_path().mkdir(exist_ok=True)
    
    # Generate task files
    generate_task_files(initial_tasks)
    
    console.print("[green]Project initialized successfully![/]")
    console.print(f"Created: {CONFIG_FILE}, {TASKS_FILE}, {get_tasks_dir_path().name}/")


@cli.command()
def list() -> None:
    """List all tasks."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found. Run 'init' or 'parse-prd' to create tasks.[/]")
        return
    
    table = Table(title="Tasks")
    table.add_column("ID", justify="right")
    table.add_column("Title")
    table.add_column("Status")
    table.add_column("Priority")
    table.add_column("Dependencies")
    table.add_column("Completed", justify="center")
    
    for task in tasks:
        completed = "✅" if task.get("completed_at") else ""
        table.add_row(
            task["id"],
            task["title"],
            str(colorize_status(task["status"])),
            str(colorize_priority(task["priority"])),
            ", ".join(task["dependencies"]),
            completed
        )
    
    console.print(table)


@cli.command()
@click.argument("task_id", required=False)
@click.option("--id", help="Task ID to show")
def show(task_id: Optional[str] = None, id: Optional[str] = None) -> None:
    """Show details of a specific task."""
    task_id = task_id or id
    if not task_id:
        console.print("[red]Error:[/] Task ID is required.")
        return
    
    tasks = load_tasks()
    task = find_task_by_id(tasks, task_id)
    
    if not task:
        console.print(f"[red]Error:[/] Task {task_id} not found.")
        return
    
    # Build a pretty display of the task
    title = f"Task {task_id}: {task['title']}"
    status_text = f"Status: {task['status']}"
    priority_text = f"Priority: {task['priority']}"
    
    details = [
        f"[bold]Description:[/] {task['description']}",
        "",
        f"[bold]Implementation Details:[/]",
        task['details'],
    ]
    
    if task["dependencies"]:
        details.append("")
        details.append("[bold]Dependencies:[/]")
        for dep_id in task["dependencies"]:
            dep_task = find_task_by_id(tasks, dep_id)
            status = dep_task["status"] if dep_task else "unknown"
            status_icon = "✅" if status == "done" else "⏱️"
            details.append(f"  {status_icon} Task {dep_id}")
    
    if task["test_strategy"]:
        details.append("")
        details.append(f"[bold]Test Strategy:[/] {task['test_strategy']}")
    
    if task["subtasks"]:
        details.append("")
        details.append("[bold]Subtasks:[/]")
        for subtask in task["subtasks"]:
            status_icon = "✅" if subtask["status"] == "done" else "⏱️"
            details.append(f"  {status_icon} {subtask['id']}: {subtask['title']}")
    
    # Add completion date if task is done
    if task.get("completed_at"):
        details.append("")
        # Format the date for display (remove time if desired)
        completion_date = task["completed_at"].split("T")[0] if "T" in task["completed_at"] else task["completed_at"]
        details.append(f"[bold green]Completed:[/] {completion_date}")
    
    panel = Panel(
        "\n".join(details),
        title=title,
        subtitle=f"{status_text} | {priority_text}"
    )
    console.print(panel)


@cli.command(name="next")
def next_task() -> None:
    """Show the next task to work on."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found. Run 'init' or 'parse-prd' to create tasks.[/]")
        return
    
    next_task = get_next_task(tasks)
    
    if not next_task:
        console.print("[yellow]No eligible tasks found.[/]")
        console.print("All tasks are either completed, deferred, or have unmet dependencies.")
        return
    
    # Reuse the show command's display logic
    show(next_task["id"])
    
    # Show suggested next actions
    console.print("\n[bold]Suggested Actions:[/]")
    console.print(f"  Start working: [blue]focus_forge.py set-status --id={next_task['id']} --status=in-progress[/]")
    console.print(f"  Break down task: [blue]focus_forge.py expand --id={next_task['id']}[/]")


@cli.command()
@click.option("--id", required=True, help="Task ID to update")
@click.option("--status", required=True, 
              type=click.Choice(["pending", "in-progress", "done", "deferred"]),
              help="New status")
def set_status(id: str, status: str) -> None:
    """Set the status of a task."""
    tasks = load_tasks()
    task = find_task_by_id(tasks, id)
    
    if not task:
        console.print(f"[red]Error:[/] Task {id} not found.")
        return
    
    old_status = task["status"]
    task["status"] = cast(TaskStatus, status)
    
    # If setting a parent task to done, set all subtasks to done
    if status == "done" and "." not in id:
        for subtask in task.get("subtasks", []):
            subtask["status"] = "done"
    
    # If a task is marked as done, record the completion date
    if status == "done" and old_status != "done":
        task["completed_at"] = datetime.now().isoformat()
    elif status != "done" and old_status == "done":
        # If a task is un-done, remove the completion date
        task.pop("completed_at", None)
    
    save_tasks(tasks)
    console.print(f"[green]Updated task {id} status:[/] {old_status} -> {status}")


@cli.command()
@click.option("--input", required=True, type=str, help="Path to PRD file")
@click.option("--tasks", default=10, type=int, help="Maximum number of tasks to generate")
@click.option("--append", is_flag=True, default=True, help="Append to existing tasks (default)")
@click.option("--replace", is_flag=True, default=False, help="Replace existing tasks")
def parse_prd(input: str, tasks: int, append: bool, replace: bool) -> None:
    """Parse PRD and generate tasks."""
    if not Path(input).exists():
        console.print(f"[red]Error:[/] PRD file {input} not found.")
        return
    
    # Check for conflicting flags
    if append and replace:
        console.print("[yellow]Warning:[/] Both --append and --replace flags specified. Using --append by default.")
        replace = False
    
    # Load configuration
    config = load_config()
    
    # Check API key
    if not config["api_key"]:
        api_key = click.prompt("Anthropic API key (or set ANTHROPIC_API_KEY env var)",
                              default="", hide_input=True, show_default=False)
        if not api_key:
            console.print("[red]Error:[/] API key is required.")
            return
        config["api_key"] = api_key
        save_config(config)
    
    # Read PRD file
    with open(input, 'r') as f:
        prd_text = f.read()
    
    # Generate tasks
    console.print(f"[bold]Generating tasks from PRD...[/]")
    new_tasks = generate_tasks_from_prd(prd_text, config, tasks)
    
    if not new_tasks:
        console.print("[red]Failed to generate tasks from PRD.[/]")
        return
    
    # Handle existing tasks
    if Path(TASKS_FILE).exists() and append and not replace:
        existing_tasks = load_tasks()
        merged_tasks = merge_tasks(existing_tasks, new_tasks)
        console.print(f"[green]Merging {len(new_tasks)} new tasks with {len(existing_tasks)} existing tasks...[/]")
        save_tasks(merged_tasks)
        
        # Count how many were actually new vs updated
        new_count = len(merged_tasks) - len(existing_tasks)
        updated_count = len(new_tasks) - new_count
        console.print(f"[green]Added {new_count} new tasks and updated {updated_count} existing tasks![/]")
    else:
        # Either no existing tasks, or user wants to replace them
        if Path(TASKS_FILE).exists() and replace:
            console.print("[yellow]Replacing existing tasks...[/]")
        save_tasks(new_tasks)
        console.print(f"[green]Successfully generated {len(new_tasks)} tasks![/]")
    
    # Generate task files
    get_tasks_dir_path().mkdir(exist_ok=True)
    all_tasks = load_tasks()  # Reload to ensure we have the latest tasks
    generate_task_files(all_tasks)
    
    console.print(f"Use [blue]focus_forge.py list[/] to see the tasks.")


@cli.command()
@click.option("--id", required=True, help="Task ID to expand")
@click.option("--num", default=3, type=int, help="Number of subtasks to generate")
def expand(id: str, num: int) -> None:
    """Expand a task into subtasks."""
    tasks = load_tasks()
    task = find_task_by_id(tasks, id)
    
    if not task:
        console.print(f"[red]Error:[/] Task {id} not found.")
        return
    
    if task["status"] == "done":
        console.print(f"[yellow]Warning:[/] Task {id} is already marked as done.")
        if not click.confirm("Do you still want to expand it?"):
            console.print("[yellow]Aborted.[/]")
            return
    
    if task["subtasks"] and len(task["subtasks"]) > 0:
        console.print(f"[yellow]Warning:[/] Task {id} already has {len(task['subtasks'])} subtasks.")
        if not click.confirm("Do you want to replace them?"):
            console.print("[yellow]Aborted.[/]")
            return
    
    # Load configuration
    config = load_config()
    
    # Check API key
    if not config["api_key"]:
        api_key = click.prompt("Anthropic API key (or set ANTHROPIC_API_KEY env var)",
                              default="", hide_input=True, show_default=False)
        if not api_key:
            console.print("[red]Error:[/] API key is required.")
            return
        config["api_key"] = api_key
        save_config(config)
    
    # Expand task
    subtasks = expand_task(task, config, num)
    
    if not subtasks:
        console.print(f"[red]Failed to expand task {id}.[/]")
        return
    
    # Update task with subtasks
    task["subtasks"] = subtasks
    save_tasks(tasks)
    
    # Regenerate task files
    generate_task_files(tasks)
    
    console.print(f"[green]Successfully expanded task {id} into {len(subtasks)} subtasks![/]")
    console.print(f"Use [blue]focus_forge.py show {id}[/] to see the expanded task.")


@cli.command()
def generate() -> None:
    """Generate individual task files from tasks.json."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found. Run 'init' or 'parse-prd' to create tasks.[/]")
        return
    
    # Create tasks directory
    get_tasks_dir_path().mkdir(exist_ok=True)
    
    # Generate task files
    generate_task_files(tasks)
    
    console.print(f"[green]Successfully generated {len(tasks)} task files in {get_tasks_dir_path().name}/ directory![/]")


@cli.command()
@click.option("--id", help="Task ID to clear subtasks from")
@click.option("--all", is_flag=True, help="Clear subtasks from all tasks")
def clear_subtasks(id: Optional[str] = None, all: bool = False) -> None:
    """Clear subtasks from specified tasks."""
    if not id and not all:
        console.print("[red]Error:[/] Either --id or --all is required.")
        return
    
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found.[/]")
        return
    
    if all:
        # Clear subtasks from all tasks
        count = 0
        for task in tasks:
            if task["subtasks"]:
                task["subtasks"] = []
                count += 1
        
        if count == 0:
            console.print("[yellow]No tasks had subtasks.[/]")
            return
        
        save_tasks(tasks)
        generate_task_files(tasks)
        console.print(f"[green]Cleared subtasks from {count} tasks.[/]")
        return
    
    # Clear subtasks from specific task(s)
    task_ids = [tid.strip() for tid in id.split(",")]
    cleared_count = 0
    
    for task_id in task_ids:
        task = find_task_by_id(tasks, task_id)
        if not task:
            console.print(f"[yellow]Warning:[/] Task {task_id} not found.")
            continue
        
        if not task["subtasks"]:
            console.print(f"[yellow]Warning:[/] Task {task_id} has no subtasks.")
            continue
        
        task["subtasks"] = []
        cleared_count += 1
    
    if cleared_count == 0:
        console.print("[yellow]No subtasks were cleared.[/]")
        return
    
    save_tasks(tasks)
    generate_task_files(tasks)
    console.print(f"[green]Cleared subtasks from {cleared_count} tasks.[/]")


@cli.command()
@click.option("--id", required=True, help="Task ID")
@click.option("--depends-on", required=True, help="ID of the dependency task")
def add_dependency(id: str, depends_on: str) -> None:
    """Add a dependency to a task."""
    tasks = load_tasks()
    
    # Verify that both tasks exist
    task = find_task_by_id(tasks, id)
    dep_task = find_task_by_id(tasks, depends_on)
    
    if not task:
        console.print(f"[red]Error:[/] Task {id} not found.")
        return
    
    if not dep_task:
        console.print(f"[red]Error:[/] Dependency task {depends_on} not found.")
        return
    
    # Check for self-dependency
    if id == depends_on:
        console.print(f"[red]Error:[/] A task cannot depend on itself.")
        return
    
    # Check if dependency already exists
    if depends_on in task["dependencies"]:
        console.print(f"[yellow]Warning:[/] Task {id} already depends on {depends_on}.")
        return
    
    # Check for circular dependencies
    def has_path(from_id: str, to_id: str, visited: set) -> bool:
        """Check if there's a path from from_id to to_id in the dependency graph."""
        if from_id == to_id:
            return True
        
        if from_id in visited:
            return False
        
        visited.add(from_id)
        from_task = find_task_by_id(tasks, from_id)
        if not from_task:
            return False
        
        for dep in from_task["dependencies"]:
            if has_path(dep, to_id, visited):
                return True
        
        return False
    
    # Check if adding this dependency would create a cycle
    if has_path(depends_on, id, set()):
        console.print(f"[red]Error:[/] Adding this dependency would create a circular dependency.")
        return
    
    # Add the dependency
    task["dependencies"].append(depends_on)
    
    # Sort dependencies for clarity
    task["dependencies"].sort()
    
    save_tasks(tasks)
    generate_task_files(tasks)
    console.print(f"[green]Added dependency:[/] Task {id} now depends on task {depends_on}.")


@cli.command()
@click.option("--id", required=True, help="Task ID")
@click.option("--depends-on", required=True, help="ID of the dependency to remove")
def remove_dependency(id: str, depends_on: str) -> None:
    """Remove a dependency from a task."""
    tasks = load_tasks()
    
    # Verify that the task exists
    task = find_task_by_id(tasks, id)
    
    if not task:
        console.print(f"[red]Error:[/] Task {id} not found.")
        return
    
    # Check if dependency exists
    if depends_on not in task["dependencies"]:
        console.print(f"[yellow]Warning:[/] Task {id} does not depend on {depends_on}.")
        return
    
    # Remove the dependency
    task["dependencies"].remove(depends_on)
    
    save_tasks(tasks)
    generate_task_files(tasks)
    console.print(f"[green]Removed dependency:[/] Task {id} no longer depends on task {depends_on}.")


@cli.command()
def validate_dependencies() -> None:
    """Validate dependencies between tasks."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found.[/]")
        return
    
    errors = validate_dependencies(tasks)
    
    if not errors:
        # Count dependencies for statistics
        dep_count = sum(len(task["dependencies"]) for task in tasks)
        task_count = len(tasks)
        
        console.print(f"[green]All dependencies are valid![/]")
        console.print(f"Found {dep_count} dependencies across {task_count} tasks.")
        return
    
    console.print(f"[red]Found {len(errors)} dependency issues:[/]")
    for error in errors:
        console.print(f"- {error}")
    
    console.print("\nRun [blue]focus_forge.py fix-dependencies[/] to automatically fix these issues.")


@cli.command()
def fix_dependencies() -> None:
    """Fix invalid dependencies between tasks."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found.[/]")
        return
    
    # Get all valid task IDs
    all_task_ids = {task["id"] for task in tasks}
    
    # Track fixes
    fixed_tasks = []
    removed_deps = []
    
    # Check each task
    for task in tasks:
        original_deps = task["dependencies"].copy()
        
        # Filter out invalid dependencies
        task["dependencies"] = [
            dep_id for dep_id in task["dependencies"]
            if dep_id in all_task_ids and dep_id != task["id"]
        ]
        
        # Check if anything was fixed
        if task["dependencies"] != original_deps:
            fixed_tasks.append(task["id"])
            removed = set(original_deps) - set(task["dependencies"])
            for dep in removed:
                removed_deps.append((task["id"], dep))
    
    if not fixed_tasks:
        console.print(f"[green]No dependency issues found![/]")
        return
    
    # Save fixed tasks
    save_tasks(tasks)
    generate_task_files(tasks)
    
    # Report fixes
    console.print(f"[green]Fixed dependencies in {len(fixed_tasks)} tasks:[/]")
    for task_id in fixed_tasks:
        console.print(f"- Task {task_id}")
    
    if removed_deps:
        console.print("\n[yellow]Removed invalid dependencies:[/]")
        for task_id, dep_id in removed_deps:
            console.print(f"- Removed {dep_id} from task {task_id}")
    
    console.print(f"\nRun [blue]focus_forge.py validate-dependencies[/] to verify all issues are fixed.")


@cli.command()
@click.option("--from", "from_id", required=True, help="Task ID to update from")
@click.option("--prompt", required=True, help="Prompt to update tasks")
def update(from_id: str, prompt: str) -> None:
    """Update tasks from a specific ID onward based on a prompt."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found.[/]")
        return
    
    # Find the task with the from_id
    task_index = -1
    for i, task in enumerate(tasks):
        if task["id"] == from_id:
            task_index = i
            break
    
    if task_index == -1:
        console.print(f"[red]Error:[/] Task {from_id} not found.")
        return
    
    # Get tasks to update (all tasks from from_id onward)
    tasks_to_update = tasks[task_index:]
    
    # Filter out tasks that are already completed
    tasks_to_update = [task for task in tasks_to_update if task["status"] != "done"]
    
    if not tasks_to_update:
        console.print(f"[yellow]No tasks to update. All tasks from ID {from_id} are already completed.[/]")
        return
    
    # Load configuration
    config = load_config()
    
    # Check API key
    if not config["api_key"]:
        api_key = click.prompt("Anthropic API key (or set ANTHROPIC_API_KEY env var)",
                              default="", hide_input=True, show_default=False)
        if not api_key:
            console.print("[red]Error:[/] API key is required.")
            return
        config["api_key"] = api_key
        save_config(config)
    
    # Generate new tasks
    console.print(f"[bold]Updating {len(tasks_to_update)} tasks based on the prompt...[/]")
    
    # Prepare the prompt for Claude
    tasks_json = json.dumps([{
        "id": t["id"],
        "title": t["title"],
        "description": t["description"],
        "dependencies": t["dependencies"],
        "details": t["details"],
        "test_strategy": t["test_strategy"]
    } for t in tasks_to_update], indent=2)
    
    claude_prompt = f"""
You are a task updater for development projects.
I need to update a series of tasks based on a new context or change in requirements.

Here are the original tasks:
```json
{tasks_json}
```

The change or new context is:
{prompt}

Please provide updated versions of these tasks, reflecting the changes described.
Maintain the same task IDs and dependencies, but update the title, description, 
implementation details, and test strategy as needed.

Format the output as JSON that can be parsed programmatically, using the same structure as the input.
Keep the same number of tasks, with the same IDs.

Respond ONLY with the JSON array. No explanations or additional text.
"""
    
    # Use console directly without spinner for testing
    response = call_claude_api(
        prompt=claude_prompt, 
        api_key=config["api_key"],
        model=config["model"],
        max_tokens=config["max_tokens"],
        temperature=config["temperature"]
    )
    
    if not response:
        console.print("[red]Failed to update tasks.[/]")
        return
    
    # Extract JSON from response
    try:
        json_match = re.search(r'(\[\s*{.*}\s*\])', response, re.DOTALL)
        if json_match:
            response = json_match.group(1)
        
        updated_tasks = json.loads(response)
        
        # Update tasks
        for i, updated_task in enumerate(updated_tasks):
            original_task = tasks_to_update[i]
            
            # Preserve fields that shouldn't change
            updated_task["status"] = original_task["status"]
            updated_task["priority"] = original_task["priority"]
            updated_task["subtasks"] = original_task["subtasks"]
            
            # Update the original task in the tasks list
            for j, task in enumerate(tasks):
                if task["id"] == updated_task["id"]:
                    tasks[j] = updated_task
                    break
        
        # Save the updated tasks
        save_tasks(tasks)
        
        # Regenerate task files
        generate_task_files(tasks)
        
        console.print(f"[green]Successfully updated {len(updated_tasks)} tasks![/]")
        console.print(f"Use [blue]focus_forge.py list[/] to see the updated tasks.")
        
    except json.JSONDecodeError as e:
        console.print(f"[red]Error decoding JSON from Claude's response:[/] {str(e)}")
        console.print("[yellow]Response:[/]")
        console.print(response)
    except Exception as e:
        console.print(f"[red]Error processing updated tasks:[/] {str(e)}")


@cli.command()
def readme() -> None:
    """Show README with usage information."""
    readme_text = """
FOCUS FORGE
===========

A task management system for development projects.

USAGE
-----

Initialize a new project:
  ./focus_forge.py init

List all tasks:
  ./focus_forge.py list

Show the next task to work on:
  ./focus_forge.py next

Show details of a specific task:
  ./focus_forge.py show <task_id>

Set the status of a task:
  ./focus_forge.py set-status --id=<task_id> --status=<status>

Parse a PRD and generate tasks:
  ./focus_forge.py parse-prd --input=<prd_file> --tasks=<num_tasks>

Generate task files:
  ./focus_forge.py generate

Expand a task into subtasks:
  ./focus_forge.py expand --id=<task_id> --num=<num_subtasks>

Clear subtasks from tasks:
  ./focus_forge.py clear-subtasks --id=<task_id>
  ./focus_forge.py clear-subtasks --all

Add a dependency to a task:
  ./focus_forge.py add-dependency --id=<task_id> --depends-on=<dependency_id>

Remove a dependency from a task:
  ./focus_forge.py remove-dependency --id=<task_id> --depends-on=<dependency_id>

Validate dependencies:
  ./focus_forge.py validate-dependencies

Fix invalid dependencies:
  ./focus_forge.py fix-dependencies

Update tasks based on new context:
  ./focus_forge.py update --from=<task_id> --prompt="New context or changes"

Generate nix-shell configuration:
  ./focus_forge.py nix-config

For more information on any command:
  ./focus_forge.py <command> --help
"""
    
    console.print(Panel(readme_text, title="Focus Forge README"))


@cli.command()
@click.option("--output", default="default.nix", help="Output file for nix-shell configuration")
def nix_config(output: str) -> None:
    """Generate nix-shell configuration for Focus Forge."""
    nix_config = """{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    python310
    python310Packages.click
    python310Packages.rich
    python310Packages.requests
  ];
  
  shellHook = ''
    alias focus-forge="python ${./focus_forge.py}"
    
    echo ""
    echo "Focus Forge nix-shell environment activated!"
    echo "Run 'focus-forge' to use the tool."
    echo "For help, run 'focus-forge --help'"
    echo ""
  '';
}
"""
    
    with open(output, 'w') as f:
        f.write(nix_config)
    
    console.print(f"[green]Generated nix-shell configuration: {output}[/]")
    console.print("To use it, run:")
    console.print(f"  [blue]nix-shell {output}[/]")


@cli.command()
def ai_guidance_status() -> None:
    """Show status of AI guidance integration."""
    has_ai, has_claude = check_ai_guidance_folders()
    
    console.print(Panel(
        f"[bold]AI Guidance Status[/]\n\n"
        f".ai folder: {'[green]Found[/]' if has_ai else '[red]Not found[/]'}\n"
        f".claude folder: {'[green]Found[/]' if has_claude else '[red]Not found[/]'}\n"
        f"Butterbot integration: {'[green]Available[/]' if has_butterbot_integration() else '[red]Not available[/]'}\n",
        title="Focus Forge AI Guidance", 
        border_style="blue"
    ))
    
    if has_butterbot_integration():
        console.print("\n[bold]Available AI Guidance Resources:[/]")
        for context_type in ["patterns", "standards", "workflows", "security"]:
            context = get_butterbot_context(context_type)
            if context:
                console.print(f"- [green]{context_type.capitalize()}[/]: Available")
            else:
                console.print(f"- [yellow]{context_type.capitalize()}[/]: Not found")


@cli.command()
@click.option("--output", default="prd_parser_prd.md", help="Output file for the PRD")
def create_prd_parser_prd(output: str) -> None:
    """Create a sample PRD for turning PRDs into tasks with dependencies."""
    prd_content = """# PRD Parser Enhancement Specification

## Overview
The PRD Parser module will be enhanced to better analyze Product Requirement Documents and automatically generate a comprehensive set of development tasks with appropriate dependencies. This enhancement will focus on identifying relationships between features, determining the correct sequence of development tasks, and ensuring that all components and requirements are properly captured.

## Goals
- Improve the identification of task dependencies in PRD documents
- Generate tasks with more accurate dependency chains
- Identify architectural components and their relationships
- Support complex task hierarchies and subtask generation
- Ensure no critical development tasks are overlooked

## Features

### 1. Natural Language Relationship Detection
The system will analyze PRD text to detect statements indicating dependencies between components:
- "X depends on Y" or "Y is required for X" patterns
- Sequential indicators like "after," "before," "following," "prior to"
- Conditional relationships such as "only if," "requires," "necessitates"
- Time-based dependencies using phrases like "first," "then," "finally"

### 2. Component Detection and Grouping
- Identify system components (services, modules, libraries)
- Detect feature groups that should be developed together
- Recognize infrastructure requirements that must be in place before feature development

### 3. Development Sequence Analysis
- Determine logical implementation order based on technical constraints
- Identify foundation systems that must be built first
- Distinguish between backend and frontend dependencies
- Recognize data model dependencies

### 4. Priority and Complexity Inference
- Detect language indicating criticality or importance
- Identify scope indicators suggesting task complexity
- Recognize time-based requirements affecting priority
- Understand customer/user impact levels

### 5. Visualization and Validation
- Generate a dependency graph visualization for review
- Provide confidence scores for detected relationships
- Allow manual adjustment of detected dependencies
- Validate circular dependency detection

## Technical Requirements

### Algorithm Requirements
- Use sophisticated NLP techniques to analyze relationship phrases
- Implement a dependency graph builder
- Design a task ordering algorithm respecting all dependencies
- Create a validation system to check for completeness

### Integration Points
- Must work with the existing task management system
- Should integrate with the current PRD parsing command
- Must generate tasks in the established JSON format
- Should respect existing dependency validation mechanisms

### Performance Requirements
- The system should analyze PRDs up to 50 pages in under 60 seconds
- Generated task lists should include appropriate dependency chains
- Edge cases like circular dependencies must be detected and reported

## Success Metrics
- 90% or higher accuracy in dependency detection
- Reduction in manual task dependency setup by 75%
- Improved task completion flow with fewer dependency-related blockages
- User rating of task dependency quality (4.5/5 or higher)

## Timeline
- Design and specification: 1 week
- Algorithm development: 2 weeks
- Integration with existing system: 1 week
- Testing and validation: 1 week
- Documentation and deployment: 3 days

## Appendix: Example PRD Input/Output Patterns
The system should recognize various formulations of requirements and their implicit dependencies, such as:

```
Input PRD excerpt:
"The user authentication system must be completed before implementing the permission-based access controls. Once the access controls are in place, we can proceed with developing the content management features. The dashboard should only display content that the user has permission to view."

Expected task dependencies:
1. Implement user authentication system
2. Implement permission-based access controls (depends on #1)
3. Develop content management features (depends on #2)
4. Create user dashboard with permission filtering (depends on #2 and #3)
```
"""
    
    with open(output, 'w') as f:
        f.write(prd_content)
    
    console.print(f"[green]Generated PRD Parser specification: {output}[/]")
    console.print("To use it, run:")
    console.print(f"  [blue]./focus_forge.py parse-prd --input={output}[/]")


@cli.command()
@click.option("--output", default="dependency_graph.dot", help="Output DOT file")
@click.option("--format", type=click.Choice(["dot", "png"]), default="dot", help="Output format")
def dependency_graph(output: str, format: str) -> None:
    """Generate a dependency graph visualization for tasks."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found. Run 'init' or 'parse-prd' to create tasks.[/]")
        return
    
    # Import the dependency graph module
    try:
        from .dependency_graph import export_dependency_graph_dot
    except ImportError:
        console.print("[red]Error:[/] Could not import dependency graph module.")
        console.print("Make sure the dependency_graph.py file is in the correct location.")
        return
    
    # Generate DOT file
    dot_file = output if format == "dot" or not output.endswith(".png") else output.replace(".png", ".dot")
    export_dependency_graph_dot(tasks, dot_file)
    console.print(f"[green]Dependency graph exported to {dot_file}[/]")
    
    # Generate PNG if requested
    if format == "png":
        png_file = output if output.endswith(".png") else output.replace(".dot", ".png")
        try:
            import subprocess
            subprocess.run(["dot", "-Tpng", "-o", png_file, dot_file], check=True, stderr=subprocess.PIPE)
            console.print(f"[green]PNG visualization saved to {png_file}[/]")
        except (subprocess.SubprocessError, FileNotFoundError):
            console.print("[yellow]Note:[/] Install Graphviz to generate PNG visualizations (apt-get install graphviz)")
            console.print(f"You can manually convert the DOT file with: dot -Tpng -o {png_file} {dot_file}")


def merge_tasks(existing_tasks: List[Task], new_tasks: List[Task]) -> List[Task]:
    """
    Merge new tasks with existing tasks, preserving completed tasks.
    
    This function ensures that:
    1. Existing tasks are preserved (especially completed ones)
    2. New tasks are added to the task list
    3. Task IDs don't conflict
    
    Args:
        existing_tasks: List of existing tasks
        new_tasks: List of new tasks to merge
        
    Returns:
        List[Task]: Merged task list
    """
    # Create a map of existing task IDs for quick lookup
    existing_task_ids = {task["id"]: task for task in existing_tasks}
    
    # Find the highest task ID to ensure new tasks get unique IDs
    highest_id = 0
    for task_id in existing_task_ids.keys():
        try:
            # Handle both numeric and T{num} format IDs
            if task_id.startswith('T'):
                id_num = int(task_id[1:])
            else:
                id_num = int(task_id)
            highest_id = max(highest_id, id_num)
        except ValueError:
            # Skip non-numeric IDs
            pass
    
    merged_tasks = existing_tasks.copy()
    
    # Process new tasks
    for new_task in new_tasks:
        # Check if this task already exists
        if new_task["id"] in existing_task_ids:
            # If it exists but is not done, update some fields but preserve status
            existing_task = existing_task_ids[new_task["id"]]
            if existing_task["status"] != "done":
                # Update fields except status
                for field in ["title", "description", "priority", "dependencies", "details", "test_strategy"]:
                    if field in new_task:
                        existing_task[field] = new_task[field]
        else:
            # If the task is new, ensure it has a unique ID if needed
            if any(t["id"] == new_task["id"] for t in merged_tasks):
                # Generate a new unique ID
                highest_id += 1
                if new_task["id"].startswith('T'):
                    new_task["id"] = f"T{highest_id}"
                else:
                    new_task["id"] = str(highest_id)
            
            # Add the new task
            merged_tasks.append(new_task)
    
    return merged_tasks


@cli.command()
@click.option("--output", default=None, help="Output file for the sequence (JSON format)")
def sequence(output: Optional[str]) -> None:
    """Generate an optimized implementation sequence for tasks."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found. Run 'init' or 'parse-prd' to create tasks.[/]")
        return
    
    # Import the sequence analysis module
    try:
        from .sequence_analysis import get_optimized_sequence, get_critical_path
    except ImportError:
        console.print("[red]Error:[/] Could not import sequence analysis module.")
        console.print("Make sure the sequence_analysis.py file is in the correct location.")
        return
    
    # Get optimized sequence
    console.print("[bold]Generating optimized implementation sequence...[/]")
    sequence = get_optimized_sequence(tasks)
    
    # Display the sequence
    table = Table(title="Optimized Implementation Sequence")
    table.add_column("Order", justify="right", style="cyan")
    table.add_column("ID", justify="right")
    table.add_column("Title")
    table.add_column("Status")
    table.add_column("Priority")
    
    for i, task in enumerate(sequence):
        status_text = colorize_status(task["status"])
        priority_text = colorize_priority(task["priority"])
        
        table.add_row(
            str(i+1), 
            task["id"], 
            task["title"], 
            str(status_text), 
            str(priority_text)
        )
    
    console.print(table)
    
    # Get critical path
    critical_path = get_critical_path(tasks)
    console.print("\n[bold]Critical Path (determines project timeline):[/]")
    
    for i, task in enumerate(critical_path):
        status = "✅" if task["status"] == "done" else "⏺️"
        console.print(f"{i+1}. {status} [cyan]{task['id']}:[/] {task['title']}")
    
    # Export to file if requested
    if output:
        sequence_data = {
            "optimized_sequence": [task["id"] for task in sequence],
            "critical_path": [task["id"] for task in critical_path],
            "generated_at": datetime.now().isoformat(),
            "version": VERSION
        }
        
        with open(output, 'w') as f:
            json.dump(sequence_data, f, indent=2)
        
        console.print(f"\n[green]Sequence data exported to {output}[/]")


@cli.command()
def parallel_tasks() -> None:
    """Identify tasks that can be implemented in parallel."""
    tasks = load_tasks()
    
    if not tasks:
        console.print("[yellow]No tasks found. Run 'init' or 'parse-prd' to create tasks.[/]")
        return
    
    # Import the sequence analysis module
    try:
        from .sequence_analysis import get_parallel_task_groups
    except ImportError:
        console.print("[red]Error:[/] Could not import sequence analysis module.")
        console.print("Make sure the sequence_analysis.py file is in the correct location.")
        return
    
    # Get parallel task groups
    console.print("[bold]Identifying tasks that can be implemented in parallel...[/]")
    parallel_groups = get_parallel_task_groups(tasks)
    
    # Display the groups
    for i, group in enumerate(parallel_groups):
        console.print(f"\n[bold cyan]Group {i+1}[/] (tasks that can be worked on in parallel):")
        
        table = Table(show_header=True)
        table.add_column("ID", justify="right")
        table.add_column("Title")
        table.add_column("Status")
        table.add_column("Priority")
        
        for task in group:
            status_text = colorize_status(task["status"])
            priority_text = colorize_priority(task["priority"])
            
            table.add_row(
                task["id"], 
                task["title"], 
                str(status_text), 
                str(priority_text)
            )
        
        console.print(table)


@cli.command()
@click.option("--input", required=True, type=str, help="Path to PRD file")
@click.option("--output", required=True, type=str, help="Path to output file")
@click.option("--config-file", type=str, help="Path to config file")
@click.option("--verbose", is_flag=True, help="Enable verbose output")
@click.option("--fail", is_flag=True, help="Fail if validation fails")
def validate_prd_tasks(input: str, output: str, config_file: str, verbose: bool, fail: bool) -> None:
    """Validate tasks against a PRD."""
    # ... existing code ...
    
    # Create tasks directory for reports if needed
    get_tasks_dir_path().mkdir(exist_ok=True)
    
    # ... existing code ...


# Main entry point
if __name__ == "__main__":
    # Import and register doc discovery CLI commands
    try:
        from focus_forge.cli_extensions import register_commands
        register_commands(cli)
    except ImportError:
        # If the focus_forge module isn't installed, try local import
        try:
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
            from focus_forge.cli_extensions import register_commands
            register_commands(cli)
        except ImportError:
            # If still can't import, just continue without the extensions
            pass
    
    # Run the CLI
    cli()
