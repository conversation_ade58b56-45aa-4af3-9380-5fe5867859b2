#!/usr/bin/env python3
"""
Dependency graph implementation for focus_forge.

This module provides a graph-based structure for managing task dependencies,
detecting cycles, and generating proper task execution order.
"""
from enum import Enum, auto
from dataclasses import dataclass
from typing import Dict, List, Set, Optional, Tuple, Iterator, Any
import json
from pathlib import Path


class DependencyType(Enum):
    """Types of dependencies between tasks."""
    DEPENDS_ON = auto()   # Task A depends on task B to be completed first
    REQUIRES = auto()     # Task A requires a component from task B
    ENABLES = auto()      # Task A enables task B to be started


@dataclass
class Dependency:
    """Represents a dependency between two tasks."""
    source_id: str
    target_id: str
    dep_type: DependencyType
    description: Optional[str] = None
    
    def __str__(self) -> str:
        """String representation of the dependency."""
        type_str = self.dep_type.name.lower().replace('_', ' ')
        return f"{self.source_id} {type_str} {self.target_id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert dependency to dictionary for serialization."""
        return {
            "source_id": self.source_id,
            "target_id": self.target_id,
            "type": self.dep_type.name,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Dependency':
        """Create dependency from dictionary."""
        return cls(
            source_id=data["source_id"],
            target_id=data["target_id"],
            dep_type=DependencyType[data["type"]],
            description=data.get("description")
        )


class DependencyGraph:
    """A graph structure to manage task dependencies."""
    
    def __init__(self):
        """Initialize an empty dependency graph."""
        self.nodes: Set[str] = set()
        self.edges: Dict[str, List[Dependency]] = {}
        self.reverse_edges: Dict[str, List[Dependency]] = {}
    
    def add_node(self, node_id: str) -> None:
        """Add a node to the graph."""
        if node_id not in self.nodes:
            self.nodes.add(node_id)
            self.edges[node_id] = []
            self.reverse_edges[node_id] = []
    
    def add_dependency(self, dep: Dependency) -> bool:
        """
        Add a dependency to the graph.
        
        Args:
            dep: The dependency to add
            
        Returns:
            bool: True if added successfully, False if it would create a cycle
        """
        # Add nodes if they don't exist
        self.add_node(dep.source_id)
        self.add_node(dep.target_id)
        
        # Check for self-dependency
        if dep.source_id == dep.target_id:
            return False
        
        # Check if adding this dependency would create a cycle
        if self.would_create_cycle(dep.source_id, dep.target_id):
            return False
        
        # Add the dependency
        self.edges[dep.source_id].append(dep)
        self.reverse_edges[dep.target_id].append(dep)
        return True
    
    def remove_dependency(self, source_id: str, target_id: str) -> bool:
        """
        Remove a dependency from the graph.
        
        Args:
            source_id: ID of the source node
            target_id: ID of the target node
            
        Returns:
            bool: True if removed successfully, False if not found
        """
        if source_id not in self.nodes or target_id not in self.nodes:
            return False
        
        # Find and remove the dependency
        removed = False
        self.edges[source_id] = [
            dep for dep in self.edges[source_id] 
            if dep.target_id != target_id or removed or (removed := True, False)[1]
        ]
        
        self.reverse_edges[target_id] = [
            dep for dep in self.reverse_edges[target_id]
            if dep.source_id != source_id
        ]
        
        return removed
    
    def would_create_cycle(self, from_id: str, to_id: str) -> bool:
        """
        Check if adding a dependency from from_id to to_id would create a cycle.
        
        Args:
            from_id: ID of the source node
            to_id: ID of the target node
            
        Returns:
            bool: True if it would create a cycle, False otherwise
        """
        # If there's already a path from to_id to from_id, adding an edge
        # from from_id to to_id would create a cycle
        return self.has_path(to_id, from_id)
    
    def has_path(self, from_id: str, to_id: str) -> bool:
        """
        Check if there is a path from from_id to to_id in the graph.
        
        Args:
            from_id: ID of the source node
            to_id: ID of the target node
            
        Returns:
            bool: True if a path exists, False otherwise
        """
        if from_id not in self.nodes or to_id not in self.nodes:
            return False
        
        visited = set()
        
        def dfs(node: str) -> bool:
            if node == to_id:
                return True
            
            if node in visited:
                return False
            
            visited.add(node)
            
            for dep in self.edges.get(node, []):
                if dfs(dep.target_id):
                    return True
            
            return False
        
        return dfs(from_id)
    
    def get_dependencies(self, node_id: str) -> List[Dependency]:
        """
        Get all dependencies where node_id is the source.
        
        Args:
            node_id: ID of the node
            
        Returns:
            List[Dependency]: List of dependencies
        """
        if node_id not in self.nodes:
            return []
        
        return self.edges.get(node_id, [])
    
    def get_dependents(self, node_id: str) -> List[Dependency]:
        """
        Get all dependencies where node_id is the target.
        
        Args:
            node_id: ID of the node
            
        Returns:
            List[Dependency]: List of dependencies
        """
        if node_id not in self.nodes:
            return []
        
        return self.reverse_edges.get(node_id, [])
    
    def get_roots(self) -> Set[str]:
        """
        Get all nodes that don't depend on any other nodes.
        
        Returns:
            Set[str]: Set of root node IDs
        """
        return {node for node in self.nodes if not self.edges.get(node, [])}
    
    def get_leaves(self) -> Set[str]:
        """
        Get all nodes that no other nodes depend on.
        
        Returns:
            Set[str]: Set of leaf node IDs
        """
        return {node for node in self.nodes if not self.reverse_edges.get(node, [])}
    
    def get_nodes(self) -> Set[str]:
        """
        Get all nodes in the graph.
        
        Returns:
            Set[str]: Set of all node IDs
        """
        return self.nodes.copy()
    
    def find_connected_nodes(self, start_node: str) -> Set[str]:
        """
        Find all nodes connected to the start node (in either direction).
        
        Args:
            start_node: ID of the starting node
            
        Returns:
            Set[str]: Set of connected node IDs
        """
        if start_node not in self.nodes:
            return set()
        
        connected = set()
        queue = [start_node]
        
        while queue:
            node = queue.pop(0)
            if node in connected:
                continue
                
            connected.add(node)
            
            # Add targets of outgoing edges
            for dep in self.edges.get(node, []):
                if dep.target_id not in connected:
                    queue.append(dep.target_id)
            
            # Add sources of incoming edges
            for dep in self.reverse_edges.get(node, []):
                if dep.source_id not in connected:
                    queue.append(dep.source_id)
        
        return connected
    
    def find_longest_path(self, start_node: str) -> List[str]:
        """
        Find the longest path starting from the given node.
        
        Args:
            start_node: ID of the starting node
            
        Returns:
            List[str]: List of node IDs in the longest path
        """
        if start_node not in self.nodes:
            return []
        
        # Use dynamic programming to find the longest path
        memo = {}
        
        def dfs(node: str) -> List[str]:
            if node in memo:
                return memo[node]
            
            longest_path = [node]
            
            for dep in self.edges.get(node, []):
                target_path = dfs(dep.target_id)
                if len(target_path) + 1 > len(longest_path):
                    longest_path = [node] + target_path
            
            memo[node] = longest_path
            return longest_path
        
        return dfs(start_node)
    
    def find_cycles(self) -> List[List[str]]:
        """
        Find cycles in the graph.
        
        Returns:
            List[List[str]]: List of cycles, where each cycle is a list of node IDs
        """
        cycles = []
        visited = set()
        rec_stack = set()
        
        def dfs(node: str, path: List[str]) -> None:
            if node in rec_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycles.append(path[cycle_start:] + [node])
                return
                
            if node in visited:
                return
                
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for dep in self.edges.get(node, []):
                dfs(dep.target_id, path.copy())
            
            # Remove from recursion stack when backtracking
            rec_stack.remove(node)
        
        for node in self.nodes:
            if node not in visited:
                dfs(node, [])
        
        return cycles
    
    def topological_sort(self) -> Optional[List[str]]:
        """
        Perform a topological sort of the graph.
        
        Returns:
            Optional[List[str]]: Sorted list of node IDs, or None if a cycle exists
        """
        if self.find_cycles():
            return None
        
        result = []
        visited = set()
        
        def visit(node: str) -> None:
            if node in visited:
                return
            
            visited.add(node)
            
            for dep in self.edges.get(node, []):
                visit(dep.target_id)
            
            result.append(node)
        
        for node in self.nodes:
            if node not in visited:
                visit(node)
        
        # The result is in reverse order
        return list(reversed(result))
    
    def to_dot(self) -> str:
        """
        Generate a DOT file representation of the graph for visualization.
        
        Returns:
            str: DOT file contents
        """
        lines = ["digraph DependencyGraph {", "  rankdir=LR;", "  node [shape=box, style=filled, fillcolor=lightblue];", ""]
        
        # Add nodes
        for node in self.nodes:
            safe_node_id = node.replace("-", "_").replace(".", "_")
            lines.append(f'  {safe_node_id} [label="{node}"];')
        
        lines.append("")
        
        # Add edges
        for source, deps in self.edges.items():
            safe_source = source.replace("-", "_").replace(".", "_")
            for dep in deps:
                safe_target = dep.target_id.replace("-", "_").replace(".", "_")
                label = dep.dep_type.name.lower()
                if dep.description:
                    label += f" ({dep.description})"
                lines.append(f'  {safe_source} -> {safe_target} [label="{label}"];')
        
        lines.append("}")
        return "\n".join(lines)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert graph to dictionary for serialization.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the graph
        """
        dependencies = []
        for source, deps in self.edges.items():
            for dep in deps:
                dependencies.append(dep.to_dict())
        
        return {
            "nodes": list(self.nodes),
            "dependencies": dependencies
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DependencyGraph':
        """
        Create graph from dictionary.
        
        Args:
            data: Dictionary representation of the graph
            
        Returns:
            DependencyGraph: Created graph
        """
        graph = cls()
        
        # Add all nodes
        for node in data.get("nodes", []):
            graph.add_node(node)
        
        # Add all dependencies
        for dep_data in data.get("dependencies", []):
            dep = Dependency.from_dict(dep_data)
            graph.add_dependency(dep)
        
        return graph
    
    def save_to_file(self, file_path: str) -> None:
        """
        Save graph to file.
        
        Args:
            file_path: Path to save to
        """
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'DependencyGraph':
        """
        Load graph from file.
        
        Args:
            file_path: Path to load from
            
        Returns:
            DependencyGraph: Loaded graph
        """
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        return cls.from_dict(data)


def build_graph_from_tasks(tasks: List[Dict[str, Any]]) -> DependencyGraph:
    """
    Build a dependency graph from a list of tasks.
    
    Args:
        tasks: List of task dictionaries
        
    Returns:
        DependencyGraph: Dependency graph
    """
    graph = DependencyGraph()
    
    # Add all tasks as nodes
    for task in tasks:
        graph.add_node(task["id"])
    
    # Add dependencies
    for task in tasks:
        for dep_id in task.get("dependencies", []):
            # Create a dependency (source depends on target)
            dep = Dependency(
                source_id=task["id"],
                target_id=dep_id,
                dep_type=DependencyType.DEPENDS_ON,
                description=f"{task['id']} requires {dep_id} to be completed first"
            )
            graph.add_dependency(dep)
    
    return graph


def validate_task_dependencies(tasks: List[Dict[str, Any]]) -> List[str]:
    """
    Validate dependencies between tasks.
    
    Args:
        tasks: List of tasks to validate
        
    Returns:
        List[str]: List of error messages
    """
    errors = []
    
    # Build a dependency graph
    graph = build_graph_from_tasks(tasks)
    
    # Check for invalid dependencies (referencing nonexistent tasks)
    all_task_ids = {task["id"] for task in tasks}
    for task in tasks:
        for dep_id in task.get("dependencies", []):
            if dep_id not in all_task_ids:
                errors.append(f"Task {task['id']} depends on non-existent task {dep_id}")
    
    # Check for self-dependencies
    for task in tasks:
        if task["id"] in task.get("dependencies", []):
            errors.append(f"Task {task['id']} depends on itself")
    
    # Check for circular dependencies
    cycles = graph.find_cycles()
    for cycle in cycles:
        errors.append(f"Circular dependency detected: {' -> '.join(cycle)}")
    
    return errors


def get_execution_order(tasks: List[Dict[str, Any]]) -> Optional[List[str]]:
    """
    Get the execution order for tasks.
    
    Args:
        tasks: List of tasks
        
    Returns:
        Optional[List[str]]: Ordered list of task IDs, or None if circular dependencies exist
    """
    graph = build_graph_from_tasks(tasks)
    return graph.topological_sort()


def export_dependency_graph_dot(tasks: List[Dict[str, Any]], output_file: str) -> None:
    """
    Export dependency graph to DOT file for visualization.
    
    Args:
        tasks: List of tasks
        output_file: Path to output file
    """
    graph = build_graph_from_tasks(tasks)
    dot_content = graph.to_dot()
    
    with open(output_file, 'w') as f:
        f.write(dot_content)


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python dependency_graph.py tasks.json [output.dot]")
        sys.exit(1)
    
    tasks_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else "dependency_graph.dot"
    
    try:
        with open(tasks_file, 'r') as f:
            data = json.load(f)
            tasks = data.get("tasks", [])
        
        # Validate dependencies
        errors = validate_task_dependencies(tasks)
        if errors:
            print("Dependency validation errors:")
            for error in errors:
                print(f"- {error}")
            print()
        
        # Get execution order
        order = get_execution_order(tasks)
        if order:
            print("Execution order:")
            for i, task_id in enumerate(order):
                task = next((t for t in tasks if t["id"] == task_id), None)
                if task:
                    print(f"{i+1}. {task_id}: {task.get('title', 'Unknown')}")
            print()
        else:
            print("Cannot determine execution order due to circular dependencies.")
            print()
        
        # Export DOT file
        export_dependency_graph_dot(tasks, output_file)
        print(f"Dependency graph exported to {output_file}")
        print("To visualize, use: dot -Tpng -o dependency_graph.png dependency_graph.dot")
    
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1) 