#!/usr/bin/env python3
"""
NLP module for detecting relationship phrases in PRD documents.

This module implements Task T1: Implement NLP module for relationship phrase detection.
It provides functionality to identify dependency-indicating phrases in PRD text and
extract the entities involved in these relationships.
"""
from typing import List, Tuple, Dict, Set, Optional, Union
import re
import json
import os
from pathlib import Path


# Type definitions
Relationship = Tuple[str, str, str]  # (source, relationship_type, target)
RelationshipList = List[Relationship]


class RelationshipDetector:
    """
    Class for detecting dependency relationships in text.
    
    Provides methods to analyze PRD text and extract relationship information
    indicating dependencies between components, features, or tasks.
    """
    
    RELATIONSHIP_PATTERNS = {
        # Standard dependency indicators
        "depends_on": [
            r"(\w+[\w\s]+)\s+depends\s+on\s+(\w+[\w\s]+)",
            r"(\w+[\w\s]+)\s+is\s+dependent\s+on\s+(\w+[\w\s]+)",
            r"(\w+[\w\s]+)\s+has\s+a\s+dependency\s+on\s+(\w+[\w\s]+)",
        ],
        
        # Required for/by relationships
        "required_for": [
            r"(\w+[\w\s]+)\s+is\s+required\s+for\s+(\w+[\w\s]+)",
            r"(\w+[\w\s]+)\s+requires\s+(\w+[\w\s]+)",
            r"(\w+[\w\s]+)\s+is\s+necessary\s+for\s+(\w+[\w\s]+)",
        ],
        
        # Temporal relationships (before/after)
        "before": [
            r"(\w+[\w\s]+)\s+before\s+(\w+[\w\s]+)",
            r"(\w+[\w\s]+)\s+must\s+be\s+completed\s+before\s+(\w+[\w\s]+)",
            r"before\s+(\w+[\w\s]+),\s+(\w+[\w\s]+)\s+must\s+be\s+completed",
        ],
        
        "after": [
            r"(\w+[\w\s]+)\s+after\s+(\w+[\w\s]+)",
            r"after\s+(\w+[\w\s]+),\s+(\w+[\w\s]+)",
            r"once\s+(\w+[\w\s]+),\s+(\w+[\w\s]+)",
        ],
        
        # Other relationship types
        "enables": [
            r"(\w+[\w\s]+)\s+enables\s+(\w+[\w\s]+)",
            r"(\w+[\w\s]+)\s+allows\s+for\s+(\w+[\w\s]+)",
        ],
        
        "prerequisite": [
            r"(\w+[\w\s]+)\s+is\s+a\s+prerequisite\s+for\s+(\w+[\w\s]+)",
            r"(\w+[\w\s]+)\s+prerequisites\s+include\s+(\w+[\w\s]+)",
        ],
    }
    
    NEGATION_PATTERNS = [
        r"not\s+depend",
        r"no\s+dependency",
        r"independent\s+of",
        r"without\s+waiting\s+for",
        r"doesn't\s+require",
        r"does\s+not\s+require",
    ]
    
    def __init__(self, training_data_path: Optional[str] = None):
        """
        Initialize the relationship detector.
        
        Args:
            training_data_path: Path to training data file (optional)
        """
        self.training_data = []
        
        # Load training data if provided
        if training_data_path and os.path.exists(training_data_path):
            self._load_training_data(training_data_path)
    
    def _load_training_data(self, filepath: str) -> None:
        """
        Load training data from a JSON file.
        
        Args:
            filepath: Path to the training data file
        """
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
                if "examples" in data:
                    self.training_data = data["examples"]
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error loading training data: {e}")
    
    def detect_relationships(self, text: str) -> RelationshipList:
        """
        Detect dependency relationships in the given text.
        
        Args:
            text: The text to analyze
            
        Returns:
            List of identified relationships as (source, relationship_type, target) tuples
        """
        # This is a placeholder implementation that would be replaced with more
        # sophisticated NLP techniques in the real implementation
        
        # Check for negations first
        if any(re.search(pattern, text, re.IGNORECASE) for pattern in self.NEGATION_PATTERNS):
            # If negation is found, we need more sophisticated analysis to determine
            # if it applies to the relationship we're looking for
            # For now, we'll just return empty if any negation is found
            return []
        
        relationships = []
        
        # Apply pattern matching for each relationship type
        for rel_type, patterns in self.RELATIONSHIP_PATTERNS.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    if rel_type in ["depends_on", "required_for", "prerequisite"]:
                        # Normal direction
                        source, target = match.group(1).strip(), match.group(2).strip()
                        relationships.append((source, rel_type, target))
                    elif rel_type in ["before"]:
                        # Reversed dependency for "before" - if B before A, then A depends on B
                        source, target = match.group(2).strip(), match.group(1).strip()
                        relationships.append((source, "depends_on", target))
                    elif rel_type in ["after"]:
                        # Normal dependency for "after" - if A after B, then A depends on B
                        source, target = match.group(2).strip(), match.group(1).strip()
                        relationships.append((source, "depends_on", target))
                    else:
                        # Other relationships
                        source, target = match.group(1).strip(), match.group(2).strip()
                        relationships.append((source, rel_type, target))
        
        return relationships
    
    def evaluate_metrics(self, gold_standard: List[Dict]) -> Tuple[float, float, float]:
        """
        Evaluate the detector's precision, recall, and F1 score.
        
        Args:
            gold_standard: List of dictionaries with manually annotated examples
            
        Returns:
            Tuple of (precision, recall, f1_score)
        """
        if not gold_standard:
            return 0.0, 0.0, 0.0
        
        total_expected = 0
        total_predicted = 0
        total_correct = 0
        
        for example in gold_standard:
            text = example.get("text", "")
            expected_relationships = example.get("relationships", [])
            
            predicted_relationships = self.detect_relationships(text)
            
            total_expected += len(expected_relationships)
            total_predicted += len(predicted_relationships)
            
            # Count correct predictions
            for expected in expected_relationships:
                if expected in predicted_relationships:
                    total_correct += 1
        
        # Calculate metrics
        precision = total_correct / total_predicted if total_predicted > 0 else 0
        recall = total_correct / total_expected if total_expected > 0 else 0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        return precision, recall, f1
    
    def process_multi_entity_dependencies(self, text: str) -> RelationshipList:
        """
        Process dependencies involving multiple entities.
        
        Args:
            text: Text that potentially contains multi-entity dependencies
            
        Returns:
            List of relationships extracted from the text
        """
        # This would be implemented with more sophisticated NLP techniques
        # For now, a simple pattern matching approach
        relationships = []
        
        # Pattern to match "X requires A, B, and C"
        pattern = r"(\w+[\w\s]+)\s+requires\s+((?:\w+[\w\s]+,\s+)*(?:\w+[\w\s]+\s+and\s+)?(?:\w+[\w\s]+))"
        matches = re.finditer(pattern, text, re.IGNORECASE)
        
        for match in matches:
            source = match.group(1).strip()
            targets_text = match.group(2)
            
            # Split the targets
            if "and" in targets_text:
                parts = targets_text.split("and")
                last_target = parts[-1].strip()
                other_targets = parts[0].strip()
                
                if other_targets:
                    for target in other_targets.split(","):
                        target = target.strip()
                        if target:
                            relationships.append((source, "depends_on", target))
                
                if last_target:
                    relationships.append((source, "depends_on", last_target))
            else:
                for target in targets_text.split(","):
                    target = target.strip()
                    if target:
                        relationships.append((source, "depends_on", target))
        
        return relationships
    
    def analyze_prd(self, prd_text: str) -> Dict[str, List[Relationship]]:
        """
        Analyze a PRD document and extract all detected relationships.
        
        Args:
            prd_text: The PRD text to analyze
            
        Returns:
            Dictionary with categorized relationships
        """
        # Split the PRD into sentences/paragraphs for analysis
        paragraphs = [p.strip() for p in prd_text.split("\n\n") if p.strip()]
        sentences = []
        
        for paragraph in paragraphs:
            # Simple sentence splitting - would be improved in real implementation
            for sentence in re.split(r'(?<=[.!?])\s+', paragraph):
                if sentence.strip():
                    sentences.append(sentence.strip())
        
        # Process each sentence
        all_relationships = []
        for sentence in sentences:
            # Get regular dependencies
            relationships = self.detect_relationships(sentence)
            all_relationships.extend(relationships)
            
            # Get multi-entity dependencies
            multi_rels = self.process_multi_entity_dependencies(sentence)
            all_relationships.extend(multi_rels)
        
        # Categorize by relationship type
        categorized = {}
        for source, rel_type, target in all_relationships:
            if rel_type not in categorized:
                categorized[rel_type] = []
            categorized[rel_type].append((source, rel_type, target))
        
        return categorized


# Standalone function for direct use
def extract_relationships_from_file(filepath: str) -> Dict[str, List[Relationship]]:
    """
    Extract relationships from a file.
    
    Args:
        filepath: Path to the file containing PRD text
        
    Returns:
        Dictionary with categorized relationships
    """
    try:
        with open(filepath, 'r') as f:
            text = f.read()
        
        detector = RelationshipDetector()
        return detector.analyze_prd(text)
    except IOError as e:
        print(f"Error reading file: {e}")
        return {}


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        filepath = sys.argv[1]
        results = extract_relationships_from_file(filepath)
        
        print("Detected Relationships:")
        for rel_type, relationships in results.items():
            print(f"\n{rel_type.upper()}:")
            for source, _, target in relationships:
                print(f"  - {source} -> {target}")
    else:
        print("Usage: python nlp.py <prd_file>") 