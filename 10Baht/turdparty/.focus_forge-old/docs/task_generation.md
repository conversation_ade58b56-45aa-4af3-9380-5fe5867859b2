# Task Generation and Validation System

This document describes the task generation and validation system implemented as part of Task T7 "Develop Task Generation System". The system enables automatic generation of actionable development tasks from PRD (Product Requirements Document) text and validation of generated tasks for correctness, consistency, and completeness.

## Overview

The task generation and validation system consists of two main components:

1. **Task Generation Component** (`focus_forge/task_generation.py`): Converts PRD text into structured tasks with proper dependencies and priorities.
2. **Task Validation Component** (`focus_forge/task_validation.py`): Validates generated tasks for schema compliance, dependency correctness, and PRD requirement coverage.

These components integrate with the existing focus_forge infrastructure including the dependency graph, NLP relationship detection, and priority inference systems.

## Task Generation

### Key Classes

- **TaskGenerationConfig**: Configuration for task generation behavior
- **TaskTemplate**: Template structure for generating tasks
- **TaskType**: Enumeration of different task types (FOUNDATION, FEATURE, INTEGRATION, etc.)
- **TaskGenerator**: Main class that generates tasks from PRD text or component data

### Features

- **Template-Based Generation**: Uses templates for different task types to ensure consistent format and content
- **Component Detection**: Identifies system components from PRD text
- **Relationship Detection**: Detects dependencies between components
- **Priority Inference**: Determines task priority based on textual indicators
- **Subtask Generation**: Automatically breaks down tasks into logical subtasks
- **Sequence Optimization**: Uses sequence analysis to optimize task ordering

### Usage

```python
from focus_forge.task_generation import TaskGenerator, TaskGenerationConfig

# Create a task generator with custom configuration
config = TaskGenerationConfig(
    default_priority="medium",
    generate_subtasks=True,
    max_subtasks_per_task=3,
    id_prefix="T"
)
generator = TaskGenerator(config)

# Generate tasks from PRD text
tasks = generator.generate_tasks_from_prd(prd_text)

# Or generate from components directly
tasks = generator.generate_tasks_from_components(components)
```

### Custom Templates

You can define custom templates for different task types by creating a JSON file at `focus_forge/templates/task_templates.json`. Example template format:

```json
{
  "feature": {
    "title": "Implement {name} Feature",
    "description": "Develop the {name} feature as specified in the requirements.",
    "details": "Create the {name} feature with the specified functionality...",
    "test_strategy": "Create unit tests for components and integration tests...",
    "subtasks": [
      {
        "title": "Design {name} Feature Components",
        "description": "Design the components and interactions for the {name} feature."
      },
      {
        "title": "Implement {name} Business Logic",
        "description": "Develop the core business logic for the {name} feature."
      },
      {
        "title": "Create {name} User Interface",
        "description": "Implement the user interface for the {name} feature."
      }
    ]
  }
}
```

## Task Validation

### Key Classes

- **TaskValidator**: Main class for validating tasks and generating reports
- **TaskValidationError**: Exception for validation errors

### Features

- **Schema Validation**: Validates tasks against a defined schema
- **Dependency Consistency**: Checks for valid dependencies between tasks
- **Circular Dependency Detection**: Identifies circular dependencies in the task graph
- **PRD Coverage Analysis**: Checks how well tasks cover the requirements in the PRD
- **Quality Scoring**: Provides an overall quality score for generated tasks
- **Recommendations**: Generates recommendations for improving task quality

### Usage

```python
from focus_forge.task_validation import TaskValidator, validate_tasks_file

# Create a validator and validate a list of tasks
validator = TaskValidator()
report = validator.validate_and_report(tasks, prd_text)

# Or validate tasks from a file
report = validate_tasks_file("tasks.json", "prd.txt")

# Check validation results
if report["schema_compliance"]["is_compliant"]:
    print("Tasks comply with schema")
else:
    print(f"Found {report['schema_compliance']['num_errors']} schema errors")
    
print(f"Overall quality score: {report['overall_quality_score']}")
```

### Command-Line Usage

```bash
# Run task generation demo
python test_task_generation_prd.py

# Run validation on a specific file
python test_task_validation.py --file tasks.json --prd prd.txt

# Run all validation tests
python test_task_validation.py --all
```

## Integration with focus_forge

The task generation and validation system is fully integrated with the focus_forge command-line interface. The main integration points are:

1. **parse-prd command**: Uses task generation to create tasks from PRD text
2. **expand command**: Uses subtask generation functionality to break down tasks
3. **validation**: Task validation is used when importing or modifying tasks

## Implementation Details

### Task Generation Process

1. **PRD Analysis**: Split PRD into sections, extract components and relationships
2. **Component Categorization**: Categorize components as foundation, feature, integration, etc.
3. **Priority Determination**: Infer priority based on textual indicators
4. **Task Creation**: Generate a task for each component using appropriate templates
5. **Dependency Resolution**: Link tasks based on component dependencies
6. **Subtask Generation**: Create subtasks for each task using templates or generic phases
7. **Sequence Optimization**: Reorder tasks for optimal implementation sequence

### Task Validation Process

1. **Schema Validation**: Check task structure against the schema definition
2. **Dependency Validation**: Verify that dependencies are valid and consistent
3. **PRD Coverage Analysis**: Check how well tasks cover requirements in the PRD
4. **Quality Scoring**: Calculate an overall quality score based on validation results
5. **Report Generation**: Create a comprehensive validation report with recommendations

## Task Management Pattern Note

The task management system in Focus Forge requires explicit closing of parent tasks even when all subtasks are completed. This is a deliberate design choice to allow for final verification before marking a parent task complete. This ensures that the overall task objectives have been met beyond the completion of individual subtasks.

## Future Enhancements

- Enhanced PRD parsing with machine learning techniques
- More sophisticated dependency inference
- Effort estimation for tasks
- Integration with project planning tools
- Timeline generation based on dependencies and priorities
- Auto-completion of parent tasks when all subtasks are completed (optional and configurable) 