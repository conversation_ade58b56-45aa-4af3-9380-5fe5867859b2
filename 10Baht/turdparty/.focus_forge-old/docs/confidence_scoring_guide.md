# Confidence Scoring and Review System Guide

## Overview

The Confidence Scoring and Review System provides tools to analyze PRDs (Product Requirements Documents) and tasks to assess how well the tasks reflect the requirements. It identifies ambiguous language, detects potentially missing requirements, and provides suggestions for improvements.

This guide explains how to use the system effectively, interpret the results, and improve your PRD-to-task conversion process.

## Key Features

- **Clarity Score**: Measures how clear and unambiguous the PRD language is
- **Coverage Score**: Evaluates how well tasks cover the PRD requirements
- **Complexity Score**: Assesses the complexity of requirements
- **Overall Confidence Score**: Combines the above metrics to provide an overall confidence level
- **Ambiguity Detection**: Identifies vague or ambiguous language in requirements
- **Missing Requirement Detection**: Finds requirements that aren't adequately covered by tasks
- **Task Improvement Suggestions**: Recommends ways to improve task quality and coverage
- **Clarification Suggestions**: Offers specific suggestions for clarifying ambiguous requirements

## Using the CLI Tool

The confidence scoring functionality is available through a command-line interface (CLI) that provides two main commands:

1. `score`: Generate confidence scores for PRD and tasks
2. `review`: Review PRD and tasks with detailed suggestions

### Installation

The CLI tool is included in the Focus Forge nix-shell environment:

```bash
# Activate the nix-shell environment
nix-shell

# Run the confidence scorer
confidence-scorer --help
```

You can also run it directly:

```bash
python3 confidence_scoring_cli.py --help
```

### Basic Usage

#### Scoring a PRD and Tasks

To generate a basic confidence score for a PRD and associated tasks:

```bash
confidence-scorer score path/to/prd.md path/to/tasks.json
```

For more detailed output:

```bash
confidence-scorer score path/to/prd.md path/to/tasks.json -v
```

To save the report to a file:

```bash
confidence-scorer score path/to/prd.md path/to/tasks.json -o path/to/report.json
```

#### Reviewing a PRD and Tasks

To perform a comprehensive review with detailed suggestions:

```bash
confidence-scorer review path/to/prd.md path/to/tasks.json
```

To save the review interface data to a file:

```bash
confidence-scorer review path/to/prd.md path/to/tasks.json -o path/to/review.json
```

## Understanding the Scores

### Clarity Score (0.0 - 1.0)

The clarity score measures how clear and unambiguous the PRD language is.

- **0.8 - 1.0**: High clarity, requirements are well-defined and unambiguous
- **0.5 - 0.8**: Moderate clarity, some requirements may be ambiguous
- **0.0 - 0.5**: Low clarity, many requirements use ambiguous language

The system looks for ambiguous phrases like "might", "could", "should", "possibly", "consider", "maybe", etc., and prefers clear language like "must", "will", "shall", etc.

### Coverage Score (0.0 - 1.0)

The coverage score measures how well the tasks cover the requirements in the PRD.

- **0.8 - 1.0**: High coverage, most requirements are addressed by tasks
- **0.5 - 0.8**: Moderate coverage, some requirements may not be addressed
- **0.0 - 0.5**: Low coverage, many requirements are not addressed by tasks

### Complexity Score (0.0 - 1.0)

The complexity score measures the complexity of the requirements themselves.

- **0.0 - 0.3**: Low complexity, requirements are simple and straightforward
- **0.3 - 0.7**: Moderate complexity, some requirements may be challenging
- **0.7 - 1.0**: High complexity, many requirements are complex and intricate

Note that complexity itself is not necessarily bad, but it affects the difficulty of implementation and may require more detailed task breakdowns.

### Overall Confidence Score (0.0 - 1.0)

The overall confidence score combines clarity, coverage, and complexity scores to provide a single measure of confidence.

- **0.8 - 1.0**: High confidence, tasks likely reflect the PRD well
- **0.5 - 0.8**: Moderate confidence, some improvements may be needed
- **0.0 - 0.5**: Low confidence, significant improvements are recommended

## Interpreting Reports

### Ambiguous Requirements

The system identifies ambiguous requirements and lists them with their severity level. It considers phrases like "might", "could", "should", etc., as indicators of ambiguity.

Example output:

```
AMBIGUOUS REQUIREMENTS
==============================================
1. [MEDIUM] Multi-factor authentication might be implemented if time permits
   Ambiguous phrases: might, if time permits

2. [MEDIUM] Tasks could possibly be organized into projects
   Ambiguous phrases: could, possibly
```

### Missing Requirements

The system identifies requirements that don't appear to be covered by tasks. It analyzes the language of requirements and tasks to detect potential gaps.

Example output:

```
POTENTIALLY MISSING REQUIREMENTS
==============================================
1. [HIGH] System must export data in CSV format
   Possibly related tasks: T4

2. [MEDIUM] Tasks should be categorized by type
```

### Recommendations

The system provides recommendations based on the analysis, prioritized by importance.

Example output:

```
RECOMMENDATIONS
==============================================
1. [HIGH] Address potentially missing important requirements
   There are 2 important requirements that may not be adequately covered by tasks.

2. [MEDIUM] Consider clarifying moderately ambiguous requirements
   There are 8 moderately ambiguous requirements that may benefit from clarification.
```

## Best Practices

### Improving Clarity

To improve clarity in your PRD:

1. **Use Clear Language**: Replace ambiguous phrases like "should", "could", "might" with clear directives like "must", "shall", "will"
2. **Be Specific**: Include specific criteria, constraints, and acceptance criteria
3. **Quantify When Possible**: Use numbers instead of vague terms (e.g., "support 1000 concurrent users" instead of "support many users")
4. **Define Priorities**: Clearly indicate which requirements are essential vs. optional

### Improving Coverage

To improve task coverage:

1. **Cross-Check**: Ensure each requirement has at least one corresponding task
2. **Break Down Large Tasks**: Divide complex tasks into smaller, more manageable subtasks
3. **Include Details**: Add implementation details and acceptance criteria to tasks
4. **Link Explicitly**: Reference requirements in task descriptions when possible

### Handling Complexity

To handle complex requirements effectively:

1. **Identify Dependencies**: Map out dependencies between components
2. **Create Subtasks**: Break down complex requirements into smaller pieces
3. **Provide Context**: Include background information and rationale
4. **Consider Phasing**: Plan implementation in phases or iterations

## Example Workflow

1. **Write PRD**: Create your product requirements document
2. **Generate Tasks**: Create tasks based on the PRD
3. **Score Confidence**: Run `confidence-scorer score prd.md tasks.json -v`
4. **Review Issues**: Examine ambiguous requirements and missing coverage
5. **Get Suggestions**: Run `confidence-scorer review prd.md tasks.json`
6. **Update PRD**: Clarify ambiguous requirements
7. **Update Tasks**: Add or modify tasks to improve coverage
8. **Rescore**: Run the confidence scorer again to verify improvements

## Example: Task Management System

Let's walk through an example analysis of a simple Task Management System.

### PRD Excerpt:

```markdown
# Task Management System PRD

## Requirements

### User Authentication
1. Users must be able to register with email and password
2. Users must be able to log in with their credentials
3. The system should provide password reset functionality
4. Multi-factor authentication might be implemented if time permits

### Task Management
1. Users must be able to create new tasks
2. Tasks should have title, description, and due date
3. Users should be able to assign tasks to other users
```

### Sample Tasks:

```json
{
  "tasks": [
    {
      "id": "T1",
      "title": "Implement User Authentication",
      "description": "Create authentication system with login and registration",
      "details": "Implement a secure authentication system...",
      "subtasks": [
        { "id": "T1.1", "title": "Create User Registration" },
        { "id": "T1.2", "title": "Implement Login Functionality" }
      ]
    },
    {
      "id": "T2",
      "title": "Develop Task Creation",
      "description": "Implement functionality for creating and editing tasks",
      "details": "Create a system for users to add new tasks..."
    }
  ]
}
```

### Analysis Results:

- **Clarity Score**: 0.42 (Low, due to "should" and "might" phrases)
- **Coverage Score**: 0.82 (High, most requirements have corresponding tasks)
- **Complexity Score**: 0.35 (Moderate complexity)
- **Overall Confidence**: 0.63 (Moderate confidence)

### Improvement Suggestions:

1. Clarify ambiguous requirements:
   - "The system should provide password reset" → "The system shall provide password reset"
   - "Multi-factor authentication might be implemented" → "Multi-factor authentication will be implemented"

2. Add tasks for missing requirements:
   - Add a task for password reset functionality
   - Consider a task for multi-factor authentication

3. Add subtasks for better breakdown:
   - Break down T2 into more specific subtasks

## Conclusion

The Confidence Scoring and Review System helps you create clear requirements and comprehensive tasks. By identifying ambiguities, coverage gaps, and suggesting improvements, it enables you to enhance the quality of your PRD-to-task conversion process, leading to better software development outcomes.

For more detailed information, consult the API documentation in the source code comments. 