# Focus Forge: Time Tracking & Productivity Insights PRD

## 1. Overview

This document outlines the requirements for enhancing Focus Forge with comprehensive time tracking capabilities and productivity analytics. The goal is to provide developers with valuable insights into their work patterns, enabling them to optimize their productivity while maintaining a healthy work-life balance.

## 2. Product Objectives

1. Add native time tracking capabilities to Focus Forge's task management system
2. Integrate with external time tracking tools (primarily Wakatime) for automatic code time measurement
3. Provide meaningful productivity analytics to help developers understand their work patterns
4. Enable accurate estimation by comparing planned vs. actual time spent
5. Support popular productivity methodologies like the Pomodoro Technique
6. Generate insightful reports to track progress over time

## 3. Target Users

- Individual developers looking to improve their productivity
- Development teams tracking task completion times
- Engineering managers requiring visibility into project timelines
- Freelancers needing to track billable hours

## 4. Requirements

### 4.1 Task-Based Time Tracking

#### Functionality
- **F1.1**: Start timer for specific tasks with a simple command
  ```bash
  focus-forge start-timer --id=T1
  ```

- **F1.2**: Stop timer for current task with elapsed time saved to task metadata
  ```bash
  focus-forge stop-timer
  ```

- **F1.3**: Pause and resume timers to handle interruptions
  ```bash
  focus-forge pause-timer
  focus-forge resume-timer
  ```

- **F1.4**: Automatically stop timer when task status is changed to "done"

- **F1.5**: Manual time entry for tasks worked on outside the tracking system
  ```bash
  focus-forge add-time --id=T1 --minutes=45 --date="2025-03-25"
  ```

- **F1.6**: Timer persistence across terminal sessions

#### Time Data Model
```json
{
  "time_entries": [
    {
      "task_id": "T1",
      "start_time": "2025-03-25T14:35:22Z",
      "end_time": "2025-03-25T15:20:45Z",
      "duration_seconds": 2723,
      "notes": "Initial database setup",
      "tags": ["backend", "database"]
    }
  ],
  "total_time_spent_seconds": 2723
}
```

### 4.2 Wakatime Integration

#### Functionality
- **F2.1**: Configure Wakatime API key for authentication
  ```bash
  focus-forge config --wakatime-api-key=YOUR_API_KEY
  ```

- **F2.2**: Import coding time data from Wakatime API
  ```bash
  focus-forge import-wakatime --from="2025-03-20" --to="2025-03-25"
  ```

- **F2.3**: Associate Wakatime projects with Focus Forge tasks
  ```bash
  focus-forge link-wakatime --task-id=T1 --wakatime-project="authentication-service"
  ```

- **F2.4**: Automatic time attribution based on git branch names
  - If working on branch "feature/T1-user-auth", time is attributed to task T1

- **F2.5**: Manual override for Wakatime time attribution
  ```bash
  focus-forge reassign-time --wakatime-entry-id=12345 --task-id=T1
  ```

- **F2.6**: Periodic synchronization with Wakatime (configurable interval)

#### Integration Data Model
```json
{
  "wakatime_integration": {
    "api_key": "encrypted_key_here",
    "last_sync": "2025-03-25T15:20:45Z",
    "sync_interval_minutes": 60,
    "project_mappings": [
      {
        "wakatime_project": "authentication-service",
        "task_id": "T1"
      }
    ],
    "branch_pattern": "feature/{task_id}-*"
  }
}
```

### 4.3 Productivity Analytics

#### Functionality
- **F3.1**: Calculate and display productivity metrics
  ```bash
  focus-forge analytics --timeframe=week
  ```

- **F3.2**: Identify peak productivity hours based on completed task data

- **F3.3**: Track time spent by task status (pending, in-progress, done)

- **F3.4**: Generate heatmaps of activity by hour and day of week

- **F3.5**: Calculate focus metrics (time spent on single task vs. task switching)

- **F3.6**: Track time spent by task tag/category

#### Analytics Data Model
```json
{
  "productivity_metrics": {
    "calculated_at": "2025-03-25T15:20:45Z",
    "timeframe": "week",
    "total_hours": 38.5,
    "most_productive_day": "Wednesday",
    "most_productive_hour": 10,
    "focus_score": 0.82,
    "task_switching_frequency": 4.2,
    "time_by_category": {
      "backend": 18.2,
      "frontend": 12.3,
      "testing": 5.0,
      "meetings": 3.0
    }
  }
}
```

### 4.4 Estimation vs. Reality

#### Functionality
- **F4.1**: Add estimated time to task definition
  ```bash
  focus-forge set-estimate --id=T1 --hours=4
  ```

- **F4.2**: Compare estimated time vs. actual time spent
  ```bash
  focus-forge compare-estimates
  ```

- **F4.3**: Calculate estimation accuracy metrics over time

- **F4.4**: Generate estimation improvement suggestions based on historical data

- **F4.5**: Forecast completion time for upcoming tasks based on historical performance

#### Estimation Data Model
```json
{
  "estimates": {
    "task_id": "T1",
    "estimated_hours": 4.0,
    "actual_hours": 5.2,
    "accuracy_percentage": 76.9,
    "estimation_history": [
      {
        "date": "2025-02-25",
        "average_accuracy": 82.3
      }
    ]
  }
}
```

### 4.5 Pomodoro Technique Integration

#### Functionality
- **F5.1**: Start a Pomodoro session for a specific task
  ```bash
  focus-forge pomodoro --id=T1 --duration=25
  ```

- **F5.2**: Configure Pomodoro settings (work duration, break duration, cycles)
  ```bash
  focus-forge config --pomodoro-work=25 --pomodoro-break=5 --pomodoro-long-break=15
  ```

- **F5.3**: Track Pomodoro completion statistics

- **F5.4**: Notify user when Pomodoro work/break periods end

- **F5.5**: Automatic task time tracking during Pomodoro sessions

#### Pomodoro Data Model
```json
{
  "pomodoro_settings": {
    "work_minutes": 25,
    "break_minutes": 5,
    "long_break_minutes": 15,
    "cycles_before_long_break": 4
  },
  "pomodoro_statistics": {
    "total_completed": 38,
    "total_work_minutes": 950,
    "daily_average": 6.2,
    "completion_rate": 0.87
  }
}
```

### 4.6 Reporting

#### Functionality
- **F6.1**: Generate daily/weekly/monthly time reports
  ```bash
  focus-forge report --period=week --format=md
  ```

- **F6.2**: Export time data in various formats (CSV, JSON, Markdown)
  ```bash
  focus-forge export-time --from="2025-03-01" --to="2025-03-31" --format=csv
  ```

- **F6.3**: Create billable hours reports for client work
  ```bash
  focus-forge billable-report --client="ClientX" --from="2025-03-01" --to="2025-03-31"
  ```

- **F6.4**: Visual report generation with charts and graphs
  ```bash
  focus-forge visual-report --output=productivity.png
  ```

- **F6.5**: Team-level aggregated reports for managers
  ```bash
  focus-forge team-report --members=all
  ```

#### Report Templates
- Daily standup report
- Weekly progress report
- Monthly client billing report
- Productivity analysis report
- Team performance report

## 5. CLI Commands

### 5.1 Core Time Tracking Commands
```
focus-forge start-timer --id=<task_id> [--notes=<notes>] [--tags=<tag1,tag2>]
focus-forge stop-timer [--notes=<notes>]
focus-forge pause-timer
focus-forge resume-timer
focus-forge add-time --id=<task_id> --minutes=<duration> [--date=<YYYY-MM-DD>] [--notes=<notes>]
focus-forge current-timer
focus-forge clear-timer
```

### 5.2 Wakatime Integration Commands
```
focus-forge import-wakatime [--from=<YYYY-MM-DD>] [--to=<YYYY-MM-DD>]
focus-forge link-wakatime --task-id=<task_id> --wakatime-project=<project_name>
focus-forge reassign-time --wakatime-entry-id=<entry_id> --task-id=<task_id>
focus-forge wakatime-status
focus-forge sync-wakatime
```

### 5.3 Analytics Commands
```
focus-forge analytics [--timeframe=day|week|month|year] [--output=<file>]
focus-forge productive-hours
focus-forge category-breakdown [--visual]
focus-forge focus-stats
focus-forge task-switching-analysis
```

### 5.4 Estimation Commands
```
focus-forge set-estimate --id=<task_id> --hours=<hours>
focus-forge compare-estimates [--id=<task_id>|--all]
focus-forge accuracy-trend [--timeframe=month|quarter|year]
focus-forge forecast-completion --id=<task_id>
```

### 5.5 Pomodoro Commands
```
focus-forge pomodoro --id=<task_id> [--duration=<minutes>] [--break=<minutes>]
focus-forge pomodoro-stats
focus-forge config-pomodoro
```

### 5.6 Reporting Commands
```
focus-forge report --period=day|week|month [--format=txt|md|html]
focus-forge export-time [--from=<YYYY-MM-DD>] [--to=<YYYY-MM-DD>] [--format=csv|json|md]
focus-forge billable-report [--client=<client_name>] [--from=<YYYY-MM-DD>] [--to=<YYYY-MM-DD>]
focus-forge visual-report [--type=<report_type>] [--output=<file>]
focus-forge team-report [--members=<member1,member2|all>]
```

## 6. Technical Implementation Guidelines

### 6.1 Architecture
- The time tracking system will be implemented as a module within the Focus Forge codebase
- Time data will be stored in a separate JSON file (`time_data.json`) to maintain backward compatibility
- Background processes will handle timer persistence
- Wakatime integration will use the official Wakatime API

### 6.2 Performance Requirements
- Timer operations must have minimal delay (<100ms)
- Time data storage should handle at least 5 years of usage without performance degradation
- Wakatime synchronization should be optimized to minimize API calls

### 6.3 Security Considerations
- API keys must be stored securely (encrypted at rest)
- Time data should be backed up regularly
- Client billing information must be protected

### 6.4 Dependencies
- Wakatime Python SDK for API integration
- Matplotlib/Plotly for visualization (optional, can fall back to terminal graphs)
- Rich library for terminal UI components

## 7. User Experience

### 7.1 Terminal UI
- Active timer displayed in terminal prompt (configurable)
- Color-coded time tracking status
- Visual timer countdown for Pomodoro sessions
- Interactive charts in terminal where supported

### 7.2 Notifications
- Configurable notifications for timer events
- Desktop notifications for Pomodoro transitions
- Warning when approaching estimated time limits

### 7.3 Integration Points
- Shell prompt integration via PS1 variable
- tmux status bar integration
- Terminal multiplexer (tmux/screen) support for background timers

## 8. Rollout Strategy

### 8.1 Phase 1: Core Time Tracking
- Implement basic timer functionality
- Task-time association
- Timer persistence
- Basic reporting

### 8.2 Phase 2: Wakatime Integration
- API authentication
- Data import/sync
- Project mapping
- Branch-task mapping

### 8.3 Phase 3: Analytics & Pomodoro
- Productivity metrics
- Pomodoro implementation
- Focus analytics
- Estimation comparison

### 8.4 Phase 4: Advanced Reporting
- Visual report generation
- Export formats
- Team reporting
- Billable hours tracking

## 9. Success Metrics

- 70% of daily active users adopt time tracking features
- Average time tracked increases by 25% per week after introduction
- Estimation accuracy improves by 15% after 3 months of usage
- Wakatime integration adopted by 50% of eligible users
- Pomodoro completion rate exceeds 80%

## 10. Future Considerations

- Integration with additional time tracking services (Clockify, Toggl, etc.)
- Mobile companion app for on-the-go time tracking
- Voice-activated time tracking commands
- AI-based project time estimation
- Team productivity optimization suggestions

## Appendix A: Wakatime API Integration Details

The Wakatime API integration will use the following endpoints:
- GET /api/v1/users/current/summaries
- GET /api/v1/users/current/projects
- GET /api/v1/users/current/durations

Authentication will use the Wakatime API key configured by the user, with proper error handling for invalid or expired keys. 