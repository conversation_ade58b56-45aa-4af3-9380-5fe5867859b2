# Focus Forge AI Guidance Enhancement PRD

## 1. Overview

This document outlines requirements for enhancing Focus Forge with intelligent documentation discovery, analysis, and caching capabilities. These improvements will enable Focus Forge to work effectively across diverse project structures, from those with formal AI guidance systems to projects with only standard documentation.

## 2. Product Objectives

1. Create a hierarchical document discovery system that works with or without formal AI guidance
2. Implement an AI guidance structure detection system to identify and classify documentation systems
3. Develop a flexible caching mechanism for discovered documentation
4. Build a learning system that adapts to project-specific documentation patterns

## 3. Target Users

- Developers using Focus Forge on projects with formal AI guidance (.ai/.claude)
- Developers on projects with only standard documentation (README, PRD, etc.)
- Teams transitioning between different documentation systems
- New projects establishing documentation practices

## 4. Requirements

### 4.1 Hierarchical Document Discovery

#### Functionality
- **F1.1**: Scan project directories for documentation in priority order:
  - Priority 1: `.ai` or `.claude` folders
  - Priority 2: Standard documentation files (README.md, CHANGELOG.md, PRD*.md)
  - Priority 3: Markdown files in docs/ directory
  - Priority 4: Any markdown files in project root
  
- **F1.2**: Extract metadata from discovered documents:
  - Document type classification
  - Last modified date
  - Content summary
  - Related documents (via links)
  
- **F1.3**: Create a document relationship graph showing connections between documents

#### CLI Commands
- **C1.1**: `discover-docs` - Scan and report on available documentation 
- **C1.2**: `extract-doc --path=<path> --type=<auto|requirements|architecture|etc>`
- **C1.3**: `link-docs` - Generate visualization of document relationships

### 4.2 AI Guidance Structure Detection

#### Functionality
- **F2.1**: Analyze and classify AI guidance systems:
  - Butterbot standard
  - Claude guidance
  - Custom documentation
  
- **F2.2**: Calculate confidence scores for guidance system identification

- **F2.3**: Detect completeness of guidance systems:
  - Required files/folders presence
  - Documentation quality assessment
  - Missing critical elements

#### Guidance Markers
- **M2.1**: Butterbot markers:
  - STRUCTURE.md
  - Numbered directories (1-context, 2-technical-design, etc.)
  - Standard file naming patterns
  
- **M2.2**: Claude markers:
  - Assistant configurations
  - Claude-specific templates
  - Special formatting patterns

#### CLI Commands
- **C2.1**: `analyze-guidance --path=<.ai|.claude|docs> --verbose`
- **C2.2**: `guidance-completeness` - Check for missing critical documentation
- **C2.3**: `guidance-quality` - Assess quality of existing documentation

### 4.3 Documentation Caching System

#### Functionality
- **F3.1**: Generate structured JSON cache of all discovered documentation
- **F3.2**: Selectively include/exclude document content vs. metadata
- **F3.3**: Cache invalidation based on file changes
- **F3.4**: Query interface for cached documents

#### Cache Structure
```json
{
  "metadata": {
    "project": "ProjectName",
    "last_updated": "ISO8601-timestamp",
    "focus_forge_version": "0.7.0"
  },
  "guidance_system": {
    "primary_type": "butterbot|claude|custom|standard",
    "confidence": 0.92,
    "path": ".ai",
    "completeness": 0.85
  },
  "documents": {
    "requirements": [
      {
        "path": "docs/PRD-v1.md",
        "type": "requirements",
        "confidence": 0.95,
        "modified": "ISO8601-timestamp",
        "version": "1.0",
        "summary": "..."
      }
    ],
    "architecture": [...],
    "workflows": [...],
    "guides": [...]
  },
  "relationships": [
    {
      "source": "docs/architecture.md",
      "target": "docs/components/auth.md",
      "type": "references",
      "count": 3
    }
  ]
}
```

#### CLI Commands
- **C3.1**: `cache-docs --include-content --output=<path>`
- **C3.2**: `query-cache --type=requirements --format=json|table|md`
- **C3.3**: `invalidate-cache --selective|--full`

### 4.4 Custom Structure Learning

#### Functionality
- **F4.1**: Learn and adapt to project-specific documentation patterns
- **F4.2**: Store project-specific heuristics for document classification
- **F4.3**: Improve detection accuracy based on user feedback
- **F4.4**: Migrate learned patterns between projects (import/export)

#### Learning Mechanisms
- **L4.1**: Pattern detection for custom document structures
- **L4.2**: Keyword/phrase frequency analysis for classification
- **L4.3**: Document similarity clustering

#### CLI Commands
- **C4.1**: `learn-structure --from=<path> --save-profile=<name>`
- **C4.2**: `apply-learning --profile=<name>`
- **C4.3**: `export-learning --output=<path>`
- **C4.4**: `feedback --doc=<path> --correct-type=<type>` (user corrections)

## 5. Integration with Existing Commands

### 5.1 Parse PRD Command
- **I5.1**: Enhance `parse-prd` with documentation-aware context:
  ```
  focus_forge.py parse-prd --input=prd.md --guidance-from=auto|ai|standard|none
  ```

### 5.2 Task Generation
- **I5.2**: Incorporate learned project patterns into task generation:
  ```
  focus_forge.py generate-tasks --use-project-patterns
  ```

### 5.3 AI Guidance Status
- **I5.3**: Enhance status reporting with structure analysis:
  ```
  focus_forge.py ai-guidance-status --analyze --include-standard-docs
  ```

## 6. Technical Implementation Guidelines

### 6.1 Performance Requirements
- **P6.1**: Document discovery must complete in <2s for projects with <1000 files
- **P6.2**: Cache generation should process >50 documents per second
- **P6.3**: Memory usage should not exceed 200MB during operation

### 6.2 Design Principles
- **D6.1**: Modular detector system with pluggable analyzers
- **D6.2**: Progressive enhancement (work with minimal docs, leverage more when available)
- **D6.3**: Lazy evaluation where possible (only scan what's needed)
- **D6.4**: Clear separation between discovery, analysis, and caching

### 6.3 Data Storage
- **S6.1**: Documentation cache stored in `.focus_forge/docs_cache.json`
- **S6.2**: Learning profiles stored in `.focus_forge/learning/`
- **S6.3**: Regular backup of cache and learning data

## 7. Success Metrics

- **M7.1**: 95% accuracy in document type classification across 5 different project structures
- **M7.2**: 90% confidence threshold for AI guidance system detection
- **M7.3**: 80% reduction in document scanning time when using cache
- **M7.4**: 70% success rate in adapting to custom project structures after learning

## 8. Phased Delivery

### Phase 1: Document Discovery
- Basic scanning capabilities
- Standard doc type detection
- Simple cache structure

### Phase 2: AI Guidance Analysis
- Butterbot/Claude detection
- Confidence scoring
- Structure completeness assessment

### Phase 3: Enhanced Caching
- Full JSON cache implementation
- Relationship mapping
- Query capabilities

### Phase 4: Learning System
- Pattern detection
- User feedback integration
- Export/import of learning profiles

## 9. Testing Strategy

- Unit tests for each document classifier
- Integration tests with diverse project structures
- Performance benchmarks for caching system
- User testing with different project types

## 10. Backward Compatibility

- All new functionality must be optional
- Existing commands must work without new parameters
- Cache format versioning to support upgrades 