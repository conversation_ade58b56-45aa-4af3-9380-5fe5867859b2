#!/usr/bin/env python3
"""
Integration example for confidence scoring system.

This script demonstrates how to programmatically use the confidence scoring
and review system in your Python applications.
"""
import json
import argparse
import sys
import os
import glob
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional, Iterator

# Import confidence scoring components
try:
    # First try to import from the local focus_forge directory
    from my_focus_forge.confidence_scoring import ConfidenceScorer, ReviewSystem
except ImportError:
    try:
        # Then try to import from the actual module
        from focus_forge.confidence_scoring import ConfidenceScorer, ReviewSystem
    except ImportError:
        print("Error: Could not import confidence scoring module.")
        print("Make sure you have the confidence_scoring.py file in either the 'my_focus_forge' or 'focus_forge' directory.")
        sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("confidence_scoring.log")
    ]
)
logger = logging.getLogger(__name__)


def analyze_with_thresholds(prd_text: str, tasks: List[Dict[str, Any]], 
                         config: Optional[Dict[str, Any]] = None) -> Tuple[bool, Dict[str, Any]]:
    """
    Analyze PRD and tasks with quality thresholds.
    
    Args:
        prd_text: The PRD text
        tasks: List of tasks
        config: Optional configuration dictionary
        
    Returns:
        Tuple[bool, Dict[str, Any]]: (passed_thresholds, report)
    """
    # Load configuration if not provided
    if config is None:
        config = load_config()
    
    # Get thresholds from config
    THRESHOLDS = config.get("thresholds", {
        "confidence": 0.7,
        "clarity": 0.6,
        "coverage": 0.8,
        "max_ambiguous_high": 0,
        "max_missing_high": 0
    })
    
    # Get scoring weights if provided
    scoring_weights = config.get("scoring_weights", None)
    
    # Generate confidence report
    confidence_scorer = ConfidenceScorer(config={"scoring_weights": scoring_weights})
    report = confidence_scorer.generate_review_report(prd_text, tasks)
    
    # Check against thresholds
    passed = True
    threshold_results = {}
    
    # Check confidence score
    confidence_score = report["confidence_score"]
    threshold_results["confidence"] = {
        "actual": confidence_score,
        "threshold": THRESHOLDS["confidence"],
        "passed": confidence_score >= THRESHOLDS["confidence"]
    }
    passed = passed and threshold_results["confidence"]["passed"]
    
    # Check clarity score
    clarity_score = report["clarity_score"]
    threshold_results["clarity"] = {
        "actual": clarity_score,
        "threshold": THRESHOLDS["clarity"],
        "passed": clarity_score >= THRESHOLDS["clarity"]
    }
    passed = passed and threshold_results["clarity"]["passed"]
    
    # Check coverage score
    coverage_score = report["coverage_score"]
    threshold_results["coverage"] = {
        "actual": coverage_score,
        "threshold": THRESHOLDS["coverage"],
        "passed": coverage_score >= THRESHOLDS["coverage"]
    }
    passed = passed and threshold_results["coverage"]["passed"]
    
    # Check high severity ambiguous requirements
    ambiguous_high_count = report["ambiguous_requirements"]["high_severity_count"]
    threshold_results["ambiguous_high"] = {
        "actual": ambiguous_high_count,
        "threshold": THRESHOLDS["max_ambiguous_high"],
        "passed": ambiguous_high_count <= THRESHOLDS["max_ambiguous_high"]
    }
    passed = passed and threshold_results["ambiguous_high"]["passed"]
    
    # Check high severity missing requirements
    missing_high_count = report["missing_requirements"]["high_severity_count"]
    threshold_results["missing_high"] = {
        "actual": missing_high_count,
        "threshold": THRESHOLDS["max_missing_high"],
        "passed": missing_high_count <= THRESHOLDS["max_missing_high"]
    }
    passed = passed and threshold_results["missing_high"]["passed"]
    
    # Add threshold results to report
    report["threshold_results"] = threshold_results
    report["passed_thresholds"] = passed
    
    return passed, report


def generate_improvement_plan(prd_text: str, tasks: List[Dict[str, Any]], 
                           config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Generate an improvement plan based on confidence analysis.
    
    Args:
        prd_text: The PRD text
        tasks: List of tasks
        config: Optional configuration dictionary
        
    Returns:
        Dict[str, Any]: Improvement plan
    """
    # Load configuration if not provided
    if config is None:
        config = load_config()
    
    # Get review system configuration
    review_config = config.get("review_system", {})
    max_clarifications = review_config.get("max_clarifications", 10)
    max_improvements = review_config.get("max_improvements", 10)
    
    # Create a review system
    review_system = ReviewSystem(config=review_config)
    
    # Get clarification suggestions
    clarifications = review_system.suggest_clarifications(prd_text)
    
    # Get task improvement suggestions
    improvements = review_system.suggest_task_improvements(prd_text, tasks)
    
    # Get flagged issues
    issues = review_system.analyze_and_flag_issues(prd_text, tasks)
    
    # Sort by priority
    priority_order = review_config.get("priority_order", ["high", "medium", "low"])
    priority_map = {p: i for i, p in enumerate(priority_order)}
    
    clarifications.sort(key=lambda x: priority_map.get(x.get("severity", "low"), len(priority_order)))
    improvements.sort(key=lambda x: priority_map.get(x.get("severity", "low"), len(priority_order)))
    issues.sort(key=lambda x: priority_map.get(x.get("priority", "low"), len(priority_order)))
    
    # Create an improvement plan
    plan = {
        "prd_improvements": [],
        "task_improvements": [],
        "critical_issues": []
    }
    
    # Add PRD improvement actions
    for clarification in clarifications[:max_clarifications]:
        if clarification.get("severity") == "high":
            plan["prd_improvements"].append({
                "action": "Replace ambiguous requirement",
                "original": clarification["original"],
                "replacement": clarification["suggestion"],
                "priority": "high"
            })
        else:
            plan["prd_improvements"].append({
                "action": "Consider clarifying requirement",
                "original": clarification["original"],
                "replacement": clarification["suggestion"],
                "priority": clarification.get("severity", "medium")
            })
    
    # Add task improvement actions
    for improvement in improvements[:max_improvements]:
        if improvement["type"] == "new_task":
            plan["task_improvements"].append({
                "action": "Add new task",
                "details": improvement["suggested_task"],
                "rationale": improvement["rationale"],
                "priority": improvement.get("severity", "medium")
            })
        else:
            plan["task_improvements"].append({
                "action": improvement["suggestion"],
                "task_id": improvement["task_id"],
                "rationale": improvement["rationale"],
                "priority": improvement.get("severity", "medium")
            })
    
    # Add critical issues
    for issue in issues:
        if issue.get("priority") == "high":
            plan["critical_issues"].append({
                "type": issue["type"],
                "description": issue["description"],
                "details": issue
            })
    
    return plan


def quality_gate_check(prd_file: str, tasks_file: str, output_file: str = None, 
                      fail_on_threshold: bool = False, config_file: str = None) -> None:
    """
    Perform a quality gate check on PRD and tasks.
    
    Args:
        prd_file: Path to PRD file
        tasks_file: Path to tasks file
        output_file: Optional path to save the report
        fail_on_threshold: Whether to exit with error if thresholds aren't met
        config_file: Optional path to configuration file
    """
    # Load configuration
    config = load_config(config_file) if config_file else load_config()
    
    # Load PRD text
    with open(prd_file, 'r') as f:
        prd_text = f.read()
    
    # Load tasks
    with open(tasks_file, 'r') as f:
        data = json.load(f)
        if isinstance(data, dict) and "tasks" in data:
            tasks = data["tasks"]
        else:
            tasks = data
    
    # Analyze with thresholds
    passed, report = analyze_with_thresholds(prd_text, tasks, config)
    
    # Generate improvement plan if thresholds weren't met
    if not passed:
        improvement_plan = generate_improvement_plan(prd_text, tasks, config)
        report["improvement_plan"] = improvement_plan
    
    # Print summary
    print("\n" + "=" * 70)
    print("QUALITY GATE CHECK")
    print("=" * 70)
    print(f"Overall Result: {'PASSED' if passed else 'FAILED'}")
    print("\nThreshold Checks:")
    
    for name, result in report["threshold_results"].items():
        status = "✅ PASS" if result["passed"] else "❌ FAIL"
        print(f"  {name.upper()}: {result['actual']:.2f if isinstance(result['actual'], float) else result['actual']} "
              f"(threshold: {result['threshold']:.2f if isinstance(result['threshold'], float) else result['threshold']}) - {status}")
    
    # Print improvement recommendations if thresholds weren't met
    if not passed:
        print("\nCritical Issues to Address:")
        
        if report["improvement_plan"]["critical_issues"]:
            for i, issue in enumerate(report["improvement_plan"]["critical_issues"], 1):
                print(f"  {i}. {issue['description']}")
        else:
            print("  No critical issues found.")
        
        print("\nPRD Improvements (Top 3):")
        for i, improvement in enumerate(report["improvement_plan"]["prd_improvements"][:3], 1):
            print(f"  {i}. {improvement['action']}: {improvement['original']}")
            print(f"     Suggested: {improvement['replacement']}")
        
        print("\nTask Improvements (Top 3):")
        for i, improvement in enumerate(report["improvement_plan"]["task_improvements"][:3], 1):
            print(f"  {i}. {improvement['action']}")
            if "task_id" in improvement:
                print(f"     Task: {improvement['task_id']}")
            print(f"     Rationale: {improvement['rationale']}")
    
    # Save report to file if specified
    if output_file:
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\nDetailed report saved to: {output_file}")
    
    print("\n" + "=" * 70)
    
    # Exit with error code if thresholds weren't met and fail_on_threshold is true
    if fail_on_threshold and not passed:
        sys.exit(1)


def find_prd_task_pairs(base_dir: str, prd_pattern: str = "*.md", tasks_pattern: str = "*.json") -> Iterator[Tuple[str, str]]:
    """
    Find pairs of PRD and task files based on name patterns.
    
    This function finds PRD files matching prd_pattern and attempts to find
    corresponding task files with the same base name but matching tasks_pattern.
    
    Args:
        base_dir: Base directory to search in
        prd_pattern: Glob pattern for PRD files
        tasks_pattern: Glob pattern for task files
        
    Returns:
        Iterator[Tuple[str, str]]: Iterator of (prd_file_path, tasks_file_path) pairs
    """
    base_path = Path(base_dir)
    
    # Find all PRD files
    prd_files = list(base_path.glob(f"**/{prd_pattern}"))
    logger.info(f"Found {len(prd_files)} PRD files matching pattern {prd_pattern}")
    
    for prd_file in prd_files:
        # Get the base name without extension
        base_name = prd_file.stem
        
        # Look for matching task files in the same directory
        # First, try exact name match
        task_dir = prd_file.parent
        task_file = task_dir / f"{base_name}.json"
        
        if not task_file.exists():
            # Try with _tasks suffix
            task_file = task_dir / f"{base_name}_tasks.json"
        
        if not task_file.exists():
            # Try with -tasks suffix
            task_file = task_dir / f"{base_name}-tasks.json"
            
        if not task_file.exists():
            # Look for any JSON file in the same directory
            json_files = list(task_dir.glob("*.json"))
            if json_files:
                task_file = json_files[0]
            else:
                logger.warning(f"No matching task file found for PRD: {prd_file}")
                continue
        
        logger.info(f"Found PRD-Tasks pair: {prd_file} -> {task_file}")
        yield str(prd_file), str(task_file)


def batch_process(base_dir: str, output_dir: str, prd_pattern: str = None,
                 tasks_pattern: str = None, max_workers: int = None, 
                 config_file: str = None) -> Dict[str, Any]:
    """
    Process multiple PRD-task pairs in batch mode.
    
    Args:
        base_dir: Base directory to search for PRD and task files
        output_dir: Directory to save reports
        prd_pattern: Glob pattern for PRD files (defaults to config value)
        tasks_pattern: Glob pattern for task files (defaults to config value)
        max_workers: Maximum number of parallel workers (defaults to config value)
        config_file: Optional path to configuration file
    
    Returns:
        Dict[str, Any]: Summary of batch processing results
    """
    # Load configuration
    config = load_config(config_file) if config_file else load_config()
    
    # Get batch configuration
    batch_config = config.get("batch_processing", {})
    
    # Use provided parameters or fall back to config values
    prd_pattern = prd_pattern or batch_config.get("prd_pattern", "*.md")
    tasks_pattern = tasks_pattern or batch_config.get("tasks_pattern", "*.json")
    max_workers = max_workers or batch_config.get("default_workers", 4)
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find PRD-task pairs
    pairs = list(find_prd_task_pairs(base_dir, prd_pattern, tasks_pattern))
    
    if not pairs:
        logger.warning(f"No PRD-task pairs found in {base_dir}")
        return {"status": "error", "message": "No PRD-task pairs found"}
    
    results = {
        "total": len(pairs),
        "passed": 0,
        "failed": 0,
        "errors": 0,
        "reports": []
    }
    
    # Process pairs in parallel
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        futures = {}
        
        for prd_file, tasks_file in pairs:
            base_name = Path(prd_file).stem
            output_file = os.path.join(output_dir, f"{base_name}_report.json")
            
            # Submit the task to the executor
            future = executor.submit(process_single_pair, prd_file, tasks_file, output_file, config)
            futures[future] = {
                "prd_file": prd_file,
                "tasks_file": tasks_file,
                "output_file": output_file
            }
        
        # Process results as they complete
        for future in as_completed(futures):
            info = futures[future]
            try:
                result = future.result()
                results["reports"].append(result)
                
                if result["passed"]:
                    results["passed"] += 1
                else:
                    results["failed"] += 1
                    
                logger.info(f"Processed {info['prd_file']} -> {'PASSED' if result['passed'] else 'FAILED'}")
                
            except Exception as e:
                logger.error(f"Error processing {info['prd_file']}: {e}")
                results["errors"] += 1
                results["reports"].append({
                    "prd_file": info["prd_file"],
                    "tasks_file": info["tasks_file"],
                    "error": str(e),
                    "status": "error"
                })
    
    # Save summary report
    summary_file = os.path.join(output_dir, "batch_summary.json")
    with open(summary_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Batch processing complete. Summary: {results['passed']}/{results['total']} passed, "
                f"{results['failed']} failed, {results['errors']} errors")
    
    return results


def process_single_pair(prd_file: str, tasks_file: str, output_file: str, 
                        config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Process a single PRD-task pair and return results.
    
    This function is designed to be run in a separate process.
    
    Args:
        prd_file: Path to PRD file
        tasks_file: Path to tasks file
        output_file: Path to save the report
        config: Optional configuration dictionary
        
    Returns:
        Dict[str, Any]: Processing result
    """
    try:
        # Load configuration if not provided
        if config is None:
            config = load_config()
        
        # Load PRD text
        with open(prd_file, 'r') as f:
            prd_text = f.read()
        
        # Load tasks
        with open(tasks_file, 'r') as f:
            data = json.load(f)
            if isinstance(data, dict) and "tasks" in data:
                tasks = data["tasks"]
            else:
                tasks = data
        
        # Analyze with thresholds
        passed, report = analyze_with_thresholds(prd_text, tasks, config)
        
        # Generate improvement plan if thresholds weren't met
        if not passed:
            improvement_plan = generate_improvement_plan(prd_text, tasks, config)
            report["improvement_plan"] = improvement_plan
        
        # Save report to file
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Return summary
        return {
            "prd_file": prd_file,
            "tasks_file": tasks_file,
            "output_file": output_file,
            "passed": passed,
            "confidence_score": report["confidence_score"],
            "clarity_score": report["clarity_score"],
            "coverage_score": report["coverage_score"],
            "status": "complete"
        }
        
    except Exception as e:
        logger.error(f"Error processing {prd_file} and {tasks_file}: {e}")
        return {
            "prd_file": prd_file,
            "tasks_file": tasks_file,
            "error": str(e),
            "status": "error"
        }


def generate_ci_report(prd_file: str, tasks_file: str, output_file: str = None, 
                      config_file: str = None) -> Dict[str, Any]:
    """
    Generate a report specifically formatted for CI/CD integration.
    
    This creates a simplified report with statuses appropriate for CI/CD systems.
    
    Args:
        prd_file: Path to PRD file
        tasks_file: Path to tasks file
        output_file: Optional path to save the report
        config_file: Optional path to configuration file
        
    Returns:
        Dict[str, Any]: CI-friendly report
    """
    try:
        # Load configuration
        config = load_config(config_file) if config_file else load_config()
        
        # Get CI configuration
        ci_config = config.get("ci_integration", {})
        include_suggestions = ci_config.get("include_improvement_suggestions", True)
        max_issues = ci_config.get("max_issues_to_report", 5)
        
        # Load PRD text
        with open(prd_file, 'r') as f:
            prd_text = f.read()
        
        # Load tasks
        with open(tasks_file, 'r') as f:
            data = json.load(f)
            if isinstance(data, dict) and "tasks" in data:
                tasks = data["tasks"]
            else:
                tasks = data
        
        # Analyze with thresholds
        passed, full_report = analyze_with_thresholds(prd_text, tasks, config)
        
        # Create CI-friendly report
        ci_report = {
            "status": "success" if passed else "failure",
            "file": {
                "prd": prd_file,
                "tasks": tasks_file
            },
            "scores": {
                "confidence": full_report["confidence_score"],
                "clarity": full_report["clarity_score"],
                "coverage": full_report["coverage_score"]
            },
            "thresholds": full_report["threshold_results"],
            "issues": {
                "ambiguous": full_report["ambiguous_requirements"]["high_severity_count"],
                "missing": full_report["missing_requirements"]["high_severity_count"]
            },
            "timestamp": full_report["timestamp"]
        }
        
        # Add critical issues if there are any and include_suggestions is enabled
        if not passed and include_suggestions:
            improvement_plan = generate_improvement_plan(prd_text, tasks, config)
            
            ci_report["critical_issues"] = [
                {"type": issue["type"], "description": issue["description"]}
                for issue in improvement_plan["critical_issues"][:max_issues]
            ]
            
            # Add top improvement suggestions
            ci_report["improvement_suggestions"] = {
                "prd": [
                    {"action": imp["action"], "original": imp["original"]}
                    for imp in improvement_plan["prd_improvements"][:max_issues]
                ],
                "tasks": [
                    {"action": imp["action"], "task_id": imp.get("task_id", "new")}
                    for imp in improvement_plan["task_improvements"][:max_issues]
                ]
            }
        
        # Save report if output file is specified
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(ci_report, f, indent=2)
        
        return ci_report
        
    except Exception as e:
        error_report = {
            "status": "error",
            "file": {
                "prd": prd_file,
                "tasks": tasks_file
            },
            "error": str(e)
        }
        
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(error_report, f, indent=2)
        
        return error_report


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Confidence Scoring Integration Example")
    
    # Add global arguments
    parser.add_argument("--config", help="Path to configuration file")
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Single PRD-task check command
    check_parser = subparsers.add_parser("check", help="Perform quality gate check on a single PRD-task pair")
    check_parser.add_argument("prd_file", help="Path to PRD file")
    check_parser.add_argument("tasks_file", help="Path to tasks JSON file")
    check_parser.add_argument("-o", "--output", help="Path to save the report (optional)")
    check_parser.add_argument("--fail", action="store_true", help="Exit with error code if thresholds aren't met")
    
    # Batch processing command
    batch_parser = subparsers.add_parser("batch", help="Process multiple PRD-task pairs in batch")
    batch_parser.add_argument("base_dir", help="Base directory to search for PRD and task files")
    batch_parser.add_argument("output_dir", help="Directory to save reports")
    batch_parser.add_argument("--prd-pattern", help="Glob pattern for PRD files")
    batch_parser.add_argument("--tasks-pattern", help="Glob pattern for task files")
    batch_parser.add_argument("--workers", type=int, help="Maximum number of parallel workers")
    
    # CI integration command
    ci_parser = subparsers.add_parser("ci", help="Generate CI-friendly report")
    ci_parser.add_argument("prd_file", help="Path to PRD file")
    ci_parser.add_argument("tasks_file", help="Path to tasks JSON file")
    ci_parser.add_argument("-o", "--output", help="Path to save the report (optional)")
    ci_parser.add_argument("--fail", action="store_true", help="Exit with error code if thresholds aren't met")
    
    return parser.parse_args()


def main() -> None:
    """Main entry point."""
    args = parse_args()
    config_file = getattr(args, "config", None)
    
    if args.command == "check" or not args.command:
        # Default to check command if none specified
        prd_file = getattr(args, "prd_file", None)
        tasks_file = getattr(args, "tasks_file", None)
        
        if not prd_file or not tasks_file:
            print("Error: PRD file and tasks file are required.")
            sys.exit(1)
            
        quality_gate_check(
            prd_file, 
            tasks_file, 
            getattr(args, "output", None), 
            getattr(args, "fail", False),
            config_file
        )
    
    elif args.command == "batch":
        # Run batch processing
        results = batch_process(
            args.base_dir, 
            args.output_dir, 
            getattr(args, "prd_pattern", None), 
            getattr(args, "tasks_pattern", None), 
            getattr(args, "workers", None),
            config_file
        )
        
        # Load configuration to check if we should fail on errors
        config = load_config(config_file)
        fail_on_threshold = config.get("ci_integration", {}).get("fail_on_threshold", False)
        
        # Exit with error code if any checks failed and either --fail flag is set or config says so
        if (getattr(args, "fail", False) or fail_on_threshold) and (results["failed"] > 0 or results["errors"] > 0):
            sys.exit(1)
    
    elif args.command == "ci":
        # Generate CI report
        report = generate_ci_report(
            args.prd_file, 
            args.tasks_file, 
            getattr(args, "output", None),
            config_file
        )
        
        # Print summary in CI-friendly format
        print(f"CI Report Status: {report['status']}")
        
        if report['status'] == 'success':
            print(f"Confidence Score: {report['scores']['confidence']:.2f}")
            print(f"Clarity Score: {report['scores']['clarity']:.2f}")
            print(f"Coverage Score: {report['scores']['coverage']:.2f}")
        elif report['status'] == 'error':
            print(f"Error: {report['error']}")
        else:
            print("Failed threshold checks:")
            for name, result in report["thresholds"].items():
                if not result["passed"]:
                    print(f"  {name}: {result['actual']} (threshold: {result['threshold']})")
            
            if "critical_issues" in report and report["critical_issues"]:
                print("\nCritical issues:")
                for issue in report["critical_issues"]:
                    print(f"  - {issue['description']}")
        
        # Load configuration to check if we should fail on errors
        config = load_config(config_file)
        fail_on_threshold = config.get("ci_integration", {}).get("fail_on_threshold", False)
        
        # Exit with error code if checks failed and either --fail flag is set or config says so
        if (getattr(args, "fail", False) or fail_on_threshold) and report["status"] != "success":
            sys.exit(1)
    
    else:
        print(f"Unknown command: {args.command}")
        sys.exit(1)


def load_config(config_file: str = "confidence_scoring_config.json") -> Dict[str, Any]:
    """
    Load configuration from JSON file with fallback to defaults.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        Dict[str, Any]: Configuration dictionary
    """
    # Default configuration
    default_config = {
        "thresholds": {
            "confidence": 0.7,
            "clarity": 0.6,
            "coverage": 0.8,
            "max_ambiguous_high": 0,
            "max_missing_high": 0
        },
        "batch_processing": {
            "default_workers": 4,
            "prd_pattern": "*.md",
            "tasks_pattern": "*.json",
            "auto_pair_files": True
        },
        "ci_integration": {
            "fail_on_threshold": True,
            "include_improvement_suggestions": True,
            "max_issues_to_report": 5
        },
        "logging": {
            "log_file": "confidence_scoring.log",
            "log_level": "INFO",
            "include_timestamp": True
        }
    }
    
    # Try to load from file
    config = default_config
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                loaded_config = json.load(f)
                
                # Update default config with loaded values
                for section, section_values in loaded_config.items():
                    if section in config:
                        if isinstance(section_values, dict):
                            config[section].update(section_values)
                        else:
                            config[section] = section_values
                    else:
                        config[section] = section_values
                        
            logger.info(f"Loaded configuration from {config_file}")
        else:
            logger.warning(f"Configuration file {config_file} not found, using defaults")
    except Exception as e:
        logger.error(f"Error loading configuration file: {e}")
        logger.info("Using default configuration")
    
    return config


if __name__ == "__main__":
    main() 