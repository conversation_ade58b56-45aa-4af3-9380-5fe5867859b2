"""
Focus Forge - Task management CLI for development projects.
"""

from .constants import (
    APP_NAME,
    VERSION,
    CONFIG_FILE,
    TASKS_FILE,
    TASKS_DIR,
    AI_FOLDER,
    CLAUDE_FOLDER,
)

from .dependency_graph import DependencyGraph, DependencyType, Dependency
from .component_detection import ComponentDetector
from .nlp import RelationshipDetector
from .doc_discovery import DocDiscovery
from .priority_inference import (
    PriorityInferenceEngine,
    PriorityLevel,
    ComplexityLevel,
    analyze_text_file,
)
from .task_generation import (
    TaskGenerator,
    TaskGenerationConfig,
    TaskTemplate,
    TaskType,
    convert_prd_to_tasks,
)
from .task_validation import (
    TaskValidator,
    TaskValidationError,
    validate_tasks_file,
)
from .cli_extensions import (
    discover_docs_cmd,
    analyze_guidance_cmd,
    cache_docs_cmd,
    query_cache_cmd,
    ai_guidance_status_cmd,
    extract_doc_cmd,
    learn_structure_cmd,
    apply_learning_cmd,
    export_learning_cmd,
    import_learning_cmd,
    list_profiles_cmd,
    feedback_cmd,
    register_commands
)
from .focus_forge import (
    Task,
    Config,
    Subtask,
    TaskStatus,
    TaskPriority,
    load_config,
    save_config,
    load_tasks,
    save_tasks,
    find_task_by_id,
    colorize_status,
    colorize_priority,
    merge_tasks,
    validate_dependencies,
    get_next_task,
    generate_task_files,
    call_claude_api,
)

__version__ = VERSION
__all__ = [
    'APP_NAME',
    'VERSION',
    'CONFIG_FILE',
    'TASKS_FILE',
    'TASKS_DIR',
    'AI_FOLDER',
    'CLAUDE_FOLDER',
    'Task',
    'Config',
    'Subtask',
    'TaskStatus',
    'TaskPriority',
    'load_config',
    'save_config',
    'load_tasks',
    'save_tasks',
    'find_task_by_id',
    'colorize_status',
    'colorize_priority',
    'merge_tasks',
    'validate_dependencies',
    'get_next_task',
    'generate_task_files',
    'call_claude_api',
    'DependencyGraph',
    'DependencyType',
    'Dependency',
    'ComponentDetector',
    'RelationshipDetector',
    'PriorityInferenceEngine',
    'PriorityLevel',
    'ComplexityLevel',
    'analyze_text_file',
    'TaskGenerator',
    'TaskGenerationConfig',
    'TaskTemplate',
    'TaskType',
    'convert_prd_to_tasks',
    'TaskValidator',
    'TaskValidationError',
    'validate_tasks_file',
    'DocDiscovery',
    'discover_docs_cmd',
    'analyze_guidance_cmd',
    'cache_docs_cmd',
    'query_cache_cmd',
    'ai_guidance_status_cmd',
    'extract_doc_cmd',
    'learn_structure_cmd',
    'apply_learning_cmd',
    'export_learning_cmd',
    'import_learning_cmd',
    'list_profiles_cmd',
    'feedback_cmd',
    'register_commands'
]
