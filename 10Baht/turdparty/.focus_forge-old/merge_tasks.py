#!/usr/bin/env python3
"""Merge database model fixes tasks into the main tasks.json file."""

import json
import sys
from datetime import datetime

def main():
    # Load existing tasks
    with open('tasks.json', 'r') as f:
        existing_tasks = json.load(f)
    
    # Load database model fixes tasks
    with open('database_model_fixes_tasks.json', 'r') as f:
        model_fixes_tasks = json.load(f)
    
    # Get existing and new tasks
    tasks = existing_tasks['tasks']
    new_tasks = model_fixes_tasks['tasks']
    
    # Generate next task ID
    # Assuming existing task IDs are in format Tn where n is a number
    max_id = 0
    for task in tasks:
        if task['id'].startswith('T'):
            try:
                task_num = int(task['id'][1:])
                max_id = max(max_id, task_num)
            except ValueError:
                pass
    
    # Renumber the new tasks from Tn+1
    for i, task in enumerate(new_tasks):
        old_id = task['id']
        new_id = f"T{max_id + i + 1}"
        
        # Update dependencies in this task
        if task['dependencies']:
            new_deps = []
            for dep in task['dependencies']:
                if dep.startswith('M'):
                    # Find the corresponding new task ID
                    dep_num = int(dep[1:]) - 1  # M1 is index 0
                    if 0 <= dep_num < len(new_tasks):
                        new_deps.append(f"T{max_id + dep_num + 1}")
                else:
                    new_deps.append(dep)
            task['dependencies'] = new_deps
        
        # Update task ID
        task['id'] = new_id
        
        # Update subtask IDs
        for subtask in task['subtasks']:
            subtask_id_parts = subtask['id'].split('.')
            if len(subtask_id_parts) == 2 and subtask_id_parts[0] == old_id:
                subtask['id'] = f"{new_id}.{subtask_id_parts[1]}"
    
    # Append new tasks to existing tasks
    tasks.extend(new_tasks)
    
    # Update timestamp
    existing_tasks['updated_at'] = datetime.now().isoformat()
    
    # Save merged tasks
    with open('tasks.json', 'w') as f:
        json.dump(existing_tasks, f, indent=2)
    
    print(f"Successfully merged {len(new_tasks)} new tasks into tasks.json")

if __name__ == "__main__":
    main() 