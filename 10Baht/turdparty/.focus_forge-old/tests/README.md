# Focus Forge Test Suite

This directory contains the test suite for the Focus Forge project. It includes unit tests, integration tests, and tests for edge cases and error handling.

## Test Structure

- `conftest.py`: Contains pytest fixtures used across all tests
- `test_utils.py`: Unit tests for utility functions
- `test_cli.py`: Tests for CLI commands
- `test_api.py`: Tests for API integration
- `test_integration.py`: End-to-end tests for complete workflows
- `test_edge_cases.py`: Tests for edge cases and error handling
- `test_comprehensive.py`: Comprehensive tests covering multiple aspects of the code
- `test_butterbot.py`: Tests specifically for butterbot integration
- `test_sequence_analysis_extended.py`: Advanced tests for T04 sequence analysis functionality
- `test_sequence_constraints.py`: Tests for sequence constraints and dependency ordering
- `test_critical_path.py`: Tests for critical path detection and analysis
- `test_dependency_graph.py`: Tests for the dependency graph implementation
- `test_dependency_graph_minimal.py`: Minimal tests for dependency graph core features

## Running Tests

To run the tests, use the pytest command from the project root directory:

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_utils.py

# Run T04 sequence analysis tests
pytest tests/test_sequence_analysis_extended.py tests/test_sequence_constraints.py tests/test_critical_path.py

# Run tests with a specific marker
pytest -m unit
pytest -m integration
pytest -m api
pytest -m cli
pytest -m performance
pytest -m security

# Run tests with coverage report
pytest --cov=focus_forge --cov-report=term-missing
```

## Test Markers

- `unit`: Unit tests
- `integration`: Integration tests
- `cli`: Command-line interface tests
- `api`: API integration tests
- `performance`: Performance tests
- `security`: Security-related tests

## Adding New Tests

When adding new tests:

1. Place them in the appropriate file based on what they test
2. Add proper docstrings explaining what each test does
3. Use fixtures from `conftest.py` where appropriate
4. Add new fixtures to `conftest.py` if needed for multiple tests
5. Apply appropriate markers to categorize tests

## T04 - Sequence Analysis Test Suite

The T04 test suite consists of three main test files:

1. `test_sequence_analysis_extended.py`: Tests the core sequence analysis functionality including:
   - Dependency sequence generation
   - Constraint-based ordering
   - Priority and complexity scoring
   - Implementation time estimation

2. `test_sequence_constraints.py`: Tests the constraint system including:
   - Different constraint types (foundation first, technical prerequisite, etc.)
   - Constraint prioritization and weight handling
   - Conflict resolution between constraints
   - Edge cases and robustness

3. `test_critical_path.py`: Tests the critical path analysis including:
   - Linear and branching path detection
   - Weighted critical path (considering task durations)
   - Edge cases like circular dependencies
   - Parallel task identification

These tests can be run together to fully validate the T04 functionality:

```bash
nix-shell default.nix --run "python3 tests/test_sequence_analysis_extended.py && python3 tests/test_sequence_constraints.py && python3 tests/test_critical_path.py"
```

## Mocking External Dependencies

For tests that involve external dependencies (e.g., API calls), use the `unittest.mock` library to mock responses:

```python
from unittest.mock import patch

def test_with_mock():
    with patch('focus_forge.call_claude_api', return_value='mocked response'):
        # Test code that calls the API
        # ...
```

## Test Coverage

The test suite aims to achieve high code coverage, testing:

- Basic functionality
- Edge cases
- Error handling
- Complete workflows
- Performance characteristics
- Security aspects
- Butterbot integration

The coverage report can be generated using pytest-cov:

```bash
pytest --cov=focus_forge --cov-report=html
```

This will generate an HTML report in the `htmlcov` directory.

## Comprehensive Testing

The comprehensive tests in `test_comprehensive.py` are designed to test multiple aspects of the code simultaneously, including:

- Utility functions
- Command integration
- Edge cases
- Performance
- Security
- Integration of multiple commands

## Butterbot Integration Testing

The butterbot integration tests in `test_butterbot.py` specifically test the AI guidance and butterbot integration features:

- AI folder detection
- Guidance path determination
- Context retrieval
- PRD parsing with butterbot context
- AI guidance status reporting 