# Makefile for running Focus Forge tests

.PHONY: all test test-unit test-cli test-api test-integration test-butterbot test-comprehensive test-coverage test-html-coverage test-performance test-security clean

# Default target
all: test

# Run all tests
test:
	pytest -v

# Run unit tests
test-unit:
	pytest -v -m unit

# Run CLI tests
test-cli:
	pytest -v tests/test_cli.py

# Run API tests
test-api:
	pytest -v tests/test_api.py

# Run integration tests
test-integration:
	pytest -v tests/test_integration.py

# Run butterbot integration tests
test-butterbot:
	pytest -v tests/test_butterbot.py

# Run comprehensive tests
test-comprehensive:
	pytest -v tests/test_comprehensive.py

# Run performance tests
test-performance:
	pytest -v -m performance

# Run security tests
test-security:
	pytest -v -m security

# Run tests with coverage
test-coverage:
	pytest --cov=focus_forge --cov-report=term-missing

# Generate HTML coverage report
test-html-coverage:
	pytest --cov=focus_forge --cov-report=html
	@echo "HTML coverage report generated in htmlcov/ directory"

# Clean up generated files
clean:
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -f .coverage

# Help target
help:
	@echo "Available targets:"
	@echo "  all               : Run all tests (default)"
	@echo "  test              : Run all tests"
	@echo "  test-unit         : Run unit tests"
	@echo "  test-cli          : Run CLI tests"
	@echo "  test-api          : Run API tests"
	@echo "  test-integration  : Run integration tests"
	@echo "  test-butterbot    : Run butterbot integration tests"
	@echo "  test-comprehensive: Run comprehensive tests"
	@echo "  test-performance  : Run performance tests"
	@echo "  test-security     : Run security tests"
	@echo "  test-coverage     : Run tests with coverage report"
	@echo "  test-html-coverage: Generate HTML coverage report"
	@echo "  clean             : Clean up generated files" 