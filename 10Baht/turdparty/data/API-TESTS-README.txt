# API Integration Tests
To set up and run the API integration tests, run the following commands:

1. Create the Docker network:
   docker network create test-network

2. Build and start the API container:
   docker build -t api-test-server -f Dockerfile.simple .
   docker run -d -p 3055:8000 --name api-test --network test-network api-test-server

3. Connect Playwright container to the network:
   docker network connect test-network turdparty_playwright

4. Run the integration tests:
   .dockerwrapper/run-test-direct.sh tests/playwright/api-health.spec.js

5. Clean up (optional):
   docker stop api-test && docker rm api-test

The integration tests verify that:
1. The API container is accessible from the Playwright container
2. The health endpoint returns a 200 OK response with the expected JSON payload
3. Network connectivity between containers is properly configured
