"""Database structure implementation tasks.

This module contains tasks for implementing the database structure as defined in the PRD.
"""

from typing import List, Dict, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class Task:
    """Represents a single task in the database implementation."""
    id: str
    title: str
    description: str
    priority: int
    dependencies: List[str]
    estimated_hours: float
    status: str
    created_at: datetime
    updated_at: datetime

def create_database_tasks() -> List[Task]:
    """Create tasks for implementing the database structure.
    
    Returns:
        List[Task]: List of tasks for database implementation.
    """
    tasks = [
        Task(
            id="DB-001",
            title="Set up PostgreSQL Database",
            description="Configure PostgreSQL 13 with specified settings:\n- Host: localhost\n- Port: 5430\n- Database: turdparty\n- User: postgres\n- Password: postgres",
            priority=1,
            dependencies=[],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-002",
            title="Create Base Tables",
            description="Create the following tables:\n- users\n- items\n- files\n- vms\n- vm_logs",
            priority=2,
            dependencies=["DB-001"],
            estimated_hours=2.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-003",
            title="Implement User Table",
            description="Create users table with fields:\n- id (SERIAL PRIMARY KEY)\n- username (VARCHAR(50) UNIQUE)\n- email (VARCHAR(255) UNIQUE)\n- hashed_password (VARCHAR(255))\n- mfa_secret (VARCHAR(32))\n- is_active (BOOLEAN)\n- timestamps",
            priority=2,
            dependencies=["DB-002"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-004",
            title="Implement Items Table",
            description="Create items table with fields:\n- id (SERIAL PRIMARY KEY)\n- name (VARCHAR(255))\n- description (TEXT)\n- user_id (FOREIGN KEY)\n- timestamps",
            priority=2,
            dependencies=["DB-002"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-005",
            title="Implement Files Table",
            description="Create files table with fields:\n- id (SERIAL PRIMARY KEY)\n- filename (VARCHAR(255))\n- original_filename (VARCHAR(255))\n- content_type (VARCHAR(100))\n- size (BIGINT)\n- bucket_name (VARCHAR(255))\n- object_name (VARCHAR(255))\n- user_id (FOREIGN KEY)\n- timestamps",
            priority=2,
            dependencies=["DB-002"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-006",
            title="Implement VMs Table",
            description="Create vms table with fields:\n- id (SERIAL PRIMARY KEY)\n- name (VARCHAR(255))\n- status (VARCHAR(50))\n- template (VARCHAR(255))\n- user_id (FOREIGN KEY)\n- timestamps",
            priority=2,
            dependencies=["DB-002"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-007",
            title="Implement VM_Logs Table",
            description="Create vm_logs table with fields:\n- id (SERIAL PRIMARY KEY)\n- vm_id (FOREIGN KEY)\n- log_type (VARCHAR(50))\n- message (TEXT)\n- created_at (TIMESTAMP)",
            priority=2,
            dependencies=["DB-002"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-008",
            title="Create Custom Types",
            description="Create custom types:\n- vm_status (ENUM: created, starting, running, stopped, error)",
            priority=3,
            dependencies=["DB-002"],
            estimated_hours=0.5,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-009",
            title="Implement Indexes",
            description="Create indexes:\n- Primary keys for all tables\n- Unique constraints for users\n- Performance indexes for foreign keys",
            priority=3,
            dependencies=["DB-003", "DB-004", "DB-005", "DB-006", "DB-007"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-010",
            title="Implement Triggers",
            description="Create triggers:\n- update_updated_at_column function\n- Triggers for all tables with updated_at",
            priority=3,
            dependencies=["DB-003", "DB-004", "DB-005", "DB-006"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-011",
            title="Set up Alembic Migrations",
            description="Configure Alembic:\n- Initialize migrations\n- Create initial migration\n- Set up migration scripts",
            priority=2,
            dependencies=["DB-001"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-012",
            title="Implement Backup System",
            description="Set up backup and recovery:\n- Configure pg_dump\n- Create backup scripts\n- Test recovery process",
            priority=3,
            dependencies=["DB-001"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-013",
            title="Database Testing",
            description="Create and run tests:\n- Table creation tests\n- Index tests\n- Trigger tests\n- Migration tests",
            priority=2,
            dependencies=["DB-009", "DB-010", "DB-011"],
            estimated_hours=2.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        Task(
            id="DB-014",
            title="Documentation",
            description="Create documentation:\n- Schema documentation\n- Migration guide\n- Backup/recovery guide",
            priority=2,
            dependencies=["DB-013"],
            estimated_hours=1.0,
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    ]
    return tasks

def get_task_dependencies() -> Dict[str, List[str]]:
    """Get task dependencies for database implementation.
    
    Returns:
        Dict[str, List[str]]: Dictionary mapping task IDs to their dependencies.
    """
    return {
        "DB-001": [],
        "DB-002": ["DB-001"],
        "DB-003": ["DB-002"],
        "DB-004": ["DB-002"],
        "DB-005": ["DB-002"],
        "DB-006": ["DB-002"],
        "DB-007": ["DB-002"],
        "DB-008": ["DB-002"],
        "DB-009": ["DB-003", "DB-004", "DB-005", "DB-006", "DB-007"],
        "DB-010": ["DB-003", "DB-004", "DB-005", "DB-006"],
        "DB-011": ["DB-001"],
        "DB-012": ["DB-001"],
        "DB-013": ["DB-009", "DB-010", "DB-011"],
        "DB-014": ["DB-013"]
    }

def get_task_estimates() -> Dict[str, float]:
    """Get time estimates for database implementation tasks.
    
    Returns:
        Dict[str, float]: Dictionary mapping task IDs to estimated hours.
    """
    return {
        "DB-001": 1.0,
        "DB-002": 2.0,
        "DB-003": 1.0,
        "DB-004": 1.0,
        "DB-005": 1.0,
        "DB-006": 1.0,
        "DB-007": 1.0,
        "DB-008": 0.5,
        "DB-009": 1.0,
        "DB-010": 1.0,
        "DB-011": 1.0,
        "DB-012": 1.0,
        "DB-013": 2.0,
        "DB-014": 1.0
    }

if __name__ == "__main__":
    # Create tasks
    tasks = create_database_tasks()
    
    # Print task summary
    print("Database Implementation Tasks:")
    print("-" * 50)
    for task in tasks:
        print(f"\nTask ID: {task.id}")
        print(f"Title: {task.title}")
        print(f"Priority: {task.priority}")
        print(f"Estimated Hours: {task.estimated_hours}")
        print(f"Dependencies: {', '.join(task.dependencies) if task.dependencies else 'None'}")
        print(f"Status: {task.status}")
        print("-" * 30)
    
    # Print total estimated hours
    total_hours = sum(task.estimated_hours for task in tasks)
    print(f"\nTotal Estimated Hours: {total_hours}") 