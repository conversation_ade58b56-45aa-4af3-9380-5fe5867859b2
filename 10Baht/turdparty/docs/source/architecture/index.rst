Architecture
============

This section provides comprehensive information on the architecture of TurdParty.

.. toctree::
   :maxdepth: 2
   :caption: Overview:

   overview

.. toctree::
   :maxdepth: 2
   :caption: Core Components:

   server/overview
   server/infrastructure
   server/startup
   server/request_flow
   combined_server

.. toctree::
   :maxdepth: 2
   :caption: Services:

   celery
   cachet

.. toctree::
   :maxdepth: 2
   :caption: Vagrant:

   vagrant/architecture
   vagrant/integration
   vagrant/operations
   vagrant/setup
   vagrant/ssh
   vagrant/management

.. toctree::
   :maxdepth: 2
   :caption: Storage:

   minio/storage
   minio/vagrant_integration
