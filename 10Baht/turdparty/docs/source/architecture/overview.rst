Architecture Overview
====================

TurdParty is built with a modular architecture that separates concerns and allows for easy extension.

Components
---------

The main components of the system are:

- **API Server**: A FastAPI application that provides the REST API
- **MinIO Storage**: Object storage for files
- **Vagrant VM Management**: Management of Vagrant virtual machines
- **File Upload**: File upload and validation
- **VM Injection**: Injection of files into Vagrant VMs
- **Celery Workers**: Asynchronous task processing for long-running operations

System Diagram
-------------

.. code-block:: text

    ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
    │             │     │             │     │             │
    │  Frontend   │────▶│  API Server │────▶│  MinIO      │
    │             │     │             │     │  Storage    │
    └─────────────┘     └─────────────┘     └─────────────┘
                              │                    ▲
                              │                    │
                              ▼                    │
                        ┌─────────────┐     ┌─────────────┐
                        │             │     │             │
                        │  Vagrant    │     │  Celery     │
                        │  VMs        │◀───▶│  Workers    │
                        │             │     │             │
                        └─────────────┘     └─────────────┘
                                                  │
                                                  │
                                                  ▼
                                            ┌─────────────┐
                                            │             │
                                            │  Redis      │
                                            │  Broker     │
                                            │             │
                                            └─────────────┘

For more detailed information on each component, see the following sections:

- :doc:`server/overview`
- :doc:`minio/storage`
- :doc:`vagrant/architecture`
- :doc:`celery/index`
