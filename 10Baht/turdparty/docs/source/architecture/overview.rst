Architecture Overview
====================

TurdParty is built using a modern microservices architecture with Docker containerization. The system consists of multiple interconnected services that provide a comprehensive platform for VM management, file processing, and service monitoring.

Core Architecture Components
----------------------------

**Application Layer**
   - **FastAPI Application**: Main REST API server with comprehensive endpoints
   - **React Frontend**: Modern web interface served via Nginx
   - **Celery Workers**: Distributed task processing for background operations

**Data Layer**
   - **PostgreSQL Database**: Primary application data storage
   - **PostgreSQL (Cachet)**: Dedicated database for status dashboard
   - **Redis**: Message broker and caching layer
   - **MinIO Object Storage**: S3-compatible file storage

**Infrastructure Layer**
   - **Docker Network**: Container orchestration and communication
   - **Health Monitoring**: Service health checks and status reporting
   - **Load Balancing**: Nginx-based request routing

**Monitoring & Operations**
   - **Cachet Dashboard**: Real-time service status monitoring
   - **Celery Flower**: Task queue monitoring and management
   - **Logging System**: Centralized application logging

Service Communication
---------------------

The services communicate through:

- **HTTP/REST**: API endpoints for client-server communication
- **Redis Pub/Sub**: Asynchronous task queue messaging
- **Database Connections**: Direct database access for data persistence
- **Docker Network**: Internal service-to-service communication

System Diagram
-------------

.. code-block:: text

    ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
    │   Frontend  │────▶│  API Server │────▶│   MinIO     │
    │ (React/Nginx│     │  (FastAPI)  │     │  Storage    │
    │ :3100)      │     │  (:3050)    │     │  (:3300)    │
    └─────────────┘     └─────────────┘     └─────────────┘
                              │                     │
                              │                     │
                              ▼                     ▼
                        ┌─────────────┐     ┌─────────────┐
                        │ PostgreSQL  │     │   Vagrant   │
                        │ Database    │     │    VMs      │
                        │  (:3200)    │     │             │
                        └─────────────┘     └─────────────┘
                              │
                              │
                              ▼
    ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
    │   Cachet    │────▶│    Redis    │────▶│   Celery    │
    │ Dashboard   │     │   Broker    │     │  Workers    │
    │  (:3501)    │     │  (:3400)    │     │             │
    └─────────────┘     └─────────────┘     └─────────────┘
          │                                       │
          │                                       │
          ▼                                       ▼
    ┌─────────────┐                         ┌─────────────┐
    │ PostgreSQL  │                         │   Flower    │
    │  (Cachet)   │                         │ Dashboard   │
    │             │                         │  (:3450)    │
    └─────────────┘                         └─────────────┘

Service Ports
-------------

The following ports are used by TurdParty services:

- **3050**: TurdParty API (FastAPI)
- **3100**: Frontend Application (React/Nginx)
- **3200**: PostgreSQL Database
- **3300**: MinIO API
- **3301**: MinIO Console
- **3400**: Redis
- **3450**: Celery Flower Dashboard
- **3501**: Cachet Status Dashboard
- **2223**: MinIO SSH Access

For more detailed information on each component, see the following sections:

- :doc:`server/overview`
- :doc:`minio/storage`
- :doc:`vagrant/architecture`
