# Docker Wrapper Configuration Changes

This document outlines the changes made to the Docker Wrapper configuration to fix issues with the file upload functionality and improve the overall setup.

## Changes Made

### 1. Docker Compose Configuration

#### Development Environment (`dev-compose.yml`)
- Updated container names to match the expected naming convention (`dockerwrapper_*`)
- Changed port mappings to use the 3050-3400 range:
  - API: 3050 (was 9667)
  - Frontend: 3100 (was 3000)
  - Dashboard: 3150 (new)
  - PostgreSQL: 3200 (was 9666)
  - Playwright: 3250 (new)
- Added environment variables for file uploads (`FILE_UPLOAD_DIR`, `API_PREFIX`)
- Added volume mount for the uploads directory
- Updated the command to use the correct module path (`app.main:app`)
- Updated the React app to connect to the correct API URL

#### Production Environment (`docker-compose.yml`)
- Made similar changes as in the development environment
- Set `TEST_MODE` to false and `DEBUG` to false for production
- Updated the React app command to build and serve the production build

### 2. Container Initialization Scripts

#### Development Script (`dev-container-init.sh`)
- Added code to create and set permissions for the file upload directory
- Added installation of `python-multipart` package for file upload support

#### Production Script (`container-init.sh`)
- Added code to create and set permissions for the file upload directory

### 3. API Configuration

#### API Version Middleware (`api/middleware/api_version_middleware.py`)
- Created a middleware to handle API versioning
- Redirects requests from old API paths (`/api/*`) to new versioned paths (`/api/v1/*`)

#### Configuration Settings (`api/core/config.py`)
- Added file upload settings:
  - `FILE_UPLOAD_DIR`: Directory for file uploads
  - `MAX_UPLOAD_SIZE`: Maximum allowed file size (100MB)
  - `ALLOWED_UPLOAD_EXTENSIONS`: List of allowed file extensions

### 4. File Upload Router (`api/routes/file_upload.py`)
- Created a basic file upload router with endpoints for:
  - Listing file uploads
  - Uploading a file
  - Getting file metadata
  - Downloading a file
  - Updating file metadata
  - Deleting a file
  - Uploading multiple files as a folder

### 5. Utility Scripts

#### Restart Script (`restart-containers.sh`)
- Created a script to restart the Docker containers
- Supports both development and production environments
- Creates necessary directories
- Shows running containers after restart
- Updated to display the new port mappings

### 6. Documentation

#### Docker Wrapper README (`.dockerwrapper/README.md`)
- Created a README file with instructions on how to use the Docker Wrapper
- Includes information on directory structure, quick start, file upload functionality, configuration, and troubleshooting
- Added a port configuration section with a table of all port mappings

## Port Configuration

The application now uses the following port mappings, all within the required 3050-3400 range:

| Service    | Host Port | Container Port | Description                |
|------------|-----------|----------------|----------------------------|
| API        | 3050      | 8000           | FastAPI application        |
| Frontend   | 3100      | 3000           | React application          |
| Dashboard  | 3150      | 8080           | Monitoring dashboard       |
| PostgreSQL | 3200      | 5432           | PostgreSQL database        |
| Playwright | 3250      | 9323           | Playwright testing service |

## How to Use the Changes

1. To start the development environment:
   ```bash
   cd .dockerwrapper
   ./restart-containers.sh dev
   ```

2. To start the production environment:
   ```bash
   cd .dockerwrapper
   ./restart-containers.sh
   ```

3. To access the file upload API:
   - Development: http://localhost:3050/api/v1/file_upload/
   - Production: http://localhost:3050/api/v1/file_upload/

4. To access the React frontend:
   - Development: http://localhost:3100
   - Production: http://localhost:3100

5. To access the dashboard:
   - Development: http://localhost:3150
   - Production: http://localhost:3150

## Troubleshooting

If you encounter issues with the Docker containers:

1. Check the container logs:
   ```bash
   docker logs dockerwrapper_api_1
   ```

2. Restart the containers:
   ```bash
   cd .dockerwrapper
   ./restart-containers.sh dev
   ```

3. Check the API health:
   ```bash
   curl http://localhost:3050/health
   ``` 