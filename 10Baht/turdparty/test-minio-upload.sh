#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing MinIO File Upload Process${NC}"
echo -e "${YELLOW}===============================${NC}"

# Activate virtual environment if it exists
if [ -d ".venv" ]; then
  echo -e "${GREEN}Activating virtual environment...${NC}"
  source .venv/bin/activate
fi

# Create a test file
TEST_DIR="/tmp/minio-test"
TEST_FILE="${TEST_DIR}/test-file.txt"

mkdir -p "${TEST_DIR}"
echo "This is a test file for MinIO upload - $(date)" > "${TEST_FILE}"
echo -e "${GREEN}Created test file at ${TEST_FILE}${NC}"

# Create a Python script to test uploading to MinIO
cat > /tmp/test_minio_upload.py << 'EOF'
from minio import Minio
import os
import time
import uuid
from datetime import timedelta

# Get connection parameters from environment or use defaults
host = os.environ.get("MINIO_HOST", "localhost")
port = os.environ.get("MINIO_PORT", "9000")
access_key = os.environ.get("MINIO_ACCESS_KEY", "minioadmin")
secret_key = os.environ.get("MINIO_SECRET_KEY", "minioadmin")

print(f"Connecting to MinIO at {host}:{port} with credentials {access_key}:****")

# Create MinIO client
client = Minio(
    f"{host}:{port}",
    access_key=access_key,
    secret_key=secret_key,
    secure=False
)

# Define bucket name and test path
bucket_name = "test-bucket"
test_file_path = "/tmp/minio-test/test-file.txt"
file_size = os.path.getsize(test_file_path)
object_name = f"test-{uuid.uuid4()}.txt"

print(f"Uploading file {test_file_path} (size: {file_size} bytes) to bucket {bucket_name} as {object_name}")

# Create bucket if it doesn't exist
if not client.bucket_exists(bucket_name):
    client.make_bucket(bucket_name)
    print(f"Created bucket: {bucket_name}")
else:
    print(f"Bucket {bucket_name} already exists")

# Upload the file
start_time = time.time()
client.fput_object(
    bucket_name, 
    object_name, 
    test_file_path,
)
upload_time = time.time() - start_time
print(f"Upload completed in {upload_time:.2f} seconds")

# Get file stats to verify upload
try:
    stats = client.stat_object(bucket_name, object_name)
    print(f"Object stats: size={stats.size}, etag={stats.etag}")
    print(f"Successfully verified object {object_name} in bucket {bucket_name}")
except Exception as e:
    print(f"Error verifying upload: {e}")
    exit(1)

# Generate a presigned URL for the object (use timedelta instead of int)
try:
    url = client.presigned_get_object(bucket_name, object_name, expires=timedelta(hours=1))
    print(f"Presigned URL for the uploaded file (valid for 1 hour):")
    print(url)
except Exception as e:
    print(f"Error generating presigned URL: {e}")

# List all objects in the bucket
print(f"\nListing objects in bucket {bucket_name}:")
objects = client.list_objects(bucket_name, recursive=True)
count = 0
for obj in objects:
    print(f"- {obj.object_name} (size: {obj.size} bytes, last modified: {obj.last_modified})")
    count += 1
    if count >= 10:
        print(f"... and more (showing first 10 objects only)")
        break

print("\nTest completed successfully!")
EOF

# Set environment variables and run the test
export MINIO_HOST=localhost
export MINIO_PORT=9000
export MINIO_ACCESS_KEY=minioadmin
export MINIO_SECRET_KEY=minioadmin

echo -e "${YELLOW}Running MinIO upload test...${NC}"
python /tmp/test_minio_upload.py

# Clean up
echo -e "${YELLOW}Cleaning up test files...${NC}"
rm -f "${TEST_FILE}"
rmdir "${TEST_DIR}" 2>/dev/null || true

echo -e "${GREEN}Test complete!${NC}" 