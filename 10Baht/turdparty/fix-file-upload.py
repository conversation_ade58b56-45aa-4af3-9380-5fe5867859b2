#!/usr/bin/env python3
"""
Script to diagnose and fix file upload issues.
"""
import os
import sys
import requests
import json
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_connection(base_url="http://localhost:3050"):
    """Test basic API connectivity."""
    logger.info(f"Testing API connectivity at {base_url}")
    try:
        response = requests.get(f"{base_url}/api/v1/health")
        if response.status_code == 200:
            logger.info(f"API is accessible. Response: {response.json()}")
            return True
        else:
            logger.error(f"API returned status code {response.status_code}. Response: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error connecting to API: {str(e)}")
        return False

def test_file_upload_endpoint(base_url="http://localhost:3050", file_path="test-file.txt"):
    """Test the file upload endpoint."""
    logger.info(f"Testing file upload endpoint with file {file_path}")
    
    # Ensure test file exists
    if not os.path.exists(file_path):
        with open(file_path, 'w') as f:
            f.write("This is a test file for upload testing.")
        logger.info(f"Created test file {file_path}")
    
    try:
        # Get auth token first (if API is in test mode, this might be bypassed)
        token = None
        try:
            auth_response = requests.post(
                f"{base_url}/api/v1/auth/login",
                json={"username": "test", "password": "test"}
            )
            if auth_response.status_code == 200:
                token = auth_response.json().get("access_token")
                logger.info("Successfully obtained authentication token")
            else:
                logger.warning(f"Could not get auth token. Status: {auth_response.status_code}. Will try without auth.")
        except Exception as e:
            logger.warning(f"Error getting auth token: {str(e)}. Will try without auth.")
        
        # Prepare headers
        headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # Try multiple upload endpoints
        endpoints = [
            "/api/v1/file_upload/",
            "/api/v1/file_upload/upload",
            "/api/v1/upload",
            "/api/v1/files"
        ]
        
        success = False
        for endpoint in endpoints:
            try:
                logger.info(f"Trying upload to {endpoint}")
                
                with open(file_path, 'rb') as f:
                    files = {'file': (os.path.basename(file_path), f)}
                    response = requests.post(
                        f"{base_url}{endpoint}",
                        files=files,
                        headers=headers
                    )
                
                logger.info(f"Response from {endpoint}: Status {response.status_code}")
                if response.status_code in (200, 201):
                    logger.info(f"Upload successful to {endpoint}. Response: {response.json()}")
                    success = True
                    break
                else:
                    logger.warning(f"Upload failed to {endpoint}. Response: {response.text}")
            except Exception as e:
                logger.error(f"Error uploading to {endpoint}: {str(e)}")
        
        return success
    except Exception as e:
        logger.error(f"Error in file upload test: {str(e)}")
        return False

def check_api_routes_configuration():
    """Check the API routes configuration in the application."""
    logger.info("Checking API routes configuration")
    
    # Look for relevant files
    files_to_check = [
        "api/application.py",
        "api/routes/file_upload.py",
        "api/core/endpoints.py",
        "api/services/file_upload_service.py"
    ]
    
    issues_found = False
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            logger.warning(f"File {file_path} does not exist")
            continue
        
        with open(file_path, 'r') as f:
            content = f.read()
            
            # Check for common issues
            if 'file_upload' in file_path:
                if 'PREFIX' in content and 'APIEndpoints.PREFIX' not in content:
                    logger.error(f"Found direct PREFIX reference in {file_path} which may cause issues")
                    issues_found = True
                    
                if 'import APIEndpoints' in content and 'from api.core.endpoints import APIEndpoints' not in content:
                    logger.error(f"Incorrect import of APIEndpoints in {file_path}")
                    issues_found = True
            
            if 'application.py' in file_path:
                if 'include_router(file_upload.router' in content:
                    logger.info(f"File upload router is included in application.py")
                else:
                    logger.error(f"File upload router is not included in application.py")
                    issues_found = True
    
    if not issues_found:
        logger.info("No obvious configuration issues found in API routes")
    
    return not issues_found

def main():
    """Main function."""
    logger.info("Starting file upload diagnostic script")
    
    # Check API routes configuration
    config_ok = check_api_routes_configuration()
    if not config_ok:
        logger.warning("Issues found in API routes configuration")
    
    # Test API connection
    api_ok = test_api_connection()
    if not api_ok:
        logger.error("API connectivity test failed. Please check if the API is running.")
        return
    
    # Test file upload
    upload_ok = test_file_upload_endpoint()
    if upload_ok:
        logger.info("File upload test succeeded!")
    else:
        logger.error("File upload test failed. Please check the logs for details.")

if __name__ == "__main__":
    main() 