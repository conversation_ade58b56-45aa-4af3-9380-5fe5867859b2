#!/usr/bin/env node

/**
 * TurdParty Docker Dashboard
 * A CLI dashboard for monitoring and managing Docker containers
 */

const blessed = require('blessed');
const contrib = require('blessed-contrib');
const Docker = require('dockerode');
const chalk = require('chalk');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

// Initialize Docker client
const docker = new Docker({ socketPath: '/var/run/docker.sock' });

// Create a screen object
const screen = blessed.screen({
  smartCSR: true,
  title: 'TurdParty Docker Dashboard',
  dockBorders: true,
  fullUnicode: true
});

// Create the layout grid
const grid = new contrib.grid({ rows: 12, cols: 12, screen: screen });

// Header with title and instructions
const header = grid.set(0, 0, 1, 12, blessed.box, {
  content: chalk.bold.blue(' TurdParty Docker Dashboard ') + 
           chalk.gray(' | ') + 
           chalk.yellow('Press ') + 
           chalk.bold.green('R') + 
           chalk.yellow(' to restart, ') + 
           chalk.bold.green('X') + 
           chalk.yellow(' to stop, ') + 
           chalk.bold.green('I') + 
           chalk.yellow(' for details, ') + 
           chalk.bold.green('S') + 
           chalk.yellow(' to start all, ') + 
           chalk.bold.green('H') + 
           chalk.yellow(' for help, ') + 
           chalk.bold.green('Q') + 
           chalk.yellow(' to quit'),
  style: {
    fg: 'white',
    bg: 'blue',
  },
  tags: true
});

// Container table
const containerTable = grid.set(1, 0, 5, 8, contrib.table, {
  keys: true,
  vi: true,
  fg: 'white',
  selectedFg: 'white',
  selectedBg: 'blue',
  interactive: true,
  label: 'Containers',
  columnSpacing: 3,
  columnWidth: [16, 10, 12, 10, 20]
});

// Container logs
const containerLogs = grid.set(6, 0, 6, 8, blessed.log, {
  fg: 'green',
  selectedFg: 'green',
  label: 'Container Logs',
  tags: true,
  scrollable: true,
  mouse: true,
  scrollbar: {
    ch: ' ',
    track: {
      bg: 'gray'
    },
    style: {
      inverse: true
    }
  }
});

// System stats
const systemStats = grid.set(1, 8, 3, 4, contrib.gauge, {
  label: 'CPU Usage',
  percent: [0, 0, 0],
  style: {
    fg: 'white',
    bg: 'black',
    border: {
      fg: 'white'
    }
  },
  gaugeSpacing: 0,
  gaugeWidth: 12,
  showLabel: true,
  stack: [
    { percent: 0, stroke: 'red' },
    { percent: 0, stroke: 'green' },
    { percent: 0, stroke: 'blue' }
  ]
});

// Memory usage
const memoryUsage = grid.set(4, 8, 3, 4, contrib.donut, {
  label: 'Memory Usage',
  radius: 8,
  arcWidth: 3,
  remainColor: 'black',
  yPadding: 2,
  data: [
    { percent: 0, label: 'Used', color: 'red' },
    { percent: 100, label: 'Free', color: 'green' }
  ]
});

// Network traffic
const networkTraffic = grid.set(7, 8, 5, 4, contrib.line, {
  style: {
    line: 'yellow',
    text: 'white',
    baseline: 'black'
  },
  xLabelPadding: 3,
  xPadding: 5,
  label: 'Network Traffic',
  showLegend: true,
  legend: { width: 12 }
});

// Set up network data
const networkData = {
  x: Array(10).fill(0).map((_, i) => i.toString()),
  y: [Array(10).fill(0), Array(10).fill(0)]
};

networkTraffic.setData([
  {
    title: 'RX',
    x: networkData.x,
    y: networkData.y[0],
    style: { line: 'green' }
  },
  {
    title: 'TX',
    x: networkData.x,
    y: networkData.y[1],
    style: { line: 'red' }
  }
]);

// Status bar
const statusBar = grid.set(11, 0, 1, 12, blessed.box, {
  content: ' Status: Initializing...',
  style: {
    fg: 'white',
    bg: 'blue'
  }
});

// Set key bindings
screen.key(['escape', 'q', 'C-c'], function() {
  return process.exit(0);
});

screen.key('r', async function() {
  const selectedContainer = containerTable.rows.selected;
  if (selectedContainer >= 0) {
    const containerId = containerData[selectedContainer][0];
    try {
      statusBar.setContent(` Status: Restarting container ${containerId}...`);
      screen.render();
      await restartContainer(containerId);
      statusBar.setContent(` Status: Container ${containerId} restarted successfully`);
      screen.render();
      updateContainers();
    } catch (error) {
      statusBar.setContent(` Status: Error restarting container: ${error.message}`);
      screen.render();
    }
  }
});

screen.key('s', async function() {
  try {
    statusBar.setContent(' Status: Starting all containers...');
    screen.render();
    await startAllContainers();
    statusBar.setContent(' Status: All containers started successfully');
    screen.render();
    updateContainers();
  } catch (error) {
    statusBar.setContent(` Status: Error starting containers: ${error.message}`);
    screen.render();
  }
});

// Stop container
screen.key('x', async function() {
  const selectedContainer = containerTable.rows.selected;
  if (selectedContainer >= 0) {
    const containerId = containerData[selectedContainer][0];
    try {
      statusBar.setContent(` Status: Stopping container ${containerId}...`);
      screen.render();
      await stopContainer(containerId);
      statusBar.setContent(` Status: Container ${containerId} stopped successfully`);
      screen.render();
      updateContainers();
    } catch (error) {
      statusBar.setContent(` Status: Error stopping container: ${error.message}`);
      screen.render();
    }
  }
});

// View container details
screen.key('i', async function() {
  const selectedContainer = containerTable.rows.selected;
  if (selectedContainer >= 0) {
    const containerId = containerData[selectedContainer][0];
    try {
      statusBar.setContent(` Status: Fetching details for container ${containerId}...`);
      screen.render();
      await showContainerDetails(containerId);
    } catch (error) {
      statusBar.setContent(` Status: Error fetching container details: ${error.message}`);
      screen.render();
    }
  }
});

// Show help
screen.key('h', function() {
  showHelp();
});

// Container data
let containerData = [];
let selectedContainerId = null;

// Update container list
async function updateContainers() {
  try {
    const containers = await docker.listContainers({ all: true });
    
    containerData = containers.map(container => {
      const name = container.Names[0].replace(/^\//, '');
      const status = container.State;
      const statusColor = getStatusColor(status);
      const uptime = container.Status;
      const image = container.Image;
      const ports = container.Ports.map(port => 
        `${port.PublicPort || ''}:${port.PrivatePort}`).join(', ');
      
      return [name, statusColor(status), uptime, ports, image];
    });
    
    containerTable.setData({
      headers: ['Name', 'Status', 'Uptime', 'Ports', 'Image'],
      data: containerData
    });
    
    // Update status bar
    const runningCount = containers.filter(c => c.State === 'running').length;
    statusBar.setContent(` Status: ${runningCount}/${containers.length} containers running`);
    
    screen.render();
  } catch (error) {
    statusBar.setContent(` Status: Error: ${error.message}`);
    screen.render();
  }
}

// Get container logs
async function getContainerLogs(containerId) {
  try {
    const container = docker.getContainer(containerId);
    const logs = await container.logs({
      stdout: true,
      stderr: true,
      tail: 20,
      follow: false
    });
    
    // Convert Buffer to string and split by newlines
    return logs.toString('utf8').split('\n');
  } catch (error) {
    return [`Error getting logs: ${error.message}`];
  }
}

// Restart container
async function restartContainer(containerId) {
  const container = docker.getContainer(containerId);
  await container.restart();
}

// Start all containers
async function startAllContainers() {
  try {
    const projectRoot = path.resolve(__dirname);
    const dockerComposeFile = path.join(projectRoot, '.dockerwrapper', 'docker-compose.yml');
    
    statusBar.setContent(` Status: Starting containers using ${dockerComposeFile}...`);
    screen.render();
    
    await execAsync(`docker-compose -f ${dockerComposeFile} -p turdparty up -d`);
  } catch (error) {
    throw new Error(`Failed to start containers: ${error.message}`);
  }
}

// Get color based on container status
function getStatusColor(status) {
  switch (status) {
    case 'running':
      return chalk.green;
    case 'created':
      return chalk.blue;
    case 'exited':
      return chalk.red;
    default:
      return chalk.yellow;
  }
}

// Update system stats
async function updateSystemStats() {
  try {
    const info = await docker.info();
    const stats = await Promise.all(
      containerData
        .filter(container => container[1].includes('running'))
        .map(container => {
          const containerObj = docker.getContainer(container[0]);
          return containerObj.stats({ stream: false });
        })
    );
    
    // Calculate CPU usage
    let totalCpuPercentage = 0;
    const cpuPercentages = stats.map(stat => {
      const cpuDelta = stat.cpu_stats.cpu_usage.total_usage - stat.precpu_stats.cpu_usage.total_usage;
      const systemCpuDelta = stat.cpu_stats.system_cpu_usage - stat.precpu_stats.system_cpu_usage;
      const cpuCount = stat.cpu_stats.online_cpus || stat.cpu_stats.cpu_usage.percpu_usage.length;
      const cpuPercent = (cpuDelta / systemCpuDelta) * cpuCount * 100;
      totalCpuPercentage += cpuPercent;
      return cpuPercent;
    });
    
    // Update CPU gauge
    systemStats.setStack([
      { percent: Math.min(totalCpuPercentage, 100), stroke: 'red' }
    ]);
    
    // Calculate memory usage
    let totalMemUsed = 0;
    let totalMemLimit = 0;
    
    stats.forEach(stat => {
      totalMemUsed += stat.memory_stats.usage;
      totalMemLimit += stat.memory_stats.limit;
    });
    
    const memPercent = (totalMemUsed / totalMemLimit) * 100;
    
    // Update memory donut
    memoryUsage.setData([
      { percent: memPercent, label: 'Used', color: 'red' },
      { percent: 100 - memPercent, label: 'Free', color: 'green' }
    ]);
    
    // Update network data
    let rxTotal = 0;
    let txTotal = 0;
    
    stats.forEach(stat => {
      const networks = stat.networks || {};
      Object.keys(networks).forEach(iface => {
        rxTotal += networks[iface].rx_bytes;
        txTotal += networks[iface].tx_bytes;
      });
    });
    
    // Shift data points
    networkData.y[0].shift();
    networkData.y[1].shift();
    
    // Add new data points (convert to KB)
    networkData.y[0].push(rxTotal / 1024);
    networkData.y[1].push(txTotal / 1024);
    
    networkTraffic.setData([
      {
        title: 'RX (KB)',
        x: networkData.x,
        y: networkData.y[0],
        style: { line: 'green' }
      },
      {
        title: 'TX (KB)',
        x: networkData.x,
        y: networkData.y[1],
        style: { line: 'red' }
      }
    ]);
    
    screen.render();
  } catch (error) {
    // Silently fail for stats updates
    console.error(error);
  }
}

// Handle container selection
containerTable.rows.on('select', async (item, index) => {
  const selectedContainer = containerData[index][0];
  selectedContainerId = selectedContainer;
  
  // Clear logs and show loading message
  containerLogs.setContent('Loading logs...');
  screen.render();
  
  // Get and display logs
  const logs = await getContainerLogs(selectedContainer);
  containerLogs.setContent('');
  logs.forEach(line => containerLogs.add(line));
  
  screen.render();
});

// Initial update
updateContainers();

// Set up intervals for updates
setInterval(updateContainers, 3000);
setInterval(updateSystemStats, 2000);

// Render the screen
screen.render();

// Stop container
async function stopContainer(containerId) {
  const container = docker.getContainer(containerId);
  await container.stop();
}

// Show container details
async function showContainerDetails(containerId) {
  try {
    const container = docker.getContainer(containerId);
    const details = await container.inspect();
    
    // Create a details modal
    const detailsModal = blessed.box({
      parent: screen,
      top: 'center',
      left: 'center',
      width: '80%',
      height: '80%',
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        fg: 'white',
        bg: 'black',
        border: {
          fg: '#f0f0f0'
        }
      },
      scrollable: true,
      alwaysScroll: true,
      keys: true,
      vi: true,
      mouse: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'gray'
        },
        style: {
          inverse: true
        }
      }
    });
    
    // Format container details
    const content = [
      `{bold}Container Details: ${details.Name.replace(/^\//, '')}{/bold}`,
      '',
      `{bold}ID:{/bold} ${details.Id}`,
      `{bold}Created:{/bold} ${new Date(details.Created).toLocaleString()}`,
      `{bold}Status:{/bold} ${details.State.Status}`,
      `{bold}Image:{/bold} ${details.Config.Image}`,
      '',
      '{bold}Network Settings:{/bold}',
      ...Object.entries(details.NetworkSettings.Networks).map(([network, config]) => 
        `  {bold}${network}:{/bold} ${config.IPAddress} (Gateway: ${config.Gateway})`
      ),
      '',
      '{bold}Mounts:{/bold}',
      ...details.Mounts.map(mount => 
        `  {bold}${mount.Source}{/bold} -> ${mount.Destination} (${mount.Mode})`
      ),
      '',
      '{bold}Environment Variables:{/bold}',
      ...details.Config.Env.map(env => `  ${env}`),
      '',
      '{bold}Command:{/bold}',
      `  ${details.Config.Cmd ? details.Config.Cmd.join(' ') : 'N/A'}`,
      '',
      '{bold}Labels:{/bold}',
      ...Object.entries(details.Config.Labels || {}).map(([key, value]) => 
        `  {bold}${key}:{/bold} ${value}`
      ),
      '',
      '{yellow}Press ESC or q to close{/yellow}'
    ].join('\n');
    
    detailsModal.setContent(content);
    
    // Handle key events for the modal
    detailsModal.key(['escape', 'q'], () => {
      screen.remove(detailsModal);
      screen.render();
    });
    
    screen.append(detailsModal);
    detailsModal.focus();
    screen.render();
    
    statusBar.setContent(` Status: Showing details for container ${containerId}`);
  } catch (error) {
    throw new Error(`Failed to get container details: ${error.message}`);
  }
}

// Show help screen
function showHelp() {
  const helpModal = blessed.box({
    parent: screen,
    top: 'center',
    left: 'center',
    width: '60%',
    height: '60%',
    tags: true,
    border: {
      type: 'line'
    },
    style: {
      fg: 'white',
      bg: 'black',
      border: {
        fg: '#f0f0f0'
      }
    }
  });
  
  const content = [
    '{center}{bold}TurdParty Docker Dashboard Help{/bold}{/center}',
    '',
    '{bold}Navigation:{/bold}',
    '  {bold}↑/↓{/bold} - Navigate container list',
    '  {bold}Enter{/bold} - Select container to view logs',
    '',
    '{bold}Container Actions:{/bold}',
    '  {bold}R{/bold} - Restart selected container',
    '  {bold}X{/bold} - Stop selected container',
    '  {bold}I{/bold} - View detailed information about selected container',
    '',
    '{bold}Global Actions:{/bold}',
    '  {bold}S{/bold} - Start all containers',
    '  {bold}H{/bold} - Show this help screen',
    '  {bold}Q{/bold} or {bold}Esc{/bold} or {bold}Ctrl+C{/bold} - Quit dashboard',
    '',
    '{center}{yellow}Press any key to close this help screen{/yellow}{/center}'
  ].join('\n');
  
  helpModal.setContent(content);
  
  // Handle key events for the modal
  helpModal.key(['escape', 'q', 'h', 'enter', 'space'], () => {
    screen.remove(helpModal);
    screen.render();
  });
  
  screen.append(helpModal);
  helpModal.focus();
  screen.render();
} 