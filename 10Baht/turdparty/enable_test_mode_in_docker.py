#!/usr/bin/env python3
"""
Script to enable test mode in the Docker container.
"""
import subprocess
import logging
import sys

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# The code to inject into conftest.py
INJECT_CODE = """
# Import and enable test mode
from api.core.test_config import test_settings
test_settings.enable_test_mode()
logger.info("Test mode enabled for all tests - authentication bypassed")
"""

def main():
    """Enable test mode in the Docker container."""
    logger.info("Enabling test mode in Docker container...")
    
    try:
        # Check if the container is running
        check_cmd = ["docker", "ps", "-q", "-f", "name=TurdParty-container"]
        result = subprocess.run(check_cmd, capture_output=True, text=True)
        
        if not result.stdout.strip():
            logger.error("TurdParty-container is not running")
            return 1
        
        # Create a temporary file with the code to inject
        with open("/tmp/test_mode_code.py", "w") as f:
            f.write(INJECT_CODE)
        
        # Copy the file to the container
        copy_cmd = ["docker", "cp", "/tmp/test_mode_code.py", "TurdParty-container:/tmp/"]
        subprocess.run(copy_cmd, check=True)
        
        # Check if the code is already in conftest.py
        check_content_cmd = [
            "docker", "exec", "TurdParty-container",
            "grep", "-q", "test_settings.enable_test_mode()", "/app/api/tests/conftest.py"
        ]
        check_result = subprocess.run(check_content_cmd)
        
        if check_result.returncode == 0:
            logger.info("Test mode is already enabled in conftest.py")
            return 0
        
        # Inject the code into conftest.py
        inject_cmd = [
            "docker", "exec", "TurdParty-container",
            "bash", "-c", 
            "cat /tmp/test_mode_code.py >> /app/api/tests/conftest.py"
        ]
        subprocess.run(inject_cmd, check=True)
        
        logger.info("Test mode enabled in Docker container")
        return 0
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Error enabling test mode in Docker: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 