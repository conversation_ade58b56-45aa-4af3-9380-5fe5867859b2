#!/bin/bash

# Simple test script for folder upload functionality

# Set color variables
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Simple Folder Upload Test${NC}"
echo "=========================="

# Test file paths
TEST_DIR="./test_upload_dir"
API_URL="http://localhost:3050"

# Create test directory and files if they don't exist
if [ ! -d "$TEST_DIR" ]; then
    mkdir -p "$TEST_DIR"
    echo "This is file 1" > "$TEST_DIR/file1.txt"
    echo "This is file 2" > "$TEST_DIR/file2.txt"
    echo -e "${GREEN}Created test directory and files${NC}"
fi

# Simplest folder upload test - using two files
echo -e "\n${YELLOW}Uploading two files with paths${NC}"
echo "----------------------------"

RESPONSE=$(curl -s -o response.txt -w "%{http_code}" \
    -X POST "$API_URL/api/v1/file_upload/folder" \
    -F "files=@$TEST_DIR/file1.txt" \
    -F "files=@$TEST_DIR/file2.txt" \
    -F "paths=file1.txt" \
    -F "paths=file2.txt" \
    -F "description=Simple folder upload test")

echo "Status code: $RESPONSE"

if [[ "$RESPONSE" == 2* ]]; then
    echo -e "${GREEN}Folder upload successful!${NC}"
    echo "Response:"
    cat response.txt
    echo -e "\n${GREEN}=== Folder upload test succeeded! ===${NC}"
    exit 0
else
    echo -e "${RED}Folder upload failed.${NC}"
    echo "Response:"
    cat response.txt
    echo -e "\n${RED}=== Folder upload test failed. ===${NC}"
    exit 1
fi 