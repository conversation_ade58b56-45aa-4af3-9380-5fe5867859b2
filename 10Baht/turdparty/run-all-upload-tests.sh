#!/bin/bash

# <PERSON>ript to run all file upload tests in sequence
# This script checks the environment, installs dependencies if needed,
# and runs UI, API, and E2E tests for the file upload functionality.

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Running All File Upload Tests                      ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Function to check and install NodeJS dependencies
check_dependencies() {
  echo -e "${YELLOW}Checking Node.js dependencies...${NC}"
  
  # Check if we're in Docker environment
  if [ -f "/.dockerenv" ] || grep -q docker /proc/1/cgroup; then
    echo -e "${GREEN}Running inside Docker container${NC}"
    DOCKER_ENV=true
  else
    echo -e "${YELLOW}Running in host environment${NC}"
    DOCKER_ENV=false
  fi
  
  # If we have npm, install dependencies
  if command -v npm > /dev/null; then
    echo -e "${GREEN}npm is available, checking dependencies...${NC}"
    
    # Check if axios and form-data are installed
    if ! npm list axios > /dev/null 2>&1 || ! npm list form-data > /dev/null 2>&1; then
      echo -e "${YELLOW}Installing required packages...${NC}"
      npm install --no-save axios form-data
    fi
  else
    echo -e "${RED}npm not found. Cannot install dependencies.${NC}"
    echo "Tests might fail if dependencies are missing."
  fi
}

# Function to run tests and capture results
run_test() {
  local test_name=$1
  local test_cmd=$2
  local exit_on_fail=$3
  
  echo -e "${YELLOW}\n=======================================================${NC}"
  echo -e "${YELLOW}Running ${test_name}...${NC}"
  echo -e "${YELLOW}=======================================================${NC}"
  
  eval $test_cmd
  local result=$?
  
  if [ $result -eq 0 ]; then
    echo -e "${GREEN}✅ ${test_name} completed successfully!${NC}"
    return 0
  else
    echo -e "${RED}❌ ${test_name} failed with exit code ${result}${NC}"
    if [ "$exit_on_fail" = true ]; then
      echo -e "${RED}Exiting tests due to failure${NC}"
      exit $result
    fi
    return $result
  fi
}

# Check if any of the test files exist in Docker
check_docker_files() {
  echo -e "${YELLOW}Checking if we should run tests in Docker...${NC}"
  
  if [ "$DOCKER_ENV" = true ]; then
    echo -e "${GREEN}Already running in Docker, continuing with local tests${NC}"
    return 1
  fi
  
  # Check if Docker is available
  if ! command -v docker > /dev/null; then
    echo -e "${YELLOW}Docker not available, running tests locally${NC}"
    return 1
  fi
  
  # Check if relevant containers are running
  PLAYWRIGHT_CONTAINER=$(docker ps --format '{{.Names}}' | grep playwright | head -1)
  
  if [ -z "$PLAYWRIGHT_CONTAINER" ]; then
    echo -e "${YELLOW}No Playwright container found, running tests locally${NC}"
    return 1
  fi
  
  echo -e "${GREEN}Found Playwright container: ${PLAYWRIGHT_CONTAINER}${NC}"
  return 0
}

# Run tests in Docker if available
run_docker_tests() {
  echo -e "${YELLOW}Running tests in Docker container: ${PLAYWRIGHT_CONTAINER}${NC}"
  
  # Copy test files to container
  echo -e "${YELLOW}Copying test files to container...${NC}"
  docker cp test-file-upload-ui.js ${PLAYWRIGHT_CONTAINER}:/app/
  docker cp test-api-upload.js ${PLAYWRIGHT_CONTAINER}:/app/
  docker cp test-e2e-upload.js ${PLAYWRIGHT_CONTAINER}:/app/
  
  # Install dependencies in container
  echo -e "${YELLOW}Installing dependencies in container...${NC}"
  docker exec ${PLAYWRIGHT_CONTAINER} npm install axios form-data
  
  # Run UI test
  echo -e "${YELLOW}Running UI test in container...${NC}"
  docker exec -it ${PLAYWRIGHT_CONTAINER} node /app/test-file-upload-ui.js
  
  # Run API test
  echo -e "${YELLOW}Running API test in container...${NC}"
  docker exec -it ${PLAYWRIGHT_CONTAINER} node /app/test-api-upload.js
  
  # Run E2E test
  echo -e "${YELLOW}Running E2E test in container...${NC}"
  docker exec -it ${PLAYWRIGHT_CONTAINER} node /app/test-e2e-upload.js
  
  echo -e "${GREEN}Docker container tests completed${NC}"
}

# Main execution starts here
check_dependencies

# Check if we should run tests in Docker
if check_docker_files; then
  run_docker_tests
  exit $?
fi

# Create test file if it doesn't exist
if [ ! -f "test-file.txt" ]; then
  echo "This is a test file for upload" > test-file.txt
  echo -e "${GREEN}Created test file: test-file.txt${NC}"
fi

# 1. Run API tests first since they're most basic
run_test "API Upload Tests" "node test-api-upload.js" false

# 2. Run UI tests
run_test "UI Upload Tests" "node test-file-upload-ui.js" false

# 3. Run E2E tests if available
if [ -f "test-e2e-upload.js" ]; then
  run_test "E2E Upload Tests" "node test-e2e-upload.js" false
else
  echo -e "${YELLOW}Skipping E2E tests (test-e2e-upload.js not found)${NC}"
fi

# 4. Run Playwright tests if available
if command -v npx > /dev/null && [ -f "tests/file-upload-e2e.test.js" ]; then
  run_test "Playwright Upload Tests" "npx playwright test tests/file-upload-e2e.test.js" false
else
  echo -e "${YELLOW}Skipping Playwright tests (tests/file-upload-e2e.test.js not found or npx not available)${NC}"
fi

echo -e "\n${GREEN}=======================================================${NC}"
echo -e "${GREEN}    All File Upload Tests Completed                     ${NC}"
echo -e "${GREEN}=======================================================${NC}" 