#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running tests with host-based MinIO${NC}"
echo -e "${YELLOW}=====================================${NC}"

# Activate virtual environment if it exists
if [ -d ".venv" ]; then
  echo -e "${GREEN}Activating virtual environment...${NC}"
  source .venv/bin/activate
fi

# Ensure pytest is installed
if ! command -v pytest &> /dev/null; then
  echo -e "${YELLOW}Installing pytest...${NC}"
  pip install pytest
fi

# Make sure MinIO client is installed
if ! pip list | grep -q minio; then
  echo -e "${YELLOW}Installing MinIO client...${NC}"
  pip install minio
fi

# Set environment variables to point to host MinIO
export MINIO_HOST=localhost
export MINIO_PORT=9000
export MINIO_ACCESS_KEY=minioadmin
export MINIO_SECRET_KEY=minioadmin
export MINIO_DIRECT=true
export TEST_MODE=true
export API_V1_STR="/api/v1"

# Create a temporary test file to directly target the API v1 routes
cat > /tmp/test_api_endpoints.py << 'EOF'
"""
Test API endpoints with correct versioning.
"""
import logging
import pytest
from fastapi.testclient import TestClient
from minio import Minio
import os

from api.application import get_application

# Set up logging
logger = logging.getLogger(__name__)

@pytest.fixture
def client():
    """Test client fixture."""
    app = get_application()
    return TestClient(app)

def test_health_endpoint(client):
    """Test that the direct health endpoint returns a 200 OK response."""
    # Test the direct API v1 endpoint
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert data["status"] == "ok"

def test_ping_endpoint(client):
    """Test that the ping endpoint with v1 prefix returns a 200 OK response."""
    response = client.get("/api/v1/health/ping")
    assert response.status_code == 200
    data = response.json()
    assert "ping" in data
    assert data["ping"] == "pong"

def test_minio_direct_connection():
    """Test direct connection to MinIO."""
    try:
        # Get connection parameters from environment
        host = os.environ.get("MINIO_HOST", "localhost")
        port = os.environ.get("MINIO_PORT", "9000")
        access_key = os.environ.get("MINIO_ACCESS_KEY", "minioadmin")
        secret_key = os.environ.get("MINIO_SECRET_KEY", "minioadmin")
        
        # Create MinIO client
        client = Minio(
            f"{host}:{port}",
            access_key=access_key,
            secret_key=secret_key,
            secure=False
        )
        
        # Check if MinIO is accessible by listing buckets
        buckets = client.list_buckets()
        
        # If we got here, the connection was successful
        assert True
        
        # Log bucket info
        logger.info(f"Successfully connected to MinIO. Found {len(buckets)} buckets.")
        
    except Exception as e:
        pytest.fail(f"MinIO connection failed: {str(e)}")

def test_minio_api_endpoint(client):
    """Test the MinIO status API endpoint."""
    response = client.get("/api/v1/minio-status")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
EOF

echo -e "${YELLOW}Running API v1 endpoint tests...${NC}"
pytest /tmp/test_api_endpoints.py -v

echo -e "${GREEN}Tests completed!${NC}"

# Try connecting to MinIO directly with a simple Python script
echo -e "${YELLOW}Testing direct MinIO connection...${NC}"
cat > /tmp/test_minio_direct.py << 'EOF'
from minio import Minio
import os

# Get connection parameters from environment
host = os.environ.get("MINIO_HOST", "localhost")
port = os.environ.get("MINIO_PORT", "9000")
access_key = os.environ.get("MINIO_ACCESS_KEY", "minioadmin")
secret_key = os.environ.get("MINIO_SECRET_KEY", "minioadmin")

print(f"Connecting to MinIO at {host}:{port} with credentials {access_key}:****")

# Create MinIO client
client = Minio(
    f"{host}:{port}",
    access_key=access_key,
    secret_key=secret_key,
    secure=False
)

# Check if MinIO is accessible by listing buckets
buckets = client.list_buckets()
print(f"Successfully connected to MinIO. Found {len(buckets)} buckets.")

# List bucket names
if buckets:
    print("Buckets:")
    for bucket in buckets:
        print(f"- {bucket.name} (created: {bucket.creation_date})")
else:
    print("No buckets found.")

# Try to create a test bucket
test_bucket_name = "test-bucket"
if not client.bucket_exists(test_bucket_name):
    client.make_bucket(test_bucket_name)
    print(f"Created test bucket: {test_bucket_name}")
else:
    print(f"Test bucket already exists: {test_bucket_name}")
EOF

python /tmp/test_minio_direct.py 