#!/usr/bin/env python3
"""
Test script for VM injection
"""
import requests
import json
import sys
import time

# Base URL for API
API_BASE = "http://localhost:3050/api/v1"

def get_auth_token():
    """Get authentication token"""
    print("Getting auth token...")
    resp = requests.post(f"{API_BASE}/auth/test-token")
    token = resp.json().get("access_token")
    if not token:
        print("Failed to get token")
        sys.exit(1)
    print("Token obtained")
    return token

def list_vms(token):
    """List available VMs"""
    print("Listing VMs...")
    headers = {"Authorization": f"Bearer {token}"}
    resp = requests.get(f"{API_BASE}/vagrant_vm/", headers=headers)
    if resp.status_code != 200:
        print(f"Failed to list VMs: {resp.status_code}")
        print(resp.text)
        sys.exit(1)
    
    vms = resp.json().get("items", [])
    print(f"Found {len(vms)} VMs")
    return vms

def find_ubuntu_vm(vms):
    """Find Ubuntu VM from list"""
    ubuntu_vm = next((vm for vm in vms if "ubuntu" in vm.get("name", "").lower()), None)
    if ubuntu_vm:
        print(f"Found Ubuntu VM: {ubuntu_vm['id']} ({ubuntu_vm['name']})")
        return ubuntu_vm
    else:
        print("No Ubuntu VM found")
        return None

def upload_appimage(token):
    """Upload mock AppImage file"""
    print("Uploading mock AppImage...")
    headers = {"Authorization": f"Bearer {token}"}
    with open("/tmp/cursor-appimage/cursor-test.AppImage", "rb") as f:
        files = {"file": f}
        resp = requests.post(f"{API_BASE}/file_upload/", headers=headers, files=files)
        if resp.status_code not in [200, 201]:
            print(f"Failed to upload file: {resp.status_code}")
            print(resp.text)
            sys.exit(1)
        
        file_data = resp.json()
        print(f"Uploaded file: {file_data['id']} ({file_data['filename']})")
        return file_data

def inject_file_to_vm(token, vm_id, file_data):
    """Inject file to VM by calling API directly"""
    print(f"Injecting file to VM {vm_id}...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Use the new exec endpoint to download and run the file
    try:
        # Create command to download and execute the file
        download_url = f"http://localhost:3050{file_data['download_url']}"
        command = f"wget '{download_url}' -O /home/<USER>/cursor-test.AppImage && chmod +x /home/<USER>/cursor-test.AppImage && /home/<USER>/cursor-test.AppImage > /home/<USER>/cursor_output.log 2>&1 && cat /home/<USER>/cursor_output.log"
        
        # Call VM exec endpoint
        exec_payload = {
            "command": command
        }
        
        print(f"Executing command on VM: {command}")
        resp = requests.post(f"{API_BASE}/vagrant_vm/{vm_id}/exec", headers=headers, json=exec_payload)
        
        if resp.status_code not in [200, 201, 202]:
            print(f"Failed to execute command: {resp.status_code}")
            print(resp.text)
            return None
        
        exec_data = resp.json()
        print(f"Command execution initiated: {exec_data}")
        
        # Wait for action to complete
        print("Waiting for command to complete...")
        time.sleep(5)  # Give it a moment to execute
        
        # Check VM status to see if execution was recorded
        status_resp = requests.get(f"{API_BASE}/vagrant_vm/{vm_id}/status", headers=headers)
        status_data = status_resp.json()
        print(f"VM status after execution: {status_data}")
        
        return exec_data
    except Exception as e:
        print(f"Error during file execution: {str(e)}")
        return None

def main():
    """Main function"""
    # Get auth token
    token = get_auth_token()
    
    # List VMs
    vms = list_vms(token)
    
    # Find Ubuntu VM
    ubuntu_vm = find_ubuntu_vm(vms)
    if not ubuntu_vm:
        sys.exit(1)
    
    # Upload AppImage
    file_data = upload_appimage(token)
    
    # Inject file to VM
    injection = inject_file_to_vm(token, ubuntu_vm["id"], file_data)
    
    if injection:
        print("Test completed successfully!")
    else:
        print("Test failed")

if __name__ == "__main__":
    main() 