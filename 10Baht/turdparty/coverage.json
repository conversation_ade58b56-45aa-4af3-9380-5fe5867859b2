{"meta": {"version": "5.5", "timestamp": "2025-03-20T22:30:00", "branch_coverage": true, "show_contexts": false}, "files": {"api/routes/health.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 11, 12, 13, 14, 16, 17, 19, 20, 22, 24, 25, 27, 29, 30, 31, 33], "summary": {"covered_lines": 24, "num_statements": 30, "percent_covered": 80.0, "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [34, 35, 36, 37, 38, 40], "excluded_lines": []}, "api/routes/file_upload.py": {"executed_lines": [1, 2, 3, 4, 5, 8, 9, 10, 12, 14, 16, 18, 20, 22, 24, 26, 27, 29, 31, 33], "summary": {"covered_lines": 20, "num_statements": 45, "percent_covered": 44.4, "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [35, 36, 37, 38, 40, 42, 44, 46, 48, 50, 52, 55, 56, 58, 60, 62, 64, 65, 67, 69, 70, 72, 74, 76, 78], "excluded_lines": []}, "api/services/file_upload_service.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 15, 17, 19, 20, 22, 24, 26, 28, 30, 32, 34, 35], "summary": {"covered_lines": 23, "num_statements": 30, "percent_covered": 76.7, "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [37, 39, 41, 43, 45, 47, 49], "excluded_lines": []}, "api/schemas/file_upload.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 9, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30], "summary": {"covered_lines": 19, "num_statements": 20, "percent_covered": 95.0, "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [32], "excluded_lines": []}, "api/services/minio_service.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 8, 10, 12, 14, 15, 17, 19, 20, 23], "summary": {"covered_lines": 15, "num_statements": 35, "percent_covered": 42.9, "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63], "excluded_lines": []}}, "totals": {"covered_lines": 101, "num_statements": 160, "percent_covered": 63.1, "missing_lines": 59, "excluded_lines": 0}}