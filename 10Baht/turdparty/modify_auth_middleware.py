#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to modify the auth_middleware.py file to make the get_current_user function respect test mode.
"""
import subprocess
import logging
import sys

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# The code to replace the get_current_user function
REPLACEMENT_CODE = """
async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    \"\"\"
    Get the current authenticated user.
    
    Args:
        token: JWT token from request
        
    Returns:
        User: The authenticated user
        
    Raises:
        HTTPException: If authentication fails
    \"\"\"
    # Check if test mode is enabled
    if test_settings.is_testing():
        logger.debug("Test mode enabled, returning test user")
        # Return a test user
        return User(
            id=999,
            username="test_user",
            email="<EMAIL>",
            is_active=True,
            is_superuser=True
        )
        
    try:
        payload = decode_token(token)
        username = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"}
            )
            
        # You would typically load the user from the database here
        # This is a simplified example
        return User(
            id=payload.get("id"),
            username=username,
            email=payload.get("email", ""),
            is_active=True,
            is_superuser=payload.get("is_superuser", False)
        )
            
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"}
        )
"""

def main():
    """Modify the auth_middleware.py file to make the get_current_user function respect test mode."""
    logger.info("Modifying auth_middleware.py in Docker container...")
    
    try:
        # Check if the container is running
        check_cmd = ["docker", "ps", "-q", "-f", "name=TurdParty-container"]
        result = subprocess.run(check_cmd, capture_output=True, text=True)
        
        if not result.stdout.strip():
            logger.error("TurdParty-container is not running")
            return 1
        
        # Create a backup of the auth_middleware.py file
        backup_cmd = [
            "docker", "exec", "TurdParty-container",
            "cp", "/app/api/middleware/auth_middleware.py", "/app/api/middleware/auth_middleware.py.bak"
        ]
        subprocess.run(backup_cmd, check=True)
        logger.info("Created backup of auth_middleware.py")
        
        # Create a temporary file with the replacement code
        with open("/tmp/get_current_user.py", "w") as f:
            f.write(REPLACEMENT_CODE)
        
        # Copy the file to the container
        copy_cmd = ["docker", "cp", "/tmp/get_current_user.py", "TurdParty-container:/tmp/"]
        subprocess.run(copy_cmd, check=True)
        
        # Replace the get_current_user function in auth_middleware.py
        replace_cmd = [
            "docker", "exec", "TurdParty-container",
            "bash", "-c", 
            "sed -i '/async def get_current_user/,/return current_user/c\\' /app/api/middleware/auth_middleware.py && cat /tmp/get_current_user.py >> /app/api/middleware/auth_middleware.py && echo 'async def get_current_superuser(current_user: User = Depends(get_current_user)) -> User:\\n    \"\"\"\\n    Get the current authenticated superuser.\\n    \\n    Args:\\n        current_user: The current authenticated user\\n        \\n    Returns:\\n        User: The authenticated superuser\\n        \\n    Raises:\\n        HTTPException: If the user is not a superuser\\n    \"\"\"\\n    if not current_user.is_superuser:\\n        raise HTTPException(\\n            status_code=status.HTTP_403_FORBIDDEN,\\n            detail=\"Insufficient permissions\"\\n        )\\n    return current_user' >> /app/api/middleware/auth_middleware.py"
        ]
        subprocess.run(replace_cmd, check=True)
        
        logger.info("Modified auth_middleware.py to respect test mode")
        
        # Restart the container to apply changes
        restart_cmd = ["docker", "restart", "TurdParty-container"]
        subprocess.run(restart_cmd, check=True)
        logger.info("Container restarted to apply changes")
        
        return 0
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Error modifying auth_middleware.py: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 