#!/usr/bin/env python
"""
Check if test_cases table exists and remove it if found.
"""
import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import SQLAlchemy
try:
    from sqlalchemy import create_engine, text, inspect
except ImportError:
    logger.error("SQLAlchemy is not installed. Please install it with 'pip install sqlalchemy'")
    sys.exit(1)

def fix_database():
    """Check and drop the test_cases table if it exists."""
    # Get database URL from environment variable or use default
    database_url = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/app")
    
    logger.info(f"Connecting to database: {database_url}")
    
    try:
        # Create engine
        engine = create_engine(database_url)
        
        # Check if table exists
        inspector = inspect(engine)
        all_tables = inspector.get_table_names()
        logger.info(f"All tables in database: {all_tables}")
        
        # Connect to database
        with engine.connect() as connection:
            # Drop the problematic index directly from PostgreSQL
            logger.info("Attempting to drop index using PostgreSQL command")
            connection.execute(text("DROP INDEX IF EXISTS ix_test_cases_id;"))
            
            # Check if test_cases table exists
            if 'test_cases' in all_tables:
                logger.info("Found test_cases table, dropping it")
                connection.execute(text("DROP TABLE IF EXISTS test_cases CASCADE;"))
                
            # Check for any other tables that might have the problematic index
            logger.info("Checking all indexes in the database")
            result = connection.execute(text("SELECT indexname, tablename FROM pg_indexes WHERE indexname = 'ix_test_cases_id';"))
            for row in result:
                logger.info(f"Found problematic index on table: {row.tablename}")
                logger.info(f"Dropping index ix_test_cases_id from table {row.tablename}")
                connection.execute(text(f"DROP INDEX IF EXISTS ix_test_cases_id;"))
            
            # Commit changes
            connection.commit()
            
            # Verify the index is gone
            result = connection.execute(text("SELECT indexname, tablename FROM pg_indexes WHERE indexname = 'ix_test_cases_id';"))
            if result.fetchone() is None:
                logger.info("Verified index ix_test_cases_id no longer exists")
            else:
                logger.warning("Index ix_test_cases_id still exists. There might be another issue.")
                
        logger.info("Database fix completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error fixing database: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_database()
    sys.exit(0 if success else 1) 