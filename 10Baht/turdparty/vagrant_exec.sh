#!/bin/bash
# Script to run Vagrant commands on the host
# This script is meant to be called from the Docker container
# For testing, it will simulate Vagrant output if real Vagrant is not available

# Log to stderr
log() {
    echo "$@" >&2
}

# Simulate command execution based on command type
simulate_command() {
    local COMMAND="$1"
    
    log "Simulating command: $COMMAND"
    
    echo "=== SIMULATED VAGRANT OUTPUT ==="
    echo "VM Name: $VM_NAME"
    echo "Command: $COMMAND"
    
    # Detect command type and provide appropriate simulation output
    if [[ "$COMMAND" == *"hostname"* ]]; then
        echo "$(hostname)-vagrant-vm"
    elif [[ "$COMMAND" == *"uname"* ]]; then
        if [[ "$COMMAND" == *"-a"* ]]; then
            echo "Linux $(hostname)-vagrant-vm 5.15.0-1031-aws #38-Ubuntu SMP Wed Feb 22 14:07:48 UTC 2023 x86_64 GNU/Linux"
        else
            echo "Linux"
        fi
    elif [[ "$COMMAND" == *"ls"* ]]; then
        if [[ "$COMMAND" == *"-la"* ]]; then
            echo "total 20"
            echo "drwxr-xr-x 5 <USER> <GROUP> 4096 Mar 20 12:34 ."
            echo "drwxr-xr-x 3 <USER>    <GROUP>    4096 Mar 20 12:30 .."
            echo "-rw------- 1 <USER> <GROUP>  220 Mar 20 12:30 .bash_logout"
            echo "-rw------- 1 <USER> <GROUP> 3771 Mar 20 12:30 .bashrc"
            echo "-rw-r--r-- 1 <USER> <GROUP>  807 Mar 20 12:34 .profile"
            echo "drwx------ 2 <USER> <GROUP> 4096 Mar 20 12:34 .ssh"
            echo "-rw-r--r-- 1 <USER> <GROUP>    0 Mar 20 12:34 file1.txt"
            echo "-rw-r--r-- 1 <USER> <GROUP>    0 Mar 20 12:34 file2.txt"
        else
            echo "file1.txt"
            echo "file2.txt"
        fi
    elif [[ "$COMMAND" == *"cat"* ]]; then
        # Extract filename
        FILENAME=$(echo "$COMMAND" | grep -oP 'cat \K.*?($| )' || echo "unknown_file")
        echo "This is simulated content for $FILENAME"
        echo "Line 2 of simulated content"
        echo "Line 3 of simulated content"
    elif [[ "$COMMAND" == *"echo"* ]]; then
        # Extract echo text
        ECHO_TEXT=$(echo "$COMMAND" | grep -oP 'echo \K.*?($| )' || echo "Hello from Vagrant VM")
        # Remove surrounding quotes if present
        ECHO_TEXT="${ECHO_TEXT%\"}"
        ECHO_TEXT="${ECHO_TEXT#\"}"
        ECHO_TEXT="${ECHO_TEXT%\'}"
        ECHO_TEXT="${ECHO_TEXT#\'}"
        echo "$ECHO_TEXT"
    elif [[ "$COMMAND" == *"ps"* ]]; then
        echo "  PID TTY          TIME CMD"
        echo "    1 ?        00:00:01 systemd"
        echo " 1234 ?        00:00:00 sshd"
        echo " 5678 pts/0    00:00:00 bash"
        echo " 9012 pts/0    00:00:00 ps"
    elif [[ "$COMMAND" == *"lscpu"* ]]; then
        echo "Architecture:            x86_64"
        echo "CPU(s):                  2"
        echo "Thread(s) per core:      1"
        echo "Core(s) per socket:      2"
        echo "Socket(s):               1"
        echo "Vendor ID:               GenuineIntel"
        echo "Model name:              Intel(R) Xeon(R) CPU"
    elif [[ "$COMMAND" == *"wget"* ]]; then
        # Simulate wget output
        URL=$(echo "$COMMAND" | grep -oP 'wget \K.*?($| -O )' || echo "http://unknown-url")
        echo "--2025-03-20 15:43:21--  $URL"
        echo "Resolving localhost (localhost)... 127.0.0.1"
        echo "Connecting to localhost (localhost)|127.0.0.1|:3050... connected."
        echo "HTTP request sent, awaiting response... 200 OK"
        echo "Length: 12345 (12K) [application/octet-stream]"
        echo "Saving to: 'cursor-test.AppImage'"
        echo ""
        echo "cursor-test.AppImage  100%[===================>]  12.05K  --.-KB/s    in 0.001s"
        echo ""
        echo "2025-03-20 15:43:22 (12.0 MB/s) - 'cursor-test.AppImage' saved [12345/12345]"
    elif [[ "$COMMAND" == *"chmod"* ]]; then
        # No output for chmod commands if successful
        echo ""
    else
        # Generic simulation for any other command
        echo "Simulated execution successful"
        echo "This is a generic simulation response for: $COMMAND"
        echo "Command completed with exit code 0"
    fi
    
    echo "=== END SIMULATED OUTPUT ==="
}

# Check if VM name is provided
if [ -z "$1" ]; then
    log "Error: VM name not provided"
    exit 1
fi

# Get VM name and command
VM_NAME="$1"
shift
COMMAND="$@"

if [ -z "$COMMAND" ]; then
    log "Error: Command not provided"
    exit 1
fi

log "Executing command on VM: $VM_NAME"
log "Command: $COMMAND"

# Check if we're in test mode or vagrant is not available
if [ -n "$VAGRANT_TEST_MODE" ] || ! command -v vagrant &> /dev/null; then
    log "Running in simulation mode (real Vagrant not available)"
    simulate_command "$COMMAND"
    exit 0
fi

# Navigate to Vagrant directory (this should be modified to match your setup)
VAGRANT_DIR="/home/<USER>/turdparty/vagrant"
if [ ! -d "$VAGRANT_DIR" ]; then
    log "Warning: Vagrant directory not found, using current directory"
    VAGRANT_DIR="."
fi

# Try to change to Vagrant directory
if ! cd "$VAGRANT_DIR"; then
    log "Error: Failed to change to Vagrant directory. Using simulation mode."
    simulate_command "$COMMAND"
    exit 0
fi

# Execute vagrant command
log "Running: vagrant ssh $VM_NAME -c \"$COMMAND\""

# Try to execute the vagrant command, but fall back to simulation if it fails
if ! vagrant ssh "$VM_NAME" -c "$COMMAND"; then
    EXIT_CODE=$?
    log "Vagrant command failed with exit code $EXIT_CODE. Using simulation mode."
    simulate_command "$COMMAND"
    exit 0
fi

# Command succeeded
exit 0 