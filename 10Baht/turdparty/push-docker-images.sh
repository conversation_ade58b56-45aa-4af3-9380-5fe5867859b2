#!/bin/bash
set -e

# Configuration
VERSION="v1.0.0"
REGISTRY="${1:-docker.io/yourusername}"  # Default registry or use first argument

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Preparing to tag and push Docker images to ${REGISTRY}${NC}"

# Tag the images with the registry
echo -e "${GREEN}Tagging images with registry prefix...${NC}"
docker tag dockerwrapper_api:${VERSION} ${REGISTRY}/turdparty-api:${VERSION}
docker tag dockerwrapper_playwright:${VERSION} ${REGISTRY}/turdparty-playwright:${VERSION}

# Optional: Also tag as latest
docker tag dockerwrapper_api:${VERSION} ${REGISTRY}/turdparty-api:latest
docker tag dockerwrapper_playwright:${VERSION} ${REGISTRY}/turdparty-playwright:latest

echo -e "${GREEN}Tagged images:${NC}"
docker images | grep "${REGISTRY}/turdparty"

# Login to Docker registry (remove this if using Docker Hub with credentials already set up)
echo -e "${YELLOW}You may need to login to your registry:${NC}"
echo "docker login ${REGISTRY}"

# Push the images
echo -e "${YELLOW}Ready to push images to registry.${NC}"
echo "Run the following commands to push:"
echo -e "${GREEN}docker push ${REGISTRY}/turdparty-api:${VERSION}${NC}"
echo -e "${GREEN}docker push ${REGISTRY}/turdparty-playwright:${VERSION}${NC}"
echo -e "${GREEN}docker push ${REGISTRY}/turdparty-api:latest${NC}"
echo -e "${GREEN}docker push ${REGISTRY}/turdparty-playwright:latest${NC}"

echo -e "${YELLOW}After pushing, you can use the production config with:${NC}"
echo -e "${GREEN}docker-compose -f .dockerwrapper/docker-compose.production.yml up -d${NC}"

# Make the script executable
chmod +x push-docker-images.sh 