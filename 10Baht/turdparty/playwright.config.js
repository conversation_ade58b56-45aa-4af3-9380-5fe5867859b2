// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * Playwright configuration for TurdParty testing
 * 
 * Defines test settings, browser configurations, and reporting options.
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  testDir: './tests',
  timeout: 60000, // Global timeout for tests: 60 seconds
  expect: {
    timeout: 10000  // Element assertion timeout: 10 seconds
  },
  
  // Don't fail the run if there are test failures
  reporter: [
    ['html'],
    ['list']
  ],
  
  // Run tests in files in parallel
  fullyParallel: false,
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry failing tests to reduce flakiness
  retries: process.env.CI ? 2 : 1,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : 1,
  
  // Configure browser options
  use: {
    // Base URL to use in navigation
    baseURL: process.env.FRONTEND_URL || 'http://turdparty_frontend:3100/ui',
    
    // Viewport dimensions
    viewport: { width: 1280, height: 720 },
    
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // Browser launch options
    launchOptions: {
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--disable-gpu',
      ]
    },
    // Take screenshot on test failure
    screenshot: 'only-on-failure',
  },
  
  // Configure projects for browsers
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
  
  // Local web server configuration - commented out to avoid linter errors
  // If you need to start a local server, uncomment and properly configure this section
  /*
  webServer: {
    command: 'npm run start:test',
    url: 'http://localhost:3100',
    reuseExistingServer: true,
    timeout: 30000, // 30 seconds to start the server
  },
  */
}); 