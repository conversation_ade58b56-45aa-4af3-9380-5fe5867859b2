# TurdParty Project Overview

## Project Description

TurdParty is a comprehensive platform for file upload, VM management, and file injection. It allows users to upload files, create and manage VMs, and inject files into VMs for testing and analysis. The platform is designed to be scalable, secure, and easy to use.

## Mission Statement

To provide a robust and user-friendly platform for managing files, VMs, and file injections, enabling efficient testing and analysis workflows.

## Target Audience

- Security researchers
- Software testers
- DevOps engineers
- System administrators
- Quality assurance teams

## Key Features

- **File Upload and Management**
  - Secure file upload and storage using MinIO
  - File metadata management
  - File versioning and history
  - File search and filtering

- **VM Management**
  - Create and manage Vagrant VMs through a RESTful API
  - VM lifecycle management (create, start, stop, delete)
  - VM status monitoring
  - VM configuration management

- **File Injection**
  - Inject files into VMs for testing and analysis
  - Track injection status and results
  - Support for various injection methods
  - Injection history and audit logs

- **Asynchronous Task Processing**
  - Background task processing with Celery
  - Task queues for different types of operations
  - Task status tracking and monitoring
  - Retry mechanisms for failed tasks

- **User Interface**
  - React-based web UI
  - Responsive design
  - Dark mode support
  - Interactive dashboards and visualizations

- **API**
  - RESTful API for programmatic access
  - Swagger UI documentation
  - API versioning
  - Authentication and authorization

## Technology Stack

- **Backend**
  - Python 3.11+
  - FastAPI
  - SQLAlchemy ORM
  - Alembic for database migrations
  - Celery for asynchronous task processing
  - Redis for message broker and result backend

- **Database**
  - PostgreSQL 14

- **Storage**
  - MinIO for object storage

- **VM Management**
  - Vagrant
  - VirtualBox

- **Frontend**
  - React
  - Material-UI
  - TypeScript

- **Authentication**
  - JWT-based authentication
  - Role-based access control

- **Deployment**
  - Docker
  - Docker Compose
  - Namespaced containers with `turdparty_` prefix

## Architecture

TurdParty follows a modular architecture with clear separation of concerns:

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Frontend   │────▶│  API Server │────▶│  MinIO      │
│             │     │             │     │  Storage    │
└─────────────┘     └─────────────┘     └─────────────┘
                          │                    ▲
                          │                    │
                          ▼                    │
                    ┌─────────────┐     ┌─────────────┐
                    │             │     │             │
                    │  Vagrant    │     │  Celery     │
                    │  VMs        │◀───▶│  Workers    │
                    │             │     │             │
                    └─────────────┘     └─────────────┘
                                              │
                                              │
                                              ▼
                                        ┌─────────────┐
                                        │             │
                                        │  Redis      │
                                        │  Broker     │
                                        │             │
                                        └─────────────┘
```

## Development Workflow

TurdParty follows a feature-driven development workflow:

1. **Feature Specification**: Define the feature requirements and acceptance criteria
2. **Implementation**: Develop the feature according to the specification
3. **Testing**: Write and run tests to verify the feature works as expected
4. **Code Review**: Review the code for quality, security, and performance
5. **Deployment**: Deploy the feature to production

## Testing Strategy

TurdParty uses a comprehensive testing strategy:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components
- **End-to-End Tests**: Test complete workflows from UI to backend
- **Performance Tests**: Test system performance under various conditions
- **Security Tests**: Test for security vulnerabilities
- **Celery Integration Tests**: Test asynchronous task processing

## Docker Container Namespacing

All Docker containers in the TurdParty project follow a consistent naming convention:

```
turdparty_<service_name>
```

For example:
- `turdparty_api` - The API service
- `turdparty_db` - The PostgreSQL database
- `turdparty_redis` - The Redis service
- `turdparty_minio` - The MinIO object storage
- `turdparty_celery_file_ops` - The Celery worker for file operations

This naming convention improves organization and avoids conflicts with other projects.

## Project History

TurdParty was initially developed as a simple file upload service, but has evolved to include VM management, file injection, and asynchronous task processing capabilities. The project has undergone several major refactorings to improve code quality, performance, and maintainability.

## Roadmap

Future plans for TurdParty include:

- Enhanced security features
- Support for additional VM types
- Advanced file analysis capabilities
- Improved performance and scalability
- Enhanced user interface and experience
- Integration with CI/CD pipelines
