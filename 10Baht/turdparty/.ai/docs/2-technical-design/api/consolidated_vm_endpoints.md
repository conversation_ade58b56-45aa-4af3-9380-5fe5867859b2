# Consolidated VM Endpoints

## Overview

This document describes the consolidated VM endpoints that provide a unified interface for all Vagrant VM operations. These endpoints replace the previously scattered and duplicated endpoints across different routes.

## Important Note

The consolidated endpoints under `/api/v1/virtual-machines/...` are now the **primary API** for all VM operations. The old endpoints are still available for backward compatibility but will redirect to the new endpoints. New code should use the consolidated endpoints.

For a detailed migration guide, see [VM Endpoints Migration Guide](vm_endpoints_migration_guide.md).

## Endpoint Structure

All VM-related operations are now available under the `/api/v1/virtual-machines` prefix with a consistent naming convention:

### Connection Management
- `GET /api/v1/virtual-machines/connection` - Get the current connection status to the Vagrant service
- `POST /api/v1/virtual-machines/connection` - Force a new connection check to the Vagrant service

### VM Management
- `GET /api/v1/virtual-machines` - List all VMs
- `POST /api/v1/virtual-machines` - Create a new VM
- `GET /api/v1/virtual-machines/{vm_id}` - Get a specific VM by ID
- `PUT /api/v1/virtual-machines/{vm_id}` - Update a VM
- `DELETE /api/v1/virtual-machines/{vm_id}` - Delete a VM
- `GET /api/v1/virtual-machines/{vm_id}/status` - Get the status of a VM
- `GET /api/v1/virtual-machines/{vm_id}/info` - Get detailed information about a VM
- `GET /api/v1/virtual-machines/templates` - Get available VM templates

### VM Actions
- `POST /api/v1/virtual-machines/{vm_id}/action` - Perform an action on a VM (start, stop, restart, destroy)
- `POST /api/v1/virtual-machines/{vm_id}/start` - Start a VM
- `POST /api/v1/virtual-machines/{vm_id}/stop` - Stop a VM
- `POST /api/v1/virtual-machines/{vm_id}/destroy` - Destroy a VM
- `POST /api/v1/virtual-machines/{vm_id}/suspend` - Suspend a VM
- `POST /api/v1/virtual-machines/{vm_id}/execute` - Execute a command on a VM

### Box Management
- `GET /api/v1/virtual-machines/boxes` - List available Vagrant boxes

### VM Injection
- `GET /api/v1/virtual-machines/injections` - List all VM injections
- `POST /api/v1/virtual-machines/injections` - Create a new VM injection
- `GET /api/v1/virtual-machines/injections/{injection_id}` - Get a specific VM injection by ID
- `PUT /api/v1/virtual-machines/injections/{injection_id}` - Update a VM injection
- `DELETE /api/v1/virtual-machines/injections/{injection_id}` - Delete a VM injection
- `GET /api/v1/virtual-machines/injections/{injection_id}/status` - Get the status of a VM injection
- `POST /api/v1/virtual-machines/injections/{injection_id}/retry` - Retry a failed VM injection

## Backward Compatibility

The original endpoints are still available for backward compatibility, but they now redirect to the new consolidated endpoints:

- `/api/v1/vagrant/...` → `/api/v1/virtual-machines/...`
- `/api/v1/vagrant_vm/...` → `/api/v1/virtual-machines/...`
- `/api/v1/vagrant-vm/...` → `/api/v1/virtual-machines/...`
- `/api/v1/vm_injection/...` → `/api/v1/virtual-machines/injections/...`
- `/api/v1/vm-injection/...` → `/api/v1/virtual-machines/injections/...`
- `/api/v1/vms/...` → `/api/v1/virtual-machines/...`
- `/api/v1/vagrant/vagrant/...` → `/api/v1/virtual-machines/...` (fixing double-prefixed endpoints)

These redirects ensure that existing code will continue to work, but all requests will be handled by the consolidated endpoints.

## Benefits of Consolidation

1. **Consistency**: All VM-related operations follow a consistent naming convention and structure
2. **Discoverability**: All VM-related operations are grouped under a single prefix
3. **Maintainability**: Easier to maintain and extend with new functionality
4. **Documentation**: Better documentation and organization of endpoints
5. **Reduced Duplication**: Eliminates redundant endpoints with similar functionality

## Migration Guide

When developing new features or updating existing ones, use the consolidated endpoints instead of the original ones. The original endpoints will be deprecated in a future release.

Example of migrating from the original endpoints to the consolidated ones:

```javascript
// Old way - inconsistent and scattered endpoints
fetch('/api/v1/vagrant/status/my-vm')
  .then(response => response.json())
  .then(data => console.log(data));

// Old way - double-prefixed endpoints
fetch('/api/v1/vagrant/vagrant/status/my-vm')
  .then(response => response.json())
  .then(data => console.log(data));

// New way - consistent and organized endpoints
fetch('/api/v1/virtual-machines/my-vm/status')
  .then(response => response.json())
  .then(data => console.log(data));
```

## Implementation Details

The consolidated endpoints are implemented in `api/routes/consolidated_vagrant.py` and registered in `api/application.py`. They use the same services and models as the original endpoints, ensuring consistent behavior.

## Usage Examples

### List All VMs

**Request:**
```http
GET /api/v1/virtual-machines
Authorization: Bearer {token}
```

**Response:**
```json
{
  "items": [
    {
      "id": "ubuntu-focal",
      "name": "Ubuntu Focal",
      "status": "running",
      "box": "ubuntu/focal64",
      "created_at": "2023-06-15T10:30:00Z",
      "updated_at": "2023-06-15T10:35:00Z"
    },
    {
      "id": "debian-bullseye",
      "name": "Debian Bullseye",
      "status": "stopped",
      "box": "debian/bullseye64",
      "created_at": "2023-06-14T14:20:00Z",
      "updated_at": "2023-06-14T16:45:00Z"
    }
  ],
  "total": 2
}
```

### Get VM Status

**Request:**
```http
GET /api/v1/virtual-machines/ubuntu-focal/status
Authorization: Bearer {token}
```

**Response:**
```json
{
  "id": "ubuntu-focal",
  "status": "running",
  "updated_at": "2023-06-15T10:35:00Z"
}
```

### Start a VM

**Request:**
```http
POST /api/v1/virtual-machines/debian-bullseye/start
Authorization: Bearer {token}
Content-Type: application/json

{}
```

**Response:**
```json
{
  "id": "debian-bullseye",
  "status": "starting",
  "message": "VM is starting"
}
```

### Execute a Command on a VM

**Request:**
```http
POST /api/v1/virtual-machines/ubuntu-focal/execute
Authorization: Bearer {token}
Content-Type: application/json

{
  "command": "ls -la /home"
}
```

**Response:**
```json
{
  "id": "ubuntu-focal",
  "command": "ls -la /home",
  "stdout": "total 12\ndrwxr-xr-x  3 <USER> <GROUP> 4096 Jun 15 10:30 .\ndrwxr-xr-x 24 <USER> <GROUP> 4096 Jun 15 10:30 ..\ndrwxr-xr-x  2 <USER> <GROUP> 4096 Jun 15 10:30 ubuntu\n",
  "stderr": "",
  "exit_code": 0
}
```

### List VM Templates

**Request:**
```http
GET /api/v1/virtual-machines/templates
Authorization: Bearer {token}
```

**Response:**
```json
[
  {
    "id": "ubuntu-small",
    "name": "Ubuntu Small",
    "description": "Small Ubuntu VM with 1GB RAM and 1 CPU",
    "box": "ubuntu/focal64",
    "memory_mb": 1024,
    "cpus": 1,
    "disk_gb": 10
  },
  {
    "id": "ubuntu-medium",
    "name": "Ubuntu Medium",
    "description": "Medium Ubuntu VM with 2GB RAM and 2 CPUs",
    "box": "ubuntu/focal64",
    "memory_mb": 2048,
    "cpus": 2,
    "disk_gb": 20
  },
  {
    "id": "ubuntu-large",
    "name": "Ubuntu Large",
    "description": "Large Ubuntu VM with 4GB RAM and 4 CPUs",
    "box": "ubuntu/focal64",
    "memory_mb": 4096,
    "cpus": 4,
    "disk_gb": 40
  }
]
```

### Create a VM Injection

**Request:**
```http
POST /api/v1/virtual-machines/injections
Authorization: Bearer {token}
Content-Type: application/json

{
  "vm_id": "ubuntu-focal",
  "source_path": "/tmp/files/script.sh",
  "destination_path": "/home/<USER>/script.sh",
  "execute": true
}
```

**Response:**
```json
{
  "id": "inj-123456",
  "vm_id": "ubuntu-focal",
  "source_path": "/tmp/files/script.sh",
  "destination_path": "/home/<USER>/script.sh",
  "execute": true,
  "status": "pending",
  "created_at": "2023-06-15T11:00:00Z"
}
```

### Get VM Injection Status

**Request:**
```http
GET /api/v1/virtual-machines/injections/inj-123456/status
Authorization: Bearer {token}
```

**Response:**
```json
{
  "id": "inj-123456",
  "status": "completed",
  "message": "Injection completed successfully",
  "execution_result": {
    "stdout": "Hello, World!\n",
    "stderr": "",
    "exit_code": 0
  },
  "updated_at": "2023-06-15T11:01:30Z"
}
```
