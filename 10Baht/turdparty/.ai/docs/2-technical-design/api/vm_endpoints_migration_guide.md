# VM Endpoints Migration Guide

## Overview

This guide provides instructions for migrating from the old VM-related endpoints to the new consolidated endpoints. The new endpoints provide a more consistent and intuitive API structure while maintaining backward compatibility with existing code.

## Why Migrate?

The new consolidated endpoints offer several advantages:

1. **Consistency**: All VM-related operations follow a consistent naming convention and structure
2. **Discoverability**: Easier to find and use the right endpoints
3. **Maintainability**: Simpler to maintain and extend with new features
4. **Documentation**: Better documentation and examples

## Migration Timeline

- **Phase 1 (Current)**: New consolidated endpoints available, old endpoints redirected
- **Phase 2 (Next Release)**: Old endpoints marked as deprecated in documentation
- **Phase 3 (Future Release)**: Old endpoints removed

## Endpoint Mapping

### Vagrant VM Endpoints

| Old Endpoint | New Endpoint | HTTP Method |
|--------------|--------------|-------------|
| `/api/v1/vagrant_vm/` | `/api/v1/virtual-machines` | GET |
| `/api/v1/vagrant_vm/{id}` | `/api/v1/virtual-machines/{id}` | GET |
| `/api/v1/vagrant_vm/{id}` | `/api/v1/virtual-machines/{id}` | PUT |
| `/api/v1/vagrant_vm/{id}` | `/api/v1/virtual-machines/{id}` | DELETE |
| `/api/v1/vagrant_vm/{id}/start` | `/api/v1/virtual-machines/{id}/start` | POST |
| `/api/v1/vagrant_vm/{id}/stop` | `/api/v1/virtual-machines/{id}/stop` | POST |
| `/api/v1/vagrant_vm/{id}/destroy` | `/api/v1/virtual-machines/{id}/destroy` | POST |
| `/api/v1/vagrant_vm/{id}/suspend` | `/api/v1/virtual-machines/{id}/suspend` | POST |
| `/api/v1/vagrant_vm/{id}/resume` | `/api/v1/virtual-machines/{id}/resume` | POST |
| `/api/v1/vagrant_vm/{id}/execute` | `/api/v1/virtual-machines/{id}/execute` | POST |
| `/api/v1/vagrant_vm/{id}/logs` | `/api/v1/virtual-machines/{id}/logs` | GET |
| `/api/v1/vagrant_vm/{id}/resources` | `/api/v1/virtual-machines/{id}/resources` | GET |
| `/api/v1/vagrant_vm/templates` | `/api/v1/virtual-machines/templates` | GET |

### Vagrant Endpoints

| Old Endpoint | New Endpoint | HTTP Method |
|--------------|--------------|-------------|
| `/api/v1/vagrant/status/{id}` | `/api/v1/virtual-machines/{id}/status` | GET |
| `/api/v1/vagrant/info/{id}` | `/api/v1/virtual-machines/{id}/info` | GET |
| `/api/v1/vagrant/remote-status/{id}` | `/api/v1/virtual-machines/{id}/status` | GET |
| `/api/v1/vagrant/boxes` | `/api/v1/virtual-machines/boxes` | GET |
| `/api/v1/vagrant/connection/status` | `/api/v1/virtual-machines/connection` | GET |
| `/api/v1/vagrant/up/{id}` | `/api/v1/virtual-machines/{id}/start` | POST |
| `/api/v1/vagrant/halt/{id}` | `/api/v1/virtual-machines/{id}/stop` | POST |
| `/api/v1/vagrant/destroy/{id}` | `/api/v1/virtual-machines/{id}/destroy` | POST |
| `/api/v1/vagrant/execute/{id}` | `/api/v1/virtual-machines/{id}/execute` | POST |
| `/api/v1/vagrant/vms/{id}/suspend` | `/api/v1/virtual-machines/{id}/suspend` | POST |

### VM Injection Endpoints

| Old Endpoint | New Endpoint | HTTP Method |
|--------------|--------------|-------------|
| `/api/v1/vm_injection/` | `/api/v1/virtual-machines/injections` | GET |
| `/api/v1/vm_injection/` | `/api/v1/virtual-machines/injections` | POST |
| `/api/v1/vm_injection/{id}` | `/api/v1/virtual-machines/injections/{id}` | GET |
| `/api/v1/vm_injection/{id}` | `/api/v1/virtual-machines/injections/{id}` | PUT |
| `/api/v1/vm_injection/{id}` | `/api/v1/virtual-machines/injections/{id}` | DELETE |
| `/api/v1/vm_injection/{id}/status` | `/api/v1/virtual-machines/injections/{id}/status` | GET |

## Code Examples

### JavaScript/TypeScript

#### Before:
```javascript
// Get all VMs
const getVMs = async () => {
  const response = await fetch('/api/v1/vagrant_vm/');
  return response.json();
};

// Start a VM
const startVM = async (vmId) => {
  const response = await fetch(`/api/v1/vagrant_vm/${vmId}/start`, {
    method: 'POST'
  });
  return response.json();
};

// Get VM injection status
const getInjectionStatus = async (injectionId) => {
  const response = await fetch(`/api/v1/vm_injection/${injectionId}/status`);
  return response.json();
};
```

#### After:
```javascript
// Get all VMs
const getVMs = async () => {
  const response = await fetch('/api/v1/virtual-machines');
  return response.json();
};

// Start a VM
const startVM = async (vmId) => {
  const response = await fetch(`/api/v1/virtual-machines/${vmId}/start`, {
    method: 'POST'
  });
  return response.json();
};

// Get VM injection status
const getInjectionStatus = async (injectionId) => {
  const response = await fetch(`/api/v1/virtual-machines/injections/${injectionId}/status`);
  return response.json();
};
```

### Python

#### Before:
```python
import requests

# Get all VMs
def get_vms():
    response = requests.get('/api/v1/vagrant_vm/')
    return response.json()

# Start a VM
def start_vm(vm_id):
    response = requests.post(f'/api/v1/vagrant_vm/{vm_id}/start')
    return response.json()

# Get VM injection status
def get_injection_status(injection_id):
    response = requests.get(f'/api/v1/vm_injection/{injection_id}/status')
    return response.json()
```

#### After:
```python
import requests

# Get all VMs
def get_vms():
    response = requests.get('/api/v1/virtual-machines')
    return response.json()

# Start a VM
def start_vm(vm_id):
    response = requests.post(f'/api/v1/virtual-machines/{vm_id}/start')
    return response.json()

# Get VM injection status
def get_injection_status(injection_id):
    response = requests.get(f'/api/v1/virtual-machines/injections/{injection_id}/status')
    return response.json()
```

## Testing Your Migration

After updating your code to use the new endpoints, you should test thoroughly to ensure everything works as expected. The old endpoints will continue to work through redirects, but it's best to use the new endpoints directly for better performance and future compatibility.

## Reporting Issues

If you encounter any issues during migration, please report them to the development team. We're committed to making this transition as smooth as possible.

## FAQ

### Will the old endpoints continue to work?
Yes, the old endpoints will continue to work through redirects for the foreseeable future. However, they will eventually be deprecated and removed.

### Do I need to update all my code at once?
No, you can migrate gradually. The old endpoints will continue to work, so you can update your code at your own pace.

### Will the response format change?
No, the response format will remain the same. Only the endpoint URLs are changing.

### What about authentication and authorization?
Authentication and authorization requirements remain the same for the new endpoints.

### Are there any performance benefits to using the new endpoints?
Yes, using the new endpoints directly avoids the redirect overhead and provides slightly better performance.
