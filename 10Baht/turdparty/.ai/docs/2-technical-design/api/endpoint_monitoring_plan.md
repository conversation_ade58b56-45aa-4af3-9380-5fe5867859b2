# Endpoint Monitoring Plan

## Overview

This document outlines the plan for monitoring the usage of VM-related endpoints to track the migration from old endpoints to the new consolidated endpoints. This monitoring will help us understand adoption rates, identify areas that need attention, and make informed decisions about when to deprecate the old endpoints.

## Monitoring Objectives

1. **Track Usage**: Monitor the usage of both old and new endpoints
2. **Identify Clients**: Determine which clients are still using the old endpoints
3. **Measure Adoption**: Calculate the adoption rate of the new endpoints
4. **Detect Issues**: Identify any issues or errors related to the endpoint migration
5. **Inform Decisions**: Provide data to inform decisions about deprecation timelines

## Implementation

### 1. Add Logging Middleware

Add a middleware to log all requests to VM-related endpoints:

```python
@app.middleware("http")
async def log_vm_endpoints(request: Request, call_next):
    # Get the request path
    path = request.url.path
    
    # Check if it's a VM-related endpoint
    is_old_endpoint = any([
        "/api/v1/vagrant/" in path,
        "/api/v1/vagrant_vm/" in path,
        "/api/v1/vagrant-vm/" in path,
        "/api/v1/vm_injection/" in path,
        "/api/v1/vm-injection/" in path
    ])
    
    is_new_endpoint = "/api/v1/virtual-machines" in path
    
    # If it's a VM-related endpoint, log the request
    if is_old_endpoint or is_new_endpoint:
        # Get client information
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        auth_header = request.headers.get("authorization", "")
        
        # Log the request
        logger.info(
            f"VM Endpoint Request: path={path}, "
            f"method={request.method}, "
            f"client_ip={client_ip}, "
            f"user_agent={user_agent}, "
            f"is_old_endpoint={is_old_endpoint}, "
            f"is_new_endpoint={is_new_endpoint}"
        )
    
    # Continue processing the request
    response = await call_next(request)
    return response
```

### 2. Create a Dashboard

Create a dashboard to visualize the endpoint usage data:

- **Endpoint Usage Over Time**: Line chart showing the usage of old vs. new endpoints over time
- **Adoption Rate**: Percentage of requests using the new endpoints
- **Client Breakdown**: Table showing which clients are still using the old endpoints
- **Error Rate**: Line chart showing the error rate for old vs. new endpoints
- **Response Time**: Line chart showing the response time for old vs. new endpoints

### 3. Set Up Alerts

Set up alerts to notify the team of important events:

- **Low Adoption Rate**: Alert if the adoption rate is below the target after a certain period
- **High Error Rate**: Alert if the error rate for either old or new endpoints exceeds a threshold
- **Specific Clients**: Alert if specific high-priority clients are still using the old endpoints

## Data Collection

Collect the following data for each request:

- **Timestamp**: When the request was made
- **Endpoint**: The full path of the endpoint
- **Method**: The HTTP method (GET, POST, etc.)
- **Client IP**: The IP address of the client
- **User Agent**: The user agent string
- **Authorization**: The authorization header (anonymized)
- **Response Status**: The HTTP status code of the response
- **Response Time**: The time taken to process the request
- **Endpoint Type**: Whether it's an old or new endpoint

## Analysis

Perform the following analyses on the collected data:

### 1. Usage Trends

- **Daily/Weekly/Monthly Usage**: Track how usage of old vs. new endpoints changes over time
- **Peak Usage Times**: Identify when the endpoints are most heavily used
- **Usage by Endpoint**: Break down usage by specific endpoints to identify which ones are most popular

### 2. Client Analysis

- **Client Identification**: Use IP addresses and user agents to identify specific clients
- **Client Migration Progress**: Track how quickly each client is migrating to the new endpoints
- **Client Communication**: Use this data to target communication to clients still using old endpoints

### 3. Performance Comparison

- **Response Time**: Compare response times between old and new endpoints
- **Error Rate**: Compare error rates between old and new endpoints
- **Resource Usage**: Compare server resource usage between old and new endpoints

## Reporting

Generate the following reports:

- **Weekly Migration Report**: Summary of migration progress over the past week
- **Monthly Executive Report**: High-level overview of migration status for executive stakeholders
- **Client-Specific Reports**: Detailed reports for specific clients showing their migration progress

## Timeline and Milestones

- **Month 1**: Set up monitoring infrastructure and establish baseline metrics
- **Month 2-3**: Monitor adoption rate and reach out to clients still using old endpoints
- **Month 4-5**: Evaluate progress and adjust strategy as needed
- **Month 6**: Make decision about deprecation timeline based on adoption rate

## Success Criteria

The migration will be considered successful when:

- **90% Adoption Rate**: At least 90% of requests are using the new endpoints
- **Key Client Migration**: All key clients have migrated to the new endpoints
- **Low Error Rate**: The error rate for the new endpoints is below 0.1%
- **Stable Performance**: The response time for the new endpoints is stable and within acceptable limits

## Deprecation Plan

Once the success criteria are met, proceed with the following deprecation plan:

1. **Announce Deprecation**: Formally announce the deprecation of the old endpoints with a specific end-of-life date
2. **Add Deprecation Headers**: Add deprecation headers to responses from old endpoints
3. **Increase Logging**: Increase logging for old endpoints to capture any remaining usage
4. **Final Communication**: Send final communication to any clients still using old endpoints
5. **Remove Redirects**: Remove the redirects from old endpoints to new endpoints
6. **Remove Old Endpoints**: Remove the old endpoint code entirely
