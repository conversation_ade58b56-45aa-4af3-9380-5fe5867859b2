# Celery Architecture in TurdParty

## Overview

TurdParty uses Celery for asynchronous task processing, allowing long-running operations to be executed in the background without blocking the API. This document describes the architecture of the Celery implementation.

## Architecture Components

The Celery implementation consists of the following components:

1. **Message Broker**: <PERSON>is serves as both the message broker and result backend
2. **Task Queues**: Specialized queues for different types of tasks
   - `file_ops`: For file upload and processing operations
   - `vm_lifecycle`: For VM creation and management
   - `vm_injection`: For file injection into VMs
   - `monitoring`: For monitoring and health check tasks
3. **Celery Workers**: Specialized workers for different task types
   - `turdparty_celery_file_ops`: Processes file operations tasks
   - `turdparty_celery_vm_ops`: Processes VM lifecycle and injection tasks
   - `turdparty_celery_monitoring`: Processes monitoring tasks
4. **Task Status Tracking**: Database tables for tracking task status and results
5. **API Integration**: Asynchronous API endpoints for submitting tasks and checking status

## Task Flow

The typical flow for an asynchronous task is:

1. <PERSON><PERSON> submits a request to an async API endpoint
2. API creates a TaskStatus record in the database (PENDING)
3. API enqueues a task in the appropriate Celery queue
4. API returns the task_id to the client (202 Accepted)
5. Celery worker picks up the task from the queue
6. Worker updates the TaskStatus record (STARTED)
7. Worker processes the task
8. Worker updates the TaskStatus record (SUCCESS/FAILURE)
9. Client can check the task status using the task_id

## Task Implementation

Tasks are implemented in the following files:

- `tasks/base_task.py`: Base task class with common functionality
- `tasks/file_tasks.py`: File operation tasks
- `tasks/vm_tasks.py`: VM lifecycle and injection tasks
- `tasks/monitoring_tasks.py`: Monitoring and health check tasks

Each task inherits from the `TurdPartyTask` base class, which provides:

- Error handling
- Task status tracking
- Retry mechanisms
- Logging

## Configuration

Celery configuration is defined in `celeryconfig.py` in the project root:

```python
# Broker settings
broker_url = 'redis://redis:6379/0'
result_backend = 'redis://redis:6379/1'

# Task serialization
task_serializer = 'json'
result_serializer = 'json'
accept_content = ['json']

# Task routing
task_routes = {
    'tasks.file_tasks.*': {'queue': 'file_ops'},
    'tasks.vm_tasks.*': {'queue': 'vm_lifecycle'},
    'tasks.vm_injection_tasks.*': {'queue': 'vm_injection'},
    'tasks.monitoring_tasks.*': {'queue': 'monitoring'},
}

# Task execution settings
task_acks_late = True
worker_prefetch_multiplier = 1
task_track_started = True

# Task result settings
task_ignore_result = False
result_expires = 60 * 60 * 24  # 24 hours
```

## Docker Integration

Celery workers run in Docker containers with the `turdparty_` prefix:

- `turdparty_celery_file_ops`: Processes file operations tasks
- `turdparty_celery_vm_ops`: Processes VM lifecycle and injection tasks
- `turdparty_celery_monitoring`: Processes monitoring tasks

The Docker Compose configuration for Celery workers is in `.dockerwrapper/docker-compose.test.yml`.

## Testing

Celery integration tests are in `tests/integration/test_celery_integration.py`. These tests verify:

1. Task creation and execution
2. Task result retrieval
3. Task status tracking
4. Error handling and retries

To run Celery integration tests:

```bash
./run_celery_tests.sh
```

## Best Practices

1. **Task Idempotency**: Tasks should be idempotent (can be run multiple times without side effects)
2. **Error Handling**: Tasks should handle errors gracefully and update task status
3. **Retry Mechanisms**: Tasks should use retry mechanisms for transient errors
4. **Task Status**: Tasks should update task status at key points
5. **Task Granularity**: Tasks should be granular and focused on a single operation
6. **Task Dependencies**: Complex workflows should be broken down into smaller tasks
