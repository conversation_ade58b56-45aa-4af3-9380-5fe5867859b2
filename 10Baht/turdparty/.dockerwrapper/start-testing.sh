#!/bin/bash

# Simple script to start a specific testing environment without prompting
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Starting Testing Environment                       ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Navigate to the root directory
cd "$(dirname "$0")/.."

# Determine which environment to start
if [ "$1" == "prod" ] || [ "$1" == "1" ]; then
    COMPOSE_FILE=".dockerwrapper/docker-compose.yml"
    echo -e "${YELLOW}Starting production testing environment...${NC}"
elif [ "$1" == "dev" ] || [ "$1" == "2" ]; then
    COMPOSE_FILE=".dockerwrapper/dev-compose.yml"
    echo -e "${YELLOW}Starting development testing environment...${NC}"
elif [ "$1" == "playwright" ] || [ "$1" == "3" ]; then
    COMPOSE_FILE=".dockerwrapper/docker-compose.playwright.yml"
    echo -e "${YELLOW}Starting Playwright testing environment...${NC}"
else
    echo -e "${YELLOW}Usage: $0 [prod|dev|playwright] or [1|2|3]${NC}"
    echo -e "${YELLOW}  prod/1: Production Testing (docker-compose.yml)${NC}"
    echo -e "${YELLOW}  dev/2: Development Testing (dev-compose.yml)${NC}"
    echo -e "${YELLOW}  playwright/3: Playwright Testing (docker-compose.playwright.yml)${NC}"
    exit 1
fi

# Ensure necessary directories exist
echo -e "${YELLOW}Creating necessary directories...${NC}"
mkdir -p test_screenshots test-results playwright-report uploads

# Stop related containers if requested
if [ "$2" == "clean" ]; then
    echo -e "${YELLOW}Stopping existing containers...${NC}"
    docker ps --filter "name=turdparty" -q | xargs -r docker stop
    docker ps -a --filter "name=turdparty" --filter "status=exited" -q | xargs -r docker rm
fi

# Start the containers
echo -e "${YELLOW}Starting containers with ${COMPOSE_FILE}...${NC}"
docker compose -f $COMPOSE_FILE up -d

# Wait for containers to start
echo -e "${YELLOW}Waiting for containers to start up...${NC}"
sleep 10

# Check container status
echo -e "${YELLOW}Checking container status...${NC}"
docker compose -f $COMPOSE_FILE ps

echo -e "${GREEN}Containers started!${NC}"
echo -e "${YELLOW}To check logs, use: docker compose -f ${COMPOSE_FILE} logs${NC}" 