#!/bin/bash

# Script to build and run Playwright tests in a dedicated container
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Running Playwright Tests in Docker Container       ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Navigate to the root directory
cd "$(dirname "$0")/.."

# Create necessary directories
mkdir -p test_screenshots test-results playwright-report

# Check existing containers
if docker compose -f .dockerwrapper/docker-compose.playwright.yml ps | grep -q "Up"; then
  echo -e "${YELLOW}Stopping existing playwright containers...${NC}"
  docker compose -f .dockerwrapper/docker-compose.playwright.yml down
fi

# Build and start the Playwright container and all services
echo -e "${YELLOW}Starting Playwright test environment...${NC}"
docker compose -f .dockerwrapper/docker-compose.playwright.yml up -d --build

# Give services time to initialize
echo -e "${YELLOW}Waiting for services to be ready...${NC}"
sleep 10

# Check if the services are running properly
echo -e "${YELLOW}Checking container health status...${NC}"
if ! docker ps | grep -q "turdparty_api"; then
  echo -e "${RED}Error: API container is not running${NC}"
  docker compose -f .dockerwrapper/docker-compose.playwright.yml logs api
  exit 1
fi

if ! docker ps | grep -q "turdparty_frontend"; then
  echo -e "${RED}Error: Frontend container is not running${NC}"
  docker compose -f .dockerwrapper/docker-compose.playwright.yml logs frontend
  exit 1
fi

if ! docker ps | grep -q "turdparty_playwright"; then
  echo -e "${RED}Error: Playwright container is not running${NC}"
  docker compose -f .dockerwrapper/docker-compose.playwright.yml logs playwright
  exit 1
fi

# Determine if we're running specific tests or all tests
TEST_ARGS=""
if [ "$1" == "--all" ]; then
  echo -e "${YELLOW}Running all tests${NC}"
  TEST_ARGS=""
elif [ -n "$1" ] && [ "$1" != "--headed" ]; then
  echo -e "${YELLOW}Running specific test: $1${NC}"
  TEST_ARGS="$1"
else
  echo -e "${YELLOW}Running verification test${NC}"
  TEST_ARGS="tests/verification/basic.spec.js"
fi

# Determine if we're running in headed mode
HEADED_ARGS=""
if [ "$1" == "--headed" ] || [ "$2" == "--headed" ]; then
  echo -e "${YELLOW}Running in headed mode${NC}"
  HEADED_ARGS="--headed"
fi

# Run the tests in the Playwright container
echo -e "${YELLOW}Running Playwright tests...${NC}"
docker exec -it turdparty_playwright bash -c "cd /app && npx playwright test $TEST_ARGS $HEADED_ARGS"
TEST_RESULT=$?

# Copy reports and screenshots
echo -e "${YELLOW}Copying test reports and screenshots...${NC}"
docker cp turdparty_playwright:/app/playwright-report/. ./playwright-report/ || true
docker cp turdparty_playwright:/app/test-results/. ./test-results/ || true
docker cp turdparty_playwright:/app/test_screenshots/. ./test_screenshots/ || true

# Check test result
if [ $TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tests completed successfully!${NC}"
  echo -e "${YELLOW}Test reports available at:${NC}"
  echo -e "  - test_screenshots/"
  echo -e "  - test-results/"
  echo -e "  - playwright-report/index.html"
else
  echo -e "${RED}Tests failed. Check the output above for details.${NC}"
  echo -e "${YELLOW}Logs available with: docker compose -f .dockerwrapper/docker-compose.playwright.yml logs${NC}"
fi

# Ask if we should clean up the containers
read -p "Do you want to stop the test containers? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  echo -e "${YELLOW}Stopping test containers...${NC}"
  docker compose -f .dockerwrapper/docker-compose.playwright.yml down
else
  echo -e "${YELLOW}Leaving test containers running...${NC}"
  echo -e "${YELLOW}You can stop them with: docker compose -f .dockerwrapper/docker-compose.playwright.yml down${NC}"
fi

exit $TEST_RESULT 