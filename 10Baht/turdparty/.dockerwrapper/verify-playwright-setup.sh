#!/bin/bash

# Simple script to verify <PERSON><PERSON> is working correctly
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Verifying Playwright Installation                   ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Check if the container is running
if ! docker ps | grep -q "playwright-test"; then
  echo -e "${RED}Error: Playwright test container is not running${NC}"
  echo -e "${YELLOW}Start it with: .dockerwrapper/persistent-test-env.sh${NC}"
  exit 1
fi

echo -e "${YELLOW}Running simple verification test...${NC}"

# Run the verification test
docker exec -it playwright-test bash -c "cd /app && npx playwright -V || echo 'Playwright command not found'"
docker exec -it playwright-test bash -c "cd /app && npx playwright test --version || echo 'Playwright test command not found'"

# Check what's in the node_modules directory
echo -e "${YELLOW}Checking node_modules...${NC}"
docker exec -it playwright-test bash -c "cd /app && ls -la node_modules/@playwright || echo '@playwright not found'"
docker exec -it playwright-test bash -c "cd /app && ls -la node_modules/playwright-core || echo 'playwright-core not found'"

# Try installing directly
echo -e "${YELLOW}Installing dependencies directly...${NC}"
docker exec -it playwright-test bash -c "cd /app && npm install -g playwright-core @playwright/test"

# Run the simple test
echo -e "${YELLOW}Running verification test...${NC}"
docker exec -it playwright-test bash -c "cd /app && /usr/local/bin/npx playwright test tests/playwright/simple-verification.spec.js --reporter=line"

exit $? 