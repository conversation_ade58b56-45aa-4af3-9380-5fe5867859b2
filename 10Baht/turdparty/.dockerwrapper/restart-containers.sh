#!/bin/bash
set -e

# Change to the .dockerwrapper directory
cd "$(dirname "$0")"

# Process arguments
LIVE_MODE=false
DEV_MODE=false

for arg in "$@"; do
  case $arg in
    dev)
      DEV_MODE=true
      ;;
    --live)
      LIVE_MODE=true
      ;;
  esac
done

# Determine if we're in development or production mode
if [ "$DEV_MODE" = true ]; then
  COMPOSE_FILE="dev-compose.yml"
  echo "Using development configuration: $COMPOSE_FILE"
else
  COMPOSE_FILE="docker-compose.yml"
  echo "Using production configuration: $COMPOSE_FILE"
fi

# Stop any existing containers
echo "Stopping existing containers..."
docker-compose -f $COMPOSE_FILE down

# Create uploads directory if it doesn't exist
mkdir -p ../uploads
chmod 755 ../uploads

# Start the containers
if [ "$LIVE_MODE" = true ]; then
  echo "Starting containers with $COMPOSE_FILE in live mode (press Ctrl+C to stop)..."
  docker-compose -f $COMPOSE_FILE up
else
  echo "Starting containers with $COMPOSE_FILE in detached mode..."
  docker-compose -f $COMPOSE_FILE up -d

  # Show running containers
  echo "Running containers:"
  docker ps

  echo "Container restart completed."
  echo "API is available at http://localhost:3050"
  echo "Frontend is available at http://localhost:3100"
  echo "Dashboard is available at http://localhost:3150"
  echo "PostgreSQL is available at localhost:3200"
  echo "Minio (test) is available at http://localhost:3300 (API) and http://localhost:3301 (Console)"
  
  echo ""
  echo "To see container logs, run: docker-compose -f $COMPOSE_FILE logs -f"
  echo "To stop containers, run: docker-compose -f $COMPOSE_FILE down"
fi 