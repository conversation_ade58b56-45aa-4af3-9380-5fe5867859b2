#!/bin/bash

set -e

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=======================================================${NC}"
echo -e "${GREEN}    Fixing Playwright Persistent Container Environment  ${NC}"
echo -e "${GREEN}=======================================================${NC}"

# Check if the container is running
CONTAINER_ID=$(docker ps -q --filter "name=turdparty_playwright")

if [ -z "$CONTAINER_ID" ]; then
    echo -e "${RED}Error: The Playwright test container is not running.${NC}"
    echo -e "Start the container with: ${YELLOW}.dockerwrapper/persistent-test-env.sh${NC}"
    exit 1
fi

echo -e "Playwright container found with ID: ${YELLOW}$CONTAINER_ID${NC}"

# Fix permissions on test-results directory
echo -e "\n${GREEN}Fixing permissions for test-results directory...${NC}"
docker exec -it $CONTAINER_ID bash -c "sudo mkdir -p /app/test-results /app/test_screenshots && sudo chmod -R 777 /app/test-results /app/test_screenshots"

# Clean installation of dependencies
echo -e "\n${GREEN}Reinstalling Playwright dependencies...${NC}"
docker exec -it $CONTAINER_ID bash -c "cd /app && rm -rf node_modules && npm init -y && npm install --no-save @playwright/test playwright playwright-core uuid"

# Create a simple test config
echo -e "\n${GREEN}Creating Playwright configuration...${NC}"
docker exec -it $CONTAINER_ID bash -c "cd /app && cat > playwright.config.js << 'EOF'
/** @type {import('@playwright/test').PlaywrightTestConfig} */
const config = {
  testDir: './tests/playwright',
  timeout: 60000,
  reporter: 'list',
  use: {
    headless: true,
    viewport: { width: 1280, height: 720 },
    launchOptions: {
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    },
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
  },
  outputDir: 'test-results',
};

module.exports = config;
EOF"

# Verify the setup
echo -e "\n${GREEN}Verifying setup with simple test...${NC}"
docker exec -it $CONTAINER_ID bash -c "cd /app && npx playwright test tests/playwright/simple-verification.spec.js --reporter=list"

echo -e "\n${GREEN}Fix complete!${NC}"
echo -e "You can now run tests with: ${YELLOW}.dockerwrapper/run-test-direct.sh all${NC}"
echo -e "For more detailed logs add the 'debug' parameter: ${YELLOW}.dockerwrapper/run-test-direct.sh all debug${NC}" 