#!/bin/bash

# Script to diagnose network issues between containers
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=======================================================${NC}"
echo -e "${GREEN}    Container Network Diagnostics                      ${NC}"
echo -e "${GREEN}=======================================================${NC}"

# Check if the Playwright container is running
PLAYWRIGHT_CONTAINER=$(docker ps -q --filter "name=turdparty_playwright")
if [ -z "$PLAYWRIGHT_CONTAINER" ]; then
    echo -e "${RED}Playwright container is not running.${NC}"
    echo -e "Start it with: ${YELLOW}.dockerwrapper/persistent-test-env.sh${NC}"
    exit 1
fi
echo -e "Found Playwright container: ${YELLOW}$PLAYWRIGHT_CONTAINER${NC}"

# Check if the API container is running
API_CONTAINER=$(docker ps -q --filter "name=turdparty_api")
if [ -z "$API_CONTAINER" ]; then
    echo -e "${RED}API container is not running.${NC}"
    echo -e "Start it with: ${YELLOW}docker-compose up -d api${NC}"
    exit 1
fi
echo -e "Found API container: ${YELLOW}$API_CONTAINER${NC}"

# Get the networks of both containers
echo -e "\n${GREEN}Network Information:${NC}"
PLAYWRIGHT_NETWORKS=$(docker inspect --format='{{range $net,$v := .NetworkSettings.Networks}}{{$net}} {{end}}' $PLAYWRIGHT_CONTAINER)
API_NETWORKS=$(docker inspect --format='{{range $net,$v := .NetworkSettings.Networks}}{{$net}} {{end}}' $API_CONTAINER)

echo -e "Playwright container networks: ${YELLOW}$PLAYWRIGHT_NETWORKS${NC}"
echo -e "API container networks: ${YELLOW}$API_NETWORKS${NC}"

# Check if they share at least one common network
COMMON_NETWORK=""
for NETWORK in $PLAYWRIGHT_NETWORKS; do
    if [[ $API_NETWORKS == *"$NETWORK"* ]]; then
        COMMON_NETWORK=$NETWORK
        break
    fi
done

if [ -z "$COMMON_NETWORK" ]; then
    echo -e "${RED}The containers do not share a common network.${NC}"
    echo -e "Connect them to the same network with:"
    echo -e "${YELLOW}docker network connect <network> <container>${NC}"
else
    echo -e "Containers share common network: ${GREEN}$COMMON_NETWORK${NC}"
fi

# Get IP addresses
PLAYWRIGHT_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $PLAYWRIGHT_CONTAINER)
API_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $API_CONTAINER)

echo -e "Playwright container IP: ${YELLOW}$PLAYWRIGHT_IP${NC}"
echo -e "API container IP: ${YELLOW}$API_IP${NC}"

# Run connectivity tests from Playwright container to API
echo -e "\n${GREEN}Running connectivity tests from Playwright container to API...${NC}"

echo -e "\n${YELLOW}1. Testing basic network connectivity with ping:${NC}"
docker exec $PLAYWRIGHT_CONTAINER ping -c 3 $API_IP || echo -e "${RED}Ping failed${NC}"

echo -e "\n${YELLOW}2. Testing DNS resolution:${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "echo 'Resolving api:' && getent hosts api" || echo -e "${RED}DNS resolution failed for 'api'${NC}"

echo -e "\n${YELLOW}3. Testing host file entries:${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "grep -i api /etc/hosts" || echo -e "${RED}No entry for API in /etc/hosts${NC}"

echo -e "\n${YELLOW}4. Testing network route:${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "ip route" || echo -e "${RED}Failed to get route information${NC}"

echo -e "\n${YELLOW}5. Testing HTTP connectivity to API:${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "curl -sSL -I http://$API_IP:3050/api/health 2>/dev/null || echo 'Connection failed'" || echo -e "${RED}HTTP request failed${NC}"

echo -e "\n${YELLOW}6. Testing HTTP connectivity using hostname:${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "curl -sSL -I http://api:3050/api/health 2>/dev/null || echo 'Connection failed'" || echo -e "${RED}HTTP request failed${NC}"

echo -e "\n${GREEN}Diagnostic Summary:${NC}"
echo -e "If tests showed connectivity issues, try the following:"
echo -e "1. Add the API container IP to Playwright's /etc/hosts:"
echo -e "   ${YELLOW}docker exec $PLAYWRIGHT_CONTAINER bash -c \"echo '$API_IP api' | sudo tee -a /etc/hosts\"${NC}"
echo -e "2. Ensure containers are on the same Docker network:"
echo -e "   ${YELLOW}docker network connect $COMMON_NETWORK $PLAYWRIGHT_CONTAINER${NC}"
echo -e "   ${YELLOW}docker network connect $COMMON_NETWORK $API_CONTAINER${NC}"
echo -e "3. Check the API is listening on the correct ports:"
echo -e "   ${YELLOW}docker exec $API_CONTAINER bash -c \"netstat -tulpn | grep 3050\"${NC}"
echo -e "4. Verify the API health endpoint is working:"
echo -e "   ${YELLOW}docker exec $API_CONTAINER bash -c \"curl -sSL http://localhost:3050/api/health\"${NC}"
echo -e "5. Update the API_URL in tests to use the correct endpoint" 