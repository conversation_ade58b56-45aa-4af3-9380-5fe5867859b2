#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# Function to display usage
function show_usage {
    echo -e "${YELLOW}Usage:${NC} $0 [start|stop|restart|logs|status]"
    echo ""
    echo "Commands:"
    echo "  start   - Start the development environment with live editing"
    echo "  stop    - Stop the development environment"
    echo "  restart - Restart the development environment"
    echo "  logs    - Show logs from all containers"
    echo "  status  - Show status of all containers"
    echo ""
}

# Function to start the development environment
function start_dev {
    echo -e "${GREEN}Starting development environment with live editing...${NC}"
    docker-compose -f dev-compose.yml up -d
    echo -e "${GREEN}Development environment started!${NC}"
    echo -e "${YELLOW}API:${NC} http://localhost:8000"
    echo -e "${YELLOW}Frontend:${NC} http://localhost:3000"
}

# Function to stop the development environment
function stop_dev {
    echo -e "${YELLOW}Stopping development environment...${NC}"
    docker-compose -f dev-compose.yml down
    echo -e "${GREEN}Development environment stopped.${NC}"
}

# Function to show logs
function show_logs {
    echo -e "${GREEN}Showing logs from all containers...${NC}"
    docker-compose -f dev-compose.yml logs -f
}

# Function to show status
function show_status {
    echo -e "${GREEN}Status of development containers:${NC}"
    docker-compose -f dev-compose.yml ps
}

# Main script logic
case "$1" in
    start)
        start_dev
        ;;
    stop)
        stop_dev
        ;;
    restart)
        stop_dev
        start_dev
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

exit 0 