#!/bin/bash

# A simpler script to set up Play<PERSON> in the container
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Setting up Playwright in the Docker Container      ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

if ! docker ps | grep -q turdparty_test_playwright; then
  echo -e "${RED}Error: The Playwright test container is not running.${NC}"
  echo -e "${YELLOW}Start the containers with: docker-compose -f .dockerwrapper/dev-compose.yml up -d${NC}"
  exit 1
fi

# Create a directory for the Playwright project inside the container
echo -e "${YELLOW}Creating Playwright project in container...${NC}"
docker exec -it turdparty_test_playwright bash -c "
mkdir -p /tmp/pw-project
cd /tmp/pw-project
npm init -y
# Make sure to include uuid as a dependency
npm install playwright@latest @playwright/test uuid
"

# Install the browsers
echo -e "${YELLOW}Installing Chromium browser inside container...${NC}"
docker exec -it turdparty_test_playwright bash -c "
cd /tmp/pw-project
npx playwright install chromium
"

# Copy our tests to the project directory
echo -e "${YELLOW}Setting up tests in the Playwright project...${NC}"
docker cp tests/playwright/ turdparty_test_playwright:/tmp/pw-project/tests/
docker cp test-upload.txt turdparty_test_playwright:/tmp/pw-project/

# Create a simple playwright.config.js file
echo -e "${YELLOW}Creating Playwright configuration...${NC}"
docker exec -it turdparty_test_playwright bash -c "
cat > /tmp/pw-project/playwright.config.js << EOF
// @ts-check
const { defineConfig } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests',
  timeout: 120000,
  expect: {
    timeout: 30000
  },
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: 1,
  workers: 1,
  reporter: [
    ['html'],
    ['list']
  ],
  use: {
    baseURL: 'http://react-app:3000',
    trace: 'on',
    screenshot: 'on',
  },
  projects: [
    {
      name: 'chromium',
      use: {
        browserName: 'chromium',
        headless: true,
        viewport: { width: 1280, height: 720 },
        ignoreHTTPSErrors: true,
        launchOptions: {
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox'
          ]
        }
      }
    }
  ],
  outputDir: './test-output',
});
EOF

mkdir -p /tmp/pw-project/test-output
"

# Create a run script
echo -e "${YELLOW}Creating a script to run the tests...${NC}"
docker exec -it turdparty_test_playwright bash -c "
cat > /tmp/pw-project/run-tests.sh << EOF
#!/bin/bash
cd /tmp/pw-project
npx playwright test \$@
EOF

chmod +x /tmp/pw-project/run-tests.sh
"

# Create a simple test file to verify the environment
echo -e "${YELLOW}Creating a simple test to validate the environment...${NC}"
docker exec -it turdparty_test_playwright bash -c "
cat > /tmp/pw-project/tests/env-check.spec.ts << EOF
import { test, expect } from '@playwright/test';

test('Environment check', async ({ page }) => {
  console.log('Running environment check test...');
  
  // Navigate to a page
  await page.goto('http://react-app:3000');
  
  // Take a screenshot
  await page.screenshot({ path: './test-output/environment-check.png' });
  
  // Simple assertion - just checking if we can load the page
  const title = await page.title();
  console.log('Page title:', title);
  
  // Basic verification that we could load a page
  expect(page).toBeDefined();
  
  console.log('Environment check completed successfully');
});
EOF
"

# Run the verification test
echo -e "${YELLOW}Running a simple test to validate the environment...${NC}"
docker exec -it turdparty_test_playwright bash -c "
cd /tmp/pw-project
./run-tests.sh tests/env-check.spec.ts
"

# Check if the test was successful
if [ $? -eq 0 ]; then
  echo -e "${GREEN}Setup complete! Environment is working correctly.${NC}"
  
  # Create a simple test to check file upload functionality
  echo -e "${YELLOW}Creating a simple file upload test...${NC}"
  docker exec -it turdparty_test_playwright bash -c "
  cat > /tmp/pw-project/tests/simple-upload.spec.ts << EOF
  import { test, expect } from '@playwright/test';
  import * as fs from 'fs';
  import * as path from 'path';
  
  test('Simple file upload test', async ({ page }) => {
    console.log('Running simple file upload test...');
    
    // Create a test file
    const testFilePath = path.join('/tmp/pw-project', 'test-upload.txt');
    const testContent = 'This is a test file for upload';
    fs.writeFileSync(testFilePath, testContent);
    
    // Navigate to the upload page
    await page.goto('http://react-app:3000/upload');
    await page.waitForTimeout(1000);
    await page.screenshot({ path: './test-output/upload-page.png' });
    
    // Fill the description field
    await page.getByPlaceholder(/description/i).fill('Test upload from Playwright');
    
    // Upload the file
    const fileInput = page.locator('input[type=\"file\"]');
    await fileInput.setInputFiles(testFilePath);
    
    // Screenshot after file selection
    await page.screenshot({ path: './test-output/file-selected.png' });
    
    // Log that the test ran successfully
    console.log('Simple file upload test completed');
  });
  EOF
  "
  
  # Run the simple file upload test
  echo -e "${YELLOW}Running simple file upload test...${NC}"
  docker exec -it turdparty_test_playwright bash -c "
  cd /tmp/pw-project
  ./run-tests.sh tests/simple-upload.spec.ts
  "
  
  echo -e "${GREEN}Tests are now ready to use!${NC}"
  echo -e "${YELLOW}You can run tests with:${NC}"
  echo -e "  docker exec -it turdparty_test_playwright /tmp/pw-project/run-tests.sh tests/file-upload-to-vm.spec.ts"
  echo -e "${YELLOW}Or view all available tests:${NC}"
  echo -e "  docker exec -it turdparty_test_playwright ls -la /tmp/pw-project/tests/"
else
  echo -e "${RED}Setup failed - environment test was not successful.${NC}"
  exit 1
fi 