#!/bin/bash

# Script to run Playwright tests in a self-contained container
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Running Tests in Self-Contained Container          ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Create necessary directories
mkdir -p test-results test_screenshots playwright-report

# Check if a test was specified
if [ -z "$1" ]; then
  echo -e "${RED}Error: No test specified${NC}"
  echo -e "${YELLOW}Usage: $0 <test-file-or-directory>${NC}"
  echo -e "${YELLOW}Examples:${NC}"
  echo -e "${YELLOW}  $0 simple-verification.spec.js${NC}"
  echo -e "${YELLOW}  $0 vm-management/vagrant-integration.spec.js${NC}"
  echo -e "${YELLOW}  $0 all${NC}"
  exit 1
fi

# Determine which test to run
if [ "$1" == "vm" ]; then
  TEST_PATH="tests/playwright/vagrant-integration.spec.js"
elif [ "$1" == "file" ]; then
  TEST_PATH="tests/playwright/file-to-vm-integration.spec.js"
elif [ "$1" == "verify" ]; then
  TEST_PATH="tests/playwright/simple-verification.spec.js"
elif [ "$1" == "all" ]; then
  TEST_PATH="tests/playwright"
else
  TEST_PATH="tests/playwright/$1"
fi

# Get API URL if turdparty_api container is running
API_URL="http://localhost:3050"
if docker ps | grep -q "turdparty_api"; then
  API_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' turdparty_api)
  API_URL="http://${API_IP}:3050"
  echo -e "${YELLOW}Found API container at ${API_URL}${NC}"
fi

# Determine reporter
REPORTER="--reporter=list"
if [ "$2" == "html" ]; then
  REPORTER="--reporter=html"
fi

# Run the test in a fresh container
echo -e "${YELLOW}Running test: ${TEST_PATH}${NC}"
docker run --rm -it \
  -v "$(pwd)/tests/playwright:/app/tests/playwright" \
  -v "$(pwd)/test-results:/app/test-results" \
  -v "$(pwd)/test_screenshots:/app/test_screenshots" \
  -v "$(pwd)/playwright-report:/app/playwright-report" \
  -e API_URL="${API_URL}" \
  -e NODE_ENV=test \
  -e HEADLESS=true \
  mcr.microsoft.com/playwright:v1.41.0-focal \
  /bin/bash -c "cd /app && \
  npm init -y && \
  npm install @playwright/test playwright playwright-core && \
  echo 'module.exports = { use: { headless: true, viewport: { width: 1280, height: 720 }, launchOptions: { args: [\"--no-sandbox\", \"--disable-setuid-sandbox\"] } }, testDir: \"./tests/playwright\", reporter: \"list\" };' > /app/playwright.config.js && \
  mkdir -p /app/tests/playwright/fixtures/test-files && \
  npx playwright test ${TEST_PATH} ${REPORTER}"

TEST_RESULT=$?

if [ $TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tests completed successfully!${NC}"
  echo -e "${YELLOW}Test reports available at:${NC}"
  echo -e "  - test_screenshots/"
  echo -e "  - test-results/"
  echo -e "  - playwright-report/index.html"
else
  echo -e "${RED}Tests failed with code ${TEST_RESULT}. Check the output above for details.${NC}"
fi

exit $TEST_RESULT 