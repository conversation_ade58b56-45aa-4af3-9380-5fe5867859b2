FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    openssh-client \
    && rm -rf /var/lib/apt/lists/*

# Install Docker CLI
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" > /etc/apt/sources.list.d/docker.list \
    && apt-get update \
    && apt-get install -y docker-ce-cli \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user and set up permissions
RUN useradd -m -u 1000 app && \
    mkdir -p /home/<USER>
    chown -R app:app /home/<USER>
    groupadd -f docker && \
    usermod -aG docker app

# Set up work directory and ownership
WORKDIR /app
RUN chown app:app /app

# Copy dashboard files with correct ownership
COPY --chown=app:app ../dashboard/dashboard.py /app/
COPY --chown=app:app ../dashboard/pyproject.toml /app/
COPY --chown=app:app ../dashboard/poetry.lock /app/
COPY --chown=app:app dev-compose.yml /app/docker-compose.yml

# Create default config
RUN mkdir -p /home/<USER>'{"refresh_interval": 3, "log_lines": 50, "docker_compose_file": "/app/docker-compose.yml", "project_name": "turdparty_test"}' > /home/<USER>/.turdparty-dashboard.json

# Install Python dependencies as root
RUN pip install --no-cache-dir poetry==1.7.1 click==8.1.8 docker==6.1.3 urwid==2.6.16 pyyaml==6.0.2

# Switch to non-root user
USER app

# Install poetry but don't create a virtual environment
ENV PATH="/home/<USER>/.local/bin:$PATH"

# Default command
CMD ["python", "/app/dashboard.py", "start", "--dev"] 