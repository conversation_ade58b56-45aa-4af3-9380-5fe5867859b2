# Port Configuration System

This directory contains a port configuration system that allows you to centralize and manage all port mappings for Docker containers in one place.

```mermaid
graph TD
    subgraph "Port Configuration System"
        A[port_mappings.json] -->|read by| B[update_ports.py]
        B -->|updates| C[Docker Compose Files]
        A -->|read by| D[restart-containers.sh]
        D -->|displays URLs with| E[Correct Ports]
        F[update_ports.sh] -->|runs| B
    end
    
    subgraph "Docker Environment"
        C -->|configures| G[Docker Containers]
        G -->|expose ports| H[Host Machine]
    end
    
    classDef config fill:#d5e8d4,stroke:#82b366,stroke-width:1px;
    classDef script fill:#dae8fc,stroke:#6c8ebf,stroke-width:1px;
    classDef container fill:#ffe6cc,stroke:#d79b00,stroke-width:1px;
    
    class A config;
    class B,D,F script;
    class C,G,H container;
```

## Overview

The port configuration system consists of:

1. **port_mappings.json**: A JSON file containing all port mappings for Docker containers
2. **update_ports.py**: A Python script that updates Docker Compose files with port mappings from the JSON file
3. **update_ports.sh**: A shell script that makes the Python script executable and provides a convenient way to run it
4. **restart-containers.sh**: A shell script that starts Docker containers with the updated port mappings

## How It Works

1. Port mappings are defined in `port_mappings.json`
2. The `update_ports.py` script reads the port mappings from the JSON file and updates the Docker Compose files
3. The `restart-containers.sh` script reads the port mappings from the JSON file and uses them to display the correct URLs
4. All Python execution is done through `nix-shell` using the project's `shell.nix` file to ensure consistent dependencies

## Usage

### Updating Port Mappings

If you need to change a port mapping (e.g., because a port is already in use), follow these steps:

1. Edit the `port_mappings.json` file to update the port mapping
2. Run the `update_ports.sh` script to update the Docker Compose files:

```bash
cd .dockerwrapper
./update_ports.sh
```

3. Restart the Docker containers with the updated port mappings:

```bash
cd .dockerwrapper
./restart-containers.sh
```

Alternatively, you can use the `--update-ports` flag with `restart-containers.sh` to update the port mappings and restart the containers in one step:

```bash
cd .dockerwrapper
./restart-containers.sh --update-ports
```

### Adding New Port Mappings

If you need to add a new port mapping for a new service, follow these steps:

1. Edit the `port_mappings.json` file to add the new port mapping
2. Run the `update_ports.sh` script to update the Docker Compose files
3. Update the Docker Compose files manually to add the new service with the port mapping
4. Restart the Docker containers with the updated port mappings

## Port Mappings

The `port_mappings.json` file contains the following port mappings:

| Service | Port | Description |
|---------|------|-------------|
| api | 3050 | FastAPI application |
| frontend | 3100 | React application |
| dashboard | 3150 | Monitoring dashboard |
| postgres | 3200 | PostgreSQL database |
| minio_api | 9000 | MinIO API |
| minio_console | 9001 | MinIO Console |
| minio_ssh | 2223 | MinIO SSH |
| playwright | 3250 | Playwright testing service |
| test_api | 3050 | Test FastAPI application |
| test_frontend | 3100 | Test React application |
| test_dashboard | 3150 | Test monitoring dashboard |
| test_postgres | 3200 | Test PostgreSQL database |
| test_minio_api | 3300 | Test MinIO API |
| test_minio_console | 3301 | Test MinIO Console |
| test_playwright | 3250 | Test Playwright testing service |

## Troubleshooting

If you encounter issues with port conflicts, follow these steps:

1. Check which ports are in use:

```bash
sudo lsof -i -P -n | grep LISTEN
```

2. Update the `port_mappings.json` file to use different ports for the conflicting services
3. Run the `update_ports.sh` script to update the Docker Compose files
4. Restart the Docker containers with the updated port mappings
