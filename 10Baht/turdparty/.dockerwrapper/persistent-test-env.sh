#!/bin/bash

# Script to set up a persistent test environment with volume mounts
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Setting Up Persistent Playwright Test Environment   ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Navigate to the root directory
cd "$(dirname "$0")/.."

# Create necessary directories
mkdir -p test_screenshots test-results playwright-report

# Check if the container is already running
CONTAINER_ID=$(docker ps -q --filter "name=turdparty_playwright")

if [ -n "$CONTAINER_ID" ]; then
    echo "Playwright test container is already running"
    echo "You can run tests with: .dockerwrapper/run-test-direct.sh"
    exit 0
fi

# Only handle the Playwright container, do not affect other containers
echo "Removing any stopped Playwright container..."
docker rm turdparty_playwright 2>/dev/null || true

echo "Starting Playwright test container only..."
# Use docker compose to only start the Playwright container
docker-compose -f .dockerwrapper/docker-compose.playwright-fixed.yml up -d playwright

# Install dependencies in the container
echo -e "${YELLOW}Installing dependencies...${NC}"
docker exec -it playwright-test bash -c "apt-get update && apt-get install -y curl jq sudo dnsutils iputils-ping net-tools vim"
docker exec -it playwright-test bash -c "npm install --no-save @playwright/test playwright playwright-core"

# Set up simple playwright config if it doesn't exist
docker exec -it playwright-test bash -c "mkdir -p /app/tests/playwright/fixtures/test-files"
docker exec -it playwright-test bash -c "[ -f /app/playwright.config.js ] || echo 'module.exports = { use: { headless: true, viewport: { width: 1280, height: 720 }, launchOptions: { args: [\"--no-sandbox\", \"--disable-setuid-sandbox\"] } }, testDir: \"./tests/playwright\", reporter: \"list\" };' > /app/playwright.config.js"

# Create a host entry for turdparty_api
if docker ps | grep -q "turdparty_api"; then
  CONTAINER_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' turdparty_api)
  echo -e "${YELLOW}Adding host entry for turdparty_api: ${CONTAINER_IP}${NC}"
  docker exec -it playwright-test bash -c "echo '$CONTAINER_IP api' | sudo tee -a /etc/hosts"
fi

echo -e "${GREEN}Persistent test environment is ready!${NC}"
echo -e "${YELLOW}You can run tests with: .dockerwrapper/run-test-direct.sh${NC}"
echo -e "${YELLOW}To stop the environment: docker stop playwright-test${NC}"
echo -e "${YELLOW}To start it again later: docker start playwright-test${NC}" 