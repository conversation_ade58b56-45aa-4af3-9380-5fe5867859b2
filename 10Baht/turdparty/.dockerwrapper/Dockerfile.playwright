FROM mcr.microsoft.com/playwright:v1.41.0-focal

# Install additional dependencies
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    sudo \
    dnsutils \
    iputils-ping \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Set up a non-root user with sudo permissions
RUN useradd -m -s /bin/bash -G sudo turduser \
    && echo "turduser ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/turduser

# Create directories for test outputs with proper permissions
RUN mkdir -p /app/test-results /app/test_screenshots /app/playwright-report \
    && chown -R turduser:turduser /app

# Set the working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY turdparty-app/package.json turdparty-app/package-lock.json /app/
COPY turdparty-app/playwright.config.ts /app/playwright.config.js

# Fix permissions before npm install
RUN chown -R turduser:turduser /app

# Switch to non-root user for npm operations
USER turduser

# Install dependencies with npm
RUN npm install --legacy-peer-deps

# Install Playwright browsers
RUN npx playwright install chromium

# Copy the rest of the application
# Use COPY with chown to ensure correct permissions
COPY --chown=turduser:turduser turdparty-app /app/

# Create directories for tests
RUN mkdir -p /app/scripts/playwright-tests/utils

# Set environment variables
ENV NODE_ENV=test
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0

# Ensure correct permissions for the test directories
RUN sudo chown -R turduser:turduser /app/test-results /app/test_screenshots /app/playwright-report

# Create a healthcheck script
RUN echo '#!/bin/bash\n\
curl -f http://localhost:9323 || exit 1' > /app/healthcheck.sh \
    && chmod +x /app/healthcheck.sh

# Add script to fix permissions when container starts
RUN echo '#!/bin/bash\n\
if [ "$(id -u)" = "0" ]; then\n\
  echo "Running as root, fixing permissions..."\n\
  mkdir -p /app/test-results /app/test_screenshots /app/playwright-report\n\
  chown -R turduser:turduser /app /ms-playwright\n\
  exec sudo -u turduser "$@"\n\
else\n\
  exec "$@"\n\
fi' > /app/docker-entrypoint.sh \
    && chmod +x /app/docker-entrypoint.sh

# Set the entry point
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["npx", "playwright", "test"] 