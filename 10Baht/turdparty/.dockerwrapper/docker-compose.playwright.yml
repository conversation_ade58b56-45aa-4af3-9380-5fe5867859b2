version: '3.8'

services:
  playwright:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.playwright
    container_name: turdparty_playwright
    ports:
      - "3250:9323"
    volumes:
      - ../scripts:/app/scripts:ro
      - ../playwright-report:/app/playwright-report:rw
      - ../test-results:/app/test-results:rw
      - ../test_screenshots:/app/test_screenshots:rw
      - ../node_modules:/app/node_modules:rw
    environment:
      - NODE_ENV=test
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      - CI=true
      - FRONTEND_URL=http://ui:3100
      - API_URL=http://api:3050
      - PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0
    entrypoint: ["/bin/bash", "-c"]
    command:
      - |
        # Wait for services to be up
        echo "Waiting for services to be available..."
        # Use healthcheck API endpoint
        for i in {1..30}; do
          if curl -s http://api:3050/health >/dev/null 2>&1; then
            echo "API is available!"
            break
          fi
          echo "Waiting for API to be available... ($i/30)"
          sleep 2
        done
        
        # Run network configuration setup
        if [ -f "/app/scripts/setup-network-config.sh" ]; then
          /app/scripts/setup-network-config.sh
        else
          echo "Network configuration script not found, using default configuration"
        fi
        
        # Start HTTP server
        (cd /app && python3 -m http.server 9323) &
        
        # Run the tests
        echo "Running Playwright tests..."
        cd /app && npx playwright test
    depends_on:
      - ui
      - api
      - minio
    networks:
      - turdparty_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9323"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 40s

  ui:
    image: node:18-alpine
    container_name: turdparty_ui
    ports:
      - "3100:3100"
    volumes:
      - ../turdparty-app:/app:rw
      - frontend_node_modules:/app/node_modules
    working_dir: /app
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://api:3050
      - PORT=3100
      - WDS_SOCKET_PORT=3100
    command: sh -c "npm install --legacy-peer-deps && npm run start"
    networks:
      - turdparty_network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:3100"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 40s

  api:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
    image: turdparty-api:latest
    container_name: turdparty_api
    ports:
      - "3050:8000"
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 512M
    extra_hosts:
      - "postgres:${POSTGRES_HOST_IP:-**********}"
      - "db:${POSTGRES_HOST_IP:-**********}"
      - "minio:${MINIO_HOST_IP:-**********}"
    environment:
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=development
      - DATABASE_URL=**************************************/turdparty
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@db:5432/turdparty
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_DIRECT=true
      - TEST_MODE=true
      - DEBUG=true
      - PORT=8000
      - PYTHONPATH=/app
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
    volumes:
      - ..:/app:rw
      - ../logs:/app/logs:rw
      - ../uploads:/app/uploads:rw
      - /app/.venv
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    depends_on:
      - db
      - minio
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  db:
    image: postgres:14-alpine
    container_name: turdparty_db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=turdparty
    volumes:
      - db_data:/var/lib/postgresql/data
    networks:
      - turdparty_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  minio:
    image: minio/minio:latest
    container_name: turdparty_minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - turdparty_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 10s

networks:
  turdparty_network:
    driver: bridge

volumes:
  frontend_node_modules:
  db_data:
  minio_data: 