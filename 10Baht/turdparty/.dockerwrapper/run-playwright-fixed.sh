#!/bin/bash

# Script to run Playwright tests with fixed Docker environment
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=======================================================${NC}"
echo -e "${GREEN}    Running Playwright Tests in Fixed Docker Environment${NC}"
echo -e "${GREEN}=======================================================${NC}"

# Navigate to the root directory
cd "$(dirname "$0")/.."

# Create necessary directories
mkdir -p test_screenshots test-results playwright-report

# Only stop the Playwright container, not all containers
echo "Stopping existing playwright container only..."
docker stop turdparty_playwright 2>/dev/null || true
docker rm turdparty_playwright 2>/dev/null || true

echo "Starting Playwright test environment..."
# Use docker compose to only start the Playwright container
docker-compose -f .dockerwrapper/docker-compose.playwright-fixed.yml up -d playwright

# Give services time to initialize
echo -e "${YELLOW}Waiting for services to be ready...${NC}"
sleep 15

# Check if the services are running properly
echo -e "${YELLOW}Checking container health status...${NC}"
if ! docker ps | grep -q "turdparty_api"; then
  echo -e "${RED}Error: API container is not running${NC}"
  docker-compose -f .dockerwrapper/docker-compose.playwright-fixed.yml logs api
  exit 1
fi

if ! docker ps | grep -q "turdparty_ui"; then
  echo -e "${RED}Error: Frontend container is not running${NC}"
  docker-compose -f .dockerwrapper/docker-compose.playwright-fixed.yml logs ui
  exit 1
fi

if ! docker ps | grep -q "turdparty_playwright"; then
  echo -e "${RED}Error: Playwright container is not running${NC}"
  docker-compose -f .dockerwrapper/docker-compose.playwright-fixed.yml logs playwright
  exit 1
fi

# Determine which test to run
TEST_PATH=""
if [ "$1" == "vm" ]; then
  echo -e "${YELLOW}Running VM integration test${NC}"
  TEST_PATH="tests/playwright/vagrant-integration.spec.js"
elif [ "$1" == "file" ]; then
  echo -e "${YELLOW}Running file upload test${NC}"
  TEST_PATH="tests/playwright/file-to-vm-integration.spec.js"
elif [ "$1" == "all" ]; then
  echo -e "${YELLOW}Running all tests${NC}"
  TEST_PATH="tests/playwright"
elif [ -n "$1" ]; then
  echo -e "${YELLOW}Running specific test: $1${NC}"
  TEST_PATH="$1"
else
  echo -e "${YELLOW}Running default test${NC}"
  TEST_PATH="tests/playwright/vagrant-integration.spec.js"
fi

# Run the test in the container
echo -e "${YELLOW}Running Playwright test: ${TEST_PATH}${NC}"
docker exec -it turdparty_playwright bash -c "cd /app && npx playwright test ${TEST_PATH} --reporter=list"
TEST_RESULT=$?

# Copy reports and screenshots
echo -e "${YELLOW}Copying test reports and screenshots...${NC}"
docker cp turdparty_playwright:/app/playwright-report/. ./playwright-report/ || true
docker cp turdparty_playwright:/app/test-results/. ./test-results/ || true
docker cp turdparty_playwright:/app/test_screenshots/. ./test_screenshots/ || true

# Check test result
if [ $TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tests completed successfully!${NC}"
  echo -e "${YELLOW}Test reports available at:${NC}"
  echo -e "  - test_screenshots/"
  echo -e "  - test-results/"
  echo -e "  - playwright-report/index.html"
else
  echo -e "${RED}Tests failed. Check the output above for details.${NC}"
  echo -e "${YELLOW}Logs available with: docker-compose -f .dockerwrapper/docker-compose.playwright-fixed.yml logs${NC}"
fi

# Ask if we should clean up the containers
read -p "Do you want to stop the test containers? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  echo -e "${YELLOW}Stopping test containers...${NC}"
  docker-compose -f .dockerwrapper/docker-compose.playwright-fixed.yml down
else
  echo -e "${YELLOW}Leaving test containers running...${NC}"
  echo -e "${YELLOW}You can stop them with: docker-compose -f .dockerwrapper/docker-compose.playwright-fixed.yml down${NC}"
fi

exit $TEST_RESULT 