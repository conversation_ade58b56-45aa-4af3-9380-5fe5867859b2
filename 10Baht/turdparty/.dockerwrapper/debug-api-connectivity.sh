#!/bin/bash

# Advanced debugging script for API connectivity issues
# Don't exit on errors
set +e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=======================================================${NC}"
echo -e "${BLUE}    Advanced API Connectivity Diagnostics             ${NC}"
echo -e "${BLUE}=======================================================${NC}"

# Check if containers are running
PLAYWRIGHT_CONTAINER=$(docker ps -q --filter "name=turdparty_playwright")
if [ -z "$PLAYWRIGHT_CONTAINER" ]; then
    echo -e "${RED}Playwright container is not running.${NC}"
    exit 1
fi

API_CONTAINER=$(docker ps -q --filter "name=turdparty_api")
if [ -z "$API_CONTAINER" ]; then
    echo -e "${RED}API container is not running.${NC}"
    exit 1
fi

echo -e "${GREEN}Found Playwright container:${NC} $PLAYWRIGHT_CONTAINER"
echo -e "${GREEN}Found API container:${NC} $API_CONTAINER"

# Get container network information
echo -e "\n${BLUE}Container Network Information:${NC}"
echo -e "${GREEN}Playwright Container:${NC}"
docker inspect --format='{{range $net,$v := .NetworkSettings.Networks}}{{printf "Network: %s\nIP: %s\n" $net $v.IPAddress}}{{end}}' $PLAYWRIGHT_CONTAINER

echo -e "\n${GREEN}API Container:${NC}"
docker inspect --format='{{range $net,$v := .NetworkSettings.Networks}}{{printf "Network: %s\nIP: %s\n" $net $v.IPAddress}}{{end}}' $API_CONTAINER

# Get shared networks between containers
PLAYWRIGHT_NETWORKS=$(docker inspect --format='{{range $net,$v := .NetworkSettings.Networks}}{{$net}} {{end}}' $PLAYWRIGHT_CONTAINER)
API_NETWORKS=$(docker inspect --format='{{range $net,$v := .NetworkSettings.Networks}}{{$net}} {{end}}' $API_CONTAINER)

# Find common network
COMMON_NETWORK=""
for NETWORK in $PLAYWRIGHT_NETWORKS; do
    if [[ $API_NETWORKS == *"$NETWORK"* ]]; then
        COMMON_NETWORK=$NETWORK
        break
    fi
done

if [ -z "$COMMON_NETWORK" ]; then
    echo -e "${RED}No common network found between containers!${NC}"
else
    echo -e "\n${GREEN}Common network:${NC} $COMMON_NETWORK"
fi

# Get API container's exposed ports
echo -e "\n${BLUE}API Container Port Configuration:${NC}"
docker inspect --format='{{range $p, $conf := .NetworkSettings.Ports}}{{$p}} -> {{range $conf}}{{.HostIp}}:{{.HostPort}}{{end}}{{println}}{{end}}' $API_CONTAINER

# Check if API process is running and listening
echo -e "\n${BLUE}API Process Status:${NC}"
docker exec $API_CONTAINER bash -c "ps aux | grep -i python || echo 'ps command not available'"

echo -e "\n${BLUE}API Listening Ports:${NC}"
docker exec $API_CONTAINER bash -c "netstat -tulpn 2>/dev/null || ss -tulpn 2>/dev/null || echo 'Network tools not available'"

# Try to connect to API using curl from within API container
echo -e "\n${BLUE}API Self-Check:${NC}"
docker exec $API_CONTAINER bash -c "curl -s -m 5 -I http://localhost:3050/api/health 2>/dev/null || curl -s -m 5 -I http://localhost:8000/api/health 2>/dev/null || echo 'Self-connection failed'"

# Try different methods to connect from Playwright container
API_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{if eq .NetworkID "'$(docker network inspect dockerwrapper_turdparty_network -f '{{.Id}}')'"}}.IPAddress{{end}}{{end}}' $API_CONTAINER)

echo -e "\n${BLUE}Connection Tests from Playwright container:${NC}"

# 1. Try with hostname
echo -e "\n${GREEN}1. Using hostname 'api':${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "curl -v -m 5 http://api:3050/api/health 2>&1 || echo 'Connection failed'"

# 2. Try with IP address
echo -e "\n${GREEN}2. Using IP address $API_IP:${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "curl -v -m 5 http://$API_IP:3050/api/health 2>&1 || echo 'Connection failed'"

# 3. Try with different ports
echo -e "\n${GREEN}3. Testing other potential ports:${NC}"
for PORT in 8000 5000 8080 3000; do
    echo -e "${YELLOW}Testing port $PORT:${NC}"
    docker exec $PLAYWRIGHT_CONTAINER bash -c "curl -s -m 2 -I http://$API_IP:$PORT/api/health 2>/dev/null || echo 'Failed'"
done

# 4. Check Docker DNS resolution
echo -e "\n${GREEN}4. Docker DNS resolution:${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "cat /etc/resolv.conf || echo 'Cannot read resolv.conf'"

# 5. Install and test TCP connectivity on port 3050
echo -e "\n${GREEN}5. Testing raw TCP connection:${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "apt-get update -qq && apt-get install -qq -y netcat-openbsd > /dev/null 2>&1 || true"
docker exec $PLAYWRIGHT_CONTAINER bash -c "nc -zv $API_IP 3050 2>&1 || echo 'TCP connection failed'"

# 6. Check API container environment variables
echo -e "\n${BLUE}API Container Environment Variables:${NC}"
docker exec $API_CONTAINER bash -c "env | grep -E 'PORT|HOST|LISTEN|BIND|API' || echo 'No matching environment variables'"

# 7. Check API logs
echo -e "\n${BLUE}Recent API Container Logs:${NC}"
docker logs --tail 20 $API_CONTAINER 2>&1 || echo "Failed to get logs"

# 8. Try alternative local healthcheck
echo -e "\n${BLUE}Alternative API Health Checks:${NC}"
echo -e "\n${GREEN}8.1 API container localhost:8000 check:${NC}"
docker exec $API_CONTAINER bash -c "wget -q --spider http://localhost:8000/api/health || echo 'Health check failed on port 8000'"

echo -e "\n${GREEN}8.2 API container localhost:3050 check:${NC}"
docker exec $API_CONTAINER bash -c "wget -q --spider http://localhost:3050/api/health || echo 'Health check failed on port 3050'"

# 9. Find what process is using port 3050 in API container
echo -e "\n${BLUE}9. Check what process is using port 3050 in API container:${NC}"
docker exec $API_CONTAINER bash -c "lsof -i :3050 2>/dev/null || fuser 3050/tcp 2>/dev/null || echo 'Cannot determine process using port 3050'"

# 10. Check if API is running with uvicorn
echo -e "\n${BLUE}10. Check for uvicorn process:${NC}"
docker exec $API_CONTAINER bash -c "ps aux | grep -i uvicorn || echo 'No uvicorn process found'"

echo -e "\n${BLUE}Diagnostic Summary:${NC}"
echo -e "1. If API is not listening on port 3050, check the application configuration."
echo -e "2. If API is listening but not accessible, check for firewalls or network isolation."
echo -e "3. If hostname 'api' doesn't resolve, add it to the hosts file or use IP directly."
echo -e "4. Consider updating the API URL in test configuration to match what's actually working."

echo -e "\n${BLUE}Next steps:${NC}"
echo -e "1. Check the port the API is actually listening on (from netstat output)"
echo -e "2. Update test configuration to use the correct port/hostname"
echo -e "3. If needed, modify the API container to listen on the expected port" 