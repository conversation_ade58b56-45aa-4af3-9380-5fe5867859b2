version: '3.8'

services:
  playwright:
    image: mcr.microsoft.com/playwright:v1.36.0-focal
    container_name: turdparty_playwright_test
    ports:
      - "3250:9323"
    volumes:
      - ../tests:/app/tests:ro
      - ../playwright.config.js:/app/playwright.config.js:ro
      - ../tests/playwright:/app/tests/playwright:ro
      - ../playwright-report:/app/playwright-report:rw
      - ../test-results:/app/test-results:rw
      - ../test_screenshots:/app/test_screenshots:rw
      - ../node_modules:/app/node_modules:rw
    environment:
      - NODE_ENV=test
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      - CI=true
      - FRONTEND_URL=http://ui:3100
      - API_URL=http://api-test:8000
      - PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0
    working_dir: /app
    command: ["/bin/bash", "-c", "echo 'Playwright container ready for testing' && tail -f /dev/null"]
    networks:
      - test-network

  api-test:
    build:
      context: ..
      dockerfile: Dockerfile.simple
    container_name: api-test
    ports:
      - "3055:8000"
    volumes:
      - ..:/app:ro
      - /app/.venv
      - /app/__pycache__
      - ../test_logs:/app/test_logs:rw
    environment:
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=test
      - "DATABASE_URL=sqlite:///:memory:"
      - "DATABASE_URL_ASYNC=sqlite+aiosqlite:///:memory:"
      - TEST_MODE=true
      - DEBUG=true
      - PORT=8000
      - PYTHONPATH=/app
      - API_PREFIX=/api/v1
    command: ["python3", "api_test_server.py"]
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  python-test:
    build:
      context: ..
      dockerfile: Dockerfile.test
    container_name: python-test
    volumes:
      - ..:/app:ro
      - /app/.venv
      - /app/__pycache__
      - ../test_logs:/app/test_logs:rw
    environment:
      - PYTHONUNBUFFERED=1
      - TEST_MODE=true
      - "DATABASE_URL=sqlite:///:memory:"
      - "DATABASE_URL_ASYNC=sqlite+aiosqlite:///:memory:"
      - PYTHONPATH=/app
    command: ["/bin/bash", "-c", "echo 'Python test container ready' && tail -f /dev/null"]
    networks:
      - test-network

networks:
  test-network:
    driver: bridge 