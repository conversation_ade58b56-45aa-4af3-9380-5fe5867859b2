# IMPORTANT: DO NOT EDIT WITHOUT PERMISSION

The files in this directory are locked at version 1.0.0 and are essential for the stable operation of the application.

## Why are these files protected?

These Docker configuration files have been carefully tested and configured to work properly with the application. 
Modifying them without proper testing can cause deployment failures and system instability.

## How to modify these files

If you need to make changes to these files:

1. Request explicit permission from the repository owner
2. Make small, incremental changes
3. Test thoroughly before committing
4. Create a new version tag when changes are stable

## Cursor Rules

These files are protected via Cursor Rules in the `.cursor.rules` file, which prevents the assistant from modifying these files without explicit permission. 