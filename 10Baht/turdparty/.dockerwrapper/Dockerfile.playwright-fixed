FROM mcr.microsoft.com/playwright:v1.51.1-focal

# Install additional dependencies
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    sudo \
    dnsutils \
    iputils-ping \
    net-tools \
    netcat \
    vim \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user for running tests
RUN useradd -m -s /bin/bash -G sudo turduser \
    && echo "turduser ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/turduser

# Create app directories
RUN mkdir -p /app/test-results /app/test_screenshots /app/playwright-report \
    && mkdir -p /app/tests/playwright/utils /app/tests/playwright/fixtures/test-files \
    && chown -R turduser:turduser /app

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* /app/
RUN chown turduser:turduser /app/package.json /app/package-lock.json*

# Switch to non-root user
USER turduser

# Install Node.js dependencies
RUN npm install --no-save @playwright/test playwright playwright-core

# Set up environment variables
ENV NODE_ENV=test
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
ENV HEADLESS=true
ENV CI=true
ENV DEBUG=pw:api

# Copy test files
COPY --chown=turduser:turduser tests/playwright /app/tests/playwright
COPY --chown=turduser:turduser playwright.config.js /app/

# Create a simple configuration file if not exists
RUN [ -f "/app/playwright.config.js" ] || echo 'module.exports = { use: { headless: true, viewport: { width: 1280, height: 720 }, launchOptions: { args: ["--no-sandbox", "--disable-setuid-sandbox"] } }, testDir: "./tests/playwright", reporter: "list" };' > /app/playwright.config.js

# Add DNS entries for the service containers
RUN echo "127.0.0.1 api" | sudo tee -a /etc/hosts \
    && echo "127.0.0.1 frontend" | sudo tee -a /etc/hosts \
    && echo "127.0.0.1 minio" | sudo tee -a /etc/hosts

# Health check
HEALTHCHECK --interval=5s --timeout=5s --start-period=5s --retries=3 \
    CMD ps aux | grep node || exit 1

# Keep container running
CMD ["bash", "-c", "while true; do sleep 60; done"] 