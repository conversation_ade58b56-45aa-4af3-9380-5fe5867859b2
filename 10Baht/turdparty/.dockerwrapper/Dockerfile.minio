FROM ubuntu:22.04

# Install required packages
RUN apt-get update && \
    apt-get install -y \
    openssh-server \
    openssh-client \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Set up SSH
RUN mkdir -p /run/sshd && \
    ssh-keygen -A && \
    echo "PermitRootLogin yes" >> /etc/ssh/sshd_config && \
    echo "PasswordAuthentication no" >> /etc/ssh/sshd_config && \
    mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh

# Copy SSH public key
COPY .ssh/minio_ssh_key.pub /root/.ssh/authorized_keys
RUN chmod 600 /root/.ssh/authorized_keys

# Install MinIO client
RUN wget https://dl.min.io/client/mc/release/linux-amd64/mc -O /usr/local/bin/mc && \
    chmod +x /usr/local/bin/mc

# Copy initialization scripts
COPY .dockerwrapper/scripts/start-services.sh /usr/local/bin/

# Make scripts executable
RUN chmod +x /usr/local/bin/start-services.sh

# Expose SSH port
EXPOSE 22

# Set the entrypoint to start services
ENTRYPOINT ["/usr/local/bin/start-services.sh"] 