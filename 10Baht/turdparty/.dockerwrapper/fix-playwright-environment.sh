#!/bin/bash

# Script to fix Playwright environment in Docker container
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Setting up Playwright Testing Environment          ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

if ! docker ps | grep -q turdparty_test_playwright; then
  echo -e "${RED}Error: The Playwright test container is not running.${NC}"
  echo -e "${YELLOW}Start the containers with: docker-compose -f .dockerwrapper/dev-compose.yml up -d${NC}"
  exit 1
fi

echo -e "${YELLOW}Installing Playwright globally in the container using npm...${NC}"

# Execute installation as root to avoid permission issues
docker exec -it turdparty_test_playwright bash -c "
set -e
echo 'Installing Playwright globally with npm...'
sudo npm install -g playwright@1.41.2 @playwright/test
sudo npm list -g playwright

echo 'Installing Chromium browser...'
sudo PLAYWRIGHT_BROWSERS_PATH=/usr/local/lib/playwright npx playwright install --with-deps chromium

echo 'Setting up correct permissions and environment...'
# Create test directories with proper permissions
sudo mkdir -p /app/test_screenshots /app/test-results
sudo chmod -R 777 /app/test_screenshots /app/test-results

# Set environment variables in global profile
sudo bash -c 'echo \"export PLAYWRIGHT_BROWSERS_PATH=/usr/local/lib/playwright\" >> /etc/profile.d/playwright.sh'
sudo bash -c 'echo \"export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1\" >> /etc/profile.d/playwright.sh'
sudo chmod +x /etc/profile.d/playwright.sh
"

echo -e "${GREEN}Creating helper script for running tests...${NC}"

# Create a helper script to run the tests with the correct environment
docker exec -it turdparty_test_playwright bash -c "
sudo bash -c 'cat > /usr/local/bin/run-playwright-tests << \"EOF\"
#!/bin/bash

# Script to run Playwright tests with the correct environment variables
export PLAYWRIGHT_BROWSERS_PATH=/usr/local/lib/playwright
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

# Set DEBUG if requested
if [ \"\$1\" = \"--debug\" ]; then
  export DEBUG=pw:api
  shift
fi

# Check if we want to run in headed mode
HEADED_ARG=\"\"
if [ \"\$1\" = \"--headed\" ]; then
  HEADED_ARG=\"--headed\"
  shift
fi

# Run the tests
echo \"Running Playwright tests...\"
cd /app
npx playwright test \$@ \$HEADED_ARG
EOF'

sudo chmod +x /usr/local/bin/run-playwright-tests
"

echo -e "${GREEN}Creating a simple test to verify the environment...${NC}"

# Create a simple test to verify that Playwright is working
docker exec -it turdparty_test_playwright bash -c "
mkdir -p /app/tests/verification
cat > /app/tests/verification/simple-test.spec.ts << 'EOF'
import { test, expect } from '@playwright/test';

test('Playwright environment check', async ({ page }) => {
  console.log('Running simple verification test...');
  
  // Navigate to a test page
  await page.goto('https://playwright.dev/');
  
  // Take a screenshot
  await page.screenshot({ path: 'test_screenshots/verification.png' });
  
  // Simple assertion
  const title = await page.title();
  console.log(`Page title: ${title}`);
  expect(title).toContain('Playwright');
  
  console.log('Test completed successfully!');
});
EOF

# Create a simple playwright.config.js for the verification test
cat > /app/playwright.config.ts << 'EOF'
import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './tests/verification',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  use: {
    headless: true,
    browserName: 'chromium',
    screenshot: 'only-on-failure',
  },
  reporter: [
    ['html', { outputFolder: 'test-results/playwright-report' }],
    ['list']
  ],
  outputDir: 'test_screenshots',
});
EOF
"

echo -e "${YELLOW}Running verification test to check environment...${NC}"

# Run the verification test
docker exec -it turdparty_test_playwright bash -c "
cd /app
export PLAYWRIGHT_BROWSERS_PATH=/usr/local/lib/playwright
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
npx playwright test tests/verification/simple-test.spec.ts
"

# Check test exit code
if [ $? -eq 0 ]; then
  echo -e "${GREEN}Playwright environment has been set up successfully!${NC}"
  echo -e "${YELLOW}You can now run tests with:${NC}"
  echo -e "  docker exec -it turdparty_test_playwright /usr/local/bin/run-playwright-tests tests/playwright/complete-upload-flow.spec.ts"
else
  echo -e "${RED}Verification test failed. Check the output above for errors.${NC}"
  exit 1
fi 