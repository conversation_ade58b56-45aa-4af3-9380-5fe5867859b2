services:
  api:
    container_name: turdparty_api_1
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-api
    ports:
      - "3050:8000"
    depends_on:
      - postgres
      - minio
    environment:
      - DATABASE_URL=********************************************/app
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@postgres:5432/app
      - PORT=8000
      - TEST_MODE=true
      - PYTHONPATH=/app
      - DEBUG=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_DIRECT=true
      - VAGRANT_TEST_MODE=1
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - ../uploads:/app/uploads
      - turdparty_ssh_keys:/home/<USER>/.ssh
      - ./scripts/container-init.sh:/usr/local/bin/container-init.sh
      - ../vagrant_exec.sh:/app/vagrant_exec.sh
    entrypoint: ["/usr/local/bin/container-init.sh"]
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
    cap_add:
      - NET_ADMIN
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-api.rule=Host(`localhost`) && PathPrefix(`/api`)"
      - "traefik.http.routers.turdparty-api.entrypoints=web"
      - "traefik.http.services.turdparty-api.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik-network"

  react-app:
    container_name: turdparty_frontend_1
    image: nginx:alpine
    ports:
      - "3100:80"
    depends_on:
      - api
    volumes:
      - ../turdparty-app/build:/usr/share/nginx/html
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-frontend.rule=Host(`localhost`)"
      - "traefik.http.routers.turdparty-frontend.entrypoints=web"
      - "traefik.http.services.turdparty-frontend.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik-network"

  dashboard:
    container_name: turdparty_dashboard
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.dashboard
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-dashboard
    ports:
      - "3150:8080"
    volumes:
      - ..:/app
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - PYTHONPATH=/app
      - CONTAINER_PREFIX=turdparty
    command: ["python", "/app/dashboard.py", "start"]
    user: root
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-dashboard.rule=Host(`localhost`) && PathPrefix(`/dashboard`)"
      - "traefik.http.routers.turdparty-dashboard.entrypoints=web"
      - "traefik.http.services.turdparty-dashboard.loadbalancer.server.port=8080"
      - "traefik.docker.network=traefik-network"

  postgres:
    container_name: turdparty_postgres_1
    image: postgres:14-alpine
    ports:
      - "3200:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=app
    volumes:
      - turdparty_postgres_data:/var/lib/postgresql/data
    networks:
      - turdparty_network
    restart: unless-stopped

  minio:
    container_name: turdparty_minio_1
    image: minio/minio:latest
    ports:
      - "9090:9000"
      - "9091:9001"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      # MinIO API
      - "traefik.http.routers.turdparty-minio-api.rule=Host(`localhost`) && PathPrefix(`/minio`)"
      - "traefik.http.routers.turdparty-minio-api.entrypoints=web"
      - "traefik.http.routers.turdparty-minio-api.service=turdparty-minio-api"
      - "traefik.http.services.turdparty-minio-api.loadbalancer.server.port=9000"
      # MinIO Console
      - "traefik.http.routers.turdparty-minio-console.rule=Host(`localhost`) && PathPrefix(`/minio-console`)"
      - "traefik.http.routers.turdparty-minio-console.entrypoints=web"
      - "traefik.http.routers.turdparty-minio-console.service=turdparty-minio-console"
      - "traefik.http.services.turdparty-minio-console.loadbalancer.server.port=9001"
      - "traefik.docker.network=traefik-network"

  minio-ssh:
    container_name: turdparty_minio_ssh_1
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.minio
    ports:
      - "2223:22"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    networks:
      - turdparty_network
    restart: unless-stopped

volumes:
  turdparty_postgres_data:
  turdparty_ssh_keys:
  turdparty_frontend_node_modules:
  turdparty_minio_data:

networks:
  turdparty_network:
    driver: bridge
  traefik-network:
    external: true
    name: traefik-network