services:
  api:
    container_name: turdparty_api
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-api
    expose:
      - "8000"  # Internal port for Traefik
    depends_on:
      postgres:
        condition: service_healthy
      minio:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DATABASE_URL=************************************************************************************/app
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:turdparty_service_dashboard_secure_password_2025@postgres:5432/app
      - PORT=8000
      - TEST_MODE=true
      - PYTHONPATH=/app
      - DEBUG=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_DIRECT=true
      - VAGRANT_TEST_MODE=1
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - ../uploads:/app/uploads
      - turdparty_ssh_keys:/home/<USER>/.ssh
      - ./scripts/container-init.sh:/usr/local/bin/container-init.sh
      - ../vagrant_exec.sh:/app/vagrant_exec.sh
    entrypoint: ["/usr/local/bin/container-init.sh"]
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
    cap_add:
      - NET_ADMIN
    networks:
      - turdparty_network
      - net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.localhost`)"
      - "traefik.http.routers.api.entrypoints=web"
      - "traefik.http.services.api.loadbalancer.server.port=8000"
    # healthcheck:
    #   test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/api/v1/health')"]
    #   interval: 10s
    #   timeout: 5s
    #   retries: 3
    #   start_period: 20s

  react-app:
    container_name: turdparty_frontend
    image: nginx:alpine
    expose:
      - "80"  # Internal port for Traefik
    depends_on:
      - api
    volumes:
      - ../turdparty-app/build:/usr/share/nginx/html
    networks:
      - turdparty_network
      - net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`frontend.localhost`)"
      - "traefik.http.routers.frontend.entrypoints=web"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:80"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s

  dashboard:
    container_name: turdparty_dashboard
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.dashboard
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-dashboard
    expose:
      - "8080"  # Internal port for Traefik
    volumes:
      - ..:/app
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - PYTHONPATH=/app
      - CONTAINER_PREFIX=turdparty
    command: ["python", "/app/.dockerwrapper/dashboard/dashboard.py", "start"]
    user: root
    networks:
      - turdparty_network
      - net
    restart: "no"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`dashboard.localhost`)"
      - "traefik.http.routers.dashboard.entrypoints=web"
      - "traefik.http.services.dashboard.loadbalancer.server.port=8080"
    healthcheck:
      test: ["CMD", "ps", "aux", "|", "grep", "[p]ython /app/.dockerwrapper/dashboard/dashboard.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  postgres:
    container_name: turdparty_postgres
    image: postgres:14-alpine
    ports:
      - "3200:5432"  # Keep external port for direct database access
    expose:
      - "5432"  # Internal port for container communication
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=turdparty_service_dashboard_secure_password_2025
      - POSTGRES_DB=app
      - POSTGRES_MULTIPLE_DATABASES=app,cachet
    volumes:
      - turdparty_postgres_data:/var/lib/postgresql/data
      - ./scripts/service-dashboard/create-multiple-postgresql-databases.sh:/docker-entrypoint-initdb.d/create-multiple-postgresql-databases.sh
    networks:
      - turdparty_network
      # Note: PostgreSQL doesn't need traefik-network as it's not a web service
    restart: unless-stopped
    labels:
      - "traefik.enable=false"  # Database services shouldn't be exposed via HTTP
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  minio:
    container_name: turdparty_minio
    image: minio/minio:latest
    ports:
      - "3300:9000"  # External MinIO API access
      - "3301:9001"  # External MinIO Console access
    expose:
      - "9000"  # Internal MinIO API port for Traefik
      - "9001"  # Internal MinIO Console port for Traefik
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      # MinIO API
      - "traefik.http.routers.minio.rule=Host(`minio.localhost`)"
      - "traefik.http.routers.minio.entrypoints=web"
      - "traefik.http.routers.minio.service=minio"
      - "traefik.http.services.minio.loadbalancer.server.port=9000"
      # MinIO Console
      - "traefik.http.routers.minio-console.rule=Host(`minio-console.localhost`)"
      - "traefik.http.routers.minio-console.entrypoints=web"
      - "traefik.http.routers.minio-console.service=minio-console"
      - "traefik.http.services.minio-console.loadbalancer.server.port=9001"

  minio-ssh:
    container_name: turdparty_minio_ssh
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.minio
    ports:
      - "2223:22"  # SSH access port
    expose:
      - "22"  # Internal SSH port
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    networks:
      - turdparty_network
      # Note: SSH services don't need traefik-network as they're not HTTP services
    restart: unless-stopped
    labels:
      - "traefik.enable=false"  # SSH services shouldn't be exposed via HTTP
    depends_on:
      minio:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "pgrep", "sshd"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  redis:
    container_name: turdparty_redis
    image: redis:7-alpine
    ports:
      - "3400:6379"  # Keep external port for direct Redis access
    expose:
      - "6379"  # Internal port for container communication
    volumes:
      - turdparty_redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - turdparty_network
      # Note: Redis doesn't need traefik-network as it's not a web service
    restart: unless-stopped
    labels:
      - "traefik.enable=false"  # Cache services shouldn't be exposed via HTTP

  celery-worker-default:
    container_name: turdparty_celery_default
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      redis:
        condition: service_healthy
      api:
        condition: service_started
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_QUEUE=default
      - CELERY_CONCURRENCY=2
      - CELERY_LOG_LEVEL=info
      - WAIT_FOR_API=false
    volumes:
      - ..:/app
      - ../logs:/app/logs
    command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "default", "-c", "2"]
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=false"  # Celery workers don't need web access
    healthcheck:
      test: ["CMD", "celery", "-A", "api.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-worker-file-ops:
    container_name: turdparty_celery_file_ops
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      redis:
        condition: service_healthy
      api:
        condition: service_started
      minio:
        condition: service_healthy
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_QUEUE=file_ops
      - CELERY_CONCURRENCY=2
      - CELERY_LOG_LEVEL=info
      - WAIT_FOR_API=false
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - ../uploads:/app/uploads
    command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "file_ops", "-c", "2"]
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=false"  # Celery workers don't need web access
    healthcheck:
      test: ["CMD", "celery", "-A", "api.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-worker-vm-ops:
    container_name: turdparty_celery_vm_ops
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      redis:
        condition: service_healthy
      api:
        condition: service_started
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_QUEUE=vm_ops
      - CELERY_CONCURRENCY=2
      - CELERY_LOG_LEVEL=info
      - WAIT_FOR_API=false
      - VAGRANT_TEST_MODE=1
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - turdparty_ssh_keys:/home/<USER>/.ssh
      - ../vagrant_exec.sh:/app/vagrant_exec.sh
    command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "vm_ops", "-c", "2"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=false"  # Celery workers don't need web access
    healthcheck:
      test: ["CMD", "celery", "-A", "api.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-flower:
    container_name: turdparty_celery_flower
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      redis:
        condition: service_healthy
      celery-worker-default:
        condition: service_healthy
    ports:
      - "3450:5555"  # External Flower access port
    expose:
      - "5555"  # Internal port for Traefik
    environment:
      - PYTHONPATH=/app
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FLOWER_UNAUTHENTICATED_API=true
    volumes:
      - ..:/app
    entrypoint: ["/usr/local/bin/celery-flower-entrypoint.sh"]
    command: ["celery", "-A", "api.celery_app", "flower", "--port=5555", "--address=0.0.0.0"]
    networks:
      - turdparty_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.flower.rule=Host(`flower.localhost`)"
      - "traefik.http.routers.flower.entrypoints=web"
      - "traefik.http.services.flower.loadbalancer.server.port=5555"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555/api/workers"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  turdparty_postgres_data:
  turdparty_ssh_keys:
  turdparty_frontend_node_modules:
  turdparty_minio_data:
  turdparty_redis_data:

networks:
  turdparty_network:
    driver: bridge
  net:
    external: true
    name: net