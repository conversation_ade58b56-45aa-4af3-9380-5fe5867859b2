FROM mcr.microsoft.com/playwright:v1.41.0-focal

WORKDIR /app

# Install dependencies
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    vim \
    iputils-ping \
    dnsutils \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package.json package-lock.json* ./
RUN npm install
RUN npm install uuid

# Install Playwright specific dependencies
RUN npx playwright install chromium
RUN npx playwright install-deps chromium

# Create output directories
RUN mkdir -p /app/test_screenshots /app/test-results
RUN chmod -R 777 /app/test_screenshots /app/test-results

# Create a simple verification test
RUN mkdir -p /app/tests/verification
RUN echo "import { test, expect } from '@playwright/test';" > /app/tests/verification/basic.spec.js && \
    echo "test('basic test', async ({ page }) => {" >> /app/tests/verification/basic.spec.js && \
    echo "  console.log('Running basic verification test...');" >> /app/tests/verification/basic.spec.js && \
    echo "  try {" >> /app/tests/verification/basic.spec.js && \
    echo "    console.log('Connecting to frontend at localhost:3100');" >> /app/tests/verification/basic.spec.js && \
    echo "    await page.goto('http://localhost:3100');" >> /app/tests/verification/basic.spec.js && \
    echo "    await page.screenshot({ path: '/app/test_screenshots/homepage.png' });" >> /app/tests/verification/basic.spec.js && \
    echo "    const title = await page.title();" >> /app/tests/verification/basic.spec.js && \
    echo "    console.log('Page title:', title);" >> /app/tests/verification/basic.spec.js && \
    echo "    expect(page.url()).toContain('localhost:3100');" >> /app/tests/verification/basic.spec.js && \
    echo "    console.log('Basic test completed successfully');" >> /app/tests/verification/basic.spec.js && \
    echo "  } catch (error) {" >> /app/tests/verification/basic.spec.js && \
    echo "    console.error('Test error:', error);" >> /app/tests/verification/basic.spec.js && \
    echo "    throw error;" >> /app/tests/verification/basic.spec.js && \
    echo "  }" >> /app/tests/verification/basic.spec.js && \
    echo "});" >> /app/tests/verification/basic.spec.js

# Create a simple test for uploading a file
RUN echo "import { test, expect } from '@playwright/test';" > /app/tests/verification/upload.spec.js && \
    echo "import * as fs from 'fs';" >> /app/tests/verification/upload.spec.js && \
    echo "test('file upload test', async ({ page }) => {" >> /app/tests/verification/upload.spec.js && \
    echo "  console.log('Running file upload test...');" >> /app/tests/verification/upload.spec.js && \
    echo "  try {" >> /app/tests/verification/upload.spec.js && \
    echo "    // Create a test file" >> /app/tests/verification/upload.spec.js && \
    echo "    const testFilePath = '/tmp/test-upload.txt';" >> /app/tests/verification/upload.spec.js && \
    echo "    fs.writeFileSync(testFilePath, 'This is a test file for upload');" >> /app/tests/verification/upload.spec.js && \
    echo "    console.log('Created test file at', testFilePath);" >> /app/tests/verification/upload.spec.js && \
    echo "    // Navigate to the upload page" >> /app/tests/verification/upload.spec.js && \
    echo "    await page.goto('http://localhost:3100/upload');" >> /app/tests/verification/upload.spec.js && \
    echo "    await page.screenshot({ path: '/app/test_screenshots/upload-page.png' });" >> /app/tests/verification/upload.spec.js && \
    echo "    console.log('Upload page loaded');" >> /app/tests/verification/upload.spec.js && \
    echo "    const title = await page.title();" >> /app/tests/verification/upload.spec.js && \
    echo "    console.log('Page title:', title);" >> /app/tests/verification/upload.spec.js && \
    echo "    console.log('File upload test completed');" >> /app/tests/verification/upload.spec.js && \
    echo "  } catch (error) {" >> /app/tests/verification/upload.spec.js && \
    echo "    console.error('Test error:', error);" >> /app/tests/verification/upload.spec.js && \
    echo "    throw error;" >> /app/tests/verification/upload.spec.js && \
    echo "  }" >> /app/tests/verification/upload.spec.js && \
    echo "});" >> /app/tests/verification/upload.spec.js

# Create simple playwright config file
COPY .dockerwrapper/playwright.config.js /app/playwright.config.js

# Create and add the run-tests.sh script
COPY .dockerwrapper/run-tests.sh /app/run-tests.sh
RUN chmod +x /app/run-tests.sh

# Create a network test script
RUN echo "#!/bin/bash" > /app/check-network.sh && \
    echo "echo 'Checking network connectivity...'" >> /app/check-network.sh && \
    echo "echo -n 'Container hostname: ' && hostname" >> /app/check-network.sh && \
    echo "echo -n 'Container IP: ' && hostname -i" >> /app/check-network.sh && \
    echo "echo 'DNS resolution for react-app:' && getent hosts react-app || echo 'Failed to resolve react-app'" >> /app/check-network.sh && \
    echo "echo 'DNS resolution for api:' && getent hosts api || echo 'Failed to resolve api'" >> /app/check-network.sh && \
    echo "echo 'DNS resolution for localhost:' && getent hosts localhost || echo 'Failed to resolve localhost'" >> /app/check-network.sh && \
    echo "echo 'Pinging react-app:' && ping -c 2 react-app || echo 'Could not ping react-app'" >> /app/check-network.sh && \
    echo "echo 'Pinging localhost:' && ping -c 2 localhost || echo 'Could not ping localhost'" >> /app/check-network.sh && \
    echo "echo 'Checking if react-app port 3000 is open:' && nc -zv react-app 3000 || echo 'Port 3000 not accessible'" >> /app/check-network.sh && \
    echo "echo 'Checking if localhost port 3100 is open:' && nc -zv localhost 3100 || echo 'Port 3100 not accessible'" >> /app/check-network.sh && \
    echo "echo 'Network check completed'" >> /app/check-network.sh && \
    chmod +x /app/check-network.sh

ENTRYPOINT ["/bin/bash", "-c"]
CMD ["npx playwright test --debug"] 