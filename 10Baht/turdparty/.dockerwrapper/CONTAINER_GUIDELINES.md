# Container Management Guidelines

## Important Rules

1. **NEVER stop other containers unnecessarily**
   - Each script should only manage the containers it directly needs
   - Avoid using `docker-compose down` which stops all containers
   - Use targeted commands like `docker stop <container_name>` for specific containers

2. **Preserve Running Environments**
   - The API, DB, UI, and MinIO containers should remain running between test runs
   - Only restart containers if explicitly requested by the user
   - When in doubt, preserve the state of existing containers

3. **Use Isolated Network Configuration**
   - Test containers should connect to existing service containers
   - Configure proper networking to allow container-to-container communication
   - Use container names or network aliases for service discovery

4. **Container Version Management**
   - Keep Docker image versions in sync with installed dependencies
   - Document version requirements in Dockerfiles and compose files
   - Use version checks to prevent mismatches

## Script Design Patterns

### Good Pattern: Container-Specific Operations

```bash
# Only stop/remove specific container
docker stop turdparty_playwright 2>/dev/null || true
docker rm turdparty_playwright 2>/dev/null || true

# Only start specific container
docker-compose -f docker-compose.yml up -d playwright
```

### Bad Pattern: Stopping All Containers

```bash
# AVOID THIS - stops all containers
docker-compose -f docker-compose.yml down

# AVOID THIS - rebuilds all services
docker-compose -f docker-compose.yml up -d --build
```

## Networking Between Containers

To ensure proper communication between containers:

1. Make sure all containers are on the same Docker network
2. Use container names as hostnames in service URLs
3. Configure environment variables to point to the correct hosts
4. Use proper health checks to ensure services are ready

## Testing Guidelines

1. Always run test containers in the context of existing services
2. Use volume mounts to share test files and results
3. Clean up test artifacts but preserve running containers
4. Use consistent user and permission settings 