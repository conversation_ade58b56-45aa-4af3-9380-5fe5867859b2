#!/bin/bash
set -e

# Skip host.docker.internal setup - this is now handled by extra_hosts in docker-compose.yml

# Setup SSH for app user if not already done
if [ ! -f "/home/<USER>/.ssh/id_rsa" ]; then
  mkdir -p /home/<USER>/.ssh
  chmod 700 /home/<USER>/.ssh
  
  # Generate SSH key (already running as app user)
  ssh-keygen -t ed25519 -f /home/<USER>/.ssh/id_rsa -N ''
  echo 'Host *
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null' > /home/<USER>/.ssh/config
  chmod 600 /home/<USER>/.ssh/config
  echo "SSH setup completed"
fi

# Ensure file upload directory exists
FILE_UPLOAD_DIR=${FILE_UPLOAD_DIR:-/app/uploads}
mkdir -p $FILE_UPLOAD_DIR || true
echo "File upload directory set up at $FILE_UPLOAD_DIR"

echo "Development container initialized. SSH public key:"
cat /home/<USER>/.ssh/id_rsa.pub

# Ensure Python3 is available
command -v python3 >/dev/null 2>&1 || { echo "python3 is not installed. Installing..."; apt-get update && apt-get install -y python3 python3-pip python3-venv; }

# Always create and activate virtual environment
echo "Setting up Python virtual environment..."
python3 -m venv /app/.venv || { echo "Failed to create venv with python3. Trying with python..."; python -m venv /app/.venv; }
source /app/.venv/bin/activate
pip install --upgrade pip

# Skip pyproject.toml installation as it's causing issues
echo "Skipping pyproject.toml installation"

# Install dependencies from requirements.txt if it exists
if [ -f "/app/requirements.txt" ]; then
  echo "Installing dependencies from requirements.txt..."
  pip install -r /app/requirements.txt
else
  # Install all required packages with version pinning
  echo "Installing required packages from hardcoded list..."
  pip install --no-cache-dir \
    fastapi==0.68.0 \
    uvicorn==0.15.0 \
    python-multipart==0.0.5 \
    PyJWT==2.8.0 \
    passlib[bcrypt]==1.7.4 \
    sqlalchemy==1.4.23 \
    alembic==1.7.1 \
    psycopg2-binary==2.9.1 \
    asyncpg==0.24.0 \
    aiofiles==0.7.0 \
    python-dotenv==0.19.0 \
    pydantic==1.8.2 \
    pydantic-settings==2.0.0 \
    email-validator==1.1.3 \
    minio==7.1.0 \
    paramiko==2.7.2 \
    python-magic==0.4.24 \
    aiohttp==3.8.0 \
    requests==2.26.0 \
    python-dateutil==2.8.2 \
    prometheus-client==0.11.0 \
    python-json-logger==2.0.2 \
    pytest==6.2.5 \
    pytest-asyncio==0.15.1 \
    httpx==0.18.2 \
    pytest-cov==2.12.1 \
    pytest-mock==3.6.1 \
    black==21.7b0 \
    flake8==3.9.2 \
    mypy==0.910 \
    isort==5.9.3 \
    mkdocs==1.2.2 \
    mkdocs-material==7.2.6
fi

echo "Python virtual environment setup completed"

# Execute the original command
if [ -z "$1" ]; then
    # If no command is provided, run the default command
    echo "Running default command: python main.py"
    exec python main.py
else
    # Execute the provided command with the virtual environment activated
    echo "Running command: $@"
    exec "$@"
fi 