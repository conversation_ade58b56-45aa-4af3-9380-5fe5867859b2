# 🚀 Traefik Integration for TurdParty Services

## 📋 Overview

All TurdParty Docker services are configured to register with <PERSON>raefik using the `servicename.localhost` pattern. Each service gets its own subdomain for clean access.

## 🌐 Service Routes

### **Production Services** (`docker-compose.yml`)

| Service | Internal Port | Traefik Route | Description |
|---------|---------------|---------------|-------------|
| **Frontend** | 80 | `http://frontend.localhost` | Main React application |
| **API** | 8000 | `http://api.localhost` | FastAPI backend |
| **Dashboard** | 8080 | `http://dashboard.localhost` | Service monitoring dashboard |
| **PostgreSQL** | 5432 | `http://postgres.localhost` | Database server |
| **Redis** | 6379 | `http://redis.localhost` | Cache and message broker |
| **MinIO API** | 9000 | `http://minio.localhost` | Object storage API |
| **MinIO Console** | 9001 | `http://minio-console.localhost` | MinIO web interface |
| **MinIO SSH** | 22 | `http://minio-ssh.localhost` | SSH access to storage |
| **Flower** | 5555 | `http://flower.localhost` | Celery task monitoring |

### **Development Services** (`dev-compose.yml`)

| Service | Internal Port | Traefik Route | Description |
|---------|---------------|---------------|-------------|
| **Test API** | 8000 | `http://test-api.localhost` | Development API server |
| **Test Frontend** | 3000 | `http://test-frontend.localhost` | Development React server |
| **Test Dashboard** | 9323 | `http://test-dashboard.localhost` | Development dashboard |
| **Test MinIO** | 9000 | `http://test-minio.localhost` | Development object storage |
| **Playwright** | 9323 | `http://playwright.localhost` | Test runner interface |

### **Testing Services** (`docker-compose.playwright.yml`)

| Service | Internal Port | Traefik Route | Description |
|---------|---------------|---------------|-------------|
| **Playwright Test** | 9323 | `http://playwright-test.localhost` | Test runner interface |
| **UI Test** | 3100 | `http://ui-test.localhost` | Test UI server |
| **API Test** | 8000 | `http://api-test.localhost` | Test API server |

### **Frontend Only** (`frontend-compose.yml`)

| Service | Internal Port | Traefik Route | Description |
|---------|---------------|---------------|-------------|
| **Frontend Dev** | 3000 | `http://frontend-dev.localhost` | Standalone frontend development |

## 🔧 Configuration Pattern

All services follow this Traefik configuration pattern:

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.{service-name}.rule=Host(`{service-name}.localhost`)"
  - "traefik.http.routers.{service-name}.entrypoints=web"
  - "traefik.http.services.{service-name}.loadbalancer.server.port={port}"
```

### **Network Configuration:**

Web services connect to both:
- **Internal network**: `turdparty_network` / `turdparty_test_network`
- **External network**: `net` (Traefik network)

```yaml
networks:
  - turdparty_network
  - net

networks:
  net:
    external: true
    name: net
```

### **Port Configuration:**

Following best practices:
- **No external port mappings** for Traefik-managed services
- **Only `expose` directives** for internal communication
- **External ports removed** - Traefik handles all routing

## 🚀 Getting Started

### **Prerequisites:**

1. **Traefik must be running** on the `net` network:
   ```bash
   # Create the external network
   docker network create net

   # Start Traefik (configured to use 'net' network)
   # Traefik should be configured with network: net
   ```

### **Starting Services:**

```bash
# Production services
cd .dockerwrapper
docker compose up -d

# Development services  
docker compose -f dev-compose.yml up -d

# Testing services
docker compose -f docker-compose.playwright.yml up -d

# Frontend only
docker compose -f frontend-compose.yml up -d
```

## 📊 Service Access Examples

Once Traefik and services are running:

### **Production:**
- **🏠 Main App**: http://frontend.localhost
- **🔧 API**: http://api.localhost
- **📊 Dashboard**: http://dashboard.localhost
- **💾 MinIO**: http://minio.localhost
- **🖥️ MinIO Console**: http://minio-console.localhost
- **🌸 Flower**: http://flower.localhost

### **Development:**
- **🧪 Test API**: http://test-api.localhost
- **🧪 Test Frontend**: http://test-frontend.localhost
- **🎭 Playwright**: http://playwright.localhost

### **Testing:**
- **🎭 Playwright Tests**: http://playwright-test.localhost
- **🧪 UI Tests**: http://ui-test.localhost

## 🎯 Benefits

- **🔗 Clean URLs**: Each service has its own subdomain
- **🛡️ Service Isolation**: Clear separation between environments
- **🔄 Easy Discovery**: Predictable naming pattern
- **📈 Scalability**: Ready for production deployment
- **🚀 Development Friendly**: Multiple environments supported

---

**All TurdParty services are now Traefik-ready with clean subdomain access! 🎉**
