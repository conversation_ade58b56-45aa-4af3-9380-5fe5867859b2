# 🚀 Traefik Integration for TurdParty Services

## 📋 Overview

All TurdParty Docker services are now configured to register with Traefik reverse proxy running on `localhost:8080`. This provides unified access to all services through a single entry point with proper routing.

## 🌐 Service Routes

### **Production Services** (`docker-compose.yml`)

| Service | Internal Port | Traefik Route | Description |
|---------|---------------|---------------|-------------|
| **Frontend** | 80 | `http://localhost:8080/` | Main React application |
| **API** | 8000 | `http://localhost:8080/api` | FastAPI backend |
| **Dashboard** | 8080 | `http://localhost:8080/dashboard` | Service monitoring dashboard |
| **MinIO API** | 9000 | `http://localhost:8080/minio` | Object storage API |
| **MinIO Console** | 9001 | `http://localhost:8080/minio-console` | MinIO web interface |

### **Development Services** (`dev-compose.yml`)

| Service | Internal Port | Traefik Route | Description |
|---------|---------------|---------------|-------------|
| **Test API** | 8000 | `http://localhost:8080/test-api` | Development API server |
| **Test Frontend** | 3000 | `http://localhost:8080/test` | Development React server |

### **Testing Services** (`docker-compose.playwright.yml`)

| Service | Internal Port | Traefik Route | Description |
|---------|---------------|---------------|-------------|
| **Playwright** | 9323 | `http://localhost:8080/playwright` | Test runner interface |

## 🔧 Configuration Details

### **Traefik Labels Applied:**

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.{service-name}.rule=Host(`localhost`) && PathPrefix(`{path}`)"
  - "traefik.http.routers.{service-name}.entrypoints=web"
  - "traefik.http.services.{service-name}.loadbalancer.server.port={port}"
  - "traefik.docker.network=traefik-network"
```

### **Network Configuration:**

All services are connected to both:
- **Internal network**: `turdparty_network` (for inter-service communication)
- **External network**: `traefik-network` (for Traefik routing)

```yaml
networks:
  - turdparty_network
  - traefik-network

networks:
  traefik-network:
    external: true
    name: traefik-network
```

## 🚀 Getting Started

### **Prerequisites:**

1. **Traefik must be running** on the `traefik-network`:
   ```bash
   # Create the external network if it doesn't exist
   docker network create traefik-network
   
   # Start Traefik (example configuration)
   docker run -d \
     --name traefik \
     --network traefik-network \
     -p 8080:8080 \
     -p 80:80 \
     -v /var/run/docker.sock:/var/run/docker.sock \
     traefik:v2.10 \
     --api.insecure=true \
     --providers.docker=true \
     --entrypoints.web.address=:80
   ```

### **Starting Services:**

```bash
# Production services
cd .dockerwrapper
docker compose up -d

# Development services  
docker compose -f dev-compose.yml up -d

# Testing services
docker compose -f docker-compose.playwright.yml up -d
```

## 📊 Service Access

Once Traefik and services are running:

- **🏠 Main App**: http://localhost:8080/
- **🔧 API Docs**: http://localhost:8080/api/docs
- **📊 Dashboard**: http://localhost:8080/dashboard
- **💾 MinIO Console**: http://localhost:8080/minio-console
- **🧪 Test Interface**: http://localhost:8080/test
- **🎭 Playwright**: http://localhost:8080/playwright
- **⚙️ Traefik Dashboard**: http://localhost:8080:8080

## 🔍 Troubleshooting

### **Common Issues:**

1. **Services not accessible through Traefik:**
   - Verify `traefik-network` exists: `docker network ls`
   - Check Traefik is running: `docker ps | grep traefik`
   - Verify services are on the network: `docker network inspect traefik-network`

2. **Path conflicts:**
   - Each service has unique path prefixes to avoid conflicts
   - API routes are under `/api`, dashboard under `/dashboard`, etc.

3. **Port conflicts:**
   - Direct port access still available (e.g., `:3050` for API)
   - Traefik provides unified access through `:8080`

### **Debugging Commands:**

```bash
# Check Traefik routing
curl -H "Host: localhost" http://localhost:8080/api/health

# Verify service registration
docker logs traefik

# Check network connectivity
docker exec turdparty_api_1 ping traefik
```

## 🎯 Benefits

- **🔗 Unified Access**: Single entry point for all services
- **🛡️ Path-based Routing**: Clean URLs with logical paths
- **🔄 Load Balancing**: Traefik handles service discovery
- **📈 Monitoring**: Centralized routing and metrics
- **🚀 Production Ready**: Scalable reverse proxy setup

---

**All TurdParty services are now Traefik-ready! 🎉**
