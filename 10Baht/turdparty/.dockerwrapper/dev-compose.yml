version: '3.8'

services:
  api:
    container_name: turdparty_test_api
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-test-api
    ports:
      - "3050:8000"
    depends_on:
      - postgres
      - minio-test
    environment:
      - DATABASE_URL=********************************************/app
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@postgres:5432/app
      - PORT=8000
      - TEST_MODE=true
      - PYTHONPATH=/app
      - DEBUG=true
      - RELOAD=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
      - MINIO_HOST=minio-test
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_DIRECT=true
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - ../uploads:/app/uploads
      - turdparty_test_ssh_keys:/home/<USER>/.ssh
      - ./scripts/dev-container-init.sh:/usr/local/bin/container-init.sh
      - turdparty_test_api_venv:/app/.venv
    entrypoint: ["/usr/local/bin/container-init.sh"]
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
    cap_add:
      - NET_ADMIN
    networks:
      - turdparty_test_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-test-api.rule=Host(`localhost`) && PathPrefix(`/test-api`)"
      - "traefik.http.routers.turdparty-test-api.entrypoints=web"
      - "traefik.http.services.turdparty-test-api.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik-network"

  react-app:
    container_name: turdparty_test_frontend
    image: node:18-alpine
    working_dir: /app
    ports:
      - "3100:3000"
    depends_on:
      - api
    environment:
      - REACT_APP_API_URL=http://turdparty_test_api:8000
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WDS_SOCKET_PORT=3100
    volumes:
      - ../turdparty-app:/app
      - turdparty_test_frontend_node_modules:/app/node_modules
    command: sh -c "cd /app && npm install --legacy-peer-deps --no-package-lock && npm install ajv@8.12.0 --legacy-peer-deps --force && npm start"
    networks:
      - turdparty_test_network
      - traefik-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turdparty-test-frontend.rule=Host(`localhost`) && PathPrefix(`/test`)"
      - "traefik.http.routers.turdparty-test-frontend.entrypoints=web"
      - "traefik.http.services.turdparty-test-frontend.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik-network"

  dashboard:
    container_name: turdparty_test_dashboard
    build:
      context: .
      dockerfile: Dockerfile.dashboard
      args:
        - IMAGE_PREFIX=turdparty-test
    image: turdparty-test-dashboard
    ports:
      - "3150:9323"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - PYTHONPATH=/app
      - DEBUG=true
      - DOCKER_HOST=unix:///var/run/docker.sock
    group_add:
      - ${DOCKER_GROUP_ID:-999}
    depends_on:
      - api
      - react-app
      - postgres
    command: >
      bash -c "cd /app &&
               python -m venv /app/.venv &&
               source /app/.venv/bin/activate &&
               pip install click==8.1.8 docker==6.1.3 urwid==2.6.16 pyyaml==6.0.2 &&
               python dashboard.py start --dev"
    networks:
      - turdparty_test_network
    restart: unless-stopped

  postgres:
    container_name: turdparty_test_postgres
    image: postgres:14-alpine
    ports:
      - "3200:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=app
    volumes:
      - turdparty_test_postgres_data:/var/lib/postgresql/data
    networks:
      - turdparty_test_network
    restart: unless-stopped

  minio-test:
    container_name: turdparty_test_minio
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.minio
    ports:
      - "3300:9000"
      - "3301:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - turdparty_test_minio_data:/data
    networks:
      - turdparty_test_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 10s

  playwright:
    container_name: turdparty_test_playwright
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.playwright
    ports:
      - "3250:9323"
    volumes:
      - ..:/app:rw
      - ../turdparty-app:/app/turdparty-app:rw
      - ../docs/screenshots:/ms-playwright/screenshots:rw
      - ../docs/test-results:/ms-playwright/test-results:rw
      - turdparty_test_api_venv:/app/.venv
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://api:8000
      - PYTHONPATH=/app
    command: >
      bash -c "cd /app && python3 -m http.server 9323"
    networks:
      - turdparty_test_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9323"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  turdparty_test_postgres_data:
  turdparty_test_ssh_keys:
  turdparty_test_frontend_node_modules:
  turdparty_test_api_venv:
  turdparty_test_dashboard_venv:
  turdparty_test_minio_data:

networks:
  turdparty_test_network:
    driver: bridge
  traefik-network:
    external: true
    name: traefik-network