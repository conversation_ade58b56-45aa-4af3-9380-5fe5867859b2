# TurdParty React Development Server Dockerfile
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies for building native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Copy package files
COPY turdparty-app/package*.json ./

# Install dependencies with legacy peer deps to resolve conflicts
RUN npm install --legacy-peer-deps

# Copy source code
COPY turdparty-app/ ./

# Create a startup script to handle the development server
RUN echo '#!/bin/sh' > /start.sh && \
    echo 'echo "🚀 Starting TurdParty React Development Server..."' >> /start.sh && \
    echo 'echo "📁 File Upload & VM Management Frontend"' >> /start.sh && \
    echo 'echo "🌐 Available at: http://localhost:3200"' >> /start.sh && \
    echo 'echo ""' >> /start.sh && \
    echo 'echo "📋 TurdParty React Pages:"' >> /start.sh && \
    echo 'echo "   • File Upload: /file_upload"' >> /start.sh && \
    echo 'echo "   • File Selection: /file_selection"' >> /start.sh && \
    echo 'echo "   • VM Management: /vagrant_vm"' >> /start.sh && \
    echo 'echo "   • VM Injection: /vm_injection"' >> /start.sh && \
    echo 'echo "   • VM Status: /vm_status"' >> /start.sh && \
    echo 'echo "   • Main Dashboard: /"' >> /start.sh && \
    echo 'echo ""' >> /start.sh && \
    echo 'npm start' >> /start.sh && \
    chmod +x /start.sh

# Expose port 3000 (React dev server default)
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Start the development server
CMD ["/start.sh"]
