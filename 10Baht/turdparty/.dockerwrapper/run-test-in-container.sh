#!/bin/bash

# Script to run a specific Playwright test in an already running container
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Running Test Directly in Playwright Container      ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Check if container is running
if ! docker ps | grep -q "turdparty_playwright"; then
  echo -e "${RED}Error: Playwright container is not running.${NC}"
  echo -e "${YELLOW}Start the container first with:${NC}"
  echo -e "${YELLOW}  .dockerwrapper/run-playwright-fixed.sh${NC}"
  exit 1
fi

# Determine which test to run
TEST_PATH=""
if [ "$1" == "vm" ]; then
  echo -e "${YELLOW}Running VM integration test${NC}"
  TEST_PATH="tests/playwright/vagrant-integration.spec.js"
elif [ "$1" == "file" ]; then
  echo -e "${YELLOW}Running file upload test${NC}"
  TEST_PATH="tests/playwright/file-to-vm-integration.spec.js"
elif [ -n "$1" ]; then
  echo -e "${YELLOW}Running specific test: $1${NC}"
  TEST_PATH="$1"
else
  echo -e "${YELLOW}No test specified. Please specify a test to run:${NC}"
  echo -e "${YELLOW}  vm    - Run VM integration tests${NC}"
  echo -e "${YELLOW}  file  - Run file-to-VM integration tests${NC}"
  echo -e "${YELLOW}  path  - Specify a custom test path${NC}"
  exit 1
fi

# Additional options
REPORTER="--reporter=list"
if [ "$2" == "html" ]; then
  REPORTER="--reporter=html"
fi

# Run the test in the container
echo -e "${YELLOW}Running Playwright test: ${TEST_PATH} ${REPORTER}${NC}"
docker exec -it turdparty_playwright bash -c "cd /app && npx playwright test ${TEST_PATH} ${REPORTER}"
TEST_RESULT=$?

# Copy reports and screenshots
echo -e "${YELLOW}Copying test reports and screenshots...${NC}"
docker cp turdparty_playwright:/app/playwright-report/. ./playwright-report/ || true
docker cp turdparty_playwright:/app/test-results/. ./test-results/ || true
docker cp turdparty_playwright:/app/test_screenshots/. ./test_screenshots/ || true

# Check test result
if [ $TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tests completed successfully!${NC}"
  echo -e "${YELLOW}Test reports available at:${NC}"
  echo -e "  - test_screenshots/"
  echo -e "  - test-results/"
  echo -e "  - playwright-report/index.html"
else
  echo -e "${RED}Tests failed with code ${TEST_RESULT}. Check the output above for details.${NC}"
fi

exit $TEST_RESULT 