#!/bin/bash

set -e

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=======================================================${NC}"
echo -e "${GREEN}    Updating Playwright Docker Image Version            ${NC}"
echo -e "${GREEN}=======================================================${NC}"

DOCKERFILE=".dockerwrapper/Dockerfile.playwright-fixed"

# Check if the Dockerfile exists
if [ ! -f "$DOCKERFILE" ]; then
    echo -e "${RED}Error: $DOCKERFILE not found.${NC}"
    exit 1
fi

echo -e "Updating Playwright version in $DOCKERFILE..."

# Get the currently specified version
CURRENT_VERSION=$(grep "FROM mcr.microsoft.com/playwright" "$DOCKERFILE" | sed -E 's/.*playwright:v([0-9.]+).*/\1/')
echo -e "Current version: ${YELLOW}$CURRENT_VERSION${NC}"
echo -e "Target version: ${GREEN}1.51.1${NC}"

# Update the FROM line in the Dockerfile
sed -i 's/FROM mcr.microsoft.com\/playwright:v.*-focal/FROM mcr.microsoft.com\/playwright:v1.51.1-focal/' "$DOCKERFILE"

echo -e "\n${GREEN}Updated Dockerfile with the correct version.${NC}"
echo -e "To rebuild the container, run: ${YELLOW}.dockerwrapper/run-playwright-fixed.sh all${NC}"

# Check if the docker-compose file needs updating too
COMPOSE_FILE=".dockerwrapper/docker-compose.playwright-fixed.yml"
if [ -f "$COMPOSE_FILE" ]; then
    echo -e "\nChecking compose file for image references..."
    if grep -q "image: mcr.microsoft.com/playwright:v" "$COMPOSE_FILE"; then
        echo -e "Updating image reference in $COMPOSE_FILE"
        sed -i 's/image: mcr.microsoft.com\/playwright:v.*-focal/image: mcr.microsoft.com\/playwright:v1.51.1-focal/' "$COMPOSE_FILE"
    fi
fi

echo -e "\n${GREEN}Version update complete!${NC}"
echo -e "Next steps:"
echo -e "1. Rebuild the containers with: ${YELLOW}.dockerwrapper/run-playwright-fixed.sh all${NC}"
echo -e "2. If using persistent environment, recreate it with: ${YELLOW}.dockerwrapper/persistent-test-env.sh${NC}"
echo -e "3. Run tests with: ${YELLOW}.dockerwrapper/run-test-direct.sh all${NC}" 