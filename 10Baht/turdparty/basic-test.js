const { chromium } = require("playwright"); (async () => { const browser = await chromium.launch({ headless: true }); const context = await browser.newContext(); const page = await context.newPage(); try { console.log("Testing Home page..."); await page.goto("http://172.18.0.4:3000/"); console.log("Home page loaded, title:", await page.title()); await page.screenshot({ path: "/tmp/homepage.png" }); console.log("Testing VM Status page..."); await page.goto("http://172.18.0.4:3000/vm_status"); console.log("VM Status page loaded"); await page.screenshot({ path: "/tmp/vm_status.png" }); console.log("Testing File Upload page..."); await page.goto("http://172.18.0.4:3000/file_upload"); console.log("File Upload page loaded"); await page.screenshot({ path: "/tmp/file_upload.png" }); console.log("All tests completed successfully"); } catch (error) { console.error("Error:", error.message); } finally { await browser.close(); } })();
