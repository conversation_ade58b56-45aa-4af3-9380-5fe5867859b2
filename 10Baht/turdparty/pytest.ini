[pytest]
markers =
    integration: mark a test as an integration test
    e2e: mark a test as an end-to-end test
    unit: mark a test as a unit test
    slow: mark a test as slow running
    api: mark a test as an API test
    db: mark a test as a database test
    security: mark a test as a security test
    performance: mark a test as a performance test

testpaths = api/tests

python_files = test_*.py
python_classes = Test* *Test *Tests *TestCase
python_functions = test_*

filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::RuntimeWarning

asyncio_mode = auto
