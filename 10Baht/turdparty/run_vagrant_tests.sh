#!/bin/bash

# Script to run all Vagrant VM tests

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Vagrant VM Testing Suite ===${NC}"
echo "Starting test run at $(date)"
echo

# Create logs directory if it doesn't exist
mkdir -p tests/logs

# Run the Python test runner
echo -e "${YELLOW}Running Python test runner...${NC}"
python api/tests/run_vagrant_tests.py

# Store the exit code
EXIT_CODE=$?

# Print summary
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}All tests passed!${NC}"
else
    echo -e "${RED}Some tests failed. Check the logs for details.${NC}"
fi

echo
echo "Test reports are available at:"
echo "  - tests/logs/vagrant_test_report.txt (Human-readable report)"
echo "  - tests/logs/vagrant_test_report.json (JSON report)"
echo

# Exit with the same code as the Python script
exit $EXIT_CODE 