import { test, expect } from '@playwright/test';

test('upload file test', async ({ page }) => {
  console.log('Starting test');
  
  // Go to upload page
  await page.goto('http://localhost:3100/upload');
  console.log('On upload page');
  await page.screenshot({ path: 'test_screenshots/01-upload-page.png' });
  
  // Fill in form
  await page.getByPlaceholder(/description/i).fill('Test upload from Playwright Docker');
  
  // Attach file
  await page.setInputFiles('input[type="file"]', 'test-upload.txt');
  await page.screenshot({ path: 'test_screenshots/02-file-selected.png' });
  
  // Submit form
  await page.getByRole('button', { name: /upload/i }).click();
  
  // Wait for success
  await page.waitForSelector('.ant-message-success, .upload-success', { timeout: 30000 });
  await page.screenshot({ path: 'test_screenshots/03-success.png' });
  
  // Navigate to files page
  await page.goto('http://localhost:3100/files');
  await page.screenshot({ path: 'test_screenshots/04-files-list.png' });
  
  console.log('Test completed');
});
