[tool.poetry]
name = "turdparty"
version = "0.1.0"
description = "TurdParty API and UI"
authors = ["Your Name <<EMAIL>>"]
packages = [{include = "api"}]
package-mode = false

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "0.110.0"
uvicorn = "0.27.1"
pydantic = "2.6.1"
pydantic-settings = "2.2.0"
SQLAlchemy = "2.0.27"
alembic = "1.13.1"
psycopg2-binary = "2.9.9"
python-multipart = "0.0.7"
logging-formatter-anticrlf = "1.2"
httpx = "0.27.0"
PyJWT = "2.8.0"
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
bcrypt = "4.0.1"
pyjwt = "2.10.1"
requests = "2.32.3"
boto3 = "1.37.4"
babel = "2.17.0"
google-cloud-translate = "2.0.1"
asyncpg = "0.30.0"
prometheus-client = "0.17.1"
Flask = "2.3.3"
pyotp = "2.9.0"
qrcode = "7.4.2"
pillow = "10.3.0"
email-validator = "2.1.0"
aiofiles = "^0.7.0"
python-dotenv = "^0.19.0"
minio = "^7.1.0"
paramiko = "^2.7.2"
python-magic = "^0.4.24"
aiohttp = "^3.8.0"
python-dateutil = "^2.8.2"
python-json-logger = "^2.0.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.dev-dependencies]
pytest = "^6.2.5"
pytest-asyncio = "^0.15.1"
pytest-cov = "^2.12.1"
pytest-mock = "^3.6.1"
black = "^21.7b0"
flake8 = "^3.9.2"
mypy = "^0.910"
isort = "^5.9.3"
mkdocs = "^1.2.2"
mkdocs-material = "^7.2.6"