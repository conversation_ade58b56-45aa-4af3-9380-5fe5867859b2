#!/usr/bin/env python3
"""
Script to run API tests with authentication disabled.
"""
import sys
import os
import subprocess
import argparse
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import and enable test mode
from api.core.test_config import test_settings

def main():
    """Run the tests with authentication disabled."""
    parser = argparse.ArgumentParser(description="Run tests with authentication disabled")
    parser.add_argument(
        "--category",
        choices=["unit", "integration", "e2e", "all"],
        default="all",
        help="Category of tests to run"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Generate coverage reports"
    )
    parser.add_argument(
        "test_path",
        nargs="?",
        default=None,
        help="Specific test file or directory to run"
    )
    parser.add_argument(
        "--docker",
        action="store_true",
        help="Run tests in Docker container"
    )
    
    args = parser.parse_args()
    
    logger.info("Enabling test mode to bypass authentication...")
    test_settings.enable_test_mode()
    
    if args.docker:
        return run_in_docker(args)
    else:
        return run_locally(args)

def run_locally(args):
    """Run tests locally."""
    logger.info("Running tests locally...")
    
    # Build command
    cmd = ["pytest", "-v"]
    
    # Add coverage if requested
    if args.coverage:
        cmd.extend(["--cov=api", "--cov-report=term", "--cov-report=html"])
    
    # Add test path or determine based on category
    if args.test_path:
        cmd.append(args.test_path)
    elif args.category != "all":
        if args.category == "unit":
            cmd.extend([
                "api/tests/test_models.py",
                "api/tests/test_repositories.py",
                "api/tests/test_services.py"
            ])
        elif args.category == "integration":
            cmd.extend([
                "api/tests/test_api_endpoints.py",
                "api/tests/test_error_handling.py"
            ])
        elif args.category == "e2e":
            cmd.append("api/tests/test_e2e.py")
    else:
        cmd.append("api/tests")
    
    logger.info(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd)
        return result.returncode
    except Exception as e:
        logger.error(f"Error running tests: {str(e)}")
        return 1

def run_in_docker(args):
    """Run tests in Docker container."""
    logger.info("Running tests in Docker container...")
    
    # Build the command to run inside Docker
    docker_cmd = ["docker", "exec", "TurdParty-container"]
    
    # Add pytest command
    pytest_cmd = ["pytest", "-v"]
    
    # Add coverage if requested
    if args.coverage:
        pytest_cmd.extend(["--cov=api", "--cov-report=term", "--cov-report=html"])
    
    # Add test path or determine based on category
    if args.test_path:
        pytest_cmd.append(args.test_path)
    elif args.category != "all":
        if args.category == "unit":
            pytest_cmd.extend([
                "api/tests/test_models.py",
                "api/tests/test_repositories.py",
                "api/tests/test_services.py"
            ])
        elif args.category == "integration":
            pytest_cmd.extend([
                "api/tests/test_api_endpoints.py",
                "api/tests/test_error_handling.py"
            ])
        elif args.category == "e2e":
            pytest_cmd.append("api/tests/test_e2e.py")
    else:
        pytest_cmd.append("api/tests")
    
    # Combine commands
    cmd = docker_cmd + pytest_cmd
    
    logger.info(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd)
        return result.returncode
    except Exception as e:
        logger.error(f"Error running tests in Docker: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 