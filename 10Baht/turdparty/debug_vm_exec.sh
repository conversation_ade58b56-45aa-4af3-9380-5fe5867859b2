#!/bin/bash
# Debug wrapper for vm_exec_test.py

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Python exists
echo -e "${YELLOW}Checking if Python exists...${NC}"
PYTHON_CMD=""
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    echo -e "${GREEN}Python 3 is available at $(which python3)${NC}"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    echo -e "${GREEN}Python is available at $(which python)${NC}"
else
    echo -e "${RED}Python is not available. Please install Python.${NC}"
    exit 1
fi

# Check if the script exists
if [ ! -f "./vm_exec_test.py" ]; then
    echo -e "${RED}vm_exec_test.py not found in current directory${NC}"
    exit 1
fi

echo -e "${YELLOW}Running vm_exec_test.py in debug mode...${NC}"
echo -e "${BLUE}Command: $PYTHON_CMD ./vm_exec_test.py $@${NC}"
echo -e "${YELLOW}===================== START OUTPUT =====================${NC}"

# Run with the python interpreter explicitly
$PYTHON_CMD -u ./vm_exec_test.py "$@"
EXIT_CODE=$?

echo -e "${YELLOW}====================== END OUTPUT ======================${NC}"
echo -e "${YELLOW}Exit code: ${EXIT_CODE}${NC}"

exit $EXIT_CODE 