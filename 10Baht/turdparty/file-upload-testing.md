# File Upload Testing Framework

This document outlines the file upload testing framework for the TurdParty project, including the current status, test approaches, and recommendations for usage.

## Overview

The file upload testing framework provides a comprehensive solution for testing file upload functionality in the application across different environments:

- Direct browser testing with Playwright
- Cross-container testing with appropriate network configuration
- Fallback to multiple upload endpoints and mechanisms
- Detailed diagnostic information and error reporting

## Components

The framework consists of several key components:

### 1. Enhanced Upload Test (enhanced-upload-test.js)

The main test script that:
- Tests multiple target environments (local frontend, docker frontend, container network, API)
- Provides detailed diagnostic information
- Captures screenshots at each stage of the test
- Includes robust error handling and reporting
- Collects page HTML on failures for debugging

### 2. Test Runner (scripts/run-enhanced-upload-test.sh)

A shell script that:
- Automatically detects the execution environment (host system or container)
- Finds and configures container networking for cross-container communication
- Sets up appropriate environment variables for tests
- <PERSON>les file copying between containers if needed
- Consolidates and displays test results

### 3. Comprehensive Test Suite (scripts/run-complete-upload-test.sh)

A complete testing solution that:
- Runs all tests in sequence (network diagnostics, curl tests, frontend checks, Playwright tests)
- Generates detailed test reports and summaries
- Collects all test artifacts in a timestamped directory
- Provides a clear final verdict on the upload functionality
- Generates a Markdown summary report with next steps

### 4. Diagnostic Tools

Additional scripts for debugging issues:
- `curl-upload-test.sh`: Tests uploads using curl directly
- `debug-container-network.sh`: Diagnoses container network issues
- `simple-frontend-check.sh`: Verifies frontend accessibility

## Usage

### Running the Complete Test Suite

For a comprehensive test of all file upload functionality:

```bash
./scripts/run-complete-upload-test.sh
```

This will:
1. Check the system environment and detect containers
2. Run network diagnostics tests
3. Perform curl upload tests
4. Verify frontend functionality
5. Run enhanced Playwright tests
6. Generate a detailed report in the `upload_test_results_[timestamp]` directory

### Running Individual Tests

You can also run individual components for more targeted testing:

```bash
# Run just the enhanced Playwright tests
./scripts/run-enhanced-upload-test.sh

# Test direct curl uploads
./scripts/curl-upload-test.sh

# Check frontend accessibility
./scripts/simple-frontend-check.sh

# Debug container network issues
./scripts/debug-container-network.sh
```

## Configuration

The framework can be configured through environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| FRONTEND_URL | URL for the frontend | http://localhost:3100 |
| UPLOAD_URL | URL for the upload endpoint | http://localhost:3100/upload |
| FILES_URL | URL for the files list | http://localhost:3100/files |
| API_URL | URL for the API | http://localhost:3050 |
| CONTAINER_FRONTEND_URL | URL for accessing frontend from containers | (auto-detected) |
| TEST_FILE_PATH | Path to the test file | ./test-upload.txt |
| SCREENSHOT_DIR | Directory for screenshots | ./test_screenshots |
| HEADLESS | Run in headless mode | true |
| NAVIGATION_TIMEOUT | Page navigation timeout (ms) | 30000 |
| UPLOAD_TIMEOUT | File upload timeout (ms) | 45000 |
| TEST_RETRIES | Number of retries for failed tests | 2 |

## Test Artifacts and Reports

The comprehensive test suite generates the following artifacts:

- **Test Results Directory**: `upload_test_results_[timestamp]/`
- **Screenshots**: `[test_results_dir]/screenshots/`
- **Container Information**: `[test_results_dir]/frontend_container_info.json`, `[test_results_dir]/api_container_info.json`
- **Test Logs**: Individual logs for each test component
- **Summary Report**: `[test_results_dir]/summary.md` - Markdown report with test results and next steps
- **Final Result**: `[test_results_dir]/RESULT` - SUCCESS or FAILURE

## Current Status and Findings

Based on our testing, we have identified several issues with the file upload functionality:

1. **API Endpoint Inaccessibility**: The API endpoints for file uploads are either not accessible or not fully implemented.

2. **Frontend Upload Issues**: The frontend upload endpoint returns errors, suggesting incomplete implementation.

3. **Container Network Communication**: While containers can communicate with each other, the upload functionality is not properly configured.

Refer to `upload-testing-findings.md` for a detailed analysis of these issues.

## Recommendations

1. **Complete API Implementation**: Implement the missing API endpoints for file upload.

2. **Configure Frontend Upload**: Ensure the frontend upload endpoint is properly configured.

3. **Use Test Scripts for Verification**: After implementation, use the testing framework to verify functionality:
   ```bash
   # Run the complete test suite
   ./scripts/run-complete-upload-test.sh
   
   # Or run individual tests
   ./scripts/curl-upload-test.sh
   ./scripts/run-enhanced-upload-test.sh
   ```

4. **Monitor Test Results**: Check the test results directory for comprehensive test information and next steps.

## Troubleshooting

If tests are failing, check:

1. **Container Network**: Ensure containers can communicate with each other
   ```bash
   ./scripts/debug-container-network.sh
   ```

2. **Endpoint Accessibility**: Verify endpoints are accessible
   ```bash
   curl http://localhost:3100/upload
   curl http://localhost:3050/api/v1/files
   ```

3. **Screenshots and Logs**: Check the test_screenshots directory for visual indications of where tests are failing

4. **Docker Logs**: Check container logs for backend errors
   ```bash
   docker logs $(docker ps | grep turdparty_test_frontend | awk '{print $1}')
   docker logs $(docker ps | grep turdparty_test_api | awk '{print $1}')
   ```

## Next Steps

The immediate next steps are:

1. Complete the implementation of the file upload API endpoints
2. Fix frontend upload route configuration
3. Run the comprehensive test suite to verify functionality
4. Document the final working endpoints for future reference 