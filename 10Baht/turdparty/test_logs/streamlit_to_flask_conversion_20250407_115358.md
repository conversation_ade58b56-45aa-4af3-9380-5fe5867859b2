# Streamlit to Flask Conversion Log
Generated on Mon  7 Apr 11:53:58 CEST 2025

## Files requiring conversion from Streamlit to Flask

Total files to convert: 13

### By directory

#### ./ui/

- **./ui/app.py**
  - Streamlit API calls: ~21
  - Components used:
    - Buttons
    - Sidebar
  - Sample usage:
```python
    16:st.set_page_config(
    48:            st.success("Item added successfully!")
    49:            st.experimental_rerun()
    52:            st.error("Failed to add item")
    57:        st.error(error_msg)
    # ... and 16 more instances
```

- **./ui/components/auth_component.py**
  - Streamlit API calls: ~56
  - Components used:
    - Buttons
    - Forms
    - Input fields
    - Text display
    - Session state (needs Flask sessions)
  - Sample usage:
```python
    42:        with st.form(key=f"{self.key}_login_form"):
    43:            st.subheader("Login")
    44:            email = st.text_input("Email", key=f"{self.key}_email")
    45:            password = st.text_input("Password", type="password", key=f"{self.key}_password")
    47:            submit = st.form_submit_button("Login")
    # ... and 51 more instances
```

- **./ui/components/base_component.py**
  - Streamlit API calls: ~2
  - Components used:
  - Sample usage:
```python
    49:                st.subheader(self.title)
    53:            st.error(f"Error rendering component: {str(e)}")
```

- **./ui/components/card_component.py**
  - Streamlit API calls: ~7
  - Components used:
    - Buttons
    - Text display
  - Sample usage:
```python
    53:        st.markdown(card_style, unsafe_allow_html=True)
    62:        st.markdown(card_html, unsafe_allow_html=True)
    114:            card_container = st.container()
    118:                st.markdown(
    134:                st.markdown(
    # ... and 2 more instances
```

- **./ui/components/file_upload_component.py**
  - Streamlit API calls: ~38
  - Components used:
    - Forms
    - Input fields
    - Selection widgets
    - Text display
    - File uploaders
  - Sample usage:
```python
    51:            st.subheader(self.title)
    54:            tab1, tab2, tab3 = st.tabs(["Upload File", "Upload Folder", "Select Template"])
    67:            st.error(f"Error displaying file upload component: {str(e)}")
    71:        st.write("Upload a single file to the system.")
    73:        with st.form(key=f"{self.key}_file_upload_form"):
    # ... and 33 more instances
```

- **./ui/components/form_component.py**
  - Streamlit API calls: ~21
  - Components used:
    - Forms
    - Input fields
    - Selection widgets
    - Text display
  - Sample usage:
```python
    40:        with st.form(key=f"form_{self.key}"):
    52:                    value = st.text_input(
    59:                    value = st.text_area(
    69:                    value = st.number_input(
    79:                    value = st.selectbox(
    # ... and 16 more instances
```

- **./ui/components/mfa_component.py**
  - Streamlit API calls: ~38
  - Components used:
    - Buttons
    - Input fields
    - Text display
    - Session state (needs Flask sessions)
  - Sample usage:
```python
    29:        st.title("Set Up MFA")
    31:        if "mfa_setup" not in st.session_state:
    32:            if st.button("Generate MFA Secret"):
    36:                        st.session_state.mfa_setup = response
    39:                    st.error(f"Error setting up MFA: {str(e)}")
    # ... and 33 more instances
```

- **./ui/components/profile_component.py**
  - Streamlit API calls: ~31
  - Components used:
    - Buttons
    - Forms
    - Input fields
    - Text display
  - Sample usage:
```python
    52:        with st.expander("User Profile", expanded=True):
    54:                st.warning("User data could not be loaded.")
    55:                if st.button("Try Again", key=f"{self.key}_retry"):
    57:                    st.experimental_rerun()
    61:            st.subheader("User Information")
    # ... and 26 more instances
```

- **./ui/components/status_component.py**
  - Streamlit API calls: ~16
  - Components used:
    - Buttons
    - Text display
    - Data tables
  - Sample usage:
```python
    58:            st.info("No endpoints configured for monitoring.")
    77:            st.success(f"All services operational ({services_up}/{total_services})")
    79:            st.warning(f"Partial outage - {services_up}/{total_services} services operational")
    81:            st.error(f"Major outage - All services down (0/{total_services})")
    100:            st.dataframe(status_data)
    # ... and 11 more instances
```

- **./ui/components/system_monitor_component.py**
  - Streamlit API calls: ~48
  - Components used:
    - Buttons
    - Text display
    - Charts/visualizations
    - Session state (needs Flask sessions)
  - Sample usage:
```python
    43:        if f"{self.key}_cpu_history" not in st.session_state:
    44:            st.session_state[f"{self.key}_cpu_history"] = []
    46:        if f"{self.key}_memory_history" not in st.session_state:
    47:            st.session_state[f"{self.key}_memory_history"] = []
    49:        if f"{self.key}_disk_history" not in st.session_state:
    # ... and 43 more instances
```

- **./ui/components/vm_injection_component.py**
  - Streamlit API calls: ~34
  - Components used:
    - Buttons
    - Forms
    - Selection widgets
    - Text display
  - Sample usage:
```python
    46:            st.subheader(self.title)
    49:            tab1, tab2 = st.tabs(["New Injection", "Injection Status"])
    59:            st.error(f"Error displaying VM injection component: {str(e)}")
    63:        st.write("Create a new VM injection to deploy files to a VM.")
    72:            st.warning("No VMs available. Please create a VM first.")
    # ... and 29 more instances
```

- **./ui/pages/file_management.py**
  - Streamlit API calls: ~4
  - Components used:
    - Text display
  - Sample usage:
```python
    32:    st.title("File Management")
    34:    st.write("""
    50:    st.header("Recently Uploaded Files")
    92:    st.header("Selected Templates")
```

- **./ui/pages/vm_injection_page.py**
  - Streamlit API calls: ~3
  - Components used:
    - Text display
  - Sample usage:
```python
    24:    st.title("VM Injection")
    26:    st.write("""
    61:    st.header("Recent Injections")
```


#### ./ui/components/

- **./ui/components/auth_component.py**
  - Streamlit API calls: ~56
  - Components used:
    - Buttons
    - Forms
    - Input fields
    - Text display
    - Session state (needs Flask sessions)
  - Sample usage:
```python
    42:        with st.form(key=f"{self.key}_login_form"):
    43:            st.subheader("Login")
    44:            email = st.text_input("Email", key=f"{self.key}_email")
    45:            password = st.text_input("Password", type="password", key=f"{self.key}_password")
    47:            submit = st.form_submit_button("Login")
    # ... and 51 more instances
```

- **./ui/components/base_component.py**
  - Streamlit API calls: ~2
  - Components used:
  - Sample usage:
```python
    49:                st.subheader(self.title)
    53:            st.error(f"Error rendering component: {str(e)}")
```

- **./ui/components/card_component.py**
  - Streamlit API calls: ~7
  - Components used:
    - Buttons
    - Text display
  - Sample usage:
```python
    53:        st.markdown(card_style, unsafe_allow_html=True)
    62:        st.markdown(card_html, unsafe_allow_html=True)
    114:            card_container = st.container()
    118:                st.markdown(
    134:                st.markdown(
    # ... and 2 more instances
```

- **./ui/components/file_upload_component.py**
  - Streamlit API calls: ~38
  - Components used:
    - Forms
    - Input fields
    - Selection widgets
    - Text display
    - File uploaders
  - Sample usage:
```python
    51:            st.subheader(self.title)
    54:            tab1, tab2, tab3 = st.tabs(["Upload File", "Upload Folder", "Select Template"])
    67:            st.error(f"Error displaying file upload component: {str(e)}")
    71:        st.write("Upload a single file to the system.")
    73:        with st.form(key=f"{self.key}_file_upload_form"):
    # ... and 33 more instances
```

- **./ui/components/form_component.py**
  - Streamlit API calls: ~21
  - Components used:
    - Forms
    - Input fields
    - Selection widgets
    - Text display
  - Sample usage:
```python
    40:        with st.form(key=f"form_{self.key}"):
    52:                    value = st.text_input(
    59:                    value = st.text_area(
    69:                    value = st.number_input(
    79:                    value = st.selectbox(
    # ... and 16 more instances
```

- **./ui/components/mfa_component.py**
  - Streamlit API calls: ~38
  - Components used:
    - Buttons
    - Input fields
    - Text display
    - Session state (needs Flask sessions)
  - Sample usage:
```python
    29:        st.title("Set Up MFA")
    31:        if "mfa_setup" not in st.session_state:
    32:            if st.button("Generate MFA Secret"):
    36:                        st.session_state.mfa_setup = response
    39:                    st.error(f"Error setting up MFA: {str(e)}")
    # ... and 33 more instances
```

- **./ui/components/profile_component.py**
  - Streamlit API calls: ~31
  - Components used:
    - Buttons
    - Forms
    - Input fields
    - Text display
  - Sample usage:
```python
    52:        with st.expander("User Profile", expanded=True):
    54:                st.warning("User data could not be loaded.")
    55:                if st.button("Try Again", key=f"{self.key}_retry"):
    57:                    st.experimental_rerun()
    61:            st.subheader("User Information")
    # ... and 26 more instances
```

- **./ui/components/status_component.py**
  - Streamlit API calls: ~16
  - Components used:
    - Buttons
    - Text display
    - Data tables
  - Sample usage:
```python
    58:            st.info("No endpoints configured for monitoring.")
    77:            st.success(f"All services operational ({services_up}/{total_services})")
    79:            st.warning(f"Partial outage - {services_up}/{total_services} services operational")
    81:            st.error(f"Major outage - All services down (0/{total_services})")
    100:            st.dataframe(status_data)
    # ... and 11 more instances
```

- **./ui/components/system_monitor_component.py**
  - Streamlit API calls: ~48
  - Components used:
    - Buttons
    - Text display
    - Charts/visualizations
    - Session state (needs Flask sessions)
  - Sample usage:
```python
    43:        if f"{self.key}_cpu_history" not in st.session_state:
    44:            st.session_state[f"{self.key}_cpu_history"] = []
    46:        if f"{self.key}_memory_history" not in st.session_state:
    47:            st.session_state[f"{self.key}_memory_history"] = []
    49:        if f"{self.key}_disk_history" not in st.session_state:
    # ... and 43 more instances
```

- **./ui/components/vm_injection_component.py**
  - Streamlit API calls: ~34
  - Components used:
    - Buttons
    - Forms
    - Selection widgets
    - Text display
  - Sample usage:
```python
    46:            st.subheader(self.title)
    49:            tab1, tab2 = st.tabs(["New Injection", "Injection Status"])
    59:            st.error(f"Error displaying VM injection component: {str(e)}")
    63:        st.write("Create a new VM injection to deploy files to a VM.")
    72:            st.warning("No VMs available. Please create a VM first.")
    # ... and 29 more instances
```


#### ./ui/pages/

- **./ui/pages/file_management.py**
  - Streamlit API calls: ~4
  - Components used:
    - Text display
  - Sample usage:
```python
    32:    st.title("File Management")
    34:    st.write("""
    50:    st.header("Recently Uploaded Files")
    92:    st.header("Selected Templates")
```

- **./ui/pages/vm_injection_page.py**
  - Streamlit API calls: ~3
  - Components used:
    - Text display
  - Sample usage:
```python
    24:    st.title("VM Injection")
    26:    st.write("""
    61:    st.header("Recent Injections")
```


## Conversion Guide

### Common Streamlit to Flask Mappings

| Streamlit Component | Flask/HTML Equivalent |
|-------------------|------------------------|
| st.button | `<button>` or `<input type="submit">` |
| st.text_input | `<input type="text">` |
| st.number_input | `<input type="number">` |
| st.selectbox | `<select>` dropdown |
| st.multiselect | `<select multiple>` |
| st.checkbox | `<input type="checkbox">` |
| st.radio | `<input type="radio">` |
| st.form | `<form>` element |
| st.sidebar | Left navigation or sidebar with CSS |
| st.write/markdown | Rendered with Jinja2 templates |
| st.dataframe/table | HTML tables or DataTables.js |
| st.file_uploader | `<input type="file">` |
| st.session_state | Flask session |
| st.cache | Flask-Caching extension |

