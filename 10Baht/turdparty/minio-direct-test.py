#!/usr/bin/env python3
"""
Direct MinIO test script for validating file upload functionality.
This script bypasses the API and directly tests the MinIO connection and file operations.
"""
import os
import uuid
import hashlib
from datetime import datetime
from io import BytesIO
from minio import Minio

# Configuration
MINIO_HOST = 'minio-test:9000'
MINIO_ACCESS_KEY = 'minioadmin'
MINIO_SECRET_KEY = 'minioadmin'
MINIO_SECURE = False
BUCKET_NAME = 'turdparty-uploads'

def main():
    """Run the MinIO test script."""
    print("=== Direct MinIO File Upload Test ===")
    
    # Create MinIO client
    print(f"Creating MinIO client for {MINIO_HOST}")
    client = Minio(
        MINIO_HOST,
        access_key=MINIO_ACCESS_KEY,
        secret_key=MINIO_SECRET_KEY,
        secure=MINIO_SECURE
    )
    
    # Verify connection
    try:
        buckets = client.list_buckets()
        print(f"Connection successful! Found {len(buckets)} buckets:")
        for bucket in buckets:
            print(f"  - {bucket.name}")
    except Exception as e:
        print(f"Error connecting to MinIO: {str(e)}")
        return False
    
    # Ensure bucket exists
    try:
        if not client.bucket_exists(BUCKET_NAME):
            print(f"Creating bucket: {BUCKET_NAME}")
            client.make_bucket(BUCKET_NAME)
        else:
            print(f"Bucket already exists: {BUCKET_NAME}")
    except Exception as e:
        print(f"Error ensuring bucket exists: {str(e)}")
        return False
    
    # Create test file
    file_id = str(uuid.uuid4())
    filename = "test-file.txt"
    content = f"Test file content\nCreated at: {datetime.now().isoformat()}\nRandom ID: {file_id}"
    
    print(f"Created test file with ID {file_id}")
    file_bytes = content.encode('utf-8')
    file_size = len(file_bytes)
    content_type = "text/plain"
    
    # Upload file
    try:
        object_key = f"{file_id}/{filename}"
        print(f"Uploading file {object_key} to bucket {BUCKET_NAME}")
        
        client.put_object(
            bucket_name=BUCKET_NAME,
            object_name=object_key,
            data=BytesIO(file_bytes),
            length=file_size,
            content_type=content_type
        )
        print(f"Upload successful!")
    except Exception as e:
        print(f"Error uploading file: {str(e)}")
        return False
    
    # Verify upload
    try:
        # List objects with prefix to find our file
        objects = list(client.list_objects(BUCKET_NAME, prefix=f"{file_id}/"))
        if not objects:
            print(f"Error: File not found after upload")
            return False
        
        obj = objects[0]
        print(f"File found in bucket: {obj.object_name}, size: {obj.size} bytes")
        
        # Download the file
        print(f"Downloading file...")
        response = client.get_object(BUCKET_NAME, obj.object_name)
        downloaded_content = response.read().decode('utf-8')
        response.close()
        
        # Verify content
        if downloaded_content == content:
            print("✅ Content verification successful!")
        else:
            print("❌ Content verification failed!")
            print(f"Expected: {content}")
            print(f"Got: {downloaded_content}")
            return False
        
        # Create a download URL
        download_url = f"/api/v1/file_upload/download/{file_id}"
        print(f"Download URL would be: {download_url}")
        
        # Output file metadata as it would appear in the API
        file_hash = hashlib.md5(file_bytes).hexdigest()
        now = datetime.utcnow().isoformat()
        
        metadata = {
            "id": file_id,
            "filename": filename,
            "file_size": file_size,
            "content_type": content_type,
            "file_hash": file_hash,
            "description": "Direct MinIO test upload",
            "created_at": now,
            "updated_at": now,
            "download_url": download_url
        }
        
        print("\nFile metadata:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
        
        # Delete the test file
        print(f"\nDeleting test file {object_key}")
        client.remove_object(BUCKET_NAME, object_key)
        print("Cleanup successful!")
        
        return True
    except Exception as e:
        print(f"Error verifying upload: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    print("\n=== Test Result ===")
    if success:
        print("✅ All tests passed successfully!")
        exit(0)
    else:
        print("❌ Test failed!")
        exit(1) 