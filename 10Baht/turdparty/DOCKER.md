# Docker Images for TurdParty

This repository contains Docker images for the TurdParty application.

## Available Images

The following Docker images have been created and locked at version v1.0.0:

- `dockerwrapper_api:v1.0.0` - The FastAPI backend service
- `dockerwrapper_playwright:v1.0.0` - The Playwright testing service

## Using the Docker Images

### Development Environment

To run the development environment:

```bash
docker-compose -f .dockerwrapper/docker-compose.playwright.yml up -d
```

### Production Environment

For production, use the production compose file which uses the locked image versions:

```bash
docker-compose -f .dockerwrapper/docker-compose.production.yml up -d
```

## Pushing to a Remote Registry

The repository includes a script to push the Docker images to a remote registry:

```bash
# Push to Docker Hub (replace yourusername with your Docker Hub username)
./push-docker-images.sh docker.io/yourusername

# Push to GitHub Container Registry
./push-docker-images.sh ghcr.io/yourusername
```

## Docker Image Lock

The Docker images are locked at version v1.0.0 to ensure consistency across environments. The image specifications are recorded in the `docker-lock.json` file.

## Rebuilding

If you need to rebuild the images, use:

```bash
docker-compose -f .dockerwrapper/docker-compose.playwright.yml build
docker tag dockerwrapper_api:latest dockerwrapper_api:v1.0.0
docker tag dockerwrapper_playwright:latest dockerwrapper_playwright:v1.0.0
```

## Troubleshooting

If you encounter issues with the Docker containers:

1. Check the container logs: `docker-compose -f .dockerwrapper/docker-compose.playwright.yml logs`
2. Ensure all containers are running: `docker-compose -f .dockerwrapper/docker-compose.playwright.yml ps`
3. Restart the containers: `docker-compose -f .dockerwrapper/docker-compose.playwright.yml restart` 