const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

// Configuration
const config = {
  apiUrl: 'http://localhost:3050',
  apiEndpoints: {
    health: '/api/v1/health',
    fileUpload: '/api/v1/file_upload/',
    folderUpload: '/api/v1/file_upload/folder/',
    fileList: '/api/v1/file_upload/',
    download: '/api/v1/file_upload/download/'
  },
  testFiles: {
    singleFile: '/tmp/api-test-file.txt',
    folder: '/tmp/api-test-folder'
  },
  debug: true
};

// Create console logger with colors
const logger = {
  info: (msg) => console.log('\x1b[36m%s\x1b[0m', msg),
  success: (msg) => console.log('\x1b[32m%s\x1b[0m', msg),
  error: (msg) => console.log('\x1b[31m%s\x1b[0m', msg),
  warning: (msg) => console.log('\x1b[33m%s\x1b[0m', msg),
  debug: (msg) => config.debug && console.log('\x1b[90m%s\x1b[0m', msg)
};

// Setup test files
async function setupTestFiles() {
  logger.info('Setting up test files...');
  
  // Create single file
  fs.writeFileSync(config.testFiles.singleFile, 'This is a test file for API upload testing.');
  logger.success(`Created test file: ${config.testFiles.singleFile}`);
  
  // Create folder with multiple files
  if (!fs.existsSync(config.testFiles.folder)) {
    fs.mkdirSync(config.testFiles.folder, { recursive: true });
  }
  
  fs.writeFileSync(path.join(config.testFiles.folder, 'file1.txt'), 'File 1 content');
  fs.writeFileSync(path.join(config.testFiles.folder, 'file2.txt'), 'File 2 content');
  
  // Create a subfolder with a file
  const subFolder = path.join(config.testFiles.folder, 'subdir');
  if (!fs.existsSync(subFolder)) {
    fs.mkdirSync(subFolder, { recursive: true });
  }
  fs.writeFileSync(path.join(subFolder, 'file3.txt'), 'File 3 content in subfolder');
  
  logger.success(`Created test folder with files at: ${config.testFiles.folder}`);
}

// Clean up test files
function cleanupTestFiles() {
  logger.info('Cleaning up test files...');
  if (fs.existsSync(config.testFiles.singleFile)) {
    fs.unlinkSync(config.testFiles.singleFile);
  }
  
  if (fs.existsSync(config.testFiles.folder)) {
    fs.rmSync(config.testFiles.folder, { recursive: true, force: true });
  }
  
  logger.success('Test files removed');
}

// Function to get auth token
async function getAuthToken() {
  try {
    logger.info('Attempting to get auth token...');
    const response = await axios.post(`${config.apiUrl}/api/v1/auth/test-token`);
    
    if (response.data && response.data.access_token) {
      logger.success('Auth token received');
      return response.data.access_token;
    } else {
      logger.warning('No auth token in response');
      return null;
    }
  } catch (error) {
    logger.error(`Error getting auth token: ${error.message}`);
    logger.debug(error.response ? JSON.stringify(error.response.data) : 'No response data');
    return null;
  }
}

// Test health endpoint
async function testHealthEndpoint() {
  try {
    logger.info('Testing health endpoint...');
    const response = await axios.get(`${config.apiUrl}${config.apiEndpoints.health}`);
    
    if (response.status === 200) {
      logger.success('Health endpoint is working');
      return true;
    } else {
      logger.error(`Health endpoint returned status: ${response.status}`);
      return false;
    }
  } catch (error) {
    logger.error(`Health endpoint error: ${error.message}`);
    return false;
  }
}

// Test file upload endpoint
async function testFileUpload(token) {
  try {
    logger.info('Testing file upload endpoint...');
    
    const formData = new FormData();
    formData.append('file', fs.createReadStream(config.testFiles.singleFile));
    formData.append('description', 'API test upload');
    
    const headers = {
      ...formData.getHeaders()
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await axios.post(
      `${config.apiUrl}${config.apiEndpoints.fileUpload}`,
      formData,
      { headers }
    );
    
    if (response.status === 200 || response.status === 201) {
      logger.success('File upload successful');
      logger.debug(`Response: ${JSON.stringify(response.data)}`);
      return {
        success: true,
        fileId: response.data.id
      };
    } else {
      logger.error(`File upload returned status: ${response.status}`);
      return { success: false };
    }
  } catch (error) {
    logger.error(`File upload error: ${error.message}`);
    logger.debug(error.response ? JSON.stringify(error.response.data) : 'No response data');
    return { success: false };
  }
}

// Test folder upload endpoint
async function testFolderUpload(token) {
  try {
    logger.info('Testing folder upload endpoint...');
    
    const formData = new FormData();
    
    // Add files from the folder
    const files = [
      { path: path.join(config.testFiles.folder, 'file1.txt'), name: 'file1.txt' },
      { path: path.join(config.testFiles.folder, 'file2.txt'), name: 'file2.txt' },
      { path: path.join(config.testFiles.folder, 'subdir/file3.txt'), name: 'subdir/file3.txt' }
    ];
    
    files.forEach(file => {
      formData.append('files', fs.createReadStream(file.path));
      formData.append('paths', file.name);
    });
    
    formData.append('description', 'API test folder upload');
    
    const headers = {
      ...formData.getHeaders()
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await axios.post(
      `${config.apiUrl}${config.apiEndpoints.folderUpload}`,
      formData,
      { headers }
    );
    
    if (response.status === 200 || response.status === 201) {
      logger.success('Folder upload successful');
      logger.debug(`Response: ${JSON.stringify(response.data)}`);
      return { success: true };
    } else {
      logger.error(`Folder upload returned status: ${response.status}`);
      return { success: false };
    }
  } catch (error) {
    logger.error(`Folder upload error: ${error.message}`);
    logger.debug(error.response ? JSON.stringify(error.response.data) : 'No response data');
    return { success: false };
  }
}

// Test file list endpoint
async function testFileList(token) {
  try {
    logger.info('Testing file list endpoint...');
    
    const headers = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await axios.get(
      `${config.apiUrl}${config.apiEndpoints.fileList}`,
      { headers }
    );
    
    if (response.status === 200) {
      logger.success('File list retrieved successfully');
      logger.info(`Found ${response.data.items ? response.data.items.length : 0} files`);
      return { success: true };
    } else {
      logger.error(`File list returned status: ${response.status}`);
      return { success: false };
    }
  } catch (error) {
    logger.error(`File list error: ${error.message}`);
    logger.debug(error.response ? JSON.stringify(error.response.data) : 'No response data');
    return { success: false };
  }
}

// Test file download
async function testFileDownload(fileId, token) {
  try {
    if (!fileId) {
      logger.warning('No file ID provided for download test');
      return { success: false };
    }
    
    logger.info(`Testing file download for ID: ${fileId}`);
    
    const headers = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await axios.get(
      `${config.apiUrl}${config.apiEndpoints.download}${fileId}`,
      { 
        headers,
        responseType: 'stream'
      }
    );
    
    if (response.status === 200) {
      const downloadPath = '/tmp/downloaded-test-file.txt';
      const writer = fs.createWriteStream(downloadPath);
      
      response.data.pipe(writer);
      
      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          logger.success(`File downloaded successfully to ${downloadPath}`);
          resolve({ success: true });
        });
        
        writer.on('error', (err) => {
          logger.error(`Error writing downloaded file: ${err.message}`);
          reject({ success: false });
        });
      });
    } else {
      logger.error(`File download returned status: ${response.status}`);
      return { success: false };
    }
  } catch (error) {
    logger.error(`File download error: ${error.message}`);
    logger.debug(error.response ? JSON.stringify(error.response.data) : 'No response data');
    return { success: false };
  }
}

// Run all tests
async function runTests() {
  logger.info('==================================');
  logger.info('  File Upload API Tests');
  logger.info('==================================');
  
  let results = {
    health: false,
    auth: false,
    upload: false,
    folderUpload: false,
    list: false,
    download: false
  };
  
  let fileId = null;
  let token = null;
  
  try {
    // Setup test files
    await setupTestFiles();
    
    // Test health endpoint
    results.health = await testHealthEndpoint();
    
    // Get auth token
    token = await getAuthToken();
    results.auth = !!token;
    
    // Test file upload
    const uploadResult = await testFileUpload(token);
    results.upload = uploadResult.success;
    fileId = uploadResult.fileId;
    
    // Test folder upload
    results.folderUpload = (await testFolderUpload(token)).success;
    
    // Test file list
    results.list = (await testFileList(token)).success;
    
    // Test file download (if we have a file ID)
    if (fileId) {
      results.download = (await testFileDownload(fileId, token)).success;
    } else {
      logger.warning('Skipping download test as no file ID was returned from upload');
    }
    
    // Report results
    logger.info('\n==================================');
    logger.info('  Test Results');
    logger.info('==================================');
    logger.info(`Health Endpoint: ${results.health ? '✅ PASS' : '❌ FAIL'}`);
    logger.info(`Authentication: ${results.auth ? '✅ PASS' : '❌ FAIL'}`);
    logger.info(`File Upload: ${results.upload ? '✅ PASS' : '❌ FAIL'}`);
    logger.info(`Folder Upload: ${results.folderUpload ? '✅ PASS' : '❌ FAIL'}`);
    logger.info(`File List: ${results.list ? '✅ PASS' : '❌ FAIL'}`);
    logger.info(`File Download: ${results.download ? '✅ PASS' : '❌ FAIL'}`);
    
    const allPassed = Object.values(results).every(Boolean);
    if (allPassed) {
      logger.success('\n✅ ALL TESTS PASSED!');
    } else {
      logger.error('\n❌ SOME TESTS FAILED');
    }
    
  } catch (error) {
    logger.error(`Unexpected error: ${error.message}`);
    logger.debug(error.stack);
  } finally {
    // Clean up test files
    cleanupTestFiles();
  }
}

// Run the tests
runTests(); 