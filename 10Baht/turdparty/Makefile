
.PHONY: translate update-translations clean-translations

translate:
	python scripts/translate_docs.py

update-translations:
	@echo "Updating translations from source files..."
	python scripts/translate_docs.py

clean-translations:
	@echo "Cleaning translation artifacts..."
	rm -rf lang/temp
	@echo "Translation artifacts cleaned"

help:
	@echo "Translation management commands:"
	@echo "  make translate          - Generate translations for all documentation"
	@echo "  make update-translations - Update translations from source files"
	@echo "  make clean-translations - Clean translation artifacts"
