#!/usr/bin/env python3
"""
Test script for diagnosing Vagrant VM template loading issues
and connecting to the Vagrant gRPC service on port 40000
"""
import requests
import grpc
import sys
import json
import os
import time
import subprocess
from typing import Dict, List, Optional, Any, Tuple


# Configuration
GRPC_PORT = 40000
GRPC_HOST = "localhost"
API_BASE = "http://localhost:3050/api/v1"


def test_grpc_connection() -> bool:
    """Test basic connectivity to the Vagrant gRPC service"""
    try:
        # Create an insecure channel to the gRPC server
        channel = grpc.insecure_channel(f"{GRPC_HOST}:{GRPC_PORT}")
        
        # Try connecting with a timeout
        try:
            # Check if channel is ready (with timeout)
            ready = grpc.channel_ready_future(channel).result(timeout=5)
            print(f"Successfully connected to gRPC server on port {GRPC_PORT}")
            return True
        except grpc.FutureTimeoutError:
            print(f"Timeout connecting to gRPC server on port {GRPC_PORT}")
            return False
    except Exception as e:
        print(f"Error connecting to gRPC server: {e}")
        return False


def get_auth_token() -> Optional[str]:
    """Get authentication token for API access"""
    print("Getting auth token...")
    try:
        resp = requests.post(f"{API_BASE}/auth/test-token")
        if resp.status_code == 200:
            token = resp.json().get("access_token")
            if token:
                print("Token obtained successfully")
                return token
        
        print(f"Failed to get token. Status code: {resp.status_code}")
        print(f"Response: {resp.text}")
        return None
    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None


def fetch_vm_templates_api(token: str) -> Optional[List[Dict[str, Any]]]:
    """Fetch VM templates from the API"""
    print("\nFetching VM templates from API...")
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    try:
        resp = requests.get(f"{API_BASE}/vagrant_vm/templates", headers=headers)
        print(f"Status code: {resp.status_code}")
        
        if resp.status_code == 200:
            templates = resp.json()
            print(f"Found {len(templates)} templates")
            return templates
        else:
            print(f"Failed to fetch templates from API: {resp.text}")
            return None
    except Exception as e:
        print(f"Error fetching VM templates from API: {e}")
        return None


def fetch_vm_templates_direct() -> Optional[List[Dict[str, Any]]]:
    """Directly fetch available VM templates using Vagrant command"""
    print("\nFetching VM templates directly using Vagrant...")
    try:
        # Get available boxes using vagrant box list
        result = subprocess.run(
            ["vagrant", "box", "list"], 
            capture_output=True, 
            text=True, 
            check=True
        )
        
        # Parse the output
        templates = []
        for line in result.stdout.strip().split("\n"):
            if line:
                parts = line.split()
                if parts:
                    name = parts[0]
                    provider = "virtualbox"
                    if "(virtualbox" in line:
                        provider = "virtualbox"
                    elif "(libvirt" in line:
                        provider = "libvirt"
                        
                    templates.append({
                        "id": name.lower().replace("/", "_"),
                        "name": name,
                        "provider": provider,
                        "description": f"Vagrant box: {name}"
                    })
        
        print(f"Found {len(templates)} templates directly from Vagrant")
        return templates
    except subprocess.CalledProcessError as e:
        print(f"Failed to list Vagrant boxes: {e}")
        print(f"Error output: {e.stderr}")
        return None
    except Exception as e:
        print(f"Error fetching VM templates directly: {e}")
        return None


def list_vms_api(token: str) -> Optional[List[Dict[str, Any]]]:
    """List available VMs from the API"""
    print("\nListing available VMs from API...")
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    try:
        resp = requests.get(f"{API_BASE}/vagrant_vm/", headers=headers)
        print(f"Status code: {resp.status_code}")
        
        if resp.status_code == 200:
            vms = resp.json()
            print(f"Found {len(vms)} VMs")
            return vms
        else:
            print(f"Failed to list VMs from API: {resp.text}")
            return None
    except Exception as e:
        print(f"Error listing VMs from API: {e}")
        return None


def list_vms_direct() -> Optional[List[Dict[str, Any]]]:
    """Directly list available VMs using Vagrant command"""
    print("\nListing available VMs directly using Vagrant...")
    try:
        # Get global status to see all VMs
        result = subprocess.run(
            ["vagrant", "global-status"], 
            capture_output=True, 
            text=True, 
            check=True
        )
        
        # Parse the output
        vms = []
        lines = result.stdout.strip().split("\n")
        for line in lines:
            # Skip header and footer lines
            if not line.strip() or "id" in line.lower() or "--" in line or "above" in line.lower():
                continue
                
            parts = line.split()
            if len(parts) >= 5:
                vm_id = parts[0]
                name = parts[1]
                provider = parts[2]
                state = parts[3]
                directory = " ".join(parts[4:])
                
                vms.append({
                    "id": vm_id,
                    "name": name,
                    "provider": provider,
                    "status": state,
                    "directory": directory
                })
        
        print(f"Found {len(vms)} VMs directly from Vagrant")
        return vms
    except subprocess.CalledProcessError as e:
        print(f"Failed to get Vagrant global status: {e}")
        print(f"Error output: {e.stderr}")
        return None
    except Exception as e:
        print(f"Error listing VMs directly: {e}")
        return None


def check_grpc_env_vars() -> None:
    """Check environment variables related to gRPC"""
    grpc_env_vars = [var for var in os.environ if "GRPC" in var]
    print("\nGRPC-related environment variables:")
    if grpc_env_vars:
        for var in grpc_env_vars:
            print(f"{var}={os.environ[var]}")
    else:
        print("No GRPC-related environment variables found")


def check_vagrant_related_processes() -> None:
    """Check for vagrant-related processes"""
    print("\nChecking for vagrant-related processes...")
    try:
        ps_output = subprocess.check_output(
            ["ps", "-ef"], 
            text=True
        )
        
        # Filter for vagrant-related processes
        vagrant_processes = [
            line for line in ps_output.split("\n") 
            if "vagrant" in line.lower() or "ruby" in line.lower()
        ]
        
        if vagrant_processes:
            print("Found the following vagrant-related processes:")
            for process in vagrant_processes:
                print(process)
        else:
            print("No vagrant-related processes found")
    except Exception as e:
        print(f"Error checking processes: {e}")


def create_windows_vm_api(token: str, template_name: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """Attempt to create a Windows VM using the API"""
    print(f"\nAttempting to create a Windows VM from template '{template_name}' using API...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Payload for creating a VM
    payload = {
        "name": "test-windows-vm",
        "template": template_name
    }
    
    try:
        resp = requests.post(
            f"{API_BASE}/vagrant_vm/", 
            headers=headers, 
            json=payload
        )
        
        print(f"Status code: {resp.status_code}")
        if resp.status_code in [200, 201, 202]:
            print("VM creation initiated successfully via API")
            result = resp.json()
            print(json.dumps(result, indent=2))
            return True, result
        else:
            print(f"Failed to create VM via API: {resp.text}")
            return False, None
    except Exception as e:
        print(f"Error creating VM via API: {e}")
        return False, None


def create_windows_vm_direct(template_name: str) -> bool:
    """Create a Windows VM directly using Vagrant command"""
    print(f"\nAttempting to create a Windows VM from template '{template_name}' directly...")
    
    # Create a directory for the VM
    vm_dir = f"windows_vm_{int(time.time())}"
    try:
        os.makedirs(vm_dir, exist_ok=True)
        print(f"Created directory: {vm_dir}")
        
        # Change to the new directory
        os.chdir(vm_dir)
        
        # Create Vagrantfile
        vagrantfile_content = f"""# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  config.vm.box = "{template_name}"
  
  config.vm.provider "virtualbox" do |vb|
    vb.memory = 4096
    vb.cpus = 2
    vb.gui = false
  end

  config.vm.provider "libvirt" do |libvirt|
    libvirt.memory = 4096
    libvirt.cpus = 2
  end
end
"""
        with open("Vagrantfile", "w") as f:
            f.write(vagrantfile_content)
            
        print("Created Vagrantfile")
        
        # Run vagrant up with output streaming
        print("Running 'vagrant up'...")
        process = subprocess.Popen(
            ["vagrant", "up"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1
        )
        
        # Stream the output
        for line in iter(process.stdout.readline, ""):
            print(line, end="")
            sys.stdout.flush()
        
        # Wait for process to complete
        return_code = process.wait()
        
        if return_code == 0:
            print("Successfully created Windows VM directly!")
            return True
        else:
            print(f"Failed to create Windows VM directly. Return code: {return_code}")
            return False
    except Exception as e:
        print(f"Error creating VM directly: {e}")
        return False


def main() -> None:
    """Main function to run tests"""
    print("=== Vagrant VM Template Loading Diagnostic ===")
    
    # 1. Test gRPC connection
    print("\n--- Testing gRPC Connection ---")
    grpc_available = test_grpc_connection()
    
    # 2. Check gRPC environment variables
    check_grpc_env_vars()
    
    # 3. Check running processes
    check_vagrant_related_processes()
    
    # 4. Get auth token (for API methods)
    token = get_auth_token()
    
    # 5. Fetch VM templates
    templates = None
    if grpc_available and token:
        # Try API first
        templates = fetch_vm_templates_api(token)
    
    # Fallback to direct Vagrant
    if templates is None:
        print("Falling back to direct Vagrant commands for template listing")
        templates = fetch_vm_templates_direct()
    
    if templates:
        print("\nAvailable templates:")
        for template in templates:
            template_id = template.get('id', 'No ID')
            template_name = template.get('name', 'Unknown')
            print(f"- {template_name} ({template_id})")
    else:
        print("\nNo templates found using either method")
    
    # 6. List existing VMs
    vms = None
    if grpc_available and token:
        # Try API first
        vms = list_vms_api(token)
    
    # Fallback to direct Vagrant
    if vms is None:
        print("Falling back to direct Vagrant commands for VM listing")
        vms = list_vms_direct()
    
    if vms:
        print("\nExisting VMs:")
        for vm in vms:
            vm_id = vm.get('id', 'No ID')
            vm_name = vm.get('name', 'Unknown')
            vm_status = vm.get('status', 'Unknown status')
            print(f"- {vm_name} ({vm_id}): {vm_status}")
    else:
        print("\nNo existing VMs found using either method")
    
    # 7. Create a VM if user wants to
    if templates:
        # Let user select a template
        print("\nWindows templates:")
        windows_templates = [t for t in templates if "windows" in t.get('name', '').lower()]
        
        if not windows_templates:
            print("No Windows templates found. Available templates:")
            for i, template in enumerate(templates):
                print(f"{i+1}. {template.get('name', 'Unknown')}")
                
            choice = input("\nDo you want to attempt creating a VM from one of these templates? (Enter number or n): ")
            if choice.lower() == 'n':
                return
                
            try:
                template_idx = int(choice) - 1
                if 0 <= template_idx < len(templates):
                    selected_template = templates[template_idx]
                else:
                    print("Invalid selection")
                    return
            except ValueError:
                print("Invalid input")
                return
        else:
            # Display Windows templates
            for i, template in enumerate(windows_templates):
                print(f"{i+1}. {template.get('name', 'Unknown')}")
                
            choice = input("\nSelect a Windows template (Enter number or n to cancel): ")
            if choice.lower() == 'n':
                return
                
            try:
                template_idx = int(choice) - 1
                if 0 <= template_idx < len(windows_templates):
                    selected_template = windows_templates[template_idx]
                else:
                    print("Invalid selection")
                    return
            except ValueError:
                print("Invalid input")
                return
        
        # Get template name for creation
        template_name = selected_template.get('name', '')
        
        # Try to create VM
        success = False
        if grpc_available and token:
            # Try API first
            success, _ = create_windows_vm_api(token, template_name)
        
        # Fallback to direct Vagrant
        if not success:
            print("Falling back to direct Vagrant command for VM creation")
            create_windows_vm_direct(template_name)


if __name__ == "__main__":
    main() 