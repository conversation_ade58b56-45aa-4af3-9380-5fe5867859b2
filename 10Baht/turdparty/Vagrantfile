# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  # Use Windows 10 box from the Vagrant Cloud
  config.vm.box = "gusztavvargadr/windows-10"
  
  # Configure VM resources for VirtualBox
  config.vm.provider "virtualbox" do |vb|
    vb.memory = 4096
    vb.cpus = 2
    vb.gui = false  # Headless mode since we're on a server
    vb.customize ["modifyvm", :id, "--vram", "128"]
  end

  # Configure VM resources for libvirt (alternative provider)
  config.vm.provider "libvirt" do |libvirt|
    libvirt.memory = 4096
    libvirt.cpus = 2
    libvirt.graphics_type = "spice"
  end

  # Configure network
  config.vm.network "private_network", type: "dhcp"
  
  # Configure WinRM for communication
  config.vm.communicator = "winrm"
  config.winrm.username = "vagrant"
  config.winrm.password = "vagrant"
  config.winrm.timeout = 1800
  
  # Windows-specific customization
  config.vm.provision "shell", inline: <<-SHELL
    # Display system information
    systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"
    
    # Create a test file on desktop
    echo "Windows VM is ready for testing" > C:\\Users\\<USER>\\Desktop\\vm_ready.txt
  SHELL
end 