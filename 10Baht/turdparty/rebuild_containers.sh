#!/bin/bash
# <PERSON>ript to rebuild Docker containers with test mode enabled

echo "Rebuilding Docker containers with test mode enabled..."

# Set the docker-compose file path
DOCKER_COMPOSE_FILE="/home/<USER>/turdparty/.dockerwrapper/docker-compose.yml"

# Stop the containers
echo "Stopping containers..."
docker-compose -f $DOCKER_COMPOSE_FILE down

# Rebuild the containers
echo "Rebuilding containers..."
docker-compose -f $DOCKER_COMPOSE_FILE build

# Start the containers
echo "Starting containers..."
docker-compose -f $DOCKER_COMPOSE_FILE up -d

echo "Containers rebuilt and started with test mode enabled."
echo "You can now run tests without authentication." 