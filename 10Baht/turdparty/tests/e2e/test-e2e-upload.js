const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  frontendUrl: 'http://localhost:3100',
  apiUrl: 'http://localhost:3050',
  uploadPage: '/file_upload',
  testFilePath: './test-file.txt',
  screenshotDir: './test_screenshots',
  headless: true,
  timeout: 30000
};

// Ensure test file exists
if (!fs.existsSync(config.testFilePath)) {
  fs.writeFileSync(config.testFilePath, 'Test content for upload test.');
  console.log(`Created test file: ${config.testFilePath}`);
}

// Ensure screenshot directory exists
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true });
  console.log(`Created screenshot directory: ${config.screenshotDir}`);
}

// Helper function to take screenshots
async function takeScreenshot(page, name) {
  const screenshotPath = path.join(config.screenshotDir, `${name}_${Date.now()}.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  console.log(`Screenshot saved: ${screenshotPath}`);
}

async function runTest() {
  console.log('Starting E2E File Upload Test');
  console.log('============================');
  
  const browser = await chromium.launch({ headless: config.headless });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Step 1: Navigate to the upload page
    console.log(`Navigating to ${config.frontendUrl}${config.uploadPage}`);
    await page.goto(`${config.frontendUrl}${config.uploadPage}`, { 
      timeout: config.timeout,
      waitUntil: 'networkidle'
    });
    
    console.log('Page loaded successfully');
    await takeScreenshot(page, 'upload_page_loaded');
    
    // Step 2: Check if we need to get a token
    const tokenButton = page.locator('button:has-text("Get Authentication Token")');
    if (await tokenButton.isVisible()) {
      console.log('Getting authentication token...');
      await tokenButton.click();
      await page.waitForTimeout(2000);
      await takeScreenshot(page, 'after_token_request');
    }
    
    // Step 3: Upload a file
    console.log('Uploading test file...');
    
    // Wait for upload zone to be visible
    const fileInput = page.locator('input[type="file"]');
    await fileInput.waitFor({ state: 'visible', timeout: config.timeout });
    
    // Set the file to be uploaded
    await fileInput.setInputFiles(config.testFilePath);
    console.log('File selected for upload');
    await takeScreenshot(page, 'file_selected');
    
    // Click upload button (if not auto-uploading)
    const uploadButton = page.locator('button:has-text("Upload")');
    if (await uploadButton.isVisible()) {
      await uploadButton.click();
      console.log('Upload button clicked');
    }
    
    // Wait for upload success message or upload list to update
    await page.waitForTimeout(3000); // Give time for upload to complete
    await takeScreenshot(page, 'after_upload');
    
    // Check for success indicators
    const successIndicators = [
      'upload success',
      'file uploaded',
      'success',
      'uploaded'
    ];
    
    let uploadSuccess = false;
    for (const indicator of successIndicators) {
      const successMessage = page.locator(`text=${indicator}`, { exact: false });
      if (await successMessage.isVisible()) {
        console.log(`Found success message containing: "${indicator}"`);
        uploadSuccess = true;
        break;
      }
    }
    
    // Alternatively check if the file appears in the list
    const tableRows = await page.locator('table tbody tr').count();
    console.log(`Found ${tableRows} files in the list`);
    if (tableRows > 0) {
      uploadSuccess = true;
    }
    
    // Result
    if (uploadSuccess) {
      console.log('\nFILE UPLOAD TEST PASSED ✅');
    } else {
      console.log('\nFILE UPLOAD TEST FAILED ❌');
      // Capture page HTML for debugging
      const html = await page.content();
      fs.writeFileSync('debug_page.html', html);
      console.log('Saved page HTML to debug_page.html for debugging');
    }
  } catch (error) {
    console.error('Test failed with error:', error);
    await takeScreenshot(page, 'error_state');
    
    // Capture page HTML for debugging
    try {
      const html = await page.content();
      fs.writeFileSync('error_page.html', html);
      console.log('Saved page HTML to error_page.html for debugging');
    } catch (e) {
      console.error('Could not save error page HTML:', e);
    }
  } finally {
    // Close browser
    await browser.close();
    console.log('Test complete');
  }
}

// Run the test
runTest().catch(console.error); 