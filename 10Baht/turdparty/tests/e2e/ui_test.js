const { chromium } = require("playwright"); (async () => { const browser = await chromium.launch({ headless: true }); const context = await browser.newContext(); const page = await context.newPage(); console.log("Starting UI test..."); try { await page.goto("http://turdparty_test_frontend:3000/"); console.log("Page loaded, title:", await page.title()); await page.screenshot({ path: "/tmp/ui.png" }); const body = await page.content(); console.log("Page content:", body.substring(0, 200), "..."); } catch (error) { console.error("Error:", error.message); } await browser.close(); console.log("Test completed"); })();
