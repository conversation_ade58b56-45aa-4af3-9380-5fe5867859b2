// Direct browser automation script
const { chromium } = require('playwright');

(async () => {
  console.log('Starting test');
  
  // Launch the browser
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Go to upload page
    console.log('Navigating to upload page');
    await page.goto('http://**********:3100/upload');
    await page.screenshot({ path: 'test_screenshots/01-upload-page.png' });
    
    // Fill the form
    console.log('Filling form');
    await page.fill('textarea[placeholder*="description"]', 'Final upload test');
    
    // Upload the file
    console.log('Selecting file');
    await page.setInputFiles('input[type="file"]', 'test-upload.txt');
    await page.screenshot({ path: 'test_screenshots/02-file-selected.png' });
    
    // Click the upload button
    console.log('Submitting form');
    await page.click('button:has-text("Upload")');
    
    // Wait for success message
    console.log('Waiting for success message');
    await page.waitForSelector('.ant-message-success, .upload-success', { timeout: 30000 });
    await page.screenshot({ path: 'test_screenshots/03-success.png' });
    
    // Navigate to files page
    console.log('Checking files page');
    await page.goto('http://**********:3100/files');
    await page.screenshot({ path: 'test_screenshots/04-files-list.png' });
    
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
    await page.screenshot({ path: 'test_screenshots/error.png' });
    process.exit(1);
  } finally {
    await browser.close();
  }
})();
