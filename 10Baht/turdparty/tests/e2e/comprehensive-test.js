const { chromium } = require("playwright"); (async () => { const browser = await chromium.launch({ headless: true }); const context = await browser.newContext(); const page = await context.newPage(); try { console.log("*** Testing Home page ***"); await page.goto("http://172.18.0.4:3000/"); console.log("Home page loaded, title:", await page.title()); await page.screenshot({ path: "/tmp/homepage.png" }); const homeHeadings = await page.$$("h1, h2, h3"); console.log(`Found ${homeHeadings.length} headings on home page`); const homeButtons = await page.$$("button"); console.log(`Found ${homeButtons.length} buttons on home page`); console.log("\n*** Testing VM Status page ***"); await page.goto("http://172.18.0.4:3000/vm_status"); console.log("VM Status page loaded"); await page.screenshot({ path: "/tmp/vm_status.png" }); const vmStatusCards = await page.$$(".ant-card"); console.log(`Found ${vmStatusCards.length} cards on VM Status page`); const vmStatusAlerts = await page.$$(".ant-alert"); console.log(`Found ${vmStatusAlerts.length} alerts on VM Status page`); console.log("\n*** Testing File Upload page ***"); await page.goto("http://172.18.0.4:3000/file_upload"); console.log("File Upload page loaded"); await page.screenshot({ path: "/tmp/file_upload.png" }); const uploadComponents = await page.$$(".ant-upload, .file-upload-component"); console.log(`Found ${uploadComponents.length} upload components`); const uploadButtons = await page.$$("button"); console.log(`Found ${uploadButtons.length} buttons on upload page`); if(uploadButtons.length > 0) { const buttonTexts = await Promise.all(uploadButtons.map(button => button.textContent())); console.log("Button texts:", buttonTexts.filter(Boolean)); } console.log("\n*** Testing UI interaction ***"); if(uploadComponents.length > 0) { await page.click(".ant-upload"); await page.screenshot({ path: "/tmp/file_upload_click.png" }); console.log("Clicked on upload component"); } console.log("All tests completed successfully"); } catch (error) { console.error("Error:", error.message); console.error(error.stack); } finally { await browser.close(); } })();
