#!/bin/bash

# Test various API URLs to diagnose connectivity issues

# Set color variables
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing API Connectivity${NC}"
echo "========================="

# Test URLs
URLS=(
    "http://localhost:3050/"
    "http://localhost:3050/api"
    "http://localhost:3050/api/v1"
    "http://localhost:3050/api/v1/health"
    "http://localhost:3050/health"
    "http://localhost:3050/api/v1/file_upload"
    "http://localhost:3050/api/v1/file_upload/"
)

# Test FE URLs
FE_URLS=(
    "http://localhost:3100/"
    "http://localhost:3100/file_upload"
)

# Test each URL
echo -e "\n${YELLOW}Testing API URLs${NC}"
echo "----------------"
for URL in "${URLS[@]}"; do
    echo -en "Testing ${YELLOW}$URL${NC}... "
    RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$URL")
    
    if [[ "$RESPONSE" == 2* || "$RESPONSE" == 3* ]]; then
        echo -e "${GREEN}Success ($RESPONSE)${NC}"
        echo "Response content:"
        curl -s "$URL" | head -n 20
        echo
    else
        echo -e "${RED}Failed ($RESPONSE)${NC}"
    fi
    echo "-----------------------"
done

# Test frontend URLs
echo -e "\n${YELLOW}Testing Frontend URLs${NC}"
echo "--------------------"
for URL in "${FE_URLS[@]}"; do
    echo -en "Testing ${YELLOW}$URL${NC}... "
    RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$URL")
    
    if [[ "$RESPONSE" == 2* || "$RESPONSE" == 3* ]]; then
        echo -e "${GREEN}Success ($RESPONSE)${NC}"
    else
        echo -e "${RED}Failed ($RESPONSE)${NC}"
    fi
done

# Check Docker container status
echo -e "\n${YELLOW}Checking API Container Status${NC}"
echo "----------------------------"
docker ps | grep turdparty_test_api

# Try to get API container logs
echo -e "\n${YELLOW}API Container Logs (last 20 lines)${NC}"
echo "--------------------------------"
docker logs turdparty_test_api --tail 20 