#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing Complete AppImage Workflow via API${NC}"
echo -e "${YELLOW}=======================================${NC}"

# Configuration
API_URL="http://localhost:3050"
API_VERSION="v1"
API_BASE="${API_URL}/api/${API_VERSION}"
TEST_DIR="/tmp/appimage-test"
APPIMAGE_FILE="${TEST_DIR}/test-appimage.AppImage"

# Create test directory if it doesn't exist
mkdir -p "${TEST_DIR}"

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
  echo -e "${RED}Error: Python 3 is required but not found${NC}"
  exit 1
fi

# Check if the API is running
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${API_URL}/api/v1/health || echo "failed")

if [ "$API_STATUS" != "200" ]; then
  echo -e "${RED}Error: API is not running or not accessible at ${API_URL}${NC}"
  echo -e "${YELLOW}Make sure the API container is running with host-based MinIO configuration${NC}"
  exit 1
fi

echo -e "${GREEN}API is accessible at ${API_URL}${NC}"

# Create a dummy AppImage file for testing
echo -e "${YELLOW}Creating dummy AppImage file for testing...${NC}"
cat > ${APPIMAGE_FILE} << 'EOF'
#!/bin/bash
echo "This is a dummy AppImage executable - $(date)"
echo "Environment:"
env
echo "System info:"
uname -a
echo "Current directory: $(pwd)"
echo "AppImage test complete!"
EOF

# Make the dummy AppImage executable
chmod +x ${APPIMAGE_FILE}
echo -e "${GREEN}Created dummy AppImage at ${APPIMAGE_FILE}${NC}"

# Create Python script for the complete workflow test
cat > ${TEST_DIR}/appimage_workflow_test.py << 'EOF'
#!/usr/bin/env python3
"""
Test script for complete AppImage workflow through the API.

Workflow:
1. Authenticate to get token
2. Upload AppImage file to MinIO via API
3. List existing Vagrant VMs
4. Select or create Ubuntu VM
5. Inject and run AppImage in VM
6. Verify execution results
"""

import requests
import json
import time
import os
import sys
import argparse
from datetime import datetime
import uuid

# Configuration
API_URL = "http://localhost:3050"
API_VERSION = "v1"
API_BASE = f"{API_URL}/api/{API_VERSION}"
APPIMAGE_FILE = "/tmp/appimage-test/test-appimage.AppImage"

def log(message):
    """Log with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def get_auth_token():
    """Get authentication token"""
    log("Getting auth token...")
    try:
        # Try to get test token for development/testing
        resp = requests.post(f"{API_BASE}/auth/test-token")
        if resp.status_code == 200:
            token = resp.json().get("access_token")
            if token:
                log("Test token obtained")
                return token
            
        # If test token fails, try login with default credentials
        log("Test token failed, trying login...")
        login_data = {
            "username": "<EMAIL>",
            "password": "password123"
        }
        resp = requests.post(f"{API_BASE}/auth/login", json=login_data)
        if resp.status_code == 200:
            token = resp.json().get("access_token")
            if token:
                log("Login successful, token obtained")
                return token
        
        log(f"Failed to authenticate: {resp.status_code}")
        log(resp.text)
        return None
    except Exception as e:
        log(f"Authentication error: {str(e)}")
        return None

def upload_appimage(token):
    """Upload AppImage file to MinIO via API"""
    log(f"Uploading AppImage from {APPIMAGE_FILE}...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # Verify file exists
        if not os.path.exists(APPIMAGE_FILE):
            log(f"Error: AppImage file not found at {APPIMAGE_FILE}")
            return None
        
        # Upload the file
        with open(APPIMAGE_FILE, "rb") as f:
            files = {"file": (os.path.basename(APPIMAGE_FILE), f, "application/x-executable")}
            data = {"description": "Test AppImage for VM execution"}
            
            resp = requests.post(f"{API_BASE}/file_upload/", headers=headers, files=files, data=data)
            
            if resp.status_code not in [200, 201]:
                log(f"Failed to upload AppImage: {resp.status_code}")
                log(resp.text)
                return None
            
            file_data = resp.json()
            log(f"Upload successful! File ID: {file_data['id']}")
            log(f"Filename: {file_data['filename']}")
            log(f"File size: {file_data['file_size']} bytes")
            log(f"Download URL: {file_data['download_url']}")
            
            return file_data
    except Exception as e:
        log(f"Upload error: {str(e)}")
        return None

def list_vms(token):
    """List available Vagrant VMs"""
    log("Listing Vagrant VMs...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get(f"{API_BASE}/vagrant_vm/", headers=headers)
        
        if resp.status_code != 200:
            log(f"Failed to list VMs: {resp.status_code}")
            log(resp.text)
            return []
        
        vms = resp.json().get("items", [])
        log(f"Found {len(vms)} VMs")
        
        # Print VM details
        for i, vm in enumerate(vms):
            log(f"{i+1}. {vm['name']} (ID: {vm['id']}) - Status: {vm['status']}")
        
        return vms
    except Exception as e:
        log(f"VM listing error: {str(e)}")
        return []

def get_or_create_ubuntu_vm(token, vms):
    """Find existing Ubuntu VM or create a new one"""
    # Look for an Ubuntu VM
    ubuntu_vms = [vm for vm in vms if "ubuntu" in vm.get("name", "").lower()]
    
    if ubuntu_vms:
        vm = ubuntu_vms[0]
        log(f"Found existing Ubuntu VM: {vm['name']} (ID: {vm['id']})")
        return vm
    
    # No Ubuntu VM found, create one
    log("No Ubuntu VM found, creating a new one...")
    
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        vm_data = {
            "name": f"ubuntu-test-{uuid.uuid4().hex[:8]}",
            "description": "Test Ubuntu VM for AppImage execution",
            "box": "ubuntu/focal64",
            "memory": 1024,
            "cpus": 1
        }
        
        resp = requests.post(f"{API_BASE}/vagrant_vm/", headers=headers, json=vm_data)
        
        if resp.status_code not in [200, 201]:
            log(f"Failed to create VM: {resp.status_code}")
            log(resp.text)
            return None
        
        vm = resp.json()
        log(f"Created new VM: {vm['name']} (ID: {vm['id']})")
        
        # Wait for VM to be created
        log("Waiting for VM to be fully created...")
        time.sleep(5)
        
        return vm
    except Exception as e:
        log(f"VM creation error: {str(e)}")
        return None

def ensure_vm_running(token, vm):
    """Make sure the VM is in running state"""
    if vm["status"] == "running":
        log("VM is already running")
        return True
    
    log(f"VM status is '{vm['status']}', starting VM...")
    
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        action_data = {"action": "start"}
        
        resp = requests.post(
            f"{API_BASE}/vagrant_vm/{vm['id']}/action", 
            headers=headers, 
            json=action_data
        )
        
        if resp.status_code not in [200, 201, 202]:
            log(f"Failed to start VM: {resp.status_code}")
            log(resp.text)
            return False
        
        # Wait for VM to start
        log("VM start initiated, waiting for 30 seconds...")
        time.sleep(30)
        
        # Check VM status
        resp = requests.get(f"{API_BASE}/vagrant_vm/{vm['id']}/status", headers=headers)
        if resp.status_code == 200:
            status = resp.json().get("status")
            log(f"VM status after waiting: {status}")
            return status == "running"
        else:
            log(f"Failed to check VM status: {resp.status_code}")
            return False
    except Exception as e:
        log(f"VM start error: {str(e)}")
        return False

def execute_appimage(token, vm, file_data):
    """Execute the AppImage on the VM"""
    log(f"Setting up AppImage execution on VM {vm['id']}...")
    
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # Build the command to download and run the AppImage
        download_url = f"{API_URL}{file_data['download_url']}"
        commands = [
            f"wget '{download_url}' -O /home/<USER>/test-appimage.AppImage",
            "chmod +x /home/<USER>/test-appimage.AppImage",
            "/home/<USER>/test-appimage.AppImage > /home/<USER>/appimage_output.log 2>&1",
            "echo '=== Command Output ==='; cat /home/<USER>/appimage_output.log; echo '=== System Info ==='; uname -a"
        ]
        
        # Join commands with && to execute them in sequence
        command = " && ".join(commands)
        
        # Execute the command
        log("Sending command execution request...")
        exec_payload = {"command": command}
        
        resp = requests.post(
            f"{API_BASE}/vagrant_vm/{vm['id']}/exec", 
            headers=headers, 
            json=exec_payload
        )
        
        if resp.status_code not in [200, 201, 202]:
            log(f"Failed to execute command: {resp.status_code}")
            log(resp.text)
            return None
        
        # Get the result
        result = resp.json()
        
        log("\n--- Command Execution Result ---")
        log(f"Status: {'Success' if result.get('success', False) else 'Failed'}")
        
        if result.get('execution_method'):
            log(f"Execution Method: {result.get('execution_method', 'unknown')}")
        
        if result.get('error'):
            log("\nError:")
            log(result['error'])
        
        output = result.get('output', '')
        log("\nOutput:")
        log(output)
        
        log("-------------------------------\n")
        
        # Verify the execution
        if "AppImage test complete!" in output:
            log("✅ AppImage execution verification: SUCCESS")
        else:
            log("❌ AppImage execution verification: FAILED")
        
        return result
    except Exception as e:
        log(f"Execution error: {str(e)}")
        return None

def main():
    """Main workflow function"""
    log("Starting AppImage workflow test...")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        log("Failed to get authentication token")
        sys.exit(1)
    
    # Upload AppImage
    file_data = upload_appimage(token)
    if not file_data:
        log("Failed to upload AppImage")
        sys.exit(1)
    
    # List VMs
    vms = list_vms(token)
    
    # Get or create VM
    vm = get_or_create_ubuntu_vm(token, vms)
    if not vm:
        log("Failed to get or create VM")
        sys.exit(1)
    
    # Ensure VM is running
    if not ensure_vm_running(token, vm):
        log("Failed to ensure VM is running")
        sys.exit(1)
    
    # Execute AppImage
    result = execute_appimage(token, vm, file_data)
    if not result:
        log("Failed to execute AppImage")
        sys.exit(1)
    
    log("AppImage workflow test completed successfully!")

if __name__ == "__main__":
    main()
EOF

# Run the workflow test
echo -e "${YELLOW}Running AppImage workflow test...${NC}"
cd ${TEST_DIR}
python3 ${TEST_DIR}/appimage_workflow_test.py

# Clean up
echo -e "${YELLOW}Cleaning up test files...${NC}"
rm -f ${APPIMAGE_FILE}
rm -f ${TEST_DIR}/appimage_workflow_test.py
rmdir ${TEST_DIR} 2>/dev/null || true

echo -e "${GREEN}Test complete!${NC}" 