#!/usr/bin/env python3

"""
Base test class for VM injection tests.
"""

import os
import sys
import logging
import unittest
import asyncio
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BaseVMInjectionTest(unittest.TestCase):
    """Base class for VM injection tests."""

    def setUp(self) -> None:
        """Set up the test environment synchronously."""
        # Run the async setup
        asyncio.run(self.async_setup())

    async def async_setup(self) -> None:
        """Set up the test environment asynchronously."""
        from api.services.vagrant_client import VagrantClient

        # Create a test file
        self.test_file_path = os.path.join(os.path.dirname(__file__), 'test-file.txt')
        with open(self.test_file_path, 'w') as f:
            f.write('This is a test file for VM injection tests')

        # Create a VagrantClient instance
        self.vagrant_client = VagrantClient()

        # Default test VM ID
        self.test_vm_id = 'localhost'

    def tearDown(self) -> None:
        """Clean up the test environment synchronously."""
        # Run the async teardown
        asyncio.run(self.async_teardown())

    async def async_teardown(self) -> None:
        """Clean up the test environment asynchronously."""
        # Remove the test file
        if os.path.exists(self.test_file_path):
            os.remove(self.test_file_path)

        # Close the VagrantClient
        if hasattr(self, 'vagrant_client'):
            await self.vagrant_client.close()

    async def test_connection(self, use_ssh: bool = True) -> bool:
        """
        Test the connection to the Vagrant service.

        Args:
            use_ssh: Whether to use SSH mode.

        Returns:
            True if the connection was successful, False otherwise.
        """
        # Set the connection mode
        self.vagrant_client.use_ssh = use_ssh

        # Connect to the service
        connected = await self.vagrant_client.connect()

        # Log the connection result
        if connected:
            logger.info(f"Successfully connected to Vagrant service using {'SSH' if use_ssh else 'gRPC'} mode")
        else:
            logger.warning(f"Failed to connect to Vagrant service using {'SSH' if use_ssh else 'gRPC'} mode")

        return connected

    async def get_vm_status(self, vm_id: str) -> Dict[str, Any]:
        """
        Get the status of a VM.

        Args:
            vm_id: The ID of the VM.

        Returns:
            A dictionary with the VM status.
        """
        logger.info(f"Getting status for VM {vm_id}")
        status = await self.vagrant_client.status(vm_id)
        logger.info(f"VM status: {status}")
        return status

    async def inject_file(self, vm_id: str, source_path: str, target_path: str, permissions: str = '0755') -> Dict[str, Any]:
        """
        Inject a file into a VM.

        Args:
            vm_id: The ID of the VM.
            source_path: The path to the source file.
            target_path: The path to the target file.
            permissions: The permissions to set on the target file.

        Returns:
            A dictionary with the result of the operation.
        """
        logger.info(f"Injecting file {source_path} into VM {vm_id} at {target_path}")

        # Create the command to copy the file to the VM
        copy_command = f"cp {source_path} {target_path} && chmod {permissions} {target_path}"

        # Execute the command
        result = await self.vagrant_client.execute_command(vm_id, copy_command, sudo=True)
        logger.info(f"File injection result: {result}")

        return result

    async def execute_command(self, vm_id: str, command: str, sudo: bool = False) -> Dict[str, Any]:
        """
        Execute a command on a VM.

        Args:
            vm_id: The ID of the VM.
            command: The command to execute.
            sudo: Whether to execute the command with sudo.

        Returns:
            A dictionary with the result of the operation.
        """
        logger.info(f"Executing command on VM {vm_id}: {command}")
        result = await self.vagrant_client.execute_command(vm_id, command, sudo=sudo)
        logger.info(f"Command execution result: {result}")

        return result

    async def verify_file_injection(self, vm_id: str, target_path: str) -> bool:
        """
        Verify that a file was injected into a VM.

        Args:
            vm_id: The ID of the VM.
            target_path: The path to the target file.

        Returns:
            True if the file was injected successfully, False otherwise.
        """
        logger.info(f"Verifying file injection on VM {vm_id} at {target_path}")

        # Execute a command to check if the file exists
        result = await self.vagrant_client.execute_command(vm_id, f"ls -la {target_path}")

        # Check if the command was successful
        if result.get('success', False):
            logger.info(f"File injection verified: {result.get('stdout', '')}")
            return True
        else:
            logger.warning(f"File injection verification failed: {result.get('stderr', '')}")
            return False

    def run_async_test(self, test_method, *args, **kwargs):
        """
        Run an async test method.

        Args:
            test_method: The async test method to run.
            *args: Arguments to pass to the test method.
            **kwargs: Keyword arguments to pass to the test method.

        Returns:
            The result of the test method.
        """
        return asyncio.run(test_method(*args, **kwargs))
