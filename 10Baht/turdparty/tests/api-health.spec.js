const { test, expect } = require('@playwright/test');

test.describe('API Health Check', () => {
  test('should return health status', async ({ request }) => {
    const response = await request.get('/health/');
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.status).toBe('ok');
  });

  test('should return version information', async ({ request }) => {
    const response = await request.get('/version/');
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.version).toBe('1.0.0');
  });
}); 