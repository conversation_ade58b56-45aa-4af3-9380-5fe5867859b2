name: Generate Test Reports

on:
  workflow_run:
    workflows: ["Playwright API Tests"]
    types:
      - completed

jobs:
  report:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion != 'skipped'
    
    steps:
      - name: Download Test Artifacts
        uses: dawidd6/action-download-artifact@v2
        with:
          workflow: playwright-tests.yml
          workflow_conclusion: completed
          name: playwright-report
          path: playwright-report
      
      - name: Display structure of downloaded files
        run: ls -R
      
      - name: Generate Test Report Badge
        uses: emibcn/badge-action@v1
        with:
          label: 'API Tests'
          status: ${{ github.event.workflow_run.conclusion == 'success' && 'passing' || 'failing' }}
          color: ${{ github.event.workflow_run.conclusion == 'success' && 'green' || 'red' }}
          path: test-badge.svg
      
      - name: Upload badge to Gist
        if: success() && github.event.workflow_run.head_branch == 'main'
        uses: andymckay/append-gist-action@1.0.1
        with:
          token: ${{ secrets.GIST_TOKEN }}
          gistURL: https://gist.github.com/your-username/your-gist-id
          file: test-badge.svg
      
      - name: Deploy Test Report
        if: always()
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./playwright-report
          destination_dir: reports/${{ github.event.workflow_run.head_branch }}/playwright-tests
          keep_files: true
          
      - name: Post Results Comment
        if: github.event.workflow_run.event == 'pull_request'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            
            // Try to load test results
            let summary = "⚠️ No test results found";
            try {
              const resultsFile = './playwright-report/results.json';
              if (fs.existsSync(resultsFile)) {
                const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
                const stats = results.stats;
                
                const passed = stats.passed || 0;
                const failed = stats.failed || 0;
                const skipped = stats.skipped || 0;
                const total = passed + failed + skipped;
                
                const status = failed > 0 ? '❌ FAILED' : '✅ PASSED';
                
                summary = `## API Test Results: ${status}\n\n` +
                          `- Total: ${total} test${total !== 1 ? 's' : ''}\n` +
                          `- Passed: ${passed} ✅\n` +
                          `- Failed: ${failed} ${failed > 0 ? '❌' : ''}\n` +
                          `- Skipped: ${skipped} ⏭️\n\n` +
                          `[View Full Report](https://your-org.github.io/your-repo/reports/${process.env.GITHUB_REF_NAME}/playwright-tests/index.html)`;
              }
            } catch (error) {
              console.error('Error parsing test results:', error);
              summary += `: ${error.message}`;
            }
            
            // Find the PR number from the workflow run
            const { data: pull_requests } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              head: `${context.repo.owner}:${process.env.GITHUB_REF_NAME}`
            });
            
            if (pull_requests.length === 0) {
              console.log('No open pull request found for this branch');
              return;
            }
            
            // Post comment to the PR
            const pr_number = pull_requests[0].number;
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: pr_number,
              body: summary
            }); 