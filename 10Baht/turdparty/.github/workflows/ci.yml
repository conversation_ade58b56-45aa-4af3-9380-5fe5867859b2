name: TurdParty CI/CD Pipeline

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - production

env:
  # Global environment variables
  POSTGRES_HOST_IP: 127.0.0.1
  MINIO_HOST_IP: 127.0.0.1
  TEST_MODE: "true"
  CI: "true"

jobs:
  lint:
    name: Code Linting
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          cache: 'pip'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 black isort mypy
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
      
      - name: Lint with flake8
        run: |
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
      
      - name: Check formatting with black
        run: |
          black --check --diff .
      
      - name: Check imports with isort
        run: |
          isort --check --diff .
  
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      postgres:
        image: postgres:14-alpine
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: turdparty
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      minio:
        image: minio/minio:latest
        env:
          MINIO_ROOT_USER: minioadmin
          MINIO_ROOT_PASSWORD: minioadmin
        ports:
          - 9000:9000
          - 9001:9001
        options: >-
          --name minio
          --health-cmd "curl -f http://localhost:9000/minio/health/live || exit 1"
          --health-interval 30s
          --health-timeout 20s
          --health-retries 3
          --health-start-period 10s
        volumes:
          - /tmp/minio-data:/data
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          cache: 'pip'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest pytest-cov pytest-asyncio
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
      
      - name: Setup test configuration
        run: |
          # Create directories for test outputs
          mkdir -p test-results/unit
          mkdir -p test-reports/coverage
          
          # Configure environment variables for tests
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/turdparty" >> $GITHUB_ENV
          echo "DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@localhost:5432/turdparty" >> $GITHUB_ENV
          echo "MINIO_HOST=localhost" >> $GITHUB_ENV
          echo "MINIO_PORT=9000" >> $GITHUB_ENV
          echo "MINIO_ACCESS_KEY=minioadmin" >> $GITHUB_ENV
          echo "MINIO_SECRET_KEY=minioadmin" >> $GITHUB_ENV
      
      - name: Run unit tests
        run: |
          python -m pytest api/tests/test_unit --cov=api --cov-report=xml:test-reports/coverage/coverage.xml -v --junitxml=test-results/unit/results.xml
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: unit-test-results
          path: |
            test-results/unit/
            test-reports/coverage/
        if: always()
      
      - name: Publish test coverage
        uses: codecov/codecov-action@v3
        with:
          file: test-reports/coverage/coverage.xml
          fail_ci_if_error: false
  
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Start Docker Compose
        run: |
          cd .dockerwrapper
          docker-compose -f docker-compose.playwright.yml up -d db minio api
          
          # Wait for services to be ready
          echo "Waiting for services to be available..."
          for i in {1..30}; do
            if curl -s http://localhost:3050/health >/dev/null 2>&1; then
              echo "API is available!"
              break
            fi
            echo "Waiting for API to be available... ($i/30)"
            sleep 2
          done
      
      - name: Run integration tests
        run: |
          cd .dockerwrapper
          docker-compose -f docker-compose.playwright.yml run --rm playwright /bin/bash -c "cd /app && python -m pytest api/tests/test_integration -v --junitxml=test-results/integration/results.xml"
      
      - name: Collect test results
        run: |
          mkdir -p test-results/integration
          cp -r test-results/* test-results/integration/ || true
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: integration-test-results
          path: test-results/integration/
        if: always()
      
      - name: Stop containers
        run: |
          cd .dockerwrapper
          docker-compose -f docker-compose.playwright.yml down
        if: always()
  
  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: integration-tests
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Start Docker Compose
        run: |
          cd .dockerwrapper
          docker-compose -f docker-compose.playwright.yml up -d
          
          # Wait for services to be ready
          echo "Waiting for services to be available..."
          for i in {1..30}; do
            if curl -s http://localhost:3050/health >/dev/null 2>&1; then
              echo "API is available!"
              break
            fi
            echo "Waiting for API to be available... ($i/30)"
            sleep 2
          done
      
      - name: Run Playwright tests
        run: |
          cd .dockerwrapper
          docker-compose -f docker-compose.playwright.yml exec -T playwright /bin/bash -c "cd /app && npx playwright test"
      
      - name: Collect test results
        run: |
          mkdir -p test-results/e2e
          cp -r playwright-report/* test-results/e2e/ || true
          cp -r test-results/* test-results/e2e/ || true
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: e2e-test-results
          path: |
            test-results/e2e/
            playwright-report/
        if: always()
      
      - name: Stop containers
        run: |
          cd .dockerwrapper
          docker-compose -f docker-compose.playwright.yml down
        if: always()
  
  build-containers:
    name: Build Containers
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
      
      - name: Build and tag API image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: .dockerwrapper/Dockerfile
          push: false
          tags: turdparty-api:${{ github.sha }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          outputs: type=docker,dest=/tmp/api-image.tar
      
      - name: Build and tag Playwright image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: .dockerwrapper/Dockerfile.playwright
          push: false
          tags: turdparty-playwright:${{ github.sha }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          outputs: type=docker,dest=/tmp/playwright-image.tar
      
      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache
      
      - name: Upload container images
        uses: actions/upload-artifact@v3
        with:
          name: docker-images
          path: |
            /tmp/api-image.tar
            /tmp/playwright-image.tar
  
  generate-report:
    name: Generate Test Report
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests]
    if: always()
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Download test results
        uses: actions/download-artifact@v3
        with:
          path: test-results-all
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install junit2html
      
      - name: Generate HTML report
        run: |
          mkdir -p test-reports/html
          
          # Combine all JUnit XML files
          find test-results-all -name "*.xml" -type f -exec echo Processing {} \; -exec cp {} test-reports/ \;
          
          # Generate HTML report from all XML files
          for xml_file in test-reports/*.xml; do
            if [ -f "$xml_file" ]; then
              basename=$(basename "$xml_file" .xml)
              junit2html "$xml_file" "test-reports/html/$basename.html"
            fi
          done
          
          # Create index file
          echo "<html><head><title>Test Results</title></head><body>" > test-reports/html/index.html
          echo "<h1>Test Results</h1><ul>" >> test-reports/html/index.html
          for html_file in test-reports/html/*.html; do
            if [ -f "$html_file" ] && [ "$(basename "$html_file")" != "index.html" ]; then
              filename=$(basename "$html_file")
              echo "<li><a href=\"$filename\">$filename</a></li>" >> test-reports/html/index.html
            fi
          done
          echo "</ul></body></html>" >> test-reports/html/index.html
      
      - name: Upload report
        uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: test-reports/html/
      
      - name: Generate summary
        run: |
          echo "### Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Test Type | Status | Details |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|---------|" >> $GITHUB_STEP_SUMMARY
          
          # Unit Tests
          UNIT_STATUS="${{ needs.unit-tests.result }}"
          if [ "$UNIT_STATUS" == "success" ]; then
            echo "| Unit Tests | ✅ Pass | [Details](artifacts/test-reports/unit.html) |" >> $GITHUB_STEP_SUMMARY
          elif [ "$UNIT_STATUS" == "failure" ]; then
            echo "| Unit Tests | ❌ Fail | [Details](artifacts/test-reports/unit.html) |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Unit Tests | ⚠️ Skipped | No results available |" >> $GITHUB_STEP_SUMMARY
          fi
          
          # Integration Tests
          INT_STATUS="${{ needs.integration-tests.result }}"
          if [ "$INT_STATUS" == "success" ]; then
            echo "| Integration Tests | ✅ Pass | [Details](artifacts/test-reports/integration.html) |" >> $GITHUB_STEP_SUMMARY
          elif [ "$INT_STATUS" == "failure" ]; then
            echo "| Integration Tests | ❌ Fail | [Details](artifacts/test-reports/integration.html) |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Integration Tests | ⚠️ Skipped | No results available |" >> $GITHUB_STEP_SUMMARY
          fi
          
          # E2E Tests
          E2E_STATUS="${{ needs.e2e-tests.result }}"
          if [ "$E2E_STATUS" == "success" ]; then
            echo "| E2E Tests | ✅ Pass | [Details](artifacts/test-reports/e2e.html) |" >> $GITHUB_STEP_SUMMARY
          elif [ "$E2E_STATUS" == "failure" ]; then
            echo "| E2E Tests | ❌ Fail | [Details](artifacts/test-reports/e2e.html) |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| E2E Tests | ⚠️ Skipped | No results available |" >> $GITHUB_STEP_SUMMARY
          fi
  
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, build-containers]
    if: |
      github.event_name == 'workflow_dispatch' &&
      (github.event.inputs.environment == 'dev' ||
       github.event.inputs.environment == 'staging' ||
       github.event.inputs.environment == 'production')
    
    environment:
      name: ${{ github.event.inputs.environment }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set environment variables
        run: |
          echo "DEPLOY_ENV=${{ github.event.inputs.environment }}" >> $GITHUB_ENV
      
      - name: Download container images
        uses: actions/download-artifact@v3
        with:
          name: docker-images
          path: /tmp
      
      - name: Load Docker images
        run: |
          docker load --input /tmp/api-image.tar
          docker load --input /tmp/playwright-image.tar
      
      - name: Deploy to environment
        run: |
          echo "Deploying to $DEPLOY_ENV environment"
          
          # Example deployment script - replace with actual deployment logic
          if [ "$DEPLOY_ENV" == "dev" ]; then
            echo "Running development deployment"
            # ./deploy-scripts/deploy-dev.sh
          elif [ "$DEPLOY_ENV" == "staging" ]; then
            echo "Running staging deployment"
            # ./deploy-scripts/deploy-staging.sh
          elif [ "$DEPLOY_ENV" == "production" ]; then
            echo "Running production deployment"
            # ./deploy-scripts/deploy-production.sh
          fi
      
      - name: Verify deployment
        run: |
          echo "Verifying deployment to $DEPLOY_ENV environment"
          # Add verification steps here 