name: Playwright API Tests

on:
  push:
    branches: [ main, development ]
  pull_request:
    branches: [ main, development ]
  # Allow manual trigger
  workflow_dispatch:

jobs:
  playwright-tests:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Create docker network
        run: docker network create dockerwrapper_turdparty_network || true
      
      - name: Start API service container
        run: |
          docker-compose -f docker-compose.yml up -d api
          # Wait for API to be ready
          echo "Waiting for API service to be ready..."
          sleep 10
      
      - name: Set up test environment
        run: |
          # Create persistent test container
          .dockerwrapper/persistent-test-env.sh
          
          # Fix network configuration
          .dockerwrapper/fix-network-config.sh
          
          # Fix container configuration
          .dockerwrapper/fix-persistent-container.sh
          
          # Verify API connectivity
          docker exec turdparty_playwright bash -c "cd /app && node tests/playwright/config/verify-api-connection.js"
      
      - name: Run Playwright API tests
        run: |
          # Run the connectivity test first to verify setup
          docker exec turdparty_playwright bash -c "cd /app && npx playwright test tests/playwright/api-connectivity.spec.js --reporter=list"
          
          # Run all API endpoint tests
          docker exec turdparty_playwright bash -c "cd /app && npx playwright test tests/playwright/ --reporter=html,list"
      
      - name: Upload test report
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: |
            test-results/
            playwright-report/
          retention-days: 30
      
      - name: Display test results summary
        if: always()
        run: |
          echo "Test run complete"
          if [ -f "playwright-report/results.json" ]; then
            echo "Test Results Summary:"
            grep -A5 "stats" playwright-report/results.json || true
          fi
      
      - name: Clean up
        if: always()
        run: |
          docker-compose down
          docker network rm dockerwrapper_turdparty_network || true 