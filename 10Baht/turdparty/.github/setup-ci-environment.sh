#!/bin/bash

# Script to set up the CI environment for running tests
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==================================================${NC}"
echo -e "${BLUE}    Setting up CI environment for testing         ${NC}"
echo -e "${BLUE}==================================================${NC}"

# Create docker network if it doesn't exist
echo -e "${YELLOW}Creating Docker network...${NC}"
docker network create dockerwrapper_turdparty_network || true

# Start API service
echo -e "${YELLOW}Starting API service...${NC}"
docker-compose -f docker-compose.yml up -d api

# Wait for API to be ready
echo -e "${YELLOW}Waiting for API service to be ready (10s)...${NC}"
sleep 10

# Create persistent test container
echo -e "${YELLOW}Creating Playwright test container...${NC}"
.dockerwrapper/persistent-test-env.sh

# Fix network configuration
echo -e "${YELLOW}Configuring container network...${NC}"
.dockerwrapper/fix-network-config.sh

# Fix container configuration
echo -e "${YELLOW}Installing Playwright dependencies...${NC}"
.dockerwrapper/fix-persistent-container.sh

# Create test artifact directories in container
echo -e "${YELLOW}Creating test artifact directories...${NC}"
docker exec turdparty_playwright bash -c "mkdir -p /app/test-results /app/playwright-report && chmod -R 777 /app/test-results /app/playwright-report"

# Export CI environment variables
echo -e "${YELLOW}Setting CI environment variables...${NC}"
docker exec turdparty_playwright bash -c "export CI=true GITHUB_ACTIONS=true CI_PARALLEL_TESTS=true TEST_RETRIES=2"

# Verify API connectivity
echo -e "${YELLOW}Verifying API connectivity...${NC}"
docker exec turdparty_playwright bash -c "cd /app && node tests/playwright/config/verify-api-connection.js" || {
  echo -e "${RED}API connectivity verification failed!${NC}"
  .dockerwrapper/debug-api-connectivity.sh
  echo -e "${RED}Test environment setup failed.${NC}"
  exit 1
}

echo -e "${GREEN}CI environment setup completed successfully!${NC}"
echo -e "${GREEN}Ready to run Playwright tests.${NC}"

# Display environment information
echo -e "${BLUE}==================================================${NC}"
echo -e "${BLUE}    Environment Information                       ${NC}"
echo -e "${BLUE}==================================================${NC}"
echo -e "${GREEN}API Container:${NC}"
docker ps -f name=turdparty_api --format "ID: {{.ID}} - Status: {{.Status}}"
echo -e "${GREEN}Playwright Container:${NC}"
docker ps -f name=turdparty_playwright --format "ID: {{.ID}} - Status: {{.Status}}"
echo -e "${BLUE}==================================================${NC}" 