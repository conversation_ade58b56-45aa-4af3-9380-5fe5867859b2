{"name": "Docker Protection Rules", "description": "Rules to protect Docker configuration files from unauthorized modification", "version": "1.0.0", "rules": [{"type": "path_restriction", "id": "docker-wrapper-protection", "description": "Prevents modification of .dockerwrapper files without explicit permission", "patterns": [".dockerwrapper/**"], "access": "read_only", "message": "The Docker wrapper files are locked for editing. Please explicitly ask for permission to modify these files."}]}