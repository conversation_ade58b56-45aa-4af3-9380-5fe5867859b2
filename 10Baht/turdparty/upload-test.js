// Standalone upload test
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function runTest() {
  console.log('Starting file upload test');
  
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Step 1: Navigate to upload page
    console.log('Navigating to upload page...');
    await page.goto('http://**********:3100/upload', { timeout: 30000 });
    await page.screenshot({ path: '/app/test_screenshots/01-upload-page.png' });
    
    // Step 2: Fill form
    console.log('Filling upload form...');
    await page.locator('textarea[placeholder*="description"]').fill('Test upload from Docker');
    
    // Step 3: Select file
    console.log('Selecting file...');
    await page.locator('input[type="file"]').setInputFiles('/app/test-upload.txt');
    await page.screenshot({ path: '/app/test_screenshots/02-file-selected.png' });
    
    // Step 4: Submit form
    console.log('Submitting form...');
    await page.locator('button:has-text("Upload")').click();
    
    // Step 5: Wait for success message
    console.log('Waiting for upload to complete...');
    await page.waitForSelector('.ant-message-success, .upload-success', { timeout: 30000 });
    await page.screenshot({ path: '/app/test_screenshots/03-upload-success.png' });
    
    // Step 6: Navigate to files page
    console.log('Navigating to files page...');
    await page.goto('http://**********:3100/files', { timeout: 30000 });
    await page.screenshot({ path: '/app/test_screenshots/04-files-page.png' });
    
    console.log('Test completed successfully!');
    return true;
  } catch (error) {
    console.error('Test failed:', error);
    await page.screenshot({ path: '/app/test_screenshots/error-state.png' });
    return false;
  } finally {
    await browser.close();
  }
}

// Run the test
runTest()
  .then(success => {
    if (success) {
      console.log('✅ Test passed!');
      process.exit(0);
    } else {
      console.error('❌ Test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
