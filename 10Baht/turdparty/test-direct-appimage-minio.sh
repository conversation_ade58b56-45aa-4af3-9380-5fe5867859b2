#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing Direct AppImage Upload to MinIO${NC}"
echo -e "${YELLOW}====================================${NC}"

# Configuration
TEST_DIR="/tmp/appimage-direct-test"
APPIMAGE_FILE="${TEST_DIR}/test-appimage.AppImage"
MINIO_HOST="localhost"
MINIO_PORT="9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
BUCKET_NAME="appimage-test"

# Create test directory if it doesn't exist
mkdir -p "${TEST_DIR}"

# Create a dummy AppImage file for testing
echo -e "${YELLOW}Creating dummy AppImage file for testing...${NC}"
cat > ${APPIMAGE_FILE} << 'EOF'
#!/bin/bash
echo "This is a dummy AppImage executable - $(date)"
echo "Environment:"
env
echo "System info:"
uname -a
echo "Current directory: $(pwd)"
echo "AppImage test complete!"
EOF

# Make the dummy AppImage executable
chmod +x ${APPIMAGE_FILE}
echo -e "${GREEN}Created dummy AppImage at ${APPIMAGE_FILE}${NC}"

# Create Python script for direct MinIO test
cat > ${TEST_DIR}/direct_minio_test.py << 'EOF'
#!/usr/bin/env python3
"""
Test script for direct AppImage upload to MinIO, without using the API.

Workflow:
1. Connect to MinIO
2. Create test bucket if it doesn't exist
3. Upload AppImage file
4. Generate presigned URL
5. Download the file using the presigned URL
6. Verify the file integrity
"""

import os
import sys
import time
import uuid
import hashlib
import requests
from datetime import datetime, timedelta
from minio import Minio
from minio.error import S3Error

# Configuration
MINIO_HOST = os.environ.get("MINIO_HOST", "localhost")
MINIO_PORT = os.environ.get("MINIO_PORT", "9000")
MINIO_ACCESS_KEY = os.environ.get("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.environ.get("MINIO_SECRET_KEY", "minioadmin")
BUCKET_NAME = os.environ.get("BUCKET_NAME", "appimage-test")
APPIMAGE_FILE = os.environ.get("APPIMAGE_FILE", "/tmp/appimage-direct-test/test-appimage.AppImage")

def log(message):
    """Log with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def get_file_hash(filepath):
    """Calculate MD5 hash of a file"""
    hash_md5 = hashlib.md5()
    with open(filepath, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def main():
    """Main function for direct MinIO test"""
    log("Starting direct MinIO AppImage test...")
    
    # Verify the AppImage file exists
    if not os.path.exists(APPIMAGE_FILE):
        log(f"Error: AppImage file not found at {APPIMAGE_FILE}")
        sys.exit(1)
    
    file_size = os.path.getsize(APPIMAGE_FILE)
    file_hash = get_file_hash(APPIMAGE_FILE)
    log(f"AppImage file: {APPIMAGE_FILE}")
    log(f"File size: {file_size} bytes")
    log(f"File MD5: {file_hash}")
    
    # Create MinIO client
    log(f"Connecting to MinIO at {MINIO_HOST}:{MINIO_PORT}...")
    try:
        client = Minio(
            f"{MINIO_HOST}:{MINIO_PORT}",
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False
        )
        
        # Test connection by listing buckets
        buckets = client.list_buckets()
        log(f"Connected to MinIO! Found {len(buckets)} buckets:")
        for bucket in buckets:
            log(f"  - {bucket.name} (created: {bucket.creation_date})")
        
        # Create test bucket if it doesn't exist
        if not client.bucket_exists(BUCKET_NAME):
            log(f"Creating bucket: {BUCKET_NAME}")
            client.make_bucket(BUCKET_NAME)
        else:
            log(f"Bucket already exists: {BUCKET_NAME}")
        
        # Generate a unique object name
        object_name = f"appimage-{uuid.uuid4()}.AppImage"
        log(f"Uploading AppImage as object: {object_name}")
        
        # Upload the file
        start_time = time.time()
        client.fput_object(
            BUCKET_NAME,
            object_name,
            APPIMAGE_FILE,
            content_type="application/x-executable"
        )
        upload_time = time.time() - start_time
        log(f"Upload completed in {upload_time:.2f} seconds")
        
        # Get the object stats
        stat = client.stat_object(BUCKET_NAME, object_name)
        log(f"Object stats: size={stat.size}, etag={stat.etag}")
        
        # Generate a presigned URL for download
        presigned_url = client.presigned_get_object(
            BUCKET_NAME,
            object_name,
            expires=timedelta(hours=1)
        )
        log(f"Generated presigned URL (valid for 1 hour):")
        log(presigned_url)
        
        # Download the file using the presigned URL
        log("Downloading file using presigned URL...")
        download_path = f"{os.path.dirname(APPIMAGE_FILE)}/downloaded-{os.path.basename(object_name)}"
        response = requests.get(presigned_url)
        
        if response.status_code == 200:
            with open(download_path, "wb") as f:
                f.write(response.content)
            
            downloaded_size = os.path.getsize(download_path)
            downloaded_hash = get_file_hash(download_path)
            
            log(f"Downloaded file: {download_path}")
            log(f"Downloaded size: {downloaded_size} bytes")
            log(f"Downloaded MD5: {downloaded_hash}")
            
            # Make the downloaded file executable
            os.chmod(download_path, 0o755)
            log("Made downloaded file executable")
            
            # Verify integrity
            if file_hash == downloaded_hash:
                log("✅ File integrity verification: SUCCESS")
            else:
                log("❌ File integrity verification: FAILED - hashes don't match")
                sys.exit(1)
            
            # Remove the downloaded file
            os.remove(download_path)
            log("Removed downloaded file")
            
        else:
            log(f"❌ Download failed with status code: {response.status_code}")
            log(response.text)
            sys.exit(1)
        
        # List objects in the bucket
        log(f"Listing objects in bucket {BUCKET_NAME}:")
        objects = client.list_objects(BUCKET_NAME, recursive=True)
        count = 0
        for obj in objects:
            log(f"  - {obj.object_name} (size: {obj.size} bytes, last modified: {obj.last_modified})")
            count += 1
            if count >= 10:
                log(f"... and more (showing first 10 objects only)")
                break
        
        log("\nDirect MinIO AppImage test completed successfully!")
        return True
        
    except S3Error as e:
        log(f"S3Error: {e}")
        sys.exit(1)
    except Exception as e:
        log(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

# Set environment variables
export MINIO_HOST="${MINIO_HOST}"
export MINIO_PORT="${MINIO_PORT}"
export MINIO_ACCESS_KEY="${MINIO_ACCESS_KEY}"
export MINIO_SECRET_KEY="${MINIO_SECRET_KEY}"
export BUCKET_NAME="${BUCKET_NAME}"
export APPIMAGE_FILE="${APPIMAGE_FILE}"

# Install required Python packages if not already installed
echo -e "${YELLOW}Installing required Python packages if needed...${NC}"
python3 -m pip install minio requests --upgrade > /dev/null

# Run the test
echo -e "${YELLOW}Running direct MinIO AppImage test...${NC}"
cd ${TEST_DIR}
python3 ${TEST_DIR}/direct_minio_test.py

# Clean up
echo -e "${YELLOW}Cleaning up test files...${NC}"
rm -f ${APPIMAGE_FILE}
rm -f ${TEST_DIR}/direct_minio_test.py
rmdir ${TEST_DIR} 2>/dev/null || true

echo -e "${GREEN}Test complete!${NC}" 