"""
FastAPI Application Factory
"""
import logging
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse
import os
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_application() -> FastAPI:
    """
    Create and configure FastAPI application
    """
    app = FastAPI(
        title="TurdParty API",
        description="File upload and VM management API",
        version="1.0.0",
        docs_url="/api/v1/docs",
        redoc_url="/api/v1/redoc",
        openapi_url="/api/v1/openapi.json"
    )
    
    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add static files if directory exists
    static_dir = Path("api/static")
    if static_dir.exists():
        app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    
    # Root redirect to docs
    @app.get("/")
    async def root():
        return RedirectResponse(url="/api/v1/docs")
    
    # Health check endpoint
    @app.get("/api/v1/health")
    async def health_check():
        return {
            "status": "healthy",
            "service": "turdparty-api",
            "version": "1.0.0"
        }
    
    # Basic API info endpoint
    @app.get("/api/v1/info")
    async def api_info():
        return {
            "name": "TurdParty API",
            "version": "1.0.0",
            "description": "File upload and VM management API",
            "endpoints": {
                "health": "/api/v1/health",
                "docs": "/api/v1/docs",
                "redoc": "/api/v1/redoc"
            }
        }
    
    logger.info("FastAPI application configured successfully")
    return app
