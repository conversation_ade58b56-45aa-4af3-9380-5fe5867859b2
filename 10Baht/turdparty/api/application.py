"""
FastAPI application module with Flask application mounting.
"""
from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON>outer, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.wsgi import WSGIMiddleware
from fastapi.security import OAuth2Pass<PERSON>RequestForm
from flask import Flask
from api.routes import health, auth, items, users, vagrant, vagrant_vm, vm_injection, mfa # Added vagrant_vm and vm_injection imports
from api.routes import test
from api.routes import storage, minio_status, minio_health, docker, file_upload, docs, file_selection
from api.routes.file_upload import create_file_upload, upload_folder  # Import specific functions for aliases
from api.routes import swagger_ui  # Import our custom Swagger UI router
from api.routes import logs  # Import our logs router
from api.routes import system  # Import our system router
from api.routes import file_to_vm  # Import file-to-vm bridge router
from api.routes import success_criteria  # Import success criteria router
from api.routes import virustotal  # Import virustotal router
from api.routes import static_analysis  # Import static analysis router
import logging
from api.middleware.auth_middleware import AuthMiddleware #Import AuthMiddleware
from api.middleware.mfa_middleware import MFAMiddleware #Import MFAMiddleware
from api.middleware.api_version_middleware import APIVersionMiddleware  # Import our new middleware
from api.middleware.security_headers import SecurityHeadersMiddleware  # Import security headers middleware
from api.core.test_config import test_settings
from api.core.config import settings
from api.core.logging_config import setup_logging

logger = setup_logging()

# Create Flask app for UI
flask_app = Flask(__name__)

@flask_app.route('/')
def flask_root():
    """Root endpoint for the Flask application."""
    return {"status": "ok", "message": "UI Server running"}


def get_application() -> FastAPI:
    """
    Create and configure the FastAPI application with mounted Flask app.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    # Enable test mode to bypass authentication
    if test_settings.is_testing():
        logger.info("Test mode enabled for all requests - authentication bypassed")
    
    app = FastAPI(
        title="TurdParty API",
        description="API for the TurdParty application with versioned endpoints.",
        version="1.0.0",
        docs_url=None,  # Disable default docs
        redoc_url=None,  # Disable default redoc
    )

    # Add security headers middleware first
    app.add_middleware(SecurityHeadersMiddleware)

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Replace with your actual allowed origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add API version middleware to handle redirects from old API paths
    app.add_middleware(
        APIVersionMiddleware,
        api_prefix=settings.API_V1_STR
    )

    # Add root endpoint
    @app.get("/", tags=["root"])
    async def root():
        """Root endpoint for the API."""
        return {
            "status": "ok",
            "documentation": f"{settings.API_V1_STR}/docs/all",
            "message": "Welcome to the TurdParty API"
        }

    # Adding a minimal health check router
    from fastapi import APIRouter
    health_router = APIRouter()
    @health_router.get("/", tags=["health"])
    async def health_check():
        return {"status": "ok"}

    app.include_router(health_router, prefix="/health")
    
    # Add a direct health endpoint at /api/v1/health for tests
    api_health_router = APIRouter()
    @api_health_router.get("/", tags=["health"])
    async def api_health_check():
        return {"status": "ok"}
    
    app.include_router(api_health_router, prefix=f"{settings.API_V1_STR}/health")
    
    # Create main v1 API router
    v1_router = APIRouter()

    # Include all v1 API routes
    v1_router.include_router(auth.router, prefix="/auth", tags=["auth"])
    v1_router.include_router(users.router, prefix="/users", tags=["users"])
    v1_router.include_router(items.router, prefix="/items", tags=["items"])
    v1_router.include_router(vagrant.router, prefix="/vagrant", tags=["vagrant"])
    v1_router.include_router(vagrant_vm.router, prefix="/vagrant-vm", tags=["vagrant-vm"])
    v1_router.include_router(vm_injection.router, prefix="/vm-injection", tags=["vm-injection"])
    v1_router.include_router(mfa.router, prefix="/mfa", tags=["mfa"])
    v1_router.include_router(test.router, prefix="/test", tags=["test"])
    v1_router.include_router(storage.router, prefix="/storage", tags=["storage"])
    v1_router.include_router(minio_status.router, prefix="/minio-status", tags=["minio-status"])
    v1_router.include_router(minio_health.router, prefix="/minio-health", tags=["minio-health"])
    v1_router.include_router(docker.router, prefix="/docker", tags=["docker"])
    v1_router.include_router(file_upload.router, prefix="", tags=["file-upload"])
    v1_router.include_router(docs.router, prefix="/docs", tags=["docs"])
    v1_router.include_router(file_selection.router, prefix="/file-selection", tags=["file-selection"])
    v1_router.include_router(swagger_ui.router, prefix="/docs/all", tags=["docs"])
    v1_router.include_router(logs.router, prefix="/logs", tags=["logs"])
    v1_router.include_router(system.router, prefix="/system", tags=["system"])
    v1_router.include_router(file_to_vm.router, prefix="/file-to-vm", tags=["file-to-vm"])
    v1_router.include_router(success_criteria.router, prefix="/success-criteria", tags=["success-criteria"])
    v1_router.include_router(virustotal.router, prefix="/virustotal", tags=["virustotal"])
    v1_router.include_router(static_analysis.static_analysis_router, prefix="/static_analysis", tags=["static_analysis"])

    # Mount the v1 API router
    app.include_router(v1_router, prefix=settings.API_V1_STR)

    # Mount Flask app for UI
    app.mount("/ui", WSGIMiddleware(flask_app))

    # Add JWT auth middleware
    app.add_middleware(AuthMiddleware, auth_service_getter=lambda: None)

    # Add MFA middleware
    app.add_middleware(MFAMiddleware)

    logger.info("All routes registered successfully")
    logger.info(f"API routes available at {settings.API_V1_STR}")
    logger.info("Flask UI server mounted at /ui")
    return app

# This is needed because the minio_health import depends on the minio client.
# Without this the minio client is not properly loaded and will cause an error.