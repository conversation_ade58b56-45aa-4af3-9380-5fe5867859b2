"""Logging utilities for the application."""

import logging
from typing import Any, Dict, Optional, List
from api.core.logging_config import get_logger

# Re-export the setup_logging function
from api.core.logging_config import setup_logging


def log_event(
    logger: logging.Logger,
    message: str,
    level: int = logging.INFO,
    request_id: Optional[str] = None,
    user_id: Optional[str] = None,
    extra_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log an event with structured data.

    Args:
        logger: Logger instance
        message: Log message
        level: Log level
        request_id: Request ID for correlation
        user_id: User ID for context
        extra_data: Additional data to include in the log
    """
    extra = {}

    if request_id:
        extra["request_id"] = request_id

    if user_id:
        extra["user_id"] = user_id

    if extra_data:
        for key, value in extra_data.items():
            extra[key] = value

    logger.log(level, message, extra=extra)


def log_exception(
    logger: logging.Logger,
    exception: Exception,
    message: str = "An exception occurred",
    request_id: Optional[str] = None,
    user_id: Optional[str] = None
) -> None:
    """
    Log an exception with context.

    Args:
        logger: Logger instance
        exception: Exception to log
        message: Log message
        request_id: Request ID for correlation
        user_id: User ID for context
    """
    extra = {}

    if request_id:
        extra["request_id"] = request_id

    if user_id:
        extra["user_id"] = user_id

    logger.exception(message, exc_info=exception, extra=extra)


def setup_logging(log_level=logging.INFO, log_to_file=True):
    """
    Set up application logging configuration.
    
    Args:
        log_level: The logging level (default: INFO)
        log_to_file: Whether to log to a file (default: True)
    """
    # Create logs directory if it doesn't exist
    if log_to_file and not os.path.exists("logs"):
        os.makedirs("logs")
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Log format
    log_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(log_format)
    root_logger.addHandler(console_handler)
    
    # File handler (if enabled)
    if log_to_file:
        # Regular log file
        file_handler = RotatingFileHandler(
            filename="logs/app.log",
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(log_format)
        root_logger.addHandler(file_handler)
        
        # Error log file
        error_file_handler = RotatingFileHandler(
            filename="logs/error.log",
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
        error_file_handler.setFormatter(log_format)
        error_file_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_file_handler)
    
    # Set SQLAlchemy logging level
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    return root_logger


def check_dependencies() -> Dict[str, List[str]]:
    """
    Check for required dependencies.
    
    Returns:
        Dictionary containing success and failure lists
    """
    logger = logging.getLogger(__name__)
    required_packages = [
        "fastapi", 
        "uvicorn", 
        "sqlalchemy", 
        "alembic", 
        "pydantic", 
        "asyncpg"
    ]
    
    # Add psycopg2 check specifically
    psycopg_packages = ["psycopg2", "psycopg"]
    psycopg_found = False
    
    success = []
    failure = []
    
    logger.info("Checking dependencies...")
    
    # Check regular packages
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            success.append(package)
            logger.info(f"✓ {package} is installed")
        except ImportError:
            failure.append(package)
            logger.error(f"✗ {package} is not installed")
    
    # Check for either psycopg2 or psycopg
    for package in psycopg_packages:
        try:
            __import__(package.replace("-", "_"))
            success.append(package)
            logger.info(f"✓ {package} is installed")
            psycopg_found = True
            break
        except ImportError:
            continue
    
    if not psycopg_found:
        failure.append("psycopg2 or psycopg")
        logger.error("✗ Neither psycopg2 nor psycopg is installed")
    
    result = {"success": success, "failure": failure}
    
    if failure:
        logger.error(f"Missing dependencies: {', '.join(failure)}")
    else:
        logger.info("All dependencies successfully verified")
        
    return {"failure": failure, "success": success}

import sys
import os
from logging.handlers import RotatingFileHandler
from typing import Dict, List, Any, Optional