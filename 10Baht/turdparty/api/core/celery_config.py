"""
Celery configuration for TurdParty.

This module contains the Celery configuration settings.
"""
import os

# Broker settings
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://redis:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://redis:6379/0')

# Task settings
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TIMEZONE = 'UTC'
CELERY_ENABLE_UTC = True

# Task execution settings
CELERY_TASK_ALWAYS_EAGER = False
CELERY_TASK_EAGER_PROPAGATES = True
CELERY_TASK_IGNORE_RESULT = False
CELERY_TASK_STORE_EAGER_RESULT = True

# Worker settings
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000

# Task routing
CELERY_TASK_ROUTES = {
    'api.tasks.file_ops.*': {'queue': 'file_ops'},
    'api.tasks.vm_ops.*': {'queue': 'vm_ops'},
    'api.tasks.analysis.*': {'queue': 'analysis'},
    'api.tasks.monitoring.*': {'queue': 'monitoring'},
}

# Result backend settings
CELERY_RESULT_EXPIRES = 3600  # 1 hour

# Logging
CELERY_WORKER_LOG_FORMAT = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
CELERY_WORKER_TASK_LOG_FORMAT = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

# Beat schedule (for periodic tasks)
CELERY_BEAT_SCHEDULE = {
    'health-check': {
        'task': 'api.tasks.monitoring.health_check',
        'schedule': 60.0,  # Every minute
    },
}
