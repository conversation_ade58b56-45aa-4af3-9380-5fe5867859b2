
"""
Dependency Injection Container module.

This module provides a container for managing dependencies throughout the application.
"""
import logging
from typing import Any, Callable, Dict, Type, TypeVar

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from api.db.session import get_db
from api.db.repositories.item import ItemRepository

logger = logging.getLogger(__name__)

T = TypeVar('T')

class Container:
    """
    Dependency Injection Container.
    
    Manages the creation and lifecycle of service dependencies.
    """
    
    def __init__(self):
        """Initialize the container with empty registry."""
        self._services: Dict[str, Callable[..., Any]] = {}
        self._instances: Dict[str, Any] = {}
        
    def register(self, service_type: Type[T], factory: Callable[..., T]) -> None:
        """
        Register a service factory with the container.
        
        Args:
            service_type: The type of service to register
            factory: A factory function that creates the service
        """
        key = self._get_key(service_type)
        self._services[key] = factory
        logger.debug(f"Registered service: {key}")
        
    def register_instance(self, service_type: Type[T], instance: T) -> None:
        """
        Register a singleton instance with the container.
        
        Args:
            service_type: The type of service to register
            instance: The instance to register
        """
        key = self._get_key(service_type)
        self._instances[key] = instance
        logger.debug(f"Registered instance: {key}")
        
    def resolve(self, service_type: Type[T]) -> T:
        """
        Resolve a service from the container.
        
        Args:
            service_type: The type of service to resolve
            
        Returns:
            An instance of the requested service
            
        Raises:
            KeyError: If the service is not registered
        """
        key = self._get_key(service_type)
        
        # Return existing instance if available
        if key in self._instances:
            return self._instances[key]
        
        # Create new instance if factory is registered
        if key in self._services:
            instance = self._services[key]()
            self._instances[key] = instance
            return instance
        
        raise KeyError(f"Service not registered: {key}")
    
    def _get_key(self, service_type: Type[T]) -> str:
        """
        Get a string key for a service type.
        
        Args:
            service_type: The type to get a key for
            
        Returns:
            A string key for the service type
        """
        return f"{service_type.__module__}.{service_type.__name__}"

# Create global container instance
container = Container()

# Helper functions for FastAPI dependency injection
def get_repository(repo_type: Type[T]) -> Callable:
    """
    Create a FastAPI dependency that provides a repository.
    
    Args:
        repo_type: The repository type to provide
        
    Returns:
        A FastAPI dependency function
    """
    def _get_repo(db: AsyncSession = Depends(get_db)) -> T:
        """Get repository instance with DB session."""
        try:
            key = f"{repo_type.__module__}.{repo_type.__name__}"
            if key in container._services:
                # Call the factory with the DB session
                return container._services[key](db)
            else:
                # Initialize the repository class directly
                return repo_type(db)
        except Exception as e:
            logger.error(f"Error resolving repository {repo_type.__name__}: {e}")
            raise
    
    return _get_repo

# Register common services
def initialize_container():
    """Initialize the container with default services."""
    # Register repositories with their DB session factory
    container.register(ItemRepository, lambda: ItemRepository)
    
    logger.info("Dependency injection container initialized")
    
    return container
