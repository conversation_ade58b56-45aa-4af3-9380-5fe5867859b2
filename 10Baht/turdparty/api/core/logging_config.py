"""Logging configuration for the application."""

import logging
import logging.config
import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from logging.handlers import RotatingFileHandler

# Base directory for logs
LOG_DIR = Path("logs")
LOG_DIR.mkdir(exist_ok=True)

# Log file paths
LOG_FILE = LOG_DIR / "app.log"
ERROR_LOG_FILE = LOG_DIR / "error.log"


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""

    def format(self, record):
        """Format log record as JSON."""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = {
                "type": str(record.exc_info[0].__name__),
                "message": str(record.exc_info[1]),
            }

        # Add extra fields if present
        if hasattr(record, "request_id"):
            log_data["request_id"] = record.request_id

        if hasattr(record, "method"):
            log_data["http"] = {
                "method": record.method,
                "path": getattr(record, "path", None),
                "status_code": getattr(record, "status_code", None),
                "client_host": getattr(record, "client_host", None),
                "user_agent": getattr(record, "user_agent", None),
                "query_params": getattr(record, "query_params", None),
                "process_time_ms": getattr(record, "process_time_ms", None),
            }

        if hasattr(record, "user_id"):
            log_data["user"] = {
                "id": record.user_id,
                "username": getattr(record, "username", None),
            }

        return json.dumps(log_data)


def get_logging_config() -> Dict[str, Any]:
    """
    Get logging configuration dictionary.

    Returns:
        Logging configuration dictionary
    """
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
            },
            "json": {
                "()": "api.core.logging_config.JSONFormatter",
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "standard",
                "stream": "ext://sys.stdout"
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": str(LOG_FILE),
                "maxBytes": 10485760,  # 10 MB
                "backupCount": 5,
                "encoding": "utf8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "json",
                "filename": str(ERROR_LOG_FILE),
                "maxBytes": 10485760,  # 10 MB
                "backupCount": 5,
                "encoding": "utf8"
            }
        },
        "loggers": {
            "": {  # Root logger
                "handlers": ["console", "file", "error_file"],
                "level": "INFO",
                "propagate": True
            },
            "api": {
                "handlers": ["console", "file", "error_file"],
                "level": "DEBUG",
                "propagate": False
            },
            "sqlalchemy.engine": {
                "handlers": ["console", "file"],
                "level": "WARNING",
                "propagate": False
            },
            "uvicorn": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False
            },
            "uvicorn.access": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            }
        }
    }


def setup_logging(log_level: int = logging.INFO, log_to_file: bool = True) -> logging.Logger:
    """
    Set up logging configuration.

    Args:
        log_level: The log level to use
        log_to_file: Whether to log to files or not

    Returns:
        The root logger instance
    """
    # Create log directory if it doesn't exist
    LOG_DIR.mkdir(exist_ok=True)

    # Configure handlers
    handlers = [logging.StreamHandler(sys.stdout)]

    if log_to_file:
        # Regular log file with rotation
        file_handler = RotatingFileHandler(
            LOG_FILE,
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(JSONFormatter())
        handlers.append(file_handler)

        # Error log file with rotation
        error_handler = RotatingFileHandler(
            ERROR_LOG_FILE,
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(JSONFormatter())
        handlers.append(error_handler)

    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
        handlers=handlers
    )

    # Set module-specific log levels
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('alembic').setLevel(logging.WARNING)
    logging.getLogger('fastapi').setLevel(logging.WARNING)
    logging.getLogger('uvicorn').setLevel(logging.WARNING)
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)

    # Get root logger
    root_logger = logging.getLogger()

    # Log startup information
    root_logger.info("Logging system initialized")

    return root_logger


def get_logger(name: str = None) -> logging.Logger:
    """
    Get a logger instance with the given name.

    Args:
        name: The name for the logger

    Returns:
        A configured logger instance
    """
    return logging.getLogger(name)