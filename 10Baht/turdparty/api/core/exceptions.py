
"""Exception handling for the application."""

from typing import Any, Dict, List, Optional, Union
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exception_handlers import http_exception_handler
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel
from starlette.exceptions import HTTPException
import logging
from api.core.logging import get_logger

logger = get_logger(__name__)


class ErrorDetail(BaseModel):
    """Error detail model."""
    
    loc: List[str] = []
    msg: str
    type: str


class ErrorResponse(BaseModel):
    """Standard error response model."""
    
    status_code: int
    message: str
    details: Optional[List[ErrorDetail]] = None
    request_id: Optional[str] = None


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    Handle validation exceptions and return standardized error responses.
    
    Args:
        request: FastAPI request
        exc: Validation exception
        
    Returns:
        JSONResponse with error details
    """
    # Extract request ID
    request_id = request.headers.get("X-Request-ID")
    
    # Log error
    logger.error(
        f"Validation error: {str(exc)}",
        extra={
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method,
            "errors": exc.errors()
        }
    )
    
    # Create error details
    details = []
    for error in exc.errors():
        details.append(
            ErrorDetail(
                loc=[".".join(str(loc) for loc in error["loc"])],
                msg=error["msg"],
                type=error["type"]
            )
        )
    
    # Create response
    error_response = ErrorResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message="Validation error",
        details=details,
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response.dict()
    )


async def http_exception_handler(request: Request, exc: HTTPException):
    """
    Handle HTTP exceptions and return standardized error responses.
    
    Args:
        request: FastAPI request
        exc: HTTP exception
        
    Returns:
        JSONResponse with error details
    """
    # Extract request ID
    request_id = request.headers.get("X-Request-ID")
    
    # Log error
    logger.error(
        f"HTTP error {exc.status_code}: {exc.detail}",
        extra={
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method,
            "status_code": exc.status_code
        }
    )
    
    # Create response
    error_response = ErrorResponse(
        status_code=exc.status_code,
        message=str(exc.detail),
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict(),
        headers=getattr(exc, "headers", None)
    )


async def general_exception_handler(request: Request, exc: Exception):
    """
    Handle all other exceptions and return standardized error responses.
    
    Args:
        request: FastAPI request
        exc: Exception
        
    Returns:
        JSONResponse with error details
    """
    # Extract request ID
    request_id = request.headers.get("X-Request-ID")
    
    # Log error
    logger.exception(
        f"Unhandled exception: {str(exc)}",
        extra={
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    # Create response
    error_response = ErrorResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message="Internal server error",
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response.dict()
    )


class APIError(Exception):
    """Base API error class."""
    
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    message: str = "An unexpected error occurred"
    
    def __init__(self, message: Optional[str] = None, details: Optional[Any] = None):
        """
        Initialize API error.
        
        Args:
            message: Error message
            details: Error details
        """
        self.message = message or self.message
        self.details = details
        super().__init__(self.message)


class NotFoundError(APIError):
    """Resource not found error."""
    
    status_code = status.HTTP_404_NOT_FOUND
    message = "Resource not found"


class ServiceError(APIError):
    """Service communication or processing error."""
    
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    message = "Service error occurred"


class ValidationError(APIError):
    """Validation error."""
    
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    message = "Validation error"


class DatabaseError(APIError):
    """Database error."""
    
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    message = "Database error"


class AuthenticationError(APIError):
    """Authentication error."""
    
    status_code = status.HTTP_401_UNAUTHORIZED
    message = "Authentication error"


class PermissionError(APIError):
    """Permission error."""
    
    status_code = status.HTTP_403_FORBIDDEN
    message = "Permission denied"


async def api_error_handler(request: Request, exc: APIError):
    """
    Handle API errors and return standardized error responses.
    
    Args:
        request: FastAPI request
        exc: API error
        
    Returns:
        JSONResponse with error details
    """
    # Extract request ID
    request_id = request.headers.get("X-Request-ID")
    
    # Log error
    logger.error(
        f"API error {exc.status_code}: {exc.message}",
        extra={
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method,
            "status_code": exc.status_code,
            "details": exc.details
        }
    )
    
    # Create response
    error_response = ErrorResponse(
        status_code=exc.status_code,
        message=exc.message,
        request_id=request_id
    )
    
    # Add details if available
    if exc.details:
        if isinstance(exc.details, list):
            error_response.details = [
                ErrorDetail(msg=str(detail), type="api_error")
                for detail in exc.details
            ]
        else:
            error_response.details = [
                ErrorDetail(msg=str(exc.details), type="api_error")
            ]
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict()
    )
