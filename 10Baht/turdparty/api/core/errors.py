
"""Error handling utilities for the API."""

import logging
from typing import Dict, Any, Optional, Type, List, Union
from fastapi import Request, status

class ResourceNotFoundException(Exception):
    """Exception raised when a requested resource is not found."""
    pass

class ValidationException(Exception):
    """Exception raised when data validation fails."""
    def __init__(self, message: str, errors: Optional[Dict[str, Any]] = None):
        self.message = message
        self.errors = errors or {}
        super().__init__(self.message)
    
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    error_code = "validation_error"
from fastapi.responses import J<PERSON>NResponse
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel, Field
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from uuid import UUID

logger = logging.getLogger(__name__)

class ErrorDetail(BaseModel):
    """Error detail model for API responses."""
    loc: List[str] = Field(default_factory=list, description="Location of the error")
    msg: str = Field(..., description="Error message")
    type: str = Field(..., description="Error type")


class ErrorResponse(BaseModel):
    """Standard error response model."""
    status_code: int = Field(..., description="HTTP status code")
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[List[ErrorDetail]] = Field(default=None, description="Detailed error information")
    request_id: Optional[str] = Field(default=None, description="Request ID for tracking")


class APIError(Exception):
    """Base exception for API errors."""
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_code: str = "internal_server_error"
    message: str = "An unexpected error occurred"
    
    def __init__(
        self, 
        message: Optional[str] = None,
        status_code: Optional[int] = None,
        error_code: Optional[str] = None,
        details: Optional[List[Dict[str, Any]]] = None
    ):
        if message:
            self.message = message
        if status_code:
            self.status_code = status_code
        if error_code:
            self.error_code = error_code
        self.details = details
        super().__init__(self.message)


class NotFoundError(APIError):
    """Exception for resource not found errors."""
    status_code = status.HTTP_404_NOT_FOUND
    error_code = "not_found"
    message = "Resource not found"
    
    def __init__(
        self, 
        resource_type: str, 
        resource_id: Union[str, int, UUID],
        message: Optional[str] = None
    ):
        detail_message = message or f"{resource_type} with id {resource_id} not found"
        super().__init__(message=detail_message)


class ValidationError(APIError):
    """Exception for validation errors."""
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    error_code = "validation_error"
    message = "Validation error"


class DatabaseError(APIError):
    """Exception for database errors."""
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_code = "database_error"
    message = "Database error occurred"


class ConflictError(APIError):
    """Exception for conflict errors."""
    status_code = status.HTTP_409_CONFLICT
    error_code = "conflict"
    message = "Resource conflict"


# Error handlers
async def api_error_handler(request: Request, exc: APIError) -> JSONResponse:
    """Handle API errors."""
    error_details = None
    if hasattr(exc, 'details') and exc.details:
        error_details = [
            ErrorDetail(loc=detail.get("loc", []), msg=detail.get("msg", ""), type=detail.get("type", ""))
            for detail in exc.details
        ]
    
    error_response = ErrorResponse(
        status_code=exc.status_code,
        error=exc.error_code,
        message=exc.message,
        details=error_details,
        request_id=request.headers.get("X-Request-ID")
    )
    
    # Log the error
    log_level = logging.ERROR if exc.status_code >= 500 else logging.WARNING
    logger.log(
        log_level,
        f"API Error: {exc.error_code} - {exc.message}",
        extra={
            "status_code": exc.status_code,
            "error_code": exc.error_code,
            "request_id": request.headers.get("X-Request-ID"),
            "path": request.url.path,
            "method": request.method,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump()
    )


async def validation_error_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle validation errors from Pydantic models."""
    details = []
    for error in exc.errors():
        details.append(
            ErrorDetail(
                loc=error.get("loc", []),
                msg=error.get("msg", ""),
                type=error.get("type", "")
            )
        )
    
    error_response = ErrorResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        error="validation_error",
        message="Validation error",
        details=details,
        request_id=request.headers.get("X-Request-ID")
    )
    
    # Log the validation error
    logger.warning(
        "Validation Error",
        extra={
            "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "error_code": "validation_error",
            "request_id": request.headers.get("X-Request-ID"),
            "path": request.url.path,
            "method": request.method,
            "errors": [error.model_dump() for error in details]
        }
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response.model_dump()
    )


async def sqlalchemy_error_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """Handle SQLAlchemy errors."""
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_code = "database_error"
    message = "A database error occurred"
    
    # Handle specific database errors
    if isinstance(exc, IntegrityError):
        # Check for common integrity constraint violations
        error_message = str(exc).lower()
        if "unique constraint" in error_message or "unique violation" in error_message:
            status_code = status.HTTP_409_CONFLICT
            error_code = "unique_violation"
            message = "A record with the same unique fields already exists"
        elif "foreign key constraint" in error_message:
            status_code = status.HTTP_400_BAD_REQUEST
            error_code = "foreign_key_violation"
            message = "Referenced record does not exist"
        else:
            status_code = status.HTTP_400_BAD_REQUEST
            error_code = "integrity_error"
            message = "Database integrity error"
    
    error_response = ErrorResponse(
        status_code=status_code,
        error=error_code,
        message=message,
        request_id=request.headers.get("X-Request-ID")
    )
    
    # Log the database error
    logger.error(
        f"Database Error: {error_code} - {message}",
        extra={
            "status_code": status_code,
            "error_code": error_code,
            "request_id": request.headers.get("X-Request-ID"),
            "path": request.url.path,
            "method": request.method,
            "exception": str(exc),
        },
        exc_info=True
    )
    
    return JSONResponse(
        status_code=status_code,
        content=error_response.model_dump()
    )


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle any unhandled exceptions."""
    error_response = ErrorResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error="internal_server_error",
        message="An unexpected error occurred",
        request_id=request.headers.get("X-Request-ID")
    )
    
    # Log the unexpected error
    logger.error(
        f"Unhandled Exception: {str(exc)}",
        extra={
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
            "error_code": "internal_server_error",
            "request_id": request.headers.get("X-Request-ID"),
            "path": request.url.path,
            "method": request.method,
        },
        exc_info=True
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response.model_dump()
    )
"""Error handling module."""
from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class NotFoundError(HTTPException):
    """Exception raised when a resource is not found."""
    
    def __init__(
        self,
        detail: str = "Resource not found",
        headers: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception."""
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            headers=headers,
        )


class BadRequestError(HTTPException):
    """Exception raised for bad requests."""
    
    def __init__(
        self,
        detail: str = "Bad request",
        headers: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception."""
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            headers=headers,
        )


class UnauthorizedError(HTTPException):
    """Exception raised for unauthorized access."""
    
    def __init__(
        self,
        detail: str = "Unauthorized",
        headers: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception."""
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers=headers,
        )


class ForbiddenError(HTTPException):
    """Exception raised for forbidden access."""
    
    def __init__(
        self,
        detail: str = "Forbidden",
        headers: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception."""
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            headers=headers,
        )


class InternalServerError(HTTPException):
    """Exception raised for server errors."""
    
    def __init__(
        self,
        detail: str = "Internal server error",
        headers: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception."""
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            headers=headers,
        )
"""
Core error classes for the application.
"""
from typing import Any, Dict, Optional


class ApiError(Exception):
    """Base class for API-related errors."""
    def __init__(
        self, 
        message: str, 
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details
        super().__init__(self.message)


class AuthError(ApiError):
    """Authentication-related errors."""
    def __init__(
        self, 
        message: str = "Authentication error", 
        status_code: int = 401,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)


class TokenExpiredError(AuthError):
    """Error for expired authentication tokens."""
    def __init__(
        self, 
        message: str = "Token has expired", 
        status_code: int = 401,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)


class DatabaseError(ApiError):
    """Database-related errors."""
    def __init__(
        self, 
        message: str = "Database error", 
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)


class NotFoundError(ApiError):
    """Resource not found errors."""
    def __init__(
        self, 
        message: str = "Resource not found", 
        status_code: int = 404,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)


class ValidationError(ApiError):
    """Validation errors."""
    def __init__(
        self, 
        message: str = "Validation error", 
        status_code: int = 422,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)


class ForbiddenError(ApiError):
    """Permission or access denied errors."""
    def __init__(
        self, 
        message: str = "Access forbidden", 
        status_code: int = 403,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)


class ConfigurationError(ApiError):
    """Configuration-related errors."""
    def __init__(
        self, 
        message: str = "Configuration error", 
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)


class ExternalServiceError(ApiError):
    """External service integration errors."""
    def __init__(
        self, 
        message: str = "External service error", 
        status_code: int = 502,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)
