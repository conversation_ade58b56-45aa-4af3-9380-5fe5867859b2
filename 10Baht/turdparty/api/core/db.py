"""
Database utilities for API endpoints.
"""
import os
import logging
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get DB URL from environment variable
DATABASE_URL = os.environ.get("DATABASE_URL", "********************************************/app")

# Create engine, session, and base
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    """
    Get a database session.
    
    Yields:
        Session: Database session
    """
    db = SessionLocal()
    try:
        logger.debug("Database session created")
        yield db
    finally:
        logger.debug("Closing database session")
        db.close()

# For testing/development
TEST_MODE = os.environ.get("TEST_MODE", "false").lower() in ("true", "1", "yes")

if TEST_MODE:
    logger.warning("DB MODULE: Test mode enabled - using mock database")
    
    class MockDB:
        """Mock database for testing."""
        def __init__(self):
            self.data = {}
            
        def add(self, obj):
            """Add an object to the mock database."""
            self.data[obj.__class__.__name__] = self.data.get(obj.__class__.__name__, [])
            self.data[obj.__class__.__name__].append(obj)
            
        def commit(self):
            """Commit changes to the mock database."""
            logger.debug("Mock commit")
            
        def refresh(self, obj):
            """Refresh an object from the mock database."""
            logger.debug(f"Mock refresh for {obj}")
            
        def query(self, cls):
            """Query the mock database."""
            return self.data.get(cls.__name__, [])
            
        def close(self):
            """Close the mock database connection."""
            logger.debug("Mock close")
    
    # Override get_db to use mock database in test mode
    _mock_db = MockDB()
    
    def get_db_test() -> Generator[MockDB, None, None]:
        """
        Get a mock database session for testing.
        
        Yields:
            MockDB: Mock database session
        """
        logger.debug("Using mock database")
        yield _mock_db 