"""
Core dependencies for the API.
"""
import sys
import logging
from typing import Dict, List, Set, Tu<PERSON>, Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends

from api.db.session import AsyncSessionLocal

logger = logging.getLogger(__name__)

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get database session."""
    async_session = AsyncSessionLocal()
    try:
        logger.debug("Async database session started")
        yield async_session
        await async_session.commit()
    except Exception as e:
        logger.error(f"Error during async database session: {str(e)}")
        await async_session.rollback()
        raise
    finally:
        logger.debug("Async database session closed")
        await async_session.close()

# Required dependencies for the application
REQUIRED_DEPS = {
    "fastapi": ">=0.95.0",
    "uvicorn": ">=0.22.0",
    "sqlalchemy": ">=2.0.0",
    "pydantic": ">=2.0.0",
    "python-jose": ">=3.3.0",
    "passlib": ">=1.7.4",
    "bcrypt": ">=4.0.0",
    "python-multipart": ">=0.0.5",
    "email-validator": ">=2.0.0",
    "psycopg2-binary": ">=2.9.0",
}

def check_module_exists(module_name: str) -> bool:
    """
    Check if a module exists.
    
    Args:
        module_name: Name of the module to check
        
    Returns:
        bool: True if the module exists, False otherwise
    """
    try:
        __import__(module_name)
        return True
    except ImportError:
        return False

def check_module_version(module_name: str, version_constraint: str) -> Tuple[bool, Optional[str]]:
    """
    Check if a module meets a version constraint.
    
    Args:
        module_name: Name of the module to check
        version_constraint: Version constraint (e.g. ">=1.0.0")
        
    Returns:
        Tuple[bool, Optional[str]]: (True, version) if the module meets the constraint,
                                   (False, version) if it doesn't,
                                   (False, None) if the module doesn't exist
    """
    if not check_module_exists(module_name):
        return False, None
        
    try:
        import pkg_resources
        
        # Get the installed version
        installed_version = pkg_resources.get_distribution(module_name).version
        
        # Check if the version meets the constraint
        requirement = f"{module_name}{version_constraint}"
        pkg_resources.require(requirement)
        
        return True, installed_version
    except pkg_resources.DistributionNotFound:
        return False, None
    except pkg_resources.VersionConflict:
        return False, installed_version
    except Exception as e:
        logger.error(f"Error checking version for {module_name}: {str(e)}")
        return False, None

def check_dependencies(dependency_set: Dict[str, str] = None) -> Dict[str, List[str]]:
    """
    Check if required dependencies are installed.
    
    Args:
        dependency_set: Dictionary of dependencies to check (defaults to REQUIRED_DEPS)
        
    Returns:
        Dict[str, List[str]]: Dictionary with success and failure lists
    """
    if dependency_set is None:
        dependency_set = REQUIRED_DEPS
        
    success = []
    failure = []
    
    for dep, version in dependency_set.items():
        meets_requirement, _ = check_module_version(dep, version)
        if meets_requirement:
            success.append(dep)
        else:
            failure.append(dep)
            
    return {
        "success": success,
        "failure": failure
    }

def verify_critical_dependencies() -> bool:
    """
    Verify that critical dependencies are installed.
    
    Returns:
        bool: True if all critical dependencies are installed, False otherwise
    """
    critical_deps = {
        "fastapi": ">=0.95.0",
        "uvicorn": ">=0.22.0",
        "sqlalchemy": ">=2.0.0",
    }
    
    result = check_dependencies(critical_deps)
    return len(result["failure"]) == 0