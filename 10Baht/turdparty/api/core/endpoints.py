"""
Central source of truth for all API endpoints.
This file defines all API endpoints used in the application.
"""
from typing import Callable, Dict, Any, TypeVar, Union

T = TypeVar('T')

class APIEndpoints:
    """Central source of truth for all API endpoints."""
    
    # API version prefix
    PREFIX = "/api/v1"
    
    # File upload endpoints
    FILE_UPLOAD = {
        "BASE": f"{PREFIX}/file_upload",
        "DOWNLOAD": lambda file_id: f"{PREFIX}/file_upload/download/{file_id}",
        "FOLDER": f"{PREFIX}/file_upload/folder",
        "UPLOAD": f"{PREFIX}/file_upload/upload",
    }
    
    # Authentication endpoints
    AUTH = {
        "LOGIN": f"{PREFIX}/auth/login",
        "REFRESH": f"{PREFIX}/auth/refresh",
        "TEST_TOKEN": f"{PREFIX}/auth/test-token",
        "VERIFY": f"{PREFIX}/auth/verify",
    }
    
    # File selection endpoints
    FILE_SELECTION = {
        "BASE": f"{PREFIX}/file_selection",
        "BY_ID": lambda id: f"{PREFIX}/file_selection/{id}",
    }
    
    # Vagrant VM endpoints
    VAGRANT_VM = {
        "BASE": f"{PREFIX}/vagrant_vm",
        "BY_ID": lambda id: f"{PREFIX}/vagrant_vm/{id}",
        "ACTION": lambda id: f"{PREFIX}/vagrant_vm/{id}/action",
        "LOGS": lambda id: f"{PREFIX}/vagrant_vm/{id}/logs",
        "RESOURCES": lambda id: f"{PREFIX}/vagrant_vm/{id}/resources",
        "TEMPLATES": f"{PREFIX}/vagrant_vm/templates",
        "OS_TEMPLATES": f"{PREFIX}/vagrant_vm/os_templates",
    }
    
    # VM injection endpoints
    VM_INJECTION = {
        "BASE": f"{PREFIX}/vm_injection",
        "BY_ID": lambda id: f"{PREFIX}/vm_injection/{id}",
        "RETRY": lambda id: f"{PREFIX}/vm_injection/{id}/retry",
    }
    
    # Health endpoints
    HEALTH = {
        "BASE": f"{PREFIX}/health",
        "SYSTEM_STATUS": f"{PREFIX}/health/system-status",
        "SERVICE": lambda service: f"{PREFIX}/health/service/{service}",
        "TEST_RUNS": f"{PREFIX}/health/test-runs",
    }
    
    # Documentation endpoints
    DOCS = {
        "BASE": f"{PREFIX}/docs",
        "ALL": f"{PREFIX}/docs/all",
        "VIEW": lambda path: f"{PREFIX}/docs/view/{path}",
    }
    
    # Logs endpoints
    LOGS = {
        "UI_ERROR": f"{PREFIX}/logs/ui-error",
        "PERFORMANCE": f"{PREFIX}/logs/performance",
        "PERFORMANCE_SUMMARY": f"{PREFIX}/logs/performance/summary",
    }

    @classmethod
    def get_path(cls, endpoint_group: Dict[str, Union[str, Callable[[Any], str]]], key: str, *args: Any) -> str:
        """
        Get the path for a specific endpoint.
        
        Args:
            endpoint_group: The endpoint group (e.g., FILE_UPLOAD)
            key: The key within the endpoint group
            *args: Arguments to pass to the endpoint function if it's callable
            
        Returns:
            str: The full path for the endpoint
        """
        endpoint = endpoint_group.get(key)
        if endpoint is None:
            raise ValueError(f"Endpoint {key} not found in endpoint group")
        
        if callable(endpoint):
            if not args:
                raise ValueError(f"Endpoint {key} is callable but no arguments were provided")
            return endpoint(*args)
        
        return endpoint 