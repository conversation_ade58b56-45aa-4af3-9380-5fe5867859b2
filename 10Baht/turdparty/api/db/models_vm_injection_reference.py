"""
REFERENCE ONLY: Database models for VM injection feature.
This is a reference file. Integrate the model into your actual models.py file.
"""
from sqlalchemy import Column, String, Text, ForeignKey, Integer, Boolean, DateTime, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
import uuid
import datetime
from api.db.base import Base
from api.models.vm_injection import InjectionStatus

class VmInjection(Base):
    """VM injection database model"""
    __tablename__ = "vm_injections"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    vagrant_vm_id = Column(UUID(as_uuid=True), ForeignKey("vagrant_vms.id"), nullable=False)
    file_selection_id = Column(UUID(as_uuid=True), ForeignKey("file_selections.id"), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(Enum(InjectionStatus), nullable=False, default=InjectionStatus.PENDING)
    additional_command = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    log_output = Column(Text, nullable=True)
    created_on = Column(DateTime, default=datetime.datetime.utcnow, nullable=False)
    modified_on = Column(DateTime, onupdate=datetime.datetime.utcnow)
    completed_on = Column(DateTime, nullable=True)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Define relationships
    owner = relationship("User", back_populates="vm_injections")
    vagrant_vm = relationship("VagrantVM", back_populates="vm_injections")
    file_selection = relationship("FileSelection", back_populates="vm_injections")
    
    def __repr__(self):
        return f"<VmInjection(id={self.id}, status={self.status})>"
