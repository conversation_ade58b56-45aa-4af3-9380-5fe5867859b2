"""
REFERENCE ONLY: Database models for Vagrant VM feature.
This is a reference file. Integrate the model into your actual models.py file.
"""
from sqlalchemy import Column, String, Text, ForeignKey, Integer, Boolean, DateTime, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
import uuid
import datetime
from api.db.base import Base
from api.models.vagrant_vm import VMStatus, VMTemplate

class VagrantVM(Base):
    """Vagrant VM database model"""
    __tablename__ = "vagrant_vms"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    template = Column(Enum(VMTemplate), nullable=False)
    memory_mb = Column(Integer, nullable=False, default=1024)
    cpus = Column(Integer, nullable=False, default=1)
    disk_gb = Column(Integer, nullable=False, default=20)
    status = Column(Enum(VMStatus), nullable=False, default=VMStatus.PENDING)
    ip_address = Column(String, nullable=True)
    ssh_port = Column(Integer, nullable=True)
    vagrant_id = Column(String, nullable=True)
    vagrant_data = Column(JSONB, nullable=True)
    custom_vagrantfile = Column(Text, nullable=True)
    provision_script = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    last_action = Column(String, nullable=True)
    last_action_time = Column(DateTime, nullable=True)
    created_on = Column(DateTime, default=datetime.datetime.utcnow, nullable=False)
    modified_on = Column(DateTime, onupdate=datetime.datetime.utcnow)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Define relationships
    owner = relationship("User", back_populates="vagrant_vms")
    
    # Relationship with VM injections
    vm_injections = relationship("VmInjection", back_populates="vagrant_vm", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<VagrantVM(id={self.id}, name={self.name}, status={self.status})>"
