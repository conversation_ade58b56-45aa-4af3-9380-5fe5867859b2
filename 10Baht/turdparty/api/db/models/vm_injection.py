"""
VM Injection database model
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Text, Boolean, Index, CheckConstraint, PrimaryKeyConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, validates

from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin


class VMInjection(BaseModel, ValidationMixin, HistoryMixin):
    """VM Injection model for injecting files into VMs."""
    __tablename__ = "vm_injections"
    
    __table_args__ = (
        PrimaryKeyConstraint('id', name='pk_vm_injections'),
        Index('ix_vm_injections_vagrant_vm_id', 'vagrant_vm_id'),
        Index('ix_vm_injections_owner_id', 'owner_id'),
        CheckConstraint('LENGTH(status) BETWEEN 1 AND 20', name='ck_status_length')
    )
    
    # Relationships
    vagrant_vm_id = Column(UUID(as_uuid=True), ForeignKey("vagrant_vms.id", ondelete="CASCADE", onupdate="CASCADE"), 
                           nullable=False)
    file_selection_id = Column(UUID(as_uuid=True), ForeignKey("file_selections.id", ondelete="CASCADE", onupdate="CASCADE"), 
                              nullable=False)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE", onupdate="CASCADE"), 
                       nullable=False)
    
    # Injection details
    description = Column(Text, nullable=True)
    status = Column(String(20), nullable=False, default="pending")
    additional_command = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    completed_on = Column(DateTime, nullable=True)
    
    # Relationships
    owner = relationship("User", back_populates="vm_injections", lazy="joined")
    vagrant_vm = relationship("VagrantVM", back_populates="vm_injections", lazy="joined")
    file_selection = relationship("FileSelection", back_populates="vm_injections", lazy="joined")
    
    # Validation
    @validates('status')
    def validate_status(self, key: str, value: str) -> str:
        """Validate injection status."""
        valid_statuses = ["pending", "processing", "completed", "failed"]
        if value not in valid_statuses:
            raise ValueError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
        return value
    
    def __repr__(self) -> str:
        """Return string representation of the VM injection."""
        return f"<VMInjection(id={self.id}, status={self.status})>"
    
    # Status management methods
    def mark_processing(self) -> None:
        """Mark the injection as being processed."""
        self.status = "processing"
        self.modified_on = datetime.utcnow()
    
    def mark_completed(self) -> None:
        """Mark the injection as completed."""
        self.status = "completed"
        self.completed_on = datetime.utcnow()
        self.modified_on = datetime.utcnow()
    
    def mark_failed(self, error_message: str) -> None:
        """Mark the injection as failed with an error message."""
        self.status = "failed"
        self.error_message = error_message
        self.modified_on = datetime.utcnow() 