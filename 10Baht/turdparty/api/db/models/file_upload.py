"""
Database models for file_upload feature.
"""
from sqlalchemy import Column, String, Text, ForeignKey, Integer, Boolean, DateTime, Index, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin

class FileUpload(BaseModel, ValidationMixin, HistoryMixin):
    """File upload database model"""
    __tablename__ = "file_uploads"
    
    __table_args__ = (
        Index('ix_file_uploads_owner_id', 'owner_id'),
        Index('ix_file_uploads_vm_id', 'vm_id'),
        Index('ix_file_uploads_folder_id', 'folder_id'),
        CheckConstraint('LENGTH(filename) BETWEEN 1 AND 255', name='ck_filename_length'),
        CheckConstraint('LENGTH(file_hash) = 64', name='ck_file_hash_length')
    )
    
    filename = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    file_hash = Column(String(64), nullable=False)
    file_path = Column(String(255), nullable=False)
    file_folder_path = Column(String(255), nullable=True)  # Path within the folder structure
    folder_id = Column(String(100), nullable=True)  # Group files by folder
    description = Column(Text, nullable=True)
    download_url = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Use a default value that matches our test user
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    
    # Add VM relationship - fixed to use correct table name
    vm_id = Column(UUID(as_uuid=True), ForeignKey("vagrant_vms.id", ondelete="SET NULL"), nullable=True)
    
    # Define relationships
    owner = relationship("User", back_populates="file_uploads", lazy="joined")
    vagrant_vm = relationship("VagrantVM", back_populates="file_uploads", lazy="joined")
    selections = relationship("FileSelection", back_populates="file_upload", cascade="all, delete-orphan", lazy="selectin")
    
    def __repr__(self):
        return f"<FileUpload(id={self.id}, filename={self.filename})>" 