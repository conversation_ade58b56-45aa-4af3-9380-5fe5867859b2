"""User database model."""

import re
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, <PERSON>, <PERSON>ole<PERSON>, Integer, DateTime, ForeignKey, PrimaryKeyConstraint, Index, CheckConstraint, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, validates
from datetime import datetime
import bcrypt
from uuid import UUID as UUIDType

from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin


class User(BaseModel, ValidationMixin, HistoryMixin):
    """User model for storing user information."""

    __tablename__ = "users"
    
    __table_args__ = (
        PrimaryKeyConstraint('id', name='pk_users'),
        UniqueConstraint('username', name='uq_users_username'),
        UniqueConstraint('email', name='uq_users_email'),
        CheckConstraint('LENGTH(username) BETWEEN 3 AND 50', name='ck_username_length'),
        CheckConstraint('LENGTH(password_hash) > 10', name='ck_password_hash_length'),
        CheckConstraint('LENG<PERSON>(full_name) BETWEEN 1 AND 100', name='ck_full_name_length'),
        CheckConstraint('LENGTH(roles) BETWEEN 1 AND 255', name='ck_roles_length'),
        CheckConstraint('LENGTH(mfa_secret) = 32', name='ck_mfa_secret_length'),
        Index('ix_users_username_email', 'username', 'email')
    )
    
    # User identification and authentication
    username = Column(String(50), nullable=False)
    email = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    password_hash = Column(String(255), nullable=False)
    
    # Account status
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # Role management - a simple approach using roles as a string list
    roles = Column(String(255), nullable=False, default="user")
    
    # MFA support
    mfa_enabled = Column(Boolean, default=False, nullable=False)
    mfa_secret = Column(String(32), nullable=True)
    
    # Account security
    last_login = Column(DateTime, nullable=True)
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime, nullable=True)
    
    # Relationships
    sessions = relationship("Session", back_populates="user", cascade="all, delete-orphan", lazy="selectin")
    vagrant_vms = relationship("VagrantVM", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    vm_injections = relationship("VMInjection", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    file_uploads = relationship("FileUpload", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    file_selections = relationship("FileSelection", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    items = relationship("Item", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    hash_reports = relationship("HashReport", back_populates="owner", cascade="all, delete-orphan", lazy="selectin")
    audit_logs = relationship("AuditLog", back_populates="user", cascade="all, delete-orphan", lazy="selectin")
    
    # Password validation and management
    @validates('password_hash')
    def validate_password_hash(self, key: str, value: str) -> str:
        """Validate password hash."""
        if not value or len(value) < 10:
            raise ValueError("Invalid password hash")
        return value
    
    @classmethod
    def hash_password(cls, password: str) -> str:
        """Hash a password for storing."""
        if not password or len(password) < 8:
            raise ValueError("Password must be at least 8 characters")
            
        # Check password complexity
        if not re.search(r"[A-Z]", password):
            raise ValueError("Password must contain at least one uppercase letter")
            
        if not re.search(r"[a-z]", password):
            raise ValueError("Password must contain at least one lowercase letter")
            
        if not re.search(r"[0-9]", password):
            raise ValueError("Password must contain at least one digit")
            
        if not re.search(r"[^a-zA-Z0-9]", password):
            raise ValueError("Password must contain at least one special character")
            
        # Generate salt and hash the password
        password_bytes = password.encode('utf-8')
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password_bytes, salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str) -> bool:
        """Verify a password against the stored hash."""
        password_bytes = password.encode('utf-8')
        hash_bytes = self.password_hash.encode('utf-8')
        return bcrypt.checkpw(password_bytes, hash_bytes)
    
    def update_password(self, password: str) -> None:
        """Update the user's password."""
        self.password_hash = self.hash_password(password)
    
    # Role management
    def get_roles(self) -> List[str]:
        """Get the user's roles as a list."""
        return self.roles.split(',') if self.roles else []
    
    def set_roles(self, roles: List[str]) -> None:
        """Set the user's roles from a list."""
        self.roles = ','.join(roles)
    
    def add_role(self, role: str) -> None:
        """Add a role to the user."""
        roles = self.get_roles()
        if role not in roles:
            roles.append(role)
            self.set_roles(roles)
    
    def remove_role(self, role: str) -> None:
        """Remove a role from the user."""
        roles = self.get_roles()
        if role in roles:
            roles.remove(role)
            self.set_roles(roles)
    
    def has_role(self, role: str) -> bool:
        """Check if the user has a specific role."""
        return role in self.get_roles()
    
    # Account security methods
    def increment_failed_login(self) -> None:
        """Increment the failed login counter and lock account if necessary."""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:  # Lock after 5 failed attempts
            from datetime import timedelta
            self.locked_until = datetime.utcnow() + timedelta(minutes=30)
    
    def reset_failed_login(self) -> None:
        """Reset the failed login counter."""
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def is_locked(self) -> bool:
        """Check if the account is locked."""
        if not self.locked_until:
            return False
        return self.locked_until > datetime.utcnow()
    
    def record_login(self) -> None:
        """Record a successful login."""
        self.last_login = datetime.utcnow()
        self.reset_failed_login()
    
    def __repr__(self) -> str:
        """Return string representation of the user."""
        return f"<User(id={self.id}, username={self.username}, email={self.email}, is_active={self.is_active})>"