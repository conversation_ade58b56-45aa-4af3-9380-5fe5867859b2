"""
Database models for session feature.
"""
from sqlalchemy import <PERSON>umn, String, Text, Foreign<PERSON>ey, Integer, Boolean, DateTime, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
import datetime
from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin

class Session(BaseModel, ValidationMixin, HistoryMixin):
    """Session database model"""
    __tablename__ = "sessions"
    
    __table_args__ = (
        Index('ix_sessions_user_id', 'user_id'),
        Index('ix_sessions_token', 'token')
    )
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token = Column(String(255), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Define relationships
    user = relationship("User", back_populates="sessions", lazy="joined")
    
    def __repr__(self):
        return f"<Session(id={self.id}, user_id={self.user_id})>"
