"""
Vagrant VM database model
"""
import re
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Text, Boolean, Index, CheckConstraint, PrimaryKeyConstraint
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship, validates

from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin
from api.db.types import VMStatus


class VagrantVM(BaseModel, ValidationMixin, HistoryMixin):
    """Model for Vagrant VM instances."""
    __tablename__ = "vagrant_vms"
    
    __table_args__ = (
        PrimaryKeyConstraint('id', name='pk_vagrant_vms'),
        CheckConstraint('LENGTH(name) BETWEEN 3 AND 100', name='ck_vm_name_length'),
        CheckConstraint('cpus BETWEEN 1 AND 16', name='ck_cpus_range'),
        CheckConstraint('memory_mb BETWEEN 512 AND 32768', name='ck_memory_range'),
        CheckConstraint('disk_gb BETWEEN 1 AND 500', name='ck_disk_range'),
        CheckConstraint('LENGTH(status) BETWEEN 1 AND 20', name='ck_status_length'),
        CheckConstraint('LENGTH(template) BETWEEN 1 AND 100', name='ck_template_length'),
        CheckConstraint('LENGTH(domain) BETWEEN 1 AND 100', name='ck_domain_length'),
        Index('ix_vagrant_vms_owner_id', 'owner_id'),
        Index('ix_vagrant_vms_status', 'status'),
        Index('ix_vagrant_vms_name', 'name')
    )
    
    # VM identification
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    template = Column(String(100), nullable=False)
    
    # VM resources
    memory_mb = Column(Integer, nullable=False, default=1024)
    cpus = Column(Integer, nullable=False, default=1)
    disk_gb = Column(Integer, nullable=False, default=20)
    
    # VM state
    status = Column(String(20), nullable=False, default=VMStatus.CREATED.value)
    ip_address = Column(String(50), nullable=True)
    ssh_port = Column(Integer, nullable=True)
    vagrant_id = Column(String(100), nullable=True)
    
    # VM operations
    last_action = Column(String(50), nullable=True)
    last_action_time = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    domain = Column(String(100), nullable=False, default="TurdParty")
    
    # Resource tracking
    cpu_usage = Column(JSONB, nullable=True, default=dict)
    memory_usage = Column(JSONB, nullable=True, default=dict)
    disk_usage = Column(JSONB, nullable=True, default=dict)
    uptime_seconds = Column(Integer, nullable=False, default=0)
    
    # Ownership
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE", onupdate="CASCADE"), 
                      nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="vagrant_vms", lazy="joined")
    vm_injections = relationship("VMInjection", back_populates="vagrant_vm", cascade="all, delete-orphan", lazy="selectin")
    file_uploads = relationship("FileUpload", back_populates="vagrant_vm", cascade="all, delete-orphan", lazy="selectin")
    
    # Validation
    @validates('name')
    def validate_name(self, key: str, value: str) -> str:
        """Validate VM name."""
        if not value:
            raise ValueError("VM name cannot be empty")
        
        if len(value) < 3:
            raise ValueError("VM name must be at least 3 characters")
        
        if not re.match(r"^[a-zA-Z0-9_-]+$", value):
            raise ValueError("VM name can only contain letters, numbers, underscores, and hyphens")
        
        return value
    
    @validates('status')
    def validate_status(self, key: str, value: str) -> str:
        """Validate VM status."""
        try:
            return VMStatus(value).value
        except ValueError:
            valid_statuses = [s.value for s in VMStatus]
            raise ValueError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
    
    @validates('cpus')
    def validate_cpus(self, key: str, value: int) -> int:
        """Validate CPU count."""
        if value < 1:
            raise ValueError("CPU count must be at least 1")
        if value > 16:
            raise ValueError("CPU count cannot exceed 16")
        return value
    
    @validates('memory_mb')
    def validate_memory(self, key: str, value: int) -> int:
        """Validate memory allocation."""
        if value < 512:
            raise ValueError("Memory must be at least 512 MB")
        if value > 32768:
            raise ValueError("Memory cannot exceed 32 GB (32768 MB)")
        return value
    
    @validates('disk_gb')
    def validate_disk(self, key: str, value: int) -> int:
        """Validate disk allocation."""
        if value < 1:
            raise ValueError("Disk size must be at least 1 GB")
        if value > 500:
            raise ValueError("Disk size cannot exceed 500 GB")
        return value
    
    def __repr__(self) -> str:
        """Return string representation of the VM."""
        return f"<VagrantVM(id={self.id}, name={self.name}, status={self.status})>"
    
    # State management methods
    def start(self) -> None:
        """Start the VM."""
        if self.status not in [VMStatus.CREATED.value, VMStatus.STOPPED.value]:
            raise ValueError(f"Cannot start VM in {self.status} state")
        
        self.status = VMStatus.STARTING.value
        self.last_action = "start"
        self.last_action_time = datetime.utcnow()
        self.error_message = None
    
    def mark_running(self, ip_address: str, ssh_port: int) -> None:
        """Mark the VM as running with the given IP and port."""
        if self.status != VMStatus.STARTING.value:
            raise ValueError(f"Cannot mark VM as running from {self.status} state")
        
        self.status = VMStatus.RUNNING.value
        self.ip_address = ip_address
        self.ssh_port = ssh_port
        self.last_action = "running"
        self.last_action_time = datetime.utcnow()
    
    def stop(self) -> None:
        """Stop the VM."""
        if self.status != VMStatus.RUNNING.value:
            raise ValueError(f"Cannot stop VM in {self.status} state")
        
        self.status = VMStatus.STOPPED.value
        self.ip_address = None
        self.ssh_port = None
        self.last_action = "stop"
        self.last_action_time = datetime.utcnow()
    
    def mark_error(self, error_message: str) -> None:
        """Mark the VM as having an error."""
        self.status = VMStatus.ERROR.value
        self.error_message = error_message
        self.last_action = "error"
        self.last_action_time = datetime.utcnow()
    
    def is_running(self) -> bool:
        """Check if the VM is running."""
        return self.status == VMStatus.RUNNING.value
    
    # Resource tracking methods
    def update_resource_usage(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> None:
        """Update resource usage statistics."""
        timestamp = datetime.utcnow().isoformat()
        
        if not self.cpu_usage:
            self.cpu_usage = {}
        if not self.memory_usage:
            self.memory_usage = {}
        if not self.disk_usage:
            self.disk_usage = {}
        
        # Keep the last 100 readings
        self.cpu_usage[timestamp] = cpu_percent
        self.memory_usage[timestamp] = memory_percent
        self.disk_usage[timestamp] = disk_percent
        
        # Trim to last 100 entries
        if len(self.cpu_usage) > 100:
            # Sort timestamps and keep the most recent 100
            timestamps = sorted(self.cpu_usage.keys())
            for ts in timestamps[:-100]:
                self.cpu_usage.pop(ts, None)
                self.memory_usage.pop(ts, None)
                self.disk_usage.pop(ts, None)
        
        # If the VM is running, update the uptime
        if self.is_running():
            self.uptime_seconds += 60  # Assume updates are every minute
    
    def get_average_cpu_usage(self) -> float:
        """Get the average CPU usage over the recorded period."""
        if not self.cpu_usage:
            return 0.0
        return sum(self.cpu_usage.values()) / len(self.cpu_usage)
    
    def get_average_memory_usage(self) -> float:
        """Get the average memory usage over the recorded period."""
        if not self.memory_usage:
            return 0.0
        return sum(self.memory_usage.values()) / len(self.memory_usage)
    
    def get_uptime_hours(self) -> float:
        """Get the total uptime in hours."""
        return self.uptime_seconds / 3600
    
    def get_formatted_uptime(self) -> str:
        """Get the uptime formatted as a string (e.g., '2d 5h 10m')."""
        seconds = self.uptime_seconds
        days, seconds = divmod(seconds, 86400)
        hours, seconds = divmod(seconds, 3600)
        minutes, seconds = divmod(seconds, 60)
        
        result = []
        if days > 0:
            result.append(f"{days}d")
        if hours > 0 or days > 0:
            result.append(f"{hours}h")
        if minutes > 0 or hours > 0 or days > 0:
            result.append(f"{minutes}m")
        else:
            result.append(f"{seconds}s")
        
        return " ".join(result) 