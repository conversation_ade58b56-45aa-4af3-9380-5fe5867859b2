"""
Database models for file_selection feature.
"""
from sqlalchemy import <PERSON>umn, String, Text, Foreign<PERSON>ey, Integer, Boolean, DateTime, Index, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin

class FileSelection(BaseModel, ValidationMixin, HistoryMixin):
    """File selection database model"""
    __tablename__ = "file_selections"
    
    __table_args__ = (
        Index('ix_file_selections_owner_id', 'owner_id'),
        Index('ix_file_selections_file_upload_id', 'file_upload_id')
    )
    
    file_upload_id = Column(UUID(as_uuid=True), ForeignKey("file_uploads.id", ondelete="CASCADE"), nullable=False)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    is_active = Column(<PERSON><PERSON><PERSON>, default=True, nullable=False)
    
    # Define relationships
    file_upload = relationship("FileUpload", back_populates="selections", lazy="joined")
    owner = relationship("User", back_populates="file_selections", lazy="joined")
    vm_injections = relationship("VMInjection", back_populates="file_selection", cascade="all, delete-orphan", lazy="selectin")
    
    def __repr__(self):
        return f"<FileSelection(id={self.id}, file_upload_id={self.file_upload_id})>" 