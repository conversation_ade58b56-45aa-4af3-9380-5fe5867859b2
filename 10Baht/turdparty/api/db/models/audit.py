"""
Database models for audit feature.
"""
from sqlalchemy import Column, String, Text, ForeignKey, Integer, Boolean, DateTime, Index, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin

class AuditLog(BaseModel, ValidationMixin, HistoryMixin):
    """Audit log database model"""
    __tablename__ = "audit_logs"
    
    __table_args__ = (
        Index('ix_audit_logs_user_id', 'user_id'),
        Index('ix_audit_logs_action', 'action'),
        CheckConstraint('LENGTH(action) BETWEEN 1 AND 100', name='ck_action_length')
    )
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    action = Column(String(100), nullable=False)
    details = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Define relationships
    user = relationship("User", back_populates="audit_logs", lazy="joined")
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, user_id={self.user_id}, action={self.action})>" 