"""
Database models for hash_report feature.
"""
from sqlalchemy import Column, String, Text, ForeignKey, Integer, Boolean, DateTime, Index, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin

class HashReport(BaseModel, ValidationMixin, HistoryMixin):
    """Hash report database model"""
    __tablename__ = "hash_reports"
    
    __table_args__ = (
        Index('ix_hash_reports_owner_id', 'owner_id'),
        Index('ix_hash_reports_file_hash', 'file_hash'),
        CheckConstraint('LENGTH(file_hash) = 64', name='ck_file_hash_length')
    )
    
    file_hash = Column(String(64), nullable=False)
    report_data = Column(Text, nullable=False)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Define relationships
    owner = relationship("User", back_populates="hash_reports", lazy="joined")
    
    def __repr__(self):
        return f"<HashReport(id={self.id}, file_hash={self.file_hash})>"
