"""
REFERENCE ONLY: Database models for file_selection feature.
This is a reference file. Integrate the model into your actual models.py file.
"""
from sqlalchemy import Column, String, Text, ForeignKey, Boolean, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
import datetime
from api.db.base import Base

class FileSelection(Base):
    """File selection database model"""
    __tablename__ = "file_selections"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    file_upload_id = Column(UUID(as_uuid=True), ForeignKey("file_uploads.id"), nullable=False)
    target_path = Column(String, nullable=False)
    permissions = Column(String, nullable=False, default="0644")
    created_on = Column(DateTime, default=datetime.datetime.utcnow, nullable=False)
    modified_on = Column(DateTime, onupdate=datetime.datetime.utcnow)
    owner_id = Column(UUID(as_uuid=True), Foreign<PERSON>ey("users.id"), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Define relationships
    owner = relationship("User", back_populates="file_selections")
    file_upload = relationship("FileUpload", back_populates="selections")
    
    # Relationship with VM injections
    vm_injections = relationship("VmInjection", back_populates="file_selection", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<FileSelection(id={self.id}, name={self.name}, file_upload_id={self.file_upload_id})>"
