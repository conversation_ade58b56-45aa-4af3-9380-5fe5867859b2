"""
REFERENCE ONLY: Database models for file_upload feature.
This is a reference file. Integrate the model into your actual models.py file.
"""
from sqlalchemy import Column, String, Text, ForeignKey, Integer, Boolean, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
import datetime
from api.db.base import Base

class FileUpload(Base):
    """File upload database model"""
    __tablename__ = "file_uploads"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    filename = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String, nullable=False)
    file_hash = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_folder_path = Column(String, nullable=True)  # Path within the folder structure
    folder_id = Column(String, nullable=True, index=True)  # Group files by folder
    description = Column(Text, nullable=True)
    download_url = Column(String, nullable=False)
    created_on = Column(DateTime, default=datetime.datetime.utcnow, nullable=False)
    modified_on = Column(DateTime, onupdate=datetime.datetime.utcnow)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Define relationships
    owner = relationship("User", back_populates="file_uploads")
    
    # Define relationships with other tables
    selections = relationship("FileSelection", back_populates="file_upload", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<FileUpload(id={self.id}, filename={self.filename})>"
