import uuid
import re
from datetime import datetime
from typing import Optional, ClassVar, Dict, Any, Type, List, Union
from sqlalchemy import Column, DateTime, Boolean, String, Text, Integer, ForeignKey, Table, event, MetaData
from sqlalchemy import PrimaryKeyConstraint, UniqueConstraint, CheckConstraint, Index
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship, validates, Query, Session
from sqlalchemy.sql import func

Base = declarative_base()

metadata = MetaData()

model_history = Table(
    'model_history',
    metadata,
    Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
    Column('model', String, nullable=False),
    Column('record_id', UUID(as_uuid=True), nullable=False),
    <PERSON>umn('changes', JSON, nullable=False),
    <PERSON>umn('changed_by_id', UUID(as_uuid=True), nullable=True),
    Column('changed_at', DateTime, nullable=False, default=datetime.utcnow)
)

class BaseModel(Base):
    """Base model for all database models with common fields and functionality."""
    __abstract__ = True
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_on = Column(DateTime, nullable=False, default=datetime.utcnow, server_default=func.now())
    modified_on = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, server_default=func.now())
    deleted_on = Column(DateTime, nullable=True)
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name automatically."""
        return cls.__name__.lower() + 's'
    
    def soft_delete(self) -> None:
        """Mark the record as soft-deleted."""
        self.deleted_on = datetime.utcnow()
    
    @hybrid_property
    def is_deleted(self) -> bool:
        """Check if the record is soft-deleted."""
        return self.deleted_on is not None
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_on = None
    
    @classmethod
    def default_query(cls, query: Optional[Query] = None) -> Query:
        """Return a query that filters out soft-deleted records."""
        q = query or cls.query
        return q.filter(cls.deleted_on.is_(None))


class ValidationMixin:
    """Mixin for field validation."""
    
    @validates('email')
    def validate_email(self, key: str, value: str) -> str:
        """Validate and normalize email addresses."""
        if not value:
            raise ValueError("Email cannot be empty")
        
        if not re.match(r"[^@]+@[^@]+\.[^@]+", value):
            raise ValueError("Invalid email format")
        
        return value.lower().strip()
    
    @validates('username')
    def validate_username(self, key: str, value: str) -> str:
        """Validate username."""
        if not value:
            raise ValueError("Username cannot be empty")
        
        if len(value) < 3:
            raise ValueError("Username must be at least 3 characters")
        
        if not re.match(r"^[a-zA-Z0-9_]+$", value):
            raise ValueError("Username can only contain letters, numbers, and underscores")
        
        return value


class HistoryMixin:
    """Mixin for tracking history of changes to a model."""
    
    @classmethod
    def __declare_last__(cls) -> None:
        """Hook called after mapper setup."""
        event.listen(cls, 'after_update', cls._track_history)
    
    @staticmethod
    def _track_history(mapper: Any, connection: Any, target: Any) -> None:
        """Track changes to the model after update."""
        from sqlalchemy.orm import object_mapper, object_session
        from sqlalchemy.orm.attributes import instance_state
        
        state = instance_state(target)
        changes = {}
        
        for attr in state.attrs:
            hist = attr.history
            if hist.has_changes():
                old_value = hist.deleted[0] if hist.deleted else None
                new_value = hist.added[0] if hist.added else None
                
                # Convert datetime objects to ISO format strings
                if isinstance(old_value, datetime):
                    old_value = old_value.isoformat()
                if isinstance(new_value, datetime):
                    new_value = new_value.isoformat()
                
                changes[attr.key] = {
                    'old': old_value,
                    'new': new_value
                }
        
        if changes:
            # Insert into history table
            session = object_session(target)
            current_user_id = None
            
            # Try to get current user ID if available
            if hasattr(session, 'info') and 'user_id' in session.info:
                current_user_id = session.info['user_id']
            
            connection.execute(
                model_history.insert().values(
                    model=target.__class__.__name__,
                    record_id=target.id,
                    changes=changes,
                    changed_by_id=current_user_id,
                    changed_at=datetime.utcnow()
                )
            )


# Add global filter for soft delete
@event.listens_for(Query, "before_compile", retval=True)
def filter_soft_deleted(query: Query) -> Query:
    """
    Automatically filter out soft-deleted records unless explicitly included.
    
    To include soft-deleted records, use:
    query = query.execution_options(include_deleted=True)
    """
    if query._execution_options.get('include_deleted', False):
        return query
    
    for ent in query.column_descriptions:
        entity = ent['entity']
        if hasattr(entity, 'deleted_on'):
            query = query.filter(entity.deleted_on.is_(None))
    
    return query
