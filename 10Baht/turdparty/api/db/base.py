"""Base module for SQLAlchemy models."""
from sqlalchemy import MetaData
from sqlalchemy.ext.declarative import declarative_base

# Define naming convention for constraints
convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

# Create base class with naming convention
metadata = MetaData(naming_convention=convention)
Base = declarative_base(metadata=metadata)

__all__ = ['Base'] 