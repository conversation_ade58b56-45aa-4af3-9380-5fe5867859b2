"""Base repository implementation."""
from typing import Generic, TypeVar, Type, Dict, List, Any, Optional, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from api.db.base_model import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")

class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    Base repository class that provides common functionality for database operations.
    
    This repository implements basic CRUD operations for models.
    """
    
    def __init__(self, db_session: AsyncSession, model_class: Type[ModelType]):
        """
        Initialize the repository with a database session and model class.
        
        Args:
            db_session: SQLAlchemy async session
            model_class: SQLAlchemy model class
        """
        self.db = db_session
        self.model_class = model_class
    
    async def get_by_id(self, id: Any) -> Optional[ModelType]:
        """
        Get a model instance by ID.
        
        Args:
            id: Primary key ID value
            
        Returns:
            Model instance if found, None otherwise
        """
        query = select(self.model_class).where(self.model_class.id == id)
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_all(self) -> List[ModelType]:
        """
        Get all model instances.
        
        Returns:
            List of model instances
        """
        query = select(self.model_class)
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def get_by_filter(self, **kwargs) -> List[ModelType]:
        """
        Get model instances by filter criteria.
        
        Args:
            **kwargs: Filter criteria as keyword arguments
            
        Returns:
            List of model instances matching the criteria
        """
        query = select(self.model_class)
        for key, value in kwargs.items():
            if hasattr(self.model_class, key):
                query = query.where(getattr(self.model_class, key) == value)
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def create(self, obj_in: Union[CreateSchemaType, Dict[str, Any]]) -> ModelType:
        """
        Create a new model instance.
        
        Args:
            obj_in: Create schema instance or dictionary with model field values
            
        Returns:
            Created model instance
        """
        if isinstance(obj_in, dict):
            obj_data = obj_in
        else:
            obj_data = obj_in.model_dump()
            
        obj = self.model_class(**obj_data)
        self.db.add(obj)
        await self.db.commit()
        await self.db.refresh(obj)
        return obj
    
    async def update(self, id: Any, obj_in: Union[UpdateSchemaType, Dict[str, Any]]) -> Optional[ModelType]:
        """
        Update an existing model instance.
        
        Args:
            id: Primary key ID value
            obj_in: Update schema instance or dictionary with model field values to update
            
        Returns:
            Updated model instance if found, None otherwise
        """
        # Get current instance
        db_obj = await self.get_by_id(id)
        if db_obj is None:
            return None
        
        # Convert input to dictionary if needed
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        # Update instance with new values
        stmt = (
            update(self.model_class)
            .where(self.model_class.id == id)
            .values(**update_data)
            .execution_options(synchronize_session="fetch")
        )
        await self.db.execute(stmt)
        await self.db.commit()
        
        # Refresh and return updated instance
        await self.db.refresh(db_obj)
        return db_obj
    
    async def delete(self, id: Any) -> bool:
        """
        Delete a model instance by ID.
        
        Args:
            id: Primary key ID value
            
        Returns:
            True if deleted, False if not found
        """
        # Check if instance exists
        db_obj = await self.get_by_id(id)
        if db_obj is None:
            return False
        
        # Delete the instance
        stmt = (
            delete(self.model_class)
            .where(self.model_class.id == id)
            .execution_options(synchronize_session="fetch")
        )
        await self.db.execute(stmt)
        await self.db.commit()
        return True
