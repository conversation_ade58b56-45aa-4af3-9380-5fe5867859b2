"""User repository implementation."""
import logging
from typing import Any, Dict, List, Optional, Union

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from api.core.security import get_password_hash, verify_password
from api.db.repositories.base import BaseRepository
from api.models.user import User
from api.schemas.user import UserCreate, UserUpdate

logger = logging.getLogger(__name__)


class UserRepository(BaseRepository):
    """Repository for user-related database operations."""
    
    def __init__(self, db: AsyncSession = None):
        """
        Initialize the repository.
        
        Args:
            db: Optional database session
        """
        super().__init__(db_session=db, model_class=User)
    """Repository for User operations."""

    def __init__(self, db: AsyncSession):
        """
        Initialize repository with database session.

        Args:
            db: Database session
        """
        super().__init__(db, User)

    async def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email.

        Args:
            email: Email to search for

        Returns:
            User if found, None otherwise
        """
        try:
            query = select(self.model).where(self.model.email == email)
            result = await self.db.execute(query)
            return result.scalars().first()
        except Exception as e:
            logger.error(f"Error in get_by_email: {str(e)}", exc_info=True)
            raise

    async def create(self, obj_in: Union[UserCreate, Dict[str, Any]]) -> User:
        """
        Create a new user with hashed password.

        Args:
            obj_in: User data

        Returns:
            Created user
        """
        try:
            if isinstance(obj_in, dict):
                obj_data = obj_in.copy()
                password = obj_data.pop("password")
            else:
                obj_data = obj_in.model_dump(exclude={"password"})
                password = obj_in.password

            # Hash the password
            hashed_password = get_password_hash(password)
            obj_data["hashed_password"] = hashed_password

            db_obj = self.model(**obj_data)
            self.db.add(db_obj)
            await self.db.flush()
            await self.db.refresh(db_obj)
            return db_obj
        except Exception as e:
            logger.error(f"Error in create: {str(e)}", exc_info=True)
            raise

    async def update(
        self, 
        db_obj: User,
        obj_in: Union[UserUpdate, Dict[str, Any]]
    ) -> User:
        """
        Update user details, handling password separately if provided.

        Args:
            db_obj: Existing user object
            obj_in: Update data

        Returns:
            Updated user
        """
        try:
            if isinstance(obj_in, dict):
                update_data = obj_in.copy()
                password = update_data.pop("password", None)
            else:
                update_data = obj_in.model_dump(exclude_unset=True)
                password = update_data.pop("password", None)

            # Update password if provided
            if password:
                hashed_password = get_password_hash(password)
                update_data["hashed_password"] = hashed_password

            # Apply updates
            for field, value in update_data.items():
                setattr(db_obj, field, value)

            self.db.add(db_obj)
            await self.db.flush()
            await self.db.refresh(db_obj)
            return db_obj
        except Exception as e:
            logger.error(f"Error in update: {str(e)}", exc_info=True)
            raise

    async def authenticate(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate user by email and password.

        Args:
            email: User email
            password: Plain password

        Returns:
            User if authentication succeeds, None otherwise
        """
        try:
            user = await self.get_by_email(email=email)
            if not user:
                return None

            if not verify_password(password, user.hashed_password):
                return None

            return user
        except Exception as e:
            logger.error(f"Error in authenticate: {str(e)}", exc_info=True)
            raise

    async def count(self) -> int:
        """
        Count all users.

        Returns:
            Total count of users
        """
        try:
            query = select(func.count()).select_from(self.model)
            result = await self.db.execute(query)
            return result.scalar_one()
        except Exception as e:
            logger.error(f"Error in count: {str(e)}", exc_info=True)
            raise
"""
User repository for database operations.
"""
import logging
from typing import Optional, List

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from api.db.repositories.base import BaseRepository
from api.models.user import User
from api.db.session import get_db

logger = logging.getLogger(__name__)


class UserRepository(BaseRepository):
    """Repository for user-related database operations."""
    
    def __init__(self, db: AsyncSession = None):
        """
        Initialize the repository.
        
        Args:
            db: Optional database session
        """
        super().__init__(model=User, db=db)
        
    async def get_by_username(self, username: str) -> Optional[User]:
        """
        Get a user by username.
        
        Args:
            username: The username to search for
            
        Returns:
            Optional[User]: User model if found, None otherwise
        """
        try:
            query = select(self.model).where(
                self.model.username == username,
                self.model.deleted_at.is_(None)
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except Exception as e:
            logger.error(f"Error getting user by username: {str(e)}")
            return None
            
    async def get_by_email(self, email: str) -> Optional[User]:
        """
        Get a user by email.
        
        Args:
            email: The email to search for
            
        Returns:
            Optional[User]: User model if found, None otherwise
        """
        try:
            query = select(self.model).where(
                self.model.email == email,
                self.model.deleted_at.is_(None)
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except Exception as e:
            logger.error(f"Error getting user by email: {str(e)}")
            return None
            
    async def get_superusers(self) -> List[User]:
        """
        Get all superusers.
        
        Returns:
            List[User]: List of superuser models
        """
        try:
            query = select(self.model).where(
                self.model.is_superuser == True,
                self.model.deleted_at.is_(None)
            )
            result = await self.db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting superusers: {str(e)}")
            return []
