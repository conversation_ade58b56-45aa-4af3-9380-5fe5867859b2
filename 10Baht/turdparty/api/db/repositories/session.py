
"""
Session repository for database operations.
"""
import logging
from typing import List, Optional
import uuid
from datetime import datetime

from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession

from api.db.models.session import Session

logger = logging.getLogger(__name__)


class SessionRepository:
    """Session repository for database operations."""
    
    def __init__(self, db_session: AsyncSession):
        """
        Initialize the repository with a database session.
        
        Args:
            db_session: The database session
        """
        self.db = db_session
    
    async def create(self, user_id: str, ip_address: Optional[str] = None, 
                     user_agent: Optional[str] = None) -> Session:
        """
        Create a new session for a user.
        
        Args:
            user_id: User ID
            ip_address: Client IP address
            user_agent: Client user agent string
            
        Returns:
            The created session
        """
        try:
            token = str(uuid.uuid4())
            session = Session(
                user_id=user_id,
                token=token,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            self.db.add(session)
            await self.db.commit()
            await self.db.refresh(session)
            
            return session
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating session: {str(e)}")
            raise
    
    async def get_by_token(self, token: str) -> Optional[Session]:
        """
        Get a session by its token.
        
        Args:
            token: Session token
            
        Returns:
            Session if found, None otherwise
        """
        try:
            query = select(Session).where(
                Session.token == token,
                Session.is_active == True
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except Exception as e:
            logger.error(f"Error getting session by token: {str(e)}")
            raise
    
    async def invalidate(self, session_id: str) -> bool:
        """
        Invalidate a session.
        
        Args:
            session_id: Session ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            stmt = update(Session).where(
                Session.id == session_id
            ).values(
                is_active=False
            )
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            return result.rowcount > 0
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error invalidating session: {str(e)}")
            raise
    
    async def invalidate_all_for_user(self, user_id: str) -> int:
        """
        Invalidate all active sessions for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Number of invalidated sessions
        """
        try:
            stmt = update(Session).where(
                Session.user_id == user_id,
                Session.is_active == True
            ).values(
                is_active=False
            )
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            return result.rowcount
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error invalidating all sessions for user: {str(e)}")
            raise
    
    async def cleanup_expired(self) -> int:
        """
        Clean up expired sessions.
        
        Returns:
            Number of cleaned up sessions
        """
        try:
            now = datetime.utcnow()
            stmt = update(Session).where(
                Session.expires_at < now,
                Session.is_active == True
            ).values(
                is_active=False
            )
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            return result.rowcount
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error cleaning up expired sessions: {str(e)}")
            raise
