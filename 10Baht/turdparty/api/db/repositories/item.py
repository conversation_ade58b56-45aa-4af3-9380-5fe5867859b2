
"""Item repository implementation."""
import logging
from typing import Any, Dict, List, Optional, Union

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from api.db.repositories.base import BaseRepository
from api.models.item import Item
from api.schemas.item import ItemCreate, ItemUpdate

logger = logging.getLogger(__name__)


class ItemRepository(BaseRepository[Item, ItemCreate, ItemUpdate]):
    """Repository for Item operations."""

    def __init__(self, db: AsyncSession):
        """
        Initialize repository with database session.
        
        Args:
            db: Database session
        """
        super().__init__(db, Item)
    
    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        owner_id: Optional[int] = None
    ) -> List[Item]:
        """
        Get multiple items with optional filtering by owner ID.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            owner_id: Optional owner ID to filter by
            
        Returns:
            List of items
        """
        try:
            query = select(self.model).offset(skip).limit(limit)
            
            # Add owner filter if provided
            if owner_id is not None:
                query = query.where(self.model.owner_id == owner_id)
                
            result = await self.db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error in get_multi: {str(e)}", exc_info=True)
            raise
    
    async def count(self, owner_id: Optional[int] = None) -> int:
        """
        Count items with optional filtering by owner ID.
        
        Args:
            owner_id: Optional owner ID to filter by
            
        Returns:
            Total count of matching items
        """
        try:
            query = select(func.count()).select_from(self.model)
            
            # Add owner filter if provided
            if owner_id is not None:
                query = query.where(self.model.owner_id == owner_id)
                
            result = await self.db.execute(query)
            return result.scalar_one()
        except Exception as e:
            logger.error(f"Error in count: {str(e)}", exc_info=True)
            raise
    
    async def create(
        self, 
        obj_in: Union[ItemCreate, Dict[str, Any]], 
        owner_id: int
    ) -> Item:
        """
        Create a new item with owner ID.
        
        Args:
            obj_in: Item data
            owner_id: Owner ID to assign
            
        Returns:
            Created item
        """
        try:
            if isinstance(obj_in, dict):
                obj_data = obj_in.copy()
            else:
                obj_data = obj_in.model_dump()
                
            # Set owner ID
            obj_data["owner_id"] = owner_id
                
            db_obj = self.model(**obj_data)
            self.db.add(db_obj)
            await self.db.flush()
            await self.db.refresh(db_obj)
            return db_obj
        except Exception as e:
            logger.error(f"Error in create: {str(e)}", exc_info=True)
            raise
