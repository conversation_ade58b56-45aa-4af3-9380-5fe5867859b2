"""Database dependencies for dependency injection."""
import logging
from typing import AsyncGenerator, Optional

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from api.core.config import settings
from api.db.session import SessionLocal, engine

logger = logging.getLogger(__name__)


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get database session dependency.

    Yields:
        AsyncSession: SQLAlchemy async session

    Notes:
        This dependency provides a database session for use in route handlers
        and ensures proper cleanup of the session after use.
    """
    session = SessionLocal()
    try:
        yield session
        await session.commit()
    except Exception as e:
        await session.rollback()
        logger.error(f"Database session error: {str(e)}", exc_info=True)
        raise
    finally:
        await session.close()


async def verify_database_connection() -> bool:
    """
    Verify that the database connection is working.

    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        async with engine.connect() as conn:
            await conn.execute("SELECT 1")
            return True
    except Exception as e:
        logger.error(f"Database connection verification failed: {str(e)}", exc_info=True)
        return False
"""
Database dependency functions for FastAPI.
"""
import logging
from typing import AsyncGenerator

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import text
from sqlalchemy.exc import SQLAlchemyError

from api.db.session import get_db_session

logger = logging.getLogger(__name__)

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency for database sessions.
    
    Yields:
        AsyncSession: SQLAlchemy async session
    """
    async for session in get_db_session():
        yield session

async def verify_database_connection() -> bool:
    """
    Verify the database connection is working.
    
    Returns:
        bool: True if connection is successful, False otherwise
    """
    from api.db.session import engine
    
    try:
        async with engine.connect() as conn:
            await conn.execute(text("SELECT 1"))
            logger.info("Database connection verified successfully")
            return True
    except SQLAlchemyError as e:
        logger.error(f"Database connection failed: {str(e)}")
        return False
