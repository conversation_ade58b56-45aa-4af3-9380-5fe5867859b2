from enum import Enum
from typing import List, Optional, Dict, Any, Union, TypeVar, Generic
from uuid import UUID
from datetime import datetime

T = TypeVar('T')

class VMStatus(str, Enum):
    """Enum for VM status values."""
    CREATED = "created"
    STARTING = "starting"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"

class ItemStatus(str, Enum):
    """Enum for Item status values."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived" 