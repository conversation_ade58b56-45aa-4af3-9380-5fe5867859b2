from typing import Generic, TypeVar, Type, List, Optional, Union, Dict, Any
from sqlalchemy.orm import Session
from uuid import UUID
from pydantic import BaseModel
from sqlalchemy import inspect, desc, asc

T = TypeVar('T')
CreateSchema = TypeVar('CreateSchema', bound=BaseModel)
UpdateSchema = TypeVar('UpdateSchema', bound=BaseModel)

class Repository(Generic[T, CreateSchema, UpdateSchema]):
    """Generic repository for database operations."""
    
    def __init__(self, model: Type[T], db: Session):
        self.model = model
        self.db = db
    
    def get(self, id: UUID) -> Optional[T]:
        """Get a record by ID."""
        return self.db.query(self.model).filter(self.model.id == id).first()
    
    def list(self, skip: int = 0, limit: int = 100, **filters) -> List[T]:
        """List records with optional filtering."""
        query = self.db.query(self.model)
        
        # Apply filters
        for field, value in filters.items():
            if hasattr(self.model, field):
                query = query.filter(getattr(self.model, field) == value)
        
        return query.offset(skip).limit(limit).all()
    
    def create(self, schema: CreateSchema, **extra_data) -> T:
        """Create a new record."""
        data = {**schema.model_dump(exclude_unset=True), **extra_data}
        db_item = self.model(**data)
        self.db.add(db_item)
        self.db.commit()
        self.db.refresh(db_item)
        return db_item
    
    def update(self, id: UUID, schema: UpdateSchema) -> Optional[T]:
        """Update a record."""
        db_item = self.get(id)
        if not db_item:
            return None
        
        update_data = schema.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_item, field, value)
        
        self.db.commit()
        self.db.refresh(db_item)
        return db_item
    
    def delete(self, id: UUID, soft: bool = True) -> bool:
        """Delete a record (soft delete by default)."""
        db_item = self.get(id)
        if not db_item:
            return False
        
        if soft and hasattr(db_item, 'soft_delete'):
            db_item.soft_delete()
            self.db.commit()
        else:
            self.db.delete(db_item)
            self.db.commit()
        
        return True
    
    def count(self, **filters) -> int:
        """Count records with optional filtering."""
        query = self.db.query(self.model)
        
        # Apply filters
        for field, value in filters.items():
            if hasattr(self.model, field):
                query = query.filter(getattr(self.model, field) == value)
        
        return query.count()
    
    def exists(self, id: UUID) -> bool:
        """Check if a record exists by ID."""
        return self.db.query(self.model).filter(self.model.id == id).count() > 0
    
    def search(self, search_terms: Dict[str, Any], skip: int = 0, limit: int = 100, 
              sort_by: str = "created_on", sort_desc: bool = True) -> List[T]:
        """
        Search records with complex filtering and sorting.
        
        Args:
            search_terms: Dictionary of field names and values to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_desc: Whether to sort in descending order
            
        Returns:
            List of matching records
        """
        query = self.db.query(self.model)
        
        # Apply search terms
        for field, value in search_terms.items():
            if not hasattr(self.model, field):
                continue
                
            if isinstance(value, list):
                # If value is a list, use IN operator
                query = query.filter(getattr(self.model, field).in_(value))
            elif isinstance(value, dict):
                # If value is a dict, use comparison operators
                for op, op_value in value.items():
                    if op == "eq":
                        query = query.filter(getattr(self.model, field) == op_value)
                    elif op == "ne":
                        query = query.filter(getattr(self.model, field) != op_value)
                    elif op == "gt":
                        query = query.filter(getattr(self.model, field) > op_value)
                    elif op == "lt":
                        query = query.filter(getattr(self.model, field) < op_value)
                    elif op == "gte":
                        query = query.filter(getattr(self.model, field) >= op_value)
                    elif op == "lte":
                        query = query.filter(getattr(self.model, field) <= op_value)
                    elif op == "like":
                        query = query.filter(getattr(self.model, field).like(f"%{op_value}%"))
                    elif op == "is_null":
                        if op_value:
                            query = query.filter(getattr(self.model, field).is_(None))
                        else:
                            query = query.filter(getattr(self.model, field).isnot(None))
            else:
                # Simple equality comparison
                query = query.filter(getattr(self.model, field) == value)
        
        # Apply sorting
        if hasattr(self.model, sort_by):
            order_func = desc if sort_desc else asc
            query = query.order_by(order_func(getattr(self.model, sort_by)))
        
        # Apply pagination
        return query.offset(skip).limit(limit).all() 