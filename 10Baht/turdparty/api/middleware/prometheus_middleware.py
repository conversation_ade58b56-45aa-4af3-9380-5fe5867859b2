
"""Middleware for Prometheus metrics."""

import time
from typing import Callable, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from api.core.container import container

class PrometheusMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting Prometheus metrics on requests."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process a request and record Prometheus metrics."""
        # Get metrics service from container
        metrics_service = container.get("metrics_service")
        if not metrics_service.enabled:
            return await call_next(request)
        
        # Extract path for metrics
        path = request.url.path
        method = request.method
        
        # Start timer and track active request
        timer = metrics_service.start_request_timer(path)
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Record successful request
            metrics_service.record_request(path, method)
            
            # Record error if applicable
            if response.status_code >= 400:
                metrics_service.record_error(path, response.status_code)
            
            return response
        except Exception as exc:
            # Record error
            metrics_service.record_error(path, 500)
            raise exc
        finally:
            # End timer
            metrics_service.end_request_timer(timer, path)
