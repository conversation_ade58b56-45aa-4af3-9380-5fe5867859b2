
"""
Middleware to enforce MFA verification.
"""
import logging
import time
from typing import Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from api.core.security import decode_token
from api.services.mfa_service import MFAService

logger = logging.getLogger(__name__)


class MFAMiddleware(BaseHTTPMiddleware):
    """Middleware to enforce MFA verification."""
    
    async def dispatch(self, request: Request, call_next):
        """
        Process request and enforce MFA verification.
        
        Args:
            request: HTTP request
            call_next: Next middleware/endpoint in the chain
            
        Returns:
            HTTP response
        """
        # Skip MFA check for certain paths
        path = request.url.path
        
        # Skip MFA check for authentication endpoints and MFA endpoints themselves
        if path.startswith("/api/auth") or path.startswith("/api/mfa") or path == "/api/health":
            return await call_next(request)
        
        # Check for authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return await call_next(request)
        
        # Extract token
        token = auth_header.replace("Bearer ", "")
        user_data = decode_token(token)
        if not user_data:
            return await call_next(request)
        
        # Check if MFA is required for this user
        try:
            user_id = user_data.get("sub")
            if not user_id:
                return await call_next(request)
            
            mfa_service = MFAService()
            mfa_required = mfa_service.is_mfa_required(int(user_id))
            
            if not mfa_required:
                return await call_next(request)
            
            # Check if MFA has been validated in this session
            mfa_validated_at = request.cookies.get("mfa_validated_at")
            
            if mfa_validated_at:
                try:
                    mfa_validated_at = float(mfa_validated_at)
                    is_valid = mfa_service.validate_mfa_session(int(user_id), mfa_validated_at)
                    
                    if is_valid:
                        return await call_next(request)
                except ValueError:
                    # Invalid timestamp
                    pass
            
            # MFA is required but not validated
            return JSONResponse(
                status_code=403,
                content={"detail": "MFA verification required", "code": "mfa_required"}
            )
        except Exception as e:
            logger.error(f"Error in MFA middleware: {str(e)}")
            # If there's an error in the MFA check, we'll allow the request
            # to proceed but log the error
            return await call_next(request)
