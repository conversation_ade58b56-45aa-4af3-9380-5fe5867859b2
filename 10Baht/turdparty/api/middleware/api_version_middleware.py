"""
Middleware for handling API versioning and redirecting old API paths to new ones.
"""
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import RedirectResponse
import logging
import re

# Set up logging
logger = logging.getLogger(__name__)

class APIVersionMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle API versioning and redirect old API paths to new ones.
    
    This middleware checks if a request is using the old API path format (/api/...)
    and redirects it to the new versioned format (/api/v1/...) if needed.
    """
    
    def __init__(self, app, api_prefix="/api/v1"):
        super().__init__(app)
        self.api_prefix = api_prefix
        self.old_api_pattern = re.compile(r"^/api/(?!v1/)(.+)$")
        logger.info(f"APIVersionMiddleware initialized with API prefix: {api_prefix}")
    
    async def dispatch(self, request: Request, call_next):
        """
        Dispatch the request and handle API versioning.
        
        Args:
            request: The incoming request
            call_next: The next middleware or route handler
            
        Returns:
            The response from the next middleware or route handler
        """
        path = request.url.path
        
        # Check if the path matches the old API pattern
        match = self.old_api_pattern.match(path)
        if match:
            # Extract the path after /api/
            endpoint = match.group(1)
            
            # Construct the new path with the API prefix
            new_path = f"{self.api_prefix}/{endpoint}"
            
            # Log the redirection
            logger.info(f"Redirecting from old API path {path} to new path {new_path}")
            
            # Create the new URL with the same query parameters
            new_url = str(request.url.replace(path=new_path))
            
            # Return a redirect response
            return RedirectResponse(new_url, status_code=307)
        
        # If the path doesn't match the old API pattern, continue with the request
        return await call_next(request) 