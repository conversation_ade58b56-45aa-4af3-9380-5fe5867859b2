"""
Models for Vagrant VM feature
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict
from datetime import datetime
from enum import Enum
import uuid


class VMStatus(str, Enum):
    """VM status enum"""
    PENDING = "pending"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"
    DESTROYED = "destroyed"
    

class VMTemplate(str, Enum):
    """VM template enum"""
    UBUNTU_2004 = "UBUNTU_2004"
    UBUNTU_2204 = "UBUNTU_2204"
    DEBIAN_11 = "debian_11"
    CENTOS_7 = "CENTOS_7"
    CENTOS_8 = "CENTOS_8"
    WINDOWS_10 = "WINDOWS_10"
    WINDOWS_SERVER_2019 = "WINDOWS_SERVER_2019"
    CUSTOM = "custom"


class VagrantVMBaseSchema(BaseModel):
    """Base Vagrant VM schema"""
    name: str
    description: Optional[str] = None
    template: VMTemplate
    memory_mb: int = Field(default=1024, ge=512)
    cpus: int = Field(default=1, ge=1)
    disk_gb: int = Field(default=20, ge=5)
    domain: Optional[str] = Field(default="TurdParty", description="Domain for the VM, used for organizing VMs")


class VagrantVMCreateSchema(VagrantVMBaseSchema):
    """Vagrant VM creation schema"""
    custom_vagrantfile: Optional[str] = None
    provision_script: Optional[str] = None
    auto_start: bool = Field(default=True)


class VagrantVMUpdateSchema(BaseModel):
    """Vagrant VM update schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    memory_mb: Optional[int] = Field(default=None, ge=512)
    cpus: Optional[int] = Field(default=None, ge=1)
    disk_gb: Optional[int] = Field(default=None, ge=5)
    domain: Optional[str] = Field(default=None, description="Domain for the VM, used for organizing VMs")
    

class VagrantVMActionSchema(BaseModel):
    """Vagrant VM action schema"""
    action: str = Field(..., description="Action to perform on VM: start, stop, restart, destroy")


class VagrantVMSchema(BaseModel):
    """Vagrant VM response schema"""
    id: uuid.UUID
    name: str
    description: Optional[str] = None
    template: str
    memory_mb: int
    cpus: int
    disk_gb: int
    status: str
    ip_address: Optional[str] = None
    ssh_port: Optional[int] = None
    vagrant_id: Optional[str] = None
    created_on: datetime
    modified_on: Optional[datetime] = None
    owner_id: uuid.UUID
    last_action: Optional[str] = None
    last_action_time: Optional[datetime] = None
    error_message: Optional[str] = None
    domain: Optional[str] = "TurdParty"
    
    class Config:
        orm_mode = True


class VagrantVMListSchema(BaseModel):
    """List of Vagrant VMs"""
    items: List[VagrantVMSchema]
    total: int
    
    class Config:
        orm_mode = True
        

class VagrantVMStatusSchema(BaseModel):
    """Vagrant VM status schema"""
    id: uuid.UUID
    name: str
    status: str
    ip_address: Optional[str] = None
    ssh_port: Optional[int] = None
    last_action: Optional[str] = None
    last_action_time: Optional[datetime] = None
    error_message: Optional[str] = None
    domain: Optional[str] = None
