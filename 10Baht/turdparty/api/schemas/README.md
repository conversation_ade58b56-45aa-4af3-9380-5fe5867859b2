# Pydantic Schema Validation

This directory contains Pydantic schema models used for validating API requests and responses in the TurdParty application.

## Schema Structure

Each model type typically follows this pattern:

1. **Base**: Contains common fields shared by create/update/response models
2. **Create**: Used for creating new resources (extends Base)
3. **Update**: Used for updating existing resources (partial updates with optional fields)
4. **Response**: Used for API responses (extends Base with additional fields like id, timestamps)

## Schema Validation

Pydantic models enforce validation rules including:

- Field types (string, integer, UUID, etc.)
- Required vs. optional fields
- Minimum/maximum string lengths
- Enumerated values for status fields
- Custom validators for complex rules

## Available Schemas

### Base Schemas

- **BaseSchema**: Common configuration for all schemas
- **ResponseBase**: Common fields for all response schemas (id, created_on, modified_on)
- **PaginatedResponse**: Generic paginated response with metadata
- **ErrorResponse**: Error response format

### User Schemas

- **UserBase**: Base user properties
- **UserCreate**: Schema for creating users
- **UserUpdate**: Schema for updating users
- **UserResponse**: Schema for user responses
- **UserRole**: Enumeration of user roles

### Item Schemas

- **ItemBase**: Base item properties with validation
  - `title`: 3-255 characters
  - `status`: Must match valid ItemStatus enum values
- **ItemCreate**: Schema for creating items
- **ItemUpdate**: Schema for updating items with optional fields
- **ItemResponse**: Schema for item responses

### File Upload Schemas

- **FileUploadBase**: Base file upload properties
- **FileUploadCreate**: Schema for creating file uploads
  - Validates that file_size must be positive
- **FileUploadUpdate**: Schema for updating file uploads
- **FileUploadRead**: Schema for file upload responses
- **FolderUploadCreate**: Schema for folder uploads
  - Validates that number of paths matches number of files
- **FolderUploadRead**: Schema for folder upload responses

### File Selection Schemas

- **FileSelectionBase**: Base file selection properties with validation
  - `name`: 1-255 characters
  - `target_path`: Must not be empty
  - `permissions`: Must be valid octal format (0000-0777)
- **FileSelectionCreate**: Schema for creating file selections
  - Requires `file_upload_id`
- **FileSelectionUpdate**: Schema for updating file selections with optional fields
  - Optional fields with same validation as base
- **FileSelectionResponse**: Schema for file selection responses
  - Includes owner_id and file_upload_id

### VM Injection Schemas

- **VMInjectionBase**: Base VM injection properties
- **VMInjectionCreate**: Schema for creating VM injections
  - Requires `vagrant_vm_id` and `file_selection_id`
- **VMInjectionUpdate**: Schema for updating VM injections with optional fields
  - Optional `status` must be one of: "pending", "processing", "completed", "failed"
- **VMInjectionResponse**: Schema for VM injection responses
  - Includes status, owner_id, and related IDs
  - Validates status value
- **VMInjectionStatusUpdate**: Dedicated schema for updating VM injection status
  - Requires valid status value

## Validation Examples

### String Length Validation

```python
title: str = Field(..., min_length=3, max_length=255)
```

### Enumerated Values

```python
@validator("status")
def validate_status(cls, value: str) -> str:
    valid_statuses = ["pending", "processing", "completed", "failed"]
    if value not in valid_statuses:
        raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")
    return value
```

### Format Validation

```python
@validator("permissions")
def validate_permissions(cls, value: str) -> str:
    try:
        perm_int = int(value, 8)
        if perm_int < 0 or perm_int > 0o777:
            raise ValueError("Permissions must be between 0000 and 0777")
    except ValueError:
        raise ValueError("Permissions must be a valid octal string (e.g., '0644')")
    return value
```

## Testing

All schemas have corresponding test files in `api/tests/schemas/` that verify:
- Valid data is accepted
- Invalid data is rejected with appropriate error messages
- Default values are applied correctly
- All validation rules are enforced 