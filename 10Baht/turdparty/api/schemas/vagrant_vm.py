"""
VagrantVM-related Pydantic schemas for API validation.
"""
from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID
from pydantic import Field, validator
import re

from api.schemas.base import BaseSchema, ResponseBase
from api.db.types import VMStatus


class VagrantVMBase(BaseSchema):
    """Base model for VM data."""
    name: str = Field(..., min_length=3, max_length=100, 
                     description="VM name (3-100 chars, alphanumeric, underscores, hyphens)")
    description: Optional[str] = Field(None, description="Optional VM description")
    template: str = Field(..., min_length=1, max_length=100, description="VM template to use")
    memory_mb: int = Field(default=1024, ge=512, le=32768, description="Memory in MB (512-32768)")
    cpus: int = Field(default=1, ge=1, le=16, description="Number of CPUs (1-16)")
    disk_gb: int = Field(default=20, ge=1, le=500, description="Disk size in GB (1-500)")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "dev-vm-1",
                "description": "Development VM",
                "template": "ubuntu-20.04",
                "memory_mb": 2048,
                "cpus": 2,
                "disk_gb": 40
            }
        }
    }
    
    @validator("name")
    def validate_name(cls, value: str) -> str:
        """Validate VM name format."""
        if not re.match(r"^[a-zA-Z0-9_-]{3,100}$", value):
            raise ValueError("VM name must be 3-100 characters and contain only letters, numbers, underscores, and hyphens")
        return value


class VagrantVMCreate(VagrantVMBase):
    """Model for creating a new VM."""
    pass


class VagrantVMUpdate(BaseSchema):
    """Model for updating VM data."""
    name: Optional[str] = Field(None, min_length=3, max_length=100, 
                               description="VM name (3-100 chars, alphanumeric, underscores, hyphens)")
    description: Optional[str] = Field(None, description="Optional VM description")
    memory_mb: Optional[int] = Field(None, ge=512, le=32768, description="Memory in MB (512-32768)")
    cpus: Optional[int] = Field(None, ge=1, le=16, description="Number of CPUs (1-16)")
    disk_gb: Optional[int] = Field(None, ge=1, le=500, description="Disk size in GB (1-500)")
    
    @validator("name")
    def validate_name(cls, value: Optional[str]) -> Optional[str]:
        """Validate VM name format if provided."""
        if value is None:
            return None
            
        if not re.match(r"^[a-zA-Z0-9_-]{3,100}$", value):
            raise ValueError("VM name must be 3-100 characters and contain only letters, numbers, underscores, and hyphens")
        return value


class VagrantVMStatusUpdate(BaseSchema):
    """Model for updating VM status."""
    status: str = Field(..., description="VM status")
    error_message: Optional[str] = Field(None, description="Error message if status is 'error'")
    
    @validator("status")
    def validate_status(cls, value: str) -> str:
        """Validate VM status value."""
        try:
            return VMStatus(value).value
        except ValueError:
            valid_statuses = [s.value for s in VMStatus]
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")


class VagrantVMResponse(ResponseBase, VagrantVMBase):
    """Model for VM response."""
    status: str = Field(..., description="Current VM status")
    ip_address: Optional[str] = Field(None, description="VM IP address if running")
    ssh_port: Optional[int] = Field(None, description="SSH port if running")
    last_action: Optional[str] = Field(None, description="Last action performed on the VM")
    last_action_time: Optional[datetime] = Field(None, description="Timestamp of last action")
    error_message: Optional[str] = Field(None, description="Error message if status is 'error'")
    owner_id: UUID = Field(..., description="ID of the VM owner")
    domain: str = Field("TurdParty", description="VM domain")
    uptime_seconds: int = Field(0, description="VM uptime in seconds")
    
    # Resource usage summary
    cpu_usage_avg: Optional[float] = Field(None, description="Average CPU usage (%)")
    memory_usage_avg: Optional[float] = Field(None, description="Average memory usage (%)")
    disk_usage_avg: Optional[float] = Field(None, description="Average disk usage (%)")


class VagrantVMActionRequest(BaseSchema):
    """Model for VM action requests."""
    action: str = Field(..., description="Action to perform on the VM")
    
    @validator("action")
    def validate_action(cls, value: str) -> str:
        """Validate VM action."""
        valid_actions = ["start", "stop", "restart", "destroy"]
        if value not in valid_actions:
            raise ValueError(f"Action must be one of: {', '.join(valid_actions)}")
        return value


class VagrantVMCommandRequest(BaseSchema):
    """Model for VM command execution requests."""
    command: str = Field(..., description="Command to execute on the VM")
    timeout: int = Field(60, ge=1, le=3600, description="Command timeout in seconds (1-3600)")


class VagrantVMResourceUsage(BaseSchema):
    """Model for VM resource usage."""
    cpu_usage: Dict[str, float] = Field(..., description="CPU usage over time (timestamp -> percentage)")
    memory_usage: Dict[str, float] = Field(..., description="Memory usage over time (timestamp -> percentage)")
    disk_usage: Dict[str, float] = Field(..., description="Disk usage over time (timestamp -> percentage)")
    uptime_formatted: str = Field(..., description="Formatted uptime (e.g., '2d 5h 10m')") 