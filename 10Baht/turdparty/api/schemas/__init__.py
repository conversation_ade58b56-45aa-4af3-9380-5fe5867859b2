"""Schemas package initialization."""
from api.schemas.base import BaseSchema, ResponseBase, PaginatedResponse, ErrorResponse
from api.schemas.user import UserBase, UserCreate, UserUpdate, UserResponse, UserRole
from api.schemas.item import ItemBase, ItemCreate, ItemUpdate, ItemResponse
from api.schemas.file_selection import FileSelectionBase, FileSelectionCreate, FileSelectionUpdate, FileSelectionResponse
from api.schemas.vm_injection import VMInjectionBase, VMInjectionCreate, VMInjectionUpdate, VMInjectionResponse, VMInjectionStatusUpdate

# Export all schemas
__all__ = [
    "BaseSchema", "ResponseBase", "PaginatedResponse", "ErrorResponse",
    "UserBase", "UserCreate", "UserUpdate", "UserResponse", "UserRole",
    "ItemBase", "ItemCreate", "ItemUpdate", "ItemResponse",
    "FileSelectionBase", "FileSelectionCreate", "FileSelectionUpdate", "FileSelectionResponse",
    "VMInjectionBase", "VMInjectionCreate", "VMInjectionUpdate", "VMInjectionResponse", "VMInjectionStatusUpdate",
]
