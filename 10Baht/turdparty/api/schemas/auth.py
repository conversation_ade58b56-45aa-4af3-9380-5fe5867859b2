
"""
Authentication schemas.
"""
from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class TokenResponse(BaseModel):
    """Token response schema."""
    
    access_token: str
    token_type: str = "bearer"


class LoginRequest(BaseModel):
    """Login request schema."""
    
    email: EmailStr
    password: str


class RegisterRequest(BaseModel):
    """Register request schema."""
    
    name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    password: str = Field(..., min_length=8)


class SessionInfo(BaseModel):
    """Session information schema."""
    
    id: str
    user_id: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    
    class Config:
        """Pydantic config."""
        
        from_attributes = True
