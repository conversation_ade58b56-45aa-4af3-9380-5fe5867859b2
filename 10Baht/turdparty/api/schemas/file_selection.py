"""
File Selection Pydantic schemas for API validation.
"""
from typing import Optional
from uuid import UUID
from datetime import datetime
from pydantic import Field, validator

from api.schemas.base import BaseSchema, ResponseBase


class FileSelectionBase(BaseSchema):
    """Base model for file selection data."""
    name: str = Field(..., min_length=1, max_length=255, 
                     description="Name of the file selection")
    description: Optional[str] = Field(None, description="Optional description of the file selection")
    target_path: str = Field(..., min_length=1, 
                           description="Target path for the file in the VM")
    permissions: str = Field("0644", description="File permissions in octal format")
    is_active: bool = Field(True, description="Whether the file selection is active")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "Config file",
                "description": "Configuration file for the application",
                "target_path": "/etc/app/config.json",
                "permissions": "0644",
                "is_active": True
            }
        }
    }
    
    @validator("permissions")
    def validate_permissions(cls, value: str) -> str:
        """Validate that permissions are in octal format."""
        # Check if the value is a valid octal string between 0000 and 0777
        try:
            perm_int = int(value, 8)
            if perm_int < 0 or perm_int > 0o777:
                raise ValueError("Permissions must be between 0000 and 0777")
        except ValueError:
            raise ValueError("Permissions must be a valid octal string (e.g., '0644')")
        
        # Ensure the value starts with a leading zero and has 3 or 4 digits
        if not (value.startswith('0') and (len(value) == 4 or len(value) == 5)):
            value = f"0{value}" if len(value) <= 3 else value
            
        return value


class FileSelectionCreate(FileSelectionBase):
    """Model for creating a new file selection."""
    file_upload_id: UUID = Field(..., description="ID of the uploaded file")


class FileSelectionUpdate(BaseSchema):
    """Model for updating file selection data."""
    name: Optional[str] = Field(None, min_length=1, max_length=255, 
                              description="Name of the file selection")
    description: Optional[str] = Field(None, description="Optional description of the file selection")
    target_path: Optional[str] = Field(None, min_length=1, 
                                    description="Target path for the file in the VM")
    permissions: Optional[str] = Field(None, description="File permissions in octal format")
    is_active: Optional[bool] = Field(None, description="Whether the file selection is active")
    
    @validator("permissions")
    def validate_permissions(cls, value: Optional[str]) -> Optional[str]:
        """Validate that permissions are in octal format, if provided."""
        if value is None:
            return None
            
        # Check if the value is a valid octal string between 0000 and 0777
        try:
            perm_int = int(value, 8)
            if perm_int < 0 or perm_int > 0o777:
                raise ValueError("Permissions must be between 0000 and 0777")
        except ValueError:
            raise ValueError("Permissions must be a valid octal string (e.g., '0644')")
        
        # Ensure the value starts with a leading zero and has 3 or 4 digits
        if not (value.startswith('0') and (len(value) == 4 or len(value) == 5)):
            value = f"0{value}" if len(value) <= 3 else value
            
        return value


class FileSelectionResponse(ResponseBase, FileSelectionBase):
    """Model for file selection response."""
    file_upload_id: UUID = Field(..., description="ID of the uploaded file")
    owner_id: UUID = Field(..., description="ID of the file selection owner")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "name": "Config file",
                "description": "Configuration file for the application",
                "target_path": "/etc/app/config.json",
                "permissions": "0644",
                "is_active": True,
                "file_upload_id": "123e4567-e89b-12d3-a456-************",
                "owner_id": "123e4567-e89b-12d3-a456-************",
                "created_on": "2023-01-01T12:00:00Z",
                "modified_on": "2023-01-01T12:00:00Z"
            }
        }
    }

# For backward compatibility
FileSelection = FileSelectionResponse
FileSelectionInDB = FileSelectionResponse 