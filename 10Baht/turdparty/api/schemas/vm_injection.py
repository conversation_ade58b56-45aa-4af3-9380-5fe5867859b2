"""
VM Injection Pydantic schemas for API validation.
"""
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from pydantic import Field, validator

from api.schemas.base import BaseSchema, ResponseBase


class VMInjectionBase(BaseSchema):
    """Base model for VM injection data."""
    description: Optional[str] = Field(None, description="Optional description of the VM injection")
    additional_command: Optional[str] = Field(None, description="Additional command to run after injection")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "description": "Inject configuration files into VM",
                "additional_command": "chmod +x /setup/install.sh && /setup/install.sh"
            }
        }
    }


class VMInjectionCreate(VMInjectionBase):
    """Model for creating a new VM injection."""
    vagrant_vm_id: UUID = Field(..., description="ID of the target Vagrant VM")
    file_selection_id: UUID = Field(..., description="ID of the file selection to inject")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "description": "Inject configuration files into VM",
                "additional_command": "chmod +x /setup/install.sh && /setup/install.sh",
                "vagrant_vm_id": "123e4567-e89b-12d3-a456-************",
                "file_selection_id": "123e4567-e89b-12d3-a456-************"
            }
        }
    }


class VMInjectionUpdate(BaseSchema):
    """Model for updating VM injection data."""
    description: Optional[str] = Field(None, description="Optional description of the VM injection")
    additional_command: Optional[str] = Field(None, description="Additional command to run after injection")
    status: Optional[str] = Field(None, description="Status of the VM injection")
    error_message: Optional[str] = Field(None, description="Error message if injection failed")
    
    @validator("status")
    def validate_status(cls, value: Optional[str]) -> Optional[str]:
        """Validate that status is one of the allowed values, if provided."""
        if value is None:
            return None
            
        valid_statuses = ["pending", "processing", "completed", "failed"]
        if value not in valid_statuses:
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")
            
        return value


class VMInjectionResponse(ResponseBase, VMInjectionBase):
    """Model for VM injection response."""
    vagrant_vm_id: UUID = Field(..., description="ID of the target Vagrant VM")
    file_selection_id: UUID = Field(..., description="ID of the file selection to inject")
    owner_id: UUID = Field(..., description="ID of the VM injection owner")
    status: str = Field(..., description="Status of the VM injection")
    error_message: Optional[str] = Field(None, description="Error message if injection failed")
    completed_on: Optional[datetime] = Field(None, description="When the injection was completed")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "description": "Inject configuration files into VM",
                "additional_command": "chmod +x /setup/install.sh && /setup/install.sh",
                "vagrant_vm_id": "123e4567-e89b-12d3-a456-************",
                "file_selection_id": "123e4567-e89b-12d3-a456-************",
                "owner_id": "123e4567-e89b-12d3-a456-************",
                "status": "completed",
                "error_message": None,
                "completed_on": "2023-01-01T12:05:00Z",
                "created_on": "2023-01-01T12:00:00Z",
                "modified_on": "2023-01-01T12:05:00Z"
            }
        }
    }
    
    @validator("status")
    def validate_status(cls, value: str) -> str:
        """Validate that status is one of the allowed values."""
        valid_statuses = ["pending", "processing", "completed", "failed"]
        if value not in valid_statuses:
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")
            
        return value


class VMInjectionStatusUpdate(BaseSchema):
    """Model for updating VM injection status."""
    status: str = Field(..., description="New status of the VM injection")
    error_message: Optional[str] = Field(None, description="Error message if injection failed")
    
    @validator("status")
    def validate_status(cls, value: str) -> str:
        """Validate that status is one of the allowed values."""
        valid_statuses = ["pending", "processing", "completed", "failed"]
        if value not in valid_statuses:
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")
            
        return value


# For backward compatibility
VMInjection = VMInjectionResponse
VMInjectionInDB = VMInjectionResponse 