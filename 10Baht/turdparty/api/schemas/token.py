
"""Token schemas module."""
from typing import Optional
from pydantic import BaseModel


class Token(BaseModel):
    """Schema for access token."""
    
    access_token: str
    token_type: str


class TokenPayload(BaseModel):
    """Schema for token payload."""
    
    sub: Optional[str] = None
"""
Token schemas for API authentication.
"""
from typing import Optional
from pydantic import BaseModel, Field


class Token(BaseModel):
    """Token response schema."""
    access_token: str
    token_type: str = "bearer"
    
    class Config:
        """Pydantic configuration."""
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer"
            }
        }


class TokenPayload(BaseModel):
    """Token payload schema."""
    sub: Optional[str] = None
    exp: int = Field(..., description="Token expiration timestamp")
    iat: Optional[int] = Field(None, description="Token issued at timestamp")
    is_superuser: bool = False
    scopes: Optional[list] = None


class TokenData(BaseModel):
    """Token data for authentication."""
    username: Optional[str] = None
    scopes: Optional[list] = None
