
"""
MFA schema definitions.
"""
from pydantic import BaseModel, Field
from typing import Optional

from api.schemas.base import BaseSchema


class MFASetupResponse(BaseSchema):
    """Schema for MFA setup response."""
    
    success: bool
    secret: Optional[str] = None
    qr_code: Optional[str] = None
    error: Optional[str] = None


class MFAVerifyRequest(BaseSchema):
    """Schema for MFA verification request."""
    
    token: str = Field(..., description="The TOTP token")


class MFAVerifyResponse(BaseSchema):
    """Schema for MFA verification response."""
    
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    

class MFAStatusResponse(BaseSchema):
    """Schema for MFA status response."""
    
    enabled: bool
    message: Optional[str] = None
