
"""
Schemas for VirusTotal API responses.
"""
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field


class VirusTotalRequestData(BaseModel):
    """Schema for VirusTotal request parameters."""
    hash: str = Field(..., description="SHA-256 hash of the file to analyze")


class VirusTotalResponseData(BaseModel):
    """Schema for VirusTotal API responses."""
    data: Dict[str, Any] = Field(..., description="VirusTotal analysis data")


class VirusTotalError(BaseModel):
    """Schema for VirusTotal API errors."""
    error: str = Field(..., description="Error message")
    status_code: Optional[int] = Field(None, description="HTTP status code")
