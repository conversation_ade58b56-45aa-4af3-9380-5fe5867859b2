"""
Item-related Pydantic schemas for API validation.
"""
from typing import Optional
from uuid import UUID
from pydantic import Field, validator

from api.schemas.base import BaseSchema, ResponseBase
from api.db.types import ItemStatus


class ItemBase(BaseSchema):
    """Base model for item data."""
    title: str = Field(..., min_length=3, max_length=255, 
                      description="Item title (3-255 characters)")
    description: Optional[str] = Field(None, description="Optional item description")
    status: str = Field(default=ItemStatus.ACTIVE.value, description="Item status")
    is_active: bool = Field(True, description="Whether the item is active")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "title": "Sample Item",
                "description": "This is a sample item description",
                "status": "active",
                "is_active": True
            }
        }
    }
    
    @validator("status")
    def validate_status(cls, value: str) -> str:
        """Validate that status is one of the allowed values."""
        try:
            return ItemStatus(value).value
        except ValueError:
            valid_statuses = [s.value for s in ItemStatus]
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")


class ItemCreate(ItemBase):
    """Model for creating a new item."""
    pass


class ItemUpdate(BaseSchema):
    """Model for updating item data."""
    title: Optional[str] = Field(None, min_length=3, max_length=255, 
                                description="Item title (3-255 characters)")
    description: Optional[str] = Field(None, description="Optional item description")
    status: Optional[str] = Field(None, description="Item status")
    is_active: Optional[bool] = Field(None, description="Whether the item is active")
    
    @validator("status")
    def validate_status(cls, value: Optional[str]) -> Optional[str]:
        """Validate that status is one of the allowed values, if provided."""
        if value is None:
            return None
            
        try:
            return ItemStatus(value).value
        except ValueError:
            valid_statuses = [s.value for s in ItemStatus]
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")


class ItemResponse(ResponseBase, ItemBase):
    """Model for item response."""
    owner_id: UUID = Field(..., description="ID of the item owner")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "title": "Sample Item",
                "description": "This is a sample item description",
                "status": "active",
                "is_active": True,
                "owner_id": "123e4567-e89b-12d3-a456-************",
                "created_on": "2023-01-01T12:00:00Z",
                "modified_on": "2023-01-01T12:00:00Z"
            }
        }
    }

# For backward compatibility
Item = ItemResponse
ItemInDB = ItemResponse
