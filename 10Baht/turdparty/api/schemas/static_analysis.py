"""
Schema definitions for static analysis endpoints.
"""
from enum import Enum
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, AnyHttpUrl


class ServiceStatus(BaseModel):
    """Status response for a service."""
    available: bool = Field(..., description="Whether the service is available")
    error: Optional[str] = Field(None, description="Error message if service is unavailable")


class ServicesStatusResponse(BaseModel):
    """Response containing status of all services."""
    success: bool = Field(..., description="Whether the request was successful")
    virustotal: ServiceStatus = Field(..., description="VirusTotal service status")


class HashLookupRequest(BaseModel):
    """Request for hash lookup."""
    hash_value: str = Field(..., description="Hash value to look up (MD5, SHA-1, or SHA-256)")


class VirusTotalResult(BaseModel):
    """Response from VirusTotal API."""
    data: Optional[Dict[str, Any]] = Field(None, description="VirusTotal analysis data")
    error: Optional[str] = Field(None, description="Error message if lookup failed")


class FileHashResponse(BaseModel):
    """Response containing file hashes."""
    md5: str = Field(..., description="MD5 hash of the file")
    sha1: str = Field(..., description="SHA-1 hash of the file")
    sha256: str = Field(..., description="SHA-256 hash of the file")
    
    class Config:
        schema_extra = {
            "example": {
                "md5": "d41d8cd98f00b204e9800998ecf8427e",
                "sha1": "da39a3ee5e6b4b0d3255bfef95601890afd80709",
                "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
            }
        }


class Finding(BaseModel):
    """A finding from static analysis."""
    type: str = Field(..., description="Type of finding (e.g. vulnerability, malware)")
    severity: str = Field(..., description="Severity of the finding (e.g. high, medium, low)")
    message: str = Field(..., description="Description of the finding")
    line: Optional[int] = Field(None, description="Line number where the finding was detected")


class FileAnalysisResponse(BaseModel):
    """Response for file analysis."""
    success: bool = Field(..., description="Whether the analysis was successful")
    findings: List[Finding] = Field(default_factory=list, description="List of findings from analysis")
    hashes: Optional[FileHashResponse] = Field(None, description="File hashes")
    virustotal: Optional[VirusTotalResult] = Field(None, description="VirusTotal analysis results")
    error: Optional[str] = Field(None, description="Error message if analysis failed")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "findings": [
                    {
                        "type": "vulnerability",
                        "severity": "high",
                        "message": "SQL injection vulnerability detected",
                        "line": 42
                    }
                ],
                "hashes": {
                    "md5": "d41d8cd98f00b204e9800998ecf8427e",
                    "sha1": "da39a3ee5e6b4b0d3255bfef95601890afd80709",
                    "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
                },
                "virustotal": {
                    "data": {
                        "attributes": {
                            "last_analysis_stats": {
                                "harmless": 67,
                                "malicious": 0,
                                "suspicious": 0,
                                "undetected": 3,
                                "timeout": 0
                            }
                        }
                    },
                    "error": None
                },
                "error": None
            }
        }
