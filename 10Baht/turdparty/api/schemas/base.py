"""
Base Pydantic models for API schemas.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Generic, TypeVar
from uuid import UUID
from pydantic import BaseModel, ConfigDict, Field

DataT = TypeVar('DataT')


class BaseSchema(BaseModel):
    """Base model for all Pydantic schemas with common configuration."""
    model_config = ConfigDict(
        populate_by_name=True,
        from_attributes=True,
        json_encoders={
            datetime: lambda dt: dt.isoformat(),
            UUID: lambda uid: str(uid),
        }
    )


class ResponseBase(BaseSchema):
    """Base model for all API responses with common fields."""
    id: UUID
    created_on: datetime
    modified_on: datetime


class PaginatedResponse(BaseSchema, Generic[DataT]):
    """Generic paginated response."""
    items: List[DataT]
    total: int
    page: int
    size: int
    pages: int


class ErrorResponse(BaseSchema):
    """Model for API error responses."""
    detail: str
    status_code: int = 400
    error_type: Optional[str] = None
    field_errors: Optional[Dict[str, List[str]]] = None