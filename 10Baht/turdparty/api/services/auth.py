"""Authentication service."""
import logging
from datetime import datetime, timedelta
from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession
import jwt

from api.core.config import settings
from api.core.exceptions import AuthenticationError
from api.db.dependencies import get_db_session
from api.db.repositories.user import UserRepository
from api.models.user import User
from api.schemas.token import TokenPayload, Token
from api.core.security import ALG<PERSON><PERSON>HM, verify_password, get_password_hash

logger = logging.getLogger(__name__)


class AuthService:
    """Authentication service for user login and token operations."""

    def __init__(self, db: AsyncSession):
        """
        Initialize service with database session.

        Args:
            db: Database session
        """
        self.db = db
        self.user_repository = UserRepository(db)

    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate a user with email and password.

        Args:
            email: User email
            password: Plain password

        Returns:
            User if authentication succeeds, None otherwise
        """
        try:
            return await self.user_repository.authenticate(email=email, password=password)
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}", exc_info=True)
            raise AuthenticationError(f"Authentication failed: {str(e)}")

    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """
        Create a JWT access token.

        Args:
            data: Data to encode in token
            expires_delta: Optional expiration time

        Returns:
            The encoded JWT token
        """
        to_encode = data.copy()
        expire = datetime.utcnow() + (expires_delta or timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES))
        to_encode.update({"exp": expire})
        
        return jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)

    @staticmethod
    def verify_token(token: str) -> dict:
        """
        Verify and decode a JWT token.

        Args:
            token: The JWT token to verify

        Returns:
            The decoded token payload

        Raises:
            jwt.InvalidTokenError: If token is invalid
        """
        return jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])

    @staticmethod
    def authenticate_user(user: User, password: str) -> bool:
        """
        Authenticate a user with password.

        Args:
            user: The user to authenticate
            password: The password to verify

        Returns:
            True if authentication successful, False otherwise
        """
        if not user:
            return False
        if not verify_password(password, user.hashed_password):
            return False
        return True

    def create_token_response(self, user: User) -> Token:
        """
        Create token response for authenticated user.

        Args:
            user: The authenticated user

        Returns:
            Token response with access token
        """
        access_token = self.create_access_token(
            data={"sub": str(user.id), "email": user.email}
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer"
        )

    async def logout(self, session_token: str) -> bool:
        """
        Invalidate a user session.
        
        Args:
            session_token: Session token to invalidate
            
        Returns:
            True if logout successful, False otherwise
        """
        try:
            # In a real implementation, this would invalidate tokens in a blacklist
            # or remove sessions from a database
            logger.info(f"Logging out session: {session_token}")
            # Just return True for now, in a real app we would invalidate the token
            return True
        except Exception as e:
            logger.error(f"Error during logout: {str(e)}", exc_info=True)
            return False
            
    async def validate_session(self, session_token: str) -> Optional[dict]:
        """
        Validate a session token.
        
        Args:
            session_token: Session token to validate
            
        Returns:
            Session data if valid, None otherwise
        """
        try:
            # In a real implementation, this would check if the token is valid
            # against a session store or token blacklist
            logger.info(f"Validating session: {session_token}")
            
            # Decode the token to verify its validity
            payload = jwt.decode(
                session_token,
                settings.SECRET_KEY,
                algorithms=[ALGORITHM]
            )
            
            # Check if token is expired
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
                logger.warning(f"Session token expired: {session_token}")
                return None
                
            return payload
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid session token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error validating session: {str(e)}", exc_info=True)
            return None


async def get_auth_service(db: AsyncSession = Depends(get_db_session)) -> AuthService:
    """
    Get auth service dependency.

    Args:
        db: Database session

    Returns:
        AuthService instance
    """
    return AuthService(db)