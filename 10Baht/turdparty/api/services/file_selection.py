"""
Service for file selection feature
"""
from sqlalchemy.orm import Session
from sqlalchemy import func
from api.models.file_selection import FileSelectionCreateSchema, FileSelectionUpdateSchema
from api.db.models import FileSelection, FileUpload
import uuid
from typing import List, Optional, Tuple, Dict
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from datetime import datetime

class FileSelectionService:
    """Service for file selection operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_all(self, skip: int = 0, limit: int = 100, user_id: Optional[uuid.UUID] = None) -> Tuple[List[Dict], int]:
        """Get all file selections with optional filtering by user"""
        query = self.db.query(FileSelection)
        
        if user_id:
            query = query.filter(FileSelection.owner_id == user_id)
            
        total = query.count()
        selections = query.offset(skip).limit(limit).all()
        
        # Enrich selections with file info
        enriched_selections = []
        for selection in selections:
            selection_dict = self._enrich_with_file_info(selection)
            enriched_selections.append(selection_dict)
        
        return enriched_selections, total
    
    def get_by_id(self, file_selection_id: uuid.UUID, user_id: Optional[uuid.UUID] = None) -> Optional[Dict]:
        """Get file selection by ID with optional user check"""
        query = self.db.query(FileSelection).filter(FileSelection.id == file_selection_id)
        
        if user_id:
            query = query.filter(FileSelection.owner_id == user_id)
            
        selection = query.first()
        
        if not selection:
            return None
            
        return self._enrich_with_file_info(selection)
    
    def create(self, file_selection_data: FileSelectionCreateSchema, user_id: uuid.UUID) -> Dict:
        """Create a new file selection"""
        # Check if the file upload exists and belongs to the user
        file_upload = self.db.query(FileUpload).filter(
            FileUpload.id == file_selection_data.file_upload_id,
            FileUpload.owner_id == user_id
        ).first()
        
        if not file_upload:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File upload with id {file_selection_data.file_upload_id} not found"
            )
        
        # Create database record
        db_file_selection = FileSelection(
            name=file_selection_data.name,
            description=file_selection_data.description,
            file_upload_id=file_selection_data.file_upload_id,
            target_path=file_selection_data.target_path,
            permissions=file_selection_data.permissions,
            owner_id=user_id,
            is_active=True
        )
        
        self.db.add(db_file_selection)
        self.db.commit()
        self.db.refresh(db_file_selection)
        
        return self._enrich_with_file_info(db_file_selection)
    
    def update(self, file_selection_id: uuid.UUID, file_selection_data: FileSelectionUpdateSchema, user_id: Optional[uuid.UUID] = None) -> Dict:
        """Update a file selection with optional user check"""
        query = self.db.query(FileSelection).filter(FileSelection.id == file_selection_id)
        
        if user_id:
            query = query.filter(FileSelection.owner_id == user_id)
            
        db_file_selection = query.first()
        
        if not db_file_selection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File selection with id {file_selection_id} not found"
            )
        
        # Update only provided fields
        for key, value in file_selection_data.dict(exclude_unset=True).items():
            setattr(db_file_selection, key, value)
        
        db_file_selection.modified_on = datetime.utcnow()
        self.db.commit()
        self.db.refresh(db_file_selection)
        
        return self._enrich_with_file_info(db_file_selection)
    
    def delete(self, file_selection_id: uuid.UUID, user_id: Optional[uuid.UUID] = None) -> None:
        """Delete a file selection with optional user check"""
        query = self.db.query(FileSelection).filter(FileSelection.id == file_selection_id)
        
        if user_id:
            query = query.filter(FileSelection.owner_id == user_id)
            
        db_file_selection = query.first()
        
        if not db_file_selection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File selection with id {file_selection_id} not found"
            )
        
        self.db.delete(db_file_selection)
        self.db.commit()
    
    def _enrich_with_file_info(self, file_selection: FileSelection) -> Dict:
        """Add file information to file selection"""
        # Convert ORM object to dict
        selection_dict = {c.name: getattr(file_selection, c.name) for c in file_selection.__table__.columns}
        
        # Get file information
        file_upload = self.db.query(FileUpload).filter(FileUpload.id == file_selection.file_upload_id).first()
        
        if file_upload:
            selection_dict["file_info"] = {
                "id": file_upload.id,
                "filename": file_upload.filename,
                "file_size": file_upload.file_size,
                "content_type": file_upload.content_type,
                "download_url": file_upload.download_url
            }
        else:
            selection_dict["file_info"] = None
            
        return selection_dict
