
"""
Service for managing scheduled tasks.
"""
import os
import logging
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Callable, Awaitable, Optional
import uuid

logger = logging.getLogger(__name__)

class Task:
    """Representation of a scheduled task."""
    
    def __init__(
        self,
        task_id: str,
        name: str,
        function: Callable[..., Awaitable[Any]],
        args: tuple = (),
        kwargs: Dict[str, Any] = None,
        interval: Optional[int] = None,
        cron: Optional[str] = None,
        next_run: Optional[datetime] = None,
        last_run: Optional[datetime] = None,
        last_result: Any = None,
        enabled: bool = True
    ):
        """
        Initialize a task.
        
        Args:
            task_id: Unique ID for the task
            name: Human-readable name
            function: Async function to execute
            args: Positional arguments for the function
            kwargs: Keyword arguments for the function
            interval: Interval in seconds between runs (mutually exclusive with cron)
            cron: Cron expression for scheduling (mutually exclusive with interval)
            next_run: Next scheduled run time
            last_run: Last run time
            last_result: Result of the last run
            enabled: Whether the task is enabled
        """
        self.task_id = task_id
        self.name = name
        self.function = function
        self.args = args
        self.kwargs = kwargs or {}
        self.interval = interval
        self.cron = cron
        self.next_run = next_run
        self.last_run = last_run
        self.last_result = last_result
        self.enabled = enabled
        
        if not self.next_run and self.interval:
            self.next_run = datetime.now() + timedelta(seconds=interval)
    
    async def execute(self) -> Any:
        """
        Execute the task.
        
        Returns:
            Result of the task execution
        """
        try:
            logger.info(f"Executing task: {self.name} ({self.task_id})")
            self.last_run = datetime.now()
            
            if self.interval:
                self.next_run = self.last_run + timedelta(seconds=self.interval)
            
            result = await self.function(*self.args, **self.kwargs)
            self.last_result = result
            
            logger.info(f"Task completed: {self.name} ({self.task_id})")
            return result
        except Exception as e:
            logger.error(f"Error executing task {self.name} ({self.task_id}): {str(e)}")
            self.last_result = {"error": str(e)}
            return self.last_result
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert task to dictionary.
        
        Returns:
            Dictionary representation of the task
        """
        return {
            "task_id": self.task_id,
            "name": self.name,
            "interval": self.interval,
            "cron": self.cron,
            "next_run": self.next_run.isoformat() if self.next_run else None,
            "last_run": self.last_run.isoformat() if self.last_run else None,
            "last_result": self.last_result,
            "enabled": self.enabled
        }


class SchedulerService:
    """Service for managing scheduled tasks."""
    
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        """Ensure Singleton pattern."""
        if cls._instance is None:
            cls._instance = super(SchedulerService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the scheduler service."""
        if self._initialized:
            return
            
        self.tasks: Dict[str, Task] = {}
        self.running = False
        self.scheduler_task = None
        self._initialized = True
        logger.info("SchedulerService initialized")
    
    async def start(self):
        """Start the scheduler loop."""
        if self.running:
            logger.warning("Scheduler is already running")
            return
        
        self.running = True
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        logger.info("Scheduler started")
    
    async def stop(self):
        """Stop the scheduler loop."""
        if not self.running:
            logger.warning("Scheduler is not running")
            return
        
        self.running = False
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
            self.scheduler_task = None
        
        logger.info("Scheduler stopped")
    
    async def _scheduler_loop(self):
        """Main scheduler loop."""
        try:
            while self.running:
                now = datetime.now()
                
                # Check for tasks to run
                for task_id, task in list(self.tasks.items()):
                    if (task.enabled and task.next_run and now >= task.next_run):
                        # Create a new task for execution
                        asyncio.create_task(task.execute())
                
                # Sleep for a short interval
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            logger.info("Scheduler loop cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in scheduler loop: {str(e)}")
            self.running = False
    
    def add_task(
        self,
        name: str,
        function: Callable[..., Awaitable[Any]],
        args: tuple = (),
        kwargs: Dict[str, Any] = None,
        interval: Optional[int] = None,
        cron: Optional[str] = None,
        task_id: Optional[str] = None,
        enabled: bool = True
    ) -> str:
        """
        Add a new task to the scheduler.
        
        Args:
            name: Human-readable name
            function: Async function to execute
            args: Positional arguments for the function
            kwargs: Keyword arguments for the function
            interval: Interval in seconds between runs
            cron: Cron expression for scheduling
            task_id: Optional task ID (generated if not provided)
            enabled: Whether the task is enabled
            
        Returns:
            Task ID
        """
        task_id = task_id or str(uuid.uuid4())
        
        task = Task(
            task_id=task_id,
            name=name,
            function=function,
            args=args,
            kwargs=kwargs,
            interval=interval,
            cron=cron,
            next_run=datetime.now() + timedelta(seconds=interval) if interval else None,
            enabled=enabled
        )
        
        self.tasks[task_id] = task
        logger.info(f"Added task: {name} ({task_id})")
        
        return task_id
    
    def remove_task(self, task_id: str) -> bool:
        """
        Remove a task from the scheduler.
        
        Args:
            task_id: ID of the task to remove
            
        Returns:
            Whether the task was removed
        """
        if task_id in self.tasks:
            del self.tasks[task_id]
            logger.info(f"Removed task: {task_id}")
            return True
        
        logger.warning(f"Task not found: {task_id}")
        return False
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a task.
        
        Args:
            task_id: ID of the task
            
        Returns:
            Task information or None if not found
        """
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        return task.to_dict()
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        Get information about all tasks.
        
        Returns:
            List of task information dictionaries
        """
        return [task.to_dict() for task in self.tasks.values()]
    
    def enable_task(self, task_id: str) -> bool:
        """
        Enable a task.
        
        Args:
            task_id: ID of the task to enable
            
        Returns:
            Whether the task was enabled
        """
        task = self.tasks.get(task_id)
        if not task:
            logger.warning(f"Task not found: {task_id}")
            return False
        
        task.enabled = True
        logger.info(f"Enabled task: {task_id}")
        return True
    
    def disable_task(self, task_id: str) -> bool:
        """
        Disable a task.
        
        Args:
            task_id: ID of the task to disable
            
        Returns:
            Whether the task was disabled
        """
        task = self.tasks.get(task_id)
        if not task:
            logger.warning(f"Task not found: {task_id}")
            return False
        
        task.enabled = False
        logger.info(f"Disabled task: {task_id}")
        return True
    
    def run_task_now(self, task_id: str) -> bool:
        """
        Run a task immediately.
        
        Args:
            task_id: ID of the task to run
            
        Returns:
            Whether the task was scheduled for immediate execution
        """
        task = self.tasks.get(task_id)
        if not task:
            logger.warning(f"Task not found: {task_id}")
            return False
        
        # Create a new task for execution
        asyncio.create_task(task.execute())
        logger.info(f"Scheduled immediate execution of task: {task_id}")
        return True
