#!/usr/bin/env python
"""
MinIO SSH client for connecting to MinIO server via SSH tunnel.
"""
import os
import time
import asyncio
import logging
import tempfile
import subprocess
import json
import stat
from typing import Optional, Dict, Any, Tuple, List, Union
from pathlib import Path
import socket

import boto3
import paramiko
from botocore.exceptions import ClientError
from minio import Minio
from minio.error import S3Error
import botocore

from api.core.logging_config import setup_logging

# Set up logging
logger = setup_logging(log_level=logging.INFO)

class MinIOSSHClient:
    """Client for managing MinIO server via SSH."""

    def __init__(self, host=None, port=22, username=None, pkey_path=None):
        """Initialize MinIO SSH client.

        Args:
            host (str, optional): SSH host. Defaults to None.
            port (int, optional): SSH port. Defaults to 22.
            username (str, optional): SSH username. Defaults to None.
            pkey_path (str, optional): Path to SSH private key. Defaults to None.
        """
        self._host = host
        self._port = port
        self._username = username
        self._pkey_path = pkey_path
        self.ssh_client = None
        self.connected = False
        self.tunnel_started = False
        self.tunnel_process = None

        # Set up SSH key from environment if available
        if not self._pkey_path and "MINIO_SSH_KEY" in os.environ:
            self._setup_ssh_key_from_env()

    @property
    def host(self):
        """Get SSH host."""
        return self._host

    @host.setter
    def host(self, value):
        """Set SSH host."""
        self._host = value

    @property
    def port(self):
        """Get SSH port."""
        return self._port

    @port.setter
    def port(self, value):
        """Set SSH port."""
        self._port = value

    @property
    def username(self):
        """Get SSH username."""
        return self._username

    @username.setter
    def username(self, value):
        """Set SSH username."""
        self._username = value

    @property
    def pkey_path(self):
        """Get SSH private key path."""
        return self._pkey_path

    @pkey_path.setter
    def pkey_path(self, value):
        """Set SSH private key path."""
        self._pkey_path = value
        if value and os.path.exists(value):
            os.chmod(value, 0o600)

    def _setup_ssh_key_from_env(self):
        """Set up SSH key from environment variable.

        Returns:
            bool: True if key was set up, False otherwise.
        """
        if "MINIO_SSH_KEY" in os.environ:
            key_content = os.environ["MINIO_SSH_KEY"]
            try:
                with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
                    f.write(key_content)
                    self._pkey_path = f.name
                os.chmod(self._pkey_path, 0o600)
                return True
            except Exception as e:
                logger.error(f"Failed to set up SSH key from environment: {e}")
                if self._pkey_path and os.path.exists(self._pkey_path):
                    try:
                        os.remove(self._pkey_path)
                    except:
                        pass
                self._pkey_path = None
        return False

    async def connect(self):
        """Connect to SSH server.

        Returns:
            dict: Connection result.
        """
        if not self.host:
            return {"success": False, "error": "SSH host not set"}
        if not self.username:
            return {"success": False, "error": "SSH username not set"}
        if not self.pkey_path:
            if not self._setup_ssh_key_from_env():
                return {"success": False, "error": "SSH private key not set"}

        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                hostname=self.host,
                port=self.port,
                username=self.username,
                key_filename=self.pkey_path,
                timeout=30
            )
            self.connected = True
            return {"success": True}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def start_ssh_tunnel(self, hostname: str = None):
        """Start SSH tunnel to MinIO server.

        Args:
            hostname (str, optional): SSH hostname to connect to. If provided, updates the host.

        Returns:
            dict: Tunnel start result.
        """
        if hostname:
            self.host = hostname

        if not self.connected:
            connect_result = await self.connect()
            if not connect_result["success"]:
                return connect_result

        try:
            self.tunnel_process = await asyncio.create_subprocess_exec(
                "ssh",
                "-L", "9000:localhost:9000",
                "-N",
                f"{self.username}@{self.host}",
                "-i", self.pkey_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # Wait for tunnel to establish
            await asyncio.sleep(1)
            if self.tunnel_process.returncode is None:
                self.tunnel_started = True
                return {"success": True}
            else:
                stdout, stderr = await self.tunnel_process.communicate()
                self.tunnel_started = False
                self.tunnel_process = None
                return {
                    "success": False,
                    "error": stderr.decode() if stderr else "Failed to start SSH tunnel"
                }
        except Exception as e:
            self.tunnel_started = False
            self.tunnel_process = None
            return {"success": False, "error": str(e)}

    async def stop_ssh_tunnel(self):
        """Stop SSH tunnel.

        Returns:
            dict: Tunnel stop result.
        """
        if self.tunnel_process:
            try:
                self.tunnel_process.terminate()
                await self.tunnel_process.wait()
                self.tunnel_process = None
                self.tunnel_started = False
                return {"success": True}
            except Exception as e:
                return {"success": False, "error": str(e)}
        return {"success": True}

    async def _execute_minio_command(self, command):
        """Execute MinIO command via SSH.

        Args:
            command (str): Command to execute.

        Returns:
            dict: Command execution result.
        """
        if not self.connected:
            return {"success": False, "error": "Not connected to SSH server"}

        try:
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            exit_status = stdout.channel.recv_exit_status()
            output = stdout.read().decode().strip()
            error = stderr.read().decode().strip()

            if exit_status == 0:
                return {
                    "success": True,
                    "message": output,
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "message": output,
                    "error": error or f"Command failed with exit status {exit_status}"
                }
        except Exception as e:
            return {
                "success": False,
                "message": None,
                "error": str(e)
            }

    async def list_buckets(self):
        """List MinIO buckets.

        Returns:
            dict: List buckets result.
        """
        if not self.tunnel_started:
            return {"success": False, "error": "SSH tunnel not started"}

        try:
            s3_client = self.get_s3_client()
            response = s3_client.list_buckets()
            buckets = [bucket["Name"] for bucket in response.get("Buckets", [])]
            return {"success": True, "buckets": buckets}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def create_bucket(self, bucket_name):
        """Create MinIO bucket.

        Args:
            bucket_name (str): Name of bucket to create.

        Returns:
            dict: Create bucket result.
        """
        if not self.tunnel_started:
            return {"success": False, "error": "SSH tunnel not started"}

        try:
            s3_client = self.get_s3_client()
            s3_client.create_bucket(Bucket=bucket_name)
            return {
                "success": True,
                "message": f"Bucket {bucket_name} created successfully"
            }
        except botocore.exceptions.ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"]["Message"]
            return {
                "success": False,
                "error": error_message,
                "error_code": error_code
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def delete_bucket(self, bucket_name):
        """Delete MinIO bucket.

        Args:
            bucket_name (str): Name of bucket to delete.

        Returns:
            dict: Delete bucket result.
        """
        if not self.tunnel_started:
            return {"success": False, "error": "SSH tunnel not started"}

        try:
            s3_client = self.get_s3_client()
            s3_client.delete_bucket(Bucket=bucket_name)
            return {
                "success": True,
                "message": f"Bucket {bucket_name} deleted successfully"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def delete_object(self, bucket_name, object_key):
        """Delete object from MinIO bucket.

        Args:
            bucket_name (str): Name of bucket.
            object_key (str): Key of object to delete.

        Returns:
            dict: Delete object result.
        """
        if not self.tunnel_started:
            return {"success": False, "error": "SSH tunnel not started"}

        try:
            s3_client = self.get_s3_client()
            s3_client.delete_object(Bucket=bucket_name, Key=object_key)
            return {
                "success": True,
                "message": f"Object {object_key} deleted successfully"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def list_objects(self, bucket_name, prefix=""):
        """List objects in MinIO bucket.

        Args:
            bucket_name (str): Name of bucket.
            prefix (str, optional): Filter objects by prefix. Defaults to "".

        Returns:
            dict: List objects result.
        """
        if not self.tunnel_started:
            return {"success": False, "error": "SSH tunnel not started"}

        try:
            s3_client = self.get_s3_client()
            response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
            objects = [obj["Key"] for obj in response.get("Contents", [])]
            return {
                "success": True,
                "bucket": bucket_name,
                "prefix": prefix,
                "objects": objects
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_s3_client(self):
        """Get S3 client for MinIO operations.

        Returns:
            boto3.client: S3 client.
        """
        return boto3.client(
            "s3",
            endpoint_url="http://localhost:9000",
            aws_access_key_id="minioadmin",
            aws_secret_access_key="minioadmin",
            region_name="us-east-1"
        )

    async def close(self):
        """Close SSH connection and stop tunnel.

        Returns:
            dict: Close result.
        """
        if self.tunnel_started:
            await self.stop_ssh_tunnel()
        if self.connected:
            self.ssh_client.close()
            self.connected = False
        return {"success": True}

    close_tunnel = stop_ssh_tunnel

    async def cleanup(self):
        """Clean up resources."""
        if self.tunnel_process:
            await self.stop_ssh_tunnel()
        if self.ssh_client:
            self.ssh_client.close()
        if self._pkey_path and os.path.exists(self._pkey_path):
            try:
                os.remove(self._pkey_path)
            except:
                pass  # Ignore cleanup errors

    async def upload_object(self, bucket_name: str, object_name: str, data: Union[str, bytes]) -> Dict[str, Any]:
        """Upload an object to a bucket.
        
        Args:
            bucket_name: Name of bucket
            object_name: Name of object
            data: Object data
            
        Returns:
            Dict containing success status and message/error
        """
        if not self.tunnel_started:
            return {"success": False, "error": "SSH tunnel not started"}
            
        try:
            s3_client = self.get_s3_client()
            
            if isinstance(data, str):
                data = data.encode()
                
            s3_client.put_object(Bucket=bucket_name, Key=object_name, Body=data)
            return {"success": True, "message": f"Object {object_name} uploaded successfully"}
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            return {
                "success": False,
                "error": error_message,
                "error_code": error_code
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def download_object(self, bucket_name: str, object_name: str, file_path: str) -> Dict[str, Any]:
        """Download an object from a bucket.
        
        Args:
            bucket_name: Name of bucket
            object_name: Name of object
            file_path: Path to save downloaded object
            
        Returns:
            Dict containing success status and message/error
        """
        if not self.tunnel_started:
            return {"success": False, "error": "SSH tunnel not started"}
            
        try:
            s3_client = self.get_s3_client()
            s3_client.download_file(bucket_name, object_name, file_path)
            return {"success": True, "message": f"Object {object_name} downloaded successfully"}
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            return {
                "success": False,
                "error": error_message,
                "error_code": error_code
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def get_server_status(self) -> Dict[str, Any]:
        """
        Get MinIO server status information.
        
        Returns:
            Dict[str, Any]: Dictionary containing server status information:
                - status: "success" or "error"
                - service: "minio"
                - mode: "online" or "offline"
                - version: Server version
                - uptime: Server uptime
                - cpu: CPU usage information
                - memory: Memory usage information
                - disk: Disk usage information
        """
        if not self.tunnel_started:
            return {
                "status": "error",
                "service": "minio",
                "mode": "offline",
                "version": "unknown",
                "uptime": "",
                "cpu": {},
                "memory": {},
                "disk": {}
            }
            
        try:
            # Use S3 client to check server status
            s3_client = self.get_s3_client()
            
            # Try to list buckets to check if server is accessible
            s3_client.list_buckets()
            
            # If we get here, the server is accessible
            return {
                "status": "success",
                "service": "minio",
                "mode": "online",
                "version": "unknown",  # Can't get version info through S3 API
                "uptime": "",  # Can't get uptime through S3 API
                "cpu": {},  # Can't get CPU info through S3 API
                "memory": {},  # Can't get memory info through S3 API
                "disk": {}  # Can't get disk info through S3 API
            }
        except Exception as e:
            logger.error(f"Error getting server status via S3 client: {str(e)}")
            return {
                "status": "error",
                "service": "minio",
                "mode": "offline",
                "version": "unknown",
                "uptime": "",
                "cpu": {},
                "memory": {},
                "disk": {}
            }

    async def get_buckets_capacity(self) -> Dict[str, Dict[str, Any]]:
        """
        Get capacity information for all buckets.
        
        Returns:
            Dict[str, Dict[str, Any]]: Dictionary mapping bucket names to their capacity info:
                - size: Total size used in bytes
                - objects: Number of objects
                - versions: Number of versions
        """
        if not self.tunnel_started:
            return {}
            
        try:
            # Get S3 client
            s3_client = self.get_s3_client()
            
            # List all buckets
            buckets = s3_client.list_buckets()['Buckets']
            
            # Initialize result dictionary
            result = {}
            
            # Get capacity for each bucket
            for bucket in buckets:
                bucket_name = bucket['Name']
                
                # List objects in the bucket
                response = s3_client.list_objects_v2(Bucket=bucket_name)
                
                # Calculate total size and object count
                total_size = 0
                object_count = 0
                if 'Contents' in response:
                    for obj in response['Contents']:
                        total_size += obj['Size']
                        object_count += 1
                
                # Store bucket information
                result[bucket_name] = {
                    'size': total_size,
                    'objects': object_count,
                    'versions': 0  # MinIO doesn't support versioning by default
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting buckets capacity: {str(e)}")
            return {}
