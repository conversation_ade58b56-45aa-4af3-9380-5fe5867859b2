
"""
Service for managing and sending notifications.
"""
import os
import logging
import json
import smtplib
import aiohttp
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class NotificationService:
    """Service for managing and sending notifications."""
    
    def __init__(self):
        """Initialize the notification service."""
        # Email settings
        self.smtp_server = os.getenv("SMTP_SERVER", "")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_username = os.getenv("SMTP_USERNAME", "")
        self.smtp_password = os.getenv("SMTP_PASSWORD", "")
        self.from_email = os.getenv("FROM_EMAIL", "")
        
        # Webhook settings
        self.webhook_url = os.getenv("NOTIFICATION_WEBHOOK_URL", "")
        
        # In-memory notification storage
        self.notifications: List[Dict[str, Any]] = []
        
        logger.info("NotificationService initialized")
    
    async def send_email(
        self, 
        recipient: str, 
        subject: str, 
        body: str,
        html_body: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Send an email notification.
        
        Args:
            recipient: Email recipient
            subject: Email subject
            body: Plain text email body
            html_body: HTML email body (optional)
            
        Returns:
            Dictionary with operation result
        """
        if not all([self.smtp_server, self.smtp_port, self.smtp_username, self.smtp_password, self.from_email]):
            logger.error("SMTP settings not configured")
            return {
                "success": False,
                "error": "SMTP settings not configured"
            }
        
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.from_email
            msg['To'] = recipient
            
            # Attach plain text and HTML parts
            msg.attach(MIMEText(body, 'plain'))
            if html_body:
                msg.attach(MIMEText(html_body, 'html'))
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Email notification sent to {recipient}")
            
            # Store notification
            notification_id = str(uuid.uuid4())
            notification = {
                "id": notification_id,
                "type": "email",
                "recipient": recipient,
                "subject": subject,
                "body": body,
                "timestamp": datetime.now().isoformat(),
                "status": "sent"
            }
            self.notifications.append(notification)
            
            return {
                "success": True,
                "notification_id": notification_id
            }
        except Exception as e:
            logger.error(f"Error sending email notification: {str(e)}")
            
            # Store failed notification
            notification_id = str(uuid.uuid4())
            notification = {
                "id": notification_id,
                "type": "email",
                "recipient": recipient,
                "subject": subject,
                "body": body,
                "timestamp": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e)
            }
            self.notifications.append(notification)
            
            return {
                "success": False,
                "error": str(e),
                "notification_id": notification_id
            }
    
    async def send_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send a webhook notification.
        
        Args:
            payload: Webhook payload
            
        Returns:
            Dictionary with operation result
        """
        if not self.webhook_url:
            logger.error("Webhook URL not configured")
            return {
                "success": False,
                "error": "Webhook URL not configured"
            }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    response_text = await response.text()
                    response_status = response.status
            
            success = 200 <= response_status < 300
            
            # Store notification
            notification_id = str(uuid.uuid4())
            notification = {
                "id": notification_id,
                "type": "webhook",
                "url": self.webhook_url,
                "payload": payload,
                "timestamp": datetime.now().isoformat(),
                "status": "sent" if success else "failed",
                "response_status": response_status,
                "response_text": response_text
            }
            self.notifications.append(notification)
            
            if success:
                logger.info(f"Webhook notification sent successfully")
                return {
                    "success": True,
                    "notification_id": notification_id,
                    "response_status": response_status,
                    "response_text": response_text
                }
            else:
                logger.error(f"Error sending webhook notification: HTTP {response_status}")
                return {
                    "success": False,
                    "error": f"HTTP {response_status}: {response_text}",
                    "notification_id": notification_id
                }
        except Exception as e:
            logger.error(f"Error sending webhook notification: {str(e)}")
            
            # Store failed notification
            notification_id = str(uuid.uuid4())
            notification = {
                "id": notification_id,
                "type": "webhook",
                "url": self.webhook_url,
                "payload": payload,
                "timestamp": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e)
            }
            self.notifications.append(notification)
            
            return {
                "success": False,
                "error": str(e),
                "notification_id": notification_id
            }
    
    async def send_in_app_notification(
        self, 
        user_id: Union[int, str],
        title: str, 
        message: str, 
        level: str = "info"
    ) -> Dict[str, Any]:
        """
        Send an in-app notification.
        
        Args:
            user_id: User ID
            title: Notification title
            message: Notification message
            level: Notification level (info, warning, error)
            
        Returns:
            Dictionary with operation result
        """
        try:
            notification_id = str(uuid.uuid4())
            notification = {
                "id": notification_id,
                "type": "in_app",
                "user_id": user_id,
                "title": title,
                "message": message,
                "level": level,
                "timestamp": datetime.now().isoformat(),
                "read": False,
                "status": "sent"
            }
            
            self.notifications.append(notification)
            logger.info(f"In-app notification sent to user {user_id}")
            
            return {
                "success": True,
                "notification_id": notification_id
            }
        except Exception as e:
            logger.error(f"Error sending in-app notification: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_notifications(
        self, 
        user_id: Optional[Union[int, str]] = None,
        notification_type: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get notifications.
        
        Args:
            user_id: Filter by user ID (optional)
            notification_type: Filter by notification type (optional)
            status: Filter by status (optional)
            limit: Maximum number of notifications to return
            
        Returns:
            List of notification dictionaries
        """
        filtered = self.notifications
        
        # Apply filters
        if user_id is not None:
            filtered = [n for n in filtered if n.get("user_id") == user_id]
        
        if notification_type is not None:
            filtered = [n for n in filtered if n.get("type") == notification_type]
        
        if status is not None:
            filtered = [n for n in filtered if n.get("status") == status]
        
        # Sort by timestamp (newest first) and limit
        filtered.sort(key=lambda n: n.get("timestamp", ""), reverse=True)
        return filtered[:limit]
    
    def mark_notification_as_read(self, notification_id: str) -> bool:
        """
        Mark a notification as read.
        
        Args:
            notification_id: Notification ID
            
        Returns:
            Whether the notification was marked as read
        """
        for notification in self.notifications:
            if notification.get("id") == notification_id and notification.get("type") == "in_app":
                notification["read"] = True
                return True
        
        return False
    
    def delete_notification(self, notification_id: str) -> bool:
        """
        Delete a notification.
        
        Args:
            notification_id: Notification ID
            
        Returns:
            Whether the notification was deleted
        """
        for i, notification in enumerate(self.notifications):
            if notification.get("id") == notification_id:
                self.notifications.pop(i)
                return True
        
        return False
