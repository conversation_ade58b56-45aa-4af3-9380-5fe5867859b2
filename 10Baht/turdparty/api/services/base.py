"""Base service module for shared functionality."""
import logging
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union, cast

from fastapi import HTTPException, status
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from api.core.errors import (
    ValidationError, 
    ResourceNotFoundException,
    NotFoundError,
    DatabaseError,
    APIError as ApplicationException
)
from api.db.repositories.base import BaseRepository
from api.models.base import Base

logger = logging.getLogger(__name__)

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ResponseSchemaType = TypeVar("ResponseSchemaType", bound=BaseModel)


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType, ResponseSchemaType]):
    """Base service class with default CRUD operations."""

    def __init__(
        self,
        repository: BaseRepository,
        response_model: Type[ResponseSchemaType]
    ):
        """
        Initialize service with repository.

        Args:
            repository: Repository instance for database operations
            response_model: Pydantic model for response formatting
        """
        self.repository = repository
        self.response_model = response_model

    async def create(self, obj_in: CreateSchemaType, **kwargs: Any) -> ResponseSchemaType:
        """
        Create a new object.

        Args:
            obj_in: Object data to create

        Returns:
            Created object

        Raises:
            ValidationException: If creation validation fails
            DatabaseException: If database operation fails
        """
        try:
            db_obj = await self.repository.create(obj_in=obj_in, **kwargs)
            return cast(ResponseSchemaType, db_obj)
        except Exception as e:
            logger.error(f"Error creating object: {str(e)}", exc_info=True)
            raise DatabaseException(f"Failed to create object: {str(e)}")

    async def get(self, id: Any) -> Optional[ResponseSchemaType]:
        """
        Get an object by ID.

        Args:
            id: Object ID

        Returns:
            Object if found, None otherwise

        Raises:
            NotFoundError: If object not found
            DatabaseException: If database operation fails
        """
        try:
            db_obj = await self.repository.get(id=id)
            if not db_obj:
                raise NotFoundError(f"Object with ID {id} not found")
            return cast(ResponseSchemaType, db_obj)
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error retrieving object with ID {id}: {str(e)}", exc_info=True)
            raise DatabaseException(f"Failed to retrieve object: {str(e)}")

    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        **kwargs: Any
    ) -> List[ResponseSchemaType]:
        """
        Get multiple objects with optional filtering.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of objects

        Raises:
            DatabaseException: If database operation fails
        """
        try:
            objects = await self.repository.get_multi(skip=skip, limit=limit, **kwargs)
            return cast(List[ResponseSchemaType], objects)
        except Exception as e:
            logger.error(f"Error retrieving multiple objects: {str(e)}", exc_info=True)
            raise DatabaseException(f"Failed to retrieve objects: {str(e)}")

    async def update(
        self, 
        id: Any, 
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ResponseSchemaType:
        """
        Update an object.

        Args:
            id: Object ID
            obj_in: Update data

        Returns:
            Updated object

        Raises:
            NotFoundError: If object not found
            ValidationException: If update validation fails
            DatabaseException: If database operation fails
        """
        try:
            db_obj = await self.repository.get(id=id)
            if not db_obj:
                raise NotFoundError(f"Object with ID {id} not found")

            updated_obj = await self.repository.update(db_obj=db_obj, obj_in=obj_in)
            return cast(ResponseSchemaType, updated_obj)
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error updating object with ID {id}: {str(e)}", exc_info=True)
            raise DatabaseException(f"Failed to update object: {str(e)}")

    async def delete(self, id: Any) -> None:
        """
        Delete an object.

        Args:
            id: Object ID

        Raises:
            NotFoundError: If object not found
            DatabaseException: If database operation fails
        """
        try:
            result = await self.repository.delete(id=id)
            if not result:
                raise NotFoundError(f"Object with ID {id} not found")
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error deleting object with ID {id}: {str(e)}", exc_info=True)
            raise DatabaseException(f"Failed to delete object: {str(e)}")


async def handle_exceptions(exception: Exception, message: str) -> None:
    """
    Handle common exceptions and convert to appropriate HTTP exceptions.

    Args:
        exception: The exception that occurred
        message: Base error message

    Raises:
        HTTPException: Appropriate HTTP exception based on the original exception
    """
    logger.error(f"{message}: {str(exception)}", exc_info=True)

    if isinstance(exception, NotFoundError):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(exception)
        )
    elif isinstance(exception, ValidationException):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(exception)
        )
    elif isinstance(exception, DatabaseException):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(exception)}"
        )
    elif isinstance(exception, ApplicationException):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(exception)
        )
    else:
        # Generic exception handling
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"{message}: {str(exception)}"
        )