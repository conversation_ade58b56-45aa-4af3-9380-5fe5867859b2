
"""Item service module."""

import logging
from typing import List, Optional
from uuid import UUID
from fastapi import Depends
from sqlalchemy.orm import Session

from api.db.repositories.item import ItemRepository
from api.db.models.item import Item
from api.schemas.item import ItemCreate, ItemUpdate
from api.core.errors import NotFoundError, DatabaseError

logger = logging.getLogger(__name__)

class ItemService:
    """Service for item operations."""

    def __init__(self, item_repo: ItemRepository):
        """
        Initialize service with item repository
        
        Args:
            item_repo: Item repository
        """
        self.repository = item_repo
        logger.debug("ItemService initialized")

    async def get_items(self, skip: int = 0, limit: int = 100) -> List[Item]:
        """
        Retrieve items with pagination
        
        Args:
            skip: Number of items to skip
            limit: Maximum number of items to return
            
        Returns:
            List of items
        """
        try:
            logger.info(f"Retrieving items with skip={skip}, limit={limit}")
            items = self.repository.get_all(skip=skip, limit=limit)
            logger.debug(f"Retrieved {len(items)} items")
            return items
        except Exception as e:
            logger.error(f"Error retrieving items: {str(e)}", exc_info=True)
            raise DatabaseError(message="Failed to retrieve items")

    async def get_item(self, item_id: str) -> Item:
        """
        Retrieve a specific item by ID
        
        Args:
            item_id: Item ID
            
        Returns:
            Item
            
        Raises:
            NotFoundError: If item not found
        """
        try:
            logger.info(f"Retrieving item with id={item_id}")
            item = self.repository.get(id=UUID(item_id))
            if not item:
                logger.warning(f"Item with id={item_id} not found")
                raise NotFoundError(resource_type="Item", resource_id=item_id)
            logger.debug(f"Retrieved item: {item.id}")
            return item
        except NotFoundError:
            raise
        except ValueError as e:
            logger.error(f"Invalid UUID format: {str(e)}")
            raise NotFoundError(
                resource_type="Item", 
                resource_id=item_id, 
                message="Invalid item ID format"
            )
        except Exception as e:
            logger.error(f"Error retrieving item {item_id}: {str(e)}", exc_info=True)
            raise DatabaseError(message=f"Failed to retrieve item {item_id}")

    async def create_item(self, item_data: ItemCreate) -> Item:
        """
        Create a new item
        
        Args:
            item_data: Item data
            
        Returns:
            Created item
        """
        try:
            logger.info(f"Creating new item with name={item_data.name}")
            item = self.repository.create(obj_in=item_data)
            logger.info(f"Created item with id={item.id}")
            return item
        except Exception as e:
            logger.error(f"Error creating item: {str(e)}", exc_info=True)
            raise DatabaseError(message="Failed to create item")

    async def update_item(self, item_id: str, item_data: ItemUpdate) -> Item:
        """
        Update an existing item
        
        Args:
            item_id: Item ID
            item_data: Item data
            
        Returns:
            Updated item
            
        Raises:
            NotFoundError: If item not found
        """
        try:
            logger.info(f"Updating item with id={item_id}")
            item = await self.get_item(item_id)
            
            updated_item = self.repository.update(db_obj=item, obj_in=item_data)
            logger.info(f"Updated item with id={item_id}")
            return updated_item
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error updating item {item_id}: {str(e)}", exc_info=True)
            raise DatabaseError(message=f"Failed to update item {item_id}")

    async def delete_item(self, item_id: str) -> Item:
        """
        Delete an item (soft delete)
        
        Args:
            item_id: Item ID
            
        Returns:
            Deleted item
            
        Raises:
            NotFoundError: If item not found
        """
        try:
            logger.info(f"Soft deleting item with id={item_id}")
            item = await self.get_item(item_id)
            
            deleted_item = self.repository.soft_delete(id=UUID(item_id))
            logger.info(f"Soft deleted item with id={item_id}")
            return deleted_item
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error soft deleting item {item_id}: {str(e)}", exc_info=True)
            raise DatabaseError(message=f"Failed to delete item {item_id}")

    async def hard_delete_item(self, item_id: str) -> Item:
        """
        Permanently delete an item
        
        Args:
            item_id: Item ID
            
        Returns:
            Deleted item
            
        Raises:
            NotFoundError: If item not found
        """
        try:
            logger.info(f"Hard deleting item with id={item_id}")
            item = await self.get_item(item_id)
            
            deleted_item = self.repository.delete(id=UUID(item_id))
            logger.info(f"Hard deleted item with id={item_id}")
            return deleted_item
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error hard deleting item {item_id}: {str(e)}", exc_info=True)
            raise DatabaseError(message=f"Failed to permanently delete item {item_id}")

    async def restore_item(self, item_id: str) -> Item:
        """
        Restore a soft-deleted item
        
        Args:
            item_id: Item ID
            
        Returns:
            Restored item
            
        Raises:
            NotFoundError: If item not found
        """
        try:
            logger.info(f"Restoring deleted item with id={item_id}")
            # For restore, we need to include deleted items in the search
            item = self.repository.get(id=UUID(item_id), include_deleted=True)
            if not item:
                logger.warning(f"Item with id={item_id} not found")
                raise NotFoundError(resource_type="Item", resource_id=item_id)
                
            if not item.deleted_on:
                logger.warning(f"Item with id={item_id} is not deleted")
                return item
                
            restored_item = self.repository.restore(id=UUID(item_id))
            logger.info(f"Restored item with id={item_id}")
            return restored_item
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error restoring item {item_id}: {str(e)}", exc_info=True)
            raise DatabaseError(message=f"Failed to restore item {item_id}")

    async def get_active_items(self, skip: int = 0, limit: int = 100) -> List[Item]:
        """
        Retrieve all active items
        
        Args:
            skip: Number of items to skip
            limit: Maximum number of items to return
            
        Returns:
            List of active items
        """
        try:
            logger.info(f"Retrieving active items with skip={skip}, limit={limit}")
            items = self.repository.get_active_items(skip=skip, limit=limit)
            logger.debug(f"Retrieved {len(items)} active items")
            return items
        except Exception as e:
            logger.error(f"Error retrieving active items: {str(e)}", exc_info=True)
            raise DatabaseError(message="Failed to retrieve active items")

    async def toggle_item_active_status(self, item_id: str) -> Item:
        """
        Toggle the active status of an item
        
        Args:
            item_id: Item ID
            
        Returns:
            Updated item
            
        Raises:
            NotFoundError: If item not found
        """
        try:
            logger.info(f"Toggling active status for item with id={item_id}")
            item = await self.get_item(item_id)
            
            updated_item = self.repository.toggle_active_status(id=UUID(item_id))
            new_status = "active" if updated_item.is_active else "inactive"
            logger.info(f"Item with id={item_id} is now {new_status}")
            return updated_item
        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error toggling active status for item {item_id}: {str(e)}", exc_info=True)
            raise DatabaseError(message=f"Failed to toggle active status for item {item_id}")
