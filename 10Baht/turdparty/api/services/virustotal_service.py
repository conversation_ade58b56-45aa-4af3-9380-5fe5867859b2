
#!/usr/bin/env python
"""
VirusTotal API service for file and hash analysis.
"""
import os
import logging
from typing import Dict, Any, Optional
import requests
from requests.exceptions import RequestException, Timeout

from api.core.logging_config import setup_logging

# Set up logging
logger = setup_logging()

class VirusTotalService:
    """Service for interacting with the VirusTotal API."""
    
    BASE_URL = "https://www.virustotal.com/api/v3"
    
    def __init__(self):
        """Initialize the VirusTotal service with API key from environment variables."""
        self.api_key = os.getenv("VIRUSTOTAL_API_KEY")
        if not self.api_key:
            logger.warning("VIRUSTOTAL_API_KEY not set in environment variables")
        
        self.session = requests.Session()
        self.session.headers = {'X-Apikey': self.api_key}
    
    def get_hash_analysis(self, file_hash: str) -> Dict[str, Any]:
        """
        Get analysis results for a specific file hash.
        
        Args:
            file_hash: The SHA-256 hash of the file to analyze
            
        Returns:
            Dictionary containing the analysis results
            
        Raises:
            Exception: If there's an error getting the analysis
        """
        try:
            if not self.api_key:
                raise ValueError("VirusTotal API key not configured")
                
            url = f"{self.BASE_URL}/files/{file_hash}"
            logger.info(f"Requesting VirusTotal analysis for hash: {file_hash}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            return response.json()
        except RequestException as e:
            logger.error(f"Error getting VirusTotal analysis: {str(e)}")
            raise Exception(f"VirusTotal API error: {str(e)}")
        except Timeout:
            logger.error("VirusTotal API request timed out")
            raise Exception("VirusTotal API request timed out")
        except Exception as e:
            logger.error(f"Unexpected error with VirusTotal API: {str(e)}")
            raise
    
    def get_monitor_analysis(self, file_hash: str) -> Dict[str, Any]:
        """
        Get monitor partner analyses for a specific hash.
        
        Args:
            file_hash: The SHA-256 hash to get monitor analyses for
            
        Returns:
            Dictionary containing the monitor analyses results
            
        Raises:
            Exception: If there's an error getting the analyses
        """
        try:
            if not self.api_key:
                raise ValueError("VirusTotal API key not configured")
                
            url = f"{self.BASE_URL}/monitor_partner/hashes/{file_hash}/analyses"
            logger.info(f"Requesting VirusTotal monitor analyses for hash: {file_hash}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            return response.json()
        except RequestException as e:
            logger.error(f"Error getting VirusTotal monitor analyses: {str(e)}")
            raise Exception(f"VirusTotal API error: {str(e)}")
        except Timeout:
            logger.error("VirusTotal API request timed out")
            raise Exception("VirusTotal API request timed out")
        except Exception as e:
            logger.error(f"Unexpected error with VirusTotal API: {str(e)}")
            raise
