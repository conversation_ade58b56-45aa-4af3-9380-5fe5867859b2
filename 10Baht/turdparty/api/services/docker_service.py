
"""
Service for managing Docker containers.
"""
import os
import json
import logging
import async<PERSON>
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class DockerService:
    """Service for managing Docker containers via SSH tunneling."""
    
    def __init__(self):
        """Initialize Docker service."""
        from api.services.fathom_ssh_client import FathomSSHClient
        self.ssh_client = FathomSSHClient()
        self.default_server = os.getenv("DOCKER_HOST_SERVER", "localhost")
        logger.info("DockerService initialized")
    
    async def list_containers(self, server: Optional[str] = None) -> Dict[str, Any]:
        """
        List Docker containers on the specified server.
        
        Args:
            server: Server hostname or IP (optional)
            
        Returns:
            Dictionary with container list or error information
        """
        target_server = server or self.default_server
        try:
            result = await self.ssh_client.execute_command(
                target_server, 
                "docker ps --format '{{.ID}},{{.Image}},{{.Status}},{{.Names}}' -a"
            )
            
            if result.get("error"):
                logger.error(f"Error listing containers: {result['error']}")
                return {"error": result["error"]}
            
            containers = []
            for line in result.get("stdout", "").strip().split("\n"):
                if line:
                    parts = line.split(",")
                    if len(parts) >= 4:
                        containers.append({
                            "id": parts[0],
                            "image": parts[1],
                            "status": parts[2],
                            "name": parts[3]
                        })
            
            return {
                "success": True,
                "containers": containers
            }
        except Exception as e:
            logger.error(f"Error listing containers: {str(e)}")
            return {"error": str(e)}
    
    async def start_container(self, container_id: str, server: Optional[str] = None) -> Dict[str, Any]:
        """
        Start a Docker container.
        
        Args:
            container_id: Container ID or name
            server: Server hostname or IP (optional)
            
        Returns:
            Dictionary with operation result
        """
        target_server = server or self.default_server
        try:
            result = await self.ssh_client.execute_command(
                target_server, 
                f"docker start {container_id}"
            )
            
            if result.get("error") or result.get("exit_code", 0) != 0:
                error_msg = result.get("error") or result.get("stderr", "Unknown error")
                logger.error(f"Error starting container {container_id}: {error_msg}")
                return {"error": error_msg}
            
            return {
                "success": True,
                "message": f"Container {container_id} started successfully"
            }
        except Exception as e:
            logger.error(f"Error starting container {container_id}: {str(e)}")
            return {"error": str(e)}
    
    async def stop_container(self, container_id: str, server: Optional[str] = None) -> Dict[str, Any]:
        """
        Stop a Docker container.
        
        Args:
            container_id: Container ID or name
            server: Server hostname or IP (optional)
            
        Returns:
            Dictionary with operation result
        """
        target_server = server or self.default_server
        try:
            result = await self.ssh_client.execute_command(
                target_server, 
                f"docker stop {container_id}"
            )
            
            if result.get("error") or result.get("exit_code", 0) != 0:
                error_msg = result.get("error") or result.get("stderr", "Unknown error")
                logger.error(f"Error stopping container {container_id}: {error_msg}")
                return {"error": error_msg}
            
            return {
                "success": True,
                "message": f"Container {container_id} stopped successfully"
            }
        except Exception as e:
            logger.error(f"Error stopping container {container_id}: {str(e)}")
            return {"error": str(e)}
    
    async def get_container_logs(self, container_id: str, lines: int = 100, server: Optional[str] = None) -> Dict[str, Any]:
        """
        Get logs from a Docker container.
        
        Args:
            container_id: Container ID or name
            lines: Number of log lines to retrieve
            server: Server hostname or IP (optional)
            
        Returns:
            Dictionary with container logs or error information
        """
        target_server = server or self.default_server
        try:
            result = await self.ssh_client.execute_command(
                target_server, 
                f"docker logs --tail {lines} {container_id}"
            )
            
            if result.get("error") or result.get("exit_code", 0) != 0:
                error_msg = result.get("error") or result.get("stderr", "Unknown error")
                logger.error(f"Error getting logs for container {container_id}: {error_msg}")
                return {"error": error_msg}
            
            return {
                "success": True,
                "logs": result.get("stdout", "")
            }
        except Exception as e:
            logger.error(f"Error getting logs for container {container_id}: {str(e)}")
            return {"error": str(e)}
    
    async def get_container_stats(self, container_id: str, server: Optional[str] = None) -> Dict[str, Any]:
        """
        Get statistics for a Docker container.
        
        Args:
            container_id: Container ID or name
            server: Server hostname or IP (optional)
            
        Returns:
            Dictionary with container statistics or error information
        """
        target_server = server or self.default_server
        try:
            result = await self.ssh_client.execute_command(
                target_server, 
                f"docker stats {container_id} --no-stream --format '{{{{.CPUPerc}}}},{{{{.MemUsage}}}},{{{{.NetIO}}}},{{{{.BlockIO}}}}'"
            )
            
            if result.get("error") or result.get("exit_code", 0) != 0:
                error_msg = result.get("error") or result.get("stderr", "Unknown error")
                logger.error(f"Error getting stats for container {container_id}: {error_msg}")
                return {"error": error_msg}
            
            output = result.get("stdout", "").strip()
            if not output:
                return {"error": "No stats available"}
            
            parts = output.split(",")
            if len(parts) >= 4:
                return {
                    "success": True,
                    "stats": {
                        "cpu_usage": parts[0],
                        "memory_usage": parts[1],
                        "network_io": parts[2],
                        "block_io": parts[3]
                    }
                }
            else:
                return {"error": "Invalid stats format"}
        except Exception as e:
            logger.error(f"Error getting stats for container {container_id}: {str(e)}")
            return {"error": str(e)}
