"""
SSH client service for connecting to Vagrant VMs via Fathom.
"""
import logging
import asyncio
import os
import tempfile
import base64
from typing import Dict, List, Any, Optional, Tuple
import json
import subprocess
from pathlib import Path

from api.core.exceptions import NotFoundError, ServiceError

logger = logging.getLogger(__name__)

class FathomSSHClient:
    """SSH client for connecting to Vagrant VMs."""

    def __init__(self, 
                ssh_config: Optional[Dict[str, Any]] = None,
                ssh_key_path: Optional[str] = None,
                ssh_key: Optional[str] = None):
        """Initialize SSH client with configuration."""
        self.ssh_config = ssh_config or {}
        self.ssh_key_path = ssh_key_path or os.path.expanduser("~/.ssh/replit")
        self.ssh_key = ssh_key
        self.temp_key_file = None
        logger.info("SSH client initialized")

    async def connect(self) -> Dict[str, Any]:
        """Connect to SSH server."""
        try:
            # If SSH key is provided as a string, save it to a temporary file
            if self.ssh_key:
                self.temp_key_file = tempfile.NamedTemporaryFile(delete=False)
                self.temp_key_file.write(self.ssh_key.encode())
                self.temp_key_file.flush()
                self.ssh_key_path = self.temp_key_file.name
                os.chmod(self.ssh_key_path, 0o600)
                logger.info("Using provided SSH key")

            # Verify SSH key exists
            if not os.path.exists(self.ssh_key_path):
                error_msg = f"SSH key not found at {self.ssh_key_path}"
                logger.error(error_msg)
                return {"error": error_msg}

            logger.info("SSH client connected using key at %s", self.ssh_key_path)
            return {"success": True}
        except Exception as e:
            logger.error("Error connecting to SSH: %s", str(e))
            return {"error": str(e)}

    async def close(self) -> None:
        """Close SSH client."""
        try:
            # Clean up temporary key file if it exists
            if self.temp_key_file:
                self.temp_key_file.close()
                os.unlink(self.temp_key_file.name)
                self.temp_key_file = None
                logger.debug("Temporary SSH key file removed")
            logger.debug("SSH client closed")
        except Exception as e:
            logger.error("Error closing SSH client: %s", str(e))

    async def _execute_ssh_command(
            self, hostname: str, command: str
        ) -> Tuple[bool, str, str, int]:
        """
        Execute a command on the remote host via SSH.

        Args:
            hostname: The hostname to connect to
            command: The command to execute

        Returns:
            Tuple of (success, stdout, stderr, exit_code)
        """
        ssh_user = self.ssh_config.get("username", "vagrant")

        try:
            logger.debug("Executing SSH command on %s: %s", hostname, command)

            # Construct the ssh command
            ssh_cmd = [
                "ssh",
                "-i", self.ssh_key_path,
                "-o", "StrictHostKeyChecking=accept-new",
                "-o", "UserKnownHostsFile=/dev/null",
                f"{ssh_user}@{hostname}",
                command
            ]

            # Execute the command
            process = await asyncio.create_subprocess_exec(
                *ssh_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            # Decode and log the results
            stdout_str = stdout.decode().strip()
            stderr_str = stderr.decode().strip()
            exit_code = process.returncode

            logger.debug("SSH command result: exit_code=%d", exit_code)
            if stdout_str:
                logger.debug("SSH command stdout: %s", stdout_str)
            if stderr_str:
                logger.debug("SSH command stderr: %s", stderr_str)

            return (exit_code == 0), stdout_str, stderr_str, exit_code
        except Exception as e:
            logger.error("Error executing SSH command: %s", str(e))
            return False, "", str(e), -1

    async def status(self, hostname: str) -> Dict[str, Any]:
        """
        Get Vagrant VM status.

        Args:
            hostname: The hostname to check

        Returns:
            Dictionary with status information
        """
        try:
            success, stdout, stderr, _ = await self._execute_ssh_command(
                hostname, "vagrant status --machine-readable"
            )

            if not success:
                logger.error("Error getting VM status: %s", stderr)
                return {
                    "hostname": hostname,
                    "error": stderr or "Failed to get VM status"
                }

            # Parse machine-readable output
            status_info = {"hostname": hostname}

            for line in stdout.splitlines():
                parts = line.strip().split(',')
                if len(parts) >= 4:
                    data_type = parts[2]
                    data_value = parts[3]

                    if data_type == "state":
                        status_info["state"] = data_value
                    elif data_type == "provider-name":
                        status_info["provider"] = data_value

            # If we couldn't find the state, consider it an error
            if "state" not in status_info:
                logger.error("Could not determine VM state from output")
                status_info["error"] = "Could not determine VM state"
                status_info["raw_output"] = stdout

            return status_info
        except Exception as e:
            logger.error("Error in status method: %s", str(e))
            return {
                "hostname": hostname,
                "error": str(e)
            }

    async def up(self, hostname: str, provision: bool = True) -> Dict[str, Any]:
        """
        Start Vagrant VM.

        Args:
            hostname: The hostname to start
            provision: Whether to run provisioners

        Returns:
            Dictionary with operation result
        """
        try:
            command = "vagrant up"
            if not provision:
                command += " --no-provision"

            success, stdout, stderr, _ = await self._execute_ssh_command(
                hostname, command
            )

            if not success:
                logger.error("Error starting VM: %s", stderr)
                return {
                    "hostname": hostname,
                    "success": False,
                    "error": stderr or "Failed to start VM"
                }

            return {
                "hostname": hostname,
                "success": True,
                "message": "VM started successfully"
            }
        except Exception as e:
            logger.error("Error in up method: %s", str(e))
            return {
                "hostname": hostname,
                "success": False,
                "error": str(e)
            }

    async def halt(self, hostname: str) -> Dict[str, Any]:
        """
        Stop Vagrant VM.

        Args:
            hostname: The hostname to stop

        Returns:
            Dictionary with operation result
        """
        try:
            success, stdout, stderr, _ = await self._execute_ssh_command(
                hostname, "vagrant halt"
            )

            if not success:
                logger.error("Error stopping VM: %s", stderr)
                return {
                    "hostname": hostname,
                    "success": False,
                    "error": stderr or "Failed to stop VM"
                }

            return {
                "hostname": hostname,
                "success": True,
                "message": "VM stopped successfully"
            }
        except Exception as e:
            logger.error("Error in halt method: %s", str(e))
            return {
                "hostname": hostname,
                "success": False,
                "error": str(e)
            }

    async def destroy(self, hostname: str, force: bool = False) -> Dict[str, Any]:
        """
        Destroy Vagrant VM.

        Args:
            hostname: The hostname to destroy
            force: Whether to force destruction

        Returns:
            Dictionary with operation result
        """
        try:
            command = "vagrant destroy"
            if force:
                command += " -f"

            success, stdout, stderr, _ = await self._execute_ssh_command(
                hostname, command
            )

            if not success:
                logger.error("Error destroying VM: %s", stderr)
                return {
                    "hostname": hostname,
                    "success": False,
                    "error": stderr or "Failed to destroy VM"
                }

            return {
                "hostname": hostname,
                "success": True,
                "message": "VM destroyed successfully"
            }
        except Exception as e:
            logger.error("Error in destroy method: %s", str(e))
            return {
                "hostname": hostname,
                "success": False,
                "error": str(e)
            }

    async def execute_command(
            self, hostname: str, command: str, sudo: bool = False
        ) -> Dict[str, Any]:
        """
        Execute command on Vagrant VM.

        Args:
            hostname: The hostname to execute on
            command: The command to execute
            sudo: Whether to use sudo

        Returns:
            Dictionary with command result
        """
        try:
            vagrant_command = f"vagrant ssh -c '{command}'"
            if sudo:
                vagrant_command = f"vagrant ssh -c 'sudo {command}'"

            success, stdout, stderr, exit_code = await self._execute_ssh_command(
                hostname, vagrant_command
            )

            return {
                "hostname": hostname,
                "success": success,
                "stdout": stdout,
                "stderr": stderr,
                "exit_code": exit_code
            }
        except Exception as e:
            logger.error("Error in execute_command method: %s", str(e))
            return {
                "hostname": hostname,
                "success": False,
                "error": str(e),
                "exit_code": -1
            }

    async def suspend(self, hostname: str) -> Dict[str, Any]:
        """
        Suspend a Vagrant VM.

        Args:
            hostname: The hostname of the VM to suspend

        Returns:
            Dictionary with operation result
        """
        try:
            success, stdout, stderr, _ = await self._execute_ssh_command(
                hostname, "vagrant suspend"
            )

            if not success:
                logger.error("Error suspending VM: %s", stderr)
                return {
                    "hostname": hostname,
                    "success": False,
                    "error": stderr or "Failed to suspend VM"
                }

            return {
                "hostname": hostname,
                "success": True,
                "message": "VM suspended successfully"
            }
        except Exception as e:
            logger.error("Error in suspend method: %s", str(e))
            return {
                "hostname": hostname,
                "success": False,
                "error": str(e)
            }

class FathomSSHClient:
    """Client for connecting to Vagrant hosts via SSH."""

    def __init__(self):
        """Initialize the SSH client."""
        self.ssh_key_path = os.getenv("SSH_KEY_PATH", "~/.ssh/replit")
        self.ssh_user = os.getenv("DEFAULT_VAGRANT_SSH_USER", "vagrant")
        self.default_server = os.getenv("DEFAULT_VAGRANT_SERVER", "localhost")

        # Get SSH key content from the DEFAULT_VAGRANT_SSH_KEY secret
        self.ssh_key_content = os.getenv("DEFAULT_VAGRANT_SSH_KEY")
        self.temp_key_file = None

        if self.ssh_key_content:
            try:
                # Create temporary file for the key
                self.temp_key_file = tempfile.NamedTemporaryFile(delete=False, mode='w')

                # Write the ed25519 key with proper format
                key_content = self.ssh_key_content.strip()

                # If the key doesn't have the expected format, add it
                if not key_content.startswith('-----BEGIN'):
                    # Proper format for ED25519 keys with line breaks
                    formatted_key = "-----BEGIN OPENSSH PRIVATE KEY-----\n"

                    # Add the key content with line breaks every 70 chars
                    for i in range(0, len(key_content), 70):
                        formatted_key += key_content[i:i+70] + "\n"

                    # Add the footer
                    formatted_key += "-----END OPENSSH PRIVATE KEY-----"

                    key_content = formatted_key
                    logger.info("Reformatted SSH key with proper line breaks")

                self.temp_key_file.write(key_content)
                self.temp_key_file.close()

                # Set restrictive permissions - this is critical for SSH keys
                os.chmod(self.temp_key_file.name, 0o600)

                # Verify permissions were set correctly
                actual_perms = oct(os.stat(self.temp_key_file.name).st_mode & 0o777)
                if actual_perms != '0o600':
                    logger.warning(f"Key file permissions are {actual_perms}, expected 0o600")
                    # Try setting permissions again
                    os.chmod(self.temp_key_file.name, 0o600)
                    actual_perms = oct(os.stat(self.temp_key_file.name).st_mode & 0o777)
                    logger.info(f"After second attempt, key file permissions are {actual_perms}")

                self.ssh_key_path = self.temp_key_file.name
                logger.info("Successfully created temporary SSH key file from DEFAULT_VAGRANT_SSH_KEY at %s", self.temp_key_file.name)

                # Debug: Log file content for troubleshooting
                with open(self.temp_key_file.name, 'r') as f:
                    content = f.read()
                    logger.debug("Key file content length: %d characters", len(content))
                    logger.debug("Key file starts with: %s", content[:20] + "..." if len(content) > 20 else content)

                # Run file permission check via command line for more detailed info
                try:
                    result = subprocess.run(
                        ["ls", "-l", self.temp_key_file.name],
                        capture_output=True,
                        text=True,
                        check=False
                    )
                    logger.info(f"Key file details: {result.stdout.strip()}")
                except Exception as e:
                    logger.warning(f"Could not get key file details: {str(e)}")
            except Exception as e:
                logger.error(f"Failed to create temporary SSH key file: {str(e)}")
                # Fall back to default key path
        else:
            # Try to use the key from ~/.ssh/id_ed25519 if available and no key is provided
            if not self.ssh_key_content:
                key_path = os.path.expanduser("~/.ssh/id_ed25519")
                if os.path.exists(key_path):
                    try:
                        with open(key_path, 'r') as key_file:
                            self.ssh_key_content = key_file.read()
                        logger.info("Using SSH key from ~/.ssh/id_ed25519")
                    except Exception as e:
                        logger.error(f"Failed to read SSH key from ~/.ssh/id_ed25519: {str(e)}")

            # Expand user path (e.g., ~/.ssh/replit -> /home/<USER>/.ssh/replit)
            self.ssh_key_path = os.path.expanduser(self.ssh_key_path)

        logger.info("FathomSSHClient initialized with key path: %s", self.ssh_key_path)

    async def _execute_ssh_command(self, hostname: str, command: str) -> Tuple[bool, str, str, int]:
        """
        Execute an SSH command on the remote host.

        Args:
            hostname: The hostname to connect to.
            command: The command to execute.

        Returns:
            Tuple of (success, stdout, stderr, exit_code)
        """
        # Ensure the key file has proper permissions right before use
        if self.temp_key_file and os.path.exists(self.ssh_key_path):
            try:
                os.chmod(self.ssh_key_path, 0o600)
                logger.debug(f"Reset permissions on key file to 0o600 before SSH command")
            except Exception as e:
                logger.warning(f"Failed to reset permissions on key file: {str(e)}")

        ssh_command = [
            "ssh",
            "-i", self.ssh_key_path,
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "IdentitiesOnly=yes",  # Force use of specified key only
            "-o", "PreferredAuthentications=publickey",  # Only try public key auth
            "-o", "PasswordAuthentication=no",  # Disable password auth
            "-v",  # Verbose output for debugging
            f"{self.ssh_user}@{hostname}",
            command
        ]

        logger.info("Connecting with user %s to %s using key %s", 
                   self.ssh_user, hostname, self.ssh_key_path)

        logger.debug("Executing SSH command: %s", " ".join(ssh_command))

        try:
            process = await asyncio.create_subprocess_exec(
                *ssh_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            stdout_str = stdout.decode('utf-8').strip()
            stderr_str = stderr.decode('utf-8').strip()

            if process.returncode != 0:
                logger.warning(
                    "SSH command failed with exit code %d: %s", 
                    process.returncode, 
                    stderr_str
                )

            return (
                process.returncode == 0,
                stdout_str,
                stderr_str,
                process.returncode
            )
        except Exception as e:
            logger.error("Error executing SSH command: %s", str(e))
            return (False, "", str(e), -1)

    async def get_vm_status(self, hostname: str) -> Dict[str, Any]:
        """
        Get the status of Vagrant VMs on the host.

        Args:
            hostname: The hostname to connect to.

        Returns:
            Dict with VM status information.
        """
        success, stdout, stderr, exit_code = await self._execute_ssh_command(
            hostname, "vagrant status --machine-readable"
        )

        if not success:
            return {
                "hostname": hostname,
                "error": f"Failed to get VM status: {stderr}"
            }

        # Parse machine-readable output
        # Format: timestamp,target,type,data
        vms = {}
        for line in stdout.splitlines():
            parts = line.split(',')
            if len(parts) >= 4 and parts[2] == 'state':
                vm_name = parts[1]
                vm_state = parts[3]
                vms[vm_name] = vm_state

        return {
            "hostname": hostname,
            "success": True,
            "vms": vms
        }

    async def up(self, hostname: str, provision: bool = True) -> Dict[str, Any]:
        """
        Start a Vagrant VM.

        Args:
            hostname: The hostname to connect to.
            provision: Whether to provision the VM.

        Returns:
            Dict with command result.
        """
        command = "vagrant up"
        if not provision:
            command += " --no-provision"

        success, stdout, stderr, exit_code = await self._execute_ssh_command(
            hostname, command
        )

        return {
            "hostname": hostname,
            "success": success,
            "output": stdout,
            "error": stderr if not success else "",
            "exit_code": exit_code
        }

    async def halt(self, hostname: str, force: bool = False) -> Dict[str, Any]:
        """
        Stop a Vagrant VM.

        Args:
            hostname: The hostname to connect to.
            force: Whether to force the halt.

        Returns:
            Dict with command result.
        """
        command = "vagrant halt"
        if force:
            command += " --force"

        success, stdout, stderr, exit_code = await self._execute_ssh_command(
            hostname, command
        )

        return {
            "hostname": hostname,
            "success": success,
            "output": stdout,
            "error": stderr if not success else "",
            "exit_code": exit_code
        }

    async def destroy(self, hostname: str, force: bool = False) -> Dict[str, Any]:
        """
        Destroy a Vagrant VM.

        Args:
            hostname: The hostname to connect to.
            force: Whether to force the destruction.

        Returns:
            Dict with command result.
        """
        command = "vagrant destroy"
        if force:
            command += " --force"

        success, stdout, stderr, exit_code = await self._execute_ssh_command(
            hostname, command
        )

        return {
            "hostname": hostname,
            "success": success,
            "output": stdout,
            "error": stderr if not success else "",
            "exit_code": exit_code
        }

    async def execute_command(self, hostname: str, command: str, sudo: bool = False) -> Dict[str, Any]:
        """
        Execute a command on a Vagrant VM.

        Args:
            hostname: The hostname to connect to.
            command: The command to execute.
            sudo: Whether to execute the command with sudo.

        Returns:
            Dict with command execution result.
        """
        vagrant_command = f"vagrant ssh -c '{command if not sudo else f'sudo {command}'}'"

        success, stdout, stderr, exit_code = await self._execute_ssh_command(
            hostname, vagrant_command
        )

        return {
            "hostname": hostname,
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
            "exit_code": exit_code
        }

    async def get_remote_status(self, hostname: str) -> Dict[str, Any]:
        """
        Get detailed remote status information from a Vagrant VM.

        Args:
            hostname: The hostname to connect to.

        Returns:
            Dict with system metrics including uptime, memory, disk usage.
        """
        # Command to get various system metrics
        commands = [
            "uptime",
            "free -m",
            "df -h | grep -v tmpfs",
            "vmstat 1 1",
            "cat /proc/loadavg"
        ]

        combined_command = " && echo '---SEPARATOR---' && ".join(commands)
        vagrant_command = f"vagrant ssh -c '{combined_command}'"

        success, stdout, stderr, exit_code = await self._execute_ssh_command(
            hostname, vagrant_command
        )

        if not success:
            return {
                "hostname": hostname,
                "error": f"Failed to get remote status: {stderr}"
            }

        # Split by separator
        outputs = stdout.split('---SEPARATOR---')

        # Parse the outputs
        metrics = {
            "uptime": outputs[0].strip() if len(outputs) > 0 else "",
            "memory": outputs[1].strip() if len(outputs) > 1 else "",
            "disk": outputs[2].strip() if len(outputs) > 2 else "",
            "vmstat": outputs[3].strip() if len(outputs) > 3 else "",
            "load": outputs[4].strip() if len(outputs) > 4 else ""
        }

        return {
            "hostname": hostname,
            "success": True,
            "metrics": metrics
        }

    def __del__(self):
        """Cleanup temporary files on destruction."""
        if self.temp_key_file and os.path.exists(self.temp_key_file.name):
            try:
                os.unlink(self.temp_key_file.name)
            except Exception as e:
                logger.warning("Failed to delete temporary key file: %s", str(e))