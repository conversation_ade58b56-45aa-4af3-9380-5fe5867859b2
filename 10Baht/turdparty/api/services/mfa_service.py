"""
Service for managing multi-factor authentication.
"""
import os
import logging
import pyotp
import qrcode
from io import BytesIO
import base64
from typing import Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session

from api.db.session import get_db
from api.models.user import User

logger = logging.getLogger(__name__)

# Application name for TOTP
APP_NAME = os.getenv("APP_NAME", "FastAPI App")

class MFAService:
    """Service for managing multi-factor authentication."""

    def __init__(self, db: Optional[Session] = None):
        """
        Initialize MFA service.

        Args:
            db: Database session (optional)
        """
        self.db = db or next(get_db())
        logger.info("MFAService initialized")

    def generate_secret(self, user_id: int) -> Dict[str, Any]:
        """
        Generate a new MFA secret for a user.

        Args:
            user_id: User ID

        Returns:
            Dictionary with secret and QR code
        """
        try:
            # Get user
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"User not found: {user_id}")
                return {
                    "success": False,
                    "error": "User not found"
                }

            # Generate new secret
            secret = pyotp.random_base32()

            # Store secret in user record
            user.mfa_secret = secret
            user.mfa_enabled = False
            self.db.commit()

            # Generate QR code
            qr_code = self._generate_qr_code(user.username, secret)

            return {
                "success": True,
                "secret": secret,
                "qr_code": qr_code
            }
        except Exception as e:
            logger.error(f"Error generating MFA secret: {str(e)}")
            self.db.rollback()
            return {
                "success": False,
                "error": str(e)
            }

    def verify_token(self, user_id: int, token: str) -> Dict[str, Any]:
        """
        Verify a MFA token.

        Args:
            user_id: User ID
            token: MFA token

        Returns:
            Dictionary with verification result
        """
        try:
            # Get user
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"User not found: {user_id}")
                return {
                    "success": False,
                    "error": "User not found"
                }

            # Check if MFA is set up
            if not user.mfa_secret:
                logger.error(f"MFA not set up for user: {user_id}")
                return {
                    "success": False,
                    "error": "MFA not set up"
                }

            # Verify token
            totp = pyotp.TOTP(user.mfa_secret)
            if totp.verify(token):
                # If this is the first successful verification, enable MFA
                if not user.mfa_enabled:
                    user.mfa_enabled = True
                    self.db.commit()
                    return {
                        "success": True,
                        "message": "MFA verification successful"
                    }
                else:
                    return {
                        "success": True,
                        "message": "Token verified successfully"
                    }
            else:
                return {
                    "success": False,
                    "error": "Invalid token"
                }
        except Exception as e:
            logger.error(f"Error verifying MFA token: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def is_mfa_required(self, user_id: int) -> bool:
        """
        Check if MFA is required for a user.

        Args:
            user_id: User ID

        Returns:
            True if MFA is enabled for the user, False otherwise
        """
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            return user.mfa_enabled
        except Exception as e:
            logger.error(f"Error checking MFA requirement: {str(e)}")
            return False

    def validate_mfa_session(self, user_id: int, validated_at: Optional[float] = None) -> bool:
        """
        Validate if the user has a valid MFA session.

        Args:
            user_id: User ID
            validated_at: Timestamp when MFA was validated

        Returns:
            True if MFA session is valid, False otherwise
        """
        if not validated_at:
            return False

        # MFA sessions valid for 12 hours
        import time
        current_time = time.time()
        mfa_session_duration = 12 * 60 * 60  # 12 hours in seconds

        return (current_time - validated_at) < mfa_session_duration

    def disable_mfa(self, user_id: int) -> Dict[str, Any]:
        """
        Disable MFA for a user.

        Args:
            user_id: User ID

        Returns:
            Dictionary with operation result
        """
        try:
            # Get user
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"User not found: {user_id}")
                return {
                    "success": False,
                    "error": "User not found"
                }

            # Disable MFA
            user.mfa_secret = None
            user.mfa_enabled = False
            self.db.commit()

            return {
                "success": True,
                "message": "MFA disabled successfully"
            }
        except Exception as e:
            logger.error(f"Error disabling MFA: {str(e)}")
            self.db.rollback()
            return {
                "success": False,
                "error": str(e)
            }

    def _generate_qr_code(self, username: str, secret: str) -> str:
        """
        Generate a QR code for MFA setup.

        Args:
            username: Username
            secret: MFA secret

        Returns:
            Base64-encoded QR code image
        """
        try:
            # Create OTP URI
            issuer_name = os.getenv("MFA_ISSUER_NAME", "MyApp")
            totp = pyotp.TOTP(secret)
            uri = totp.provisioning_uri(username, issuer_name=issuer_name)

            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(uri)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64
            buffered = BytesIO()
            img.save(buffered)
            img_str = base64.b64encode(buffered.getvalue()).decode()

            return f"data:image/png;base64,{img_str}"
        except Exception as e:
            logger.error(f"Error generating QR code: {str(e)}")
            return ""