from datetime import datetime
from typing import List, Optional
from uuid import uuid4

from api.models.success_criteria import (
    SuccessCriteria,
    SuccessCriteriaCreate,
    SuccessCriteriaUpdate,
    SuccessCriteriaFilters,
    SuccessCriteriaDetail,
    SuccessCriteriaResponse,
)

class SuccessCriteriaService:
    """Service for managing success criteria."""

    def __init__(self):
        """Initialize the success criteria service."""
        # In-memory storage for development
        self._criteria: List[SuccessCriteria] = []

    async def get_success_criteria(self, filters: SuccessCriteriaFilters) -> SuccessCriteriaResponse:
        """
        Get success criteria with optional filtering.

        Args:
            filters: Filter parameters for success criteria

        Returns:
            SuccessCriteriaResponse containing filtered criteria and pagination info

        Raises:
            ValueError: If validation fails
        """
        filtered_criteria = self._criteria

        # Apply filters
        if filters.status:
            filtered_criteria = [c for c in filtered_criteria if c.status == filters.status]
        if filters.category:
            filtered_criteria = [c for c in filtered_criteria if c.category == filters.category]

        # Calculate pagination
        start_idx = (filters.page - 1) * filters.page_size
        end_idx = start_idx + filters.page_size
        paginated_criteria = filtered_criteria[start_idx:end_idx]

        return SuccessCriteriaResponse(
            data=paginated_criteria,
            total=len(filtered_criteria),
            page=filters.page,
            page_size=filters.page_size,
        )

    async def get_success_criteria_by_id(self, criterion_id: str) -> Optional[SuccessCriteria]:
        """
        Get a success criterion by ID.

        Args:
            criterion_id: ID of the success criterion

        Returns:
            SuccessCriteria if found, None otherwise
        """
        return next((c for c in self._criteria if c.id == criterion_id), None)

    async def create_success_criteria(self, criterion: SuccessCriteriaCreate) -> SuccessCriteria:
        """
        Create a new success criterion.

        Args:
            criterion: Success criterion data to create

        Returns:
            Created SuccessCriteria

        Raises:
            ValueError: If validation fails
        """
        new_criterion = SuccessCriteria(
            id=str(uuid4()),
            name=criterion.name,
            description=criterion.description,
            status="pending",
            category=criterion.category,
            details=[],
        )
        self._criteria.append(new_criterion)
        return new_criterion

    async def update_success_criteria(
        self, criterion_id: str, update: SuccessCriteriaUpdate
    ) -> Optional[SuccessCriteria]:
        """
        Update an existing success criterion.

        Args:
            criterion_id: ID of the success criterion to update
            update: Update data for the success criterion

        Returns:
            Updated SuccessCriteria if found, None otherwise

        Raises:
            ValueError: If validation fails
        """
        criterion = await self.get_success_criteria_by_id(criterion_id)
        if not criterion:
            return None

        # Update fields if provided
        if update.name is not None:
            criterion.name = update.name
        if update.description is not None:
            criterion.description = update.description
        if update.status is not None:
            criterion.status = update.status
        if update.category is not None:
            criterion.category = update.category

        criterion.updated_at = datetime.utcnow()
        return criterion

    async def update_success_criteria_status(
        self, criterion_id: str, status: str
    ) -> Optional[SuccessCriteria]:
        """
        Update the status of a success criterion.

        Args:
            criterion_id: ID of the success criterion
            status: New status for the success criterion

        Returns:
            Updated SuccessCriteria if found, None otherwise

        Raises:
            ValueError: If validation fails
        """
        criterion = await self.get_success_criteria_by_id(criterion_id)
        if not criterion:
            return None

        criterion.status = status
        criterion.updated_at = datetime.utcnow()
        return criterion

    async def add_success_criteria_detail(
        self, criterion_id: str, detail_content: str
    ) -> Optional[SuccessCriteria]:
        """
        Add a new detail to a success criterion.

        Args:
            criterion_id: ID of the success criterion
            detail_content: Content of the new detail

        Returns:
            Updated SuccessCriteria if found, None otherwise
        """
        criterion = await self.get_success_criteria_by_id(criterion_id)
        if not criterion:
            return None

        new_detail = SuccessCriteriaDetail(
            id=str(uuid4()),
            content=detail_content,
        )
        criterion.details.append(new_detail)
        criterion.updated_at = datetime.utcnow()
        return criterion

    async def delete_success_criteria(self, criterion_id: str) -> bool:
        """
        Delete a success criterion.

        Args:
            criterion_id: ID of the success criterion to delete

        Returns:
            True if deleted, False if not found
        """
        criterion = await self.get_success_criteria_by_id(criterion_id)
        if not criterion:
            return False

        self._criteria.remove(criterion)
        return True 