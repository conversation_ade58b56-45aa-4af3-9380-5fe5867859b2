"""
Service for file upload feature
"""
from sqlalchemy.orm import Session
from sqlalchemy import func
from api.models.file_upload import FileUploadCreateSchema, FileUploadUpdateSchema, FileUploadListSchema, FileUploadSchema
from api.db.models import FileUpload
import uuid
import os
import hashlib
import shutil
import mimetypes
from typing import List, Optional, Tuple
from fastapi import UploadFile, HTTPException, status
from datetime import datetime
import tempfile
import logging
from api.services.minio_ssh_client import MinIOSSHClient

# Set up logging
logger = logging.getLogger(__name__)

class FileUploadService:
    """Service for file upload operations"""
    
    UPLOAD_DIR = "/tmp/file-uploads"
    MINIO_BUCKET = "file-uploads"
    
    def __init__(self, db: Session):
        self.db = db
        # Ensure upload directory exists (for temporary storage)
        os.makedirs(self.UPLOAD_DIR, exist_ok=True)
        
    async def _get_minio_client(self) -> MinIOSSHClient:
        """Get a configured MinIO client with an active SSH tunnel"""
        # Get hostname from environment or use default
        hostname = os.getenv("MINIO_SSH_HOST", "localhost")
        
        # Create and configure MinIO client
        minio_client = MinIOSSHClient()
        
        # Log the MinIO endpoint for debugging
        if minio_client.use_direct_connection:
            logger.info(f"Using direct MinIO connection at http://{minio_client.minio_host}:{minio_client.minio_port}")
        else:
            logger.info(f"Using tunneled MinIO connection via SSH to {hostname}")
            
        tunnel_success = await minio_client.start_ssh_tunnel(hostname)
        
        if not tunnel_success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to establish connection to MinIO storage"
            )
        
        # Ensure the bucket exists
        await self._ensure_bucket_exists(minio_client)
        
        return minio_client
    
    async def _ensure_bucket_exists(self, minio_client: MinIOSSHClient):
        """Ensure that the required bucket exists"""
        buckets_response = await minio_client.list_buckets()
        
        if not buckets_response.get('success', False):
            logger.error(f"Failed to list buckets: {buckets_response.get('error')}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to access MinIO storage"
            )
        
        # Check if our bucket exists
        bucket_exists = any(bucket['name'] == self.MINIO_BUCKET for bucket in buckets_response.get('buckets', []))
        
        # Create the bucket if it doesn't exist
        if not bucket_exists:
            create_response = await minio_client.create_bucket(self.MINIO_BUCKET)
            if not create_response.get('success', False):
                logger.error(f"Failed to create bucket: {create_response.get('error')}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to initialize storage bucket"
                )
    
    def get_all(self, skip: int = 0, limit: int = 100, user_id: Optional[uuid.UUID] = None) -> Tuple[List[FileUpload], int]:
        """Get all file uploads with optional filtering by user"""
        query = self.db.query(FileUpload)
        
        if user_id:
            query = query.filter(FileUpload.owner_id == user_id)
            
        total = query.count()
        uploads = query.offset(skip).limit(limit).all()
        
        return uploads, total
    
    def get_by_id(self, file_upload_id: uuid.UUID, user_id: Optional[uuid.UUID] = None) -> Optional[FileUpload]:
        """Get file upload by ID with optional user check"""
        query = self.db.query(FileUpload).filter(FileUpload.id == file_upload_id)
        
        if user_id:
            query = query.filter(FileUpload.owner_id == user_id)
            
        return query.first()
    
    async def create_from_upload(self, upload_file: UploadFile, description: str, user_id: uuid.UUID) -> FileUpload:
        """Create a new file upload from an uploaded file and store it in MinIO"""
        # Generate a unique object key for MinIO
        file_id = uuid.uuid4()
        object_key = f"{file_id}/{upload_file.filename}"
        
        # Special case for test token
        if user_id == uuid.UUID("e3c704f3-c398-4894-bd7b-a1d092dada04") or str(user_id) == "e3c704f3-c398-4894-bd7b-a1d092dada04":
            # Use the first test user ID we created
            user_id = uuid.UUID("e3c704f3-c398-4894-bd7b-a1d092dada04")
        
        # Create a temporary file to store the upload
        with tempfile.NamedTemporaryFile(delete=False, dir=self.UPLOAD_DIR) as temp_file:
            temp_file_path = temp_file.name
            
            try:
                # Save uploaded file to temp file
                with open(temp_file_path, 'wb') as out_file:
                    # Use chunked reading for potentially large files
                    while chunk := await upload_file.read(1024 * 1024):  # 1MB chunks
                        out_file.write(chunk)
                
                # Get file size
                file_size = os.path.getsize(temp_file_path)
                
                # Calculate hash from saved file
                file_hash = self._calculate_file_hash(temp_file_path)
                
                # Determine content type
                content_type = upload_file.content_type
                if not content_type:
                    content_type = mimetypes.guess_type(upload_file.filename)[0] or "application/octet-stream"
                
                # Upload to MinIO
                minio_client = await self._get_minio_client()
                try:
                    upload_response = await minio_client.upload_file(
                        bucket_name=self.MINIO_BUCKET,
                        object_key=object_key,
                        file_path=temp_file_path
                    )
                    
                    if not upload_response.get('success', False):
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail=f"Failed to upload file to storage: {upload_response.get('error')}"
                        )
                    
                    # Create database record
                    db_file_upload = FileUpload(
                        id=file_id,
                        filename=upload_file.filename,
                        file_size=file_size,
                        content_type=content_type,
                        file_hash=file_hash,
                        file_path=object_key,  # Store the MinIO object key
                        description=description,
                        download_url=f"/api/v1/file_upload/download/{file_id}",
                        owner_id=user_id,
                        is_active=True
                    )
                    
                    self.db.add(db_file_upload)
                    self.db.commit()
                    self.db.refresh(db_file_upload)
                    return db_file_upload
                    
                finally:
                    # Close the MinIO connection
                    await minio_client.stop_ssh_tunnel()
                    
            except Exception as e:
                logger.error(f"Error in file upload: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error uploading file: {str(e)}"
                )
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of a file using chunked reading"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            # Read and update hash in chunks of 4K
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    
    async def create_from_upload_with_path(self, upload_file: UploadFile, description: str, 
                                          file_path: str, folder_id: str, user_id: uuid.UUID) -> FileUpload:
        """Create a new file upload with path information for folder uploads"""
        # Generate a unique object key for MinIO that preserves the path structure
        file_id = uuid.uuid4()
        object_key = f"{folder_id}/{file_path}/{upload_file.filename}"
        
        # Special case for test token
        if user_id == uuid.UUID("e3c704f3-c398-4894-bd7b-a1d092dada04") or str(user_id) == "e3c704f3-c398-4894-bd7b-a1d092dada04":
            # Use the first test user ID we created
            user_id = uuid.UUID("e3c704f3-c398-4894-bd7b-a1d092dada04")
        
        # Create a temporary file to store the upload
        with tempfile.NamedTemporaryFile(delete=False, dir=self.UPLOAD_DIR) as temp_file:
            temp_file_path = temp_file.name
            
            try:
                # Save uploaded file to temp file
                with open(temp_file_path, 'wb') as out_file:
                    # Use chunked reading for potentially large files
                    while chunk := await upload_file.read(1024 * 1024):  # 1MB chunks
                        out_file.write(chunk)
                
                # Get file size
                file_size = os.path.getsize(temp_file_path)
                
                # Calculate hash from saved file
                file_hash = self._calculate_file_hash(temp_file_path)
                
                # Determine content type
                content_type = upload_file.content_type
                if not content_type:
                    content_type = mimetypes.guess_type(upload_file.filename)[0] or "application/octet-stream"
                
                # Upload to MinIO
                minio_client = await self._get_minio_client()
                try:
                    upload_response = await minio_client.upload_file(
                        bucket_name=self.MINIO_BUCKET,
                        object_key=object_key,
                        file_path=temp_file_path
                    )
                    
                    if not upload_response.get('success', False):
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail=f"Failed to upload file to storage: {upload_response.get('error')}"
                        )
                    
                    # Create database record
                    db_file_upload = FileUpload(
                        id=file_id,
                        filename=upload_file.filename,
                        file_size=file_size,
                        content_type=content_type,
                        file_hash=file_hash,
                        file_path=object_key,  # Store the MinIO object key
                        file_folder_path=file_path,  # Store the original path
                        folder_id=folder_id,
                        description=description,
                        download_url=f"/api/v1/file_upload/download/{file_id}",
                        owner_id=user_id,
                        is_active=True
                    )
                    
                    self.db.add(db_file_upload)
                    self.db.commit()
                    self.db.refresh(db_file_upload)
                    return db_file_upload
                    
                finally:
                    # Close the MinIO connection
                    await minio_client.stop_ssh_tunnel()
                    
            except Exception as e:
                logger.error(f"Error in file upload: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error uploading file: {str(e)}"
                )
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
    
    async def update(self, file_upload_id: uuid.UUID, file_upload_data: FileUploadUpdateSchema, user_id: Optional[uuid.UUID] = None) -> FileUpload:
        """Update a file upload with optional user check"""
        db_file_upload = self.get_by_id(file_upload_id, user_id)
        
        if not db_file_upload:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File upload with id {file_upload_id} not found"
            )
        
        # Update only provided fields
        for key, value in file_upload_data.dict(exclude_unset=True).items():
            setattr(db_file_upload, key, value)
        
        db_file_upload.modified_on = datetime.utcnow()
        self.db.commit()
        self.db.refresh(db_file_upload)
        return db_file_upload
    
    async def delete(self, file_upload_id: uuid.UUID, user_id: Optional[uuid.UUID] = None) -> None:
        """Delete a file upload with optional user check"""
        db_file_upload = self.get_by_id(file_upload_id, user_id)
        
        if not db_file_upload:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File upload with id {file_upload_id} not found"
            )
        
        # Delete the file from MinIO
        try:
            minio_client = await self._get_minio_client()
            try:
                # Extract object key from file_path
                object_key = db_file_upload.file_path
                
                delete_response = await minio_client.delete_object(
                    bucket_name=self.MINIO_BUCKET,
                    object_key=object_key
                )
                
                if not delete_response.get('success', False):
                    logger.warning(f"Failed to delete file from storage: {delete_response.get('error')}")
            finally:
                await minio_client.stop_ssh_tunnel()
        except Exception as e:
            # Log the error but continue with database deletion
            logger.error(f"Error deleting file from storage: {str(e)}")
        
        # Delete database record
        self.db.delete(db_file_upload)
        self.db.commit()
    
    async def get_file_path(self, file_upload_id: uuid.UUID, user_id: Optional[uuid.UUID] = None) -> str:
        """Get the file path for a file upload and download it from MinIO to a temporary location"""
        db_file_upload = self.get_by_id(file_upload_id, user_id)
        
        if not db_file_upload:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File upload with id {file_upload_id} not found"
            )
        
        # Create a temporary file to store the download
        temp_file = tempfile.NamedTemporaryFile(delete=False, dir=self.UPLOAD_DIR)
        temp_file_path = temp_file.name
        temp_file.close()
        
        try:
            # Download the file from MinIO
            minio_client = await self._get_minio_client()
            try:
                # Extract object key from file_path
                object_key = db_file_upload.file_path
                
                download_response = await minio_client.download_file(
                    bucket_name=self.MINIO_BUCKET,
                    object_key=object_key,
                    file_path=temp_file_path
                )
                
                if not download_response.get('success', False):
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"Failed to download file from storage: {download_response.get('error')}"
                    )
                
                return temp_file_path
            finally:
                await minio_client.stop_ssh_tunnel()
        except Exception as e:
            # Clean up temporary file on error
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            
            logger.error(f"Error downloading file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error downloading file: {str(e)}"
            )
