"""
Static analysis service for 3rd party integrations.
"""
import logging
import hashlib
import requests
from typing import Dict, Any, Optional, List, Union
from sqlalchemy.orm import Session
import os

from api.core.config import settings
from api.services.virustotal_service import VirusTotalService
from api.db.models.hash_report import HashReport

logger = logging.getLogger(__name__)

class StaticAnalysisService:
    """Service for static analysis of files."""

    def __init__(self, api_key=None):
        """Initialize static analysis service.

        Args:
            api_key (str, optional): VirusTotal API key. Defaults to None.
        """
        self.api_key = api_key or getattr(settings, "VIRUSTOTAL_API_KEY", None)
        self.virustotal_api_key = self.api_key  # Alias for compatibility
        self.api_url = "https://www.virustotal.com/api/v3/files"

    def _get_file_hash(self, file_content, hash_type="sha256"):
        """Calculate file hash.

        Args:
            file_content (bytes): File content to hash.
            hash_type (str, optional): Hash type to use. Defaults to "sha256".

        Returns:
            str: Calculated hash.

        Raises:
            ValueError: If hash type is not supported or hash calculation fails.
        """
        if not isinstance(file_content, bytes):
            try:
                file_content = file_content.encode('utf-8')
            except (AttributeError, UnicodeEncodeError) as e:
                raise ValueError(f"Invalid file content: {str(e)}")

        try:
            hasher = None
            if hash_type == "md5":
                hasher = hashlib.md5()
            elif hash_type == "sha1":
                hasher = hashlib.sha1()
            else:
                # Default to SHA256 for unsupported hash types
                hasher = hashlib.sha256()

            hasher.update(file_content)
            return hasher.hexdigest()
        except Exception as e:
            raise ValueError(f"Failed to calculate file hash: {str(e)}")

    def lookup_file_hash_virustotal(self, file_hash):
        """Look up file hash on VirusTotal.

        Args:
            file_hash (str): File hash to look up.

        Returns:
            dict: VirusTotal lookup result.

        Raises:
            ValueError: If API key is not set or lookup fails.
        """
        if not self.api_key:
            raise ValueError("VirusTotal API key required")

        try:
            headers = {"x-apikey": self.api_key}
            response = requests.get(
                f"{self.api_url}/{file_hash}",
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result
            elif response.status_code == 404:
                return {
                    "error": "Hash not found",
                    "data": None
                }
            elif response.status_code == 403:
                raise ValueError("Invalid API key")
            else:
                raise ValueError(f"VirusTotal API error: {response.status_code}")

        except requests.exceptions.Timeout:
            raise ValueError("VirusTotal API request timed out")
        except requests.exceptions.ConnectionError:
            raise ValueError("VirusTotal API connection error")
        except Exception as e:
            raise ValueError(f"Error looking up hash: {str(e)}")

    def analyze_file(self, file_content):
        """Analyze file content.

        Args:
            file_content (bytes): File content to analyze.

        Returns:
            dict: Analysis result.
        """
        try:
            # Calculate hashes
            hashes = {
                "md5": self._get_file_hash(file_content, "md5"),
                "sha1": self._get_file_hash(file_content, "sha1"),
                "sha256": self._get_file_hash(file_content, "sha256")
            }

            # Look up SHA256 hash on VirusTotal
            try:
                vt_result = self.lookup_file_hash_virustotal(hashes["sha256"])
                findings = []
                
                # Extract findings from VirusTotal results if available
                if vt_result.get("data", {}).get("attributes", {}).get("last_analysis_stats"):
                    stats = vt_result["data"]["attributes"]["last_analysis_stats"]
                    if stats.get("malicious", 0) > 0:
                        findings.append({
                            "type": "malware",
                            "severity": "high",
                            "message": f"File detected as malicious by {stats['malicious']} scanners",
                            "line": None
                        })
                    elif stats.get("suspicious", 0) > 0:
                        findings.append({
                            "type": "suspicious",
                            "severity": "medium",
                            "message": f"File flagged as suspicious by {stats['suspicious']} scanners",
                            "line": None
                        })
                
                return {
                    "success": True,
                    "findings": findings,
                    "hashes": hashes,
                    "virustotal": vt_result,
                    "error": None
                }
            except ValueError as e:
                return {
                    "success": False,
                    "findings": [],
                    "hashes": hashes,
                    "virustotal": {
                        "data": None,
                        "error": str(e)
                    },
                    "error": str(e)
                }

        except Exception as e:
            return {
                "success": False,
                "findings": [],
                "error": str(e),
                "hashes": None,
                "virustotal": None
            }

    def get_services_status(self):
        """Get status of static analysis services.

        Returns:
            dict: Services status.
        """
        try:
            # Test VirusTotal API with a known hash
            test_hash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"  # Empty file SHA256
            try:
                self.lookup_file_hash_virustotal(test_hash)
                virustotal_status = {
                    "available": True,
                    "error": None
                }
            except ValueError as e:
                virustotal_status = {
                    "available": False,
                    "error": str(e)
                }

            return {
                "success": True,
                "virustotal": virustotal_status
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _get_or_create_hash_report(self, hash_value: str, hash_type: str, source: str) -> Dict[str, Any]:
        """
        Get a hash report from the database or create a new one.

        Args:
            hash_value: Hash value to look up
            hash_type: Type of hash (md5, sha1, sha256)
            source: Source of the report (virustotal, etc.)

        Returns:
            Report data
        """
        # Get a fresh report from the API
        logger.info(f"Fetching new {source} report for hash {hash_value}")
        if source == "virustotal":
            report_data = self.lookup_file_hash_virustotal(hash_value)

            return report_data
        else:
            raise ValueError(f"Unsupported report source: {source}")

    def _determine_hash_type(self, hash_value: str) -> str:
        """
        Determine the type of hash based on its length.

        Args:
            hash_value: Hash value to check

        Returns:
            Hash type (md5, sha1, sha256)
        """
        hash_length = len(hash_value)

        if hash_length == 32:
            return "md5"
        elif hash_length == 40:
            return "sha1"
        elif hash_length == 64:
            return "sha256"
        else:
            logger.warning(f"Unknown hash type for length {hash_length}")
            return "unknown"

    def lookup_hash(self, hash_value: str) -> Dict[str, Any]:
        """
        Look up information about a file hash.

        Args:
            hash_value: Hash value to look up

        Returns:
            Dictionary containing information about the hash
        """
        try:
            # Determine hash type based on length
            hash_type = self._determine_hash_type(hash_value)

            # First check if we have a stored report
            report = self._get_or_create_hash_report(hash_value, hash_type, "virustotal")

            return {
                "hash": hash_value,
                "hash_type": hash_type,
                "virustotal": report
            }
        except Exception as e:
            logger.error(f"Error looking up hash: {str(e)}")
            raise ValueError(f"Failed to look up hash: {str(e)}")