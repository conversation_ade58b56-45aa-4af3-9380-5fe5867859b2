"""
File validation service for security checks
"""
import os
import re
import magic
from typing import List, Optional, Tuple
from fastapi import UploadFile, HTTPException, status
import logging

# Set up logging
logger = logging.getLogger(__name__)

class FileValidationService:
    """Service for file validation and security checks"""

    # Define allowed MIME types
    ALLOWED_MIME_TYPES = {
        # Documents
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'text/markdown',
        
        # Images
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/svg+xml',  # SVG files will be sanitized
        'image/webp',
        'image/tiff',
        
        # Archives
        'application/zip',
        'application/x-tar',
        'application/x-gzip',
        'application/x-bzip2',
        
        # Other
        'application/json',
        'application/xml',
        'text/xml'
    }

    # Maximum file size (10MB)
    MAX_FILE_SIZE = 10 * 1024 * 1024

    # Dangerous file extensions
    DANGEROUS_EXTENSIONS = {
        '.exe', '.dll', '.so', '.dylib',  # Executables
        '.js', '.php', '.py', '.rb', '.pl',  # Scripts
        '.sh', '.bash', '.zsh', '.fish',  # Shell scripts
        '.bat', '.cmd', '.ps1',  # Windows scripts
        '.jar', '.war',  # Java archives
        '.msi', '.app',  # Installers
        '.apk', '.ipa',  # Mobile apps
    }

    @classmethod
    def sanitize_filename(cls, filename: str) -> str:
        """
        Sanitize filename to prevent path traversal and other attacks.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        # Remove path components
        filename = os.path.basename(filename)
        
        # Remove potentially dangerous characters
        filename = re.sub(r'[^\w\-\. ]', '_', filename)
        
        # Ensure the filename isn't empty
        if not filename:
            filename = 'unnamed_file'
            
        return filename

    @classmethod
    def validate_file_size(cls, file_size: int) -> None:
        """
        Validate file size is within limits.
        
        Args:
            file_size: Size of the file in bytes
            
        Raises:
            HTTPException: If file size exceeds limit
        """
        if file_size > cls.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {cls.MAX_FILE_SIZE / 1024 / 1024}MB"
            )

    @classmethod
    def validate_mime_type(cls, file_content: bytes, filename: str) -> str:
        """
        Validate MIME type using python-magic.
        
        Args:
            file_content: Content of the file
            filename: Name of the file
            
        Returns:
            Actual MIME type of the file
            
        Raises:
            HTTPException: If file type is not allowed
        """
        # Detect actual MIME type
        mime = magic.Magic(mime=True)
        detected_mime_type = mime.from_buffer(file_content)
        
        # Check if MIME type is allowed
        if detected_mime_type not in cls.ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                detail=f"File type {detected_mime_type} is not allowed"
            )
            
        # Check file extension
        _, ext = os.path.splitext(filename.lower())
        if ext in cls.DANGEROUS_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                detail=f"File extension {ext} is not allowed"
            )
            
        return detected_mime_type

    @classmethod
    def sanitize_svg(cls, content: bytes) -> bytes:
        """
        Sanitize SVG content to prevent XSS attacks.
        
        Args:
            content: Original SVG content
            
        Returns:
            Sanitized SVG content
        """
        # Convert to string for processing
        svg_str = content.decode('utf-8', errors='ignore')
        
        # Remove script tags and event handlers
        svg_str = re.sub(r'<script.*?</script>', '', svg_str, flags=re.DOTALL | re.IGNORECASE)
        svg_str = re.sub(r'\bon\w+=".*?"', '', svg_str, flags=re.IGNORECASE)
        svg_str = re.sub(r'\bon\w+=\'.*?\'', '', svg_str, flags=re.IGNORECASE)
        
        # Remove external references
        svg_str = re.sub(r'xlink:href="(?!#).*?"', '', svg_str, flags=re.IGNORECASE)
        svg_str = re.sub(r'href="(?!#).*?"', '', svg_str, flags=re.IGNORECASE)
        
        # Remove data URIs
        svg_str = re.sub(r'data:.*?[,;]', '', svg_str, flags=re.IGNORECASE)
        
        return svg_str.encode('utf-8')

    @classmethod
    async def validate_file(cls, file: UploadFile) -> Tuple[str, bytes, str]:
        """
        Validate and sanitize an uploaded file.
        
        Args:
            file: FastAPI UploadFile object
            
        Returns:
            Tuple of (sanitized filename, sanitized content, actual mime type)
            
        Raises:
            HTTPException: If file validation fails
        """
        try:
            # Read file content
            content = await file.read()
            
            # Validate file size
            cls.validate_file_size(len(content))
            
            # Sanitize filename
            sanitized_filename = cls.sanitize_filename(file.filename)
            
            # Validate MIME type
            actual_mime_type = cls.validate_mime_type(content, sanitized_filename)
            
            # Special handling for SVG files
            if actual_mime_type == 'image/svg+xml':
                content = cls.sanitize_svg(content)
            
            return sanitized_filename, content, actual_mime_type
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error validating file: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error validating file: {str(e)}"
            ) 