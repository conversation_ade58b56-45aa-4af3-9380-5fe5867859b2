
"""
Service for generating and analyzing test coverage reports.
"""
import os
import json
import logging
import subprocess
from typing import Dict, Any, List, Optional, Tuple

logger = logging.getLogger(__name__)

class CoverageService:
    """Service for generating and analyzing test coverage reports."""
    
    def __init__(self):
        """Initialize the coverage service."""
        self.coverage_dir = "htmlcov"
        self.coverage_json = "coverage.json"
        logger.info("CoverageService initialized")
    
    def generate_coverage_report(self) -> Dict[str, Any]:
        """
        Generate a test coverage report.
        
        Returns:
            Dictionary with generation status and summary
        """
        try:
            logger.info("Generating coverage report")
            
            # Run pytest with coverage
            process = subprocess.run(
                ["python", "-m", "pytest", "--cov=api", "--cov=ui", "--cov-report=json", "--cov-report=html"],
                capture_output=True,
                text=True,
                check=False
            )
            
            if process.returncode != 0 and not os.path.exists(self.coverage_json):
                logger.error(f"Error generating coverage report: {process.stderr}")
                return {
                    "success": False,
                    "error": process.stderr
                }
            
            # Load coverage data
            coverage_data = self._load_coverage_data()
            if not coverage_data:
                return {
                    "success": False,
                    "error": "Failed to load coverage data"
                }
            
            return {
                "success": True,
                "summary": self._generate_summary(coverage_data),
                "timestamp": coverage_data.get("meta", {}).get("timestamp")
            }
        except Exception as e:
            logger.error(f"Error generating coverage report: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _load_coverage_data(self) -> Dict[str, Any]:
        """
        Load coverage data from JSON file.
        
        Returns:
            Coverage data as dictionary
        """
        try:
            if not os.path.exists(self.coverage_json):
                logger.error(f"Coverage JSON file not found: {self.coverage_json}")
                return {}
            
            with open(self.coverage_json, "r") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading coverage data: {str(e)}")
            return {}
    
    def _generate_summary(self, coverage_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a summary of coverage data.
        
        Args:
            coverage_data: Coverage data dictionary
            
        Returns:
            Summary dictionary
        """
        try:
            totals = coverage_data.get("totals", {})
            
            summary = {
                "covered_lines": totals.get("covered_lines", 0),
                "num_statements": totals.get("num_statements", 0),
                "percent_covered": totals.get("percent_covered", 0),
                "missing_lines": totals.get("missing_lines", 0),
                "excluded_lines": totals.get("excluded_lines", 0)
            }
            
            # Add module summaries
            modules = []
            for file_path, file_data in coverage_data.get("files", {}).items():
                if file_path.startswith(("api/", "ui/")):
                    modules.append({
                        "file_path": file_path,
                        "percent_covered": file_data.get("summary", {}).get("percent_covered", 0),
                        "num_statements": file_data.get("summary", {}).get("num_statements", 0),
                        "missing_lines": file_data.get("summary", {}).get("missing_lines", 0)
                    })
            
            # Sort modules by coverage percentage (ascending)
            modules.sort(key=lambda x: x["percent_covered"])
            
            summary["modules"] = modules
            summary["low_coverage_modules"] = [m for m in modules if m["percent_covered"] < 70]
            
            return summary
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            return {}
    
    def get_coverage_trends(self) -> Dict[str, Any]:
        """
        Get coverage trends over time.
        
        Returns:
            Dictionary with coverage trends
        """
        try:
            # This would ideally read from a database or cache of historical coverage data
            # For now, we'll return a placeholder
            return {
                "success": True,
                "message": "Coverage trend analysis not implemented yet"
            }
        except Exception as e:
            logger.error(f"Error getting coverage trends: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def suggest_tests(self) -> Dict[str, Any]:
        """
        Suggest tests based on coverage data.
        
        Returns:
            Dictionary with test suggestions
        """
        try:
            coverage_data = self._load_coverage_data()
            if not coverage_data:
                return {
                    "success": False,
                    "error": "Failed to load coverage data"
                }
            
            # Find modules with low coverage
            suggestions = []
            for file_path, file_data in coverage_data.get("files", {}).items():
                if file_path.startswith(("api/", "ui/")):
                    percent_covered = file_data.get("summary", {}).get("percent_covered", 0)
                    if percent_covered < 70:
                        missing_lines = file_data.get("missing_lines", [])
                        suggestions.append({
                            "file_path": file_path,
                            "percent_covered": percent_covered,
                            "missing_lines_count": len(missing_lines),
                            "sample_missing_lines": missing_lines[:10] if missing_lines else []
                        })
            
            # Sort suggestions by coverage percentage (ascending)
            suggestions.sort(key=lambda x: x["percent_covered"])
            
            return {
                "success": True,
                "suggestions": suggestions[:10]  # Return top 10 suggestions
            }
        except Exception as e:
            logger.error(f"Error suggesting tests: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
