import pytest
from datetime import datetime
from api.models.success_criteria import (
    SuccessCriteria,
    SuccessCriteriaCreate,
    SuccessCriteriaUpdate,
    SuccessCriteriaFilters,
    SuccessCriteriaDetail,
)
from api.services.success_criteria import SuccessCriteriaService

@pytest.fixture
def service():
    """Fixture for success criteria service."""
    return SuccessCriteriaService()

@pytest.fixture
def sample_criteria():
    """Fixture for sample success criteria."""
    return SuccessCriteria(
        id="1",
        name="Test Scenarios",
        description="All test scenarios pass consistently",
        status="success",
        category="functionality",
        details=[
            SuccessCriteriaDetail(
                id="1",
                content="Unit tests passing",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            ),
        ],
    )

@pytest.mark.asyncio
async def test_get_success_criteria_empty(service):
    """Test getting success criteria when none exist."""
    filters = SuccessCriteriaFilters()
    response = await service.get_success_criteria(filters)
    assert response.data == []
    assert response.total == 0
    assert response.page == 1
    assert response.page_size == 10

@pytest.mark.asyncio
async def test_get_success_criteria_with_data(service, sample_criteria):
    """Test getting success criteria with existing data."""
    service._criteria.append(sample_criteria)
    filters = SuccessCriteriaFilters()
    response = await service.get_success_criteria(filters)
    assert len(response.data) == 1
    assert response.total == 1
    assert response.data[0].id == sample_criteria.id

@pytest.mark.asyncio
async def test_get_success_criteria_with_filters(service, sample_criteria):
    """Test getting success criteria with filters."""
    service._criteria.append(sample_criteria)
    filters = SuccessCriteriaFilters(status="success", category="functionality")
    response = await service.get_success_criteria(filters)
    assert len(response.data) == 1
    assert response.data[0].id == sample_criteria.id

@pytest.mark.asyncio
async def test_get_success_criteria_by_id(service, sample_criteria):
    """Test getting a success criterion by ID."""
    service._criteria.append(sample_criteria)
    criterion = await service.get_success_criteria_by_id("1")
    assert criterion is not None
    assert criterion.id == sample_criteria.id

@pytest.mark.asyncio
async def test_get_success_criteria_by_id_not_found(service):
    """Test getting a non-existent success criterion by ID."""
    criterion = await service.get_success_criteria_by_id("1")
    assert criterion is None

@pytest.mark.asyncio
async def test_create_success_criteria(service):
    """Test creating a new success criterion."""
    create_data = SuccessCriteriaCreate(
        name="New Criterion",
        description="Test description",
        category="performance",
    )
    criterion = await service.create_success_criteria(create_data)
    assert criterion.name == create_data.name
    assert criterion.description == create_data.description
    assert criterion.category == create_data.category
    assert criterion.status == "pending"
    assert len(criterion.details) == 0
    assert len(service._criteria) == 1

@pytest.mark.asyncio
async def test_update_success_criteria(service, sample_criteria):
    """Test updating a success criterion."""
    service._criteria.append(sample_criteria)
    update_data = SuccessCriteriaUpdate(
        name="Updated Name",
        description="Updated description",
        status="failed",
        category="security",
    )
    criterion = await service.update_success_criteria("1", update_data)
    assert criterion is not None
    assert criterion.name == update_data.name
    assert criterion.description == update_data.description
    assert criterion.status == update_data.status
    assert criterion.category == update_data.category

@pytest.mark.asyncio
async def test_update_success_criteria_not_found(service):
    """Test updating a non-existent success criterion."""
    update_data = SuccessCriteriaUpdate(name="Updated Name")
    criterion = await service.update_success_criteria("1", update_data)
    assert criterion is None

@pytest.mark.asyncio
async def test_update_success_criteria_status(service, sample_criteria):
    """Test updating the status of a success criterion."""
    service._criteria.append(sample_criteria)
    criterion = await service.update_success_criteria_status("1", "failed")
    assert criterion is not None
    assert criterion.status == "failed"

@pytest.mark.asyncio
async def test_update_success_criteria_status_not_found(service):
    """Test updating the status of a non-existent success criterion."""
    criterion = await service.update_success_criteria_status("1", "failed")
    assert criterion is None

@pytest.mark.asyncio
async def test_add_success_criteria_detail(service, sample_criteria):
    """Test adding a new detail to a success criterion."""
    service._criteria.append(sample_criteria)
    criterion = await service.add_success_criteria_detail("1", "New detail")
    assert criterion is not None
    assert len(criterion.details) == 2
    assert criterion.details[-1].content == "New detail"

@pytest.mark.asyncio
async def test_add_success_criteria_detail_not_found(service):
    """Test adding a detail to a non-existent success criterion."""
    criterion = await service.add_success_criteria_detail("1", "New detail")
    assert criterion is None

@pytest.mark.asyncio
async def test_delete_success_criteria(service, sample_criteria):
    """Test deleting a success criterion."""
    service._criteria.append(sample_criteria)
    deleted = await service.delete_success_criteria("1")
    assert deleted is True
    assert len(service._criteria) == 0

@pytest.mark.asyncio
async def test_delete_success_criteria_not_found(service):
    """Test deleting a non-existent success criterion."""
    deleted = await service.delete_success_criteria("1")
    assert deleted is False

@pytest.mark.asyncio
async def test_get_success_criteria_pagination(service, sample_criteria):
    """Test pagination of success criteria."""
    # Add multiple criteria
    for i in range(15):
        criterion = SuccessCriteria(
            id=str(i),
            name=f"Test Criterion {i}",
            description=f"Test description {i}",
            status="success",
            category="functionality",
            details=[],
        )
        service._criteria.append(criterion)

    # Test first page
    filters = SuccessCriteriaFilters(page=1, page_size=10)
    response = await service.get_success_criteria(filters)
    assert len(response.data) == 10
    assert response.total == 15
    assert response.page == 1
    assert response.page_size == 10

    # Test second page
    filters = SuccessCriteriaFilters(page=2, page_size=10)
    response = await service.get_success_criteria(filters)
    assert len(response.data) == 5
    assert response.total == 15
    assert response.page == 2
    assert response.page_size == 10

@pytest.mark.asyncio
async def test_get_success_criteria_filtering(service, sample_criteria):
    """Test filtering of success criteria."""
    # Add multiple criteria with different statuses and categories
    criteria = [
        SuccessCriteria(
            id="1",
            name="Test Criterion 1",
            description="Test description 1",
            status="success",
            category="functionality",
            details=[],
        ),
        SuccessCriteria(
            id="2",
            name="Test Criterion 2",
            description="Test description 2",
            status="pending",
            category="performance",
            details=[],
        ),
        SuccessCriteria(
            id="3",
            name="Test Criterion 3",
            description="Test description 3",
            status="failed",
            category="security",
            details=[],
        ),
    ]
    service._criteria.extend(criteria)

    # Test filtering by status
    filters = SuccessCriteriaFilters(status="success")
    response = await service.get_success_criteria(filters)
    assert len(response.data) == 1
    assert response.data[0].status == "success"

    # Test filtering by category
    filters = SuccessCriteriaFilters(category="performance")
    response = await service.get_success_criteria(filters)
    assert len(response.data) == 1
    assert response.data[0].category == "performance"

    # Test filtering by both status and category
    filters = SuccessCriteriaFilters(status="success", category="functionality")
    response = await service.get_success_criteria(filters)
    assert len(response.data) == 1
    assert response.data[0].status == "success"
    assert response.data[0].category == "functionality" 