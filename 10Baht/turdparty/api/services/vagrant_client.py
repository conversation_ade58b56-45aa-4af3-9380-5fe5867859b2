
"""
Client for interacting with Vagrant virtual machines via gRPC or SSH.
"""
import logging
import asyncio
import os
from typing import Dict, List, Any, Optional, Union

from api.services.fathom_ssh_client import FathomSSHClient

logger = logging.getLogger(__name__)

class VagrantClient:
    """Client for interacting with Vagrant virtual machines."""
    
    def __init__(self, server: Optional[str] = None):
        """Initialize Vagrant client."""
        self.server = server or os.environ.get("DEFAULT_VAGRANT_SERVER")
        self.use_ssh = os.environ.get("USE_VAGRANT_SSH", "true").lower() == "true"
        self.ssh_client = None
        self.grpc_client = None
        logger.info("Vagrant client initialized with server=%s, use_ssh=%s", 
                   self.server, self.use_ssh)
    
    async def connect(self) -> Dict[str, Any]:
        """Connect to Vagrant server."""
        try:
            if self.use_ssh:
                # Use SSH client
                ssh_key_path = os.environ.get("SSH_KEY_PATH")
                ssh_key = os.environ.get("VAGRANT_SSH_KEY")
                ssh_user = os.environ.get("VAGRANT_SSH_USER", "vagrant")
                
                ssh_config = {
                    "username": ssh_user
                }
                
                self.ssh_client = FathomSSHClient(
                    ssh_config=ssh_config,
                    ssh_key_path=ssh_key_path,
                    ssh_key=ssh_key
                )
                
                result = await self.ssh_client.connect()
                if "error" in result:
                    logger.error("Failed to connect via SSH: %s", result["error"])
                    return result
                
                logger.info("Connected to Vagrant server via SSH")
                return {"success": True, "mode": "ssh"}
            else:
                # Use gRPC client (not fully implemented in this snippet)
                logger.error("gRPC mode not implemented in this snippet")
                return {"error": "gRPC mode not implemented"}
        except Exception as e:
            logger.error("Error connecting to Vagrant server: %s", str(e))
            return {"error": str(e)}
    
    async def close(self) -> None:
        """Close the client connection."""
        try:
            if self.ssh_client:
                await self.ssh_client.close()
                self.ssh_client = None
            
            if self.grpc_client:
                await self.grpc_client.close()
                self.grpc_client = None
                
            logger.debug("Vagrant client closed")
        except Exception as e:
            logger.error("Error closing Vagrant client: %s", str(e))
    
    async def status(self, vm_id: str) -> Dict[str, Any]:
        """
        Get VM status.
        
        Args:
            vm_id: The VM identifier
            
        Returns:
            Dictionary with status information
        """
        try:
            if self.use_ssh:
                if not self.ssh_client:
                    return {"error": "SSH client not connected"}
                
                result = await self.ssh_client.status(self.server)
                
                # Map hostname to vm_id in the result
                if "hostname" in result:
                    result["vm_id"] = vm_id
                    del result["hostname"]
                
                return result
            else:
                # gRPC implementation would go here
                return {"error": "gRPC mode not implemented"}
        except Exception as e:
            logger.error("Error getting VM status: %s", str(e))
            return {"error": str(e)}
    
    async def up(self, vm_id: str, provision: bool = True) -> Dict[str, Any]:
        """
        Start a VM.
        
        Args:
            vm_id: The VM identifier
            provision: Whether to run provisioners
            
        Returns:
            Dictionary with operation result
        """
        try:
            if self.use_ssh:
                if not self.ssh_client:
                    return {"error": "SSH client not connected"}
                
                result = await self.ssh_client.up(self.server, provision=provision)
                
                # Map hostname to vm_id in the result
                if "hostname" in result:
                    result["vm_id"] = vm_id
                    del result["hostname"]
                
                return result
            else:
                # gRPC implementation would go here
                return {"error": "gRPC mode not implemented"}
        except Exception as e:
            logger.error("Error starting VM: %s", str(e))
            return {"error": str(e)}
    
    async def halt(self, vm_id: str) -> Dict[str, Any]:
        """
        Stop a VM.
        
        Args:
            vm_id: The VM identifier
            
        Returns:
            Dictionary with operation result
        """
        try:
            if self.use_ssh:
                if not self.ssh_client:
                    return {"error": "SSH client not connected"}
                
                result = await self.ssh_client.halt(self.server)
                
                # Map hostname to vm_id in the result
                if "hostname" in result:
                    result["vm_id"] = vm_id
                    del result["hostname"]
                
                return result
            else:
                # gRPC implementation would go here
                return {"error": "gRPC mode not implemented"}
        except Exception as e:
            logger.error("Error stopping VM: %s", str(e))
            return {"error": str(e)}
    
    async def destroy(self, vm_id: str, force: bool = False) -> Dict[str, Any]:
        """
        Destroy a VM.
        
        Args:
            vm_id: The VM identifier
            force: Whether to force destruction
            
        Returns:
            Dictionary with operation result
        """
        try:
            if self.use_ssh:
                if not self.ssh_client:
                    return {"error": "SSH client not connected"}
                
                result = await self.ssh_client.destroy(self.server, force=force)
                
                # Map hostname to vm_id in the result
                if "hostname" in result:
                    result["vm_id"] = vm_id
                    del result["hostname"]
                
                return result
            else:
                # gRPC implementation would go here
                return {"error": "gRPC mode not implemented"}
        except Exception as e:
            logger.error("Error destroying VM: %s", str(e))
            return {"error": str(e)}
    
    async def execute_command(
            self, vm_id: str, command: str, sudo: bool = False
        ) -> Dict[str, Any]:
        """
        Execute a command on a VM.
        
        Args:
            vm_id: The VM identifier
            command: The command to execute
            sudo: Whether to use sudo
            
        Returns:
            Dictionary with command result
        """
        try:
            if self.use_ssh:
                if not self.ssh_client:
                    return {"error": "SSH client not connected"}
                
                result = await self.ssh_client.execute_command(
                    self.server, command, sudo=sudo
                )
                
                # Map hostname to vm_id in the result
                if "hostname" in result:
                    result["vm_id"] = vm_id
                    del result["hostname"]
                
                return result
            else:
                # gRPC implementation would go here
                return {"error": "gRPC mode not implemented"}
        except Exception as e:
            logger.error("Error executing command: %s", str(e))
            return {"error": str(e)}
    
    async def remote_status(self, vm_id: str) -> Dict[str, Any]:
        """
        Get remote system status for a VM.
        
        Args:
            vm_id: The VM identifier
            
        Returns:
            Dictionary with system status
        """
        # This is a stub that would typically get system metrics
        if not self.use_ssh:
            return {
                "vm_id": vm_id,
                "error": "Remote status is only supported with SSH mode"
            }
            
    async def suspend(self, vm_id: str) -> Dict[str, Any]:
        """
        Suspend a VM.
        
        Args:
            vm_id: The VM identifier
            
        Returns:
            Dictionary with operation result
        """
        try:
            if self.use_ssh:
                if not self.ssh_client:
                    return {"error": "SSH client not connected"}
                
                result = await self.ssh_client.suspend(self.server)
                
                # Map hostname to vm_id in the result
                if "hostname" in result:
                    result["vm_id"] = vm_id
                    del result["hostname"]
                
                return result
            else:
                # gRPC implementation would go here
                return {"error": "gRPC mode not implemented"}
        except Exception as e:
            logger.error("Error suspending VM: %s", str(e))
            return {"error": str(e)}

class VagrantClient:
    """Client for interacting with Vagrant virtual machines."""
    
    def __init__(self):
        """Initialize the Vagrant client."""
        self.use_ssh = os.getenv("USE_VAGRANT_SSH", "true").lower() == "true"
        self.ssh_client = None
        self.stub = None
        self.channel = None
        
        # Default Vagrant server settings
        self.default_server = os.getenv("DEFAULT_VAGRANT_SERVER", "vagrant.example.com")
        
        logger.info("VagrantClient initialized with SSH mode: %s", self.use_ssh)
    
    async def connect(self) -> bool:
        """
        Connect to the Vagrant service.
        
        Returns:
            Boolean indicating if connection was successful.
        """
        if self.use_ssh:
            try:
                self.ssh_client = FathomSSHClient()
                logger.info("Connected to Vagrant via SSH")
                return True
            except Exception as e:
                logger.error("Failed to initialize SSH client: %s", str(e))
                return False
        else:
            # gRPC connection logic (fallback)
            try:
                import grpc
                from api.grpc import vagrant_pb2_grpc
                
                # Get server address from environment or use default
                server_addr = os.getenv("VAGRANT_GRPC_SERVER", "localhost:50051")
                logger.info(f"Connecting to Vagrant gRPC server at {server_addr}")
                
                # Create insecure channel (for development)
                self.channel = grpc.aio.insecure_channel(server_addr)
                self.stub = vagrant_pb2_grpc.VagrantServiceStub(self.channel)
                
                logger.info("Connected to Vagrant gRPC server at %s", server_addr)
                return True
            except ImportError:
                logger.error("Failed to import gRPC modules. Make sure to generate gRPC code first.")
                return False
            except Exception as e:
                logger.error("Failed to connect to Vagrant gRPC server: %s", str(e))
                return False
    
    async def _get_target_server(self, vm_id: str) -> str:
        """
        Get the target server for a VM ID.
        
        Args:
            vm_id: The ID of the VM.
            
        Returns:
            The hostname to connect to.
        """
        # This is where you'd implement server lookup logic
        # For now, we'll use the default server
        return self.default_server
    
    async def status(self, vm_id: str) -> Dict[str, Any]:
        """
        Get the status of a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            
        Returns:
            Dict with the status information.
        """
        if self.use_ssh:
            if not self.ssh_client:
                if not await self.connect():
                    return {"error": "Not connected to SSH"}
            
            target_server = await self._get_target_server(vm_id)
            return await self.ssh_client.get_vm_status(target_server)
        else:
            # gRPC implementation (fallback)
            if not self.stub:
                if not await self.connect():
                    return {"error": "Not connected to gRPC server"}
            
            try:
                from api.grpc import vagrant_pb2
                
                request = vagrant_pb2.VagrantStatusRequest(vm_id=vm_id)
                response = await self.stub.Status(request)
                
                if response.error_message:
                    logger.error("Error getting VM status: %s", response.error_message)
                    return {"error": response.error_message}
                
                return {
                    "vm_id": vm_id,
                    "status": response.status
                }
            except Exception as e:
                logger.error("Error calling Status RPC: %s", str(e))
                return {"error": str(e)}
    
    async def up(self, vm_id: str, provision: bool = True) -> Dict[str, Any]:
        """
        Start a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            provision: Whether to provision the VM.
            
        Returns:
            Dict with the command result.
        """
        if self.use_ssh:
            if not self.ssh_client:
                if not await self.connect():
                    return {"error": "Not connected to SSH"}
            
            target_server = await self._get_target_server(vm_id)
            return await self.ssh_client.up(target_server, provision)
        else:
            # gRPC implementation (fallback)
            if not self.stub:
                if not await self.connect():
                    return {"error": "Not connected to gRPC server"}
            
            try:
                from api.grpc import vagrant_pb2
                
                request = vagrant_pb2.VagrantUpRequest(vm_id=vm_id, provision=provision)
                response = await self.stub.Up(request)
                
                if response.error_message:
                    logger.error("Error starting VM: %s", response.error_message)
                    return {"error": response.error_message}
                
                return {
                    "vm_id": vm_id,
                    "success": True,
                    "message": response.message
                }
            except Exception as e:
                logger.error("Error calling Up RPC: %s", str(e))
                return {"error": str(e)}
    
    async def halt(self, vm_id: str, force: bool = False) -> Dict[str, Any]:
        """
        Stop a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            force: Whether to force the halt.
            
        Returns:
            Dict with the command result.
        """
        if self.use_ssh:
            if not self.ssh_client:
                if not await self.connect():
                    return {"error": "Not connected to SSH"}
            
            target_server = await self._get_target_server(vm_id)
            return await self.ssh_client.halt(target_server, force)
        else:
            # gRPC implementation (fallback)
            if not self.stub:
                if not await self.connect():
                    return {"error": "Not connected to gRPC server"}
            
            try:
                from api.grpc import vagrant_pb2
                
                request = vagrant_pb2.VagrantHaltRequest(vm_id=vm_id, force=force)
                response = await self.stub.Halt(request)
                
                if response.error_message:
                    logger.error("Error stopping VM: %s", response.error_message)
                    return {"error": response.error_message}
                
                return {
                    "vm_id": vm_id,
                    "success": True,
                    "message": response.message
                }
            except Exception as e:
                logger.error("Error calling Halt RPC: %s", str(e))
                return {"error": str(e)}
    
    async def destroy(self, vm_id: str, force: bool = False) -> Dict[str, Any]:
        """
        Destroy a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            force: Whether to force the destruction.
            
        Returns:
            Dict with the command result.
        """
        if self.use_ssh:
            if not self.ssh_client:
                if not await self.connect():
                    return {"error": "Not connected to SSH"}
            
            target_server = await self._get_target_server(vm_id)
            return await self.ssh_client.destroy(target_server, force)
        else:
            # gRPC implementation (fallback)
            if not self.stub:
                if not await self.connect():
                    return {"error": "Not connected to gRPC server"}
            
            try:
                from api.grpc import vagrant_pb2
                
                request = vagrant_pb2.VagrantDestroyRequest(vm_id=vm_id, force=force)
                response = await self.stub.Destroy(request)
                
                if response.error_message:
                    logger.error("Error destroying VM: %s", response.error_message)
                    return {"error": response.error_message}
                
                return {
                    "vm_id": vm_id,
                    "success": True,
                    "message": response.message
                }
            except Exception as e:
                logger.error("Error calling Destroy RPC: %s", str(e))
                return {"error": str(e)}
    
    async def execute_command(self, vm_id: str, command: str, sudo: bool = False) -> Dict[str, Any]:
        """
        Execute a command on a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            command: The command to execute.
            sudo: Whether to execute the command with sudo.
            
        Returns:
            Dict with the command execution result.
        """
        if self.use_ssh:
            if not self.ssh_client:
                if not await self.connect():
                    return {"error": "Not connected to SSH"}
            
            target_server = await self._get_target_server(vm_id)
            return await self.ssh_client.execute_command(target_server, command, sudo)
        else:
            # gRPC implementation (fallback)
            if not self.stub:
                if not await self.connect():
                    return {"error": "Not connected to gRPC server"}
            
            try:
                from api.grpc import vagrant_pb2
                
                request = vagrant_pb2.VagrantExecuteCommandRequest(
                    vm_id=vm_id,
                    command=command,
                    sudo=sudo
                )
                response = await self.stub.ExecuteCommand(request)
                
                if response.error_message:
                    logger.error("Error executing command: %s", response.error_message)
                    return {"error": response.error_message}
                
                return {
                    "vm_id": vm_id,
                    "success": True,
                    "stdout": response.stdout,
                    "stderr": response.stderr,
                    "exit_code": response.exit_code
                }
            except Exception as e:
                logger.error("Error calling ExecuteCommand RPC: %s", str(e))
                return {"error": str(e)}

    async def remote_status(self, vm_id: str) -> Dict[str, Any]:
        """
        Get detailed remote status of a Vagrant VM, including system metrics.
        
        Args:
            vm_id: The ID of the VM.
            
        Returns:
            Dict with remote status information including uptime, memory, and disk usage.
        """
        if self.use_ssh:
            if not self.ssh_client:
                if not await self.connect():
                    return {"error": "Not connected to SSH"}
            
            target_server = await self._get_target_server(vm_id)
            try:
                return await self.ssh_client.get_remote_status(target_server)
            except Exception as e:
                logger.error(f"Error getting remote status via SSH: {str(e)}")
                return {
                    "vm_id": vm_id,
                    "error": f"Failed to get remote status: {str(e)}"
                }
        else:
            # gRPC implementation
            if not self.stub:
                if not await self.connect():
                    return {"error": "Not connected to gRPC server"}
            
            try:
                from api.grpc import vagrant_pb2
                
                request = vagrant_pb2.VagrantRemoteStatusRequest(vm_id=vm_id)
                response = await self.stub.RemoteStatus(request)
                
                if response.error_message:
                    logger.error("Error getting remote status: %s", response.error_message)
                    return {"error": response.error_message}
                
                # Parse system metrics from response
                return {
                    "vm_id": vm_id,
                    "uptime": response.uptime,
                    "memory_total": response.memory_total,
                    "memory_used": response.memory_used,
                    "memory_free": response.memory_free,
                    "disk_total": response.disk_total,
                    "disk_used": response.disk_used,
                    "disk_free": response.disk_free,
                    "load_averages": [
                        response.load_avg_1,
                        response.load_avg_5,
                        response.load_avg_15
                    ]
                }
            except AttributeError:
                logger.warning("RemoteStatus RPC method may not be implemented in the server")
                return {
                    "vm_id": vm_id,
                    "error": "RemoteStatus method not implemented in the gRPC server"
                }
            except Exception as e:
                logger.error("Error calling RemoteStatus RPC: %s", str(e))
                return {"error": str(e)}
    
    async def close(self):
        """Close the client connection."""
        if self.channel:
            await self.channel.close()
            logger.info("Closed gRPC channel")
