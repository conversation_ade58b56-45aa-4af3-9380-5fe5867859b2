
"""Prometheus metrics service for monitoring application performance."""

import logging
import os
from typing import Op<PERSON>

from prometheus_client import Counter, Gauge, Histogram, start_http_server
from prometheus_client.exposition import make_wsgi_app
from werkzeug.middleware.dispatcher import DispatcherMiddleware
from werkzeug.serving import run_simple

logger = logging.getLogger(__name__)

class PrometheusMetrics:
    """Prometheus metrics service for monitoring application performance."""
    
    def __init__(self):
        """Initialize the Prometheus metrics service."""
        self.enabled = self._check_enabled()
        self.port = int(os.getenv("PROMETHEUS_PORT", "8081"))
        self.endpoint = os.getenv("PROMETHEUS_ENDPOINT", "/metrics")
        
        if self.enabled:
            # Create metrics
            self.request_count = Counter(
                'app_request_count', 
                'Total count of requests by path and method', 
                ['path', 'method']
            )
            
            self.request_latency = Histogram(
                'app_request_latency_seconds', 
                'Request latency in seconds by path', 
                ['path']
            )
            
            self.error_count = Counter(
                'app_error_count', 
                'Total count of errors by path and status', 
                ['path', 'status']
            )
            
            self.active_requests = Gauge(
                'app_active_requests', 
                'Active requests being processed', 
                ['path']
            )
            
            logger.info(f"Prometheus metrics initialized at endpoint: {self.endpoint}")
    
    def _check_enabled(self) -> bool:
        """Check if Prometheus metrics are enabled based on environment variables."""
        return os.getenv("ENABLE_PROMETHEUS", "").lower() in ("true", "1", "yes")
    
    def setup_metrics_endpoint(self, app) -> None:
        """Set up the metrics endpoint for the FastAPI application."""
        if not self.enabled:
            logger.info("Prometheus metrics disabled")
            return
        
        # Add metrics middleware
        metrics_app = make_wsgi_app()
        app.add_middleware(
            DispatcherMiddleware, 
            {self.endpoint: metrics_app}
        )
        
        logger.info(f"Prometheus metrics endpoint configured at {self.endpoint}")
    
    def record_request(self, path: str, method: str) -> None:
        """Record a request in Prometheus metrics."""
        if not self.enabled:
            return
        self.request_count.labels(path=path, method=method).inc()
    
    def record_error(self, path: str, status: int) -> None:
        """Record an error in Prometheus metrics."""
        if not self.enabled:
            return
        self.error_count.labels(path=path, status=status).inc()
    
    def start_request_timer(self, path: str) -> Optional[object]:
        """Start a timer for measuring request latency."""
        if not self.enabled:
            return None
        self.active_requests.labels(path=path).inc()
        return self.request_latency.labels(path=path).time()
    
    def end_request_timer(self, timer: Optional[object], path: str) -> None:
        """End a timer for measuring request latency."""
        if not self.enabled or timer is None:
            return
        timer.stop()  # This automatically records the duration
        self.active_requests.labels(path=path).dec()
