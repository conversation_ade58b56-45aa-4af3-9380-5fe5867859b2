"""
Service for vm_injection feature
"""
from fastapi import BackgroundTasks, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import and_
from api.models.vm_injection import VMInjectionCreateSchema, VMInjectionUpdateSchema, VMInjectionStatusSchema
from api.db.models import Vm_injection
from datetime import datetime
import uuid
from typing import List, Optional, Tuple

class VMInjectionService:
    """Service for vm_injection operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_all(self, skip: int = 0, limit: int = 100, user_id: uuid.UUID = None, vagrant_vm_id: uuid.UUID = None) -> Tuple[List[Vm_injection], int]:
        """Get all vm_injections with optional filtering"""
        query = self.db.query(Vm_injection)
        if user_id:
            query = query.filter(Vm_injection.owner_id == user_id)
        if vagrant_vm_id:
            query = query.filter(Vm_injection.vagrant_vm_id == vagrant_vm_id)
        
        total = query.count()
        items = query.offset(skip).limit(limit).all()
        return items, total
    
    def get_by_id(self, vm_injection_id: uuid.UUID, user_id: uuid.UUID = None) -> Optional[Vm_injection]:
        """Get vm_injection by ID with optional user_id check"""
        query = self.db.query(Vm_injection).filter(Vm_injection.id == vm_injection_id)
        if user_id:
            query = query.filter(Vm_injection.owner_id == user_id)
        return query.first()
    
    async def create(self, vm_injection_data: VMInjectionCreateSchema, background_tasks: BackgroundTasks, user_id: uuid.UUID) -> Vm_injection:
        """Create a new vm_injection"""
        db_vm_injection = Vm_injection(
            vagrant_vm_id=vm_injection_data.vagrant_vm_id,
            file_selection_id=vm_injection_data.file_selection_id,
            description=vm_injection_data.description,
            additional_command=vm_injection_data.additional_command,
            owner_id=user_id,
            status="pending"
        )
        self.db.add(db_vm_injection)
        self.db.commit()
        self.db.refresh(db_vm_injection)
        
        # Add background task to perform the injection
        background_tasks.add_task(self._perform_injection_async, db_vm_injection.id)
        
        return db_vm_injection
    
    def _perform_injection_async(self, vm_injection_id: uuid.UUID):
        """Background task to perform the injection"""
        # This would contain the actual injection logic
        # For now, we'll just update the status
        db_vm_injection = self.get_by_id(vm_injection_id)
        if db_vm_injection:
            db_vm_injection.status = "completed"
            db_vm_injection.completed_on = datetime.now()
            self.db.commit()
    
    def update(self, vm_injection_id: uuid.UUID, vm_injection_data: VMInjectionUpdateSchema, user_id: uuid.UUID = None) -> Vm_injection:
        """Update a vm_injection"""
        db_vm_injection = self.get_by_id(vm_injection_id, user_id)
        if not db_vm_injection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM injection with id {vm_injection_id} not found"
            )
        
        # Update only provided fields
        for key, value in vm_injection_data.dict(exclude_unset=True).items():
            setattr(db_vm_injection, key, value)
        
        self.db.commit()
        self.db.refresh(db_vm_injection)
        return db_vm_injection
    
    def get_status(self, vm_injection_id: uuid.UUID, user_id: uuid.UUID = None) -> VMInjectionStatusSchema:
        """Get the status of a vm_injection"""
        db_vm_injection = self.get_by_id(vm_injection_id, user_id)
        if not db_vm_injection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM injection with id {vm_injection_id} not found"
            )
        
        return VMInjectionStatusSchema(
            id=db_vm_injection.id,
            vagrant_vm_id=db_vm_injection.vagrant_vm_id,
            file_selection_id=db_vm_injection.file_selection_id,
            status=db_vm_injection.status,
            error_message=db_vm_injection.error_message,
            completed_on=db_vm_injection.completed_on
        )
    
    async def delete(self, vm_injection_id: uuid.UUID, user_id: uuid.UUID = None) -> None:
        """Delete a vm_injection"""
        db_vm_injection = self.get_by_id(vm_injection_id, user_id)
        if not db_vm_injection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM injection with id {vm_injection_id} not found"
            )
        
        self.db.delete(db_vm_injection)
        self.db.commit()
