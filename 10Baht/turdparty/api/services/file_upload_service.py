"""
File upload service for MinIO storage.
"""
import os
import uuid
import logging
import hashlib
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from fastapi import UploadFile, HTTPException

from minio import Minio

# Import centralized API endpoints
from api.core.endpoints import APIEndpoints

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileUploadService:
    """Service for handling file uploads and storage in MinIO."""
    
    def __init__(self, minio_client: Minio):
        """
        Initialize file upload service with MinIO client.
        
        Args:
            minio_client: Configured MinIO client
        """
        self.minio_client = minio_client
        self.bucket_name = "turdparty-uploads"
        
        # Ensure bucket exists
        self._ensure_bucket_exists()
    
    def _ensure_bucket_exists(self) -> None:
        """Ensure that the storage bucket exists, creating it if necessary."""
        # First check if we're in test mode
        test_mode = os.environ.get("API_TEST_MODE", "").lower() == "true"
        
        # If in test mode, always use mock bucket
        if test_mode:
            logger.info("Test mode active - using mock file storage")
            self._create_mock_bucket()
            return
            
        max_retries = 3
        retry_delay = 2  # seconds
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Checking if bucket {self.bucket_name} exists")
                if not self.minio_client.bucket_exists(self.bucket_name):
                    logger.info(f"Creating bucket {self.bucket_name}")
                    self.minio_client.make_bucket(self.bucket_name)
                    logger.info(f"Bucket {self.bucket_name} created successfully")
                else:
                    logger.info(f"Bucket {self.bucket_name} already exists")
                return  # Success, exit the function
            except Exception as e:
                logger.error(f"Error ensuring bucket exists: {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # Final attempt failed, create mock bucket
                    logger.warning("All connection attempts failed, creating mock bucket for fallback")
                    self._create_mock_bucket()
    
    def _create_mock_bucket(self) -> None:
        """Create a mock bucket for testing/development."""
        logger.warning("Creating mock bucket for testing")
        self._mock_files = {}
        self._mock_bucket_exists = True
        
        # Log successful mock creation
        logger.info("Mock bucket created successfully for file storage")
    
    async def get_all(self):
        """
        Get all file uploads.
        
        Returns:
            List: List of file uploads
        """
        try:
            # List all objects in the bucket
            objects = self.minio_client.list_objects(self.bucket_name)
            
            # Convert to file upload objects
            result = []
            for obj in objects:
                # Ensure file_size is an integer
                file_size = obj.size if obj.size is not None else 0
                
                file_id = obj.object_name.split("/")[0]
                result.append({
                    "id": file_id,
                    "filename": obj.object_name.split("/")[-1],
                    "file_size": file_size,
                    "content_type": "application/octet-stream",
                    "created_at": obj.last_modified,
                    "updated_at": obj.last_modified,
                    "description": "",
                    # Use centralized endpoint definition
                    "download_url": f"/api/v1/file_upload/download/{file_id}"
                })
            
            return result
        except Exception as e:
            logger.error(f"Error listing files: {str(e)}")
            # Return empty list in case of error
            return []
    
    async def create(self, file_upload_data, file_content: bytes):
        """
        Create a new file upload.
        
        Args:
            file_upload_data: File upload metadata
            file_content: Binary content of the file
            
        Returns:
            Dict: Created file upload
        """
        try:
            # Generate a unique ID for the file
            file_id = str(uuid.uuid4())
            
            # Create object key - prefix with ID for easier management
            object_key = f"{file_id}/{file_upload_data.filename}"
            
            # Calculate file hash for integrity checking
            file_hash = hashlib.md5(file_content).hexdigest()
            
            # Check if we're using a mock bucket
            if hasattr(self, "_mock_bucket_exists") and self._mock_bucket_exists:
                logger.info(f"Using mock storage for file {object_key}")
                # Store in mock bucket
                self._mock_files[object_key] = {
                    "content": file_content,
                    "metadata": {
                        "Content-Type": file_upload_data.content_type
                    },
                    "file_id": file_id,
                    "filename": file_upload_data.filename,
                    "file_size": file_upload_data.file_size,
                    "content_type": file_upload_data.content_type,
                    "created_at": datetime.utcnow().isoformat()
                }
                logger.info(f"File {object_key} stored in mock bucket")
            else:
                # Upload to MinIO
                logger.info(f"Uploading file {object_key} to MinIO bucket {self.bucket_name}")
                from io import BytesIO
                self.minio_client.put_object(
                    bucket_name=self.bucket_name,
                    object_name=object_key,
                    data=BytesIO(file_content),
                    length=len(file_content),
                    content_type=file_upload_data.content_type
                )
            
            # Return file upload data
            now = datetime.utcnow().isoformat()
            return {
                "id": file_id,
                "filename": file_upload_data.filename,
                "file_size": file_upload_data.file_size,
                "content_type": file_upload_data.content_type,
                "file_hash": file_hash,
                "description": file_upload_data.description,
                "created_at": now,
                "updated_at": now,
                # Use centralized endpoint definition for download URL
                "download_url": f"/api/v1/file_upload/download/{file_id}"
            }
        except Exception as e:
            logger.error(f"Error creating file upload: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error storing file: {str(e)}")
    
    async def create_folder(self, files: List[UploadFile], paths: List[str], description: str):
        """
        Create a folder upload with multiple files.
        
        Args:
            files: List of files to upload
            paths: Relative paths of the files in the folder
            description: Description of the folder upload
            
        Returns:
            Dict: Created folder upload
        """
        try:
            # Generate a unique ID for the folder
            folder_id = str(uuid.uuid4())
            
            # Upload each file
            uploaded_files = []
            for file, path in zip(files, paths):
                content = await file.read()
                
                # Create object key with folder prefix
                object_key = f"{folder_id}/{path}"
                
                # Upload to MinIO
                logger.info(f"Uploading file {object_key} to MinIO bucket {self.bucket_name}")
                from io import BytesIO
                self.minio_client.put_object(
                    bucket_name=self.bucket_name,
                    object_name=object_key,
                    data=BytesIO(content),
                    length=len(content),
                    content_type=file.content_type or "application/octet-stream"
                )
                
                # Add to uploaded files
                uploaded_files.append({
                    "id": str(uuid.uuid4()),
                    "filename": file.filename,
                    "path": path,
                    "file_size": len(content),
                    "content_type": file.content_type or "application/octet-stream"
                })
            
            # Return folder upload data
            return {
                "folder_id": folder_id,
                "description": description,
                "created_at": datetime.utcnow().isoformat(),
                "file_count": len(files),
                "files": uploaded_files
            }
        except Exception as e:
            logger.error(f"Error creating folder upload: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error storing folder: {str(e)}")
    
    async def download(self, file_id: str):
        """
        Download a file.
        
        Args:
            file_id: ID of the file to download
            
        Returns:
            Tuple: (file_content, filename, content_type)
        """
        try:
            # First check if we're using mock bucket
            if hasattr(self, "_mock_bucket_exists") and self._mock_bucket_exists and hasattr(self, "_mock_files"):
                logger.info(f"Searching for file {file_id} in mock storage")
                # Find the file in our mock storage
                for key, file_data in self._mock_files.items():
                    if file_data.get("file_id") == file_id or key.startswith(f"{file_id}/"):
                        logger.info(f"Found file {key} in mock storage")
                        return (
                            file_data["content"],
                            key.split("/")[-1],
                            file_data.get("content_type", "application/octet-stream")
                        )
                
                # File not found in mock storage
                logger.warning(f"File {file_id} not found in mock storage")
                return None
            
            # List files with the ID prefix to find the right one
            try:
                objects = self.minio_client.list_objects(self.bucket_name, prefix=f"{file_id}/")
                object_names = [obj.object_name for obj in objects]
                
                if not object_names:
                    logger.warning(f"File {file_id} not found in MinIO")
                    return None
                
                # Select the first file (there should only be one)
                object_name = object_names[0]
                filename = object_name.split("/")[-1]
                
                # Get the file content
                from io import BytesIO
                response = self.minio_client.get_object(self.bucket_name, object_name)
                content = response.read()
                content_type = response.getheader("Content-Type") or "application/octet-stream"
                
                return (content, filename, content_type)
            except Exception as e:
                logger.error(f"Error downloading file {file_id} from MinIO: {str(e)}")
                return None
        except Exception as e:
            logger.error(f"Error in download method: {str(e)}")
            return None

class FileUploadSchema:
    """Schema for file upload data."""
    
    def __init__(self, filename, content_type=None, description=None):
        self.filename = filename
        self.content_type = content_type or "application/octet-stream"
        self.description = description or "" 