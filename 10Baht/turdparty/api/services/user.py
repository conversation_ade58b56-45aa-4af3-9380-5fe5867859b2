
"""User service module."""

import logging
from typing import List, Optional, Dict, Any, Union

from api.db.repositories.user import UserRepository
from api.schemas.user import UserCreate, UserUpdate, UserResponse
from api.core.errors import NotFoundError, DatabaseError

logger = logging.getLogger(__name__)


class UserService:
    """Service for user-related operations."""
    
    def __init__(self, user_repository: UserRepository):
        """Initialize with user repository."""
        self.user_repository = user_repository
    
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """
        Create a new user.
        
        Args:
            user_data: User creation data
            
        Returns:
            Created user
            
        Raises:
            DatabaseError: On database error
        """
        try:
            user = await self.user_repository.create(
                email=user_data.email,
                full_name=user_data.full_name,
                password=user_data.password,
                is_active=user_data.is_active
            )
            return UserResponse.from_orm(user)
        except Exception as e:
            logger.error(f"Error in create_user service: {str(e)}")
            raise DatabaseError(f"Error creating user: {str(e)}")
    
    async def get_user(self, user_id: int) -> Optional[UserResponse]:
        """
        Get a user by ID.
        
        Args:
            user_id: ID of the user to retrieve
            
        Returns:
            User instance or None if not found
            
        Raises:
            DatabaseError: On database error
        """
        try:
            user = await self.user_repository.get(user_id)
            if not user:
                return None
            return UserResponse.from_orm(user)
        except Exception as e:
            logger.error(f"Error in get_user service: {str(e)}")
            raise DatabaseError(f"Error retrieving user: {str(e)}")
    
    async def get_user_by_email(self, email: str) -> Optional[UserResponse]:
        """
        Get a user by email.
        
        Args:
            email: Email of the user to retrieve
            
        Returns:
            User instance or None if not found
            
        Raises:
            DatabaseError: On database error
        """
        try:
            user = await self.user_repository.get_by_email(email)
            if not user:
                return None
            return UserResponse.from_orm(user)
        except Exception as e:
            logger.error(f"Error in get_user_by_email service: {str(e)}")
            raise DatabaseError(f"Error retrieving user by email: {str(e)}")
    
    async def get_users(
        self, skip: int = 0, limit: int = 100, email: Optional[str] = None
    ) -> List[UserResponse]:
        """
        Get all users with pagination and optional filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            email: Optional email filter
            
        Returns:
            List of users
            
        Raises:
            DatabaseError: On database error
        """
        try:
            filters = {}
            if email:
                filters["email"] = email
                
            users = await self.user_repository.get_all(skip=skip, limit=limit, **filters)
            return [UserResponse.from_orm(user) for user in users]
        except Exception as e:
            logger.error(f"Error in get_users service: {str(e)}")
            raise DatabaseError(f"Error retrieving users: {str(e)}")
    
    async def update_user(self, user_id: int, user_data: UserUpdate) -> UserResponse:
        """
        Update a user by ID.
        
        Args:
            user_id: ID of the user to update
            user_data: User update data
            
        Returns:
            Updated user instance
            
        Raises:
            NotFoundError: If user not found
            DatabaseError: On database error
        """
        try:
            # Prepare update data (exclude None values)
            update_data = user_data.dict(exclude_unset=True)
            if not update_data:
                # If no update data provided, just return the current user
                user = await self.user_repository.get(user_id)
                if not user:
                    raise NotFoundError(f"User with ID {user_id} not found")
                return UserResponse.from_orm(user)
            
            # Perform update
            updated_user = await self.user_repository.update(user_id, **update_data)
            if not updated_user:
                raise NotFoundError(f"User with ID {user_id} not found")
            
            return UserResponse.from_orm(updated_user)
        except NotFoundError as e:
            raise e
        except Exception as e:
            logger.error(f"Error in update_user service: {str(e)}")
            raise DatabaseError(f"Error updating user: {str(e)}")
    
    async def delete_user(self, user_id: int) -> bool:
        """
        Delete a user by ID.
        
        Args:
            user_id: ID of the user to delete
            
        Returns:
            True if successful
            
        Raises:
            NotFoundError: If user not found
            DatabaseError: On database error
        """
        try:
            return await self.user_repository.delete(user_id)
        except NotFoundError as e:
            raise e
        except Exception as e:
            logger.error(f"Error in delete_user service: {str(e)}")
            raise DatabaseError(f"Error deleting user: {str(e)}")
