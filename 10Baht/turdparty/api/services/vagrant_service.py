
"""
Vagrant service for managing Vagrant VMs via gRPC.
"""
import os
import logging
import subprocess
import json
from typing import Dict, List, Optional, Tuple, Any

from api.core.exceptions import NotFoundError, ServiceError

logger = logging.getLogger(__name__)

class VagrantService:
    """Service for interacting with Vagrant virtual machines."""
    
    def __init__(self):
        """Initialize the Vagrant service."""
        self.vagrant_path = os.getenv("VAGRANT_PATH", "/usr/bin/vagrant")
        self.api_key = os.getenv("VAGRANT_API_KEY")
        self.workspace_dir = os.getenv("VAGRANT_WORKSPACE_DIR", "/tmp/vagrant")
        
        # Ensure workspace directory exists
        os.makedirs(self.workspace_dir, exist_ok=True)
        
        if not self.api_key:
            logger.warning("VAGRANT_API_KEY environment variable not set")
        
        logger.info("VagrantService initialized with workspace: %s", self.workspace_dir)
    
    def _get_vm_path(self, vm_id: str) -> str:
        """Get the path to the VM's directory."""
        return os.path.join(self.workspace_dir, vm_id)
    
    def _execute_vagrant_command(self, args: List[str], cwd: Optional[str] = None) -> Tuple[bool, str, str, int]:
        """
        Execute a Vagrant command.
        
        Args:
            args: The Vagrant command arguments.
            cwd: The current working directory for the command.
            
        Returns:
            Tuple containing (success, stdout, stderr, exit_code).
        """
        try:
            cmd = [self.vagrant_path] + args
            logger.debug("Executing vagrant command: %s (in %s)", cmd, cwd or "current dir")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=cwd
            )
            
            stdout, stderr = process.communicate()
            success = process.returncode == 0
            
            if not success:
                logger.error("Vagrant command failed: %s, exit code: %d, stderr: %s", 
                             cmd, process.returncode, stderr)
            
            return success, stdout, stderr, process.returncode
        except Exception as e:
            logger.exception("Error executing Vagrant command: %s", str(e))
            return False, "", str(e), -1
    
    async def get_status(self, vm_id: str) -> Dict[str, Any]:
        """
        Get the status of a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            
        Returns:
            Dict with the status information.
            
        Raises:
            NotFoundError: If the VM is not found.
            ServiceError: If there is an error getting the status.
        """
        vm_path = self._get_vm_path(vm_id)
        
        if not os.path.exists(vm_path):
            raise NotFoundError(f"VM with ID {vm_id} not found")
        
        success, stdout, stderr, exit_code = self._execute_vagrant_command(['status', '--machine-readable'], cwd=vm_path)
        
        if not success:
            raise ServiceError(f"Failed to get VM status: {stderr}")
        
        # Parse machine-readable output to get state
        state = "unknown"
        for line in stdout.splitlines():
            parts = line.split(',')
            if len(parts) >= 4 and parts[2] == "state":
                state = parts[3]
                break
        
        return {
            "vm_id": vm_id,
            "state": state,
            "path": vm_path
        }
    
    async def up(self, vm_id: str, provision: bool = True) -> Dict[str, Any]:
        """
        Start a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            provision: Whether to provision the VM.
            
        Returns:
            Dict with the command result.
            
        Raises:
            NotFoundError: If the VM is not found.
            ServiceError: If there is an error starting the VM.
        """
        vm_path = self._get_vm_path(vm_id)
        
        if not os.path.exists(vm_path):
            raise NotFoundError(f"VM with ID {vm_id} not found")
        
        cmd = ['up']
        if not provision:
            cmd.append('--no-provision')
        
        success, stdout, stderr, exit_code = self._execute_vagrant_command(cmd, cwd=vm_path)
        
        if not success:
            raise ServiceError(f"Failed to start VM: {stderr}")
        
        return {
            "vm_id": vm_id,
            "success": success,
            "message": stdout
        }
    
    async def halt(self, vm_id: str, force: bool = False) -> Dict[str, Any]:
        """
        Stop a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            force: Whether to force the halt.
            
        Returns:
            Dict with the command result.
            
        Raises:
            NotFoundError: If the VM is not found.
            ServiceError: If there is an error stopping the VM.
        """
        vm_path = self._get_vm_path(vm_id)
        
        if not os.path.exists(vm_path):
            raise NotFoundError(f"VM with ID {vm_id} not found")
        
        cmd = ['halt']
        if force:
            cmd.append('--force')
        
        success, stdout, stderr, exit_code = self._execute_vagrant_command(cmd, cwd=vm_path)
        
        if not success:
            raise ServiceError(f"Failed to stop VM: {stderr}")
        
        return {
            "vm_id": vm_id,
            "success": success,
            "message": stdout
        }
    
    async def destroy(self, vm_id: str, force: bool = False) -> Dict[str, Any]:
        """
        Destroy a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            force: Whether to force the destruction.
            
        Returns:
            Dict with the command result.
            
        Raises:
            NotFoundError: If the VM is not found.
            ServiceError: If there is an error destroying the VM.
        """
        vm_path = self._get_vm_path(vm_id)
        
        if not os.path.exists(vm_path):
            raise NotFoundError(f"VM with ID {vm_id} not found")
        
        cmd = ['destroy']
        if force:
            cmd.append('--force')
        
        success, stdout, stderr, exit_code = self._execute_vagrant_command(cmd, cwd=vm_path)
        
        if not success:
            raise ServiceError(f"Failed to destroy VM: {stderr}")
        
        return {
            "vm_id": vm_id,
            "success": success,
            "message": stdout
        }
    
    async def list_boxes(self) -> List[Dict[str, str]]:
        """
        List available Vagrant boxes.
        
        Returns:
            List of box information dictionaries.
            
        Raises:
            ServiceError: If there is an error listing the boxes.
        """
        success, stdout, stderr, exit_code = self._execute_vagrant_command(['box', 'list', '--json'])
        
        if not success:
            raise ServiceError(f"Failed to list boxes: {stderr}")
        
        try:
            boxes = json.loads(stdout)
            result = []
            
            for box in boxes:
                result.append({
                    "name": box.get("name", ""),
                    "provider": box.get("provider", ""),
                    "version": box.get("version", "")
                })
            
            return result
        except json.JSONDecodeError:
            raise ServiceError("Failed to parse box list output")
    
    async def get_machine_info(self, vm_id: str) -> Dict[str, Any]:
        """
        Get detailed information about a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            
        Returns:
            Dict with the VM information.
            
        Raises:
            NotFoundError: If the VM is not found.
            ServiceError: If there is an error getting the information.
        """
        vm_path = self._get_vm_path(vm_id)
        
        if not os.path.exists(vm_path):
            raise NotFoundError(f"VM with ID {vm_id} not found")
        
        # Get status
        status_data = await self.get_status(vm_id)
        
        # Get SSH config
        success, stdout, stderr, exit_code = self._execute_vagrant_command(['ssh-config'], cwd=vm_path)
        
        if not success:
            raise ServiceError(f"Failed to get SSH config: {stderr}")
        
        ssh_config = {}
        for line in stdout.splitlines():
            line = line.strip()
            if line and "Host " not in line:
                try:
                    key, value = line.split(" ", 1)
                    ssh_config[key.strip()] = value.strip()
                except ValueError:
                    # Skip lines that don't split into key-value pairs
                    pass
        
        # Combine data
        return {
            "vm_id": vm_id,
            "state": status_data.get("state", "unknown"),
            "path": vm_path,
            "ssh_config": ssh_config
        }
    
    async def execute_command(self, vm_id: str, command: str, sudo: bool = False) -> Dict[str, Any]:
        """
        Execute a command on a Vagrant VM.
        
        Args:
            vm_id: The ID of the VM.
            command: The command to execute.
            sudo: Whether to execute the command with sudo.
            
        Returns:
            Dict with the command execution result.
            
        Raises:
            NotFoundError: If the VM is not found.
            ServiceError: If there is an error executing the command.
        """
        vm_path = self._get_vm_path(vm_id)
        
        if not os.path.exists(vm_path):
            raise NotFoundError(f"VM with ID {vm_id} not found")
        
        if sudo:
            command = f"sudo {command}"
        
        success, stdout, stderr, exit_code = self._execute_vagrant_command(['ssh', '-c', command], cwd=vm_path)
        
        return {
            "vm_id": vm_id,
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
            "exit_code": exit_code
        }
