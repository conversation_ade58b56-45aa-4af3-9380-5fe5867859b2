"""
API endpoints for Vagrant service.
"""
import logging
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

from api.services.vagrant_client import VagrantClient
from api.middleware.auth_middleware import get_current_user
from api.schemas.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/vagrant", tags=["vagrant"])

# Request/Response models
class ConnectionStatusResponse(BaseModel):
    """Response model for connection status."""
    connected: bool = Field(..., description="Whether the connection is active")
    message: Optional[str] = Field(None, description="Status message")
    error: Optional[str] = Field(None, description="Error message if disconnected")

class RemoteStatusResponse(BaseModel):
    """Response model for remote server status."""
    success: bool = Field(..., description="Whether the status check was successful")
    vm_status: Optional[Dict[str, Any]] = Field(None, description="VM status information")
    system_info: Optional[Dict[str, Any]] = Field(None, description="System information")
    error: Optional[str] = Field(None, description="Error message if status check failed")
    stdout: str = Field("", description="Standard output")
    stderr: str = Field("", description="Standard error")
    exit_code: int = Field(0, description="Exit code")


@router.get("/connection/status", response_model=ConnectionStatusResponse)
async def get_connection_status():
    """
    Get the current connection status to the Vagrant service.

    Returns:
        ConnectionStatusResponse: Object containing connection status information
    """
    logger.info("Checking Vagrant connection status")
    try:
        client = VagrantClient()
        connected = await client.connect()


        if connected:
            return {
                "connected": True,
                "message": "Successfully connected to Vagrant service"
            }
        else:
            return {
                "connected": False,
                "error": "Failed to connect to Vagrant service"
            }
    except Exception as e:
        logger.error(f"Error checking Vagrant connection status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking connection: {str(e)}"
        )
    finally:
        # Ensure client connection is closed
        if 'client' in locals():
            await client.close()

@router.post("/connection/check", response_model=ConnectionStatusResponse)
async def check_connection():
    """
    Force a new connection check to the Vagrant service.

    Returns:
        ConnectionStatusResponse: Object containing connection status information
    """
    logger.info("Forcing Vagrant connection check")
    try:
        client = VagrantClient()
        connected = await client.connect()

        if connected:
            return {
                "connected": True,
                "message": "Successfully connected to Vagrant service"
            }
        else:
            return {
                "connected": False,
                "error": "Failed to connect to Vagrant service"
            }
    except Exception as e:
        logger.error(f"Error checking Vagrant connection: {str(e)}")
        return {
            "connected": False,
            "error": f"Error checking connection: {str(e)}"
        }
    finally:
        # Ensure client connection is closed
        if 'client' in locals():
            await client.close()

@router.get("/remote/{hostname}/status", response_model=RemoteStatusResponse)
async def get_remote_status(hostname: str):
    """
    Get the status of a remote Vagrant server.

    Args:
        hostname: The hostname or IP of the remote server.

    Returns:
        RemoteStatusResponse: Object containing remote status information
    """
    logger.info(f"Checking remote status for {hostname}")
    try:
        client = VagrantClient()
        connected = await client.connect()

        if not connected:
            return {
                "success": False,
                "error": "Failed to connect to Vagrant service"
            }

        result = await client.remote_status(hostname)

        # Ensure we have the expected structure
        result["success"] = result.get("success", False)
        if "vm_status" not in result:
            result["vm_status"] = {}
        if "system_info" not in result:
            result["system_info"] = {}

        return result
    except Exception as e:
        logger.error(f"Error checking remote status: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "stdout": "",
            "stderr": f"Error: {str(e)}",
            "exit_code": 1
        }
    finally:
        # Ensure client connection is closed
        if 'client' in locals():
            await client.close()

# Request/Response models
class VagrantVM(BaseModel):
    """Vagrant VM information model."""
    vm_id: str = Field(..., description="VM identifier")

class VagrantVMInfo(BaseModel):
    """Vagrant VM status information."""
    vm_id: str = Field(..., description="VM identifier")
    state: str = Field(..., description="VM state")
    name: Optional[str] = Field(None, description="VM name")
    provider: Optional[str] = Field(None, description="Provider name")
    directory: Optional[str] = Field(None, description="VM directory")
    network: Optional[Dict[str, str]] = Field(None, description="Network configuration")

class VagrantVMCommandOptions(BaseModel):
    """Options for Vagrant VM commands."""
    force: Optional[bool] = Field(False, description="Force the operation")
    provision: Optional[bool] = Field(True, description="Run provisioners")

class VagrantVMCommandResponse(BaseModel):
    """Response model for VM commands."""
    vm_id: str = Field(..., description="VM identifier")
    success: bool = Field(..., description="Whether the operation was successful")
    message: Optional[str] = Field(None, description="Success message")
    error: Optional[str] = Field(None, description="Error message if operation failed")

class VagrantVMCommandResponse(BaseModel):
    """Response model for VM commands."""
    vm_id: str = Field(..., description="VM identifier")
    success: bool = Field(..., description="Whether the operation was successful")
    message: Optional[str] = Field(None, description="Success message")
    error: Optional[str] = Field(None, description="Error message if operation failed")

class VagrantCommandResult(BaseModel):
    """Result of a Vagrant command."""
    success: bool = Field(..., description="Success status")
    message: str = Field("", description="Command output message")
    error: Optional[str] = Field(None, description="Error message if any")

class VagrantBoxInfo(BaseModel):
    """Vagrant box information."""
    name: str = Field(..., description="Box name")
    provider: str = Field(..., description="Provider name")
    version: str = Field(..., description="Box version")

class VagrantExecuteCommandResult(BaseModel):
    """Result of command execution."""


# Helper function to get a Vagrant client
async def get_vagrant_client() -> VagrantClient:
    """Get a Vagrant client instance."""
    client = VagrantClient()
    await client.connect()
    return client

@router.get("/status/{vm_id}", response_model=VagrantVMInfo)
async def get_vm_status(
    vm_id: str,
    client: VagrantClient = Depends(get_vagrant_client),
    current_user: User = Depends(get_current_user)
):
    """
    Get the status of a Vagrant VM.

    Args:
        vm_id: The ID of the VM.

    Returns:
        VM status information.
    """
    result = await client.status(vm_id)

    if "error" in result:
        if "not found" in result["error"].lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM with ID {vm_id} not found"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting VM status: {result['error']}"
            )

    return VagrantVMInfo(
        vm_id=vm_id,
        state=result.get("status", "unknown")
    )

@router.get("/remote-status/{vm_id}", response_model=Dict[str, Any])
async def get_remote_status(
    vm_id: str, 
    user: User = Depends(get_current_user)
):
    """
    Get detailed remote status of a Vagrant VM.

    Args:
        vm_id: The ID of the VM.

    Returns:
        Remote status information including system metrics.
    """
    logger.info(f"Getting remote status for VM {vm_id}")
    client = VagrantClient()
    await client.connect()

    try:
        result = await client.remote_status(vm_id)

        if "error" in result:
            logger.error(f"Error getting remote status for VM {vm_id}: {result['error']}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get remote status: {result['error']}"
            )

        logger.info(f"Successfully retrieved remote status for VM {vm_id}")
        return result
    except Exception as e:
        logger.error(f"Error in remote status route for VM {vm_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Error getting remote status: {str(e)}"
        )

@router.post("/up/{vm_id}", response_model=VagrantCommandResult)
async def start_vm(
    vm_id: str,
    options: Optional[VagrantVMCommandOptions] = None,
    user: User = Depends(get_current_user)
):
    """
    Start a Vagrant VM.

    Args:
        vm_id: The ID of the VM.
        options: Command options.

    Returns:
        Result of the command execution.
    """
    if options is None:
        options = VagrantVMCommandOptions()

    logger.info(f"Starting VM {vm_id} with options: {options.dict()}")
    client = VagrantClient()
    await client.connect()

    try:
        result = await client.up(
            vm_id, 
            provision=options.provision
        )

        if not result.get("success", False):
            logger.warning(f"Failed to start VM {vm_id}: {result.get('error', 'Unknown error')}")
            return VagrantCommandResult(
                success=False,
                message="",
                error=result.get("error", "Unknown error")
            )

        logger.info(f"Successfully started VM {vm_id}")
        return VagrantCommandResult(
            success=True,
            message=result.get("message", "VM started successfully"),
            error=None
        )
    except Exception as e:
        logger.error(f"Error starting VM {vm_id}: {str(e)}")
        return VagrantCommandResult(
            success=False,
            message="",
            error=str(e)
        )

@router.post("/halt/{vm_id}", response_model=VagrantCommandResult)
async def stop_vm(
    vm_id: str,
    options: Optional[VagrantVMCommandOptions] = None,
    user: User = Depends(get_current_user)
):
    """
    Stop a Vagrant VM.

    Args:
        vm_id: The ID of the VM.
        options: Command options.

    Returns:
        Result of the command execution.
    """
    if options is None:
        options = VagrantVMCommandOptions()

    logger.info(f"Stopping VM {vm_id} with options: {options.dict()}")
    client = VagrantClient()
    await client.connect()

    try:
        result = await client.halt(vm_id, force=options.force)

        if not result.get("success", False):
            logger.warning(f"Failed to stop VM {vm_id}: {result.get('error', 'Unknown error')}")
            return VagrantCommandResult(
                success=False,
                message="",
                error=result.get("error", "Unknown error")
            )

        logger.info(f"Successfully stopped VM {vm_id}")
        return VagrantCommandResult(
            success=True,
            message=result.get("message", "VM stopped successfully"),
            error=None
        )
    except Exception as e:
        logger.error(f"Error stopping VM {vm_id}: {str(e)}")
        return VagrantCommandResult(
            success=False,
            message="",
            error=str(e)
        )

@router.post("/execute/{vm_id}", response_model=VagrantExecuteCommandResult)
async def execute_command(
    vm_id: str,
    command: str = Query(..., description="Command to execute"),
    sudo: bool = Query(False, description="Run with sudo"),
    user: User = Depends(get_current_user)
):
    """
    Execute a command on a Vagrant VM.

    Args:
        vm_id: The ID of the VM.
        command: The command to execute.
        sudo: Whether to run the command with sudo.

    Returns:
        Result of the command execution.
    """
    logger.info(f"Executing command on VM {vm_id}: {command} (sudo: {sudo})")
    client = VagrantClient()
    await client.connect()

    try:
        result = await client.execute_command(vm_id, command, sudo=sudo)

        return VagrantExecuteCommandResult(
            success=result.get("success", False),
            stdout=result.get("stdout", ""),
            stderr=result.get("stderr", ""),
            exit_code=result.get("exit_code", 1)
        )
    except Exception as e:
        logger.error(f"Error executing command on VM {vm_id}: {str(e)}")
        return VagrantExecuteCommandResult(
            success=False,
            stdout="",
            stderr=str(e),
            exit_code=1
        )

@router.post("/destroy/{vm_id}", response_model=VagrantCommandResult)
async def destroy_vm(
    vm_id: str,
    options: Optional[VagrantVMCommandOptions] = None,
    client: VagrantClient = Depends(get_vagrant_client),
    current_user: User = Depends(get_current_user)
):
    """
    Destroy a Vagrant VM.

    Args:
        vm_id: The ID of the VM.
        options: Command options.

    Returns:
        Command result.
    """
    if options is None:
        options = VagrantVMCommandOptions()

    result = await client.destroy(vm_id, options.force)

    if "error" in result:
        if "not found" in result["error"].lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM with ID {vm_id} not found"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error destroying VM: {result['error']}"
            )

    return VagrantCommandResult(
        success=result.get("success", False),
        message=result.get("message", "")
    )

@router.get("/boxes", response_model=List[VagrantBoxInfo])
async def list_boxes(
    client: VagrantClient = Depends(get_vagrant_client),
    current_user: User = Depends(get_current_user)
):
    """
    List available Vagrant boxes.

    Returns:
        List of box information.
    """
    result = await client.list_boxes()

    if result and "error" in result[0]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing boxes: {result[0]['error']}"
        )

    return result

@router.get("/info/{vm_id}", response_model=VagrantVMInfo)
async def get_vm_info(
    vm_id: str,
    client: VagrantClient = Depends(get_vagrant_client),
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed information about a Vagrant VM.

    Args:
        vm_id: The ID of the VM.

    Returns:
        Detailed VM information.
    """
    result = await client.get_machine_info(vm_id)

    if "error" in result:
        if "not found" in result["error"].lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM with ID {vm_id} not found"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting VM info: {result['error']}"
            )

    return VagrantVMInfo(
        vm_id=vm_id,
        name=result.get("name"),
        provider=result.get("provider"),
        state=result.get("state", "unknown"),
        directory=result.get("directory"),
        network=result.get("network")
    )
@router.post("/vms/{vm_id}/suspend", response_model=VagrantVMCommandResponse)
async def suspend_vm(
    vm_id: str,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Suspend a Vagrant VM.

    Args:
        vm_id: The VM identifier
        current_user: The authenticated user

    Returns:
        Operation result
    """
    try:
        logger.info("Suspending VM %s requested by user %s", vm_id, current_user.username)

        client = VagrantClient()
        await client.connect()

        result = await client.suspend(vm_id)
        await client.close()

        if "error" in result:
            logger.error("Error suspending VM %s: %s", vm_id, result["error"])
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to suspend VM: {result['error']}"
            )

        logger.info("VM %s suspended successfully", vm_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error suspending VM %s: %s", vm_id, str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to suspend VM: {str(e)}"
        )