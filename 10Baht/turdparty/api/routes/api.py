from fastapi import APIRouter

from api.routes.items import router as items_router
from api.routes.users import router as users_router
from api.core.config import settings # Assuming this import is available

api_router = APIRouter()

api_router.include_router(items_router)
api_router.include_router(users_router)

# Include test auth routes in development
if settings.DEBUG:
    try:
        from api.routes.test_auth import router as test_auth_router
        api_router.include_router(test_auth_router)
        logger.info("Test auth routes enabled in DEBUG mode")
    except ImportError as e:
        logger.error(f"Failed to import test_auth router: {str(e)}")