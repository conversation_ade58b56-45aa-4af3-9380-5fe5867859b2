"""
API endpoints for VM injection feature
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from typing import List, Optional
from sqlalchemy.orm import Session
from api.models.vm_injection import VMInjectionSchema, VMInjectionCreateSchema, VMInjectionUpdateSchema, VMInjectionListSchema, VMInjectionStatusSchema
from api.db.session import get_db
from api.services.vm_injection import VMInjectionService
from api.middleware.auth_middleware import get_current_user
from api.models.user import UserSchema
import uuid

router = APIRouter(
    prefix="/vm_injection",
    tags=["vm_injection"],
    responses={404: {"description": "VM injection not found"}},
)

@router.get("/", response_model=VMInjectionListSchema)
async def get_all_vm_injections(
    skip: int = 0, 
    limit: int = 100,
    vagrant_vm_id: Optional[uuid.UUID] = None,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get all VM injections for the current user, optionally filtered by VM ID
    """
    service = VMInjectionService(db)
    injections, total = service.get_all(skip=skip, limit=limit, user_id=current_user.id, vagrant_vm_id=vagrant_vm_id)
    return {"items": injections, "total": total}

@router.get("/{vm_injection_id}", response_model=VMInjectionSchema)
async def get_vm_injection(
    vm_injection_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get a specific VM injection by ID
    """
    service = VMInjectionService(db)
    vm_injection_obj = service.get_by_id(vm_injection_id, user_id=current_user.id)
    if not vm_injection_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"VM injection with id {vm_injection_id} not found"
        )
    return vm_injection_obj

@router.post("/", response_model=VMInjectionSchema, status_code=status.HTTP_201_CREATED)
async def create_vm_injection(
    background_tasks: BackgroundTasks,
    vm_injection: VMInjectionCreateSchema,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Create a new VM injection (inject a file into a VM)
    """
    service = VMInjectionService(db)
    return await service.create(vm_injection, background_tasks, user_id=current_user.id)

@router.get("/{vm_injection_id}/status", response_model=VMInjectionStatusSchema)
async def get_injection_status(
    vm_injection_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get the status of a VM injection
    """
    service = VMInjectionService(db)
    return service.get_status(vm_injection_id, user_id=current_user.id)

@router.put("/{vm_injection_id}", response_model=VMInjectionSchema)
async def update_vm_injection(
    vm_injection_id: uuid.UUID,
    vm_injection: VMInjectionUpdateSchema,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Update a VM injection (metadata only)
    """
    service = VMInjectionService(db)
    return service.update(vm_injection_id, vm_injection, user_id=current_user.id)

@router.post("/{vm_injection_id}/retry", response_model=VMInjectionStatusSchema)
async def retry_injection(
    vm_injection_id: uuid.UUID,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Retry a failed VM injection
    """
    service = VMInjectionService(db)
    return await service.retry(vm_injection_id, background_tasks, user_id=current_user.id)

@router.delete("/{vm_injection_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_vm_injection(
    vm_injection_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Delete a VM injection record
    """
    service = VMInjectionService(db)
    service.delete(vm_injection_id, user_id=current_user.id)
    return None
