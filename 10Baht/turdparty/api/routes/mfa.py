
"""
MFA API endpoints.
"""
import logging
import time
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any

from api.core.security import get_current_user
from api.services.mfa_service import MFAService
from api.schemas.mfa import (
    MFASetupResponse,
    MFAVerifyRequest,
    MFAVerifyResponse,
    MFAStatusResponse
)
from api.schemas.user import User

router = APIRouter(prefix="/mfa", tags=["mfa"])
logger = logging.getLogger(__name__)


@router.get("/setup", response_model=MFASetupResponse)
async def setup_mfa(
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Set up MFA for the current user.
    
    Returns:
        MFA setup information including QR code
    """
    try:
        mfa_service = MFAService()
        result = mfa_service.generate_secret(current_user.id)
        return result
    except Exception as e:
        logger.error(f"Error setting up MFA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error setting up MFA: {str(e)}"
        )


@router.post("/verify", response_model=MFAVerifyResponse)
async def verify_mfa(
    verify_request: MFAVerifyRequest,
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Verify MFA token.
    
    Args:
        verify_request: MFA verification request
        
    Returns:
        Verification result
    """
    try:
        mfa_service = MFAService()
        result = mfa_service.verify_token(current_user.id, verify_request.token)
        
        # If successful verification, store the timestamp in the session
        if result.get("success"):
            # Note: In a real implementation, you would store this in a session or JWT
            # For now, we'll just return a timestamp that could be stored client-side
            result["mfa_validated_at"] = time.time()
            
        return result
    except Exception as e:
        logger.error(f"Error verifying MFA token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error verifying MFA token: {str(e)}"
        )


@router.get("/status", response_model=MFAStatusResponse)
async def mfa_status(
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Get MFA status for the current user.
    
    Returns:
        MFA status
    """
    try:
        mfa_service = MFAService()
        is_enabled = mfa_service.is_mfa_required(current_user.id)
        
        return {
            "enabled": is_enabled,
            "message": "MFA is enabled" if is_enabled else "MFA is not enabled"
        }
    except Exception as e:
        logger.error(f"Error getting MFA status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting MFA status: {str(e)}"
        )


@router.post("/disable", response_model=MFAVerifyResponse)
async def disable_mfa(
    verify_request: MFAVerifyRequest,
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Disable MFA for the current user.
    
    Args:
        verify_request: MFA verification request to confirm disabling
        
    Returns:
        Operation result
    """
    try:
        mfa_service = MFAService()
        
        # Verify the token first before disabling
        verify_result = mfa_service.verify_token(current_user.id, verify_request.token)
        if not verify_result.get("success"):
            return {
                "success": False,
                "error": "Invalid verification token"
            }
        
        # If verification succeeded, disable MFA
        result = mfa_service.disable_mfa(current_user.id)
        return result
    except Exception as e:
        logger.error(f"Error disabling MFA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error disabling MFA: {str(e)}"
        )
