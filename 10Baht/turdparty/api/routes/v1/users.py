
"""API endpoints for users."""
import logging
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from api.core.errors import ResourceNotFoundException, ValidationException
from api.db.dependencies import get_db_session
from api.db.repositories.user import UserRepository
from api.models.user import User
from api.schemas.user import (
    UserCreate,
    UserResponse,
    UserUpdate,
    UsersResponse
)
from api.services.base import handle_exceptions
from api.core.security import get_current_user, get_current_active_superuser

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new user",
    description="Create a new user (admin only)",
)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_superuser),
) -> Any:
    """
    Create a new user (admin only).
    
    Args:
        user_data: The user data to create
        db: Database session dependency
        current_user: Current authenticated admin user
        
    Returns:
        The newly created user
        
    Raises:
        HTTPException: If there's an error creating the user
    """
    try:
        logger.info(f"Admin {current_user.id} creating new user")
        repository = UserRepository(db)
        
        # Check if email is already registered
        existing_user = await repository.get_by_email(email=user_data.email)
        if existing_user:
            raise ValidationException(f"User with this email already exists: {user_data.email}")
            
        user = await repository.create(obj_in=user_data)
        return user
    except ValidationException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        return await handle_exceptions(e, "Error creating user")


@router.get(
    "",
    response_model=UsersResponse,
    status_code=status.HTTP_200_OK,
    summary="Get all users",
    description="Get all users (admin only)",
)
async def get_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=100, description="Max number of users to return"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_superuser),
) -> Any:
    """
    Get all users (admin only).
    
    Args:
        skip: Number of users to skip for pagination
        limit: Maximum number of users to return
        db: Database session dependency
        current_user: Current authenticated admin user
        
    Returns:
        List of users
        
    Raises:
        HTTPException: If there's an error retrieving users
    """
    try:
        logger.info(f"Admin {current_user.id} retrieving users list")
        repository = UserRepository(db)
        users = await repository.get_multi(skip=skip, limit=limit)
        total = await repository.count()
        return {
            "users": users,
            "total": total,
            "skip": skip,
            "limit": limit
        }
    except Exception as e:
        return await handle_exceptions(e, "Error retrieving users")


@router.get(
    "/me",
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
    summary="Get current user",
    description="Get details of currently authenticated user",
)
async def get_current_user_details(
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get current user details.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user details
    """
    logger.info(f"User {current_user.id} retrieving own details")
    return current_user


@router.put(
    "/me",
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
    summary="Update current user",
    description="Update details of currently authenticated user",
)
async def update_current_user(
    user_data: UserUpdate,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Update current user details.
    
    Args:
        user_data: Updated user data
        db: Database session dependency
        current_user: Current authenticated user
        
    Returns:
        Updated user details
        
    Raises:
        HTTPException: If there's an error updating the user
    """
    try:
        logger.info(f"User {current_user.id} updating own details")
        repository = UserRepository(db)
        
        # If email is changing, check that it's not already taken
        if user_data.email and user_data.email != current_user.email:
            existing_user = await repository.get_by_email(email=user_data.email)
            if existing_user:
                raise ValidationException(f"User with this email already exists: {user_data.email}")
                
        updated_user = await repository.update(db_obj=current_user, obj_in=user_data)
        return updated_user
    except ValidationException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        return await handle_exceptions(e, "Error updating user details")


@router.get(
    "/{user_id}",
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
    summary="Get a user by ID",
    description="Get details of a specific user by ID (admin only)",
)
async def get_user(
    user_id: int = Path(..., description="The ID of the user to get"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_superuser),
) -> Any:
    """
    Get a specific user by ID (admin only).
    
    Args:
        user_id: ID of the user to retrieve
        db: Database session dependency
        current_user: Current authenticated admin user
        
    Returns:
        The requested user
        
    Raises:
        HTTPException: If user doesn't exist
    """
    try:
        logger.info(f"Admin {current_user.id} retrieving user {user_id}")
        repository = UserRepository(db)
        user = await repository.get(id=user_id)
        
        if not user:
            raise ResourceNotFoundException(f"User with ID {user_id} not found")
            
        return user
    except ResourceNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        return await handle_exceptions(e, f"Error retrieving user {user_id}")


@router.put(
    "/{user_id}",
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
    summary="Update a user",
    description="Update a specific user by ID (admin only)",
)
async def update_user(
    user_data: UserUpdate,
    user_id: int = Path(..., description="The ID of the user to update"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_superuser),
) -> Any:
    """
    Update a specific user (admin only).
    
    Args:
        user_data: Updated user data
        user_id: ID of the user to update
        db: Database session dependency
        current_user: Current authenticated admin user
        
    Returns:
        The updated user
        
    Raises:
        HTTPException: If user doesn't exist
    """
    try:
        logger.info(f"Admin {current_user.id} updating user {user_id}")
        repository = UserRepository(db)
        user = await repository.get(id=user_id)
        
        if not user:
            raise ResourceNotFoundException(f"User with ID {user_id} not found")
            
        # If email is changing, check that it's not already taken
        if user_data.email and user_data.email != user.email:
            existing_user = await repository.get_by_email(email=user_data.email)
            if existing_user and existing_user.id != user_id:
                raise ValidationException(f"User with this email already exists: {user_data.email}")
                
        updated_user = await repository.update(db_obj=user, obj_in=user_data)
        return updated_user
    except ResourceNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        return await handle_exceptions(e, f"Error updating user {user_id}")


@router.delete(
    "/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a user",
    description="Delete a specific user by ID (admin only)",
)
async def delete_user(
    user_id: int = Path(..., description="The ID of the user to delete"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_active_superuser),
) -> Any:
    """
    Delete a specific user (admin only).
    
    Args:
        user_id: ID of the user to delete
        db: Database session dependency
        current_user: Current authenticated admin user
        
    Returns:
        Empty response with 204 status code
        
    Raises:
        HTTPException: If user doesn't exist or is the current user
    """
    try:
        logger.info(f"Admin {current_user.id} deleting user {user_id}")
        
        # Prevent deleting yourself
        if user_id == current_user.id:
            raise ValidationException("You cannot delete your own user account")
            
        repository = UserRepository(db)
        user = await repository.get(id=user_id)
        
        if not user:
            raise ResourceNotFoundException(f"User with ID {user_id} not found")
            
        await repository.delete(id=user_id)
        return None
    except ResourceNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        return await handle_exceptions(e, f"Error deleting user {user_id}")
