"""API endpoints for items."""
import logging
from typing import List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from api.core.errors import ResourceNotFoundException
from api.db.dependencies import get_db_session
from api.db.repositories.item import ItemRepository
from api.models.user import User
from api.schemas.item import (
    ItemCreate,
    ItemResponse,
    ItemUpdate,
    ItemsResponse
)
from api.services.base import handle_exceptions
from api.core.security import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "",
    response_model=ItemResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new item",
    description="Create a new item in the database",
)
async def create_item(
    item_data: ItemCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Create a new item.

    Args:
        item_data: The item data to create
        db: Database session dependency
        current_user: Current authenticated user

    Returns:
        The newly created item

    Raises:
        HTTPException: If there's an error creating the item
    """
    try:
        logger.info(f"Creating new item for user {current_user.id}")
        repository = ItemRepository(db)
        item = await repository.create(
            obj_in=item_data, 
            owner_id=current_user.id
        )
        return item
    except Exception as e:
        return await handle_exceptions(e, "Error creating item")


@router.get(
    "",
    response_model=ItemsResponse,
    status_code=status.HTTP_200_OK,
    summary="Get all items",
    description="Get all items with optional filtering",
)
async def get_items(
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(100, ge=1, le=100, description="Max number of items to return"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get all items.

    Args:
        skip: Number of items to skip for pagination
        limit: Maximum number of items to return
        db: Database session dependency
        current_user: Current authenticated user

    Returns:
        List of items

    Raises:
        HTTPException: If there's an error retrieving items
    """
    try:
        logger.info(f"Retrieving items for user {current_user.id}")
        repository = ItemRepository(db)
        items = await repository.get_multi(
            skip=skip, 
            limit=limit, 
            owner_id=current_user.id
        )
        total = await repository.count(owner_id=current_user.id)
        return {
            "items": items,
            "total": total,
            "skip": skip,
            "limit": limit
        }
    except Exception as e:
        return await handle_exceptions(e, "Error retrieving items")


@router.get(
    "/{item_id}",
    response_model=ItemResponse,
    status_code=status.HTTP_200_OK,
    summary="Get an item by ID",
    description="Get details of a specific item by its ID",
)
async def get_item(
    item_id: int = Path(..., description="The ID of the item to get"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get a specific item by ID.

    Args:
        item_id: ID of the item to retrieve
        db: Database session dependency
        current_user: Current authenticated user

    Returns:
        The requested item

    Raises:
        HTTPException: If item doesn't exist or user doesn't have access
    """
    try:
        logger.info(f"Retrieving item {item_id} for user {current_user.id}")
        repository = ItemRepository(db)
        item = await repository.get(id=item_id)

        if not item:
            raise ResourceNotFoundException(f"Item with ID {item_id} not found")

        if item.owner_id != current_user.id:
            logger.warning(f"User {current_user.id} attempted to access item {item_id} owned by {item.owner_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions to access this item"
            )

        return item
    except ResourceNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        return await handle_exceptions(e, f"Error retrieving item {item_id}")


@router.put(
    "/{item_id}",
    response_model=ItemResponse,
    status_code=status.HTTP_200_OK,
    summary="Update an item",
    description="Update an existing item by its ID",
)
async def update_item(
    item_data: ItemUpdate,
    item_id: int = Path(..., description="The ID of the item to update"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Update a specific item.

    Args:
        item_data: Updated item data
        item_id: ID of the item to update
        db: Database session dependency
        current_user: Current authenticated user

    Returns:
        The updated item

    Raises:
        HTTPException: If item doesn't exist or user doesn't have access
    """
    try:
        logger.info(f"Updating item {item_id} for user {current_user.id}")
        repository = ItemRepository(db)
        item = await repository.get(id=item_id)

        if not item:
            raise ResourceNotFoundException(f"Item with ID {item_id} not found")

        if item.owner_id != current_user.id:
            logger.warning(f"User {current_user.id} attempted to update item {item_id} owned by {item.owner_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions to update this item"
            )

        updated_item = await repository.update(db_obj=item, obj_in=item_data)
        return updated_item
    except ResourceNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        return await handle_exceptions(e, f"Error updating item {item_id}")


@router.delete(
    "/{item_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete an item",
    description="Delete an existing item by its ID",
)
async def delete_item(
    item_id: int = Path(..., description="The ID of the item to delete"),
    db: AsyncSession = Depends(get_db_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Delete a specific item.

    Args:
        item_id: ID of the item to delete
        db: Database session dependency
        current_user: Current authenticated user

    Returns:
        Empty response with 204 status code

    Raises:
        HTTPException: If item doesn't exist or user doesn't have access
    """
    try:
        logger.info(f"Deleting item {item_id} for user {current_user.id}")
        repository = ItemRepository(db)
        item = await repository.get(id=item_id)

        if not item:
            raise ResourceNotFoundException(f"Item with ID {item_id} not found")

        if item.owner_id != current_user.id:
            logger.warning(f"User {current_user.id} attempted to delete item {item_id} owned by {item.owner_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions to delete this item"
            )

        await repository.delete(id=item_id)
        return None
    except ResourceNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        return await handle_exceptions(e, f"Error deleting item {item_id}")