
"""
API endpoints for Fathom remote operations.
"""
import logging
import os
from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional

from api.core.logging_config import setup_logging
from api.services.fathom_ssh_client import FathomSSHClient

# Set up logging
logger = setup_logging()

fathom_router = APIRouter()

@fathom_router.get(
    "/test/remote/{server_address}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Test Fathom remote connection",
    description="Test the connection to a Fathom remote server and return status information."
)
async def test_fathom_remote(
    server_address: str,
    port: Optional[int] = None,
    use_ssh_key: Optional[bool] = True
) -> Dict[str, Any]:
    """
    Test connection to a Fathom remote server.
    
    Args:
        server_address: The address of the remote server
        port: Optional port number (default is 22)
        use_ssh_key: Whether to use SSH key authentication
        
    Returns:
        Dict containing connection status and server information
    """
    logger.info(f"Testing Fathom remote connection to {server_address}")
    
    try:
        # Create SSH client
        ssh_client = FathomSSHClient()
        
        # Set up connection parameters
        if port:
            server_address = f"{server_address}:{port}"
        
        # Get SSH key path from environment if set
        ssh_key_path = os.getenv("SSH_KEY_PATH", None)
        
        # Connect to the server
        connection_result = await ssh_client.connect_to_server(
            server_address, 
            use_key=use_ssh_key,
            key_path=ssh_key_path
        )
        
        if not connection_result["success"]:
            logger.error(f"Failed to connect to {server_address}: {connection_result.get('error', 'Unknown error')}")
            return {
                "success": False,
                "connected": False,
                "error": connection_result.get("error", "Failed to connect to remote server"),
                "server_address": server_address
            }
        
        # Get system information if connected
        system_info = {}
        if connection_result["success"]:
            try:
                # Run basic system commands
                uname_result = await ssh_client.run_command("uname -a")
                uptime_result = await ssh_client.run_command("uptime")
                hostname_result = await ssh_client.run_command("hostname")
                
                system_info = {
                    "uname": uname_result.get("stdout", "").strip() if uname_result.get("success", False) else "Not available",
                    "uptime": uptime_result.get("stdout", "").strip() if uptime_result.get("success", False) else "Not available",
                    "hostname": hostname_result.get("stdout", "").strip() if hostname_result.get("success", False) else "Not available"
                }
                
                # Try to get memory info
                try:
                    mem_result = await ssh_client.run_command("free -m")
                    if mem_result.get("success", False):
                        system_info["memory"] = mem_result.get("stdout", "").strip()
                except Exception:
                    system_info["memory"] = "Not available"
                    
                # Try to get disk info
                try:
                    disk_result = await ssh_client.run_command("df -h | grep -v tmpfs")
                    if disk_result.get("success", False):
                        system_info["disk"] = disk_result.get("stdout", "").strip()
                except Exception:
                    system_info["disk"] = "Not available"
                
            except Exception as e:
                logger.warning(f"Error getting system information: {str(e)}")
                system_info = {"error": "Failed to collect system information"}
        
        # Close connection
        await ssh_client.close()
        
        # Return response
        return {
            "success": True,
            "connected": True,
            "server_address": server_address,
            "system_info": system_info
        }
        
    except Exception as e:
        logger.error(f"Error testing Fathom remote: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "server_address": server_address
        }
"""
Fathom remote connection endpoints.
"""
from fastapi import APIRouter, Query, Path, HTTPException, Depends, status
from typing import Dict, Any, Optional
import logging
import traceback
import os

from api.services.fathom_ssh_client import FathomSSHClient

logger = logging.getLogger(__name__)

fathom_router = APIRouter(
    tags=["fathom"]
)

@fathom_router.get("/test/remote/{hostname}")
async def test_remote_connection(
    hostname: str = Path(..., description="Remote hostname or IP"),
    port: Optional[int] = Query(None, description="Optional SSH port")
) -> Dict[str, Any]:
    """
    Test connection to a remote server via Fathom SSH.
    
    Args:
        hostname: Remote server hostname or IP
        port: Optional SSH port override
        
    Returns:
        Dict with connection test results
    """
    try:
        logger.info(f"Testing connection to remote server: {hostname}")
        
        # Set port in environment if provided
        if port:
            os.environ["SSH_PORT"] = str(port)
            logger.info(f"Using custom SSH port: {port}")
        
        # Create client
        client = FathomSSHClient()
        
        # Test results container
        results = {
            "connected": False,
            "remote_server": hostname,
            "remote_status": False,
            "vm_status": {},
            "system_info": {},
            "error": None,
            "grpc_status": {
                "connected": False,
                "server": os.getenv("VAGRANT_GRPC_SERVER", "localhost:40000")
            }
        }
        
        # Basic connection test
        logger.info("Testing basic SSH connection")
        try:
            basic_test = await client._execute_ssh_command(hostname, "echo 'Connection test'")
            success, stdout, stderr, exit_code = basic_test
            
            if success:
                results["connected"] = True
                logger.info(f"Successfully connected to {hostname}")
            else:
                logger.warning(f"Failed to connect to {hostname}: {stderr}")
                results["error"] = f"Connection failed with exit code {exit_code}: {stderr}"
                return results
        except Exception as e:
            logger.error(f"Error in basic connection test: {str(e)}")
            results["error"] = f"Connection error: {str(e)}"
            return results
        
        # Get remote status
        logger.info("Testing remote status")
        try:
            remote_result = await client.get_remote_status(hostname)
            
            if remote_result.get("success", False):
                results["remote_status"] = True
                
                # Extract system info from metrics
                metrics = remote_result.get("metrics", {})
                system_info = {
                    "uptime": metrics.get("uptime", ""),
                    "memory": _parse_memory(metrics.get("memory", "")),
                    "disk": _parse_disk(metrics.get("disk", "")),
                    "vmstat": metrics.get("vmstat", ""),
                    "load_averages": _parse_load(metrics.get("load", ""))
                }
                
                results["system_info"] = system_info
                logger.info("Remote status check successful")
            else:
                logger.warning(f"Remote status check failed: {remote_result.get('error', 'Unknown error')}")
        except Exception as e:
            logger.error(f"Error checking remote status: {str(e)}")
            # Don't fail the whole request, just log and continue
            results["system_info_error"] = str(e)
        
        # Get VM status
        logger.info("Getting VM status")
        try:
            vm_result = await client.get_vm_status(hostname)
            
            if vm_result.get("success", False):
                results["vm_status"] = vm_result.get("vms", {})
                logger.info(f"VM status retrieved: {len(results['vm_status'])} VMs found")
            else:
                logger.warning(f"VM status check failed: {vm_result.get('error', 'Unknown error')}")
        except Exception as e:
            logger.error(f"Error checking VM status: {str(e)}")
            # Don't fail the whole request, just log and continue
            results["vm_status_error"] = str(e)
        
        # Test gRPC connection if enabled
        if os.getenv("USE_VAGRANT_GRPC", "false").lower() == "true":
            logger.info("Testing gRPC connection")
            try:
                from api.services.vagrant_client import VagrantClient
                
                # Create client
                client = VagrantClient(use_grpc=True)
                
                # Connect to the server
                connected = await client.connect()
                
                if connected:
                    results["grpc_status"]["connected"] = True
                    logger.info("gRPC connection successful")
                else:
                    logger.warning("gRPC connection failed")
            except Exception as e:
                logger.error(f"Error checking gRPC connection: {str(e)}")
                results["grpc_status"]["error"] = str(e)
        
        return results
    except Exception as e:
        logger.error(f"Error testing remote connection: {str(e)}", exc_info=True)
        # Include stack trace for debugging
        tb = traceback.format_exc()
        logger.debug(f"Stack trace: {tb}")
        
        return {
            "connected": False,
            "remote_server": hostname,
            "error": str(e),
            "stack_trace": tb if os.getenv("DEBUG", "false").lower() == "true" else None
        }

# Helper functions for parsing output
def _parse_memory(memory_str: str) -> Dict[str, str]:
    """Parse memory output from 'free -m' command."""
    try:
        lines = memory_str.strip().split('\n')
        if len(lines) < 2:
            return {}
        
        # Get the memory line 
        mem_line = lines[1].split()
        if len(mem_line) < 4:
            return {}
        
        return {
            "total": f"{mem_line[1]} MB",
            "used": f"{mem_line[2]} MB",
            "free": f"{mem_line[3]} MB"
        }
    except Exception as e:
        logger.error(f"Error parsing memory output: {str(e)}")
        return {}

def _parse_disk(disk_str: str) -> Dict[str, str]:
    """Parse disk output from 'df -h' command."""
    try:
        lines = disk_str.strip().split('\n')
        if not lines:
            return {}
        
        # Skip header
        data_line = lines[1] if len(lines) > 1 else lines[0]
        parts = data_line.split()
        
        if len(parts) < 5:
            return {}
        
        return {
            "filesystem": parts[0],
            "size": parts[1],
            "used": parts[2],
            "available": parts[3],
            "use%": parts[4]
        }
    except Exception as e:
        logger.error(f"Error parsing disk output: {str(e)}")
        return {}

def _parse_load(load_str: str) -> list:
    """Parse load averages from /proc/loadavg."""
    try:
        parts = load_str.strip().split()
        return [float(parts[0]), float(parts[1]), float(parts[2])] if len(parts) >= 3 else []
    except Exception as e:
        logger.error(f"Error parsing load averages: {str(e)}")
        return []
