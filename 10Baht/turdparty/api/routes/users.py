
"""
User management routes.
"""
import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Request, status

from api.db.repositories.user import UserRepository
from api.db.session import get_async_session
from api.schemas.user import UserResponse
from api.middleware.auth_middleware import get_current_user_id

router = APIRouter(prefix="/users", tags=["users"])
logger = logging.getLogger(__name__)


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    request: Request,
    user_id: str = Depends(get_current_user_id),
    user_repo: UserRepository = Depends(lambda db=Depends(get_async_session): UserRepository(db))
):
    """
    Get current authenticated user.
    
    Args:
        request: FastAPI request object
        user_id: Current user ID from auth middleware
        user_repo: User repository
        
    Returns:
        Current user
        
    Raises:
        HTTPException: If user not found
    """
    user = await user_repo.get_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
        
    return UserResponse.model_validate(user)
"""
User routes module.
"""
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query

from api.middleware.auth_middleware import get_current_user, get_current_superuser
from api.schemas.user import User, UserCreate, UserUpdate

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_model=List[User])
async def get_users(
    skip: int = Query(0, ge=0, description="Skip the first N users"),
    limit: int = Query(100, ge=1, le=100, description="Limit the number of users returned"),
    current_user: User = Depends(get_current_superuser)
):
    """
    Get all users with pagination.
    Only accessible to superusers.
    
    Args:
        skip: Number of users to skip
        limit: Maximum number of users to return
        current_user: Current authenticated superuser
        
    Returns:
        List of users
    """
    logger.info(f"Superuser {current_user.username} retrieving users, skip={skip}, limit={limit}")
    # This is a stub - in a real implementation, you would fetch from database
    return []


@router.get("/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """
    Get information about the current authenticated user.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User information
    """
    logger.info(f"User {current_user.username} retrieving own information")
    return current_user
