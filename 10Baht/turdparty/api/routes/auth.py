"""
Authentication routes.
"""
import logging
import time
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Request, Response, status, <PERSON>ie
from fastapi.security import OAuth2PasswordRequestForm
from typing import Optional

from api.core.security import create_access_token, get_password_hash, verify_password
from api.db.session import get_db
from api.models.user import User
from api.schemas.token import Token, TokenData
from api.schemas.user import UserCreate, UserResponse
from api.services.mfa_service import MFAService

router = APIRouter(prefix="/auth", tags=["auth"])
logger = logging.getLogger(__name__)


@router.post("/login", response_model=Token)
async def login_for_access_token(
    response: Response,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db = Depends(get_db)
):
    """
    Authenticate user and provide access token.

    Args:
        response: HTTP response object for setting cookies
        form_data: Form containing username and password
        db: Database session

    Returns:
        JWT access token

    Raises:
        HTTPException: If authentication fails
    """
    user = db.query(User).filter(User.username == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = create_access_token(data={"sub": str(user.id)})

    # Check if MFA is enabled for this user
    mfa_service = MFAService(db)
    mfa_required = mfa_service.is_mfa_required(user.id)

    result = {
        "access_token": access_token, 
        "token_type": "bearer",
        "mfa_required": mfa_required
    }

    return result


@router.post("/validate-mfa", response_model=Token)
async def validate_mfa(
    response: Response,
    token: str,
    mfa_token: str,
    db = Depends(get_db)
):
    """
    Validate MFA token and complete login.

    Args:
        response: HTTP response object for setting cookies
        token: Access token from login step
        mfa_token: MFA token for verification
        db: Database session

    Returns:
        Validated JWT access token

    Raises:
        HTTPException: If MFA validation fails
    """
    from api.core.security import decode_token
    payload = decode_token(token)
    if not payload or "sub" not in payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user_id = int(payload["sub"])

    mfa_service = MFAService(db)
    verification_result = mfa_service.verify_token(user_id, mfa_token)

    if not verification_result.get("success"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid MFA token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    mfa_validated_at = time.time()
    response.set_cookie(
        key="mfa_validated_at",
        value=str(mfa_validated_at),
        httponly=True,
        max_age=12 * 60 * 60,  # 12 hours
        secure=True,
        samesite="strict"
    )

    return {
        "access_token": token, 
        "token_type": "bearer",
        "mfa_required": False,
        "mfa_validated": True
    }


@router.post("/register", response_model=UserResponse)
async def register_user(
    user_create: UserCreate,
    db = Depends(get_db)
):
    # Placeholder for existing register_user function.  Implementation not provided.
    pass

"""
Authentication routes.
"""
import logging
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from fastapi.security import OAuth2PasswordRequestForm

from api.services.auth import AuthService, get_auth_service
from api.schemas.auth import TokenResponse, LoginRequest, RegisterRequest
from api.schemas.user import UserCreate, UserResponse, UserInDB
from api.core.security import decode_token

router = APIRouter(prefix="/auth", tags=["auth"])
logger = logging.getLogger(__name__)


@router.post("/login", response_model=TokenResponse)
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Authenticate a user and return an access token.

    Args:
        request: FastAPI request object
        form_data: OAuth2 form data with username and password
        auth_service: Authentication service

    Returns:
        Token response with access token

    Raises:
        HTTPException: If authentication fails
    """
    user = await auth_service.authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return await auth_service.get_login_response(user, request)


@router.post("/login/json", response_model=TokenResponse)
async def login_json(
    request: Request,
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Authenticate a user with JSON request and return an access token.

    Args:
        request: FastAPI request object
        login_data: Login data with email and password
        auth_service: Authentication service

    Returns:
        Token response with access token

    Raises:
        HTTPException: If authentication fails
    """
    user = await auth_service.authenticate_user(login_data.email, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return await auth_service.get_login_response(user, request)


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    register_data: RegisterRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Register a new user.

    Args:
        register_data: Registration data
        auth_service: Authentication service

    Returns:
        The created user

    Raises:
        HTTPException: If registration fails
    """
    user = await auth_service.register_user(
        name=register_data.name,
        email=register_data.email,
        password=register_data.password
    )

    return UserResponse.model_validate(user)


@router.post("/logout", status_code=status.HTTP_200_OK)
async def logout(
    request: Request,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Logout a user by invalidating their session.

    Args:
        request: FastAPI request object
        auth_service: Authentication service

    Returns:
        Success message
    """
    try:
        # Get authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return {"message": "Successfully logged out"}

        # Extract token
        token = auth_header.split(" ")[1]

        # Decode token and get session
        payload = decode_token(token)
        session_token = payload.get("session")

        if session_token:
            # Invalidate session
            await auth_service.logout(session_token)

        return {"message": "Successfully logged out"}
    except Exception as e:
        logger.error(f"Error during logout: {str(e)}")
        return {"message": "Successfully logged out"}
"""Authentication routes module."""
from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from api.core.config import settings
from api.core.security import create_access_token
from api.db.session import get_db
from api.schemas.token import Token
from api.services.auth import AuthService

router = APIRouter()


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests.
    """
    auth_service = AuthService(db)
    user = await auth_service.authenticate(
        email=form_data.username,
        password=form_data.password,
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user",
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    token = create_access_token(
        subject=str(user.id), expires_delta=access_token_expires
    )

    return {"access_token": token, "token_type": "bearer"}
"""
Authentication routes.
"""
import logging
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from api.core.security import create_access_token
from api.middleware.auth_middleware import get_current_user
from api.core.security import verify_password
from api.schemas.token import Token
from api.core.config import settings
from api.services.auth import AuthService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/auth/login", response_model=Token)
async def login_access_token(
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 compatible token login.

    Args:
        form_data: OAuth2 password request form

    Returns:
        Token: Access token
    """
    # In a real application, you would authenticate against the database
    # This is a simplified example for testing purposes

    # For testing, accept any username with password "password"
    if settings.TEST_MODE and form_data.password == "password":
        logger.warning(f"Test mode login for user: {form_data.username}")
        access_token = create_access_token(
            subject=form_data.username,
            is_superuser=form_data.username.lower() == "admin"
        )
        return {"access_token": access_token, "token_type": "bearer"}

    # For production, use the auth service
    auth_service = AuthService()
    user = await auth_service.authenticate(
        username=form_data.username, 
        password=form_data.password
    )

    if not user:
        logger.warning(f"Failed login attempt for user: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = create_access_token(
        subject=user.id,
        is_superuser=user.is_superuser
    )

    logger.info(f"Successful login for user: {user.username}")
    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/auth/me")
async def read_users_me(current_user = Depends(get_current_user)):
    """
    Get current user information.

    Args:
        current_user: Current user from dependency

    Returns:
        Dict: User information
    """
    return current_user


@router.post("/auth/test-token", response_model=Token)
async def get_test_token():
    """
    Get a test token for development purposes.
    This should only be enabled in TEST_MODE.

    Returns:
        Token: A test token
    """
    if not settings.TEST_MODE:
        logger.warning("Attempt to get test token while not in test mode")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Test tokens are only available in test mode"
        )

    logger.warning("Test token created - this should only be used for development")
    
    # Use one of our known test user IDs
    access_token = create_access_token(
        subject="e3c704f3-c398-4894-bd7b-a1d092dada04"
    )

    return {"access_token": access_token, "token_type": "bearer"}