"""
MinIO health monitoring endpoint.
"""
import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, Query, HTTPException
import boto3
from botocore.exceptions import ClientError
import datetime

from api.services.minio_ssh_client import MinIOSSHClient

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/health")
async def check_minio_health(
    hostname: str = Query(..., description="Remote server hostname"),
) -> Dict[str, Any]:
    """
    Check the health of the MinIO server.

    Args:
        hostname: The hostname of the remote server

    Returns:
        Dict: Health information and status
    """
    minio_client = MinIOSSHClient()
    try:
        # Start SSH tunnel
        tunnel_success = await minio_client.start_ssh_tunnel(hostname)
        if not tunnel_success:
            return {
                "status": "error",
                "message": "Failed to establish SSH tunnel",
                "details": {
                    "hostname": hostname,
                    "ssh_status": "failed"
                }
            }

        # Test MinIO connection by listing buckets
        bucket_response = await minio_client.list_buckets()
        if not bucket_response.get("success", False):
            return {
                "status": "error",
                "message": "Connection to MinIO failed",
                "details": {
                    "hostname": hostname,
                    "ssh_status": "success",
                    "minio_status": "failed",
                    "error": bucket_response.get("error", "Unknown error")
                }
            }

        # Get additional health metrics
        metrics = await _get_minio_metrics(minio_client)

        return {
            "status": "healthy",
            "message": "MinIO server is operational",
            "details": {
                "hostname": hostname,
                "ssh_status": "success",
                "minio_status": "success",
                "buckets_count": len(bucket_response.get("buckets", [])),
                "buckets": [b.get("name") for b in bucket_response.get("buckets", [])],
                "metrics": metrics
            }
        }
    except Exception as e:
        logger.error(f"Error checking MinIO health for {hostname}: {str(e)}")
        return {
            "status": "error",
            "message": f"Error checking MinIO health: {str(e)}",
            "details": {
                "hostname": hostname,
                "error": str(e)
            }
        }
    finally:
        # Always stop the SSH tunnel
        await minio_client.stop_ssh_tunnel()


@router.get("/detailed-health")
async def get_detailed_minio_health(
    hostname: str = Query(..., description="Remote server hostname"),
) -> Dict[str, Any]:
    """
    Get detailed health information about the MinIO server.

    Args:
        hostname: The hostname of the remote server

    Returns:
        Dict: Detailed health information including storage and performance metrics
    """
    minio_client = MinIOSSHClient()
    try:
        # Start SSH tunnel
        tunnel_success = await minio_client.start_ssh_tunnel(hostname)
        if not tunnel_success:
            raise HTTPException(status_code=503, detail="Failed to establish SSH tunnel")

        # Test basic connectivity
        buckets_response = await minio_client.list_buckets()
        if not buckets_response.get("success", False):
            raise HTTPException(
                status_code=503, 
                detail=f"Connection to MinIO failed: {buckets_response.get('error', 'Unknown error')}"
            )

        # Get metrics
        metrics = await _get_minio_metrics(minio_client)

        # Get bucket details
        buckets = buckets_response.get("buckets", [])
        bucket_details = []

        for bucket in buckets:
            bucket_name = bucket.get("name")
            objects_response = await minio_client.list_objects(bucket_name)

            bucket_info = {
                "name": bucket_name,
                "creation_date": bucket.get("creation_date"),
                "object_count": len(objects_response.get("objects", [])) if objects_response.get("success", False) else "error",
                "status": "available" if objects_response.get("success", False) else "error"
            }

            bucket_details.append(bucket_info)

        return {
            "status": "healthy",
            "hostname": hostname,
            "buckets": bucket_details,
            "metrics": metrics,
            "timestamp": datetime.datetime.now().isoformat()  # Current timestamp
        }
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting detailed MinIO health for {hostname}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error checking MinIO health: {str(e)}")
    finally:
        # Always stop the SSH tunnel
        await minio_client.stop_ssh_tunnel()


async def _get_minio_metrics(minio_client: MinIOSSHClient) -> Dict[str, Any]:
    """
    Get MinIO server metrics.

    Args:
        minio_client: The MinIO SSH client

    Returns:
        Dict: Server metrics
    """
    try:
        # Use S3 client to get metrics
        s3_client = minio_client.get_s3_client()

        # We'll create a metrics dict with available information
        metrics = {
            "status": "available",
            "response_time_ms": None
        }

        # Measure response time with a simple operation
        import time
        start_time = time.time()
        s3_client.list_buckets()
        end_time = time.time()

        metrics["response_time_ms"] = round((end_time - start_time) * 1000, 2)

        return metrics
    except Exception as e:
        logger.error(f"Error getting MinIO metrics: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        }