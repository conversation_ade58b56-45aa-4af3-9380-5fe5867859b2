"""
API routes for logging
"""
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import Dict, Any, Optional
import logging
import json
from datetime import datetime
import os
from api.core.config import get_settings
from api.middleware.auth_middleware import get_current_user
from api.models.user import UserSchema

# Set up logger
logger = logging.getLogger("ui_errors")
logger.setLevel(logging.ERROR)

# Create a file handler
logs_dir = os.path.join(os.getcwd(), "logs")
os.makedirs(logs_dir, exist_ok=True)
ui_error_log_file = os.path.join(logs_dir, "ui_errors.log")
file_handler = logging.FileHandler(ui_error_log_file)
file_handler.setLevel(logging.ERROR)

# Create a formatter
formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s'
)
file_handler.setFormatter(formatter)

# Add the handler to the logger
logger.addHandler(file_handler)

router = APIRouter(
    prefix="/api/logs",
    tags=["logs"],
    responses={404: {"description": "Not found"}},
)

class UIErrorLog(BaseModel):
    """Model for UI error logs"""
    message: str
    source: str
    stack: Optional[str] = None
    componentName: Optional[str] = None
    additionalInfo: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None

@router.post("/ui-error", status_code=status.HTTP_201_CREATED)
async def log_ui_error(
    error_log: UIErrorLog,
    current_user: Optional[UserSchema] = Depends(get_current_user)
):
    """
    Log UI errors sent from the frontend
    """
    try:
        # Add user information if available
        user_info = {}
        if current_user:
            user_info = {
                "user_id": str(current_user.id),
                "username": current_user.username
            }
        
        # Format the error message
        timestamp = error_log.timestamp or datetime.now().isoformat()
        error_message = {
            "timestamp": timestamp,
            "message": error_log.message,
            "source": error_log.source,
            "stack": error_log.stack,
            "componentName": error_log.componentName,
            "additionalInfo": error_log.additionalInfo,
            "user": user_info
        }
        
        # Log the error
        logger.error(json.dumps(error_message))
        
        return {"status": "success", "message": "Error logged successfully"}
    except Exception as e:
        logger.error(f"Failed to log UI error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to log error: {str(e)}"
        ) 