
"""
MinIO status monitoring endpoints.
"""
import logging
from typing import Dict, Any, Optional, List

from fastapi import APIRouter, Query, HTTPException, Depends
import datetime
from pydantic import BaseModel, Field

from api.services.minio_ssh_client import MinIOSSHClient

logger = logging.getLogger(__name__)
router = APIRouter()


class BucketInfo(BaseModel):
    """Model for MinIO bucket information."""
    name: str = Field(..., description="Name of the bucket")
    creation_date: Optional[str] = Field(None, description="Bucket creation date")
    object_count: Optional[int] = Field(None, description="Number of objects in bucket")
    size_bytes: Optional[int] = Field(None, description="Total size in bytes")


class MinIOStatusResponse(BaseModel):
    """Response model for MinIO status endpoint."""
    status: str = Field(..., description="Status of the MinIO service")
    hostname: str = Field(..., description="MinIO server hostname")
    buckets: List[BucketInfo] = Field(default_factory=list, description="List of buckets")
    connected: bool = Field(..., description="Connection status")
    timestamp: str = Field(..., description="Timestamp of the status check")


@router.get("/status")
async def get_minio_status(
    hostname: str = Query(..., description="Remote server hostname"),
) -> Dict[str, Any]:
    """
    Get the status of the MinIO server.
    
    Args:
        hostname: The hostname of the remote server
        
    Returns:
        Dict: Status information about the MinIO service
    """
    minio_client = MinIOSSHClient()
    try:
        # Start SSH tunnel
        tunnel_success = await minio_client.start_ssh_tunnel(hostname)
        if not tunnel_success:
            logger.error(f"Failed to establish SSH tunnel to {hostname}")
            raise HTTPException(
                status_code=503,
                detail=f"Failed to establish SSH tunnel to {hostname}"
            )
        
        # Get S3 client
        s3_client = minio_client.get_s3_client()
        
        # Get bucket list
        bucket_details = []
        buckets = s3_client.list_buckets()
        for bucket in buckets.get('Buckets', []):
            try:
                # Get bucket statistics
                bucket_name = bucket.get('Name', 'unknown')
                
                # For each bucket, get objects to count and sum size
                object_count = 0
                total_size = 0
                
                objects = s3_client.list_objects_v2(Bucket=bucket_name)
                if 'Contents' in objects:
                    object_count = len(objects['Contents'])
                    total_size = sum(obj.get('Size', 0) for obj in objects['Contents'])
                
                bucket_info = BucketInfo(
                    name=bucket_name,
                    creation_date=bucket.get('CreationDate').isoformat() if bucket.get('CreationDate') else None,
                    object_count=object_count,
                    size_bytes=total_size
                )
                bucket_details.append(bucket_info.dict())
            except Exception as e:
                logger.warning(f"Error getting details for bucket {bucket.get('Name', 'unknown')}: {str(e)}")
                bucket_details.append({"name": bucket.get('Name', 'unknown'), "error": str(e)})
        
        # Close the tunnel when done
        await minio_client.close_tunnel()
        
        return {
            "status": "ok",
            "hostname": hostname,
            "buckets": bucket_details,
            "connected": True,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error checking MinIO status: {str(e)}")
        await minio_client.close_tunnel()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get MinIO status: {str(e)}"
        )
