
"""
Router for VirusTotal API endpoints.
"""
import logging
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any

from api.services.virustotal_service import VirusTotalService
from api.schemas.virustotal import VirusTotalResponseData, VirusTotalError
from api.core.logging_config import setup_logging

# Set up logging
logger = setup_logging()

router = APIRouter(
    prefix="/virustotal",
    tags=["virustotal"],
    responses={
        status.HTTP_404_NOT_FOUND: {"model": VirusTotalError},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"model": VirusTotalError},
    }
)


@router.get(
    "/hash/{file_hash}",
    response_model=VirusTotalResponseData,
    summary="Get VirusTotal analysis for a file hash",
    description="Retrieves detailed analysis results for a specific file hash from VirusTotal"
)
async def get_hash_analysis(file_hash: str) -> Dict[str, Any]:
    """
    Get analysis results for a specific file hash.
    
    Args:
        file_hash: The SHA-256 hash of the file to analyze
        
    Returns:
        VirusTotal analysis results
    """
    try:
        logger.info(f"Received request for VirusTotal analysis of hash: {file_hash}")
        service = VirusTotalService()
        result = service.get_hash_analysis(file_hash)
        return {"data": result}
    except ValueError as e:
        logger.error(f"Value error in VirusTotal request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error retrieving VirusTotal analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"VirusTotal API error: {str(e)}"
        )


@router.get(
    "/monitor/{file_hash}",
    response_model=VirusTotalResponseData,
    summary="Get VirusTotal monitor analyses for a hash",
    description="Retrieves monitor partner analyses results for a specific hash from VirusTotal"
)
async def get_monitor_analysis(file_hash: str) -> Dict[str, Any]:
    """
    Get monitor partner analyses for a specific hash.
    
    Args:
        file_hash: The SHA-256 hash to get monitor analyses for
        
    Returns:
        VirusTotal monitor analyses results
    """
    try:
        logger.info(f"Received request for VirusTotal monitor analyses of hash: {file_hash}")
        service = VirusTotalService()
        result = service.get_monitor_analysis(file_hash)
        return {"data": result}
    except ValueError as e:
        logger.error(f"Value error in VirusTotal request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error retrieving VirusTotal monitor analyses: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"VirusTotal API error: {str(e)}"
        )
