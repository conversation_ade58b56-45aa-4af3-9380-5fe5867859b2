
"""
API routes for Docker container management.
"""
from fastapi import APIRout<PERSON>, Depends, HTTPException, Query
from typing import List, Optional

from api.services.docker_service import DockerService
from api.core.security import get_current_user
from api.schemas.user import User

router = APIRouter()

@router.get("/containers")
async def list_containers(
    server: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    List Docker containers on the specified server.
    """
    docker_service = DockerService()
    result = await docker_service.list_containers(server)
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    
    return result

@router.post("/containers/{container_id}/start")
async def start_container(
    container_id: str,
    server: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    Start a Docker container.
    """
    docker_service = DockerService()
    result = await docker_service.start_container(container_id, server)
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    
    return result

@router.post("/containers/{container_id}/stop")
async def stop_container(
    container_id: str,
    server: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    Stop a Docker container.
    """
    docker_service = DockerService()
    result = await docker_service.stop_container(container_id, server)
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    
    return result

@router.get("/containers/{container_id}/logs")
async def get_container_logs(
    container_id: str,
    lines: int = Query(100, gt=0, le=1000),
    server: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    Get logs from a Docker container.
    """
    docker_service = DockerService()
    result = await docker_service.get_container_logs(container_id, lines, server)
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    
    return result

@router.get("/containers/{container_id}/stats")
async def get_container_stats(
    container_id: str,
    server: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    Get statistics for a Docker container.
    """
    docker_service = DockerService()
    result = await docker_service.get_container_stats(container_id, server)
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    
    return result
