"""
API routes for file-to-VM bridge feature.
"""
import uuid
import logging
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session
from starlette.status import HTTP_201_CREATED

from api.core.auth import authenticate_token, get_current_user
from api.core.db import get_db
from api.models.vagrant_vm import VMTemplate
from api.services.file_to_vm_bridge import FileToVMBridgeService
from api.services.file_upload_service import FileUploadService
from api.routes.minio_ssh_wrapper import get_minio_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/file-to-vm",
    tags=["file-to-vm"],
    dependencies=[Depends(authenticate_token)]
)

@router.post("/create", status_code=HTTP_201_CREATED, response_model=Dict[str, Any])
async def create_vm_from_file(
    file_id: uuid.UUID,
    vm_name: str, 
    template: VMTemplate,
    description: str = None,
    memory_mb: int = Query(2048, ge=512, le=16384),
    cpus: int = Query(1, ge=1, le=8),
    disk_gb: int = Query(20, ge=10, le=100),
    auto_start: bool = True,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new VM based on an uploaded file.
    
    Args:
        file_id: ID of the uploaded file
        vm_name: Name for the new VM
        template: VM template to use
        description: Optional description for the VM
        memory_mb: Memory in MB for the VM (min: 512, max: 16384)
        cpus: Number of CPUs for the VM (min: 1, max: 8)
        disk_gb: Disk size in GB for the VM (min: 10, max: 100)
        auto_start: Whether to auto-start the VM
    
    Returns:
        Dictionary with the created VM details
    """
    try:
        logger.info(f"Creating VM from file {file_id} with template {template}")
        
        service = FileToVMBridgeService(db)
        result = await service.create_vm_from_file(
            file_id=file_id,
            vm_name=vm_name,
            template=template,
            user_id=current_user.id,
            background_tasks=background_tasks,
            description=description,
            memory_mb=memory_mb,
            cpus=cpus,
            disk_gb=disk_gb,
            auto_start=auto_start
        )
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating VM from file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create VM: {str(e)}")

@router.post("/create-from-latest", status_code=HTTP_201_CREATED, response_model=Dict[str, Any])
async def create_vm_from_latest_file(
    vm_name: str, 
    template: VMTemplate,
    description: str = None,
    memory_mb: int = Query(2048, ge=512, le=16384),
    cpus: int = Query(1, ge=1, le=8),
    disk_gb: int = Query(20, ge=10, le=100),
    auto_start: bool = True,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new VM based on the latest uploaded file.
    
    Args:
        vm_name: Name for the new VM
        template: VM template to use
        description: Optional description for the VM
        memory_mb: Memory in MB for the VM (min: 512, max: 16384)
        cpus: Number of CPUs for the VM (min: 1, max: 8)
        disk_gb: Disk size in GB for the VM (min: 10, max: 100)
        auto_start: Whether to auto-start the VM
    
    Returns:
        Dictionary with the created VM details
    """
    try:
        # Get the latest file upload
        file_upload_service = FileUploadService(get_minio_client())
        uploads = await file_upload_service.get_all()
        
        if not uploads:
            raise HTTPException(status_code=404, detail="No file uploads found")
        
        # Sort by created_at in descending order and get the latest
        latest_upload = sorted(uploads, key=lambda x: x.get("created_at", ""), reverse=True)[0]
        file_id = latest_upload["id"]
        
        logger.info(f"Creating VM from latest file {file_id} with template {template}")
        
        # Call the regular create method
        service = FileToVMBridgeService(db)
        result = await service.create_vm_from_file(
            file_id=file_id,
            vm_name=vm_name,
            template=template,
            user_id=current_user.id,
            background_tasks=background_tasks,
            description=description or f"VM created from {latest_upload['filename']}",
            memory_mb=memory_mb,
            cpus=cpus,
            disk_gb=disk_gb,
            auto_start=auto_start
        )
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating VM from latest file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create VM: {str(e)}")

@router.get("/vms-for-file/{file_id}", response_model=List[Dict[str, Any]])
async def get_vms_for_file(
    file_id: uuid.UUID,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all VMs associated with a file.
    
    Args:
        file_id: ID of the file
    
    Returns:
        List of VMs associated with the file
    """
    try:
        logger.info(f"Getting VMs for file {file_id}")
        
        service = FileToVMBridgeService(db)
        vms = service.get_vms_for_file(file_id, current_user.id)
        
        return vms
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting VMs for file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get VMs: {str(e)}") 