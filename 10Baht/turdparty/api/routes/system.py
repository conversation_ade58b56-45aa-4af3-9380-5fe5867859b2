"""
System health and status endpoints.
"""
import os
import platform
import logging
from typing import Dict, List
from fastapi import APIRouter, Depends, HTTPException
from starlette.responses import JSONResponse

from api.routes.minio_ssh_wrapper import get_minio_client
from api.core.auth import authenticate_token
from api.services.vagrant_client import VagrantClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/system",
    tags=["system"],
)

@router.get("/health")
async def health_check():
    """Check system health and connectivity of critical services."""
    health_status = {
        "status": "healthy",
        "services": {
            "api": "ok",
            "minio": "checking",
            "vagrant": "checking",
        },
        "details": {}
    }
    
    # Check MinIO connectivity
    try:
        minio_client = get_minio_client()
        buckets = minio_client.list_buckets()
        health_status["services"]["minio"] = "ok"
        health_status["details"]["minio"] = {
            "buckets": len(buckets),
            "names": [bucket.name for bucket in buckets]
        }
    except Exception as e:
        logger.error(f"MinIO health check failed: {str(e)}")
        health_status["services"]["minio"] = "error"
        health_status["details"]["minio"] = {"error": str(e)}
        health_status["status"] = "degraded"
    
    # Check Vagrant client connectivity
    try:
        vagrant_client = VagrantClient()
        # Just create the client, don't actually connect yet
        health_status["services"]["vagrant"] = "ok"
        health_status["details"]["vagrant"] = {
            "client_initialized": True,
            "server": vagrant_client.server or "not_configured"
        }
    except Exception as e:
        logger.error(f"Vagrant client initialization failed: {str(e)}")
        health_status["services"]["vagrant"] = "error"
        health_status["details"]["vagrant"] = {"error": str(e)}
        health_status["status"] = "degraded"
    
    # System info
    health_status["details"]["system"] = {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "api_mode": os.environ.get("API_MODE", "production"),
    }
    
    return health_status

@router.get("/info", dependencies=[Depends(authenticate_token)])
async def system_info():
    """Get detailed system information (authenticated)."""
    system_details = {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "processor": platform.processor(),
        "api_version": "1.0.0",
        "environment": os.environ.get("API_MODE", "production"),
        "debug_mode": os.environ.get("DEBUG", "false").lower() in ("true", "1", "yes"),
    }
    
    # Add environment variables (filtering sensitive ones)
    filtered_env = {}
    sensitive_keys = ["SECRET", "KEY", "TOKEN", "PASSWORD", "PASS", "AUTH"]
    for key, value in os.environ.items():
        # Skip sensitive keys
        if any(sensitive in key.upper() for sensitive in sensitive_keys):
            filtered_env[key] = "******"
        else:
            filtered_env[key] = value
    
    system_details["environment_variables"] = filtered_env
    
    return system_details 