from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query, status
from api.models.success_criteria import (
    SuccessC<PERSON>ria,
    SuccessCriteriaCreate,
    SuccessCriteriaUpdate,
    SuccessCriteriaFilters,
    SuccessCriteriaResponse,
)
from api.services.success_criteria import SuccessCriteriaService

router = APIRouter(prefix="/api/success-criteria", tags=["success-criteria"])

def get_success_criteria_service() -> SuccessCriteriaService:
    """Dependency injection for success criteria service."""
    return SuccessCriteriaService()

@router.get("", response_model=SuccessCriteriaResponse)
async def get_success_criteria(
    page: int = Query(1, description="Page number"),
    page_size: int = Query(10, description="Number of items per page"),
    status: str = Query(None, description="Filter by status"),
    category: str = Query(None, description="Filter by category"),
    service: SuccessCriteriaService = Depends(get_success_criteria_service),
) -> SuccessCriteriaResponse:
    """
    Get success criteria with optional filtering.

    Args:
        page: Page number for pagination
        page_size: Number of items per page
        status: Filter by status
        category: Filter by category
        service: Success criteria service

    Returns:
        SuccessCriteriaResponse containing filtered criteria and pagination info

    Raises:
        HTTPException: If validation fails
    """
    try:
        filters = SuccessCriteriaFilters(
            page=page,
            page_size=page_size,
            status=status,
            category=category,
        )
        return await service.get_success_criteria(filters)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e),
        )

@router.get("/{criterion_id}", response_model=SuccessCriteria)
async def get_success_criteria_by_id(
    criterion_id: str,
    service: SuccessCriteriaService = Depends(get_success_criteria_service),
) -> SuccessCriteria:
    """
    Get a success criterion by ID.

    Args:
        criterion_id: ID of the success criterion
        service: Success criteria service

    Returns:
        SuccessCriteria

    Raises:
        HTTPException: If criterion not found
    """
    criterion = await service.get_success_criteria_by_id(criterion_id)
    if not criterion:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Success criterion not found",
        )
    return criterion

@router.post("", response_model=SuccessCriteria)
async def create_success_criteria(
    criterion: SuccessCriteriaCreate,
    service: SuccessCriteriaService = Depends(get_success_criteria_service),
) -> SuccessCriteria:
    """
    Create a new success criterion.

    Args:
        criterion: Success criterion data to create
        service: Success criteria service

    Returns:
        Created SuccessCriteria

    Raises:
        HTTPException: If validation fails
    """
    try:
        return await service.create_success_criteria(criterion)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e),
        )

@router.patch("/{criterion_id}", response_model=SuccessCriteria)
async def update_success_criteria(
    criterion_id: str,
    update: SuccessCriteriaUpdate,
    service: SuccessCriteriaService = Depends(get_success_criteria_service),
) -> SuccessCriteria:
    """
    Update an existing success criterion.

    Args:
        criterion_id: ID of the success criterion to update
        update: Update data for the success criterion
        service: Success criteria service

    Returns:
        Updated SuccessCriteria

    Raises:
        HTTPException: If criterion not found or validation fails
    """
    try:
        criterion = await service.update_success_criteria(criterion_id, update)
        if not criterion:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Success criterion not found",
            )
        return criterion
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e),
        )

@router.patch("/{criterion_id}/status", response_model=SuccessCriteria)
async def update_success_criteria_status(
    criterion_id: str,
    status: str,
    service: SuccessCriteriaService = Depends(get_success_criteria_service),
) -> SuccessCriteria:
    """
    Update the status of a success criterion.

    Args:
        criterion_id: ID of the success criterion
        status: New status for the success criterion
        service: Success criteria service

    Returns:
        Updated SuccessCriteria

    Raises:
        HTTPException: If criterion not found or validation fails
    """
    try:
        criterion = await service.update_success_criteria_status(criterion_id, status)
        if not criterion:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Success criterion not found",
            )
        return criterion
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e),
        )

@router.post("/{criterion_id}/details", response_model=SuccessCriteria)
async def add_success_criteria_detail(
    criterion_id: str,
    detail: str,
    service: SuccessCriteriaService = Depends(get_success_criteria_service),
) -> SuccessCriteria:
    """
    Add a new detail to a success criterion.

    Args:
        criterion_id: ID of the success criterion
        detail: Content of the new detail
        service: Success criteria service

    Returns:
        Updated SuccessCriteria

    Raises:
        HTTPException: If criterion not found
    """
    criterion = await service.add_success_criteria_detail(criterion_id, detail)
    if not criterion:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Success criterion not found",
        )
    return criterion

@router.delete("/{criterion_id}")
async def delete_success_criteria(
    criterion_id: str,
    service: SuccessCriteriaService = Depends(get_success_criteria_service),
) -> dict:
    """
    Delete a success criterion.

    Args:
        criterion_id: ID of the success criterion to delete
        service: Success criteria service

    Returns:
        Dict with success message

    Raises:
        HTTPException: If criterion not found
    """
    deleted = await service.delete_success_criteria(criterion_id)
    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Success criterion not found",
        )
    return {"message": "Success criterion deleted successfully"} 