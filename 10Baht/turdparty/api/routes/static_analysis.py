"""
API endpoints for static analysis services.
"""
import logging
from typing import Dict, Any, Optional, List

from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, status, Body
from fastapi.responses import JSONResponse

from api.schemas.static_analysis import (
    ServicesStatusResponse,
    FileAnalysisResponse,
    HashLookupRequest
)
from api.services.static_analysis_service import StaticAnalysisService
from api.core.config import settings

logger = logging.getLogger(__name__)
static_analysis_router = APIRouter()


def get_static_analysis_service() -> StaticAnalysisService:
    """Dependency to get the static analysis service."""
    return StaticAnalysisService(api_key=getattr(settings, "VIRUSTOTAL_API_KEY", None))


@static_analysis_router.get(
    "/services/status",
    response_model=ServicesStatusResponse,
    summary="Get status of integrated services",
    description="Returns the status of all integrated static analysis services."
)
async def get_services_status(
    service: StaticAnalysisService = Depends(get_static_analysis_service)
) -> Dict[str, Any]:
    """
    Get the status of integrated services.
    
    Returns:
        JSON response with status of each service
    """
    try:
        status = service.get_services_status()
        return {"success": True, **status}
    except Exception as e:
        logger.error(f"Error getting services status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get services status: {str(e)}"
        )


@static_analysis_router.post(
    "/analyze/file",
    response_model=FileAnalysisResponse,
    summary="Analyze a file",
    description="Analyzes a file using integrated static analysis services."
)
async def analyze_file(
    file: UploadFile = File(...),
    service: StaticAnalysisService = Depends(get_static_analysis_service)
) -> Dict[str, Any]:
    """
    Analyze a file using available services.
    
    Args:
        file: File to analyze
        
    Returns:
        JSON response with analysis results
    """
    try:
        file_content = await file.read()
        if len(file_content) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Empty file provided"
            )
            
        results = service.analyze_file(file_content)
        return results
    except ValueError as e:
        logger.error(f"Error analyzing file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error analyzing file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze file: {str(e)}"
        )


@static_analysis_router.post(
    "/lookup/hash",
    summary="Look up file hash",
    description="Looks up a file hash on VirusTotal."
)
async def lookup_hash(
    request: HashLookupRequest,
    service: StaticAnalysisService = Depends(get_static_analysis_service)
) -> Dict[str, Any]:
    """
    Look up a file hash on VirusTotal.
    
    Args:
        request: Request containing the hash to look up
        
    Returns:
        JSON response with lookup results
    """
    try:
        if not service.virustotal_api_key:
            logger.error("VirusTotal API key not configured")
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content={"success": False, "detail": "VirusTotal API key not configured"}
            )
            
        result = service.lookup_file_hash_virustotal(request.hash_value)
        return {"success": True, **result}
    except ValueError as e:
        logger.error(f"Error looking up hash: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"success": False, "detail": str(e)}
        )
    except Exception as e:
        logger.error(f"Error looking up hash: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"success": False, "detail": f"Failed to look up hash: {str(e)}"}
        )


@static_analysis_router.post(
    "/analyze",
    summary="Analyze a file by path",
    description="Analyzes a file at the specified path."
)
async def analyze_file_by_path(
    file_path: str = Body(..., embed=True),
    service: StaticAnalysisService = Depends(get_static_analysis_service)
) -> Dict[str, Any]:
    """
    Analyze a file at the specified path.
    
    Args:
        file_path: Path to the file to analyze
        
    Returns:
        JSON response with analysis results
    """
    try:
        with open(file_path, "rb") as f:
            file_content = f.read()
            
        results = service.analyze_file(file_content)
        return {
            "success": True,
            "status": "success",
            "findings": results.get("findings", []),
            "error": None,
            "hashes": results.get("hashes"),
            "virustotal": results.get("virustotal")
        }
    except FileNotFoundError as e:
        logger.error(f"Error analyzing file: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "success": False,
                "status": "error",
                "findings": [],
                "error": "File not found",
                "hashes": None,
                "virustotal": None
            }
        )
    except Exception as e:
        logger.error(f"Error analyzing file: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "status": "error",
                "findings": [],
                "error": str(e),
                "hashes": None,
                "virustotal": None
            }
        )
