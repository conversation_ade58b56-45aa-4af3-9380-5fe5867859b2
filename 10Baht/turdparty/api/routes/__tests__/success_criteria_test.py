import pytest
from fastapi.testclient import TestClient
from datetime import datetime
from api.models.success_criteria import (
    SuccessCriteria,
    SuccessCriteriaCreate,
    SuccessCriteriaDetail,
)
from api.services.success_criteria import SuccessCriteriaService
from api.routes.success_criteria import router
from fastapi import FastAPI

app = FastAPI()
app.include_router(router)
client = TestClient(app)

@pytest.fixture
def sample_criteria():
    """Fixture for sample success criteria."""
    return SuccessCriteria(
        id="1",
        name="Test Scenarios",
        description="All test scenarios pass consistently",
        status="success",
        category="functionality",
        details=[
            SuccessCriteriaDetail(
                id="1",
                content="Unit tests passing",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            ),
        ],
    )

def test_get_success_criteria_empty():
    """Test getting success criteria when none exist."""
    response = client.get("/api/success-criteria")
    assert response.status_code == 200
    data = response.json()
    assert data["data"] == []
    assert data["total"] == 0
    assert data["page"] == 1
    assert data["page_size"] == 10

def test_get_success_criteria_with_filters():
    """Test getting success criteria with filters."""
    response = client.get(
        "/api/success-criteria",
        params={
            "status": "success",
            "category": "functionality",
            "page": 1,
            "page_size": 10,
        },
    )
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data["data"], list)
    assert isinstance(data["total"], int)
    assert data["page"] == 1
    assert data["page_size"] == 10

def test_get_success_criteria_by_id_not_found():
    """Test getting a non-existent success criterion by ID."""
    response = client.get("/api/success-criteria/1")
    assert response.status_code == 404
    assert response.json()["detail"] == "Success criterion not found"

def test_create_success_criteria():
    """Test creating a new success criterion."""
    create_data = {
        "name": "New Criterion",
        "description": "Test description",
        "category": "performance",
    }
    response = client.post("/api/success-criteria", json=create_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == create_data["name"]
    assert data["description"] == create_data["description"]
    assert data["category"] == create_data["category"]
    assert data["status"] == "pending"
    assert len(data["details"]) == 0

def test_create_success_criteria_invalid_category():
    """Test creating a success criterion with invalid category."""
    create_data = {
        "name": "New Criterion",
        "description": "Test description",
        "category": "invalid_category",
    }
    response = client.post("/api/success-criteria", json=create_data)
    assert response.status_code == 422
    assert "validation error" in response.json()["detail"][0]["msg"].lower()

def test_update_success_criteria_not_found():
    """Test updating a non-existent success criterion."""
    update_data = {
        "name": "Updated Name",
        "description": "Updated description",
        "status": "failed",
        "category": "security",
    }
    response = client.patch("/api/success-criteria/1", json=update_data)
    assert response.status_code == 404
    assert response.json()["detail"] == "Success criterion not found"

def test_update_success_criteria_invalid_status():
    """Test updating a success criterion with invalid status."""
    update_data = {
        "status": "invalid_status",
    }
    response = client.patch("/api/success-criteria/1", json=update_data)
    assert response.status_code == 422
    assert "validation error" in response.json()["detail"][0]["msg"].lower()

def test_update_success_criteria_status_not_found():
    """Test updating the status of a non-existent success criterion."""
    response = client.patch("/api/success-criteria/1/status?status=failed")
    assert response.status_code == 404
    assert response.json()["detail"] == "Success criterion not found"

def test_update_success_criteria_status_invalid():
    """Test updating the status with an invalid value."""
    response = client.patch("/api/success-criteria/1/status?status=invalid_status")
    assert response.status_code == 422
    assert "validation error" in response.json()["detail"][0]["msg"].lower()

def test_add_success_criteria_detail_not_found():
    """Test adding a detail to a non-existent success criterion."""
    response = client.post("/api/success-criteria/1/details?detail=New detail")
    assert response.status_code == 404
    assert response.json()["detail"] == "Success criterion not found"

def test_delete_success_criteria_not_found():
    """Test deleting a non-existent success criterion."""
    response = client.delete("/api/success-criteria/1")
    assert response.status_code == 404
    assert response.json()["detail"] == "Success criterion not found"

def test_create_and_update_success_criteria():
    """Test creating and then updating a success criterion."""
    # Create a new criterion
    create_data = {
        "name": "Test Criterion",
        "description": "Initial description",
        "category": "functionality",
    }
    create_response = client.post("/api/success-criteria", json=create_data)
    assert create_response.status_code == 200
    created_data = create_response.json()
    criterion_id = created_data["id"]

    # Update the criterion
    update_data = {
        "name": "Updated Name",
        "description": "Updated description",
        "status": "success",
        "category": "security",
    }
    update_response = client.patch(f"/api/success-criteria/{criterion_id}", json=update_data)
    assert update_response.status_code == 200
    updated_data = update_response.json()
    assert updated_data["name"] == update_data["name"]
    assert updated_data["description"] == update_data["description"]
    assert updated_data["status"] == update_data["status"]
    assert updated_data["category"] == update_data["category"]

def test_create_and_add_detail():
    """Test creating a criterion and adding a detail."""
    # Create a new criterion
    create_data = {
        "name": "Test Criterion",
        "description": "Test description",
        "category": "functionality",
    }
    create_response = client.post("/api/success-criteria", json=create_data)
    assert create_response.status_code == 200
    created_data = create_response.json()
    criterion_id = created_data["id"]

    # Add a detail
    detail_response = client.post(
        f"/api/success-criteria/{criterion_id}/details",
        params={"detail": "New test detail"},
    )
    assert detail_response.status_code == 200
    detail_data = detail_response.json()
    assert len(detail_data["details"]) == 1
    assert detail_data["details"][0]["content"] == "New test detail"

def test_create_and_delete_success_criteria():
    """Test creating and then deleting a success criterion."""
    # Create a new criterion
    create_data = {
        "name": "Test Criterion",
        "description": "Test description",
        "category": "functionality",
    }
    create_response = client.post("/api/success-criteria", json=create_data)
    assert create_response.status_code == 200
    created_data = create_response.json()
    criterion_id = created_data["id"]

    # Delete the criterion
    delete_response = client.delete(f"/api/success-criteria/{criterion_id}")
    assert delete_response.status_code == 200
    assert delete_response.json()["message"] == "Success criterion deleted successfully"

    # Verify it's deleted
    get_response = client.get(f"/api/success-criteria/{criterion_id}")
    assert get_response.status_code == 404

def test_get_success_criteria_pagination():
    """Test pagination of success criteria."""
    # Create multiple criteria
    for i in range(15):
        create_data = {
            "name": f"Test Criterion {i}",
            "description": f"Test description {i}",
            "category": "functionality",
        }
        client.post("/api/success-criteria", json=create_data)

    # Test first page
    response = client.get("/api/success-criteria", params={"page": 1, "page_size": 10})
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]) == 10
    assert data["total"] == 15
    assert data["page"] == 1
    assert data["page_size"] == 10

    # Test second page
    response = client.get("/api/success-criteria", params={"page": 2, "page_size": 10})
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]) == 5
    assert data["total"] == 15
    assert data["page"] == 2
    assert data["page_size"] == 10

def test_get_success_criteria_filtering():
    """Test filtering of success criteria."""
    # Create criteria with different statuses and categories
    criteria = [
        {
            "name": "Test Criterion 1",
            "description": "Test description 1",
            "category": "functionality",
        },
        {
            "name": "Test Criterion 2",
            "description": "Test description 2",
            "category": "performance",
        },
        {
            "name": "Test Criterion 3",
            "description": "Test description 3",
            "category": "security",
        },
    ]
    criterion_ids = []
    for criterion in criteria:
        response = client.post("/api/success-criteria", json=criterion)
        assert response.status_code == 200
        criterion_ids.append(response.json()["id"])

    # Update statuses
    client.patch(f"/api/success-criteria/{criterion_ids[0]}/status?status=success")
    client.patch(f"/api/success-criteria/{criterion_ids[1]}/status?status=pending")
    client.patch(f"/api/success-criteria/{criterion_ids[2]}/status?status=failed")

    # Test filtering by status
    response = client.get("/api/success-criteria", params={"status": "success"})
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]) == 1
    assert data["data"][0]["status"] == "success"

    # Test filtering by category
    response = client.get("/api/success-criteria", params={"category": "performance"})
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]) == 1
    assert data["data"][0]["category"] == "performance"

    # Test filtering by both status and category
    response = client.get(
        "/api/success-criteria",
        params={"status": "success", "category": "functionality"},
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]) == 1
    assert data["data"][0]["status"] == "success"
    assert data["data"][0]["category"] == "functionality" 