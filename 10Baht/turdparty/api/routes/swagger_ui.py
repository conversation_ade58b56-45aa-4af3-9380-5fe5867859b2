"""
Custom Swagger UI and ReDoc implementations with dark mode support.
"""
from fastapi import APIRouter, Request
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.openapi.utils import get_openapi
import json

router = APIRouter(tags=["documentation"])

@router.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html(request: Request):
    """
    Custom Swagger UI with dark mode support.
    """
    root_path = request.scope.get("root_path", "").rstrip("/")
    openapi_url = root_path + "/openapi.json"
    
    # Create custom HTML with dark mode
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>API Documentation</title>
        <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css">
        <style>
            /* Dark mode styles */
            body {{
                background-color: #1a1a1a;
                color: #e0e0e0;
                margin: 0;
            }}
            
            .swagger-ui {{
                background-color: #1a1a1a;
                color: #e0e0e0;
            }}
            
            .swagger-ui .info .title,
            .swagger-ui .info h1, 
            .swagger-ui .info h2, 
            .swagger-ui .info h3, 
            .swagger-ui .info h4, 
            .swagger-ui .info h5, 
            .swagger-ui .info li, 
            .swagger-ui .info p, 
            .swagger-ui .info table,
            .swagger-ui .opblock-tag,
            .swagger-ui .opblock .opblock-summary-operation-id, 
            .swagger-ui .opblock .opblock-summary-path, 
            .swagger-ui .opblock .opblock-summary-path__deprecated,
            .swagger-ui .opblock-description-wrapper p,
            .swagger-ui .opblock-external-docs-wrapper p,
            .swagger-ui .opblock-title_normal p,
            .swagger-ui .tab li,
            .swagger-ui table thead tr td, 
            .swagger-ui table thead tr th,
            .swagger-ui .response-col_status,
            .swagger-ui .response-col_description,
            .swagger-ui .response-col_links,
            .swagger-ui .responses-inner h4, 
            .swagger-ui .responses-inner h5,
            .swagger-ui .model-title,
            .swagger-ui .model .property.primitive,
            .swagger-ui section.models h4,
            .swagger-ui .parameter__name,
            .swagger-ui .parameter__type,
            .swagger-ui .parameter__deprecated,
            .swagger-ui .parameter__in {{
                color: #e0e0e0;
            }}
            
            .swagger-ui .opblock-tag {{
                border-bottom: 1px solid #444;
            }}
            
            .swagger-ui .opblock {{
                background: #2d2d2d;
                border: 1px solid #444;
                border-radius: 4px;
            }}
            
            .swagger-ui .opblock .opblock-summary {{
                border-bottom: 1px solid #444;
            }}
            
            .swagger-ui .opblock .opblock-summary-method {{
                text-shadow: none;
            }}
            
            .swagger-ui .opblock.opblock-get {{
                background: rgba(0, 100, 150, 0.2);
                border-color: #0085a1;
            }}
            
            .swagger-ui .opblock.opblock-post {{
                background: rgba(0, 120, 0, 0.2);
                border-color: #00a14b;
            }}
            
            .swagger-ui .opblock.opblock-put {{
                background: rgba(150, 100, 0, 0.2);
                border-color: #a18d00;
            }}
            
            .swagger-ui .opblock.opblock-delete {{
                background: rgba(150, 0, 0, 0.2);
                border-color: #a10000;
            }}
            
            .swagger-ui .opblock.opblock-patch {{
                background: rgba(100, 0, 150, 0.2);
                border-color: #9000a1;
            }}
            
            .swagger-ui .btn {{
                background-color: #333;
                color: #e0e0e0;
                border: 1px solid #555;
            }}
            
            .swagger-ui select {{
                background-color: #333;
                color: #e0e0e0;
                border: 1px solid #555;
            }}
            
            .swagger-ui input[type=text], 
            .swagger-ui textarea {{
                background-color: #333;
                color: #e0e0e0;
                border: 1px solid #555;
            }}
            
            .swagger-ui .table-container {{
                background-color: #2d2d2d;
                border: 1px solid #444;
            }}
            
            .swagger-ui table tbody tr td {{
                border-bottom: 1px solid #444;
                color: #e0e0e0;
            }}
            
            .swagger-ui .response-col_status {{
                border-bottom: 1px solid #444;
            }}
            
            .swagger-ui .topbar {{
                background-color: #2d2d2d;
                border-bottom: 1px solid #444;
            }}
            
            .swagger-ui .topbar .download-url-wrapper .select-label {{
                color: #e0e0e0;
            }}
            
            .swagger-ui .topbar .download-url-wrapper input[type=text] {{
                border: 2px solid #555;
            }}
            
            .swagger-ui .scheme-container {{
                background-color: #2d2d2d;
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.5);
            }}
            
            .swagger-ui section.models {{
                border: 1px solid #444;
            }}
            
            .swagger-ui section.models.is-open h4 {{
                border-bottom: 1px solid #444;
            }}
            
            .swagger-ui .model-container {{
                background-color: #2d2d2d;
                border: 1px solid #444;
            }}
            
            .swagger-ui .model-box {{
                background-color: #333;
                border: 1px solid #444;
            }}
            
            .swagger-ui .dialog-ux .modal-ux {{
                background: #2d2d2d;
                border: 1px solid #444;
            }}
            
            .swagger-ui .dialog-ux .modal-ux-header h3 {{
                color: #e0e0e0;
            }}
            
            .swagger-ui .dialog-ux .modal-ux-content {{
                background: #2d2d2d;
            }}
            
            .swagger-ui .markdown code, 
            .swagger-ui .renderedMarkdown code {{
                background-color: #333;
                color: #e0e0e0;
            }}
            
            .swagger-ui .info {{
                background-color: transparent;
            }}
            
            .swagger-ui .info .title {{
                color: #e0e0e0;
            }}
            
            .swagger-ui .scopes h2 {{
                color: #e0e0e0;
            }}
            
            /* Scrollbars */
            ::-webkit-scrollbar {{
                width: 10px;
                height: 10px;
            }}
            
            ::-webkit-scrollbar-track {{
                background: #1a1a1a;
            }}
            
            ::-webkit-scrollbar-thumb {{
                background: #444;
                border-radius: 5px;
            }}
            
            ::-webkit-scrollbar-thumb:hover {{
                background: #555;
            }}
            
            /* Force dark mode for all elements */
            * {{
                color-scheme: dark;
            }}
        </style>
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
        <script>
            window.onload = function() {{
                const ui = SwaggerUIBundle({{
                    url: '{openapi_url}',
                    dom_id: '#swagger-ui',
                    deepLinking: true,
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIBundle.SwaggerUIStandalonePreset
                    ],
                    layout: "BaseLayout",
                    docExpansion: "list",
                    defaultModelsExpandDepth: 1,
                    defaultModelExpandDepth: 1,
                    defaultModelRendering: "example",
                    displayRequestDuration: true,
                    filter: true,
                    withCredentials: true,
                }});
                
                // Apply dark mode to dynamically loaded content
                const observer = new MutationObserver(function(mutations) {{
                    mutations.forEach(function(mutation) {{
                        if (mutation.addedNodes && mutation.addedNodes.length > 0) {{
                            for (let i = 0; i < mutation.addedNodes.length; i++) {{
                                const node = mutation.addedNodes[i];
                                if (node.nodeType === 1) {{ // Only process element nodes
                                    applyDarkModeToElement(node);
                                }}
                            }}
                        }}
                    }});
                }});
                
                // Start observing the document with the configured parameters
                observer.observe(document.body, {{ childList: true, subtree: true }});
                
                // Apply dark mode to an element and its children
                function applyDarkModeToElement(element) {{
                    // Apply styles to specific elements based on their class or attributes
                    if (element.classList) {{
                        if (element.classList.contains('swagger-ui')) {{
                            element.style.backgroundColor = '#1a1a1a';
                        }}
                        
                        if (element.classList.contains('opblock')) {{
                            element.style.backgroundColor = '#2d2d2d';
                            element.style.border = '1px solid #444';
                        }}
                        
                        if (element.classList.contains('model-box')) {{
                            element.style.backgroundColor = '#333';
                            element.style.border = '1px solid #444';
                        }}
                    }}
                    
                    // Process all child elements
                    const children = element.children;
                    if (children) {{
                        for (let i = 0; i < children.length; i++) {{
                            applyDarkModeToElement(children[i]);
                        }}
                    }}
                }}
                
                // Initial application to the whole document
                applyDarkModeToElement(document.body);
            }};
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)

@router.get("/redoc", include_in_schema=False)
async def redoc_html(request: Request):
    """
    Custom ReDoc with dark mode support.
    """
    root_path = request.scope.get("root_path", "").rstrip("/")
    openapi_url = root_path + "/openapi.json"
    
    # Create custom HTML with dark mode
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>API Documentation</title>
        <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
        <style>
            body {{
                margin: 0;
                padding: 0;
                background-color: #1a1a1a;
                color: #e0e0e0;
            }}
            
            #redoc-container {{
                background-color: #1a1a1a;
            }}
            
            /* Main container */
            .sc-htpNat, .menu-content, .api-info, .api-content {{
                background-color: #1a1a1a !important;
                color: #e0e0e0 !important;
            }}
            
            /* Menu and sidebar */
            .menu-content {{
                background-color: #222 !important;
                border-right: 1px solid #444 !important;
            }}
            
            /* Menu items */
            .menu-item-title, .menu-item-text, .menu-item-depth-1 > .menu-item-text {{
                color: #e0e0e0 !important;
            }}
            
            /* Active menu item */
            li.active > label, .menu-item.active, .menu-item-text.active {{
                background-color: #333 !important;
                color: #fff !important;
            }}
            
            /* Menu item hover */
            .menu-item:hover {{
                background-color: #333 !important;
            }}
            
            /* Search box */
            .search-input {{
                background-color: #333 !important;
                color: #e0e0e0 !important;
                border: 1px solid #444 !important;
            }}
            
            /* API info section */
            .api-info h1, .api-info p {{
                color: #e0e0e0 !important;
            }}
            
            /* Operation badges */
            .operation-type {{
                color: #fff !important;
            }}
            
            /* HTTP method badges */
            span[type="get"] {{
                background-color: #0085a1 !important;
            }}
            
            span[type="post"] {{
                background-color: #00a14b !important;
            }}
            
            span[type="put"] {{
                background-color: #a18d00 !important;
            }}
            
            span[type="delete"] {{
                background-color: #a10000 !important;
            }}
            
            span[type="patch"] {{
                background-color: #9000a1 !important;
            }}
            
            /* Response section */
            .response-title, .response {{
                background-color: #2d2d2d !important;
                color: #e0e0e0 !important;
            }}
            
            /* Schema section */
            .schema {{
                background-color: #2d2d2d !important;
                color: #e0e0e0 !important;
                border: 1px solid #444 !important;
            }}
            
            /* Code blocks */
            .redoc-json code, pre, code {{
                background-color: #333 !important;
                color: #e0e0e0 !important;
            }}
            
            /* Property names and types */
            .property-name, .property-type, .param-name, .param-type {{
                color: #e0e0e0 !important;
            }}
            
            /* Property required marker */
            .property-name .required {{
                color: #ff7070 !important;
            }}
            
            /* Tabs */
            .tab-list .tab-item {{
                color: #e0e0e0 !important;
            }}
            
            .tab-list .tab-item.active {{
                background-color: #333 !important;
                color: #fff !important;
            }}
            
            /* Links */
            a, a:visited {{
                color: #4da3ff !important;
            }}
            
            a:hover {{
                color: #66b0ff !important;
                text-decoration: underline !important;
            }}
            
            /* All text elements */
            [data-section-id] h1, 
            [data-section-id] h2, 
            [data-section-id] h3, 
            [data-section-id] h4, 
            [data-section-id] h5, 
            [data-section-id] p, 
            [data-section-id] a, 
            [data-section-id] li, 
            [data-section-id] td, 
            [data-section-id] th,
            h1, h2, h3, h4, h5, p, a, li, td, th,
            div, span, button {{
                color: #e0e0e0 !important;
            }}
            
            /* JSON syntax highlighting */
            .token.property {{
                color: #9cdcfe !important;
            }}
            
            .token.string {{
                color: #ce9178 !important;
            }}
            
            .token.number {{
                color: #b5cea8 !important;
            }}
            
            .token.boolean {{
                color: #569cd6 !important;
            }}
            
            .token.null {{
                color: #569cd6 !important;
            }}
            
            /* Scrollbars */
            ::-webkit-scrollbar {{
                width: 10px;
                height: 10px;
            }}
            
            ::-webkit-scrollbar-track {{
                background: #1a1a1a;
            }}
            
            ::-webkit-scrollbar-thumb {{
                background: #444;
                border-radius: 5px;
            }}
            
            ::-webkit-scrollbar-thumb:hover {{
                background: #555;
            }}
            
            /* Toggle button */
            #dark-mode-toggle {{
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 1000;
                padding: 8px 16px;
                background-color: #333;
                color: #fff;
                border: 1px solid #444;
                border-radius: 4px;
                cursor: pointer;
            }}
            
            /* Force dark mode for all elements */
            * {{
                color-scheme: dark;
            }}
        </style>
    </head>
    <body>
        <button id="dark-mode-toggle">Toggle Light/Dark Mode</button>
        <div id="redoc-container"></div>
        <script src="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js"></script>
        <script>
            // Initialize ReDoc
            Redoc.init('{openapi_url}', {{
                scrollYOffset: 50,
                hideDownloadButton: false,
                expandResponses: 'all',
                theme: {{
                    colors: {{
                        primary: {{
                            main: '#4da3ff'
                        }}
                    }},
                    typography: {{
                        fontSize: '16px',
                        headings: {{
                            fontWeight: '600'
                        }}
                    }},
                    sidebar: {{
                        backgroundColor: '#222',
                        textColor: '#e0e0e0'
                    }},
                    rightPanel: {{
                        backgroundColor: '#2d2d2d',
                        textColor: '#e0e0e0'
                    }}
                }}
            }}, document.getElementById('redoc-container'));
            
            // Apply dark mode to dynamically loaded content
            document.addEventListener('DOMContentLoaded', function() {{
                // Add a class to the body to indicate dark mode
                document.body.classList.add('redoc-dark-mode');
                
                // Create a dark mode observer to apply styles to dynamically loaded content
                const observer = new MutationObserver(function(mutations) {{
                    mutations.forEach(function(mutation) {{
                        if (mutation.addedNodes && mutation.addedNodes.length > 0) {{
                            for (let i = 0; i < mutation.addedNodes.length; i++) {{
                                const node = mutation.addedNodes[i];
                                if (node.nodeType === 1) {{ // Only process element nodes
                                    applyDarkModeToElement(node);
                                }}
                            }}
                        }}
                    }});
                }});
                
                // Start observing the document with the configured parameters
                observer.observe(document.body, {{ childList: true, subtree: true }});
                
                // Apply dark mode to an element and its children
                function applyDarkModeToElement(element) {{
                    // Apply styles to specific elements based on their class or attributes
                    if (element.classList) {{
                        if (element.classList.contains('menu-content')) {{
                            element.style.backgroundColor = '#222';
                            element.style.borderRight = '1px solid #444';
                        }}
                        
                        if (element.classList.contains('api-content')) {{
                            element.style.backgroundColor = '#1a1a1a';
                        }}
                        
                        if (element.classList.contains('schema')) {{
                            element.style.backgroundColor = '#2d2d2d';
                            element.style.border = '1px solid #444';
                        }}
                        
                        // Improve readability of text
                        if (element.tagName === 'P' || element.tagName === 'DIV' || 
                            element.tagName === 'SPAN' || element.tagName === 'LI' ||
                            element.tagName === 'TD' || element.tagName === 'TH') {{
                            element.style.color = '#e0e0e0';
                        }}
                        
                        // Make links more visible
                        if (element.tagName === 'A') {{
                            element.style.color = '#4da3ff';
                        }}
                    }}
                    
                    // Process all child elements
                    const children = element.children;
                    if (children) {{
                        for (let i = 0; i < children.length; i++) {{
                            applyDarkModeToElement(children[i]);
                        }}
                    }}
                }}
                
                // Initial application to the whole document
                applyDarkModeToElement(document.body);
                
                // Toggle button functionality
                const toggleButton = document.getElementById('dark-mode-toggle');
                if (toggleButton) {{
                    toggleButton.addEventListener('click', function() {{
                        const isDark = document.body.classList.contains('redoc-dark-mode');
                        if (isDark) {{
                            document.body.classList.remove('redoc-dark-mode');
                            document.body.style.backgroundColor = '#fff';
                            document.body.style.color = '#333';
                        }} else {{
                            document.body.classList.add('redoc-dark-mode');
                            document.body.style.backgroundColor = '#1a1a1a';
                            document.body.style.color = '#e0e0e0';
                        }}
                        
                        // Re-apply dark mode to all elements
                        applyDarkModeToElement(document.body);
                    }});
                }}
            }});
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)

@router.get("/openapi.json", include_in_schema=False)
async def get_openapi_json(request: Request):
    """
    Custom OpenAPI JSON endpoint.
    """
    from main import app  # Import the FastAPI app instance
    
    if not hasattr(app, "openapi_schema"):
        # Generate OpenAPI schema if not already generated
        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )
        app.openapi_schema = openapi_schema
    
    return JSONResponse(content=app.openapi_schema) 