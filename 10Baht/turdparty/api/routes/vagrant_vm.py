"""
API endpoints for Vagrant VM feature
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from typing import List, Optional
from sqlalchemy.orm import Session
from api.models.vagrant_vm import VagrantVMSchema, VagrantVMCreateSchema, VagrantVMUpdateSchema, VagrantVMListSchema, VagrantVMActionSchema, VagrantVMStatusSchema, VMTemplate
from api.db.session import get_db
from api.services.vagrant_vm import Vagrant_vmService as VagrantVMService
from api.middleware.auth_middleware import get_current_user
from api.models.user import UserSchema
import uuid

router = APIRouter(
    tags=["vagrant_vm"],
    responses={404: {"description": "Vagrant VM not found"}},
)

@router.get("/", response_model=VagrantVMListSchema)
async def get_all_vagrant_vms(
    skip: int = 0, 
    limit: int = 100,
    domain: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get all Vagrant VMs for the current user
    
    Optionally filter by domain
    """
    service = VagrantVMService(db)
    vms, total = service.get_all(skip=skip, limit=limit, user_id=current_user.id, domain=domain)
    return {"items": vms, "total": total}

@router.get("/{vagrant_vm_id}", response_model=VagrantVMSchema)
async def get_vagrant_vm(
    vagrant_vm_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get a specific Vagrant VM by ID
    """
    service = VagrantVMService(db)
    vagrant_vm_obj = service.get_by_id(vagrant_vm_id, user_id=current_user.id)
    if not vagrant_vm_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Vagrant VM with id {vagrant_vm_id} not found"
        )
    return vagrant_vm_obj

@router.post("/", response_model=VagrantVMSchema, status_code=status.HTTP_201_CREATED)
async def create_vagrant_vm(
    background_tasks: BackgroundTasks,
    vagrant_vm: VagrantVMCreateSchema,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Create a new Vagrant VM
    
    This will enforce TurdParty domain for all VMs.
    """
    service = VagrantVMService(db)
    
    # Enforce TurdParty domain
    if not vagrant_vm.domain:
        vagrant_vm.domain = "TurdParty"
    elif vagrant_vm.domain != "TurdParty":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="All VMs must be created with TurdParty domain"
        )
        
    return await service.create(vagrant_vm, background_tasks, user_id=current_user.id)

@router.put("/{vagrant_vm_id}", response_model=VagrantVMSchema)
async def update_vagrant_vm(
    vagrant_vm_id: uuid.UUID,
    vagrant_vm: VagrantVMUpdateSchema,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Update a Vagrant VM (metadata only)
    """
    service = VagrantVMService(db)
    return service.update(vagrant_vm_id, vagrant_vm, user_id=current_user.id)

@router.post("/{vagrant_vm_id}/action", response_model=VagrantVMStatusSchema)
async def perform_vm_action(
    vagrant_vm_id: uuid.UUID,
    action: VagrantVMActionSchema,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Perform an action on a Vagrant VM (start, stop, restart, destroy)
    """
    service = VagrantVMService(db)
    return await service.perform_action(vagrant_vm_id, action.action, background_tasks, user_id=current_user.id)

@router.post("/{vagrant_vm_id}/exec", response_model=dict)
async def execute_command(
    vagrant_vm_id: uuid.UUID,
    command_data: dict,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Execute a command on a Vagrant VM

    This endpoint allows executing arbitrary commands on a running VM.
    The command will be executed via SSH and the output will be returned.
    
    Optional parameter 'execution_method' can be provided to specify the method:
    - 'ssh': Direct SSH connection (default)
    - 'vagrant_cli': Use Vagrant CLI on host
    - 'simulation': Just simulate the execution
    """
    service = VagrantVMService(db)
    
    # Get the command from the request
    command = command_data.get("command", "")
    if not command:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Command is required"
        )
    
    # Get the execution method if provided
    execution_method = command_data.get("execution_method", None)
    
    # Set up options based on execution method
    options = {}
    if execution_method:
        if execution_method == "ssh":
            # Direct SSH - this is default behavior
            options["real_execution"] = True
            options["fallback_to_vagrant_cli"] = False
            options["fallback_to_simulation"] = False
        elif execution_method == "vagrant_cli":
            # Use Vagrant CLI on host
            options["real_execution"] = True
            options["fallback_to_vagrant_cli"] = True
            options["fallback_to_simulation"] = False
        elif execution_method == "simulation":
            # Just simulate execution
            options["real_execution"] = False
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid execution method: {execution_method}. Must be one of: ssh, vagrant_cli, simulation"
            )
    
    # Execute the command on the VM with the specified method
    return service.execute_command_on_vm(vagrant_vm_id, command, user_id=current_user.id, **options)

@router.get("/{vagrant_vm_id}/status", response_model=VagrantVMStatusSchema)
async def get_vm_status(
    vagrant_vm_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get the status of a Vagrant VM
    """
    service = VagrantVMService(db)
    return service.get_status(vagrant_vm_id, user_id=current_user.id)

@router.delete("/{vagrant_vm_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_vagrant_vm(
    vagrant_vm_id: uuid.UUID,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Delete a Vagrant VM (destroys the VM first)
    """
    service = VagrantVMService(db)
    await service.delete(vagrant_vm_id, background_tasks, user_id=current_user.id)
    return None

@router.get("/templates", response_model=List[dict])
async def get_vm_templates(
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get all available VM templates
    """
    # Convert the enum to a list of dictionaries with name and description
    templates = []
    for template in VMTemplate:
        template_info = {
            "value": template.value,
            "name": template.name,
            "description": get_template_description(template)
        }
        templates.append(template_info)
    
    return templates

def get_template_description(template: VMTemplate) -> str:
    """
    Get a human-readable description for a VM template
    """
    descriptions = {
        VMTemplate.UBUNTU_2004: "Ubuntu 20.04 LTS (Focal Fossa)",
        VMTemplate.UBUNTU_2204: "Ubuntu 22.04 LTS (Jammy Jellyfish)",
        VMTemplate.DEBIAN_11: "Debian 11 (Bullseye)",
        VMTemplate.CENTOS_7: "CentOS 7",
        VMTemplate.CENTOS_8: "CentOS 8",
        VMTemplate.WINDOWS_10: "Windows 10",
        VMTemplate.WINDOWS_SERVER_2019: "Windows Server 2019",
        VMTemplate.CUSTOM: "Custom Vagrantfile"
    }
    return descriptions.get(template, "No description available")
