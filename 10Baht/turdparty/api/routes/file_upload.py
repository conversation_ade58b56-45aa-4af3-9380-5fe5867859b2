"""
API routes for file upload feature.

This module provides endpoints for managing file uploads, including:
- Uploading individual files
- Downloading files by ID
- Listing all uploaded files
- Uploading multiple files as a folder
"""
import os
import uuid
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, Query, Path
from fastapi.responses import Response
from starlette.status import (
    HTTP_201_CREATED, 
    HTTP_204_NO_CONTENT, 
    HTTP_404_NOT_FOUND,
    HTTP_400_BAD_REQUEST,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from api.core.auth import authenticate_token
from api.schemas.file_upload import FileUploadCreate, FileUploadRead, FileUploadUpdate
from api.routes.minio_ssh_wrapper import get_minio_client
from api.services.file_upload_service import FileUploadService
from api.core.endpoints import APIEndpoints
from api.services.file_validation import FileValidationService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Update router prefix to match frontend expectations
router = APIRouter(
    prefix="/file_upload",  # Use a consistent prefix that matches APIEndpoints.FILE_UPLOAD["BASE"]
    tags=["files"],
    dependencies=[Depends(authenticate_token)]
)

# Initialize the service with MinIO client
file_upload_service = FileUploadService(get_minio_client())

@router.get(
    "/", 
    response_model=List[FileUploadRead],
    summary="List all file uploads",
    description="""
    Retrieves a list of all files that have been uploaded to the system.
    Results include metadata such as file size, content type, and download URLs.
    """
)
async def list_file_uploads(
    limit: int = Query(100, description="Maximum number of files to return", ge=1, le=1000),
    offset: int = Query(0, description="Number of files to skip", ge=0)
):
    """
    List all file uploads with pagination support.

    Parameters:
        limit: Maximum number of files to return
        offset: Number of files to skip for pagination

    Returns:
        List of file upload objects with metadata
    
    Raises:
        HTTPException: If an error occurs during retrieval
    """
    try:
        logger.info(f"Getting list of file uploads with limit {limit}, offset {offset}")
        uploads = await file_upload_service.get_all(limit=limit, offset=offset)
        return uploads
    except Exception as e:
        logger.error(f"Error listing file uploads: {str(e)}")
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Failed to list file uploads: {str(e)}"
        )

@router.post(
    "/", 
    response_model=FileUploadRead, 
    status_code=HTTP_201_CREATED,
    summary="Upload a new file",
    description="""
    Upload a new file to the system. The file will be validated, sanitized,
    and stored securely. The response includes metadata about the uploaded file
    and a URL for later downloading.
    
    Supported file types include: txt, pdf, doc, docx, xls, xlsx, csv, jpg, jpeg,
    png, gif, zip, tar, gz
    """
)
async def create_file_upload(
    file: UploadFile = File(..., description="The file to upload"),
    description: Optional[str] = Form(None, description="Optional description of the file")
):
    """
    Create a new file upload.

    Parameters:
        file: The file to upload
        description: Optional description of the file's contents or purpose

    Returns:
        FileUploadRead: The created file upload object with metadata and download URL
    
    Raises:
        HTTPException 400: If the file is invalid or of an unsupported type
        HTTPException 500: If an error occurs during upload or processing
    """
    try:
        logger.info(f"Creating new file upload for {file.filename}")
        
        # Validate and sanitize file
        sanitized_filename, content, actual_mime_type = await FileValidationService.validate_file(file)
        
        # Create file upload
        file_upload = FileUploadCreate(
            filename=sanitized_filename,
            file_size=len(content),
            content_type=actual_mime_type,
            description=description or "",
        )
        
        upload = await file_upload_service.create(file_upload, content)
        return upload
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating file upload: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create file upload: {str(e)}"
        )

@router.get(
    "/download/{file_id}",
    summary="Download a file",
    description="""
    Download a previously uploaded file by its ID. The file will be returned
    as a binary response with the appropriate content type and filename.
    """
)
async def download_file(
    file_id: str = Path(..., description="The ID of the file to download", example="f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f")
):
    """
    Download a file by its ID.

    Parameters:
        file_id: The ID of the file to download

    Returns:
        Binary file content with appropriate headers

    Raises:
        HTTPException 404: If the file is not found
        HTTPException 500: If an error occurs during download
    """
    try:
        logger.info(f"Downloading file with ID {file_id}")
        file_data = await file_upload_service.download(file_id)
        if not file_data:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND, 
                detail=f"File {file_id} not found"
            )
        
        content, filename, content_type = file_data
        
        headers = {
            "Content-Disposition": f'attachment; filename="{filename}"',
            "Content-Type": content_type,
        }
        
        return Response(content=content, headers=headers)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Failed to download file: {str(e)}"
        )

@router.delete(
    "/{file_id}",
    status_code=HTTP_204_NO_CONTENT,
    summary="Delete a file",
    description="Delete a previously uploaded file by its ID."
)
async def delete_file(
    file_id: str = Path(..., description="The ID of the file to delete", example="f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f")
):
    """
    Delete a file by its ID.
    
    Parameters:
        file_id: The ID of the file to delete
    
    Returns:
        No content on success
    
    Raises:
        HTTPException 404: If the file is not found
        HTTPException 500: If an error occurs during deletion
    """
    try:
        logger.info(f"Deleting file with ID {file_id}")
        deleted = await file_upload_service.delete(file_id)
        if not deleted:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND, 
                detail=f"File {file_id} not found"
            )
        return Response(status_code=HTTP_204_NO_CONTENT)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Failed to delete file: {str(e)}"
        )

@router.put(
    "/{file_id}",
    response_model=FileUploadRead,
    summary="Update file metadata",
    description="Update metadata for a previously uploaded file, such as its description."
)
async def update_file(
    file_id: str = Path(..., description="The ID of the file to update", example="f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f"),
    file_update: FileUploadUpdate = ...,
):
    """
    Update file metadata.
    
    Parameters:
        file_id: The ID of the file to update
        file_update: The metadata to update
    
    Returns:
        Updated file metadata
    
    Raises:
        HTTPException 404: If the file is not found
        HTTPException 500: If an error occurs during update
    """
    try:
        logger.info(f"Updating file with ID {file_id}")
        updated = await file_upload_service.update(file_id, file_update)
        if not updated:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND, 
                detail=f"File {file_id} not found"
            )
        return updated
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Failed to update file: {str(e)}"
        )

@router.post(
    "/folder", 
    response_model=dict, 
    status_code=HTTP_201_CREATED,
    summary="Upload multiple files as a folder",
    description="""
    Upload multiple files as a folder structure. Each file must have a corresponding
    path that indicates its location within the folder structure.
    """
)
async def upload_folder(
    files: List[UploadFile] = File(..., description="List of files to upload"),
    paths: List[str] = Form(..., description="Corresponding paths for each file"),
    description: Optional[str] = Form(None, description="Optional description for the folder")
):
    """
    Upload multiple files as a folder.
    
    Parameters:
        files: List of files to upload
        paths: List of paths for each file, must match the number of files
        description: Optional description for the folder
    
    Returns:
        Dictionary containing folder ID, file count, and list of uploaded files
    
    Raises:
        HTTPException 400: If number of files and paths don't match
        HTTPException 500: If an error occurs during upload
    """
    try:
        logger.info(f"Creating folder upload with {len(files)} files")
        if len(files) != len(paths):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST, 
                detail="Number of files and paths must match"
            )
        
        results = await file_upload_service.create_folder(files, paths, description or "")
        return {
            "folder_id": results["folder_id"],
            "file_count": len(results["files"]),
            "files": results["files"],
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating folder upload: {str(e)}")
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Failed to create folder upload: {str(e)}"
        )

@router.post(
    "/upload", 
    response_model=FileUploadRead, 
    status_code=HTTP_201_CREATED,
    summary="Upload a file (alias)",
    description="Alias for the main file upload endpoint to maintain compatibility with clients."
)
async def upload_file_alias(
    file: UploadFile = File(..., description="The file to upload"),
    description: Optional[str] = Form(None, description="Optional description of the file")
):
    """Alias for create_file_upload endpoint."""
    return await create_file_upload(file, description)
