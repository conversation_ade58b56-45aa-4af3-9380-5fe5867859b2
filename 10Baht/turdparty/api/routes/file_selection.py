"""
API endpoints for file selection feature
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from sqlalchemy.orm import Session
from api.db.session import get_db
from api.db.models import FileSelection, FileUpload
from api.models.file_selection import FileSelectionSchema, FileSelectionCreateSchema, FileSelectionUpdateSchema, FileSelectionListSchema
from api.middleware.auth_middleware import get_current_user
from api.models.user import UserSchema
import uuid
from datetime import datetime

router = APIRouter(
    tags=["file_selection"],
    responses={404: {"description": "File selection not found"}},
)

@router.get("/", response_model=FileSelectionListSchema)
async def get_all_file_selections(
    skip: int = 0, 
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get all file selections for the current user
    """
    query = db.query(FileSelection)
    query = query.filter(FileSelection.owner_id == current_user.id)
    total = query.count()
    items = query.offset(skip).limit(limit).all()
    return {"items": items, "total": total}

@router.get("/{file_selection_id}", response_model=FileSelectionSchema)
async def get_file_selection(
    file_selection_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get a specific file selection by ID
    """
    query = db.query(FileSelection).filter(FileSelection.id == file_selection_id)
    query = query.filter(FileSelection.owner_id == current_user.id)
    file_selection = query.first()
    if not file_selection:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"File selection with id {file_selection_id} not found"
        )
    return file_selection

@router.post("/", response_model=FileSelectionSchema, status_code=status.HTTP_201_CREATED)
async def create_file_selection(
    file_selection: FileSelectionCreateSchema,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Create a new file selection
    """
    # Validate that the file upload exists and belongs to the user
    file_upload = db.query(FileUpload).filter(
        FileUpload.id == file_selection.file_upload_id,
        FileUpload.owner_id == current_user.id
    ).first()
    
    if not file_upload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"File upload with id {file_selection.file_upload_id} not found"
        )
    
    # Create new file selection
    db_file_selection = FileSelection(
        name=file_selection.name,
        description=file_selection.description,
        file_upload_id=file_selection.file_upload_id,
        target_path=file_selection.target_path,
        permissions=file_selection.permissions,
        owner_id=current_user.id,
        is_active=True
    )
    
    db.add(db_file_selection)
    db.commit()
    db.refresh(db_file_selection)
    
    return db_file_selection

@router.put("/{file_selection_id}", response_model=FileSelectionSchema)
async def update_file_selection(
    file_selection_id: uuid.UUID,
    file_selection: FileSelectionUpdateSchema,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Update a file selection
    """
    query = db.query(FileSelection).filter(FileSelection.id == file_selection_id)
    query = query.filter(FileSelection.owner_id == current_user.id)
    db_file_selection = query.first()
    
    if not db_file_selection:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"File selection with id {file_selection_id} not found"
        )
    
    # Update fields
    update_data = file_selection.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_file_selection, key, value)
    
    db_file_selection.modified_on = datetime.utcnow()
    
    db.commit()
    db.refresh(db_file_selection)
    
    return db_file_selection

@router.delete("/{file_selection_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_file_selection(
    file_selection_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Delete a file selection
    """
    query = db.query(FileSelection).filter(FileSelection.id == file_selection_id)
    query = query.filter(FileSelection.owner_id == current_user.id)
    db_file_selection = query.first()
    
    if not db_file_selection:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"File selection with id {file_selection_id} not found"
        )
    
    db.delete(db_file_selection)
    db.commit()
    
    return None
