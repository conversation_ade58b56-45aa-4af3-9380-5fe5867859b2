
#!/usr/bin/env python
"""
API routes for MinIO storage operations via SSH.
"""
import os
import tempfile
import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import FileResponse

from api.core.logging_config import setup_logging
from api.services.minio_ssh_client import MinIO<PERSON><PERSON>lient

# Set up logging
logger = setup_logging(log_level=logging.INFO)

# Create storage router
router = APIRouter(
    prefix="/api/storage",
    tags=["storage"],
    responses={404: {"description": "Not found"}}
)

# Global SSH tunnel tracking
ssh_tunnels = {}

async def get_minio_client(hostname: str = Query(..., description="SSH hostname for MinIO server")):
    """
    Dependency to get or create a MinIO client with SSH tunnel.
    
    Args:
        hostname: SSH hostname for MinIO server
        
    Returns:
        Configured MinIOSSHClient with active tunnel
    """
    global ssh_tunnels
    
    # Check if we already have a client for this hostname
    if hostname not in ssh_tunnels:
        logger.info(f"Creating new MinIO client for {hostname}")
        ssh_tunnels[hostname] = {
            "client": MinIOSSHClient(),
            "active": False
        }
    
    # Get the client
    client = ssh_tunnels[hostname]["client"]
    
    # Check if tunnel is active, start if needed
    if not ssh_tunnels[hostname]["active"]:
        logger.info(f"Starting SSH tunnel for {hostname}")
        success = await client.start_ssh_tunnel(hostname)
        if not success:
            logger.error(f"Failed to establish SSH tunnel to {hostname}")
            raise HTTPException(status_code=500, detail=f"Failed to establish SSH connection to {hostname}")
        ssh_tunnels[hostname]["active"] = True
    
    return client

@router.get("/buckets", summary="List all buckets")
async def list_buckets(client: MinIOSSHClient = Depends(get_minio_client)):
    """
    List all buckets in MinIO.
    
    Args:
        client: MinIO client with active SSH tunnel
        
    Returns:
        List of buckets
    """
    result = await client.list_buckets()
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return {
        "success": True,
        "buckets": result["buckets"]
    }

@router.post("/buckets", summary="Create a new bucket")
async def create_bucket(
    bucket_name: str = Form(..., description="Name of the bucket to create"),
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Create a new bucket in MinIO.
    
    Args:
        bucket_name: Name of the bucket to create
        client: MinIO client with active SSH tunnel
        
    Returns:
        Operation result
    """
    result = await client.create_bucket(bucket_name)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return {
        "success": True,
        "message": result["message"]
    }

@router.delete("/buckets/{bucket_name}", summary="Delete a bucket")
async def delete_bucket(
    bucket_name: str,
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Delete a bucket from MinIO.
    
    Args:
        bucket_name: Name of the bucket to delete
        client: MinIO client with active SSH tunnel
        
    Returns:
        Operation result
    """
    result = await client.delete_bucket(bucket_name)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return {
        "success": True,
        "message": result["message"]
    }

@router.get("/buckets/{bucket_name}/objects", summary="List objects in a bucket")
async def list_objects(
    bucket_name: str,
    prefix: str = Query("", description="Prefix to filter objects"),
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    List objects in a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        prefix: Prefix to filter objects
        client: MinIO client with active SSH tunnel
        
    Returns:
        List of objects
    """
    result = await client.list_objects(bucket_name, prefix)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return {
        "success": True,
        "bucket": result["bucket"],
        "prefix": result["prefix"],
        "objects": result["objects"]
    }

@router.post("/buckets/{bucket_name}/objects", summary="Upload a file to a bucket")
async def upload_file(
    bucket_name: str,
    file: UploadFile = File(..., description="File to upload"),
    object_key: str = Form(None, description="Custom key for the object (defaults to filename)"),
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Upload a file to a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        file: File to upload
        object_key: Custom key for the object (defaults to filename)
        client: MinIO client with active SSH tunnel
        
    Returns:
        Operation result
    """
    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            # Write file contents to temp file
            temp_file.write(await file.read())
            temp_file_path = temp_file.name
        
        # Use filename as object key if not provided
        if not object_key:
            object_key = file.filename
        
        # Upload file
        result = await client.upload_file(bucket_name, object_key, temp_file_path)
        
        # Clean up temp file
        os.unlink(temp_file_path)
        
        # Handle result
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return {
            "success": True,
            "message": result["message"],
            "bucket": result["bucket"],
            "key": result["key"]
        }
    
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")

@router.get("/buckets/{bucket_name}/objects/{object_key}", summary="Download an object")
async def download_file(
    bucket_name: str,
    object_key: str,
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Download an object from a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        object_key: Key of the object to download
        client: MinIO client with active SSH tunnel
        
    Returns:
        File download response
    """
    try:
        # Create temp directory for download
        temp_dir = tempfile.mkdtemp()
        file_name = os.path.basename(object_key)
        local_path = os.path.join(temp_dir, file_name)
        
        # Download file
        result = await client.download_file(bucket_name, object_key, local_path)
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])
        
        # Return file for download
        return FileResponse(
            path=local_path,
            filename=file_name,
            media_type="application/octet-stream",
            background=lambda: os.remove(local_path)  # Clean up after download
        )
    
    except Exception as e:
        logger.error(f"Error downloading file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to download file: {str(e)}")

@router.delete("/buckets/{bucket_name}/objects/{object_key}", summary="Delete an object")
async def delete_object(
    bucket_name: str,
    object_key: str,
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Delete an object from a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        object_key: Key of the object to delete
        client: MinIO client with active SSH tunnel
        
    Returns:
        Operation result
    """
    result = await client.delete_object(bucket_name, object_key)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return {
        "success": True,
        "message": result["message"]
    }

@router.get("/buckets/{bucket_name}/objects", summary="List objects in a bucket")
async def list_objects(
    bucket_name: str,
    prefix: Optional[str] = None,
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    List objects in a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        prefix: Optional prefix filter for objects
        client: MinIO client with active SSH tunnel
        
    Returns:
        List of objects in the bucket
    """
    result = await client.list_objects(bucket_name, prefix)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return {
        "success": True,
        "bucket": bucket_name,
        "objects": result["objects"]
    }

@router.post("/buckets/{bucket_name}/objects", summary="Upload file to bucket")
async def upload_file(
    bucket_name: str,
    object_key: str = Form(..., description="Key/path for the object in the bucket"),
    file: UploadFile = File(..., description="File to upload"),
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Upload a file to a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        object_key: Key/path for the object in the bucket
        file: File to upload
        client: MinIO client with active SSH tunnel
        
    Returns:
        Upload result
    """
    # Save uploaded file to temporary location
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    try:
        # Write uploaded file data to temporary file
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        # Upload the file
        result = await client.upload_file(bucket_name, object_key, temp_file.name)
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return {
            "success": True,
            "bucket": bucket_name,
            "key": object_key,
            "size": result.get("size", len(content))
        }
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

@router.get("/buckets/{bucket_name}/objects/{object_key}", summary="Download file from bucket")
async def download_file(
    bucket_name: str,
    object_key: str,
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Download a file from a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        object_key: Key of the object to download
        client: MinIO client with active SSH tunnel
        
    Returns:
        File content as a download
    """
    # Create a temporary file for the download
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    temp_file.close()
    
    try:
        # Download the file
        result = await client.download_file(bucket_name, object_key, temp_file.name)
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])
        
        # Get metadata for content type
        metadata_result = await client.get_object_metadata(bucket_name, object_key)
        content_type = "application/octet-stream"
        if metadata_result["success"]:
            content_type = metadata_result["metadata"].get("content-type", content_type)
        
        # Generate file name from object key
        filename = os.path.basename(object_key)
        if not filename:
            filename = "download"
        
        # Return file response
        return FileResponse(
            path=temp_file.name,
            media_type=content_type,
            filename=filename,
            background=fastapi.BackgroundTasks(tasks=[lambda: os.unlink(temp_file.name)])
        )
    except Exception as e:
        # Clean up if there's an error
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/buckets/{bucket_name}/objects/{object_key}/metadata", summary="Get object metadata")
async def get_object_metadata(
    bucket_name: str,
    object_key: str,
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Get metadata for an object in a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        object_key: Key of the object
        client: MinIO client with active SSH tunnel
        
    Returns:
        Object metadata
    """
    result = await client.get_object_metadata(bucket_name, object_key)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return {
        "success": True,
        "bucket": result["bucket"],
        "key": result["key"],
        "metadata": result["metadata"]
    }

@router.delete("/buckets/{bucket_name}/objects/{object_key}", summary="Delete object from bucket")
async def delete_object(
    bucket_name: str,
    object_key: str,
    client: MinIOSSHClient = Depends(get_minio_client)
):
    """
    Delete an object from a MinIO bucket.
    
    Args:
        bucket_name: Name of the bucket
        object_key: Key of the object to delete
        client: MinIO client with active SSH tunnel
        
    Returns:
        Delete result
    """
    result = await client.delete_object(bucket_name, object_key)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    
    return {
        "success": True,
        "message": "Object deleted successfully"
    }

@router.post("/tunnel/close", summary="Close SSH tunnel")
async def close_tunnel(hostname: str = Query(..., description="SSH hostname to close tunnel")):
    """
    Close an active SSH tunnel.
    
    Args:
        hostname: SSH hostname to close tunnel
        
    Returns:
        Operation result
    """
    global ssh_tunnels
    
    if hostname not in ssh_tunnels:
        return {
            "success": True,
            "message": f"No active tunnel for {hostname}"
        }
    
    try:
        client = ssh_tunnels[hostname]["client"]
        await client.stop_ssh_tunnel()
        client.cleanup()
        
        del ssh_tunnels[hostname]
        
        return {
            "success": True,
            "message": f"Tunnel to {hostname} closed successfully"
        }
    
    except Exception as e:
        logger.error(f"Error closing tunnel to {hostname}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to close tunnel: {str(e)}")
