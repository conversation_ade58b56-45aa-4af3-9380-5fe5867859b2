"""
MinIO SSH client wrapper to configure the MinIO client with environment variables.
"""
import os
import logging
from minio import <PERSON>o
from urllib3.exceptions import MaxRetryError
from functools import lru_cache
from typing import Optional
from fastapi import Depends

from api.core.test_config import test_settings
from api.core.config import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MinIOWrapper:
    """Wrapper for MinIO client with proper host configuration."""
    
    def __init__(self):
        """Initialize MinIO wrapper with proper host configuration."""
        # MinIO connection configuration - order of precedence:
        # 1. Environment variables
        # 2. host.docker.internal (Docker default)
        # 3. ********** (Docker bridge network)
        # 4. localhost (fallback)
        self.minio_host = os.environ.get("MINIO_HOST", "host.docker.internal")
        self.minio_port = os.environ.get("MINIO_PORT", "9000")
        self.minio_access_key = os.environ.get("MINIO_ACCESS_KEY", "minioadmin")
        self.minio_secret_key = os.environ.get("MINIO_SECRET_KEY", "minioadmin")
        self.minio_secure = os.environ.get("MINIO_SECURE", "").lower() in ("true", "1", "yes")
        
        # Try multiple possible hosts if connection fails
        self.possible_hosts = [
            self.minio_host,
            "host.docker.internal",
            "**********",
            "localhost"
        ]
        
        # Ensure we don't have duplicates
        self.possible_hosts = list(dict.fromkeys(self.possible_hosts))
        
        logger.info(f"MinIO wrapper initialized with primary host {self.minio_host}:{self.minio_port}")
        logger.info(f"Fallback hosts: {self.possible_hosts[1:]}")
        
        self.client = None
        
    def get_client(self):
        """
        Get a properly configured MinIO client.
        
        Returns:
            Minio: Configured MinIO client with working connection
        """
        # Check if we're in test mode
        if os.environ.get("API_TEST_MODE", "").lower() == "true":
            logger.info("Test mode active - using mock MinIO client")
            return Minio(
                "localhost:9000",
                access_key="minioadmin",
                secret_key="minioadmin",
                secure=False
            )
            
        if self.client is not None:
            return self.client
            
        # Try each possible host until one works
        for host in self.possible_hosts:
            try:
                logger.info(f"Attempting connection to MinIO at {host}:{self.minio_port}")
                
                client = Minio(
                    f"{host}:{self.minio_port}",
                    access_key=self.minio_access_key,
                    secret_key=self.minio_secret_key,
                    secure=self.minio_secure
                )
                
                # Test connection
                client.list_buckets()
                
                # Store successful client
                self.client = client
                
                # Update primary host for future reference
                if host != self.minio_host:
                    logger.info(f"Updating primary MinIO host from {self.minio_host} to {host}")
                    self.minio_host = host
                    
                    # Update environment variable for other components
                    os.environ["MINIO_HOST"] = host
                
                logger.info(f"Successfully connected to MinIO at {host}:{self.minio_port}")
                return self.client
                
            except MaxRetryError:
                logger.warning(f"Connection failed to {host}:{self.minio_port}, trying next host")
                continue
                
            except Exception as e:
                logger.error(f"Error connecting to MinIO at {host}:{self.minio_port}: {str(e)}")
                continue
        
        # If we get here, all connection attempts failed
        logger.error(f"Failed to connect to MinIO on all hosts: {self.possible_hosts}")
        raise ConnectionError("Could not connect to MinIO server on any host")
    
    def list_buckets(self):
        """
        List all MinIO buckets.
        
        Returns:
            list: List of bucket objects
        """
        client = self.get_client()
        return client.list_buckets()
    
    def bucket_exists(self, bucket_name):
        """
        Check if a bucket exists.
        
        Args:
            bucket_name: Name of the bucket
            
        Returns:
            bool: True if bucket exists, False otherwise
        """
        client = self.get_client()
        return client.bucket_exists(bucket_name)
    
    def make_bucket(self, bucket_name):
        """
        Create a new bucket.
        
        Args:
            bucket_name: Name of the bucket
        """
        client = self.get_client()
        return client.make_bucket(bucket_name)
    
    def remove_bucket(self, bucket_name):
        """
        Remove a bucket.
        
        Args:
            bucket_name: Name of the bucket
        """
        client = self.get_client()
        return client.remove_bucket(bucket_name)

# Singleton instance for app-wide use
minio_wrapper = MinIOWrapper()

@lru_cache()
def get_minio_client() -> Minio:
    """Get MinIO client instance.
    
    In test mode, this will be overridden by the test configuration.
    In production, this will create a real MinIO client.
    """
    if test_settings.is_testing():
        # In test mode, return None - this will be overridden by test fixtures
        return None
        
    settings = get_settings()
    return Minio(
        settings.MINIO_ENDPOINT,
        access_key=settings.MINIO_ACCESS_KEY,
        secret_key=settings.MINIO_SECRET_KEY,
        secure=settings.MINIO_USE_SSL
    ) 