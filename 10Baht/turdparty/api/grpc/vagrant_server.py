
"""
gRPC server implementation for Vagrant service.
"""
import os
import logging
import asyncio
import grpc
from concurrent import futures
from typing import Any, Dict

# These imports will work after generating the gRPC code
# We're creating the structure that will be used
from api.services.vagrant_service import VagrantService
from api.core.exceptions import NotFoundError, ServiceError

logger = logging.getLogger(__name__)

# This will be the path to the generated gRPC module after running generate_grpc.py
# vagrant_pb2 = None
# vagrant_pb2_grpc = None

class VagrantServicer:
    """Implementation of the Vagrant gRPC service."""
    
    def __init__(self):
        """Initialize the servicer with the Vagrant service."""
        self.service = VagrantService()
        logger.info("VagrantServicer initialized")
    
    async def Status(self, request, context):
        """Get the status of a Vagrant VM."""
        try:
            result = await self.service.get_status(request.vm_id)
            return vagrant_pb2.VagrantStatusResponse(
                status=result["state"],
                error_message=""
            )
        except NotFoundError as e:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(e))
            return vagrant_pb2.VagrantStatusResponse(
                status="",
                error_message=str(e)
            )
        except ServiceError as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantStatusResponse(
                status="",
                error_message=str(e)
            )
        except Exception as e:
            logger.exception("Unexpected error in Status method")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return vagrant_pb2.VagrantStatusResponse(
                status="",
                error_message=f"Unexpected error: {str(e)}"
            )
    
    async def Up(self, request, context):
        """Start a Vagrant VM."""
        try:
            result = await self.service.up(request.vm_id, request.provision)
            return vagrant_pb2.VagrantCommandResponse(
                success=result["success"],
                message=result["message"],
                error_message=""
            )
        except NotFoundError as e:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=str(e)
            )
        except ServiceError as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=str(e)
            )
        except Exception as e:
            logger.exception("Unexpected error in Up method")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=f"Unexpected error: {str(e)}"
            )
    
    async def Halt(self, request, context):
        """Stop a Vagrant VM."""
        try:
            result = await self.service.halt(request.vm_id, request.force)
            return vagrant_pb2.VagrantCommandResponse(
                success=result["success"],
                message=result["message"],
                error_message=""
            )
        except NotFoundError as e:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=str(e)
            )
        except ServiceError as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=str(e)
            )
        except Exception as e:
            logger.exception("Unexpected error in Halt method")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=f"Unexpected error: {str(e)}"
            )
    
    async def Destroy(self, request, context):
        """Destroy a Vagrant VM."""
        try:
            result = await self.service.destroy(request.vm_id, request.force)
            return vagrant_pb2.VagrantCommandResponse(
                success=result["success"],
                message=result["message"],
                error_message=""
            )
        except NotFoundError as e:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=str(e)
            )
        except ServiceError as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=str(e)
            )
        except Exception as e:
            logger.exception("Unexpected error in Destroy method")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=f"Unexpected error: {str(e)}"
            )
    
    async def ListBoxes(self, request, context):
        """List available Vagrant boxes."""
        try:
            boxes = await self.service.list_boxes()
            box_protos = []
            
            for box in boxes:
                box_protos.append(vagrant_pb2.VagrantBoxInfo(
                    name=box["name"],
                    provider=box["provider"],
                    version=box["version"]
                ))
            
            return vagrant_pb2.VagrantBoxesResponse(
                boxes=box_protos,
                error_message=""
            )
        except ServiceError as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantBoxesResponse(
                boxes=[],
                error_message=str(e)
            )
        except Exception as e:
            logger.exception("Unexpected error in ListBoxes method")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return vagrant_pb2.VagrantBoxesResponse(
                boxes=[],
                error_message=f"Unexpected error: {str(e)}"
            )
    
    async def GetMachineInfo(self, request, context):
        """Get detailed information about a Vagrant VM."""
        try:
            info = await self.service.get_machine_info(request.vm_id)
            ssh_config = info.get("ssh_config", {})
            
            # Convert network info to map format
            network = {}
            if "HostName" in ssh_config:
                network["ip"] = ssh_config["HostName"]
            if "Port" in ssh_config:
                network["port"] = ssh_config["Port"]
            
            return vagrant_pb2.VagrantMachineInfoResponse(
                name=request.vm_id,
                provider="virtualbox",  # Default, would need to be determined properly
                state=info.get("state", "unknown"),
                directory=info.get("path", ""),
                network=network,
                error_message=""
            )
        except NotFoundError as e:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(e))
            return vagrant_pb2.VagrantMachineInfoResponse(
                name="",
                provider="",
                state="",
                directory="",
                network={},
                error_message=str(e)
            )
        except ServiceError as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantMachineInfoResponse(
                name="",
                provider="",
                state="",
                directory="",
                network={},
                error_message=str(e)
            )
        except Exception as e:
            logger.exception("Unexpected error in GetMachineInfo method")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return vagrant_pb2.VagrantMachineInfoResponse(
                name="",
                provider="",
                state="",
                directory="",
                network={},
                error_message=f"Unexpected error: {str(e)}"
            )
    
    async def ExecuteCommand(self, request, context):
        """Execute a command on a Vagrant VM."""
        try:
            result = await self.service.execute_command(
                request.vm_id, request.command, request.sudo
            )
            
            return vagrant_pb2.VagrantExecuteCommandResponse(
                success=result["success"],
                stdout=result["stdout"],
                stderr=result["stderr"],
                exit_code=result["exit_code"]
            )
        except NotFoundError as e:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(e))
            return vagrant_pb2.VagrantExecuteCommandResponse(
                success=False,
                stdout="",
                stderr=str(e),
                exit_code=-1
            )
        except ServiceError as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantExecuteCommandResponse(
                success=False,
                stdout="",
                stderr=str(e),
                exit_code=-1
            )
        except Exception as e:
            logger.exception("Unexpected error in ExecuteCommand method")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return vagrant_pb2.VagrantExecuteCommandResponse(
                success=False,
                stdout="",
                stderr=f"Unexpected error: {str(e)}",
                exit_code=-1
            )

async def serve(port=50051):
    """
    Start the gRPC server.
    
    Args:
        port: The port to listen on.
    """
    # Import the generated modules
    global vagrant_pb2, vagrant_pb2_grpc
    try:
        from api.grpc import vagrant_pb2, vagrant_pb2_grpc
    except ImportError:
        logger.error("Could not import generated gRPC modules. Run scripts/generate_grpc.py first.")
        return
    
    server = grpc.aio.server(futures.ThreadPoolExecutor(max_workers=10))
    vagrant_pb2_grpc.add_VagrantServiceServicer_to_server(VagrantServicer(), server)
    
    listen_addr = f"0.0.0.0:{port}"
    server.add_insecure_port(listen_addr)
    
    logger.info(f"Starting gRPC server on {listen_addr}")
    await server.start()
    
    try:
        await server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("Server stopping...")
        await server.stop(0)
        logger.info("Server stopped")
