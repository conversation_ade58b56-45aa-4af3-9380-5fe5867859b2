"""Test fixtures for integration tests."""
import os
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, clear_mappers
from uuid import uuid4
from datetime import datetime

from api.db.base_model import Base
from api.db.models.user import User
from api.db.models.item import Item
from api.db.models.file_upload import FileUpload
from api.db.models.file_selection import FileSelection
from api.db.models.vagrant_vm import VagrantVM
from api.db.models.vm_injection import VMInjection


@pytest.fixture(scope="function")
def db_engine():
    """Create and return a SQLAlchemy engine for testing."""
    # Use in-memory SQLite for testing
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    yield engine
    Base.metadata.drop_all(engine)


@pytest.fixture(scope="function")
def db_session(db_engine):
    """Create and return a SQLAlchemy session for testing."""
    Session = sessionmaker(bind=db_engine)
    session = Session()
    yield session
    session.rollback()
    session.close()


@pytest.fixture(scope="function")
def test_user(db_session):
    """Create and return a test user."""
    user = User(
        id=uuid4(),
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture(scope="function")
def test_user2(db_session):
    """Create and return a second test user."""
    user = User(
        id=uuid4(),
        username="testuser2",
        email="<EMAIL>",
        hashed_password="hashed_password"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture(scope="function")
def test_items(db_session, test_user):
    """Create and return test items for the test user."""
    items = []
    for i in range(3):
        item = Item(
            title=f"Test Item {i}",
            description=f"Test description {i}",
            owner_id=test_user.id
        )
        items.append(item)
        db_session.add(item)
    db_session.commit()
    
    for item in items:
        db_session.refresh(item)
    
    return items


@pytest.fixture(scope="function")
def test_file_upload(db_session, test_user):
    """Create and return a test file upload."""
    file_upload = FileUpload(
        filename="test-file.txt",
        original_filename="original-test-file.txt",
        content_type="text/plain",
        size=1024,
        bucket_name="test-bucket",
        object_name="test-object",
        owner_id=test_user.id
    )
    db_session.add(file_upload)
    db_session.commit()
    db_session.refresh(file_upload)
    return file_upload


@pytest.fixture(scope="function")
def test_file_selection(db_session, test_user, test_file_upload):
    """Create and return a test file selection."""
    file_selection = FileSelection(
        name="Test File Selection",
        description="Test file selection description",
        file_upload_id=test_file_upload.id,
        target_path="/etc/app/config.txt",
        permissions="0644",
        owner_id=test_user.id
    )
    db_session.add(file_selection)
    db_session.commit()
    db_session.refresh(file_selection)
    return file_selection


@pytest.fixture(scope="function")
def test_vagrant_vm(db_session, test_user):
    """Create and return a test Vagrant VM."""
    vm = VagrantVM(
        name="Test VM",
        template="ubuntu/focal64",
        status="created",
        cpu_count=2,
        memory_mb=1024,
        owner_id=test_user.id
    )
    db_session.add(vm)
    db_session.commit()
    db_session.refresh(vm)
    return vm


@pytest.fixture(scope="function")
def test_vm_injection(db_session, test_user, test_vagrant_vm, test_file_selection):
    """Create and return a test VM injection."""
    vm_injection = VMInjection(
        description="Test VM Injection",
        vagrant_vm_id=test_vagrant_vm.id,
        file_selection_id=test_file_selection.id,
        owner_id=test_user.id,
        status="pending"
    )
    db_session.add(vm_injection)
    db_session.commit()
    db_session.refresh(vm_injection)
    return vm_injection 