# Model Relationship Integration Tests

This directory contains integration tests for database model relationships in the TurdParty application.

## Test Approach

These tests verify the integrity and behavior of model relationships at the database level. The tests are designed to cover four key aspects of database relationships:

1. **T20.1: Model Relationships** - Testing that relationships load correctly in both directions
2. **T20.2: Cascade Operations** - Testing that cascade delete/update operations work correctly
3. **T20.3: Constraints** - Testing that database constraints are enforced appropriately
4. **T20.4: Data Integrity** - Testing that data integrity is maintained under various operations

## Test Structure

### Test Fixtures

Common test fixtures in `conftest.py` provide:

- In-memory SQLite database for testing
- SQLAlchemy session for database operations
- Test users, items, file uploads, and VMs

### Relationship Tests

The test suite includes tests for these key relationships:

1. **User-Item Relationships**
   - One-to-many relationship
   - Owner has multiple items
   - Items belong to a single owner
   - CASCADE delete

2. **FileUpload-FileSelection Relationships**
   - One-to-many relationship
   - One file upload can have multiple file selections
   - Each file selection belongs to a single file upload
   - CASCADE delete

3. **VM Injection Relationships**
   - Many-to-one relationships with VMs and File Selections
   - VMs can have multiple injections
   - File Selections can have multiple injections
   - CASCADE delete from both parents

4. **User-VM Relationships**
   - One-to-many relationship
   - User owns multiple VMs
   - Each VM belongs to a single owner
   - CASCADE delete

## Running Tests

Execute the tests using pytest:

```bash
# Run all integration tests
pytest api/tests/integration/

# Run a specific test file
pytest api/tests/integration/test_user_item_relationships.py

# Run with verbose output
pytest -v api/tests/integration/
```

## Test Categories

### Relationship Loading Tests

These tests verify that relationships can be navigated in both directions:

```python
# Example: User → Items relationship
assert len(test_user.items) == 3

# Example: Item → User relationship
assert item.owner.username == test_user.username
```

### Cascade Operation Tests

These tests verify that cascading deletes/updates work correctly:

```python
# Delete parent object
db_session.delete(test_user)
db_session.commit()

# Verify child objects are deleted
item = db_session.query(Item).filter(Item.id == item_id).first()
assert item is None
```

### Constraint Tests

These tests verify that database constraints are properly enforced:

```python
# Try to create object with non-existent foreign key
with pytest.raises(IntegrityError):
    db_session.commit()
```

### Data Integrity Tests

These tests verify that data remains consistent through various operations:

```python
# Verify all expected items exist with correct data
for i, item in enumerate(test_user.items):
    assert item.title.startswith("Test Item")
    assert item.owner_id == test_user.id
```

## Edge Cases Covered

- Transfer ownership between users
- Multiple child objects per parent
- Status changes and lifecycle operations
- Unique constraints
- Rollback behavior during failures
- Different ownership across related objects 