# TurdParty Test Infrastructure

This document describes the test infrastructure for the TurdParty application and provides guidance on running tests.

## Test Architecture

The test infrastructure is designed to be flexible and support various test environments:

1. **Docker Container Tests**: Tests run inside Docker containers as part of CI/CD pipeline
2. **Local Development Tests**: Tests run on a developer's machine with SQLite fallback
3. **Integration Tests**: End-to-end tests that test the full application stack

## Database Configuration

Tests can use either PostgreSQL or SQLite:

- **PostgreSQL**: Used by default when available, required for some advanced features
- **SQLite**: Used as a fallback when PostgreSQL is not available

The test framework automatically detects database availability and configures tests accordingly.

## Test Markers

The following pytest markers are available:

- `no_db_required`: Tests that don't require a database connection
- `skip_db_setup`: Tests that don't need database schema setup
- `schemas_only`: Tests that only validate schema definitions
- `sqlite_ok`: Tests that can run with SQLite (most basic tests)
- `postgres_required`: Tests that specifically require PostgreSQL features

## Running Tests

### Using the Test Script

The recommended way to run tests is using the `scripts/run-tests.sh` script:

```bash
# Run all tests with automatic database detection
./scripts/run-tests.sh

# Run tests with SQLite
./scripts/run-tests.sh --sqlite

# Run tests that require PostgreSQL
./scripts/run-tests.sh --postgres --mark postgres_required

# Run specific test files
./scripts/run-tests.sh --test test_file_upload

# Run with verbose output
./scripts/run-tests.sh --verbose
```

### Using pytest Directly

You can also run tests directly with pytest:

```bash
# Run all tests
python -m pytest

# Run specific tests
python -m pytest api/tests/test_file_upload_api.py

# Run tests with specific markers
python -m pytest -m sqlite_ok
```

## Environment Setup

Before running tests, ensure the test environment is properly set up:

1. Run the setup script to configure host entries:
   ```bash
   ./scripts/setup-test-environment.sh
   ```

2. Make sure Docker containers are running:
   ```bash
   docker-compose -f .dockerwrapper/docker-compose.playwright.yml up -d
   ```

## Mocking External Services

The test framework mocks several external services:

- **MinIO**: Mocked for file storage operations
- **Authentication**: Bypassed in test mode
- **External APIs**: Mocked as needed

## Writing Tests

When writing new tests:

1. Use appropriate markers to indicate database requirements
2. Add `sqlite_ok` marker to tests that don't need PostgreSQL-specific features
3. Use fixtures from `conftest.py` for database and application setup
4. Mock external dependencies where appropriate

Example test with SQLite support:

```python
@pytest.mark.sqlite_ok
def test_create_user(db):
    user = User(
        username="test_user",
        email="<EMAIL>"
    )
    db.add(user)
    db.commit()
    
    assert db.query(User).filter_by(username="test_user").first() is not None
```

Example test requiring PostgreSQL:

```python
@pytest.mark.postgres_required
def test_jsonb_feature(db):
    # This test uses PostgreSQL-specific JSONB features
    result = db.execute(text("SELECT '{\"key\": \"value\"}'::jsonb->'key' as json_value"))
    assert result.scalar() == "value"
```

# Testing Documentation

This directory contains the test suite for the API application. The tests are organized into different categories based on the testing strategy:

## Test Categories

1. **Unit Tests**
   - `test_models.py`: Tests for database models and their behaviors
   - `test_repositories.py`: Tests for repository layer (CRUD operations)
   - `test_services.py`: Tests for service layer (business logic)

2. **Integration Tests**
   - `test_api_endpoints.py`: Tests for API endpoints, verifying the API contract
   - `test_error_handling.py`: Tests for error handling scenarios

3. **End-to-End Tests**
   - `test_e2e.py`: Tests for complete workflows across multiple system components

## Test Configuration

- `conftest.py`: Contains pytest fixtures and test configuration
- `test_client.py`: Provides a configured FastAPI test client

## Running Tests

To run the entire test suite:

```bash
pytest api/tests
```

To run specific categories of tests:

```bash
# Unit tests only
pytest api/tests/test_models.py api/tests/test_repositories.py api/tests/test_services.py

# Integration tests only
pytest api/tests/test_api_endpoints.py api/tests/test_error_handling.py

# E2E tests only
pytest api/tests/test_e2e.py
```

To run tests with coverage reporting:

```bash
pytest --cov=api api/tests
```

## Testing Philosophy

1. **Unit Testing**: Focuses on testing individual components in isolation
   - Should be fast and comprehensive
   - Use mocks and stubs to isolate the component being tested

2. **Integration Testing**: Tests how components work together
   - Tests the API contract and error handling
   - Uses a test database to verify database operations

3. **E2E Testing**: Tests complete user workflows
   - Simulates real user interactions with the system
   - Catches integration issues that may not be found in unit tests

# API Test Suite

This directory contains tests for the API.

## Running Tests Without Authentication

There are two ways to run the API tests without the auth-middleware requiring a token:

### 1. Using the run_tests_without_auth.py script

The simplest way is to use the provided script:

```bash
# Run all tests
./run_tests_without_auth.py

# Run specific tests
./run_tests_without_auth.py api/tests/routes/test_users.py
```

### 2. Manually enabling test mode

You can also manually enable test mode in your test files:

```python
# Import and enable test mode
from api.core.test_config import test_settings
test_settings.enable_test_mode()

# Your test code here
```

## How It Works

The application includes an `AuthMiddleware` that checks for JWT tokens in request headers. However, it also checks for a test mode flag using `test_settings.is_testing()`. When test mode is enabled, the middleware bypasses authentication checks.

The test configuration in `conftest.py` already enables test mode for all tests, so authentication should be bypassed automatically when running tests through pytest.

## Troubleshooting

If you're still experiencing authentication issues in tests:

1. Make sure the test mode is enabled before the application is created
2. Check that the `AuthMiddleware` is correctly checking for test mode
3. Verify that your test client is using the application with test mode enabled

# Vagrant VM Testing Suite

This directory contains comprehensive tests for the Vagrant VM functionality in the TurdParty application. The tests cover various aspects of the system, from API endpoints to service implementation and gRPC communication.

## Test Structure

The testing suite is organized into several components:

1. **API Tests** (`test_vagrant_vm_api.py`): Tests the REST API endpoints for Vagrant VM operations, including creating, listing, retrieving, updating, and deleting VMs.

2. **Service Tests** (`test_vagrant_vm_service.py`): Tests the `VagrantVMService` implementation, focusing on the VM creation and startup process.

3. **gRPC Client Tests** (`test_vagrant_grpc_client.py`): Tests the client-side communication with the Vagrant gRPC server.

4. **gRPC Server Tests** (`test_vagrant_grpc_server.py`): Tests the server-side implementation of the Vagrant gRPC service.

5. **UI Tests** (`turdparty-app/tests/playwright/vagrant-vm-form-submission.spec.ts`): Tests the user interface for VM management using Playwright.

## Running the Tests

### Running All Tests

To run all tests and generate a comprehensive report, use the `run_vagrant_tests.py` script:

```bash
python api/tests/run_vagrant_tests.py
```

This script will:
- Run all the test files
- Analyze the test logs
- Generate a detailed report in both JSON and human-readable formats
- Print a summary of the test results

### Running Individual Tests

#### Python Tests

To run individual Python tests, use pytest:

```bash
# Run API tests
python -m pytest api/tests/test_vagrant_vm_api.py -v

# Run Service tests
python -m pytest api/tests/test_vagrant_vm_service.py -v

# Run gRPC Client tests
python -m pytest api/tests/test_vagrant_grpc_client.py -v

# Run gRPC Server tests
python -m pytest api/tests/test_vagrant_grpc_server.py -v
```

#### UI Tests

To run the Playwright UI tests:

```bash
cd turdparty-app
npx playwright test tests/playwright/vagrant-vm-form-submission.spec.ts
```

## Test Reports

After running the tests using the `run_vagrant_tests.py` script, you can find the following reports in the `tests/logs` directory:

- `vagrant_test_report.txt`: Human-readable report with a summary of test results, component statistics, potential failure points, and errors.
- `vagrant_test_report.json`: JSON-formatted report with detailed information about test results and log analysis.

Additionally, each test component generates its own log file:

- `vagrant_vm_api_tests.log`: Logs from API tests
- `vagrant_vm_service_tests.log`: Logs from Service tests
- `vagrant_grpc_client_tests.log`: Logs from gRPC Client tests
- `vagrant_grpc_server_tests.log`: Logs from gRPC Server tests
- `vagrant_vm_form_submission.log`: Logs from UI tests

## Test Coverage

The tests cover the following aspects of the Vagrant VM functionality:

### API Tests
- Creating a new VM
- Listing all VMs
- Retrieving a specific VM
- Updating VM properties
- Deleting a VM
- Error handling for various scenarios
- Authentication and authorization

### Service Tests
- VM creation process
- VM startup process
- Error handling during VM creation and startup
- Database operations

### gRPC Client Tests
- Connection to the gRPC server
- VM operations (up, halt, destroy)
- Status retrieval
- Command execution
- Box listing

### gRPC Server Tests
- Command execution
- RPC method implementations
- Error handling

### UI Tests
- Form submission for VM creation
- VM management interface
- Error handling in the UI

## Troubleshooting

If you encounter issues with the tests, check the following:

1. **Log Files**: Examine the log files in the `tests/logs` directory for detailed error messages.

2. **Dependencies**: Ensure all required dependencies are installed.

3. **Environment**: Verify that the environment variables for the Vagrant API key and server address are set correctly.

4. **Permissions**: Check that the user running the tests has the necessary permissions to execute Vagrant commands.

5. **Network**: Ensure that the gRPC server is accessible from the test environment.

## Adding New Tests

When adding new tests, follow these guidelines:

1. **Naming Convention**: Use descriptive names for test methods that clearly indicate what is being tested.

2. **Logging**: Include detailed logging statements to aid in debugging.

3. **Mocking**: Use mocks for external dependencies to ensure tests are isolated and repeatable.

4. **Error Handling**: Test both success and error scenarios.

5. **Documentation**: Update this README with information about new test files or functionality.

## Contributing

When contributing to the testing suite, please ensure that your changes maintain or improve the current level of test coverage. All pull requests should include appropriate tests for new functionality or bug fixes.

# Test Strategy

This directory contains tests for the API components.

## Database-Independent Tests

Some tests are designed to run independently of the database connection to avoid test failures when the database is unavailable or has connectivity issues.

### Service Connector Tests

The `test_service_connector.py` file contains tests for the `ServiceConnector` class, which is used for API communication. These tests have been designed to run without requiring a database connection by:

1. Using a custom mock implementation in `mock_service_connector.py` that doesn't depend on Streamlit or other UI components
2. Using the `skipif` pytest marker to avoid database connection attempts
3. Providing a standalone test runner (`run_service_connector_tests.py`) that can execute the tests without using pytest

To run the service connector tests independently:

```bash
python api/tests/run_service_connector_tests.py
```

### Schema Tests

Schema validation tests have been isolated from database dependencies using the `schemas_only` marker. This allows these tests to run even when the database is unavailable.

To run all schema tests:

```bash
pytest -v api/tests/schemas
```

### Unit Tests

Unit tests that don't require database access have been marked with the `no_db_required` marker.

### Test Database Configuration

For tests that do require a database, the connection is configured in `conftest.py`. The test suite can use either:

1. SQLite (default) - Set by the `TEST_DB_URL` environment variable
2. PostgreSQL - When explicitly configured 

If you need to skip database setup for specific tests, you can use one of these markers:

```python
@pytest.mark.skip_db_setup
@pytest.mark.no_db_required
@pytest.mark.schemas_only
def test_something():
    # This test will not attempt to connect to the database
    pass
```

## Running All Working Tests

To run all tests that have been fixed to work independently of database connections:

```bash
./api/tests/run_working_tests.sh
```

This will run:
- All schema tests
- Database dependency unit tests
- Service connector tests

## Dependencies

The application has separate dependencies for:

1. API components (FastAPI, SQLAlchemy, etc.)
2. UI components (Streamlit, etc.)
3. Development tools (pytest, flake8, etc.)

These are installed separately in the Dockerfile to ensure clean separation.

## Running Tests

To run all tests with coverage:

```bash
pytest -v --cov=api --cov-report=term-missing
```

To run specific test files:

```bash
pytest -v api/tests/test_file.py
```
