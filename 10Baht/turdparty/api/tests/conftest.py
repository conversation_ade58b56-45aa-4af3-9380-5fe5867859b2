"""Test configuration and fixtures for pytest."""
import os
# Set test mode environment variable before any imports
os.environ["API_TEST_MODE"] = "true"

import pytest
import logging
from typing import Generator, Dict, Any, List, AsyncGenerator
from uuid import uuid4
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
import jwt
from minio import Minio
from unittest.mock import MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool
import asyncio
import socket
from httpx import AsyncClient

import sys

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import and enable test mode
from api.core.test_config import test_settings
# Enable test mode to bypass authentication for all tests
test_settings.enable_test_mode()
logger = logging.getLogger(__name__)
logger.info("Test mode enabled for all tests - authentication bypassed")

from api.db.base import Base
from api.core.logging_config import setup_logging
from api.application import get_application
from api.core.config import settings
from api.db.models.user import User
from api.db.models.item import Item
from api.core.security import get_password_hash, ALGORITHM
from api.routes.minio_ssh_wrapper import get_minio_client
from api.core.dependencies import get_db

# Define markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "skip_db_setup: mark test to skip database setup")
    config.addinivalue_line("markers", "no_db_required: mark test that doesn't require database")
    config.addinivalue_line("markers", "schemas_only: mark test for schema validation only")
    config.addinivalue_line("markers", "sqlite_ok: mark test that can use SQLite instead of PostgreSQL")
    config.addinivalue_line("markers", "postgres_required: mark test that specifically requires PostgreSQL")
    config.addinivalue_line("markers", "minio_required: mark test that specifically requires real MinIO service")
    config.addinivalue_line("markers", "mock_ok: mark test that can use mock MinIO implementation")

# Set up test logging
logger = setup_logging(log_level=logging.DEBUG)

# Helper function to check if a host is reachable
def is_host_reachable(host, port, timeout=2):
    """Check if a host is reachable at the given port."""
    try:
        socket.setdefaulttimeout(timeout)
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.connect((host, port))
        s.close()
        return True
    except (socket.timeout, socket.error):
        return False

# Determine if we can connect to PostgreSQL or need to use SQLite
DB_HOST = os.getenv("POSTGRES_HOST", "db")
DB_PORT = int(os.getenv("POSTGRES_PORT", "5432"))
USE_SQLITE = os.getenv("USE_SQLITE", "false").lower() in ("true", "1", "yes")

if not USE_SQLITE:
    # Try to autodetect if PostgreSQL is reachable
    postgres_reachable = is_host_reachable(DB_HOST, DB_PORT)
    if not postgres_reachable:
        logger.warning(f"PostgreSQL at {DB_HOST}:{DB_PORT} is not reachable, using SQLite fallback")
        USE_SQLITE = True
        # Try localhost as fallback
        if is_host_reachable("localhost", DB_PORT):
            DB_HOST = "localhost"
            USE_SQLITE = False
            logger.info(f"PostgreSQL is reachable at localhost:{DB_PORT}, using it instead")
        # Try the Docker container IP address
        elif "postgres_host_ip" in os.environ and is_host_reachable(os.environ["postgres_host_ip"], DB_PORT):
            DB_HOST = os.environ["postgres_host_ip"]
            USE_SQLITE = False
            logger.info(f"PostgreSQL is reachable at {DB_HOST}:{DB_PORT}, using it")

# Set database URL based on availability
if USE_SQLITE:
    logger.info("Using SQLite for testing")
    TEST_DB_URL = "sqlite:///./test_db.sqlite"
    SQLITE_CONNECT_ARGS = {"check_same_thread": False}
else:
    logger.info(f"Using PostgreSQL for testing at {DB_HOST}:{DB_PORT}")
    TEST_DB_URL = os.environ.get(
        "TEST_DB_URL", f"postgresql://postgres:postgres@{DB_HOST}:{DB_PORT}/app"
    )
    SQLITE_CONNECT_ARGS = {}

# Override database URLs for testing
settings.DATABASE_URL = TEST_DB_URL
settings.SYNC_DATABASE_URL = TEST_DB_URL
if USE_SQLITE:
    settings.DATABASE_URL_ASYNC = TEST_DB_URL
else:
    settings.DATABASE_URL_ASYNC = TEST_DB_URL.replace("postgresql://", "postgresql+asyncpg://")

# Use PostgreSQL for testing
# TEST_DB_URL = os.environ.get("TEST_DB_URL", "********************************************/app")

# Override database URLs for testing
# settings.DATABASE_URL = TEST_DB_URL
# settings.SYNC_DATABASE_URL = TEST_DB_URL
# settings.DATABASE_URL_ASYNC = TEST_DB_URL.replace("postgresql://", "postgresql+asyncpg://")

# Create test database engine
test_engine = create_engine(
    TEST_DB_URL,
    pool_pre_ping=True,
    pool_size=5,
    max_overflow=10,
    pool_timeout=30,
    connect_args=SQLITE_CONNECT_ARGS
)

# Create test async engine 
if USE_SQLITE:
    test_async_engine = create_engine(
        TEST_DB_URL,
        pool_pre_ping=True,
        connect_args=SQLITE_CONNECT_ARGS
    )
else:
    test_async_engine = create_async_engine(
        settings.DATABASE_URL_ASYNC,
        pool_pre_ping=True,
        pool_size=5,
        max_overflow=10,
        pool_timeout=30
    )

# Create test session factories
TestingSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=test_engine
)

AsyncTestingSessionLocal = sessionmaker(
    class_=AsyncSession if not USE_SQLITE else Session,
    autocommit=False,
    autoflush=False,
    bind=test_async_engine,
    expire_on_commit=False
)

def is_db_test(request):
    """Check if test requires database."""
    marker = request.node.get_closest_marker("no_db_required")
    return marker is None

def is_postgres_required(request):
    """Check if test specifically requires PostgreSQL."""
    marker = request.node.get_closest_marker("postgres_required")
    return marker is not None

def can_use_sqlite(request):
    """Check if test can use SQLite instead of PostgreSQL."""
    marker = request.node.get_closest_marker("sqlite_ok")
    return marker is not None

@pytest.fixture(scope="session")
def engine():
    """Get test database engine."""
    return test_engine

@pytest.fixture(scope="session")
def db_session():
    """Create test database session."""
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()

@pytest.fixture(scope="session", autouse=True)
def setup_test_database(request):
    """Set up test database."""
    # Skip database setup if marked appropriately
    if not is_db_test(request):
        yield
        return
    
    # Skip if this test requires PostgreSQL but we're using SQLite
    if USE_SQLITE and is_postgres_required(request) and not can_use_sqlite(request):
        pytest.skip("This test requires PostgreSQL, but SQLite is being used")
        return
    
    try:
        # Import all models to ensure they are registered with Base.metadata
        from api.db.models.item import Item
        from api.db.models.user import User
        from api.db.models.file_upload import FileUpload
        from api.db.models.session import Session
        from api.db.models.hash_report import HashReport
        from api.db.models.vagrant_vm import VagrantVM
        from api.db.models.file_selection import FileSelection
        from api.db.models.vm_injection import VMInjection
        
        # Drop all tables first to ensure clean state
        logger.info("Dropping all existing tables...")
        Base.metadata.drop_all(bind=test_engine)
        
        # Create all tables
        logger.info("Creating test database tables...")
        Base.metadata.create_all(bind=test_engine)
        logger.info("Test database tables created successfully")
        
        yield
        
        # Drop all tables after tests
        logger.info("Dropping test database tables...")
        Base.metadata.drop_all(bind=test_engine)
        logger.info("Test database tables dropped successfully")
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        raise

@pytest.fixture
def db(request) -> Generator:
    """Get database session."""
    # Skip database connection if marked appropriately
    if not is_db_test(request):
        yield None
        return
    
    # Skip if this test requires PostgreSQL but we're using SQLite
    if USE_SQLITE and is_postgres_required(request) and not can_use_sqlite(request):
        pytest.skip("This test requires PostgreSQL, but SQLite is being used")
        return
    
    try:    
        connection = test_engine.connect()
        transaction = connection.begin()
        session = TestingSessionLocal(bind=connection)
        
        yield session
        
        session.close()
        transaction.rollback()
        connection.close()
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise

@pytest.fixture
async def async_db(request) -> AsyncGenerator:
    """Get async database session."""
    if not is_db_test(request):
        yield None
        return
        
    # Skip if this test requires PostgreSQL but we're using SQLite
    if USE_SQLITE and is_postgres_required(request) and not can_use_sqlite(request):
        pytest.skip("This test requires PostgreSQL, but SQLite is being used")
        return
        
    try:
        # For SQLite, we need to use a synchronous session as SQLite doesn't support asyncio
        if USE_SQLITE:
            connection = test_engine.connect()
            transaction = connection.begin()
            session = TestingSessionLocal(bind=connection)
            
            yield session
            
            session.close()
            transaction.rollback()
            connection.close()
        else:
            async with AsyncTestingSessionLocal() as session:
                yield session
    except Exception as e:
        logger.error(f"Async database connection failed: {e}")
        raise

@pytest.fixture
def test_app(db, request):
    """Create test app with overridden dependencies."""
    def override_get_db():
        try:
            yield db
        finally:
            pass

    # Determine if we need real MinIO or can use a mock
    requires_real_minio = request.node.get_closest_marker("minio_required") is not None
    can_use_mock = request.node.get_closest_marker("mock_ok") is not None

    async def override_get_minio_client():
        """Get MinIO client - tries real service first, falls back to mock."""
        try:
            # Use an environment variable to force mock or real service
            use_mock = os.getenv("USE_MOCK_MINIO", "auto").lower()
            
            # If test requires real MinIO and we're configured to use mock, skip the test
            if requires_real_minio and use_mock == "true":
                pytest.skip("This test requires real MinIO service, but mock is forced by environment")
                
            # If test requires real MinIO or we're configured to use real service
            if requires_real_minio or use_mock == "false":
                # Try to connect to the real MinIO service
                import socket
                from api.services.minio_ssh_client import MinIOSSHClient
                
                # Check if MinIO is reachable
                minio_host = os.getenv("MINIO_HOST", "minio")
                minio_port = int(os.getenv("MINIO_PORT", "9000"))
                
                # Try to detect MinIO container IP
                minio_reachable = False
                try:
                    socket.setdefaulttimeout(2)
                    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    s.connect((minio_host, minio_port))
                    s.close()
                    minio_reachable = True
                except (socket.timeout, socket.error):
                    # Try with container IP if available
                    if "minio_host_ip" in os.environ:
                        try:
                            minio_host = os.environ["minio_host_ip"]
                            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                            s.connect((minio_host, minio_port))
                            s.close()
                            minio_reachable = True
                        except (socket.timeout, socket.error):
                            pass
                
                if minio_reachable:
                    logger.info(f"Using real MinIO service at {minio_host}:{minio_port}")
                    client = MinIOSSHClient(
                        host=minio_host,
                        port=minio_port,
                        username=os.getenv("MINIO_SSH_USER", "root"),
                        pkey_path=os.path.expanduser(os.getenv("MINIO_SSH_KEY_PATH", "~/.ssh/id_rsa"))
                    )
                    
                    # Verify connection works
                    server_status = await client.get_server_status()
                    if server_status.get("status") == "success":
                        yield client
                        await client.close()
                        return
                    else:
                        logger.warning(f"MinIO connection test failed: {server_status}")
                        await client.close()
                
                # If we require real MinIO but it's not available, skip the test
                if requires_real_minio:
                    pytest.skip("This test requires real MinIO service, but it's not available")
                    return
            
            # If test explicitly allows mock or real service wasn't available
            if can_use_mock or use_mock == "true" or use_mock == "auto":
                # Use mock client
                logger.info("Using mock MinIO client")
                from api.tests.mocks.minio_mock import get_minio_mock_client
                mock_client = await get_minio_mock_client()
                yield mock_client
                await mock_client.close()
                return
                
            # If we get here, the test requires real MinIO which isn't available and mock isn't allowed
            pytest.skip("This test requires real MinIO service, and mock isn't allowed")
                
        except Exception as e:
            logger.error(f"Error setting up MinIO client: {str(e)}")
            # Final fallback - if mock is allowed, provide it
            if can_use_mock:
                logger.warning("Falling back to mock MinIO client due to error")
                from api.tests.mocks.minio_mock import get_minio_mock_client
                mock_client = await get_minio_mock_client()
                yield mock_client
                await mock_client.close()
            else:
                pytest.skip(f"Failed to setup MinIO client: {str(e)}")

    app = get_application()
    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_minio_client] = override_get_minio_client
    return app

@pytest.fixture
async def client(test_app):
    """Create async test client."""
    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client

@pytest.fixture
def token_headers(client: AsyncClient, test_user: User) -> Dict[str, str]:
    """Create authentication headers with test user token."""
    access_token = create_access_token(test_user.id)
    return {"Authorization": f"Bearer {access_token}"}

@pytest.fixture
def superuser_token_headers(client: AsyncClient, test_superuser: User) -> Dict[str, str]:
    """Create authentication headers with superuser token."""
    access_token = create_access_token(test_superuser.id)
    return {"Authorization": f"Bearer {access_token}"}

def create_access_token(user_id: uuid4) -> str:
    """Create a test access token."""
    to_encode = {"sub": str(user_id)}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

@pytest.fixture
def test_user(db: Session) -> User:
    """Create a test user."""
    user = User(
        id=uuid4(),
        email=f"test-{uuid4()}@example.com",
        username=f"test-{uuid4()}",
        full_name="Test User",
        password_hash=get_password_hash("TestPassword123"),
        is_active=True,
        is_superuser=False
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

@pytest.fixture
def test_superuser(db: Session) -> User:
    """Create a test superuser."""
    user = User(
        id=uuid4(),
        email=f"super-{uuid4()}@example.com",
        username=f"super-{uuid4()}",
        full_name="Test Superuser",
        password_hash=get_password_hash("TestPassword123"),
        is_active=True,
        is_superuser=True
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

@pytest.fixture
def mock_minio():
    """Mock MinIO client."""
    mock = MagicMock()
    mock.list_buckets.return_value = []
    return mock

@pytest.fixture(scope="function")
def test_items(db, test_user) -> List[Item]:
    """Create test items in the database."""
    items = []
    for i in range(3):
        item = Item(
            title=f"Test Item {i}",
            description=f"Test Description {i}",
            owner_id=test_user.id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(item)
        items.append(item)
    db.commit()
    return items

@pytest.fixture
def test_token():
    """Create a test JWT token."""
    expire = datetime.utcnow() + timedelta(minutes=15)
    data = {
        "sub": "1",
        "exp": expire,
        "type": "access_token"
    }
    return jwt.encode(data, settings.SECRET_KEY, algorithm=ALGORITHM)

@pytest.fixture
def expired_token():
    """Create an expired JWT token."""
    expire = datetime.utcnow() - timedelta(minutes=15)
    data = {
        "sub": "1",
        "exp": expire,
        "type": "access_token"
    }
    return jwt.encode(data, settings.SECRET_KEY, algorithm=ALGORITHM)

@pytest.fixture
def invalid_token():
    """Create an invalid JWT token."""
    return "invalid.token.string"

"""
Test fixtures and configuration.
"""
import pytest
import sys
import os
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

logger = logging.getLogger(__name__)

# Import and enable test mode
from api.core.test_config import test_settings
test_settings.enable_test_mode()
logger.info("Test mode enabled for all tests - authentication bypassed")

from api.application import get_application
from fastapi.testclient import TestClient

@pytest.fixture
def test_app():
    """Create a test FastAPI application."""
    app = get_application()
    return app

@pytest.fixture
def test_client(test_app):
    """Create a test client for the FastAPI application."""
    return TestClient(test_app, base_url="http://test")

"""
Pytest configuration file for API tests.
"""
import os
import sys
import pytest
import asyncio
import logging
from typing import Dict, Any, Generator, AsyncGenerator

# Add the project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import application function
from api.application import get_application

# Set up logging
logger = logging.getLogger(__name__)

# Create a fixture that returns the application
@pytest.fixture
def app_instance():
    """Returns the FastAPI application instance."""
    return get_application()

# Create a test client fixture
@pytest.fixture
async def client():
    """Returns an async test client for the application."""
    from httpx import AsyncClient
    app = get_application()
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

"""Shared fixtures for pytest test suite.

This module contains fixtures that can be used across multiple test files.
Fixtures defined here are automatically discovered by pytest.
"""

import os
import pytest
from typing import AsyncGenerator, Optional
from api.services.minio_ssh_client import MinIOSSHClient

# MinIO test configuration
TEST_SSH_HOST = os.getenv("TEST_MINIO_SSH_HOST", "minio-ssh")
TEST_SSH_PORT = int(os.getenv("TEST_MINIO_SSH_PORT", "2223"))
TEST_SSH_USER = os.getenv("TEST_MINIO_SSH_USER", "root")
TEST_SSH_KEY_PATH = os.getenv("TEST_MINIO_SSH_KEY_PATH", "~/.ssh/minio_ssh_key")

# MinIO credentials
TEST_MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
TEST_MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")
TEST_MINIO_PORT = int(os.getenv("MINIO_PORT", "9000"))  # Use container port for direct connection
TEST_MINIO_HOST = os.getenv("MINIO_HOST", "localhost")  # Use localhost for direct connection

@pytest.fixture(scope="session")
async def minio_client() -> AsyncGenerator[MinIOSSHClient, None]:
    """Create a MinIO SSH client for testing.

    This fixture provides a MinIO SSH client that can be used across multiple
    tests. It handles the connection setup and teardown automatically.

    Returns:
        AsyncGenerator[MinIOSSHClient, None]: A MinIO SSH client instance.

    Raises:
        RuntimeError: If the MinIO service is not available or credentials are invalid.
    """
    # Set MinIO credentials in environment
    os.environ["MINIO_ACCESS_KEY"] = TEST_MINIO_ACCESS_KEY
    os.environ["MINIO_SECRET_KEY"] = TEST_MINIO_SECRET_KEY
    os.environ["MINIO_DIRECT"] = "true"  # Enable direct connection
    os.environ["MINIO_PORT"] = str(TEST_MINIO_PORT)  # Set the correct port
    os.environ["MINIO_HOST"] = TEST_MINIO_HOST  # Use the configured host

    client = MinIOSSHClient(
        host=TEST_MINIO_HOST,  # Use the direct connection host
        port=TEST_MINIO_PORT,  # Use the direct connection port
        username=TEST_SSH_USER,
        pkey_path=os.path.expanduser(TEST_SSH_KEY_PATH)
    )

    # Check if MinIO is available before proceeding
    try:
        status = await client.get_server_status()
        if status.get("status") != "success":
            raise RuntimeError("MinIO service is not healthy")
    except Exception as e:
        raise RuntimeError(f"Failed to connect to MinIO: {str(e)}")

    yield client

    # Cleanup after all tests
    await client.close()

@pytest.fixture(scope="function")
async def test_bucket(minio_client: MinIOSSHClient) -> AsyncGenerator[str, None]:
    """Create a temporary test bucket.

    This fixture creates a unique bucket for test isolation and cleans it up
    after the test completes.

    Args:
        minio_client: The MinIO SSH client fixture.

    Returns:
        AsyncGenerator[str, None]: The name of the created bucket.

    Raises:
        RuntimeError: If bucket creation fails.
    """
    import uuid
    bucket_name = f"test-bucket-{uuid.uuid4()}"

    # Create the bucket
    result = await minio_client.create_bucket(bucket_name)
    if not result.get("success", False):
        raise RuntimeError(f"Failed to create test bucket: {result.get('error', 'Unknown error')}")

    yield bucket_name

    # Cleanup: Delete all objects and then the bucket
    try:
        objects = await minio_client.list_objects(bucket_name)
        for obj in objects:
            delete_result = await minio_client.delete_object(bucket_name, obj["name"])
            if not delete_result.get("success", False):
                print(f"Warning: Failed to delete object {obj['name']}: {delete_result.get('error', 'Unknown error')}")
        
        delete_result = await minio_client.delete_bucket(bucket_name)
        if not delete_result.get("success", False):
            print(f"Warning: Failed to delete bucket {bucket_name}: {delete_result.get('error', 'Unknown error')}")
    except Exception as e:
        print(f"Warning: Failed to cleanup test bucket {bucket_name}: {str(e)}")

@pytest.fixture(scope="function")
async def test_object(minio_client: MinIOSSHClient, test_bucket: str) -> AsyncGenerator[dict, None]:
    """Create a temporary test object in the test bucket.

    This fixture creates a test object that can be used for object operations
    testing. It handles cleanup automatically.

    Args:
        minio_client: The MinIO SSH client fixture.
        test_bucket: The test bucket fixture.

    Returns:
        AsyncGenerator[dict, None]: A dictionary containing object details.

    Raises:
        RuntimeError: If object creation fails.
    """
    object_name = "test-object.txt"
    content = b"This is test content for MinIO SSH integration tests."

    # Create a temporary file and upload it
    import tempfile
    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        temp_file.write(content)
        temp_file.flush()
        result = await minio_client.upload_object(
            test_bucket,
            object_name,
            temp_file.name
        )
        if not result.get("success", False):
            raise RuntimeError(f"Failed to create test object: {result.get('error', 'Unknown error')}")
        os.unlink(temp_file.name)

    yield {
        "bucket": test_bucket,
        "name": object_name,
        "size": len(content),
        "content": content
    }

    # Cleanup is handled by test_bucket fixture

@pytest.fixture
def mock_ssh_client():
    """Create a mock SSH client."""
    mock_client = MagicMock()
    mock_client.get_transport.return_value = MagicMock()
    return mock_client

@pytest.fixture
def mock_sftp_client():
    """Create a mock SFTP client."""
    mock_sftp = MagicMock()
    return mock_sftp

@pytest.fixture
def minio_client(mock_ssh_client, mock_sftp_client):
    """Create a MinIO SSH client with mocked dependencies."""
    with patch('api.services.minio_ssh_client.paramiko.SSHClient') as mock_ssh_class:
        mock_ssh_class.return_value = mock_ssh_client
        mock_ssh_client.open_sftp.return_value = mock_sftp_client
        
        client = MinIOSSHClient(
            host="test-host",
            port=22,
            username="test-user",
            pkey_path="test-key"
        )
        return client

@pytest.fixture
def mock_minio_command_success(mock_ssh_client):
    """Mock a successful MinIO command execution."""
    mock_stdout = MagicMock()
    mock_stdout.channel.recv_exit_status.return_value = 0
    mock_stdout.read.return_value = b"Command successful"
    
    mock_stderr = MagicMock()
    mock_stderr.read.return_value = b""
    
    mock_ssh_client.exec_command.return_value = (None, mock_stdout, mock_stderr)

@pytest.fixture
def mock_minio_command_failure(mock_ssh_client):
    """Mock a failed MinIO command execution."""
    mock_stdout = MagicMock()
    mock_stdout.channel.recv_exit_status.return_value = 1
    mock_stdout.read.return_value = b""
    
    mock_stderr = MagicMock()
    mock_stderr.read.return_value = b"Command failed"
    
    mock_ssh_client.exec_command.return_value = (None, mock_stdout, mock_stderr)
