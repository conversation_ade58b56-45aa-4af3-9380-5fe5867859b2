#!/bin/bash

# Run all the working tests in the API test suite
echo "Running all working API tests..."

# Use nix-shell to run docker-compose commands
nix-shell -p docker-compose --run "docker-compose -f .dockerwrapper/docker-compose.yml exec -T api pytest -v api/tests/schemas api/tests/test_unit/test_db_dependencies.py"

# Run the service connector tests 
echo -e "\nRunning service connector tests..."
nix-shell -p docker-compose --run "docker-compose -f .dockerwrapper/docker-compose.yml exec -T api python api/tests/run_service_connector_tests.py"

echo -e "\nAll tests completed!" 