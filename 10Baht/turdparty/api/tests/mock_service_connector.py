"""
Mock service connector for testing without Streamlit.
"""
import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type, Union, List
import requests
from requests.exceptions import RequestException

# Set up logging
logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceConnector(Generic[T]):
    """
    Base service connector for API communication.
    
    This class provides a reusable pattern for making API requests
    and handling responses in a consistent way.
    """
    
    def __init__(self, base_url: str, model_class: Optional[Type[T]] = None):
        """
        Initialize the service connector.
        
        Args:
            base_url: Base URL for the API
            model_class: Class to deserialize responses into (optional)
        """
        self.base_url = base_url.rstrip('/')
        self.model_class = model_class
        self.session = requests.Session()
        
    def _build_url(self, endpoint: str) -> str:
        """
        Build a complete URL from the base URL and endpoint.
        
        Args:
            endpoint: API endpoint
            
        Returns:
            Complete URL
        """
        endpoint = endpoint.lstrip('/')
        return f"{self.base_url}/{endpoint}"
    
    def _handle_response(self, response: requests.Response) -> Union[Dict[str, Any], List[Dict[str, Any]], T, List[T]]:
        """
        Handle API response and convert to appropriate type.
        
        Args:
            response: Response from API request
            
        Returns:
            Response data in appropriate format
            
        Raises:
            ValueError: If response status code indicates an error
        """
        response.raise_for_status()
        
        if not response.content:
            return None if response.status_code == 204 else {}
            
        data = response.json()
        
        if self.model_class is None:
            return data
        
        # Convert to model instance(s)
        if isinstance(data, list):
            return [self.model_class(**item) for item in data]
        else:
            return self.model_class(**data)
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Union[Dict[str, Any], List[Dict[str, Any]], T, List[T]]:
        """
        Send GET request to the API.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            
        Returns:
            Response data
        """
        url = self._build_url(endpoint)
        response = self.session.get(url, params=params)
        return self._handle_response(response)
    
    def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Union[Dict[str, Any], T]:
        """
        Send POST request to the API.
        
        Args:
            endpoint: API endpoint
            data: Request data
            
        Returns:
            Response data
        """
        url = self._build_url(endpoint)
        response = self.session.post(url, json=data)
        return self._handle_response(response)
    
    def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Union[Dict[str, Any], T]:
        """
        Send PUT request to the API.
        
        Args:
            endpoint: API endpoint
            data: Request data
            
        Returns:
            Response data
        """
        url = self._build_url(endpoint)
        response = self.session.put(url, json=data)
        return self._handle_response(response)
    
    def delete(self, endpoint: str) -> Union[Dict[str, Any], None]:
        """
        Send DELETE request to the API.
        
        Args:
            endpoint: API endpoint
            
        Returns:
            Response data or None for 204 responses
        """
        url = self._build_url(endpoint)
        response = self.session.delete(url)
        
        if response.status_code == 204:
            return None
            
        return self._handle_response(response) 