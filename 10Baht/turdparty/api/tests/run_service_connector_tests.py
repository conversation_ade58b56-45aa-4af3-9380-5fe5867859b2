#!/usr/bin/env python
"""
Test runner for service connector tests.
Run this script directly to test the service connector without using pytest.
"""

import sys
import os
import importlib.util
import unittest
import traceback

# Get absolute path to this script
script_path = os.path.abspath(__file__)
script_dir = os.path.dirname(script_path)

# Import the test module
spec = importlib.util.spec_from_file_location("test_service_connector", 
                                            os.path.join(script_dir, "test_service_connector.py"))
test_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_module)

# Create test cases from the test functions
class ServiceConnectorTestCase(unittest.TestCase):
    def test_initialization(self):
        test_module.test_initialization()
        
    def test_build_url(self):
        test_module.test_build_url()
        
    def test_handle_response_with_model(self):
        test_module.test_handle_response_with_model()
        
    def test_handle_response_without_model(self):
        test_module.test_handle_response_without_model()
        
    def test_get_request(self):
        test_module.test_get_request()
        
    def test_post_request(self):
        test_module.test_post_request()
        
    def test_put_request(self):
        test_module.test_put_request()
        
    def test_delete_request(self):
        test_module.test_delete_request()


if __name__ == "__main__":
    print("Running ServiceConnector tests...")
    
    # Run the tests
    test_suite = unittest.TestLoader().loadTestsFromTestCase(ServiceConnectorTestCase)
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Report results
    if result.wasSuccessful():
        print("\nAll tests passed! ServiceConnector is working correctly.")
        sys.exit(0)
    else:
        print(f"\nTests failed: {len(result.failures)} failures, {len(result.errors)} errors")
        sys.exit(1) 