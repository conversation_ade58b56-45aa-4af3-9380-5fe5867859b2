# VM Injection Test Strategies

This document outlines the strategies and best practices for running VM injection integration tests.

## Test Execution Strategies

### 1. Progressive Test Execution

Tests should be executed in a progressive manner, starting with basic functionality and moving towards more complex scenarios:

1. **Basic File Upload Tests**
   - Verify simple file uploads work
   - Test metadata storage 
   - Confirm file can be retrieved

2. **Basic VM Management Tests**
   - Verify VM can be created
   - Confirm VM status can be monitored
   - Test basic VM lifecycle operations

3. **Simple VM Injection Tests**
   - Test simple file injection
   - Verify execution status reporting
   - Confirm file existence on VM

4. **Complex Injection Scenarios**
   - Test folder structure injection
   - Test large file injection
   - Verify recursive folder injection

5. **Error Case Testing**
   - Test with stopped VMs
   - Test with invalid auth
   - Test with nonexistent files or VMs
   - Test with invalid permissions

### 2. Test Isolation

Each test should:
- Create its own test artifacts (files, folders)
- Not depend on state from previous tests
- Clean up after itself (delete temporary files, revert VM changes)
- Use fresh fixtures for each test case

### 3. Resource Management

VM integration tests are resource-intensive and should be managed carefully:

- Limit the number of concurrent VMs (prefer reusing existing VMs when possible)
- Implement timeouts to prevent tests from running indefinitely
- Use resource pooling for VMs when running multiple tests
- Apply appropriate wait times for VM operations

## Execution Rules

1. **Pre-execution Checks**
   - Verify required services are running (API, database, Vagrant)
   - Check available disk space for VM creation
   - Ensure test environment has necessary permissions

2. **Progress Monitoring**
   - Log detailed progress of each test step
   - Report intermediate assertions
   - Track time spent in each test phase

3. **Timeout Management**
   - Apply global timeout for entire test suite
   - Set individual timeouts for specific operations:
     - VM creation: 5 minutes
     - VM startup: 3 minutes
     - File injection: 2 minutes
     - File execution: varies by file (configurable)

4. **Failure Handling**
   - Capture detailed logs on failure
   - Take VM snapshots on critical failures
   - Implement retry mechanisms for transient failures
   - Report clear error messages with troubleshooting steps

## Reporting

Integration test reports should include:

1. **Test Summary**
   - Total tests executed
   - Pass/fail counts
   - Total execution time

2. **Detailed Results**
   - Execution time per test
   - Resource usage (memory, disk)
   - Error logs for failed tests

3. **VM Telemetry**
   - VM startup time
   - Injection operation timing
   - Command execution timing

4. **Artifacts**
   - Links to saved log files
   - File verification results
   - Reference to VM snapshots (if applicable)

## Maintenance Guidelines

1. **Test Optimization**
   - Regularly review test execution times
   - Identify and optimize slow tests
   - Consider parallelizing independent tests

2. **Test Coverage**
   - Ensure all API endpoints are covered
   - Verify both happy and error paths
   - Add tests for new features and bug fixes

3. **Test Data Management**
   - Use small test files when possible
   - Clean up test data after suite execution
   - Implement rotation for test artifacts 