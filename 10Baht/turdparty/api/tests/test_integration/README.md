# VM Injection Integration Tests

This directory contains integration tests for the VM injection functionality, testing the entire workflow from file upload to VM injection across different operating systems.

## Overview

The test suite verifies:
- File upload functionality
- VM management (creation, starting, status monitoring)
- File injection into VMs
- Script execution in VMs
- Folder structure injection
- Error handling scenarios
- Cross-platform testing (Ubuntu and Windows)

## Test Files

- `test_file_upload_to_vm_injection.py`: Tests basic file upload and injection workflow
- `test_folder_upload_to_vm_injection.py`: Tests folder structure upload and injection
- `test_vm_injection_error_handling.py`: Tests various error scenarios (non-existent files, stopped VMs, etc.)
- `test_cross_platform_vm_injection.py`: Tests VM injection across both Ubuntu and Windows VMs

## Configuration

The `vm_injection_test_config.py` file contains configuration settings for:
- VM definitions (Ubuntu and Windows)
- Test script templates
- Timeout settings
- VM pooling configuration
- Test execution parameters

## Prerequisites

Before running the tests, ensure:
1. Docker services are running (API, PostgreSQL, MinIO)
2. You have sufficient resources to run VMs (at least 2GB RAM per VM)
3. Vagrant is installed and configured
4. VirtualBox (or another supported provider) is installed

## Utility Scripts

### VM Setup Script

The `setup_test_vms.py` script helps set up Ubuntu and Windows VMs for testing:

```bash
# Set up both Ubuntu and Windows VMs
python -m api.tests.test_integration.setup_test_vms

# Set up only Ubuntu VM
python -m api.tests.test_integration.setup_test_vms --ubuntu

# Set up only Windows VM
python -m api.tests.test_integration.setup_test_vms --windows

# Force creation of new VMs
python -m api.tests.test_integration.setup_test_vms --force

# Save VM IDs to environment file
python -m api.tests.test_integration.setup_test_vms --save

# Export detailed VM information to JSON files
python -m api.tests.test_integration.setup_test_vms --export
```

### Test Result Analysis

The `analyze_test_results.py` script helps analyze test results and generate reports:

```bash
# Analyze latest test results
python -m api.tests.test_integration.analyze_test_results

# Analyze specific JUnit XML report file
python -m api.tests.test_integration.analyze_test_results --xml path/to/report.xml

# Analyze with specific log file
python -m api.tests.test_integration.analyze_test_results --log path/to/logfile.log

# Generate reports in all formats (text, CSV, JSON)
python -m api.tests.test_integration.analyze_test_results --all-formats

# Generate specific format report
python -m api.tests.test_integration.analyze_test_results --text-output report.txt
python -m api.tests.test_integration.analyze_test_results --csv-output report.csv
python -m api.tests.test_integration.analyze_test_results --json-output report.json
```

## Running the Tests

Use the `run_vm_injection_integration_tests.py` script to execute the tests:

```bash
# Run all tests
python -m api.tests.run_vm_injection_integration_tests

# Run only Ubuntu tests
python -m api.tests.run_vm_injection_integration_tests --platform ubuntu

# Run only Windows tests
python -m api.tests.run_vm_injection_integration_tests --platform windows

# Run only cross-platform tests
python -m api.tests.run_vm_injection_integration_tests --platform cross

# Generate HTML report
python -m api.tests.run_vm_injection_integration_tests --html-report

# Specify VM IDs to use (instead of creating new VMs)
python -m api.tests.run_vm_injection_integration_tests --ubuntu-vm-id <ID> --windows-vm-id <ID>

# Skip service checks
python -m api.tests.run_vm_injection_integration_tests --skip-checks
```

## Test Strategies

See the [test_strategies.md](test_strategies.md) file for detailed information about:
- Progressive test execution
- Test isolation
- Resource management
- Execution rules
- Timeout management
- Failure handling
- Reporting guidelines

## Troubleshooting

If you encounter issues while running the tests, see the [TROUBLESHOOTING.md](TROUBLESHOOTING.md) guide, which covers:
- Prerequisites and environment issues
- VM-related problems
- Test framework troubleshooting
- Injection-specific issues
- Advanced diagnostics techniques

## Adding New Tests

When adding new tests:
1. Follow the existing pattern of using pytest fixtures
2. Leverage the config file for platform-specific settings
3. Implement proper cleanup in fixtures
4. Add detailed logging
5. Include assertions for each step
6. Handle timeouts appropriately

## Windows VM Specifics

For Windows VMs:
- Use batch scripts (.bat) instead of shell scripts
- Use Windows-style paths (C:\path\to\file)
- Consider Windows-specific commands and environment variables
- Account for slower operations on Windows VMs (increase timeouts)
- Use WinRM instead of SSH for communication

## Ubuntu VM Specifics

For Ubuntu VMs:
- Make shell scripts executable (chmod +x)
- Use Linux-style paths (/path/to/file)
- Consider filesystem permissions
- Use SSH for communication

## PRD Reference

These tests implement the scenarios detailed in the [VM Injection Integration Testing PRD](../../docs/testing/vm_injection_integration_testing_prd.md). 