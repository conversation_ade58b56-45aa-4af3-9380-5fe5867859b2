"""
Configuration settings for VM injection integration tests.

This module contains the configuration for VM test environments,
including definitions for Ubuntu and Windows VMs used in tests.
"""
from typing import Dict, List, Optional, Union
import os
from pathlib import Path

# Base directory for test resources
BASE_DIR = Path(os.path.dirname(os.path.abspath(__file__)))

# VM configurations
VM_CONFIGS = {
    # Ubuntu VM configuration
    "ubuntu": {
        "name": "test-ubuntu-vm",
        "box": "generic/ubuntu2004",  # Ubuntu 20.04 LTS
        "memory": 2048,  # MB
        "cpus": 2,
        "provider": "libvirt",
        "default_username": "vagrant",
        "default_password": "vagrant",
        "is_windows": False,
        "ssh_port": 2222,
        "default_inject_path": "/home/<USER>/injected",
        "shared_folder_path": "/vagrant",
        "test_paths": {
            "scripts": "/tmp/test_scripts",
            "configs": "/etc/test_configs",
            "data": "/home/<USER>/test_data"
        }
    },
    
    # Windows VM configuration
    "windows": {
        "name": "test-windows-vm",
        "box": "generic/windows10",  # Windows 10
        "memory": 4096,  # MB
        "cpus": 2,
        "provider": "libvirt",
        "default_username": "vagrant",
        "default_password": "vagrant",
        "is_windows": True,
        "winrm_port": 5985,
        "default_inject_path": "C:\\Users\\<USER>\\injected",
        "shared_folder_path": "C:\\vagrant",
        "test_paths": {
            "scripts": "C:\\test_scripts",
            "configs": "C:\\test_configs",
            "data": "C:\\Users\\<USER>\\test_data"
        }
    }
}

# Test file configurations
TEST_FILES = {
    "basic_script": {
        "ubuntu": {
            "content": """#!/bin/bash
echo "Basic test script running on Ubuntu"
echo "Hostname: $(hostname)"
echo "Date: $(date)"
echo "OS: $(uname -a)"
""",
            "filename": "basic_test.sh",
            "execute_path": "/tmp/basic_test.sh",
            "permissions": 0o755
        },
        "windows": {
            "content": """@echo off
echo Basic test script running on Windows
echo Hostname: %COMPUTERNAME%
echo Date: %DATE% %TIME%
echo OS: %OS%
""",
            "filename": "basic_test.bat",
            "execute_path": "C:\\test_scripts\\basic_test.bat",
            "permissions": None  # Not applicable for Windows
        }
    },
    
    "folder_structure": {
        "ubuntu": {
            "base_path": "/tmp/test_folder",
            "files": {
                "script.sh": """#!/bin/bash
echo "Main script running on Ubuntu"
echo "Current directory: $(pwd)"
bash ./config/setup.sh
""",
                "config/setup.sh": """#!/bin/bash
echo "Setting up environment on Ubuntu"
mkdir -p ./data
echo "Data directory created"
cat ./data/sample.txt
""",
                "data/sample.txt": "Sample data for Ubuntu tests\nLine 1\nLine 2\nLine 3"
            }
        },
        "windows": {
            "base_path": "C:\\test_folder",
            "files": {
                "script.bat": """@echo off
echo Main script running on Windows
echo Current directory: %CD%
call .\\config\\setup.bat
""",
                "config/setup.bat": """@echo off
echo Setting up environment on Windows
if not exist .\\data mkdir .\\data
echo Data directory created
type .\\data\\sample.txt
""",
                "data/sample.txt": "Sample data for Windows tests\nLine 1\nLine 2\nLine 3"
            }
        }
    }
}

# Test timeout settings (in seconds)
TIMEOUTS = {
    "vm_creation": 300,  # 5 minutes
    "vm_start": 180,     # 3 minutes
    "vm_stop": 120,      # 2 minutes
    "file_injection": 60,  # 1 minute
    "folder_injection": 120,  # 2 minutes
    "script_execution": {
        "default": 30,
        "long_running": 120
    },
    "test_case": 600,    # 10 minutes per test case
    "test_suite": 3600   # 1 hour for the full suite
}

# VM pooling configuration
VM_POOL = {
    "max_concurrent_vms": 2,  # Maximum number of VMs to run simultaneously
    "reuse_vms": True,        # Whether to reuse existing VMs
    "recreate_on_failure": True,  # Recreate VM if a test fails
    "vm_idle_timeout": 300    # Destroy idle VMs after 5 minutes
}

# Test execution configuration
TEST_EXEC = {
    "parallelism": 1,    # Number of tests to run in parallel
    "retries": 2,        # Number of retries for failed tests
    "progressive_order": True,  # Run tests in progressive order of complexity
    "cleanup_after_each": True,  # Clean up resources after each test
    "generate_reports": True,    # Generate detailed test reports
    "capture_vm_screenshots": True,  # Capture screenshots on failure
    "required_services": [       # Services that must be running
        "api",
        "postgres",
        "minio"
    ]
}

# Retry strategy
RETRY_STRATEGY = {
    "max_retries": 3,
    "base_wait_time": 5,  # seconds
    "backoff_factor": 2,  # exponential backoff
    "max_wait_time": 30,  # maximum wait time between retries
    "retry_on_exceptions": [
        "ConnectionError",
        "Timeout",
        "SSHException"
    ]
}

# Default test users
TEST_USERS = {
    "default": {
        "id": "e3c704f3-c398-4894-bd7b-a1d092dada04",
        "username": "test_user",
        "email": "<EMAIL>"
    },
    "admin": {
        "id": "8f9dfe9e-73c5-4f4c-b7f5-962c92c0f1a9",
        "username": "admin_user",
        "email": "<EMAIL>"
    }
}

# Path transforms for different systems
# These functions convert paths between different formats
def windows_to_linux_path(path: str) -> str:
    """Convert a Windows path to a Linux path format."""
    if path.startswith("C:"):
        path = path[2:]
    return path.replace("\\", "/")

def linux_to_windows_path(path: str) -> str:
    """Convert a Linux path to a Windows path format."""
    if path.startswith("/"):
        path = path[1:]
    # Use normal string concatenation to avoid f-string backslash issues
    return "C:\\" + path.replace("/", "\\")

# Helper functions for tests
def get_vm_config(vm_type: str) -> Dict:
    """Get VM configuration by type."""
    if vm_type not in VM_CONFIGS:
        raise ValueError(f"Unknown VM type: {vm_type}. Available types: {list(VM_CONFIGS.keys())}")
    return VM_CONFIGS[vm_type]

def get_test_file(file_type: str, vm_type: str) -> Dict:
    """Get test file configuration by type and VM type."""
    if file_type not in TEST_FILES:
        raise ValueError(f"Unknown file type: {file_type}. Available types: {list(TEST_FILES.keys())}")
    if vm_type not in TEST_FILES[file_type]:
        raise ValueError(f"No configuration for VM type {vm_type} in file type {file_type}")
    return TEST_FILES[file_type][vm_type]

def get_timeout(operation: str) -> int:
    """Get timeout for a specific operation."""
    if operation in TIMEOUTS:
        return TIMEOUTS[operation]
    elif operation.startswith("script_execution."):
        script_type = operation.split(".", 1)[1]
        if script_type in TIMEOUTS["script_execution"]:
            return TIMEOUTS["script_execution"][script_type]
        return TIMEOUTS["script_execution"]["default"]
    else:
        raise ValueError(f"Unknown timeout operation: {operation}")

def get_test_user(user_type: str = "default") -> Dict:
    """Get test user configuration by type."""
    if user_type not in TEST_USERS:
        raise ValueError(f"Unknown user type: {user_type}. Available types: {list(TEST_USERS.keys())}")
    return TEST_USERS[user_type] 