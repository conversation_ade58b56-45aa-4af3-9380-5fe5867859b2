#!/usr/bin/env python3
"""
Script to analyze VM injection test results and generate summary reports.

This script parses test results, extracts metrics, and generates
summary reports for easier analysis of test outcomes.
"""
import os
import sys
import argparse
import json
import xml.etree.ElementTree as ET
import datetime
import re
import csv
from pathlib import Path
from collections import defaultdict, Counter

# Add parent directory to path for imports
SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(str(SCRIPT_DIR.parent.parent))

# Configure logging like the other test scripts
import logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("test_analysis.log")
    ]
)
logger = logging.getLogger("test_analysis")

# Directory for test reports
REPORTS_DIR = SCRIPT_DIR.parent / "reports" / "vm_injection"


def find_latest_report(report_type="xml"):
    """Find the latest report file of the specified type."""
    if not REPORTS_DIR.exists():
        logger.error(f"Reports directory not found: {REPORTS_DIR}")
        return None
    
    pattern = f"*_report_*.{report_type}"
    report_files = list(REPORTS_DIR.glob(pattern))
    
    if not report_files:
        logger.error(f"No {report_type} report files found in {REPORTS_DIR}")
        return None
    
    # Sort by modification time, newest first
    return sorted(report_files, key=lambda f: f.stat().st_mtime, reverse=True)[0]


def parse_junit_xml(xml_file):
    """Parse a JUnit XML report file and extract test results."""
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "errors": 0,
            "skipped": 0,
            "time": 0,
            "platform_results": defaultdict(lambda: {"passed": 0, "failed": 0, "skipped": 0}),
            "tests": []
        }
        
        for testsuite in root.findall(".//testsuite"):
            results["time"] += float(testsuite.get("time", 0))
            
            for testcase in testsuite.findall("testcase"):
                test_name = testcase.get("name")
                classname = testcase.get("classname")
                time_taken = float(testcase.get("time", 0))
                
                # Detect platform from test name
                platform = "unknown"
                if "ubuntu" in test_name.lower():
                    platform = "ubuntu"
                elif "windows" in test_name.lower():
                    platform = "windows"
                elif "cross_platform" in test_name.lower():
                    platform = "cross-platform"
                
                # For parameterized tests with vm_platform fixture
                if "vm_platform" in test_name and "[" in test_name and "]" in test_name:
                    platform_match = re.search(r"\[(.*?)\]", test_name)
                    if platform_match:
                        platform = platform_match.group(1)
                
                # Check test status
                status = "passed"
                failure_message = None
                
                failure = testcase.find("failure")
                error = testcase.find("error")
                skipped = testcase.find("skipped")
                
                if failure is not None:
                    status = "failed"
                    failure_message = failure.get("message")
                elif error is not None:
                    status = "error"
                    failure_message = error.get("message")
                elif skipped is not None:
                    status = "skipped"
                    failure_message = skipped.get("message")
                
                # Update counters
                results["total"] += 1
                
                if status == "passed":
                    results["passed"] += 1
                    results["platform_results"][platform]["passed"] += 1
                elif status == "failed":
                    results["failed"] += 1
                    results["platform_results"][platform]["failed"] += 1
                elif status == "error":
                    results["errors"] += 1
                    results["platform_results"][platform]["failed"] += 1
                elif status == "skipped":
                    results["skipped"] += 1
                    results["platform_results"][platform]["skipped"] += 1
                
                # Add test details
                results["tests"].append({
                    "name": test_name,
                    "classname": classname,
                    "time": time_taken,
                    "status": status,
                    "platform": platform,
                    "failure_message": failure_message
                })
        
        return results
    
    except Exception as e:
        logger.error(f"Error parsing JUnit XML file: {str(e)}")
        return None


def parse_log_file(log_file):
    """Parse a log file to extract additional metrics and information."""
    if not log_file.exists():
        logger.error(f"Log file not found: {log_file}")
        return None
    
    metrics = {
        "vm_creation_time": {},
        "injection_time": {},
        "execution_time": {},
        "api_errors": [],
        "connection_errors": []
    }
    
    try:
        with open(log_file, "r") as f:
            lines = f.readlines()
        
        for line in lines:
            # Extract VM creation times
            match = re.search(r"VM (\S+) reached target status: running \(after (\d+\.\d+)s\)", line)
            if match:
                vm_id = match.group(1)
                time_taken = float(match.group(2))
                metrics["vm_creation_time"][vm_id] = time_taken
            
            # Extract injection times
            match = re.search(r"Injection (\S+) completed in (\d+\.\d+)s", line)
            if match:
                injection_id = match.group(1)
                time_taken = float(match.group(2))
                metrics["injection_time"][injection_id] = time_taken
            
            # Extract script execution times
            match = re.search(r"Script execution completed in (\d+\.\d+)s for injection (\S+)", line)
            if match:
                time_taken = float(match.group(1))
                injection_id = match.group(2)
                metrics["execution_time"][injection_id] = time_taken
            
            # Extract API errors
            if "API Error:" in line:
                metrics["api_errors"].append(line.strip())
            
            # Extract connection errors
            if "Connection error:" in line or "ConnectionError" in line:
                metrics["connection_errors"].append(line.strip())
        
        return metrics
    
    except Exception as e:
        logger.error(f"Error parsing log file: {str(e)}")
        return None


def generate_text_report(results, metrics, output_file=None):
    """Generate a text report from the parsed results and metrics."""
    if not results:
        logger.error("No results to generate report from")
        return False
    
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("VM INJECTION INTEGRATION TEST REPORT")
    report_lines.append("=" * 80)
    report_lines.append("")
    
    # Summary
    report_lines.append("TEST SUMMARY")
    report_lines.append("-" * 50)
    report_lines.append(f"Total tests    : {results['total']}")
    report_lines.append(f"Passed         : {results['passed']} ({results['passed']/results['total']*100:.1f}%)")
    report_lines.append(f"Failed         : {results['failed']} ({results['failed']/results['total']*100:.1f}%)")
    report_lines.append(f"Errors         : {results['errors']} ({results['errors']/results['total']*100:.1f}%)")
    report_lines.append(f"Skipped        : {results['skipped']} ({results['skipped']/results['total']*100:.1f}%)")
    report_lines.append(f"Total time     : {results['time']:.2f} seconds")
    report_lines.append("")
    
    # Results by platform
    report_lines.append("RESULTS BY PLATFORM")
    report_lines.append("-" * 50)
    for platform, platform_results in results["platform_results"].items():
        total = platform_results["passed"] + platform_results["failed"] + platform_results["skipped"]
        if total > 0:
            report_lines.append(f"{platform.upper()} TESTS:")
            report_lines.append(f"  Passed  : {platform_results['passed']} ({platform_results['passed']/total*100:.1f}%)")
            report_lines.append(f"  Failed  : {platform_results['failed']} ({platform_results['failed']/total*100:.1f}%)")
            report_lines.append(f"  Skipped : {platform_results['skipped']} ({platform_results['skipped']/total*100:.1f}%)")
            report_lines.append("")
    
    # Failed tests
    failed_tests = [t for t in results["tests"] if t["status"] in ("failed", "error")]
    if failed_tests:
        report_lines.append("FAILED TESTS")
        report_lines.append("-" * 50)
        for test in failed_tests:
            report_lines.append(f"Test     : {test['name']}")
            report_lines.append(f"Class    : {test['classname']}")
            report_lines.append(f"Platform : {test['platform']}")
            report_lines.append(f"Error    : {test['failure_message']}")
            report_lines.append("")
    
    # Performance metrics
    if metrics:
        report_lines.append("PERFORMANCE METRICS")
        report_lines.append("-" * 50)
        
        if metrics["vm_creation_time"]:
            vm_times = list(metrics["vm_creation_time"].values())
            report_lines.append(f"VM Creation Time:")
            report_lines.append(f"  Average : {sum(vm_times)/len(vm_times):.2f} seconds")
            report_lines.append(f"  Min     : {min(vm_times):.2f} seconds")
            report_lines.append(f"  Max     : {max(vm_times):.2f} seconds")
            report_lines.append("")
        
        if metrics["injection_time"]:
            injection_times = list(metrics["injection_time"].values())
            report_lines.append(f"File Injection Time:")
            report_lines.append(f"  Average : {sum(injection_times)/len(injection_times):.2f} seconds")
            report_lines.append(f"  Min     : {min(injection_times):.2f} seconds")
            report_lines.append(f"  Max     : {max(injection_times):.2f} seconds")
            report_lines.append("")
        
        if metrics["execution_time"]:
            execution_times = list(metrics["execution_time"].values())
            report_lines.append(f"Script Execution Time:")
            report_lines.append(f"  Average : {sum(execution_times)/len(execution_times):.2f} seconds")
            report_lines.append(f"  Min     : {min(execution_times):.2f} seconds")
            report_lines.append(f"  Max     : {max(execution_times):.2f} seconds")
            report_lines.append("")
        
        # Error counts
        if metrics["api_errors"]:
            report_lines.append(f"API Errors: {len(metrics['api_errors'])}")
            report_lines.append("")
        
        if metrics["connection_errors"]:
            report_lines.append(f"Connection Errors: {len(metrics['connection_errors'])}")
            report_lines.append("")
    
    # Write report to file if requested
    if output_file:
        try:
            with open(output_file, "w") as f:
                f.write("\n".join(report_lines))
            logger.info(f"Text report saved to: {output_file}")
        except Exception as e:
            logger.error(f"Error writing text report: {str(e)}")
            return False
    
    # Print report to console
    print("\n".join(report_lines))
    return True


def generate_csv_report(results, metrics, output_file=None):
    """Generate a CSV report from the parsed results and metrics."""
    if not results:
        logger.error("No results to generate CSV report from")
        return False
    
    if not output_file:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = REPORTS_DIR / f"vm_injection_report_{timestamp}.csv"
    
    try:
        with open(output_file, "w", newline="") as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow([
                "Test Name", "Class", "Platform", "Status", "Time (s)", 
                "Failure Message"
            ])
            
            # Write test results
            for test in results["tests"]:
                writer.writerow([
                    test["name"],
                    test["classname"],
                    test["platform"],
                    test["status"],
                    f"{test['time']:.2f}",
                    test["failure_message"] or ""
                ])
        
        logger.info(f"CSV report saved to: {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error writing CSV report: {str(e)}")
        return False


def generate_json_report(results, metrics, output_file=None):
    """Generate a JSON report from the parsed results and metrics."""
    if not results:
        logger.error("No results to generate JSON report from")
        return False
    
    if not output_file:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = REPORTS_DIR / f"vm_injection_report_{timestamp}.json"
    
    try:
        # Combine results and metrics
        report_data = {
            "summary": {
                "total": results["total"],
                "passed": results["passed"],
                "failed": results["failed"],
                "errors": results["errors"],
                "skipped": results["skipped"],
                "time": results["time"]
            },
            "platform_results": dict(results["platform_results"]),
            "tests": results["tests"],
            "metrics": metrics
        }
        
        with open(output_file, "w") as f:
            json.dump(report_data, f, indent=2)
        
        logger.info(f"JSON report saved to: {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error writing JSON report: {str(e)}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Analyze VM injection test results")
    parser.add_argument("--xml", help="JUnit XML report file to analyze")
    parser.add_argument("--log", help="Log file to analyze")
    parser.add_argument("--text-output", help="Output file for text report")
    parser.add_argument("--csv-output", help="Output file for CSV report")
    parser.add_argument("--json-output", help="Output file for JSON report")
    parser.add_argument("--all-formats", action="store_true", help="Generate reports in all formats")
    
    args = parser.parse_args()
    
    # Find latest report file if not specified
    xml_file = args.xml
    if not xml_file:
        latest_xml = find_latest_report("xml")
        if latest_xml:
            xml_file = latest_xml
            logger.info(f"Using latest XML report: {xml_file}")
    
    if not xml_file:
        logger.error("No JUnit XML report file specified or found")
        return 1
    
    # Find latest log file if not specified
    log_file = args.log
    if not log_file:
        log_pattern = "vm_injection_integration_tests.log"
        log_file = SCRIPT_DIR.parent / log_pattern
        if not log_file.exists():
            logger.warning(f"Log file not found: {log_file}")
            log_file = None
        else:
            logger.info(f"Using log file: {log_file}")
    
    # Parse reports
    results = parse_junit_xml(xml_file)
    metrics = parse_log_file(log_file) if log_file else None
    
    if not results:
        logger.error("Failed to parse test results")
        return 1
    
    # Generate reports
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Ensure reports directory exists
    REPORTS_DIR.mkdir(parents=True, exist_ok=True)
    
    # Generate text report
    text_output = args.text_output
    if args.all_formats or text_output:
        if not text_output:
            text_output = REPORTS_DIR / f"vm_injection_report_{timestamp}.txt"
        generate_text_report(results, metrics, text_output)
    
    # Generate CSV report
    csv_output = args.csv_output
    if args.all_formats or csv_output:
        if not csv_output:
            csv_output = REPORTS_DIR / f"vm_injection_report_{timestamp}.csv"
        generate_csv_report(results, metrics, csv_output)
    
    # Generate JSON report
    json_output = args.json_output
    if args.all_formats or json_output:
        if not json_output:
            json_output = REPORTS_DIR / f"vm_injection_report_{timestamp}.json"
        generate_json_report(results, metrics, json_output)
    
    logger.info("Analysis completed successfully")
    return 0


if __name__ == "__main__":
    sys.exit(main()) 