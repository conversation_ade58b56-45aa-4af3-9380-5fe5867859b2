# VM Injection Testing Troubleshooting Guide

This document provides detailed troubleshooting guidance for common issues encountered during VM injection integration testing.

## Prerequisites and Environment Issues

### API Service Not Running or Unreachable

**Symptoms:**
- Tests fail immediately with connection errors
- Error messages about "Connection refused" when connecting to localhost:8000

**Solutions:**
1. Check if the API container is running:
   ```bash
   docker ps | grep turdparty_api
   ```

2. If not running, start the API service:
   ```bash
   docker start turdparty_api_1
   ```

3. Check API logs for startup errors:
   ```bash
   docker logs turdparty_api_1
   ```

4. Verify API health by accessing the health endpoint:
   ```bash
   curl http://localhost:8000/api/v1/health
   ```

### Database Related Issues

**Symptoms:**
- Foreign key constraint violations during file upload
- Error messages containing `psycopg2.errors.UndefinedColumn` or similar
- Tests fail at database-related operations

**Solutions:**
1. Check if the PostgreSQL container is running:
   ```bash
   docker ps | grep turdparty_postgres
   ```

2. Verify database connection from the API:
   ```bash
   docker exec turdparty_api_1 python -c "from api.db.session import SessionLocal; db = SessionLocal(); print('DB connection successful' if db else 'Failed')"
   ```

3. Reset the database if schema issues persist:
   ```bash
   # Back up any important data first
   docker exec turdparty_postgres_1 pg_dump -U postgres -d turdparty > db_backup.sql
   # Reset the database
   docker exec turdparty_postgres_1 psql -U postgres -c "DROP DATABASE turdparty;"
   docker exec turdparty_postgres_1 psql -U postgres -c "CREATE DATABASE turdparty;"
   # Restart the API to trigger migrations
   docker restart turdparty_api_1
   ```

### File Storage Issues

**Symptoms:**
- File uploads succeed but files cannot be retrieved
- Error messages about MinIO or S3 storage
- Tests fail during file injection with "file not found" errors

**Solutions:**
1. Check if MinIO container is running:
   ```bash
   docker ps | grep turdparty_minio
   ```

2. Verify MinIO configuration in the API:
   ```bash
   grep -r "MINIO" /home/<USER>/dev/replit-10baht-TurdParty-simplified/api/core/config.py
   ```

3. Test MinIO connection from the API container:
   ```bash
   docker exec turdparty_api_1 python -c "from api.services.storage import get_storage_client; client = get_storage_client(); print('Storage connection successful' if client else 'Failed')"
   ```

## VM Related Issues

### Vagrant Not Installed or Misconfigured

**Symptoms:**
- Error messages about Vagrant not found
- Tests fail when trying to create or start VMs

**Solutions:**
1. Verify Vagrant installation:
   ```bash
   vagrant --version
   ```

2. Check Vagrant configuration:
   ```bash
   vagrant global-status
   ```

3. Validate VirtualBox installation (or other provider):
   ```bash
   VBoxManage --version
   ```

### VM Creation Timeouts

**Symptoms:**
- Tests time out during VM creation
- Error messages about VM creation taking too long

**Solutions:**
1. Increase VM creation timeout in the configuration:
   ```python
   # Edit api/tests/test_integration/vm_injection_test_config.py
   TIMEOUTS = {
       "vm_creation": 600,  # Increase from 300 to 600 seconds
       # ... other timeouts
   }
   ```

2. Check system resources (CPU, RAM, disk space):
   ```bash
   free -h  # Check memory
   df -h    # Check disk space
   top      # Check CPU usage
   ```

3. Use pre-created VMs instead of creating them during tests:
   ```bash
   # Set up VMs ahead of time
   python -m api.tests.test_integration.setup_test_vms --save
   
   # Then run tests with those VMs
   source api/tests/test_integration/.env_vm_ids
   python -m api.tests.run_vm_injection_integration_tests
   ```

### Windows-specific VM Issues

**Symptoms:**
- Tests pass for Ubuntu but fail for Windows
- WinRM connection errors
- Timeouts during Windows VM operations

**Solutions:**
1. Increase Windows VM-specific timeouts:
   ```python
   # Add platform-specific timeouts in the configuration
   PLATFORM_TIMEOUTS = {
       "windows": {
           "vm_creation": 600,
           "vm_start": 300,
           "file_injection": 120,
       }
   }
   ```

2. Check WinRM configuration in the Windows VM:
   ```bash
   # Run this on the host if vagrant is installed
   vagrant winrm-config -t <vagrant_vm_name>
   ```

3. Use a different Windows box that's known to work well:
   ```python
   # Edit api/tests/test_integration/vm_injection_test_config.py
   VM_CONFIGS = {
       # ... other configs
       "windows": {
           # ... other settings
           "box": "gusztavvargadr/windows-10-20h2-standard",  # Try different box
       }
   }
   ```

### VM Network Connectivity Issues

**Symptoms:**
- Tests fail during file injection with network errors
- "Host unreachable" or "Connection timeout" errors
- SSH or WinRM connection failures

**Solutions:**
1. Verify VM networking from the host:
   ```bash
   # For Ubuntu VMs (SSH)
   ssh -p 2222 vagrant@localhost
   
   # For Windows VMs (PowerShell)
   winrm identify -r:http://localhost:5985/wsman -auth:basic -u:vagrant -p:vagrant -encoding:utf-8
   ```

2. Check VM network configuration:
   ```bash
   # For Ubuntu
   vagrant ssh <vm_name> -c "ifconfig"
   
   # For Windows
   vagrant winrm <vm_name> -c "ipconfig"
   ```

3. Restart the VM's network services:
   ```bash
   # For Ubuntu
   vagrant ssh <vm_name> -c "sudo systemctl restart networking"
   
   # For Windows
   vagrant winrm <vm_name> -c "Restart-Service -Name 'netprofm'"
   ```

## Test Framework Issues

### Pytest Configuration Problems

**Symptoms:**
- Tests fail with pytest-related errors
- Missing test dependencies
- Test discovery issues

**Solutions:**
1. Verify pytest installation and version:
   ```bash
   python -m pytest --version
   ```

2. Install required pytest plugins:
   ```bash
   pip install pytest-timeout pytest-html pytest-xdist
   ```

3. Run tests with increased verbosity to debug test discovery issues:
   ```bash
   python -m pytest test_integration/ -v
   ```

### Test Isolation Problems

**Symptoms:**
- Tests pass when run individually but fail when run together
- State leakage between tests
- Resource conflicts

**Solutions:**
1. Run tests with isolation:
   ```bash
   python -m pytest test_integration/ --numprocesses=1 --forked
   ```

2. Add proper cleanup code to test fixtures and teardown:
   ```python
   @pytest.fixture
   def test_resource():
       # Setup
       resource = create_resource()
       yield resource
       # Teardown
       try:
           clean_up_resource(resource)
       except Exception as e:
           print(f"Warning: Cleanup failed: {str(e)}")
   ```

3. Identify and fix tests with missing cleanup by running them individually.

## Injection-Specific Issues

### File Permission Problems

**Symptoms:**
- Files are injected but cannot be executed
- "Permission denied" errors in script execution results
- Tests fail during the execution phase

**Solutions:**
1. Ensure execution permissions are properly set for scripts in Ubuntu:
   ```python
   # In your test code when creating temporary script files
   if vm_platform == "ubuntu":
       os.chmod(script_path, 0o755)  # Make executable
   ```

2. Inject to a location with appropriate permissions:
   ```python
   # Choose different paths for different platforms
   dest_path = "/tmp/script.sh" if vm_platform == "ubuntu" else "C:\\Users\\<USER>\\script.bat"
   ```

3. For Windows, check execution policy:
   ```powershell
   # Add this to your test Windows scripts
   Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
   ```

### Content Encoding Issues

**Symptoms:**
- Files are injected but contain garbled text or incorrect line endings
- Script execution fails with syntax errors

**Solutions:**
1. Ensure proper line endings for the target platform:
   ```python
   # Convert line endings when creating scripts
   if vm_platform == "windows":
       content = content.replace("\n", "\r\n")
   ```

2. Specify encoding explicitly:
   ```python
   with open(script_path, "w", encoding="utf-8") as f:
       f.write(content)
   ```

3. For binary files, use binary mode:
   ```python
   with open(binary_path, "wb") as f:
       f.write(binary_content)
   ```

### Large File Injection Issues

**Symptoms:**
- Small files inject successfully but large files fail
- Timeouts during large file injection
- Memory errors

**Solutions:**
1. Increase timeouts for large file operations:
   ```python
   # In vm_injection_test_config.py
   TIMEOUTS = {
       # ... other timeouts
       "large_file_injection": 300,  # 5 minutes for large files
   }
   ```

2. Use chunked uploads for large files:
   ```python
   # When uploading large files
   with open(large_file, "rb") as f:
       chunk_size = 1024 * 1024  # 1 MB chunks
       while True:
           chunk = f.read(chunk_size)
           if not chunk:
               break
           # Upload chunk (implementation depends on your API)
   ```

3. Consider reducing test file sizes for faster test execution.

## Advanced Diagnostics

### Capturing VM Screenshots on Failure

For visual confirmation of VM state on test failures, you can enable automatic screenshot capture:

```python
@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    outcome = yield
    report = outcome.get_result()
    
    if report.when == "call" and report.failed:
        # Get the active VM fixture if it exists
        for name, value in item._request._fixture_defs.items():
            if name == "active_vm" and hasattr(item, "funcargs"):
                vm_fixture = item.funcargs.get("active_vm")
                if vm_fixture:
                    vm_id, vm_config = vm_fixture
                    # Take screenshot (implementation depends on your VM system)
                    capture_vm_screenshot(vm_id, item.name)
```

### Detailed Logging for Debugging

Enhance logging in tests to capture more detailed information:

```python
# At the top of your test file
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("vm_injection_detailed.log"),
        logging.StreamHandler()
    ]
)

# In your test functions
def test_example():
    logger = logging.getLogger("test_example")
    logger.info("Starting test")
    try:
        # Test steps with detailed logging
        logger.debug("Detailed information")
        # ...
    except Exception as e:
        logger.exception(f"Test failed: {str(e)}")
        raise
```

### Network Traffic Inspection

For network-related issues, capture and analyze network traffic:

```bash
# Capture traffic between host and VMs
sudo tcpdump -i any port 2222 or port 5985 -w vm_traffic.pcap

# Analyze with Wireshark
wireshark vm_traffic.pcap
```

## Getting Further Help

If you've tried the solutions above and still encounter issues:

1. Check the issue tracker for similar problems and solutions
2. Include detailed logs and error messages when reporting issues
3. Share VM configurations and test environment details
4. Provide a minimal reproducible test case that demonstrates the issue 