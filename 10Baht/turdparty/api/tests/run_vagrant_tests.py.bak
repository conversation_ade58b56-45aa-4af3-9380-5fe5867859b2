#!/usr/bin/env python3
"""
Script to run all Vagrant VM tests and analyze the logs.
"""
import os
import sys
import subprocess
import logging
import re
import json
from datetime import datetime
from collections import defaultdict

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='tests/logs/vagrant_test_runner.log',
    filemode='w'
)
logger = logging.getLogger(__name__)

# Ensure logs directory exists
os.makedirs('tests/logs', exist_ok=True)

# Define test files
TEST_FILES = [
    "turdparty-app/tests/playwright/vagrant-vm-form-submission.spec.ts",
    "api/tests/test_vagrant_vm_api.py",
    "api/tests/test_vagrant_vm_service.py",
    "api/tests/test_vagrant_grpc_client.py",
    "api/tests/test_vagrant_grpc_server.py"
]

def run_playwright_test(test_file):
    """Run a Playwright test."""
    logger.info(f"Running Playwright test: {test_file}")
    
    try:
        result = subprocess.run(
            ["npx", "playwright", "test", test_file, "--reporter=list"],
            cwd="turdparty-app",
            capture_output=True,
            text=True
        )
        
        logger.info(f"Playwright test exit code: {result.returncode}")
        
        if result.returncode != 0:
            logger.error(f"Playwright test failed: {result.stderr}")
            return False, result.stdout, result.stderr
        
        return True, result.stdout, result.stderr
    except Exception as e:
        logger.error(f"Error running Playwright test: {str(e)}")
        return False, "", str(e)

def run_pytest_test(test_file):
    """Run a pytest test."""
    logger.info(f"Running pytest test: {test_file}")
    
    try:
        result = subprocess.run(
            ["python", "-m", "pytest", test_file, "-v"],
            capture_output=True,
            text=True
        )
        
        logger.info(f"pytest exit code: {result.returncode}")
        
        if result.returncode != 0:
            logger.error(f"pytest failed: {result.stderr}")
            return False, result.stdout, result.stderr
        
        return True, result.stdout, result.stderr
    except Exception as e:
        logger.error(f"Error running pytest: {str(e)}")
        return False, "", str(e)

def analyze_logs():
    """Analyze test logs to identify failure points."""
    logger.info("Analyzing test logs")
    
    log_files = [
        "tests/logs/vagrant_vm_form_submission.log",
        "tests/logs/vagrant_vm_api_tests.log",
        "tests/logs/vagrant_vm_service_tests.log",
        "tests/logs/vagrant_grpc_client_tests.log",
        "tests/logs/vagrant_grpc_server_tests.log"
    ]
    
    results = {
        "errors": [],
        "warnings": [],
        "success_count": 0,
        "failure_count": 0,
        "error_count": 0,
        "component_stats": defaultdict(lambda: {"success": 0, "failure": 0, "error": 0})
    }
    
    for log_file in log_files:
        try:
            if not os.path.exists(log_file):
                logger.warning(f"Log file not found: {log_file}")
                continue
                
            component = log_file.split('/')[-1].replace('.log', '')
            
            with open(log_file, 'r') as f:
                content = f.read()
                
                # Count successes
                success_count = len(re.findall(r'(test.*success|success.*test)', content, re.IGNORECASE))
                results["success_count"] += success_count
                results["component_stats"][component]["success"] = success_count
                
                # Find errors
                error_matches = re.findall(r'ERROR.*?(?=\n\[|$)', content, re.DOTALL)
                for error in error_matches:
                    results["errors"].append({
                        "component": component,
                        "message": error.strip()
                    })
                
                results["error_count"] += len(error_matches)
                results["component_stats"][component]["error"] = len(error_matches)
                
                # Find warnings
                warning_matches = re.findall(r'WARNING.*?(?=\n\[|$)', content, re.DOTALL)
                for warning in warning_matches:
                    results["warnings"].append({
                        "component": component,
                        "message": warning.strip()
                    })
                
                # Count failures
                failure_count = len(re.findall(r'(test.*fail|fail.*test)', content, re.IGNORECASE))
                results["failure_count"] += failure_count
                results["component_stats"][component]["failure"] = failure_count
                
        except Exception as e:
            logger.error(f"Error analyzing log file {log_file}: {str(e)}")
    
    return results

def generate_report(test_results, log_analysis):
    """Generate a comprehensive test report."""
    logger.info("Generating test report")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "test_results": test_results,
        "log_analysis": log_analysis,
        "summary": {
            "total_tests": sum(1 for result in test_results.values() if result["ran"]),
            "passed_tests": sum(1 for result in test_results.values() if result["success"]),
            "failed_tests": sum(1 for result in test_results.values() if not result["success"] and result["ran"]),
            "error_count": log_analysis["error_count"],
            "warning_count": len(log_analysis["warnings"]),
            "components": {
                component: {
                    "success": stats["success"],
                    "failure": stats["failure"],
                    "error": stats["error"]
                }
                for component, stats in log_analysis["component_stats"].items()
            }
        }
    }
    
    # Identify potential failure points
    failure_points = []
    
    # Check for connection errors
    connection_errors = [
        error for error in log_analysis["errors"] 
        if "connect" in error["message"].lower() or "connection" in error["message"].lower()
    ]
    if connection_errors:
        failure_points.append({
            "component": "gRPC Connection",
            "message": "Connection issues with the gRPC server",
            "count": len(connection_errors)
        })
    
    # Check for VM creation errors
    vm_creation_errors = [
        error for error in log_analysis["errors"] 
        if "create" in error["message"].lower() and "vm" in error["message"].lower()
    ]
    if vm_creation_errors:
        failure_points.append({
            "component": "VM Creation",
            "message": "Issues with VM creation process",
            "count": len(vm_creation_errors)
        })
    
    # Check for command execution errors
    command_errors = [
        error for error in log_analysis["errors"] 
        if "command" in error["message"].lower() and "execution" in error["message"].lower()
    ]
    if command_errors:
        failure_points.append({
            "component": "Command Execution",
            "message": "Issues with executing Vagrant commands",
            "count": len(command_errors)
        })
    
    report["failure_points"] = failure_points
    
    # Write report to file
    with open('tests/logs/vagrant_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Generate human-readable report
    with open('tests/logs/vagrant_test_report.txt', 'w') as f:
        f.write("=== Vagrant VM Test Report ===\n")
        f.write(f"Generated: {report['timestamp']}\n\n")
        
        f.write("=== Summary ===\n")
        f.write(f"Total Tests: {report['summary']['total_tests']}\n")
        f.write(f"Passed Tests: {report['summary']['passed_tests']}\n")
        f.write(f"Failed Tests: {report['summary']['failed_tests']}\n")
        f.write(f"Error Count: {report['summary']['error_count']}\n")
        f.write(f"Warning Count: {report['summary']['warning_count']}\n\n")
        
        f.write("=== Component Stats ===\n")
        for component, stats in report['summary']['components'].items():
            f.write(f"{component}:\n")
            f.write(f"  Success: {stats['success']}\n")
            f.write(f"  Failure: {stats['failure']}\n")
            f.write(f"  Error: {stats['error']}\n")
        f.write("\n")
        
        if failure_points:
            f.write("=== Potential Failure Points ===\n")
            for point in failure_points:
                f.write(f"{point['component']}: {point['message']} ({point['count']} occurrences)\n")
            f.write("\n")
        
        f.write("=== Test Results ===\n")
        for test_file, result in report['test_results'].items():
            status = "PASSED" if result["success"] else "FAILED" if result["ran"] else "NOT RUN"
            f.write(f"{test_file}: {status}\n")
        f.write("\n")
        
        if log_analysis["errors"]:
            f.write("=== Errors ===\n")
            for error in log_analysis["errors"][:10]:  # Show only first 10 errors
                f.write(f"{error['component']}: {error['message']}\n")
            if len(log_analysis["errors"]) > 10:
                f.write(f"... and {len(log_analysis['errors']) - 10} more errors\n")
            f.write("\n")
    
    return report

def main():
    """Run all tests and analyze results."""
    logger.info("Starting Vagrant VM test suite")
    
    test_results = {}
    
    for test_file in TEST_FILES:
        if not os.path.exists(test_file):
            logger.warning(f"Test file not found: {test_file}")
            test_results[test_file] = {
                "ran": False,
                "success": False,
                "stdout": "",
                "stderr": f"Test file not found: {test_file}"
            }
            continue
        
        if test_file.endswith(".ts"):
            success, stdout, stderr = run_playwright_test(test_file)
        else:
            success, stdout, stderr = run_pytest_test(test_file)
        
        test_results[test_file] = {
            "ran": True,
            "success": success,
            "stdout": stdout,
            "stderr": stderr
        }
    
    # Analyze logs
    log_analysis = analyze_logs()
    
    # Generate report
    report = generate_report(test_results, log_analysis)
    
    # Print summary
    print("\n=== Vagrant VM Test Suite Summary ===")
    print(f"Total Tests: {report['summary']['total_tests']}")
    print(f"Passed Tests: {report['summary']['passed_tests']}")
    print(f"Failed Tests: {report['summary']['failed_tests']}")
    print(f"Error Count: {report['summary']['error_count']}")
    print(f"Warning Count: {report['summary']['warning_count']}")
    print("\nDetailed report available at: tests/logs/vagrant_test_report.txt")
    print("JSON report available at: tests/logs/vagrant_test_report.json")
    
    # Return exit code based on test results
    return 0 if report['summary']['failed_tests'] == 0 else 1

if __name__ == "__main__":
    sys.exit(main()) 