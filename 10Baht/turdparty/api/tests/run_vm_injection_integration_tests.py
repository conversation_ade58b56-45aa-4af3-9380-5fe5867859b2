#!/usr/bin/env python3
"""
Script to run VM injection integration tests
"""
import os
import sys
import argparse
import subprocess
import time
import logging
from pathlib import Path
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("vm_injection_integration_tests.log")
    ]
)
logger = logging.getLogger("vm_injection_tests")

# Directory containing this script
SCRIPT_DIR = Path(__file__).parent.absolute()

# Directory for test reports
REPORTS_DIR = SCRIPT_DIR / "reports" / "vm_injection"


def ensure_directory(path):
    """Ensure the directory exists, creating it if necessary"""
    path.mkdir(parents=True, exist_ok=True)
    return path


def run_tests(args):
    """Run the VM injection integration tests"""
    # Ensure reports directory exists
    ensure_directory(REPORTS_DIR)
    
    # Generate timestamp for this test run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = REPORTS_DIR / f"vm_injection_test_report_{timestamp}.xml"
    
    # Build the pytest command
    cmd = [
        sys.executable, "-m", "pytest",
    ]
    
    # Add tests to run based on platform selection
    if args.platform == "all":
        test_files = [
            "test_integration/test_file_upload_to_vm_injection.py",
            "test_integration/test_folder_upload_to_vm_injection.py",
            "test_integration/test_vm_injection_error_handling.py",
            "test_integration/test_cross_platform_vm_injection.py"
        ]
    elif args.platform == "ubuntu":
        # For Ubuntu-only tests
        test_files = [
            "test_integration/test_file_upload_to_vm_injection.py",
            "test_integration/test_folder_upload_to_vm_injection.py",
            "test_integration/test_vm_injection_error_handling.py"
        ]
        # Add platform filter for cross-platform tests
        cmd.extend(["-k", "ubuntu and not windows"])
    elif args.platform == "windows":
        # For Windows-only tests
        test_files = [
            "test_integration/test_cross_platform_vm_injection.py"
        ]
        cmd.extend(["-k", "windows and not ubuntu"])
    elif args.platform == "cross":
        # For cross-platform tests only
        test_files = [
            "test_integration/test_cross_platform_vm_injection.py"
        ]
    else:
        logger.error(f"Unknown platform: {args.platform}")
        return 1
    
    # Add test files
    cmd.extend(test_files)
    
    # Add command line options
    if args.verbose:
        cmd.append("-v")
    
    # Add JUnit XML report
    cmd.extend(["--junitxml", str(report_file)])
    
    # Add HTML report if requested
    if args.html_report:
        html_report = REPORTS_DIR / f"vm_injection_test_report_{timestamp}.html"
        cmd.extend(["--html", str(html_report), "--self-contained-html"])
    
    # Set timeout if specified
    if args.timeout:
        cmd.extend(["--timeout", str(args.timeout)])
    
    # Run only specified tests if provided
    if args.test:
        cmd.extend(["-k", args.test])
    
    # Add environment variables if testing against specific VM IDs
    env = os.environ.copy()
    if args.ubuntu_vm_id:
        env["TEST_UBUNTU_VM_ID"] = args.ubuntu_vm_id
    if args.windows_vm_id:
        env["TEST_WINDOWS_VM_ID"] = args.windows_vm_id
    
    # Log the command
    logger.info(f"Running tests with command: {' '.join(cmd)}")
    
    # Run the tests
    try:
        # Change to the tests directory
        os.chdir(SCRIPT_DIR)
        
        # Start the test process
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        end_time = time.time()
        
        # Calculate test duration
        duration = end_time - start_time
        
        # Log the result
        if result.returncode == 0:
            logger.info(f"Tests completed successfully in {duration:.2f} seconds")
        else:
            logger.error(f"Tests failed with return code {result.returncode}")
        
        # Log stdout and stderr
        logger.info("Test output:")
        logger.info(result.stdout)
        
        if result.stderr:
            logger.error("Test errors:")
            logger.error(result.stderr)
        
        # Print test report location
        logger.info(f"Test report saved to: {report_file}")
        if args.html_report:
            logger.info(f"HTML report saved to: {html_report}")
        
        return result.returncode
        
    except Exception as e:
        logger.exception(f"Error running tests: {str(e)}")
        return 1


def check_prerequisites():
    """Check that required services and VMs are available"""
    # Check for required services
    services_to_check = ["api", "postgres", "minio"]
    services_status = {}
    
    try:
        # Check Docker services if available
        result = subprocess.run(
            ["docker", "ps", "--format", "{{.Names}}"],
            capture_output=True, text=True, check=False
        )
        
        if result.returncode == 0:
            running_containers = result.stdout.splitlines()
            for service in services_to_check:
                # Check if a container with the service name exists
                services_status[service] = any(
                    service in container for container in running_containers
                )
        else:
            # If Docker command failed, we can't check services
            for service in services_to_check:
                services_status[service] = None
    
    except Exception as e:
        logger.warning(f"Error checking services: {str(e)}")
        for service in services_to_check:
            services_status[service] = None
    
    # Log services status
    logger.info("Services status:")
    all_services_ok = True
    for service, status in services_status.items():
        if status is None:
            logger.warning(f"  - {service}: Unknown")
        elif status:
            logger.info(f"  - {service}: Running")
        else:
            logger.error(f"  - {service}: Not running")
            all_services_ok = False
    
    if not all_services_ok:
        logger.warning("Not all required services are running.")
    
    return all_services_ok


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Run VM injection integration tests")
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--html-report", action="store_true", help="Generate HTML report")
    parser.add_argument("--timeout", type=int, help="Set test timeout in seconds")
    parser.add_argument("-k", "--test", help="Only run tests matching the given expression")
    parser.add_argument("--platform", choices=["all", "ubuntu", "windows", "cross"], 
                        default="all", help="Select which platform tests to run")
    parser.add_argument("--ubuntu-vm-id", help="Specify Ubuntu VM ID to use for testing")
    parser.add_argument("--windows-vm-id", help="Specify Windows VM ID to use for testing")
    parser.add_argument("--skip-checks", action="store_true", 
                        help="Skip prerequisite checks")
    
    args = parser.parse_args()
    
    logger.info("Starting VM injection integration test runner")
    
    # Check if required packages are installed
    try:
        import pytest
        try:
            import pytest_html
        except ImportError:
            logger.warning("pytest-html not installed. HTML report generation will be disabled.")
            args.html_report = False
    except ImportError:
        logger.error("pytest not installed. Please install it with 'pip install pytest'.")
        return 1
    
    # Check prerequisites unless skipped
    if not args.skip_checks:
        if not check_prerequisites():
            logger.warning("Some prerequisites are not met. Tests may fail.")
            response = input("Continue anyway? [y/N]: ").lower()
            if response != 'y':
                logger.info("Aborting test run.")
                return 1
    
    # Run the tests
    status = run_tests(args)
    
    logger.info(f"Test run completed with status: {status}")
    return status


if __name__ == "__main__":
    sys.exit(main()) 