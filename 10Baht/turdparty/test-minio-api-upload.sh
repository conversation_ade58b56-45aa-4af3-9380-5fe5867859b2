#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing MinIO File Upload via API${NC}"
echo -e "${YELLOW}=============================${NC}"

# Create test directory and file
TEST_DIR="/tmp/minio-api-test"
TEST_FILE="${TEST_DIR}/test-api-file.txt"

mkdir -p "${TEST_DIR}"
echo "This is a test file for API upload to MinIO - $(date)" > "${TEST_FILE}"
echo -e "${GREEN}Created test file at ${TEST_FILE}${NC}"

# Check if the API is running
API_URL="http://localhost:3050"
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${API_URL}/api/v1/health || echo "failed")

if [ "$API_STATUS" != "200" ]; then
  echo -e "${RED}Error: API is not running or not accessible at ${API_URL}${NC}"
  echo -e "${YELLOW}Starting test container with MinIO configuration pointing to host...${NC}"
  
  # Check if Docker is running
  if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not running${NC}"
    exit 1
  fi
  
  # Start the API container with updated MinIO configuration
  cd .dockerwrapper
  docker-compose -f docker-compose.yml up -d api
  
  echo -e "${YELLOW}Waiting for API to start...${NC}"
  sleep 10
  
  # Check again if API is accessible
  API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${API_URL}/api/v1/health || echo "failed")
  if [ "$API_STATUS" != "200" ]; then
    echo -e "${RED}Error: Failed to start API container${NC}"
    exit 1
  fi
fi

echo -e "${GREEN}API is accessible at ${API_URL}${NC}"

# Create Python script for API upload test
cat > /tmp/test_api_upload.py << 'EOF'
import requests
import os
import time
import uuid
import json

# API endpoint configuration
api_url = "http://localhost:3050"
upload_endpoint = f"{api_url}/api/v1/file_upload/upload"
file_list_endpoint = f"{api_url}/api/v1/file_upload/files"

# Test file
test_file_path = "/tmp/minio-api-test/test-api-file.txt"
file_size = os.path.getsize(test_file_path)
file_name = f"api-test-{uuid.uuid4()}.txt"

print(f"Uploading file {test_file_path} (size: {file_size} bytes) via API as {file_name}")

# Prepare file for upload
files = {
    'file': (file_name, open(test_file_path, 'rb'), 'text/plain')
}

# Additional metadata
data = {
    'description': 'Test file uploaded via API',
    'bucket': 'test-bucket'
}

# Make the upload request
start_time = time.time()
try:
    response = requests.post(upload_endpoint, files=files, data=data)
    upload_time = time.time() - start_time
    
    print(f"Upload completed in {upload_time:.2f} seconds with status code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Upload successful. Response: {json.dumps(result, indent=2)}")
        
        # Verify the file is in the list
        print("\nVerifying file in file list...")
        list_response = requests.get(file_list_endpoint)
        
        if list_response.status_code == 200:
            files_list = list_response.json()
            print(f"Retrieved file list with {len(files_list)} files")
            
            # Look for our file
            found = False
            for file_info in files_list:
                if file_info.get('filename') == file_name:
                    found = True
                    print(f"Found uploaded file in list: {json.dumps(file_info, indent=2)}")
                    break
            
            if not found:
                print(f"Warning: Uploaded file '{file_name}' not found in file list")
        else:
            print(f"Error getting file list. Status code: {list_response.status_code}")
            print(list_response.text)
    else:
        print(f"Error during upload. Status code: {response.status_code}")
        print(response.text)
        exit(1)
        
except Exception as e:
    print(f"Error during API upload: {str(e)}")
    exit(1)
finally:
    # Close the file
    files['file'][1].close()

print("\nAPI test completed successfully!")
EOF

# Run the API upload test
echo -e "${YELLOW}Running API upload test...${NC}"
python /tmp/test_api_upload.py

# Clean up
echo -e "${YELLOW}Cleaning up test files...${NC}"
rm -f "${TEST_FILE}"
rmdir "${TEST_DIR}" 2>/dev/null || true

echo -e "${GREEN}API upload test complete!${NC}" 