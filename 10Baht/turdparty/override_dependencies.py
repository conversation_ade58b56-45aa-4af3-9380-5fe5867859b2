#!/usr/bin/env python3
"""
Script to override dependencies in the FastAPI application.
"""
import sys
import logging
import uuid
from datetime import datetime
import requests

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Override dependencies in the FastAPI application."""
    logger.info("Overriding dependencies in the FastAPI application...")
    
    # Create a test user
    test_user = {
        "id": str(uuid.uuid4()),
        "username": "test_user",
        "email": "<EMAIL>",
        "is_active": True,
        "is_superuser": True,
        "created_on": datetime.utcnow().isoformat(),
        "modified_on": datetime.utcnow().isoformat()
    }
    
    # Send a request to the test-token endpoint
    url = "http://localhost:8000/api/auth/test-token"
    logger.info(f"Requesting test token from: {url}")
    
    try:
        response = requests.post(url)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response body: {response.text}")
        
        if response.status_code == 200:
            token = response.json().get("access_token")
            logger.info(f"Got test token: {token}")
            
            # Now try to access a protected endpoint with the token
            headers = {"Authorization": f"Bearer {token}"}
            me_url = "http://localhost:8000/api/auth/me"
            logger.info(f"Accessing protected endpoint: {me_url}")
            
            me_response = requests.get(me_url, headers=headers)
            logger.info(f"Response status code: {me_response.status_code}")
            logger.info(f"Response body: {me_response.text}")
            
            if me_response.status_code == 200:
                logger.info("Successfully accessed protected endpoint with test token")
                return 0
            else:
                logger.error("Failed to access protected endpoint with test token")
                return 1
        else:
            logger.error("Failed to get test token")
            return 1
    except Exception as e:
        logger.error(f"Error overriding dependencies: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 