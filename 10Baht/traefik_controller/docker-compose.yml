services:
  traefik:
    image: traefik:v3.0
    container_name: traefik-controller
    restart: unless-stopped
    command:
      # Enable Docker provider
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=traefik-network"

      # Enable file provider for static configs
      - "--providers.file.directory=/etc/traefik/dynamic"
      - "--providers.file.watch=true"

      # Configure entrypoints
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"

      # Enable dashboard and API
      - "--api=true"
      - "--api.dashboard=true"
      - "--api.insecure=true"

      # Logging
      - "--log.level=INFO"
      - "--accesslog=true"
      - "--accesslog.filepath=/var/log/traefik/access.log"

      # Metrics (optional)
      - "--metrics.prometheus=true"
      
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard (will be moved to traefik.10baht)
    
    volumes:
      # Docker socket for container discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro
      
      # Traefik configuration
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - ./dynamic:/etc/traefik/dynamic:ro
      
      # SSL certificates
      - ./certs:/etc/traefik/certs:ro
      
      # Logs
      - ./logs:/var/log/traefik
    
    networks:
      - traefik-network
    
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"
      
      # Dashboard routing
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.10baht`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=websecure"
      - "traefik.http.routers.traefik-dashboard.tls=true"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      
      # Redirect HTTP to HTTPS for dashboard
      - "traefik.http.routers.traefik-dashboard-http.rule=Host(`traefik.10baht`)"
      - "traefik.http.routers.traefik-dashboard-http.entrypoints=web"
      - "traefik.http.routers.traefik-dashboard-http.middlewares=redirect-to-https"
      
      # Redirect middleware
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.permanent=true"

networks:
  traefik-network:
    name: traefik-network
    driver: bridge
    external: false

volumes:
  traefik-logs:
    name: traefik-logs
    driver: local
