services:
  traefik:
    image: traefik:v3.0
    container_name: traefik-controller
    restart: unless-stopped
    command:
      # Enable Docker provider
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=traefik-network"

      # Enable file provider for static configs
      - "--providers.file.directory=/etc/traefik/dynamic"
      - "--providers.file.watch=true"

      # Configure entrypoints (HTTP only)
      - "--entrypoints.web.address=:80"

      # Enable dashboard and API
      - "--api=true"
      - "--api.dashboard=true"
      - "--api.insecure=true"

      # Logging
      - "--log.level=INFO"
      - "--accesslog=true"
      - "--accesslog.filepath=/var/log/traefik/access.log"

      # Metrics (optional)
      - "--metrics.prometheus=true"
      
    ports:
      - "80:80"
      - "8080:8080"  # Dashboard (will be moved to traefik.10baht)
    
    volumes:
      # Docker socket for container discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro
      
      # Traefik configuration
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - ./dynamic:/etc/traefik/dynamic:ro
      # Logs
      - ./logs:/var/log/traefik
    
    networks:
      - net
    
    labels:
      - traefik.enable=true
      - traefik.http.routers.traefik-dashboard.rule=Host("traefik.docker.localhost")
      - traefik.http.routers.traefik-dashboard.service=api@internal

  whoami:
    image: "traefik/whoami"
    labels:
      - traefik.enable=true
      - traefik.http.routers.whoami.rule=Host("whoami.docker.localhost")
    networks:
      - net

networks:
  net:
    name: net
    driver: bridge
    external: false

volumes:
  traefik-logs:
    name: traefik-logs
    driver: local
