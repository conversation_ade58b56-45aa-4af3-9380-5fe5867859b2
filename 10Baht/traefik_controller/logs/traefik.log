2025-06-08T13:29:59Z INF Traefik version 3.0.4 built on 2024-07-02T13:46:37Z version=3.0.4
2025-06-08T13:29:59Z INF 
Stats collection is disabled.
Help us improve Traefik by turning this feature on :)
More details on: https://doc.traefik.io/traefik/contributing/data-collection/

2025-06-08T13:29:59Z INF Starting provider aggregator aggregator.ProviderAggregator
2025-06-08T13:29:59Z INF Starting provider *file.Provider
2025-06-08T13:29:59Z INF Starting provider *traefik.Provider
2025-06-08T13:29:59Z INF Starting provider *docker.Provider
2025-06-08T13:29:59Z INF Starting provider *acme.ChallengeTLSALPN
2025-06-08T13:47:34Z INF I have to go...
2025-06-08T13:47:34Z INF Stopping server gracefully
2025-06-08T13:47:34Z ERR error="accept tcp [::]:8080: use of closed network connection" entryPointName=traefik
2025-06-08T13:47:34Z ERR error="accept tcp [::]:80: use of closed network connection" entryPointName=web
2025-06-08T13:47:34Z ERR error="close tcp [::]:8080: use of closed network connection" entryPointName=traefik
2025-06-08T13:47:34Z ERR error="accept tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-06-08T13:47:34Z ERR error="close tcp [::]:80: use of closed network connection" entryPointName=web
2025-06-08T13:47:34Z ERR error="close tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-06-08T13:47:34Z INF Server stopped
2025-06-08T13:47:34Z INF Shutting down
2025-06-08T13:47:46Z INF Traefik version 3.0.4 built on 2024-07-02T13:46:37Z version=3.0.4
2025-06-08T13:47:46Z INF 
Stats collection is disabled.
Help us improve Traefik by turning this feature on :)
More details on: https://doc.traefik.io/traefik/contributing/data-collection/

2025-06-08T13:47:46Z INF Starting provider aggregator aggregator.ProviderAggregator
2025-06-08T13:47:46Z INF Starting provider *file.Provider
2025-06-08T13:47:46Z INF Starting provider *traefik.Provider
2025-06-08T13:47:46Z INF Starting provider *acme.ChallengeTLSALPN
2025-06-08T13:47:46Z INF Starting provider *docker.Provider
2025-06-08T13:50:38Z INF I have to go...
2025-06-08T13:50:38Z INF Stopping server gracefully
2025-06-08T13:50:38Z ERR error="accept tcp [::]:80: use of closed network connection" entryPointName=web
2025-06-08T13:50:38Z ERR error="accept tcp [::]:8080: use of closed network connection" entryPointName=traefik
2025-06-08T13:50:38Z ERR error="close tcp [::]:8080: use of closed network connection" entryPointName=traefik
2025-06-08T13:50:38Z ERR error="accept tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-06-08T13:50:38Z ERR error="close tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-06-08T13:50:38Z ERR error="close tcp [::]:80: use of closed network connection" entryPointName=web
2025-06-08T13:50:39Z INF Server stopped
2025-06-08T13:50:39Z INF Shutting down
2025-06-08T13:50:40Z INF Traefik version 3.0.4 built on 2024-07-02T13:46:37Z version=3.0.4
2025-06-08T13:50:40Z INF 
Stats collection is disabled.
Help us improve Traefik by turning this feature on :)
More details on: https://doc.traefik.io/traefik/contributing/data-collection/

2025-06-08T13:50:40Z INF Starting provider aggregator aggregator.ProviderAggregator
2025-06-08T13:50:40Z INF Starting provider *file.Provider
2025-06-08T13:50:40Z INF Starting provider *traefik.Provider
2025-06-08T13:50:40Z INF Starting provider *docker.Provider
2025-06-08T13:50:40Z INF Starting provider *acme.ChallengeTLSALPN
2025-06-08T13:51:39Z INF I have to go...
2025-06-08T13:51:39Z INF Stopping server gracefully
2025-06-08T13:51:39Z ERR error="accept tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-06-08T13:51:39Z ERR error="accept tcp [::]:8080: use of closed network connection" entryPointName=traefik
2025-06-08T13:51:39Z ERR error="close tcp [::]:8080: use of closed network connection" entryPointName=traefik
2025-06-08T13:51:39Z ERR error="accept tcp [::]:80: use of closed network connection" entryPointName=web
2025-06-08T13:51:39Z ERR error="close tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-06-08T13:51:39Z ERR error="close tcp [::]:80: use of closed network connection" entryPointName=web
2025-06-08T13:51:40Z INF Shutting down
2025-06-08T13:51:40Z INF Server stopped
2025-06-08T13:51:40Z INF Traefik version 3.0.4 built on 2024-07-02T13:46:37Z version=3.0.4
2025-06-08T13:51:40Z INF 
Stats collection is disabled.
Help us improve Traefik by turning this feature on :)
More details on: https://doc.traefik.io/traefik/contributing/data-collection/

2025-06-08T13:51:40Z INF Starting provider aggregator aggregator.ProviderAggregator
2025-06-08T13:51:40Z INF Starting provider *file.Provider
2025-06-08T13:51:40Z INF Starting provider *traefik.Provider
2025-06-08T13:51:40Z INF Starting provider *acme.ChallengeTLSALPN
2025-06-08T13:51:40Z INF Starting provider *docker.Provider
2025-06-08T13:53:22Z INF I have to go...
2025-06-08T13:53:22Z INF Stopping server gracefully
2025-06-08T13:53:22Z ERR error="accept tcp [::]:80: use of closed network connection" entryPointName=web
2025-06-08T13:53:22Z ERR error="accept tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-06-08T13:53:22Z ERR error="accept tcp [::]:8080: use of closed network connection" entryPointName=traefik
2025-06-08T13:53:22Z ERR error="close tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-06-08T13:53:22Z ERR error="close tcp [::]:80: use of closed network connection" entryPointName=web
2025-06-08T13:53:22Z ERR error="close tcp [::]:8080: use of closed network connection" entryPointName=traefik
2025-06-08T13:53:22Z INF Server stopped
2025-06-08T13:53:22Z INF Shutting down
2025-06-08T13:53:23Z INF Traefik version 3.0.4 built on 2024-07-02T13:46:37Z version=3.0.4
2025-06-08T13:53:23Z INF 
Stats collection is disabled.
Help us improve Traefik by turning this feature on :)
More details on: https://doc.traefik.io/traefik/contributing/data-collection/

2025-06-08T13:53:23Z INF Starting provider aggregator aggregator.ProviderAggregator
2025-06-08T13:53:23Z INF Starting provider *file.Provider
2025-06-08T13:53:23Z INF Starting provider *traefik.Provider
2025-06-08T13:53:23Z INF Starting provider *acme.ChallengeTLSALPN
2025-06-08T13:53:23Z INF Starting provider *docker.Provider
