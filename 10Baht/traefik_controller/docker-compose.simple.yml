# Simplified Traefik Controller (HTTP only, no SSL)
# Based on the example config - good for quick local development

networks:
  default:
    name: traefik-network

services:
  traefik:
    image: traefik:v3.0
    container_name: traefik-controller-simple
    restart: unless-stopped
    command:
      # Enable Docker provider
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      
      # Configure entrypoints (HTTP only)
      - "--entrypoints.web.address=:80"
      
      # Enable dashboard (insecure for local dev)
      - "--api.dashboard=true"
      - "--api.insecure=true"
      
      # Logging
      - "--log.level=INFO"
      - "--accesslog=true"
    
    ports:
      - "80:80"
      - "8080:8080"  # Dashboard
    
    volumes:
      # Docker socket for container discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro
      
      # Logs
      - ./logs:/var/log/traefik
    
    labels:
      # Enable Traefik for this service (dashboard routing)
      - "traefik.enable=true"
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.10baht`)"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"

volumes:
  traefik-logs:
    name: traefik-logs
    driver: local
