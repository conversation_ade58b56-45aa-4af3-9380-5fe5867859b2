# Advanced Traefik Controller Usage

## Custom Project Configuration

### Manual Project Setup

If the automatic discovery doesn't work perfectly for your project, you can manually create configurations:

1. Create a project directory: `mkdir projects/my-project`
2. Create a custom `docker-compose.traefik.yml` based on the template
3. Add proper Traefik labels for your services

### Custom Domains

To use custom domains instead of the default `.10baht` pattern:

```yaml
# In your project's docker-compose.traefik.yml
services:
  app:
    labels:
      - "traefik.http.routers.myapp.rule=Host(`custom.domain.local`)"
```

Don't forget to add the domain to your `/etc/hosts` file:
```
127.0.0.1 custom.domain.local
```

### Multiple Services per Project

For projects with multiple web services (e.g., frontend + API):

```yaml
services:
  frontend:
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.myproject-frontend.rule=Host(`myproject.10baht`)"
      - "traefik.http.routers.myproject-frontend.entrypoints=websecure"
      - "traefik.http.routers.myproject-frontend.tls=true"
      - "traefik.http.services.myproject-frontend.loadbalancer.server.port=3000"

  api:
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.myproject-api.rule=Host(`myproject.10baht`) && PathPrefix(`/api`)"
      - "traefik.http.routers.myproject-api.entrypoints=websecure"
      - "traefik.http.routers.myproject-api.tls=true"
      - "traefik.http.services.myproject-api.loadbalancer.server.port=8000"
```

## Middleware Configuration

### Basic Authentication

Add basic auth to a service:

```yaml
services:
  app:
    labels:
      - "traefik.http.routers.myapp.middlewares=myapp-auth"
      - "traefik.http.middlewares.myapp-auth.basicauth.users=admin:$$2y$$10$$..."
```

Generate password hash:
```bash
echo $(htpasswd -nb admin password) | sed -e s/\\$/\\$\\$/g
```

### Rate Limiting

```yaml
services:
  app:
    labels:
      - "traefik.http.routers.myapp.middlewares=myapp-ratelimit"
      - "traefik.http.middlewares.myapp-ratelimit.ratelimit.burst=100"
      - "traefik.http.middlewares.myapp-ratelimit.ratelimit.average=50"
```

### CORS Headers

```yaml
services:
  app:
    labels:
      - "traefik.http.routers.myapp.middlewares=myapp-cors"
      - "traefik.http.middlewares.myapp-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE"
      - "traefik.http.middlewares.myapp-cors.headers.accesscontrolalloworigin=*"
      - "traefik.http.middlewares.myapp-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.myapp-cors.headers.addvaryheader=true"
```

## SSL/TLS Configuration

### Custom Certificates

To use custom certificates instead of mkcert:

1. Place your certificates in the `certs/` directory
2. Update `dynamic/tls.yml`:

```yaml
tls:
  certificates:
    - certFile: /etc/traefik/certs/custom.crt
      keyFile: /etc/traefik/certs/custom.key
```

### Let's Encrypt (Production)

For production use with real domains:

```yaml
# In traefik.yml
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme.json
      httpChallenge:
        entryPoint: web
```

Then use in project labels:
```yaml
- "traefik.http.routers.myapp.tls.certresolver=letsencrypt"
```

## Monitoring and Debugging

### Access Logs

Access logs are stored in `logs/access.log` in JSON format. Useful for debugging routing issues.

### Traefik API

The Traefik API is available at `https://traefik.10baht/api/` for debugging:

- `/api/http/routers` - List all routers
- `/api/http/services` - List all services
- `/api/http/middlewares` - List all middlewares

### Debug Mode

Enable debug logging by modifying `traefik.yml`:

```yaml
log:
  level: DEBUG
```

## Performance Optimization

### Connection Pooling

```yaml
services:
  app:
    labels:
      - "traefik.http.services.myapp.loadbalancer.passhostheader=true"
      - "traefik.http.services.myapp.loadbalancer.responseforwarding.flushinterval=1ms"
```

### Compression

```yaml
services:
  app:
    labels:
      - "traefik.http.routers.myapp.middlewares=myapp-compress"
      - "traefik.http.middlewares.myapp-compress.compress=true"
```

## Troubleshooting

### Common Issues

1. **Service not accessible**: Check if containers are on the `traefik-network`
2. **SSL errors**: Regenerate certificates with `./scripts/install-mkcert.sh`
3. **Port conflicts**: Ensure no other services are using ports 80/443
4. **Domain not resolving**: Check `/etc/hosts` or DNS configuration

### Debug Commands

```bash
# Check Traefik configuration
docker exec traefik-controller traefik version

# List all routers
curl -s https://traefik.10baht/api/http/routers | jq

# Check container networks
docker network ls
docker network inspect traefik-network

# View real-time logs
docker compose logs -f traefik
```

## Integration with CI/CD

### Automated Deployment

Example GitHub Actions workflow:

```yaml
name: Deploy to Traefik
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v2
      - name: Deploy with Traefik
        run: |
          cd /path/to/traefik_controller
          ./scripts/discover-projects.sh
          ./scripts/start-project.sh ${{ github.event.repository.name }}
```
