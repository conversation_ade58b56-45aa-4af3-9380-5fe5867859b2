#!/bin/bash

# Start all configured projects
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"
PROJECTS_DIR="$TRAEFIK_DIR/projects"

echo "🚀 Starting all configured projects..."

# Check if <PERSON><PERSON><PERSON><PERSON> is running
if ! docker ps | grep -q "traefik-controller"; then
    echo "❌ Traefik Controller is not running!"
    echo "🚀 Start it first with: ./scripts/start.sh"
    exit 1
fi

# Check if projects directory exists
if [ ! -d "$PROJECTS_DIR" ]; then
    echo "❌ No projects configured!"
    echo "🔧 Run './scripts/discover-projects.sh' first"
    exit 1
fi

# Find all project directories
PROJECTS=()
for project_dir in "$PROJECTS_DIR"/*; do
    if [ -d "$project_dir" ]; then
        project_name=$(basename "$project_dir")
        PROJECTS+=("$project_name")
    fi
done

if [ ${#PROJECTS[@]} -eq 0 ]; then
    echo "❌ No projects found!"
    echo "🔧 Run './scripts/discover-projects.sh' first"
    exit 1
fi

echo "📋 Found ${#PROJECTS[@]} projects to start:"
for project in "${PROJECTS[@]}"; do
    echo "   📦 $project"
done

echo ""

# Start each project
STARTED_PROJECTS=()
FAILED_PROJECTS=()

for project in "${PROJECTS[@]}"; do
    echo "🚀 Starting $project..."
    
    if "$SCRIPT_DIR/start-project.sh" "$project" > /dev/null 2>&1; then
        STARTED_PROJECTS+=("$project")
        echo "   ✅ $project started"
    else
        FAILED_PROJECTS+=("$project")
        echo "   ❌ $project failed to start"
    fi
done

echo ""
echo "📊 Summary:"
echo "   ✅ Started: ${#STARTED_PROJECTS[@]} projects"
echo "   ❌ Failed: ${#FAILED_PROJECTS[@]} projects"

if [ ${#STARTED_PROJECTS[@]} -gt 0 ]; then
    echo ""
    echo "🌐 Running projects:"
    for project in "${STARTED_PROJECTS[@]}"; do
        domain="${project,,}.10baht"
        echo "   🌐 $project -> https://$domain"
    done
fi

if [ ${#FAILED_PROJECTS[@]} -gt 0 ]; then
    echo ""
    echo "❌ Failed projects:"
    for project in "${FAILED_PROJECTS[@]}"; do
        echo "   📦 $project"
    done
    echo ""
    echo "🔍 Check individual project logs for details"
fi

echo ""
echo "🌐 Traefik Dashboard: https://traefik.10baht"
