#!/bin/bash

# Helper script to add Traefik labels to a project
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"

show_help() {
    echo "🏷️  Add Traefik Labels to Project"
    echo "================================"
    echo ""
    echo "This script helps you add Traefik labels to your project's docker-compose.yml"
    echo ""
    echo "Usage: $0 <project-name> [service-name] [port] [--simple]"
    echo ""
    echo "Arguments:"
    echo "  project-name    Name of your project (will become projectname.10baht)"
    echo "  service-name    Name of the service in docker-compose.yml (default: app)"
    echo "  port           Port your service runs on (default: 8000)"
    echo "  --simple       Generate HTTP-only labels (no HTTPS/SSL)"
    echo ""
    echo "Examples:"
    echo "  $0 myproject                    # myproject.10baht -> app service on port 8000 (HTTPS)"
    echo "  $0 myproject frontend 3000      # myproject.10baht -> frontend service on port 3000 (HTTPS)"
    echo "  $0 myproject api 8080 --simple  # myproject.10baht -> api service on port 8080 (HTTP only)"
}

if [ $# -eq 0 ] || [ "$1" = "help" ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

PROJECT_NAME="$1"
SERVICE_NAME="${2:-app}"
SERVICE_PORT="${3:-8000}"
SIMPLE_MODE=true  # Default to HTTP only
DOMAIN="${PROJECT_NAME,,}.docker.localhost"  # Convert to lowercase

# Check for --https flag to enable HTTPS mode
for arg in "$@"; do
    if [ "$arg" = "--https" ]; then
        SIMPLE_MODE=false
        break
    fi
done

echo "🏷️  Generating Traefik labels for:"
echo "   Project: $PROJECT_NAME"
echo "   Service: $SERVICE_NAME"
echo "   Domain: $DOMAIN"
echo "   Port: $SERVICE_PORT"
if [ "$SIMPLE_MODE" = true ]; then
    echo "   Mode: HTTP only"
else
    echo "   Mode: HTTPS with SSL"
fi
echo ""

# Generate the labels
if [ "$SIMPLE_MODE" = true ]; then
    # Simple HTTP-only labels
    cat << EOF
# Add these labels to your '$SERVICE_NAME' service in docker-compose.yml:

services:
  $SERVICE_NAME:
    # ... your existing configuration ...
    networks:
      - net  # HTTP mode uses net network
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"

      # HTTP routing
      - "traefik.http.routers.$PROJECT_NAME-$SERVICE_NAME.rule=Host(\`$DOMAIN\`)"
      - "traefik.http.routers.$PROJECT_NAME-$SERVICE_NAME.entrypoints=web"
      - "traefik.http.services.$PROJECT_NAME-$SERVICE_NAME.loadbalancer.server.port=$SERVICE_PORT"

# Add this network section at the bottom of your docker-compose.yml:

networks:
  net:
    external: true
    name: net

EOF
else
    # Full HTTPS labels
    cat << EOF
# Add these labels to your '$SERVICE_NAME' service in docker-compose.yml:

services:
  $SERVICE_NAME:
    # ... your existing configuration ...
    networks:
      - default
      - net
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"

      # HTTPS routing
      - "traefik.http.routers.$PROJECT_NAME-$SERVICE_NAME.rule=Host(\`$DOMAIN\`)"
      - "traefik.http.routers.$PROJECT_NAME-$SERVICE_NAME.entrypoints=websecure"
      - "traefik.http.routers.$PROJECT_NAME-$SERVICE_NAME.tls=true"
      - "traefik.http.services.$PROJECT_NAME-$SERVICE_NAME.loadbalancer.server.port=$SERVICE_PORT"

      # HTTP to HTTPS redirect
      - "traefik.http.routers.$PROJECT_NAME-$SERVICE_NAME-http.rule=Host(\`$DOMAIN\`)"
      - "traefik.http.routers.$PROJECT_NAME-$SERVICE_NAME-http.entrypoints=web"
      - "traefik.http.routers.$PROJECT_NAME-$SERVICE_NAME-http.middlewares=redirect-to-https"

# Add this network section at the bottom of your docker-compose.yml:

networks:
  net:
    external: true
    name: net

EOF
fi

echo ""
echo "📋 Next steps:"
echo "1. Copy the labels above to your .dockerwrapper/docker-compose.yml"
echo "2. Make sure your service is connected to both 'default' and 'net'"
echo "3. Remove any port mappings like '8000:8000' (Traefik will handle routing)"
echo "4. Start your project: cd .dockerwrapper && docker compose up -d"
if [ "$SIMPLE_MODE" = true ]; then
    echo "5. Access your project at: http://$DOMAIN"
else
    echo "5. Access your project at: https://$DOMAIN"
fi
echo ""
echo "💡 For multiple services (API + Frontend), run this script multiple times"
echo "💡 Use PathPrefix for API routes: add '&& PathPrefix(\`/api\`)' to the rule"
