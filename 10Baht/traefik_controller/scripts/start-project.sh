#!/bin/bash

# Start a specific project with Traefik integration
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"
PROJECTS_DIR="$TRAEFIK_DIR/projects"

# Check if project name is provided
if [ $# -eq 0 ]; then
    echo "❌ Please provide a project name"
    echo ""
    echo "Usage: $0 <project-name>"
    echo ""
    echo "Available projects:"
    if [ -d "$PROJECTS_DIR" ]; then
        for project_dir in "$PROJECTS_DIR"/*; do
            if [ -d "$project_dir" ]; then
                project_name=$(basename "$project_dir")
                if [ -f "$project_dir/project-info.yml" ]; then
                    domain=$(grep "domain:" "$project_dir/project-info.yml" | cut -d' ' -f2)
                    echo "   📦 $project_name -> https://$domain"
                else
                    echo "   📦 $project_name"
                fi
            fi
        done
    else
        echo "   ⚠️  No projects configured. Run './scripts/discover-projects.sh' first."
    fi
    exit 1
fi

PROJECT_NAME="$1"
PROJECT_CONFIG_DIR="$PROJECTS_DIR/$PROJECT_NAME"

# Check if project exists
if [ ! -d "$PROJECT_CONFIG_DIR" ]; then
    echo "❌ Project '$PROJECT_NAME' not found!"
    echo ""
    echo "Available projects:"
    for project_dir in "$PROJECTS_DIR"/*; do
        if [ -d "$project_dir" ]; then
            project_name=$(basename "$project_dir")
            echo "   📦 $project_name"
        fi
    done
    echo ""
    echo "💡 Run './scripts/discover-projects.sh' to refresh project list"
    exit 1
fi

# Check if Traefik is running
if ! docker ps | grep -q "traefik-controller"; then
    echo "❌ Traefik Controller is not running!"
    echo "🚀 Start it first with: ./scripts/start.sh"
    exit 1
fi

echo "🚀 Starting project: $PROJECT_NAME"

# Change to project config directory
cd "$PROJECT_CONFIG_DIR"

# Check if traefik compose file exists
if [ ! -f "docker-compose.traefik.yml" ]; then
    echo "❌ Traefik configuration not found for $PROJECT_NAME"
    echo "🔧 Run './scripts/discover-projects.sh' to regenerate configurations"
    exit 1
fi

# Start the project
echo "📋 Starting containers..."
docker-compose -f docker-compose.traefik.yml up -d

# Wait a moment for containers to start
sleep 3

# Check if containers are running
if docker-compose -f docker-compose.traefik.yml ps | grep -q "Up"; then
    echo "✅ $PROJECT_NAME started successfully!"
    
    # Get domain from project info
    if [ -f "project-info.yml" ]; then
        domain=$(grep "domain:" "project-info.yml" | cut -d' ' -f2)
        echo "🌐 Available at: https://$domain"
    fi
    
    echo ""
    echo "📋 Container status:"
    docker-compose -f docker-compose.traefik.yml ps
    
    echo ""
    echo "🔍 View logs with:"
    echo "   docker-compose -f $PROJECT_CONFIG_DIR/docker-compose.traefik.yml logs -f"
    
    echo ""
    echo "🛑 Stop with:"
    echo "   ./scripts/stop-project.sh $PROJECT_NAME"
else
    echo "❌ Failed to start $PROJECT_NAME"
    echo "🔍 Check logs with:"
    echo "   docker-compose -f $PROJECT_CONFIG_DIR/docker-compose.traefik.yml logs"
    exit 1
fi
