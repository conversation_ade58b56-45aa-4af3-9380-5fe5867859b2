#!/bin/bash

# Discover and configure 10Baht projects for Traefik
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"
PROJECTS_DIR="$TRAEFIK_DIR/projects"
BAHT_DIR="$(dirname "$TRAEFIK_DIR")"

# Function to generate Traefik-enabled docker-compose.yml
generate_traefik_compose() {
    local project_name="$1"
    local project_path="$2"
    local config_dir="$3"
    local domain="${project_name,,}.10baht"  # Convert to lowercase

    # Create a Traefik-enabled version of the docker-compose.yml
    cat > "$config_dir/docker-compose.traefik.yml" << EOF
# Traefik-enabled configuration for $project_name
# Generated automatically - do not edit manually
# Original: $project_path/.dockerwrapper/docker-compose.yml

version: '3.8'

# Include original services from the project
include:
  - path: $project_path/.dockerwrapper/docker-compose.yml

# Override services to add Traefik labels
services:
EOF

    # Analyze the original compose file to find web services
    # This is a simplified approach - we'll add Traefik labels to likely web services
    if grep -q "ports:" "$project_path/.dockerwrapper/docker-compose.yml"; then
        # Find services with exposed ports (likely web services)
        local web_services=($(grep -B 10 "ports:" "$project_path/.dockerwrapper/docker-compose.yml" | grep -E "^[[:space:]]*[a-zA-Z0-9_-]+:" | sed 's/://g' | xargs))

        for service in "${web_services[@]}"; do
            # Skip database and cache services
            if [[ "$service" =~ ^(db|database|postgres|mysql|redis|mongo|elasticsearch)$ ]]; then
                continue
            fi

            cat >> "$config_dir/docker-compose.traefik.yml" << EOF
  $service:
    networks:
      - traefik-network
      - default
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.$project_name-$service.rule=Host(\`$domain\`)"
      - "traefik.http.routers.$project_name-$service.entrypoints=websecure"
      - "traefik.http.routers.$project_name-$service.tls=true"
      - "traefik.http.services.$project_name-$service.loadbalancer.server.port=8000"

      # HTTP to HTTPS redirect
      - "traefik.http.routers.$project_name-$service-http.rule=Host(\`$domain\`)"
      - "traefik.http.routers.$project_name-$service-http.entrypoints=web"
      - "traefik.http.routers.$project_name-$service-http.middlewares=redirect-to-https"

EOF
        done
    fi

    cat >> "$config_dir/docker-compose.traefik.yml" << EOF

networks:
  traefik-network:
    external: true
    name: traefik-network
EOF
}

# Function to generate project-specific scripts
generate_project_scripts() {
    local project_name="$1"
    local project_path="$2"
    local config_dir="$3"

    # Generate start script for this project
    cat > "$config_dir/start.sh" << EOF
#!/bin/bash
# Start $project_name with Traefik integration
set -e

echo "🚀 Starting $project_name..."
cd "$config_dir"
docker compose -f docker-compose.traefik.yml up -d

echo "✅ $project_name started!"
echo "🌐 Available at: https://${project_name,,}.10baht"
EOF

    # Generate stop script for this project
    cat > "$config_dir/stop.sh" << EOF
#!/bin/bash
# Stop $project_name
set -e

echo "🛑 Stopping $project_name..."
cd "$config_dir"
docker compose -f docker-compose.traefik.yml down

echo "✅ $project_name stopped!"
EOF

    # Make scripts executable
    chmod +x "$config_dir/start.sh"
    chmod +x "$config_dir/stop.sh"

    # Generate project info file
    cat > "$config_dir/project-info.yml" << EOF
name: $project_name
domain: ${project_name,,}.10baht
path: $project_path
dockerwrapper: $project_path/.dockerwrapper
traefik_config: $config_dir/docker-compose.traefik.yml
EOF
}

echo "🔍 Discovering 10Baht projects with .dockerwrapper folders..."

# Create projects directory
mkdir -p "$PROJECTS_DIR"

# Find all projects with .dockerwrapper folders
DISCOVERED_PROJECTS=()
while IFS= read -r -d '' dockerwrapper_path; do
    project_path="$(dirname "$dockerwrapper_path")"
    project_name="$(basename "$project_path")"
    
    # Skip if it's the traefik_controller itself
    if [ "$project_name" = "traefik_controller" ]; then
        continue
    fi
    
    echo "📦 Found project: $project_name"
    DISCOVERED_PROJECTS+=("$project_name:$project_path")
done < <(find "$BAHT_DIR" -name ".dockerwrapper" -type d -print0)

if [ ${#DISCOVERED_PROJECTS[@]} -eq 0 ]; then
    echo "❌ No projects with .dockerwrapper folders found!"
    exit 1
fi

echo ""
echo "🔧 Configuring projects for Traefik..."

for project_info in "${DISCOVERED_PROJECTS[@]}"; do
    IFS=':' read -r project_name project_path <<< "$project_info"
    
    echo "⚙️  Configuring $project_name..."
    
    # Create project directory in traefik_controller
    project_config_dir="$PROJECTS_DIR/$project_name"
    mkdir -p "$project_config_dir"
    
    # Analyze the original docker-compose.yml
    original_compose="$project_path/.dockerwrapper/docker-compose.yml"
    
    if [ ! -f "$original_compose" ]; then
        echo "   ⚠️  No docker-compose.yml found in $project_name/.dockerwrapper/"
        continue
    fi
    
    # Generate Traefik-enabled docker-compose.yml
    generate_traefik_compose "$project_name" "$project_path" "$project_config_dir"
    
    # Generate project-specific scripts
    generate_project_scripts "$project_name" "$project_path" "$project_config_dir"
    
    echo "   ✅ $project_name configured for Traefik"
done

echo ""
echo "✅ Project discovery complete!"
echo ""
echo "📋 Discovered projects:"
for project_info in "${DISCOVERED_PROJECTS[@]}"; do
    IFS=':' read -r project_name project_path <<< "$project_info"
    domain="${project_name,,}.10baht"  # Convert to lowercase
    echo "   🌐 $project_name -> https://$domain"
done

echo ""
echo "🚀 Start projects with:"
echo "   ./scripts/start-project.sh <project-name>"
echo ""
echo "🌐 Or start all projects:"
echo "   ./scripts/start-all-projects.sh"


