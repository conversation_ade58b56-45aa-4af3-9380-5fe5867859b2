#!/bin/bash

# Stop a specific project
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"
PROJECTS_DIR="$TRAEFIK_DIR/projects"

# Check if project name is provided
if [ $# -eq 0 ]; then
    echo "❌ Please provide a project name"
    echo ""
    echo "Usage: $0 <project-name>"
    echo ""
    echo "Available projects:"
    if [ -d "$PROJECTS_DIR" ]; then
        for project_dir in "$PROJECTS_DIR"/*; do
            if [ -d "$project_dir" ]; then
                project_name=$(basename "$project_dir")
                echo "   📦 $project_name"
            fi
        done
    else
        echo "   ⚠️  No projects configured. Run './scripts/discover-projects.sh' first."
    fi
    exit 1
fi

PROJECT_NAME="$1"
PROJECT_CONFIG_DIR="$PROJECTS_DIR/$PROJECT_NAME"

# Check if project exists
if [ ! -d "$PROJECT_CONFIG_DIR" ]; then
    echo "❌ Project '$PROJECT_NAME' not found!"
    echo ""
    echo "Available projects:"
    for project_dir in "$PROJECTS_DIR"/*; do
        if [ -d "$project_dir" ]; then
            project_name=$(basename "$project_dir")
            echo "   📦 $project_name"
        fi
    done
    exit 1
fi

echo "🛑 Stopping project: $PROJECT_NAME"

# Change to project config directory
cd "$PROJECT_CONFIG_DIR"

# Check if traefik compose file exists
if [ ! -f "docker-compose.traefik.yml" ]; then
    echo "❌ Traefik configuration not found for $PROJECT_NAME"
    exit 1
fi

# Stop the project
echo "📋 Stopping containers..."
docker-compose -f docker-compose.traefik.yml down

echo "✅ $PROJECT_NAME stopped successfully!"
