#!/bin/bash

# Verify Traefik Controller setup
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"

echo "🔍 Verifying Traefik Controller setup..."

# Check Docker Compose v2
echo "📋 Checking Docker Compose..."
if docker compose version &> /dev/null; then
    echo "✅ Docker Compose v2 is available"
    docker compose version
else
    echo "❌ Docker Compose v2 is not available"
    echo "💡 Please install Docker Compose v2 or update Docker Desktop"
    exit 1
fi

# Check Docker daemon
echo ""
echo "🐳 Checking Docker daemon..."
if docker info &> /dev/null; then
    echo "✅ Docker daemon is running"
else
    echo "❌ Docker daemon is not running"
    echo "💡 Please start Docker"
    exit 1
fi

# Check required directories
echo ""
echo "📁 Checking directory structure..."
cd "$TRAEFIK_DIR"

required_dirs=("scripts" "certs" "logs" "dynamic" "projects")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/ exists"
    else
        echo "⚠️  Creating $dir/"
        mkdir -p "$dir"
    fi
done

# Check required files
echo ""
echo "📄 Checking required files..."
required_files=("docker-compose.yml" "traefik.yml" "traefik-ctl")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file is missing"
        exit 1
    fi
done

# Check script permissions
echo ""
echo "🔐 Checking script permissions..."
for script in scripts/*.sh traefik-ctl; do
    if [ -x "$script" ]; then
        echo "✅ $script is executable"
    else
        echo "⚠️  Making $script executable"
        chmod +x "$script"
    fi
done

# Check for mkcert
echo ""
echo "🔑 Checking mkcert..."
if command -v mkcert &> /dev/null; then
    echo "✅ mkcert is installed"
    mkcert -version
else
    echo "⚠️  mkcert is not installed"
    echo "💡 Run './scripts/install-mkcert.sh' to install it"
fi

# Check certificates
echo ""
echo "📜 Checking SSL certificates..."
if [ -f "certs/10baht.crt" ] && [ -f "certs/10baht.key" ]; then
    echo "✅ SSL certificates exist"
else
    echo "⚠️  SSL certificates not found"
    echo "💡 Run './scripts/install-mkcert.sh' to generate them"
fi

# Check for projects
echo ""
echo "📦 Checking for 10Baht projects..."
BAHT_DIR="$(dirname "$TRAEFIK_DIR")"
project_count=0
while IFS= read -r -d '' dockerwrapper_path; do
    project_path="$(dirname "$dockerwrapper_path")"
    project_name="$(basename "$project_path")"
    
    # Skip traefik_controller itself
    if [ "$project_name" = "traefik_controller" ]; then
        continue
    fi
    
    echo "📦 Found: $project_name"
    ((project_count++))
done < <(find "$BAHT_DIR" -name ".dockerwrapper" -type d -print0 2>/dev/null)

if [ $project_count -eq 0 ]; then
    echo "⚠️  No projects with .dockerwrapper folders found"
    echo "💡 Run './scripts/discover-projects.sh' after creating projects"
else
    echo "✅ Found $project_count projects"
fi

echo ""
echo "🎉 Setup verification complete!"
echo ""
echo "📋 Next steps:"
if ! command -v mkcert &> /dev/null || [ ! -f "certs/10baht.crt" ]; then
    echo "   1. Run './scripts/install-mkcert.sh' to set up SSL certificates"
fi
echo "   2. Run './traefik-ctl start' to start Traefik"
echo "   3. Run './traefik-ctl discover' to configure projects"
echo "   4. Run './traefik-ctl start-project <name>' to start individual projects"
echo ""
echo "💡 Use './traefik-ctl help' for all available commands"
