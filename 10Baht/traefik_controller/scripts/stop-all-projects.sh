#!/bin/bash

# Stop all running projects
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"
PROJECTS_DIR="$TRAEFIK_DIR/projects"

echo "🛑 Stopping all projects..."

# Check if projects directory exists
if [ ! -d "$PROJECTS_DIR" ]; then
    echo "❌ No projects configured!"
    exit 1
fi

# Find all project directories
PROJECTS=()
for project_dir in "$PROJECTS_DIR"/*; do
    if [ -d "$project_dir" ]; then
        project_name=$(basename "$project_dir")
        PROJECTS+=("$project_name")
    fi
done

if [ ${#PROJECTS[@]} -eq 0 ]; then
    echo "❌ No projects found!"
    exit 1
fi

echo "📋 Found ${#PROJECTS[@]} projects to stop:"
for project in "${PROJECTS[@]}"; do
    echo "   📦 $project"
done

echo ""

# Stop each project
STOPPED_PROJECTS=()
FAILED_PROJECTS=()

for project in "${PROJECTS[@]}"; do
    echo "🛑 Stopping $project..."
    
    if "$SCRIPT_DIR/stop-project.sh" "$project" > /dev/null 2>&1; then
        STOPPED_PROJECTS+=("$project")
        echo "   ✅ $project stopped"
    else
        FAILED_PROJECTS+=("$project")
        echo "   ❌ $project failed to stop (may not be running)"
    fi
done

echo ""
echo "📊 Summary:"
echo "   ✅ Stopped: ${#STOPPED_PROJECTS[@]} projects"
echo "   ❌ Failed: ${#FAILED_PROJECTS[@]} projects"

if [ ${#FAILED_PROJECTS[@]} -gt 0 ]; then
    echo ""
    echo "❌ Failed to stop:"
    for project in "${FAILED_PROJECTS[@]}"; do
        echo "   📦 $project"
    done
fi

echo ""
echo "✅ All projects stopped!"
echo "💡 Traefik Controller is still running. Stop it with: ./scripts/stop.sh"
