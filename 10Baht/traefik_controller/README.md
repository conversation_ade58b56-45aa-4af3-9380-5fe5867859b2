# Traefik Controller for 10Baht Projects

This repository provides a centralized Traefik reverse proxy for all 10Baht projects. Projects self-register by adding Traefik labels to their docker-compose files.

## Features

- **Automatic Domain Routing**: Projects get `projectname.10baht` domains via labels
- **Port Management**: No more port conflicts - Traefik handles everything on 80/443
- **SSL Certificates**: Locally trusted certificates using mkcert
- **Self-Registration**: Projects add their own Traefik labels
- **Dashboard**: Web UI at `traefik.10baht` to monitor routes

## Quick Start

```bash
# 1. Install mkcert and start Traefik
./traefik-ctl setup

# 2. Add Traefik labels to your project's docker-compose.yml
# (see examples below)

# 3. Start your project normally from its .dockerwrapper folder
cd ../your-project/.dockerwrapper
docker compose up -d
```

## How It Works

1. **Traefik Controller**: Runs as the main reverse proxy on ports 80/443
2. **Project Self-Registration**: Projects add Traefik labels to their services
3. **Automatic Discovery**: Traefik automatically detects labeled containers
4. **Domain Resolution**: Projects become accessible via `.10baht` domains
5. **SSL Termination**: Trae<PERSON>k handles SSL with locally trusted certificates

## Adding Traefik to Your Project

Add these labels to your main web service in `.dockerwrapper/docker-compose.yml`:

```yaml
services:
  your-app:
    # ... your existing configuration ...
    networks:
      - default
      - traefik-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.yourproject.rule=Host(\`yourproject.10baht\`)"
      - "traefik.http.routers.yourproject.entrypoints=websecure"
      - "traefik.http.routers.yourproject.tls=true"
      - "traefik.http.services.yourproject.loadbalancer.server.port=8000"

      # HTTP to HTTPS redirect
      - "traefik.http.routers.yourproject-http.rule=Host(\`yourproject.10baht\`)"
      - "traefik.http.routers.yourproject-http.entrypoints=web"
      - "traefik.http.routers.yourproject-http.middlewares=redirect-to-https"

networks:
  traefik-network:
    external: true
    name: traefik-network
```

## Example Project Domains

- `turdparty.10baht` - TurdParty project
- `certrats.10baht` - CertRats project
- `webotter.10baht` - WebOTteR project
- `traefik.10baht` - Traefik dashboard

## Directory Structure

```
traefik_controller/
├── README.md                 # This file
├── docker-compose.yml        # Main Traefik container
├── traefik.yml              # Traefik configuration
├── traefik-ctl              # Main management script
├── scripts/                 # Management scripts
│   ├── install-mkcert.sh    # Install mkcert for SSL
│   ├── start.sh             # Start Traefik
│   ├── stop.sh              # Stop Traefik
│   └── status.sh            # Show Traefik status
├── examples/                # Example configurations
├── certs/                   # SSL certificates
├── dynamic/                 # Dynamic Traefik config
└── logs/                    # Traefik logs
```

## Common Label Patterns

### Basic Web Service
```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.myapp.rule=Host(\`myapp.10baht\`)"
  - "traefik.http.routers.myapp.entrypoints=websecure"
  - "traefik.http.routers.myapp.tls=true"
  - "traefik.http.services.myapp.loadbalancer.server.port=8000"
```

### API with Path Prefix
```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.myapi.rule=Host(\`myapp.10baht\`) && PathPrefix(\`/api\`)"
  - "traefik.http.routers.myapi.entrypoints=websecure"
  - "traefik.http.routers.myapi.tls=true"
  - "traefik.http.services.myapi.loadbalancer.server.port=8000"
```

### Frontend (Port 80/3000)
```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.myfrontend.rule=Host(\`myapp.10baht\`)"
  - "traefik.http.routers.myfrontend.entrypoints=websecure"
  - "traefik.http.routers.myfrontend.tls=true"
  - "traefik.http.services.myfrontend.loadbalancer.server.port=3000"
```

## Management Commands

```bash
# Start/stop Traefik
./traefik-ctl start
./traefik-ctl stop

# Check status and routes
./traefik-ctl status
./traefik-ctl routes

# Generate labels for your project
./traefik-ctl add-labels myproject api 8000

# View logs and dashboard
./traefik-ctl logs
./traefik-ctl dashboard
```

## Troubleshooting

- **Domain not resolving**: Check if mkcert is installed and certificates are generated
- **Port conflicts**: Stop other services using ports 80/443
- **Service not appearing**: Check if container has correct Traefik labels and is on traefik-network
- **SSL errors**: Regenerate certificates with `./scripts/install-mkcert.sh`

## Manual Configuration

For advanced users, you can manually edit:
- `traefik.yml` - Main Traefik configuration
- `dynamic/` - Dynamic configuration files
