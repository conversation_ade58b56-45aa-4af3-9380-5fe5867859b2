# Example Traefik-enabled docker-compose.yml template
# This shows how projects should be configured for Traefik integration

version: '3.8'

# Include original services from the project
include:
  - path: ../PROJECT_NAME/.dockerwrapper/docker-compose.yml

# Override services to add Traefik labels
services:
  # Main application service
  app:
    networks:
      - traefik-network
      - default
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"
      
      # HTTPS router
      - "traefik.http.routers.PROJECT_NAME-app.rule=Host(`PROJECT_NAME.10baht`)"
      - "traefik.http.routers.PROJECT_NAME-app.entrypoints=websecure"
      - "traefik.http.routers.PROJECT_NAME-app.tls=true"
      - "traefik.http.services.PROJECT_NAME-app.loadbalancer.server.port=8000"
      
      # HTTP to HTTPS redirect
      - "traefik.http.routers.PROJECT_NAME-app-http.rule=Host(`PROJECT_NAME.10baht`)"
      - "traefik.http.routers.PROJECT_NAME-app-http.entrypoints=web"
      - "traefik.http.routers.PROJECT_NAME-app-http.middlewares=redirect-to-https"
      
      # Optional: Custom middleware for this service
      # - "traefik.http.routers.PROJECT_NAME-app.middlewares=PROJECT_NAME-auth"
      # - "traefik.http.middlewares.PROJECT_NAME-auth.basicauth.users=admin:$$2y$$10$$..."

  # API service (if separate)
  api:
    networks:
      - traefik-network
      - default
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.PROJECT_NAME-api.rule=Host(`PROJECT_NAME.10baht`) && PathPrefix(`/api`)"
      - "traefik.http.routers.PROJECT_NAME-api.entrypoints=websecure"
      - "traefik.http.routers.PROJECT_NAME-api.tls=true"
      - "traefik.http.services.PROJECT_NAME-api.loadbalancer.server.port=8000"

  # Frontend service (if separate)
  frontend:
    networks:
      - traefik-network
      - default
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.PROJECT_NAME-frontend.rule=Host(`PROJECT_NAME.10baht`)"
      - "traefik.http.routers.PROJECT_NAME-frontend.entrypoints=websecure"
      - "traefik.http.routers.PROJECT_NAME-frontend.tls=true"
      - "traefik.http.services.PROJECT_NAME-frontend.loadbalancer.server.port=80"

  # Database service (usually no Traefik labels needed)
  db:
    # Databases typically don't need Traefik routing
    # They stay on the internal network only
    networks:
      - default

networks:
  traefik-network:
    external: true
    name: traefik-network

# Common Traefik label patterns:
#
# Basic service:
# - "traefik.enable=true"
# - "traefik.http.routers.SERVICE_NAME.rule=Host(`domain.10baht`)"
# - "traefik.http.routers.SERVICE_NAME.entrypoints=websecure"
# - "traefik.http.routers.SERVICE_NAME.tls=true"
# - "traefik.http.services.SERVICE_NAME.loadbalancer.server.port=PORT"
#
# Path-based routing:
# - "traefik.http.routers.SERVICE_NAME.rule=Host(`domain.10baht`) && PathPrefix(`/path`)"
#
# Multiple domains:
# - "traefik.http.routers.SERVICE_NAME.rule=Host(`domain1.10baht`) || Host(`domain2.10baht`)"
#
# Custom middleware:
# - "traefik.http.routers.SERVICE_NAME.middlewares=middleware-name"
# - "traefik.http.middlewares.middleware-name.TYPE.OPTION=value"
#
# Health check:
# - "traefik.http.services.SERVICE_NAME.loadbalancer.healthcheck.path=/health"
