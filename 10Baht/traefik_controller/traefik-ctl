#!/bin/bash

# Traefik Controller Management Script
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

show_help() {
    echo "🚀 Traefik Controller Management"
    echo "================================"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  setup                    - Initial setup (install mkcert, start Traefik)"
    echo "  start                    - Start Traefik Controller"
    echo "  stop                     - Stop Traefik Controller"
    echo "  status                   - Show Traefik status and routes"
    echo "  logs                     - Show Traefik logs"
    echo "  dashboard               - Open Traefik dashboard in browser"
    echo "  routes                  - Show current routes"
    echo "  add-labels <project> [service] [port] - Generate Traefik labels for a project"
    echo "  mode <simple|full|status> - Switch between HTTP/HTTPS modes"
    echo "  help                    - Show this help"
    echo ""
    echo "Project Management:"
    echo "  Projects self-register by adding Traefik labels to their docker-compose.yml"
    echo "  Start projects normally from their .dockerwrapper folders"
    echo ""
    echo "Examples:"
    echo "  $0 setup                # First-time setup"
    echo "  $0 status               # Check Traefik status"
    echo "  $0 routes               # See active routes"
    echo "  $0 add-labels myproject # Generate labels for myproject"
    echo "  $0 dashboard            # Open web dashboard"
}

case "${1:-help}" in
    setup)
        echo -e "${BLUE}🔧 Setting up Traefik Controller...${NC}"
        "$SCRIPT_DIR/scripts/install-mkcert.sh"
        "$SCRIPT_DIR/scripts/start.sh"
        echo -e "${GREEN}✅ Setup complete!${NC}"
        echo -e "${YELLOW}💡 Add Traefik labels to your projects and start them normally${NC}"
        echo -e "${YELLOW}💡 See README.md for label examples${NC}"
        ;;
    start)
        "$SCRIPT_DIR/scripts/start.sh"
        ;;
    stop)
        "$SCRIPT_DIR/scripts/stop.sh"
        ;;
    status)
        echo -e "${BLUE}📊 Traefik Controller Status${NC}"
        echo "================================"

        # Check Traefik status
        cd "$SCRIPT_DIR"
        if docker ps | grep -q "traefik-controller"; then
            echo -e "${GREEN}✅ Traefik Controller: RUNNING${NC}"
            echo "🌐 Dashboard: http://traefik.docker.localhost"
            echo "🌐 Dashboard (direct): http://localhost:8080/dashboard/"
            echo "📊 API: http://localhost:8080/api/"
        else
            echo -e "${RED}❌ Traefik Controller: STOPPED${NC}"
            echo "🚀 Start with: $0 start"
        fi

        echo ""
        echo -e "${BLUE}🔗 Active Routes:${NC}"
        if docker ps | grep -q "traefik-controller"; then
            # Try to get routes from API
            if curl -s http://localhost:8080/api/http/routers &>/dev/null; then
                curl -s http://localhost:8080/api/http/routers | jq -r '.[] | select(.status == "enabled") | "   🌐 \(.rule) -> \(.service)"' 2>/dev/null || echo "   💡 Install jq to see detailed routes"
            else
                echo "   💡 Check dashboard at https://traefik.10baht for routes"
            fi
        else
            echo "   ❌ Traefik not running"
        fi
        ;;
    logs)
        echo -e "${BLUE}📋 Showing Traefik logs...${NC}"
        cd "$SCRIPT_DIR"
        docker compose logs -f traefik
        ;;
    routes)
        echo -e "${BLUE}🔗 Current Routes${NC}"
        echo "================================"
        if docker ps | grep -q "traefik-controller"; then
            if command -v curl &> /dev/null; then
                if curl -s http://localhost:8080/api/overview &>/dev/null; then
                    echo "Active routes:"
                    if command -v jq &> /dev/null; then
                        curl -s http://localhost:8080/api/http/routers 2>/dev/null | jq -r 'to_entries[] | "   🌐 \(.value.rule) -> \(.value.service)"' 2>/dev/null
                    else
                        echo "   💡 Install jq for formatted output, or check dashboard at http://localhost:8080/dashboard/"
                        overview=$(curl -s http://localhost:8080/api/overview 2>/dev/null)
                        if [ -n "$overview" ]; then
                            echo "   📊 Total routers: $(echo "$overview" | grep -o '"total":[0-9]*' | head -1 | cut -d':' -f2)"
                        fi
                    fi
                else
                    echo "❌ Cannot connect to Traefik API"
                fi
            else
                echo "💡 Install curl to see routes, or check dashboard at http://localhost:8080/dashboard/"
            fi
        else
            echo "❌ Traefik Controller is not running"
        fi
        ;;
    dashboard)
        echo -e "${BLUE}🌐 Opening Traefik dashboard...${NC}"
        if command -v xdg-open &> /dev/null; then
            xdg-open "http://localhost:8080/dashboard/"
        elif command -v open &> /dev/null; then
            open "http://localhost:8080/dashboard/"
        else
            echo "🌐 Dashboard available at:"
            echo "   - http://localhost:8080/dashboard/ (direct)"
            echo "   - http://traefik.docker.localhost (via domain)"
        fi
        ;;
    add-labels)
        shift  # Remove 'add-labels' from arguments
        "$SCRIPT_DIR/scripts/add-project-labels.sh" "$@"
        ;;
    mode)
        shift  # Remove 'mode' from arguments
        "$SCRIPT_DIR/scripts/switch-mode.sh" "$@"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
