#!/bin/bash

# Traefik Controller Management Script
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

show_help() {
    echo "🚀 Traefik Controller Management"
    echo "================================"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  setup                    - Initial setup (install mkcert, start Traefik)"
    echo "  start                    - Start Traefik Controller"
    echo "  stop                     - Stop Traefik Controller"
    echo "  status                   - Show status of Traefik and projects"
    echo "  discover                 - Discover and configure projects"
    echo "  start-project <name>     - Start a specific project"
    echo "  stop-project <name>      - Stop a specific project"
    echo "  start-all               - Start all projects"
    echo "  stop-all                - Stop all projects"
    echo "  logs [project]          - Show logs (Traefik or specific project)"
    echo "  dashboard               - Open Traefik dashboard in browser"
    echo "  list                    - List all configured projects"
    echo "  help                    - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 setup                # First-time setup"
    echo "  $0 start-project bahtbrowse"
    echo "  $0 logs certrats"
    echo "  $0 dashboard"
}

case "${1:-help}" in
    setup)
        echo -e "${BLUE}🔧 Setting up Traefik Controller...${NC}"
        "$SCRIPT_DIR/scripts/install-mkcert.sh"
        "$SCRIPT_DIR/scripts/start.sh"
        "$SCRIPT_DIR/scripts/discover-projects.sh"
        echo -e "${GREEN}✅ Setup complete!${NC}"
        echo -e "${YELLOW}💡 Use '$0 start-project <name>' to start individual projects${NC}"
        ;;
    start)
        "$SCRIPT_DIR/scripts/start.sh"
        ;;
    stop)
        "$SCRIPT_DIR/scripts/stop.sh"
        ;;
    status)
        "$SCRIPT_DIR/scripts/status.sh"
        ;;
    discover)
        "$SCRIPT_DIR/scripts/discover-projects.sh"
        ;;
    start-project)
        if [ -z "$2" ]; then
            echo -e "${RED}❌ Please provide a project name${NC}"
            echo "Usage: $0 start-project <project-name>"
            exit 1
        fi
        "$SCRIPT_DIR/scripts/start-project.sh" "$2"
        ;;
    stop-project)
        if [ -z "$2" ]; then
            echo -e "${RED}❌ Please provide a project name${NC}"
            echo "Usage: $0 stop-project <project-name>"
            exit 1
        fi
        "$SCRIPT_DIR/scripts/stop-project.sh" "$2"
        ;;
    start-all)
        "$SCRIPT_DIR/scripts/start-all-projects.sh"
        ;;
    stop-all)
        "$SCRIPT_DIR/scripts/stop-all-projects.sh"
        ;;
    logs)
        if [ -z "$2" ]; then
            echo -e "${BLUE}📋 Showing Traefik logs...${NC}"
            cd "$SCRIPT_DIR"
            docker-compose logs -f traefik
        else
            project_dir="$SCRIPT_DIR/projects/$2"
            if [ -d "$project_dir" ]; then
                echo -e "${BLUE}📋 Showing logs for $2...${NC}"
                cd "$project_dir"
                docker-compose -f docker-compose.traefik.yml logs -f
            else
                echo -e "${RED}❌ Project '$2' not found${NC}"
                exit 1
            fi
        fi
        ;;
    dashboard)
        echo -e "${BLUE}🌐 Opening Traefik dashboard...${NC}"
        if command -v xdg-open &> /dev/null; then
            xdg-open "https://traefik.10baht"
        elif command -v open &> /dev/null; then
            open "https://traefik.10baht"
        else
            echo "🌐 Dashboard available at: https://traefik.10baht"
        fi
        ;;
    list)
        echo -e "${BLUE}📦 Configured Projects:${NC}"
        if [ -d "$SCRIPT_DIR/projects" ]; then
            for project_dir in "$SCRIPT_DIR/projects"/*; do
                if [ -d "$project_dir" ]; then
                    project_name=$(basename "$project_dir")
                    domain="${project_name,,}.10baht"
                    if [ -f "$project_dir/project-info.yml" ]; then
                        domain=$(grep "domain:" "$project_dir/project-info.yml" | cut -d' ' -f2)
                    fi
                    echo "   📦 $project_name -> https://$domain"
                fi
            done
        else
            echo -e "${YELLOW}⚠️  No projects configured. Run '$0 discover' first.${NC}"
        fi
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
