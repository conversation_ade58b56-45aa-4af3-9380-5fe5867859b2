#!/bin/bash

# Simple JavaScript minification script using basic shell commands
# This is a fallback for when Node.js is not available

# Function to minify a JavaScript file
minify_js() {
    local input_file=$1
    local output_file="${input_file%.js}.min.js"

    echo "Minifying $input_file to $output_file..."

    # Get original file size
    local original_size=$(wc -c < "$input_file")

    # Basic minification using sed:
    # 1. Remove comments (both // and /* */)
    # 2. Remove whitespace at the beginning and end of lines
    # 3. Remove newlines within statements
    # 4. Collapse multiple spaces into one
    sed -E '
        # Remove single-line comments
        s/\/\/.*$//g
        # Remove multi-line comments (not perfect but works for most cases)
        s/\/\*.*?\*\///g
        # Remove leading and trailing whitespace
        s/^[ \t]+//g
        s/[ \t]+$//g
        # Collapse multiple spaces into one
        s/[ \t]+/ /g
    ' "$input_file" | tr -d '\n' | sed 's/} /}/g; s/{ /{/g; s/; /;/g; s/, /,/g' > "$output_file"

    # Get minified file size
    local minified_size=$(wc -c < "$output_file")

    # Calculate reduction percentage
    local reduction=$(( (original_size - minified_size) * 100 / original_size ))

    echo "✓ Minified $input_file ($original_size → $minified_size bytes, ${reduction}% reduction)"
}

# Find and minify all JavaScript files
find_and_minify() {
    local dir=$1

    echo "Processing directory: $dir"

    # Find all .js files that are not already minified
    for js_file in $(find "$dir" -name "*.js" -not -name "*.min.js"); do
        minify_js "$js_file"
    done
}

# Main script
echo "Starting simple JavaScript minification..."

# Process the main directories
find_and_minify "."
find_and_minify "popup"
find_and_minify "options"

echo "Simple minification complete!"
