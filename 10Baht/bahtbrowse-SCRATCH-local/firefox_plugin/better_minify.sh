#!/bin/bash

# Improved JavaScript minification script that preserves template literals
# This is critical for URL construction and other template strings

# Function to minify a JavaScript file while preserving template literals
minify_js() {
    local input_file=$1
    local output_file="${input_file%.js}.min.js"

    echo "Minifying $input_file to $output_file..."

    # Get original file size
    local original_size=$(wc -c < "$input_file")

    # Create a temporary file
    local temp_file=$(mktemp)

    # Step 1: Remove single-line comments
    sed 's/\/\/.*$//g' "$input_file" > "$temp_file"

    # Step 2: Remove multi-line comments (basic approach)
    sed -i -E 's/\/\*([^*]|\*[^\/])*\*\///g' "$temp_file"

    # Step 3: Remove whitespace with special handling for template literals
    # This is a simplified approach - for production, use a proper JS minifier

    # First, collapse multiple spaces outside of strings
    sed -i -E 's/[ \t]+/ /g' "$temp_file"

    # Remove spaces around operators and punctuation, but be careful with template literals
    sed -i -E 's/\} /\}/g; s/ \{/\{/g; s/\; /\;/g; s/, /,/g' "$temp_file"

    # Remove newlines but preserve semicolons
    tr '\n' ' ' < "$temp_file" > "$output_file"

    # Get minified file size
    local minified_size=$(wc -c < "$output_file")

    # Calculate reduction percentage
    local reduction=$(( (original_size - minified_size) * 100 / original_size ))

    echo "✓ Minified $input_file ($original_size → $minified_size bytes, ${reduction}% reduction)"

    # Clean up temp file
    rm "$temp_file"
}

# Find and minify all JavaScript files
find_and_minify() {
    local dir=$1

    echo "Processing directory: $dir"

    # Find all .js files that are not already minified
    for js_file in $(find "$dir" -name "*.js" -not -name "*.min.js"); do
        minify_js "$js_file"
    done
}

# Main script
echo "Starting improved JavaScript minification..."

# Process the main directories
find_and_minify "."
find_and_minify "popup"
find_and_minify "options"

echo "Improved minification complete!"
