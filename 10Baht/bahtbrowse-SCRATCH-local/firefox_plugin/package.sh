#!/bin/bash

# Create build directory if it doesn't exist
mkdir -p build

# Create a temporary directory for packaging
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Copy all necessary files to the temporary directory
cp -r background.js icons manifest.json options popup README.md INSTALLATION.md package.json $TEMP_DIR/

# Create the XPI file (which is just a ZIP file with .xpi extension)
cd $TEMP_DIR
mkdir -p ../build
tar -czf ../build/bahtbrowse_redirector.tar.gz *
cd -

echo "Extension packaged as build/bahtbrowse_redirector.tar.gz"
echo "You can install it in Firefox by going to about:debugging > This Firefox > Load Temporary Add-on"
