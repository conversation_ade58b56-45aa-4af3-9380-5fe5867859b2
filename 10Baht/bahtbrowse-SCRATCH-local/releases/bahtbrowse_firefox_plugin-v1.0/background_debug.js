// Debug version of background script for BahtBrowse Redirector

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8082',
  openInNewTab: true,
  enableWhitelist: false,
  whitelist: [],
  enableBlacklist: false,
  blacklist: [],
  showNotifications: true
};

// Log function for debugging
function logDebug(message, data) {
  console.log(`[BahtBrowse Debug] ${message}`, data || '');
}

// Initialize settings
browser.runtime.onInstalled.addListener(() => {
  logDebug('Extension installed or updated');
  browser.storage.sync.get('settings').then((result) => {
    if (!result.settings) {
      logDebug('No settings found, using defaults');
      browser.storage.sync.set({ settings: DEFAULT_SETTINGS });
    } else {
      logDebug('Existing settings found', result.settings);
    }

    try {
      // Show welcome notification
      showNotification('BahtBrowse Redirector Installed', 'Click the toolbar button to redirect pages to BahtBrowse');
      logDebug('Welcome notification shown');
    } catch (error) {
      logDebug('Error showing notification', error);
    }
  }).catch(error => {
    logDebug('Error getting settings', error);
  });
});

// Add context menu item
try {
  browser.contextMenus.create({
    id: 'open-in-bahtbrowse',
    title: 'Open in BahtBrowse',
    contexts: ['link', 'page']
  });
  logDebug('Context menu created');
} catch (error) {
  logDebug('Error creating context menu', error);
}

// Handle context menu clicks
browser.contextMenus.onClicked.addListener((info, tab) => {
  logDebug('Context menu clicked', info);
  if (info.menuItemId === 'open-in-bahtbrowse') {
    // If it's a link context, use the link URL, otherwise use the page URL
    const url = info.linkUrl || tab.url;
    logDebug('URL to redirect', url);
    redirectToBahtBrowse(tab, url);
  }
});

// Handle toolbar button click
browser.browserAction.onClicked.addListener((tab) => {
  logDebug('Toolbar button clicked');
  logDebug('Tab info', tab);
  logDebug('URL to redirect', tab.url);
  redirectToBahtBrowse(tab, tab.url);
});

// Function to redirect URL to BahtBrowse
function redirectToBahtBrowse(tab, url) {
  logDebug('redirectToBahtBrowse called', { tabId: tab.id, url: url });

  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    logDebug('Settings retrieved', settings);

    // Check if URL should be redirected based on whitelist/blacklist
    if (shouldRedirect(url, settings)) {
      logDebug('URL should be redirected');

      const bahtbrowseUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/?url=${encodeURIComponent(url)}`;
      logDebug('Redirect URL', bahtbrowseUrl);

      try {
        if (settings.openInNewTab) {
          logDebug('Opening in new tab');
          browser.tabs.create({ url: bahtbrowseUrl }).then(
            newTab => logDebug('New tab created', newTab),
            error => logDebug('Error creating new tab', error)
          );
        } else {
          logDebug('Updating current tab');
          browser.tabs.update(tab.id, { url: bahtbrowseUrl }).then(
            updatedTab => logDebug('Tab updated', updatedTab),
            error => logDebug('Error updating tab', error)
          );
        }

        // Show notification if enabled
        if (settings.showNotifications) {
          try {
            showNotification('Redirected to BahtBrowse', `Opening ${url} in BahtBrowse`);
            logDebug('Notification shown');
          } catch (error) {
            logDebug('Error showing notification', error);
          }
        }
      } catch (error) {
        logDebug('Error during redirection', error);
      }
    } else {
      logDebug('URL should not be redirected due to whitelist/blacklist settings');
    }
  }).catch(error => {
    logDebug('Error getting settings for redirection', error);
  });
}

// Function to show browser notifications
function showNotification(title, message) {
  try {
    browser.notifications.create({
      type: 'basic',
      iconUrl: browser.runtime.getURL('icons/bahtbrowse-48.png'),
      title: title,
      message: message
    });
  } catch (error) {
    logDebug('Error in showNotification', error);
    // Fallback to alert if notifications fail
    alert(`${title}: ${message}`);
  }
}

// Function to check if URL should be redirected based on whitelist/blacklist
function shouldRedirect(url, settings) {
  logDebug('Checking if URL should be redirected', { url, whitelist: settings.enableWhitelist, blacklist: settings.enableBlacklist });

  // If whitelist is enabled, only redirect if URL is in whitelist
  if (settings.enableWhitelist && settings.whitelist.length > 0) {
    const shouldRedirect = settings.whitelist.some(pattern => url.includes(pattern));
    logDebug('Whitelist check result', shouldRedirect);
    return shouldRedirect;
  }

  // If blacklist is enabled, don't redirect if URL is in blacklist
  if (settings.enableBlacklist && settings.blacklist.length > 0) {
    const shouldRedirect = !settings.blacklist.some(pattern => url.includes(pattern));
    logDebug('Blacklist check result', shouldRedirect);
    return shouldRedirect;
  }

  // If neither whitelist nor blacklist is enabled, always redirect
  logDebug('No whitelist/blacklist enabled, should redirect');
  return true;
}
