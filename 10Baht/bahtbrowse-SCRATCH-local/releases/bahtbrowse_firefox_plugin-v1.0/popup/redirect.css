body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  width: 320px;
  background-color: #f9f9fa;
}

.container {
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.header {
  margin-bottom: 16px;
  border-bottom: 1px solid #ddd;
}

.header h1 {
  font-size: 18px;
  margin: 0 0 8px 0;
  color: #0c0c0d;
}

.content {
  margin-bottom: 16px;
}

.content p {
  margin: 0 0 8px 0;
  color: #0c0c0d;
}

.url-display {
  background-color: #f0f0f4;
  border: 1px solid #d7d7db;
  border-radius: 2px;
  padding: 8px;
  margin-bottom: 16px;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  max-height: 60px;
  overflow-y: auto;
}

.buttons {
  display: flex;
  justify-content: space-between;
}

button {
  background-color: #0060df;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 13px;
}

button:hover {
  background-color: #003eaa;
}

#optionsButton {
  background-color: #f9f9fa;
  color: #0060df;
  border: 1px solid #0060df;
}

#optionsButton:hover {
  background-color: #e7e7e7;
}

.footer {
  margin-top: 8px;
  border-top: 1px solid #ddd;
  padding-top: 8px;
  font-size: 12px;
  color: #737373;
}
