// Popup script for BahtBrowse Redirector

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8082',
  openInNewTab: true,
  enableWhitelist: false,
  whitelist: [],
  enableBlacklist: false,
  blacklist: []
};

// Get current tab URL and display it
document.addEventListener('DOMContentLoaded', () => {
  browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => {
    const currentUrl = tabs[0].url;
    document.getElementById('currentUrl').textContent = currentUrl;
  });

  // Set up button event listeners
  document.getElementById('redirectButton').addEventListener('click', redirectCurrentPage);
  document.getElementById('optionsButton').addEventListener('click', openOptions);
});

// Function to redirect current page to BahtBrowse
function redirectCurrentPage() {
  browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => {
    const currentTab = tabs[0];
    const currentUrl = currentTab.url;

    browser.storage.sync.get('settings').then((result) => {
      const settings = result.settings || DEFAULT_SETTINGS;
      const bahtbrowseUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/?url=${encodeURIComponent(currentUrl)}`;

      if (settings.openInNewTab) {
        browser.tabs.create({ url: bahtbrowseUrl });
      } else {
        browser.tabs.update(currentTab.id, { url: bahtbrowseUrl });
      }

      window.close();
    });
  });
}

// Function to open options page
function openOptions() {
  browser.runtime.openOptionsPage();
}
