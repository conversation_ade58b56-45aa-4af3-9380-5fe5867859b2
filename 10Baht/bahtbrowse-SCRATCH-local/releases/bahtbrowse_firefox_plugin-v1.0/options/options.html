<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="container">
    <h1>BahtBrowse Redirector Settings</h1>

    <div class="section">
      <h2>BahtBrowse Server</h2>
      <div class="form-group">
        <label for="bahtbrowseHost">Host:</label>
        <input type="text" id="bahtbrowseHost" placeholder="localhost">
      </div>
      <div class="form-group">
        <label for="bahtbrowsePort">Port:</label>
        <input type="text" id="bahtbrowsePort" placeholder="8081">
      </div>
    </div>

    <div class="section">
      <h2>Behavior</h2>
      <div class="form-group checkbox">
        <input type="checkbox" id="openInNewTab">
        <label for="openInNewTab">Open in new tab</label>
      </div>
      <div class="form-group checkbox">
        <input type="checkbox" id="showNotifications">
        <label for="showNotifications">Show notifications</label>
      </div>
    </div>

    <div class="section">
      <h2>URL Filtering</h2>
      <div class="form-group checkbox">
        <input type="checkbox" id="enableWhitelist">
        <label for="enableWhitelist">Enable whitelist (only redirect these domains)</label>
      </div>
      <div class="form-group">
        <label for="whitelist">Whitelist (one domain per line):</label>
        <textarea id="whitelist" placeholder="example.com"></textarea>
      </div>

      <div class="form-group checkbox">
        <input type="checkbox" id="enableBlacklist">
        <label for="enableBlacklist">Enable blacklist (never redirect these domains)</label>
      </div>
      <div class="form-group">
        <label for="blacklist">Blacklist (one domain per line):</label>
        <textarea id="blacklist" placeholder="trusted-site.com"></textarea>
      </div>
    </div>

    <div class="buttons">
      <button id="saveButton">Save Settings</button>
      <button id="resetButton">Reset to Defaults</button>
    </div>

    <div id="status" class="status"></div>
  </div>
  <script src="options.js"></script>
</body>
</html>
