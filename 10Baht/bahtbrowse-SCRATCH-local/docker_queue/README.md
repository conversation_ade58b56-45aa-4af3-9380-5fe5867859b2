# BahtBrowse Docker Queue Management

A Celery-based system for managing a pool of Docker containers for BahtBrowse.

## Overview

This system maintains a pool of pre-initialized Docker containers and efficiently assigns them to users upon request. It ensures that each user has immediate access to a dedicated Docker container when needed, while efficiently managing system resources.

## Features

- **Container Pool Management**: Maintains a pool of pre-initialized Docker containers
- **User Session Management**: Assigns containers to users upon request
- **Queue Management**: Implements a fair queuing system for container requests
- **Resource Optimization**: Recycles idle containers after a configurable timeout
- **Monitoring and Metrics**: Tracks container usage patterns and system performance

## Architecture

The system uses a Celery-based architecture with <PERSON><PERSON> as the message broker and result backend:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Web Server     │────▶│  Celery Worker  │────▶│  Docker API     │
│  (Flask)        │     │  (Task Queue)   │     │                 │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                      │                       │
         │                      │                       │
         ▼                      ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Redis          │     │  Container      │     │  Docker         │
│  (Message Broker)│     │  Pool Manager   │     │  Containers     │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Components

- **Web API**: Flask application for handling user requests
- **Celery Workers**: Process container management tasks asynchronously
- **Redis**: Message broker and result backend
- **Docker API**: Interfaces with Docker to create, manage, and destroy containers

## Installation

### Prerequisites

- Docker and Docker Compose
- Python 3.11 or higher

### Setup

1. Clone the repository:
   ```
   git clone https://github.com/tenbahtsecurity/bahtbrowse.git
   cd bahtbrowse
   git checkout testing/celery-implementation
   ```

2. Build and start the services:
   ```
   docker-compose up -d
   ```

3. Check the status of the services:
   ```
   docker-compose ps
   ```

## API Endpoints

### Container Management

- `POST /api/containers/request`: Request a container for a user
- `GET /api/containers/status/<request_id>`: Check the status of a container request
- `POST /api/containers/release`: Release a container assigned to a user

### Pool Management

- `GET /api/pool/status`: Get the status of the container pool
- `POST /api/pool/scale`: Scale the container pool

### Monitoring

- `GET /api/metrics/current`: Get current system metrics
- `GET /api/metrics/history`: Get historical metrics
- `GET /api/health`: Check the health of the system
- `GET /api/report`: Generate a usage report

## Configuration

Configuration settings can be modified in `docker_queue/config.py` or by setting environment variables:

- `REDIS_HOST`: Redis host (default: localhost)
- `REDIS_PORT`: Redis port (default: 6379)
- `DOCKER_BASE_URL`: Docker API URL (default: unix:///var/run/docker.sock)
- `CONTAINER_IMAGE`: Docker image for containers (default: bahtbrowse:latest)
- `POOL_TARGET_SIZE`: Target pool size (default: 10)
- `POOL_MIN_AVAILABLE`: Minimum available containers (default: 3)
- `POOL_MAX_SIZE`: Maximum pool size (default: 50)
- `CONTAINER_IDLE_TIMEOUT`: Idle timeout in seconds (default: 1800)

## Monitoring

The system includes Celery Flower for monitoring Celery tasks:

- Access Flower at http://localhost:5555

## License

This project is licensed under the MIT License - see the LICENSE file for details.
