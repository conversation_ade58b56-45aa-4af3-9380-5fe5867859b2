metricbeat.config:
  modules:
    path: ${path.config}/modules.d/*.yml
    reload.enabled: false

metricbeat.autodiscover:
  providers:
    - type: docker
      hints.enabled: true
      templates:
        - condition:
            contains:
              container.labels.app: "bahtbrowse"
          config:
            - module: docker
              metricsets:
                - "container"
                - "cpu"
                - "memory"
                - "network"
                - "diskio"
              hosts: ["unix:///var/run/docker.sock"]
              period: 10s

# Module configurations
metricbeat.modules:
  - module: system
    metricsets:
      - cpu
      - load
      - memory
      - network
      - process
      - process_summary
      - socket_summary
      - filesystem
      - fsstat
    process.include_top_n:
      by_cpu: 5
      by_memory: 5
    period: 10s
    cpu.metrics:
      - percentages
      - normalized_percentages
    core.metrics: ["percentages"]

  - module: docker
    metricsets:
      - "container"
      - "cpu"
      - "memory"
      - "network"
      - "diskio"
    hosts: ["unix:///var/run/docker.sock"]
    period: 10s

# Processors for enriching data
processors:
  - add_docker_metadata:
      host: "unix:///var/run/docker.sock"
  - add_host_metadata: ~
  - add_cloud_metadata: ~
  - add_kubernetes_metadata: ~
  - add_fields:
      target: ''
      fields:
        service.name: 'bahtbrowse'
        environment: 'production'

# Output configuration
output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  indices:
    - index: "metricbeat-%{[agent.version]}-%{+yyyy.MM.dd}"

# Logging
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/metricbeat
  name: metricbeat
  keepfiles: 7
  permissions: 0644

# Monitoring settings
monitoring.enabled: true
monitoring.elasticsearch:
  hosts: ["elasticsearch:9200"]
