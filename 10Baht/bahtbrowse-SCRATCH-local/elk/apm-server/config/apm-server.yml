apm-server:
  host: "0.0.0.0:8200"
  rum:
    enabled: true
    allow_origins: ['*']
    allow_headers: ['*']
    source_mapping:
      enabled: true
      cache:
        expiration: 5m
  auth:
    secret_token: ""
  kibana:
    enabled: true
    host: "kibana:5601"

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  indices:
    - index: "apm-%{[observer.version]}-transaction-%{+yyyy.MM.dd}"
      when.contains:
        processor.event: "transaction"
    - index: "apm-%{[observer.version]}-span-%{+yyyy.MM.dd}"
      when.contains:
        processor.event: "span"
    - index: "apm-%{[observer.version]}-error-%{+yyyy.MM.dd}"
      when.contains:
        processor.event: "error"
    - index: "apm-%{[observer.version]}-metric-%{+yyyy.MM.dd}"
      when.contains:
        processor.event: "metric"

logging:
  level: info
  to_files: true
  files:
    path: /var/log/apm-server
    name: apm-server
    keepfiles: 7
    permissions: 0644

monitoring:
  enabled: true
  elasticsearch:
    hosts: ["elasticsearch:9200"]
