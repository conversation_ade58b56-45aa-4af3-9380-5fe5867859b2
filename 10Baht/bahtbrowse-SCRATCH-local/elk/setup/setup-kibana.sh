#!/bin/bash
# Script to set up Kibana dashboards for 10Baht bahtBrowse

# Wait for Elasticsearch to be ready
echo "Waiting for Elasticsearch..."
until curl -s http://elasticsearch:9200 > /dev/null; do
    sleep 5
done
echo "Elasticsearch is ready!"

# Wait for Kibana to be ready
echo "Waiting for Kibana..."
until curl -s http://kibana:5601/api/status > /dev/null; do
    sleep 5
done
echo "Kibana is ready!"

# Create index patterns
echo "Creating index patterns..."
curl -X POST "http://kibana:5601/api/saved_objects/index-pattern/bahtbrowse-logs-*" \
     -H 'kbn-xsrf: true' \
     -H 'Content-Type: application/json' \
     -d '{"attributes":{"title":"bahtbrowse-logs-*","timeFieldName":"@timestamp"}}'

curl -X POST "http://kibana:5601/api/saved_objects/index-pattern/bahtbrowse-metrics-*" \
     -H 'kbn-xsrf: true' \
     -H 'Content-Type: application/json' \
     -d '{"attributes":{"title":"bahtbrowse-metrics-*","timeFieldName":"@timestamp"}}'

curl -X POST "http://kibana:5601/api/saved_objects/index-pattern/bahtbrowse-apm-*" \
     -H 'kbn-xsrf: true' \
     -H 'Content-Type: application/json' \
     -d '{"attributes":{"title":"bahtbrowse-apm-*","timeFieldName":"@timestamp"}}'

# Set default index pattern
echo "Setting default index pattern..."
curl -X POST "http://kibana:5601/api/saved_objects/config/8.6.0" \
     -H 'kbn-xsrf: true' \
     -H 'Content-Type: application/json' \
     -d '{"attributes":{"defaultIndex":"bahtbrowse-logs-*"}}'

# Import dashboards
echo "Importing dashboards..."
# This would normally import dashboards from JSON files
# For now, we'll just create a simple dashboard

# Create visualization for browser types
curl -X POST "http://kibana:5601/api/saved_objects/visualization/browser-type-distribution" \
     -H 'kbn-xsrf: true' \
     -H 'Content-Type: application/json' \
     -d '{"attributes":{"title":"Browser Type Distribution","visState":"{\"title\":\"Browser Type Distribution\",\"type\":\"pie\",\"params\":{\"type\":\"pie\",\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"isDonut\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"browser_type.keyword\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}}]}","uiStateJSON":"{}","description":"","version":1,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"index\":\"bahtbrowse-logs-*\",\"filter\":[],\"query\":{\"query\":\"\",\"language\":\"kuery\"}}"}}}' 

# Create visualization for session duration
curl -X POST "http://kibana:5601/api/saved_objects/visualization/session-duration" \
     -H 'kbn-xsrf: true' \
     -H 'Content-Type: application/json' \
     -d '{"attributes":{"title":"Session Duration","visState":"{\"title\":\"Session Duration\",\"type\":\"line\",\"params\":{\"type\":\"line\",\"grid\":{\"categoryLines\":false},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"filter\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Average Session Duration (s)\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"line\",\"mode\":\"normal\",\"data\":{\"label\":\"Average Session Duration (s)\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"lineWidth\":2,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false,\"labels\":{},\"thresholdLine\":{\"show\":false,\"value\":10,\"width\":1,\"style\":\"full\",\"color\":\"#E7664C\"}},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"avg\",\"schema\":\"metric\",\"params\":{\"field\":\"duration\",\"customLabel\":\"Average Session Duration (s)\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"timeRange\":{\"from\":\"now-24h\",\"to\":\"now\"},\"useNormalizedEsInterval\":true,\"scaleMetricValues\":false,\"interval\":\"auto\",\"drop_partials\":false,\"min_doc_count\":1,\"extended_bounds\":{}}}]}","uiStateJSON":"{}","description":"","version":1,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"index\":\"bahtbrowse-logs-*\",\"filter\":[],\"query\":{\"query\":\"\",\"language\":\"kuery\"}}"}}}' 

# Create visualization for container startup time
curl -X POST "http://kibana:5601/api/saved_objects/visualization/container-startup-time" \
     -H 'kbn-xsrf: true' \
     -H 'Content-Type: application/json' \
     -d '{"attributes":{"title":"Container Startup Time","visState":"{\"title\":\"Container Startup Time\",\"type\":\"line\",\"params\":{\"type\":\"line\",\"grid\":{\"categoryLines\":false},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"filter\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Average Startup Time (s)\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"line\",\"mode\":\"normal\",\"data\":{\"label\":\"Average Startup Time (s)\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"lineWidth\":2,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false,\"labels\":{},\"thresholdLine\":{\"show\":false,\"value\":10,\"width\":1,\"style\":\"full\",\"color\":\"#E7664C\"}},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"avg\",\"schema\":\"metric\",\"params\":{\"field\":\"startup_time\",\"customLabel\":\"Average Startup Time (s)\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"timeRange\":{\"from\":\"now-24h\",\"to\":\"now\"},\"useNormalizedEsInterval\":true,\"scaleMetricValues\":false,\"interval\":\"auto\",\"drop_partials\":false,\"min_doc_count\":1,\"extended_bounds\":{}}}]}","uiStateJSON":"{}","description":"","version":1,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"index\":\"bahtbrowse-logs-*\",\"filter\":[],\"query\":{\"query\":\"\",\"language\":\"kuery\"}}"}}}' 

# Create dashboard
curl -X POST "http://kibana:5601/api/saved_objects/dashboard/bahtbrowse-overview" \
     -H 'kbn-xsrf: true' \
     -H 'Content-Type: application/json' \
     -d '{"attributes":{"title":"10Baht bahtBrowse Overview","hits":0,"description":"Overview dashboard for 10Baht bahtBrowse","panelsJSON":"[{\"panelIndex\":\"1\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},\"embeddableConfig\":{},\"version\":\"8.6.0\",\"panelRefName\":\"panel_0\"},{\"panelIndex\":\"2\",\"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},\"embeddableConfig\":{},\"version\":\"8.6.0\",\"panelRefName\":\"panel_1\"},{\"panelIndex\":\"3\",\"gridData\":{\"x\":0,\"y\":15,\"w\":48,\"h\":15,\"i\":\"3\"},\"embeddableConfig\":{},\"version\":\"8.6.0\",\"panelRefName\":\"panel_2\"}]","optionsJSON":"{\"useMargins\":true,\"hidePanelTitles\":false}","version":1,"timeRestore":false,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[]}"},"references":[{"name":"panel_0","type":"visualization","id":"browser-type-distribution"},{"name":"panel_1","type":"visualization","id":"session-duration"},{"name":"panel_2","type":"visualization","id":"container-startup-time"}]}}'

echo "Kibana setup complete!"
