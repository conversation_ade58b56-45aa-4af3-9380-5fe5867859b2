filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /elk_demo.log
  json.keys_under_root: true
  json.add_error_key: true
  json.message_key: message
  fields:
    service: demo
    log_source: application
  fields_under_root: true

setup.template.name: "demo-logs"
setup.template.pattern: "demo-logs-*"
setup.ilm.enabled: false

output.elasticsearch:
  hosts: ["localhost:9200"]
  index: "demo-logs-%{+yyyy.MM.dd}"
