#!/usr/bin/env python3
"""Session validator for BahtBrowse.

This module provides a web server that validates sessions with IP locking
for VNC connections.
"""

import logging
import os
from urllib.parse import parse_qs

import aiohttp
from aiohttp import web

# Import our session manager
from session_manager import session_manager


async def validate_session(request: web.Request) -> web.Response:
    """Validate a session ID and client IP.

    Args:
        request: The HTTP request object containing the session ID

    Returns:
        A JSON response indicating whether the session is valid
    """
    # Get session ID from query parameters
    session_id = request.query.get("session")
    if not session_id:
        return web.json_response(
            {"valid": False, "error": "No session ID provided"}, status=400
        )

    # Get client IP
    client_ip = request.remote

    # Validate session
    is_valid = session_manager.validate_session(session_id, client_ip)

    if is_valid:
        return web.json_response({"valid": True})
    else:
        return web.json_response(
            {"valid": False, "error": "Invalid or expired session"}, status=403
        )


async def websockify_proxy(request: web.Request) -> web.Response:
    """Proxy WebSocket connections after validating the session.

    This handler checks if the client's IP matches the session before
    allowing the WebSocket connection to the VNC server.

    Args:
        request: The HTTP request object

    Returns:
        A WebSocket connection or an error response
    """
    # Extract session ID from query parameters
    query_string = request.query_string
    query_params = parse_qs(query_string)
    
    # Look for _cb parameter which contains the session ID
    cb_param = query_params.get("_cb", [None])[0]
    if not cb_param or not cb_param.startswith("session_"):
        return web.Response(
            status=403, text="Invalid session identifier"
        )
    
    session_id = cb_param
    client_ip = request.remote
    
    # Validate session
    is_valid = session_manager.validate_session(session_id, client_ip)
    
    if not is_valid:
        return web.Response(
            status=403, 
            text="Access denied: Your IP address does not match the session's original IP"
        )
    
    # If valid, proxy to the actual websockify service
    # This would typically be implemented with aiohttp.ClientWebSocketResponse
    # For now, we'll just return a success message
    return web.Response(text="Session validated, WebSocket connection would be established")


def main() -> None:
    """Initialize and start the session validator web server."""
    # Configure logging
    logging.basicConfig(
        filename="/tmp/session_validator.log",
        level=logging.DEBUG,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # Create the web application
    app = web.Application()
    
    # Add routes
    app.router.add_get("/validate", validate_session)
    app.router.add_get("/websockify", websockify_proxy)
    
    # Start the server
    web.run_app(app, host="0.0.0.0", port=8083)


if __name__ == "__main__":
    main()
