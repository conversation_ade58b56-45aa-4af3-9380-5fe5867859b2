<html>
	<head>
		<meta http-equiv="refresh" content="0;url=http://localhost:6080/vnc.html?host=localhost&port=6080&password=123456&autoconnect=1&resize=remote">
		<style>
body {
  background: #2c2c2c;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  margin: 0;
  font-family: 'Roboto', sans-serif;
}

.container {
  background-color: #333333;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  padding: 40px;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.logo {
  font-size: 48px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 20px;
  letter-spacing: 1px;
}

.connect-button {
  background: linear-gradient(135deg, #8e2de2, #4a00e0);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 50px;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  width: 200px;
}

.connect-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74, 0, 224, 0.4);
}

.connect-button i {
  margin-right: 10px;
}

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');
		</style>
		<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
	</head>
<body>
<div class="container">
  <div class="logo">BahtBrowse</div>
  <button class="connect-button" id="connect-btn">
    <i class="fas fa-link"></i> Connect
  </button>
</div>
<script type="text/javascript">
    document.getElementById("connect-btn").addEventListener("click", function() {
        // Redirect to the VNC interface
        window.location.href = "http://localhost:6080/vnc.html?host=localhost&port=6080&password=123456&autoconnect=1&resize=remote";
    });
</script>

</body>
</html>
