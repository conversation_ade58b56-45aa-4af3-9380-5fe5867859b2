#!/bin/bash

# Create logs and test results directories
mkdir -p /tmp/logs
mkdir -p /tmp/test_results

# Log startup information
echo "Starting BahtBrowse container at $(date)" > /tmp/logs/startup.log
echo "Container hostname: $(hostname)" >> /tmp/logs/startup.log

# Set up VNC password in user's home directory
mkdir -p $HOME/.vnc
/bin/bash -c "echo -e '123456\n123456\nn' | vncpasswd $HOME/.vnc/passwd"; echo;
echo "VNC password set up at $(date)" >> /tmp/logs/startup.log

# Redirect XKB warnings to /dev/null to suppress them
# Start VNC server
Xtigervnc :1 -depth 24 -geometry 1920x1080 -br -PasswordFile=$HOME/.vnc/passwd 2>/dev/null &
echo "VNC server started at $(date)" >> /tmp/logs/startup.log

# Start noVNC proxy
/tmp/noVNC/utils/novnc_proxy --vnc localhost:5901 &
echo "noVNC proxy started at $(date)" >> /tmp/logs/startup.log

# Start nginx for HTTP access
# Since we're running as root for debugging, we can start nginx
nginx
echo "Nginx started at $(date)" >> /tmp/logs/startup.log

# Wait for services to initialize
sleep 2

# Start the BahtBrowse server
python3 /tmp/app.py > /tmp/logs/app.log 2>&1 &
echo "BahtBrowse API server started at $(date)" >> /tmp/logs/startup.log

# Start the session validator
python3 /tmp/session_validator.py > /tmp/logs/session_validator.log 2>&1 &
echo "Session validator started at $(date)" >> /tmp/logs/startup.log

# Run startup tests in background
(
    # Wait for services to be fully available
    sleep 5

    echo "Running startup tests at $(date)" >> /tmp/logs/startup.log
    python3 /tmp/startup_tests.py > /tmp/logs/startup_tests_output.log 2>&1

    # Check test results
    if [ $? -eq 0 ]; then
        echo "✅ All startup tests passed at $(date)" >> /tmp/logs/startup.log
    else
        echo "❌ Some startup tests failed at $(date)" >> /tmp/logs/startup.log
    fi
) &

echo "Serving on http://$(hostname):6080/vnc.html"
echo "API available at http://$(hostname):8082/"
echo "Landing page at http://$(hostname):80/"

# Start window manager
DISPLAY=:1 matchbox-window-manager
