upstream vnc_proxy {
    server 127.0.0.1:6080;
}

server {
    listen 80;
    server_name example.com;

    # Use a simpler root directive that works in our test case
    root /tmp/serve;
    index index.html;

    # Keep the custom error handler
    error_page 400 403 404 500 502 503 504 = @error_redirect;

    location @error_redirect {
        return 302 /;
    }

    # Make locations relative to the root
    location /vnc1/websockify {
          proxy_http_version 1.1;
          proxy_pass http://vnc_proxy/;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          proxy_read_timeout 61s;
          proxy_buffering off;
    }

    # Don't need location / block since root and index handle it

    location /browse/ {
        proxy_pass http://127.0.0.1:8082/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Error handling and CORS headers remain the same
        error_page 400 403 404 500 502 503 504 = @error_redirect;
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Test-Connection,X-Client-Info,X-Request-Source';

        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Test-Connection,X-Client-Info,X-Request-Source';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        access_log /tmp/browse_access.log;
        error_log /tmp/browse_error.log;
    }

    # Add direct access to the API server for downloads
    location /downloads {
        proxy_pass http://127.0.0.1:8082/downloads;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /downloads-api/ {
        proxy_pass http://127.0.0.1:8082/downloads-api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /browse/test-connection {
        proxy_pass http://127.0.0.1:8082/test-connection;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Test-Connection,X-Client-Info,X-Request-Source';

        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Test-Connection,X-Client-Info,X-Request-Source';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    location ^~ /vnc1/ {
        alias /tmp/noVNC/;
        autoindex on;
        try_files $uri $uri/ =404;
    }

    location /vnc1/run {
        # Extract the session parameter from query string
        if ($args ~ "session=([^&]+)") {
            set $session_id $1;
        }
        
        # Pass the session parameter to the VNC client properly
        proxy_pass http://127.0.0.1:6080/vnc.html?host=localhost&port=6080&autoconnect=1&resize=remote&password=123456&session=$session_id;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_redirect http://127.0.0.1:6080/ /vnc1/;
        proxy_buffering off;
    }

    # Session validation endpoint
    location /validate {
        proxy_pass http://127.0.0.1:8083/validate;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
