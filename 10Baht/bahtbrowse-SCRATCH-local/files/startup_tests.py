#!/usr/bin/env python3
"""
BahtBrowse Container Startup Tests
Runs tests to verify container functionality at startup
"""

import json
import os
import subprocess
import sys
import time
import urllib.request
import urllib.error
import socket
import logging
from datetime import datetime

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.live import Live
    from rich import print as rprint
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Rich library not available. Using standard output.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/tmp/logs/startup_tests.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("startup-tests")

# Initialize Rich console if available
if RICH_AVAILABLE:
    console = Console()
else:
    console = None

class BahtBrowseStartupTests:
    """Tests that run at container startup to verify functionality"""

    def __init__(self):
        """Initialize test environment"""
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0
            }
        }
        self.hostname = socket.gethostname()

    def run_test(self, test_name, test_func, *args, **kwargs):
        """Run a test and record the result"""
        logger.info(f"Running test: {test_name}")

        # Display test running status with Rich if available
        if RICH_AVAILABLE:
            status_text = f"[cyan]Running test:[/cyan] [bold]{test_name}[/bold]"
            console.print(status_text)

        start_time = time.time()
        try:
            result = test_func(*args, **kwargs)
            success = True
            message = "Test passed"
            if isinstance(result, tuple) and len(result) == 2:
                success, message = result
        except Exception as e:
            success = False
            message = f"Error: {str(e)}"
            logger.exception(f"Test {test_name} failed with exception")

        duration = time.time() - start_time

        test_result = {
            "name": test_name,
            "success": success,
            "message": message,
            "duration": round(duration, 3)
        }

        self.test_results["tests"].append(test_result)
        self.test_results["summary"]["total"] += 1

        # Display test result with Rich if available
        if RICH_AVAILABLE:
            if success:
                result_text = f"[green]✅ PASS:[/green] [bold]{test_name}[/bold] - {message} ([cyan]{round(duration, 3)}s[/cyan])"
                console.print(result_text)
            else:
                result_text = f"[red]❌ FAIL:[/red] [bold]{test_name}[/bold] - {message} ([cyan]{round(duration, 3)}s[/cyan])"
                console.print(result_text)

        if success:
            self.test_results["summary"]["passed"] += 1
            logger.info(f"✅ PASS: {test_name} - {message}")
        else:
            self.test_results["summary"]["failed"] += 1
            logger.error(f"❌ FAIL: {test_name} - {message}")

        return success

    def test_vnc_server_running(self):
        """Test if VNC server is running"""
        try:
            output = subprocess.check_output(["ps", "aux"], universal_newlines=True)
            if "Xtigervnc" in output:
                return True, "VNC server is running"
            return False, "VNC server is not running"
        except subprocess.CalledProcessError:
            return False, "Failed to check VNC server status"

    def test_novnc_proxy_running(self):
        """Test if noVNC proxy is running"""
        try:
            output = subprocess.check_output(["ps", "aux"], universal_newlines=True)
            if "websockify" in output:
                return True, "noVNC proxy is running"
            return False, "noVNC proxy is not running"
        except subprocess.CalledProcessError:
            return False, "Failed to check noVNC proxy status"

    def test_window_manager_running(self):
        """Test if window manager is running"""
        try:
            output = subprocess.check_output(["ps", "aux"], universal_newlines=True)
            if "matchbox-window-manager" in output:
                return True, "Window manager is running"
            return False, "Window manager is not running"
        except subprocess.CalledProcessError:
            return False, "Failed to check window manager status"

    def test_api_server_running(self):
        """Test if API server is running"""
        try:
            output = subprocess.check_output(["ps", "aux"], universal_newlines=True)
            if "python3 /tmp/app.py" in output:
                return True, "API server is running"
            return False, "API server is not running"
        except subprocess.CalledProcessError:
            return False, "Failed to check API server status"

    def test_session_validator_running(self):
        """Test if session validator is running"""
        try:
            output = subprocess.check_output(["ps", "aux"], universal_newlines=True)
            if "python3 /tmp/session_validator.py" in output:
                return True, "Session validator is running"
            return False, "Session validator is not running"
        except subprocess.CalledProcessError:
            return False, "Failed to check session validator status"

    def test_vnc_port_open(self):
        """Test if VNC port is open"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 5901))
        sock.close()
        if result == 0:
            return True, "VNC port 5901 is open"
        return False, f"VNC port 5901 is not open (error code: {result})"

    def test_novnc_port_open(self):
        """Test if noVNC WebSocket port is open"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 6080))
        sock.close()
        if result == 0:
            return True, "noVNC port 6080 is open"
        return False, f"noVNC port 6080 is not open (error code: {result})"

    def test_http_port_open(self):
        """Test if HTTP port is open"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 80))
        sock.close()
        if result == 0:
            return True, "HTTP port 80 is open"
        return False, f"HTTP port 80 is not open (error code: {result})"

    def test_api_port_open(self):
        """Test if API port is open"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8082))
        sock.close()
        if result == 0:
            return True, "API port 8082 is open"
        return False, f"API port 8082 is not open (error code: {result})"

    def test_session_validator_port_open(self):
        """Test if session validator port is open"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8083))
        sock.close()
        if result == 0:
            return True, "Session validator port 8083 is open"
        return False, f"Session validator port 8083 is not open (error code: {result})"

    def test_landing_page_accessible(self):
        """Test if landing page is accessible"""
        try:
            with urllib.request.urlopen("http://localhost:80/") as response:
                content = response.read().decode('utf-8')
                if "BahtBrowse" in content:
                    return True, "Landing page is accessible and contains 'BahtBrowse'"
                return False, "Landing page is accessible but doesn't contain 'BahtBrowse'"
        except urllib.error.URLError as e:
            return False, f"Failed to access landing page: {str(e)}"

    def test_novnc_accessible(self):
        """Test if noVNC interface is accessible"""
        try:
            with urllib.request.urlopen("http://localhost:6080/vnc.html") as response:
                content = response.read().decode('utf-8')
                if "noVNC" in content:
                    return True, "noVNC interface is accessible"
                return False, "noVNC interface is accessible but doesn't contain 'noVNC'"
        except urllib.error.URLError as e:
            return False, f"Failed to access noVNC interface: {str(e)}"

    def test_api_accessible(self):
        """Test if API is accessible"""
        try:
            with urllib.request.urlopen("http://localhost:8082/test-connection") as response:
                content = response.read().decode('utf-8')
                try:
                    data = json.loads(content)
                    if data.get("status") == "success":
                        return True, "API is accessible and returns success"
                    return False, f"API is accessible but returns unexpected status: {data.get('status')}"
                except json.JSONDecodeError:
                    return False, "API is accessible but doesn't return valid JSON"
        except urllib.error.URLError as e:
            return False, f"Failed to access API: {str(e)}"

    def test_session_validator_accessible(self):
        """Test if session validator is accessible"""
        try:
            # Create a test session ID
            test_session_id = f"test_session_{int(time.time())}"

            # Try to validate the session (should fail with a 400 or 403, but that's expected)
            try:
                urllib.request.urlopen(f"http://localhost:8083/validate?session={test_session_id}")
                # If we get here, it's unexpected but the service is running
                return True, "Session validator is accessible (unexpected success)"
            except urllib.error.HTTPError as e:
                # We expect a 400 or 403 error for an invalid session
                if e.code in (400, 403):
                    return True, f"Session validator is accessible and correctly rejected invalid session with code {e.code}"
                return False, f"Session validator returned unexpected error code: {e.code}"
        except urllib.error.URLError as e:
            return False, f"Failed to access session validator: {str(e)}"

    def test_landing_page_redirect(self):
        """Test if landing page redirects to noVNC"""
        try:
            # Since we're using a meta refresh in the HTML, we need to check the content
            # rather than HTTP redirects
            with urllib.request.urlopen("http://localhost:80/") as response:
                content = response.read().decode('utf-8')
                if 'meta http-equiv="refresh"' in content and 'vnc.html' in content:
                    return True, "Landing page contains meta refresh redirect to noVNC"
                elif '<button class="connect-button"' in content and 'Connect' in content:
                    return True, "Landing page contains Connect button to access noVNC"
                return False, "Landing page doesn't contain redirect to noVNC"
        except urllib.error.URLError as e:
            return False, f"Failed to access landing page: {str(e)}"

    def test_chromium_installed(self):
        """Test if Chromium is installed"""
        try:
            output = subprocess.check_output(["which", "chromium"], universal_newlines=True)
            if output.strip():
                return True, f"Chromium is installed at {output.strip()}"
            return False, "Chromium is not installed"
        except subprocess.CalledProcessError:
            try:
                output = subprocess.check_output(["which", "chromium-browser"], universal_newlines=True)
                if output.strip():
                    return True, f"Chromium is installed at {output.strip()}"
                return False, "Chromium is not installed"
            except subprocess.CalledProcessError:
                return False, "Chromium is not installed"

    def test_downloads_directory(self):
        """Test if downloads directory exists and is writable"""
        if os.path.isdir("/tmp/downloads"):
            if os.access("/tmp/downloads", os.W_OK):
                return True, "Downloads directory exists and is writable"
            return False, "Downloads directory exists but is not writable"
        return False, "Downloads directory does not exist"

    def test_vnc_password_set(self):
        """Test if VNC password is set"""
        # Check both possible locations for VNC password file
        if os.path.isfile("/home/<USER>/.vnc/passwd"):
            return True, "VNC password file exists in bahtuser home"
        elif os.path.isfile("/root/.vnc/passwd"):
            return True, "VNC password file exists in root home"

        # If not found, check if we're running as root and HOME is set to /root
        if os.environ.get('HOME') == '/root' and os.path.isfile(os.path.join(os.environ.get('HOME'), '.vnc/passwd')):
            return True, "VNC password file exists in $HOME/.vnc"

        return False, "VNC password file does not exist in any expected location"

    def run_all_tests(self):
        """Run all tests"""
        logger.info("Starting BahtBrowse startup tests")

        # Define all tests to run
        tests_to_run = [
            # Process tests
            ("Process Tests", [
                ("VNC Server Running", self.test_vnc_server_running),
                ("noVNC Proxy Running", self.test_novnc_proxy_running),
                ("Window Manager Running", self.test_window_manager_running),
                ("API Server Running", self.test_api_server_running),
                ("Session Validator Running", self.test_session_validator_running)
            ]),

            # Port tests
            ("Network Tests", [
                ("VNC Port Open", self.test_vnc_port_open),
                ("noVNC Port Open", self.test_novnc_port_open),
                ("HTTP Port Open", self.test_http_port_open),
                ("API Port Open", self.test_api_port_open),
                ("Session Validator Port Open", self.test_session_validator_port_open)
            ]),

            # Access tests
            ("Web Access Tests", [
                ("Landing Page Accessible", self.test_landing_page_accessible),
                ("noVNC Accessible", self.test_novnc_accessible),
                ("API Accessible", self.test_api_accessible),
                ("Session Validator Accessible", self.test_session_validator_accessible),
                ("Landing Page Redirect", self.test_landing_page_redirect)
            ]),

            # Configuration tests
            ("Configuration Tests", [
                ("Chromium Installed", self.test_chromium_installed),
                ("Downloads Directory", self.test_downloads_directory),
                ("VNC Password Set", self.test_vnc_password_set)
            ])
        ]

        if RICH_AVAILABLE:
            # Run tests with Rich UI
            for category_name, tests in tests_to_run:
                console.print(f"\n[bold blue]{category_name}[/bold blue]")

                for test_name, test_func in tests:
                    self.run_test(test_name, test_func)
        else:
            # Run tests with plain output
            for category_name, tests in tests_to_run:
                print(f"\n{category_name}")

                for test_name, test_func in tests:
                    self.run_test(test_name, test_func)

        # Save test results
        self.save_results()

        # Return overall success/failure
        return self.test_results["summary"]["failed"] == 0

    def save_results(self):
        """Save test results to file"""
        results_dir = "/tmp/test_results"
        os.makedirs(results_dir, exist_ok=True)

        # Save JSON results
        json_file = os.path.join(results_dir, "startup_tests.json")
        with open(json_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)

        # Save text report
        report_file = os.path.join(results_dir, "startup_tests.txt")
        with open(report_file, 'w') as f:
            f.write("BahtBrowse Container Startup Test Report\n")
            f.write("=======================================\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Container: {self.hostname}\n\n")

            f.write("Test Results:\n")
            for test in self.test_results["tests"]:
                status = "PASS" if test["success"] else "FAIL"
                f.write(f"[{status}] {test['name']}: {test['message']} ({test['duration']}s)\n")

            f.write("\nSummary:\n")
            f.write(f"Total Tests: {self.test_results['summary']['total']}\n")
            f.write(f"Passed: {self.test_results['summary']['passed']}\n")
            f.write(f"Failed: {self.test_results['summary']['failed']}\n")

        logger.info(f"Test results saved to {json_file} and {report_file}")

        # Print summary with Rich if available
        if RICH_AVAILABLE:
            # Create a table for test results
            table = Table(title="BahtBrowse Startup Test Results")
            table.add_column("Status", style="bold")
            table.add_column("Test", style="cyan")
            table.add_column("Message")
            table.add_column("Duration", justify="right")

            for test in self.test_results["tests"]:
                status = "[green]✓ PASS[/green]" if test["success"] else "[red]✗ FAIL[/red]"
                table.add_row(
                    status,
                    test["name"],
                    test["message"],
                    f"{test['duration']}s"
                )

            console.print(table)

            # Print summary
            total = self.test_results["summary"]["total"]
            passed = self.test_results["summary"]["passed"]
            failed = self.test_results["summary"]["failed"]

            summary_style = "green" if failed == 0 else "red"
            summary = f"[bold {summary_style}]Summary: {passed}/{total} tests passed"
            if failed > 0:
                summary += f", {failed} failed"
            summary += f"[/bold {summary_style}]"

            console.print(Panel(summary, border_style=summary_style))

            if failed > 0:
                failed_tests = [test for test in self.test_results["tests"] if not test["success"]]
                failed_table = Table(title="Failed Tests", border_style="red")
                failed_table.add_column("Test", style="cyan")
                failed_table.add_column("Message")

                for test in failed_tests:
                    failed_table.add_row(test["name"], test["message"])

                console.print(failed_table)
        else:
            # Print summary in plain text
            print("\n=== BahtBrowse Startup Test Summary ===")
            print(f"Total Tests: {self.test_results['summary']['total']}")
            print(f"Passed: {self.test_results['summary']['passed']}")
            print(f"Failed: {self.test_results['summary']['failed']}")
            print(f"See {report_file} for details")

            # If any tests failed, print them
            if self.test_results["summary"]["failed"] > 0:
                print("\nFailed Tests:")
                for test in self.test_results["tests"]:
                    if not test["success"]:
                        print(f"- {test['name']}: {test['message']}")

def main():
    """Main function"""
    # Display startup message with Rich if available
    if RICH_AVAILABLE:
        console.print("\n[bold cyan]BahtBrowse Container Startup Tests[/bold cyan]")
        console.print("[yellow]Waiting for services to start...[/yellow]")
    else:
        print("\nBahtBrowse Container Startup Tests")
        print("Waiting for services to start...")

    # Wait a bit for services to start with a countdown if Rich is available
    if RICH_AVAILABLE:
        with console.status("[bold green]Waiting for services to initialize...[/bold green]") as status:
            for i in range(5, 0, -1):
                status.update(f"[bold green]Waiting for services to initialize... {i}s[/bold green]")
                time.sleep(1)
    else:
        time.sleep(5)

    # Run tests
    if RICH_AVAILABLE:
        console.print("\n[bold green]Starting tests...[/bold green]")
    else:
        print("\nStarting tests...")

    tests = BahtBrowseStartupTests()
    success = tests.run_all_tests()

    # Display final message
    if RICH_AVAILABLE:
        if success:
            console.print("\n[bold green]All tests passed successfully![/bold green]")
        else:
            console.print("\n[bold red]Some tests failed. See details above.[/bold red]")
    else:
        if success:
            print("\nAll tests passed successfully!")
        else:
            print("\nSome tests failed. See details above.")

    # Return exit code based on test success
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
