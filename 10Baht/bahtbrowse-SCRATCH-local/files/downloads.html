<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>bahtBrowse Downloads</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .file-list {
            list-style: none;
            padding: 0;
        }
        .file-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .file-item:hover {
            background-color: #f9f9f9;
        }
        .file-name {
            flex-grow: 1;
            margin-right: 15px;
            word-break: break-all;
        }
        .file-size {
            color: #7f8c8d;
            width: 100px;
            text-align: right;
        }
        .file-date {
            color: #7f8c8d;
            width: 200px;
            text-align: right;
        }
        .file-actions {
            display: flex;
            gap: 10px;
            margin-left: 15px;
        }
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .empty-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .file-icon {
            margin-right: 10px;
            width: 24px;
            height: 24px;
        }
        .refresh-btn {
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fcf8e3;
            border: 1px solid #faebcc;
            color: #8a6d3b;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .file-item.dangerous {
            background-color: #ffeeee;
        }
        .dangerous-tag {
            background-color: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>bahtBrowse Downloads</h1>
        
        <div class="warning">
            <strong>Security Notice:</strong> Files downloaded in this secure container are isolated from your main system. 
            Exercise caution when downloading and opening files, especially executables (.exe, .bat, .sh, etc.).
        </div>
        
        <button id="refreshBtn" class="btn btn-primary refresh-btn">Refresh File List</button>
        
        <div id="fileListContainer">
            <ul id="fileList" class="file-list">
                <!-- Files will be listed here -->
                <li class="empty-message">Loading files...</li>
            </ul>
        </div>
    </div>

    <script>
        // List of potentially dangerous file extensions
        const dangerousExtensions = [
            'exe', 'bat', 'cmd', 'sh', 'ps1', 'vbs', 'js', 'jar', 'jnlp', 'msi', 'dll', 'scr', 'com'
        ];
        
        // Function to format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Function to get file icon based on extension
        function getFileIcon(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            
            // Map common extensions to icons
            const iconMap = {
                'pdf': '📄',
                'doc': '📝',
                'docx': '📝',
                'xls': '📊',
                'xlsx': '📊',
                'ppt': '📊',
                'pptx': '📊',
                'txt': '📄',
                'csv': '📊',
                'jpg': '🖼️',
                'jpeg': '🖼️',
                'png': '🖼️',
                'gif': '🖼️',
                'zip': '📦',
                'rar': '📦',
                'tar': '📦',
                'gz': '📦',
                'exe': '⚠️',
                'sh': '⚠️',
                'bat': '⚠️',
                'js': '⚠️',
                'html': '🌐',
                'htm': '🌐',
                'mp3': '🎵',
                'mp4': '🎬',
                'avi': '🎬',
                'mov': '🎬'
            };
            
            return iconMap[ext] || '📄';
        }
        
        // Function to check if a file is potentially dangerous
        function isDangerousFile(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            return dangerousExtensions.includes(ext);
        }
        
        // Function to load file list
        function loadFileList() {
            fetch('/downloads-api/list')
                .then(response => response.json())
                .then(data => {
                    const fileList = document.getElementById('fileList');
                    
                    if (data.files && data.files.length > 0) {
                        fileList.innerHTML = '';
                        
                        data.files.forEach(file => {
                            const isDangerous = isDangerousFile(file.name);
                            const li = document.createElement('li');
                            li.className = `file-item ${isDangerous ? 'dangerous' : ''}`;
                            
                            li.innerHTML = `
                                <div class="file-name">
                                    <span class="file-icon">${getFileIcon(file.name)}</span>
                                    ${file.name}
                                    ${isDangerous ? '<span class="dangerous-tag">CAUTION</span>' : ''}
                                </div>
                                <div class="file-size">${formatFileSize(file.size)}</div>
                                <div class="file-date">${new Date(file.mtime).toLocaleString()}</div>
                                <div class="file-actions">
                                    <a href="/downloads/${encodeURIComponent(file.name)}" class="btn btn-primary" download>Download</a>
                                    <button class="btn btn-danger" onclick="deleteFile('${file.name}')">Delete</button>
                                </div>
                            `;
                            
                            fileList.appendChild(li);
                        });
                    } else {
                        fileList.innerHTML = '<li class="empty-message">No files found</li>';
                    }
                })
                .catch(error => {
                    console.error('Error loading file list:', error);
                    document.getElementById('fileList').innerHTML = 
                        '<li class="empty-message">Error loading files. Please try again.</li>';
                });
        }
        
        // Function to delete a file
        function deleteFile(filename) {
            if (confirm(`Are you sure you want to delete ${filename}?`)) {
                fetch(`/downloads-api/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ filename }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadFileList();
                    } else {
                        alert('Error deleting file: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting file');
                });
            }
        }
        
        // Load file list when page loads
        document.addEventListener('DOMContentLoaded', loadFileList);
        
        // Refresh button event listener
        document.getElementById('refreshBtn').addEventListener('click', loadFileList);
    </script>
</body>
</html>
