#!/usr/bin/env python3
"""
Session Manager for BahtBrowse

Handles secure session management with IP locking for VNC connections.
"""

import time
import uuid
import logging
import threading
import ipad<PERSON>
from typing import Dict, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("session-manager")

class SessionManager:
    """Manages secure sessions with IP locking for VNC connections"""
    
    def __init__(self, session_timeout_seconds: int = 3600):
        """Initialize the session manager
        
        Args:
            session_timeout_seconds: How long sessions remain valid (default: 1 hour)
        """
        self.sessions: Dict[str, Dict] = {}
        self.session_timeout = session_timeout_seconds
        self.lock = threading.RLock()
        
        # Start a background thread to clean up expired sessions
        self.cleanup_thread = threading.Thread(target=self._cleanup_expired_sessions, daemon=True)
        self.cleanup_thread.start()
        
        logger.info("Session manager initialized with timeout of %d seconds", session_timeout_seconds)
    
    def create_session(self, client_ip: str) -> str:
        """Create a new session for a client IP
        
        Args:
            client_ip: The IP address of the client
            
        Returns:
            The session ID
        """
        # Validate IP address
        try:
            ipaddress.ip_address(client_ip)
        except ValueError:
            logger.error("Invalid IP address: %s", client_ip)
            raise ValueError(f"Invalid IP address: {client_ip}")
        
        with self.lock:
            # Generate a unique session ID
            session_id = str(uuid.uuid4())
            
            # Store session info
            self.sessions[session_id] = {
                "ip": client_ip,
                "created_at": time.time(),
                "last_accessed": time.time()
            }
            
            logger.info("Created new session %s for IP %s", session_id, client_ip)
            return session_id
    
    def validate_session(self, session_id: str, client_ip: str) -> bool:
        """Validate a session ID and client IP
        
        Args:
            session_id: The session ID to validate
            client_ip: The IP address of the client
            
        Returns:
            True if the session is valid, False otherwise
        """
        with self.lock:
            # Check if session exists
            if session_id not in self.sessions:
                logger.warning("Session %s not found", session_id)
                return False
            
            session = self.sessions[session_id]
            
            # Check if session has expired
            if time.time() - session["created_at"] > self.session_timeout:
                logger.warning("Session %s has expired", session_id)
                del self.sessions[session_id]
                return False
            
            # Check if IP matches
            if session["ip"] != client_ip:
                logger.warning("IP mismatch for session %s: expected %s, got %s", 
                              session_id, session["ip"], client_ip)
                return False
            
            # Update last accessed time
            session["last_accessed"] = time.time()
            logger.debug("Session %s validated for IP %s", session_id, client_ip)
            return True
    
    def get_session_info(self, session_id: str) -> Optional[Dict]:
        """Get information about a session
        
        Args:
            session_id: The session ID to look up
            
        Returns:
            Session information or None if not found
        """
        with self.lock:
            if session_id in self.sessions:
                return self.sessions[session_id].copy()
            return None
    
    def invalidate_session(self, session_id: str) -> bool:
        """Invalidate a session
        
        Args:
            session_id: The session ID to invalidate
            
        Returns:
            True if the session was invalidated, False if it didn't exist
        """
        with self.lock:
            if session_id in self.sessions:
                del self.sessions[session_id]
                logger.info("Session %s invalidated", session_id)
                return True
            return False
    
    def _cleanup_expired_sessions(self):
        """Background thread to clean up expired sessions"""
        while True:
            time.sleep(60)  # Check every minute
            
            with self.lock:
                current_time = time.time()
                expired_sessions = [
                    session_id for session_id, session in self.sessions.items()
                    if current_time - session["created_at"] > self.session_timeout
                ]
                
                for session_id in expired_sessions:
                    del self.sessions[session_id]
                    logger.info("Expired session %s removed", session_id)

# Create a global instance
session_manager = SessionManager()
