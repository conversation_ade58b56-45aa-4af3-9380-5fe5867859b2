"""Backend API server for BahtBrowse secure browser solution.

This module provides a web server that handles requests to launch browsers
(Firefox or ungoogled-chromium) in a containerized environment, with various
security settings. It manages browser sessions, handles console logging, and
provides connection testing endpoints.
"""

import datetime
import json
import logging
import os
import shutil
import subprocess
import time
import uuid
import ipaddress

import aiohttp
import validators
from aiohttp import web

# Import our session manager
from session_manager import session_manager


async def handle_request(request: web.Request) -> web.Response:
    """<PERSON>le requests to launch a browser session.

    Processes both GET and POST requests, validates the URL,
    and launches the selected browser in a containerized environment.

    Args:
        request: The HTTP request object containing URL, mode, and browser parameters

    Returns:
        A web response, either redirecting to the VNC interface or
        containing an error message

    Raises:
        Exception: If there's an error launching the browser
    """
    # Handle both GET and POST requests
    if request.method == "POST":
        try:
            # Get data from POST request
            data = await request.post()
            url = data.get("url")
            mode = data.get("mode", "normal")
            disable_js_execution = data.get("disable_js_execution", "false")
            js_timeout = data.get("js_timeout", "10000")
            browser = data.get("browser", "firefox")
            locale = data.get("locale", "en-US")  # Default to en-US if not provided
        except Exception as e:
            error_msg = f"Error processing POST data: {str(e)}"
            logging.error(error_msg)
            return aiohttp.web.Response(status=400, body=error_msg)
    else:
        # Maintain backwards compatibility with GET requests
        url = request.query.get("url")
        mode = request.query.get("mode", "normal")
        disable_js_execution = request.query.get("disable_js_execution", "false")
        js_timeout = request.query.get("js_timeout", "10000")
        browser = request.query.get("browser", "firefox")
        locale = request.query.get("locale", "en-US")  # Default to en-US if not provided

    # Validate browser parameter
    if browser not in ["firefox", "chromium"]:
        return aiohttp.web.Response(status=400, body="Invalid browser specified. Use 'firefox' or 'chromium'.")

    # Force Firefox for now since Chromium is not properly installed
    browser = "firefox"

    # Check if URL is provided and valid
    if not url:
        return aiohttp.web.Response(status=400, body="URL is required")

    validation = validators.url(url)
    if validation:
        print("URL valid")
    else:
        return aiohttp.web.Response(status=400, body="URL is invalid")

    # Log the request with parameters
    log_message = (
        f"Request for URL: {url} | Mode: {mode} | Browser: {browser} | "
        f"Disable JS: {disable_js_execution} | JS Timeout: {js_timeout} | "
        f"Locale: {locale} | Method: {request.method}"
    )
    print(log_message)
    logging.info(log_message)

    # Kill any existing browser processes to ensure a fresh start
    try:
        if browser == "firefox":
            # Kill Firefox and remove any lock files
            subprocess.run("pkill -f firefox || true", shell=True)
        else:  # chromium
            # Kill Chromium and remove any lock files
            subprocess.run("pkill -f chromium-browser || true", shell=True)
        # Wait a moment for processes to terminate
        time.sleep(1)
    except Exception as e:
        logging.warning(f"Error trying to kill previous browser processes: {e}")

    my_env = os.environ.copy()
    my_env["DISPLAY"] = ":1"

    # Set locale environment variables based on the detected locale
    # Convert from browser format (e.g., en-US) to Linux format (e.g., en_US.UTF-8)
    locale_parts = locale.split('-')
    if len(locale_parts) > 1:
        linux_locale = f"{locale_parts[0]}_{locale_parts[1].upper()}.UTF-8"
    else:
        linux_locale = f"{locale}.UTF-8"

    my_env["LANG"] = linux_locale
    my_env["LANGUAGE"] = linux_locale
    my_env["LC_ALL"] = linux_locale

    logging.info(f"Setting locale to: {linux_locale}")

    # Create a truly unique session ID for this browsing session with UUID
    # Get client IP address
    client_ip = request.remote
    logging.info(f"Client IP: {client_ip}")

    # Create a session with IP binding
    try:
        session_id = session_manager.create_session(client_ip)
        logging.info(f"Created new session: {session_id} for IP: {client_ip}")
    except ValueError as e:
        error_msg = f"Error creating session: {str(e)}"
        logging.error(error_msg)
        return aiohttp.web.Response(status=400, body=error_msg)

    # Create browser-specific profile directory
    if browser == "firefox":
        profile_dir = f"/tmp/firefox_profile_{session_id}"
        os.makedirs(profile_dir, exist_ok=True)

        # Log the creation of the profile directory
        logging.info(f"Created new Firefox profile directory: {profile_dir}")

        # Copy the user.js file to the new profile directory
        try:
            # Copy the base user.js file
            shutil.copy("/tmp/firefox_profile/user.js", f"{profile_dir}/user.js")

            # Append additional Firefox preferences to disable caching
            with open(f"{profile_dir}/user.js", "a") as f:
                f.write("\n// Added preferences to disable caching\n")
                f.write('user_pref("browser.cache.disk.enable", false);\n')
                f.write('user_pref("browser.cache.memory.enable", false);\n')
                f.write('user_pref("browser.cache.offline.enable", false);\n')
                f.write('user_pref("network.http.use-cache", false);\n')
                f.write('user_pref("browser.privatebrowsing.autostart", true);\n')

            logging.info("Copied and modified user.js to new Firefox profile with cache disabled")
        except Exception as e:
            logging.warning(f"Failed to prepare new Firefox profile: {e}")
    else:  # chromium
        profile_dir = f"/tmp/chromium_profile_{session_id}"
        os.makedirs(profile_dir, exist_ok=True)

        # Log the creation of the profile directory
        logging.info(f"Created new Chromium profile directory: {profile_dir}")

        # Set up Chromium profile
        try:
            # Create Default directory for Chromium profile
            os.makedirs(f"{profile_dir}/Default", exist_ok=True)

            # Copy the preferences file
            shutil.copy("/tmp/chromium_preferences.json", f"{profile_dir}/Default/Preferences")

            logging.info("Copied preferences to new Chromium profile")
        except Exception as e:
            logging.warning(f"Failed to prepare new Chromium profile: {e}")

    # Add cache-buster to URL if needed
    if "?" in url:
        cache_buster_url = f"{url}&_cb={session_id}"
    else:
        cache_buster_url = f"{url}?_cb={session_id}"

    logging.info(f"Using cache-busted URL: {cache_buster_url}")

    # Build browser-specific command with additional parameters if needed
    if browser == "firefox":
        if mode == "compatibility":
            # Special handling for complex sites like Amazon
            command = (
                f"/tmp/firefox/firefox --profile {profile_dir} --new-instance "
                f'--url "{cache_buster_url}" --kiosk '
                f"--pref=javascript.enabled={disable_js_execution != 'true'} "
                f"--pref=dom.max_script_run_time={int(js_timeout)//1000} &"
            )
            print(f"Using Firefox compatibility mode with custom settings: {command}")
        else:
            # Standard command - use the unique profile and force new instance
            command = (
                f"/tmp/firefox/firefox --profile {profile_dir} --new-instance "
                f'--url "{cache_buster_url}" --kiosk &'
            )
            print(f"Using Firefox standard mode: {command}")
    else:  # chromium
        if mode == "compatibility":
            # Special handling for complex sites
            command = (
                f"/usr/bin/chromium-browser --user-data-dir={profile_dir} "
                f"--no-first-run --no-default-browser-check "
                f"--incognito --kiosk "
                f"--disable-features=TranslateUI "
                f"--disable-sync "
                f"--js-flags=\"--max-old-space-size=128\" "
                f'"{cache_buster_url}" &'
            )
            print(f"Using Chromium compatibility mode with custom settings: {command}")
        else:
            # Standard command for Chromium
            command = (
                f"/usr/bin/chromium-browser --user-data-dir={profile_dir} "
                f"--no-first-run --no-default-browser-check "
                f"--incognito --kiosk "
                f'"{cache_buster_url}" &'
            )
            print(f"Using Chromium standard mode: {command}")

    try:
        # Execute the subprocess
        subprocess.run(command, env=my_env, shell=True)

        # Log the redirection
        # Extract the host without the port
        host_parts = request.host.split(":")
        host_without_port = host_parts[0]

        # Redirect to Nginx (port 80) which handles the /vnc1/run proxy
        # Add cache-busting to ensure a fresh load of the VNC page
        redirect_url = f"http://{host_without_port}:80/vnc1/run?session={session_id}"
        print(f"Redirecting to: {redirect_url}")
        logging.info(f"Redirecting to: {redirect_url}")

        # Redirect to the correct Nginx port
        return web.HTTPFound(location=redirect_url)
    except Exception as e:
        error_msg = f"Error launching browser: {str(e)}"
        logging.error(error_msg)
        return aiohttp.web.Response(status=500, body=error_msg)


def handle_test_connection(request: web.Request) -> web.Response:
    """Handle test connection requests.

    Process connection test requests and log client information.

    Args:
        request: The HTTP request object

    Returns:
        A JSON response with connection status and information
    """
    # Log the test connection request
    client_ip = request.remote
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    user_agent = request.headers.get("User-Agent", "Unknown")
    # Get headers but don't store if they're not used
    request.headers.get("Referer", "Unknown")
    request.headers.get("X-Test-Connection", "Unknown")
    client_info = request.headers.get("X-Client-Info", "Unknown")
    request_source = request.headers.get("X-Request-Source", "Unknown")

    # Get plugin version if available
    plugin_version = request.headers.get("X-Plugin-Version", "Unknown")

    # Get additional information from query parameters or POST data
    if request.method == "POST":
        try:
            data = request.post()
            host = data.get("host", "Unknown")
            port = data.get("port", "Unknown")
        except Exception as e:
            logging.error(f"Error processing POST data in test-connection: {str(e)}")
            host = "Unknown"
            port = "Unknown"
    else:
        host = request.query.get("host", "Unknown")
        port = request.query.get("port", "Unknown")

    # Log the test connection with all headers
    log_message = (
        f"[{timestamp}] Test connection from {client_ip} | "
        f"Method: {request.method} | User-Agent: {user_agent} | "
        f"Client-Info: {client_info} | Request-Source: {request_source} | "
        f"Plugin-Version: {plugin_version} | Host: {host} | Port: {port}"
    )
    print(log_message)
    logging.info(log_message)

    # Log all headers for debugging
    headers_log = f"[{timestamp}] Headers: {dict(request.headers)}"
    print(headers_log)
    logging.debug(headers_log)

    # Return a success response with more detailed information
    return web.json_response(
        {
            "status": "success",
            "message": "BahtBrowse service is available",
            "timestamp": timestamp,
            "client_ip": str(client_ip),
            "client_info": client_info,
            "request_source": request_source,
            "plugin_version": plugin_version,
            "host": host,
            "port": port,
            "server_version": "1.1.0",
            "compatibility_mode": True,
            "supported_browsers": ["firefox", "chromium"],
            "supported_sites": ["amazon.com", "amazon.*", "amzn.*"],
            "server_time": datetime.datetime.now().isoformat(),
        }
    )


async def handle_console_log(request: web.Request) -> web.Response:
    """Handle console log messages from the browser.

    Process and log console messages sent from the browser.

    Args:
        request: The HTTP request object containing log data

    Returns:
        A JSON response indicating success or failure

    Raises:
        Exception: If there's an error processing the log message
    """
    # Handle console log messages from the browser
    try:
        # Get the request body as JSON
        data = await request.json()

        # Extract information from the request
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_level = data.get("level", "info")
        message = data.get("message", "No message")
        url = data.get("url", "Unknown URL")
        line = data.get("line", "Unknown")
        column = data.get("column", "Unknown")
        session_id = data.get("session_id", "Unknown")
        stack = data.get("stack", "No stack trace")

        # Format the log message
        log_message = (
            f"[{timestamp}] [BROWSER CONSOLE] [{log_level.upper()}] {message} | "
            f"URL: {url} | Line: {line} | Column: {column} | "
            f"Session: {session_id}"
        )

        # Log the message
        print(log_message)
        if log_level.lower() == "error":
            logging.error(log_message)
            # Log stack trace for errors
            if stack:
                stack_message = f"[{timestamp}] [BROWSER CONSOLE] [STACK] {stack}"
                logging.error(stack_message)
                print(stack_message)
        elif log_level.lower() == "warn":
            logging.warning(log_message)
        else:
            logging.info(log_message)

        return web.json_response(
            {"status": "success", "message": "Console log received"}
        )
    except Exception as e:
        error_message = f"Error processing console log: {str(e)}"
        logging.error(error_message)
        return web.json_response(
            {"status": "error", "message": error_message}, status=500
        )


async def handle_downloads_list(request: web.Request) -> web.Response:
    """Handle requests to list downloaded files.

    Args:
        request: The HTTP request object

    Returns:
        A JSON response with the list of downloaded files
    """
    try:
        downloads_dir = "/tmp/downloads"
        files = []

        # Check if downloads directory exists
        if os.path.exists(downloads_dir) and os.path.isdir(downloads_dir):
            # Get all files in the downloads directory
            for filename in os.listdir(downloads_dir):
                file_path = os.path.join(downloads_dir, filename)
                if os.path.isfile(file_path):
                    # Get file stats
                    stats = os.stat(file_path)
                    files.append({
                        "name": filename,
                        "size": stats.st_size,
                        "mtime": stats.st_mtime * 1000,  # Convert to milliseconds for JavaScript
                    })

        # Sort files by modification time (newest first)
        files.sort(key=lambda x: x["mtime"], reverse=True)

        return web.json_response({"success": True, "files": files})
    except Exception as e:
        error_message = f"Error listing downloads: {str(e)}"
        logging.error(error_message)
        return web.json_response(
            {"success": False, "error": error_message}, status=500
        )


async def handle_downloads_delete(request: web.Request) -> web.Response:
    """Handle requests to delete a downloaded file.

    Args:
        request: The HTTP request object containing the filename to delete

    Returns:
        A JSON response indicating success or failure
    """
    try:
        # Get the request body as JSON
        data = await request.json()
        filename = data.get("filename")

        if not filename:
            return web.json_response(
                {"success": False, "error": "Filename is required"}, status=400
            )

        # Sanitize filename to prevent directory traversal attacks
        filename = os.path.basename(filename)
        file_path = os.path.join("/tmp/downloads", filename)

        # Check if file exists
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            return web.json_response(
                {"success": False, "error": "File not found"}, status=404
            )

        # Delete the file
        os.remove(file_path)
        logging.info(f"Deleted file: {file_path}")

        return web.json_response({"success": True})
    except Exception as e:
        error_message = f"Error deleting file: {str(e)}"
        logging.error(error_message)
        return web.json_response(
            {"success": False, "error": error_message}, status=500
        )


async def handle_downloads_page(request: web.Request) -> web.Response:
    """Serve the downloads manager HTML page.

    Args:
        request: The HTTP request object

    Returns:
        The HTML page response
    """
    try:
        with open("/tmp/downloads.html", "r") as f:
            content = f.read()
        return web.Response(text=content, content_type="text/html")
    except Exception as e:
        error_message = f"Error serving downloads page: {str(e)}"
        logging.error(error_message)
        return web.Response(text=f"Error: {error_message}", status=500)


def main() -> None:
    """Initialize and start the BahtBrowse web server.

    Sets up logging, configures routes, and starts the aiohttp server.
    """
    # Configure logging
    logging.basicConfig(
        filename="/tmp/bahtbrowse_server.log", level=logging.DEBUG, format="%(message)s"
    )

    # Also create a separate log file for browser console errors
    console_handler = logging.FileHandler("/tmp/browser_console.log")
    console_handler.setLevel(logging.DEBUG)
    console_formatter = logging.Formatter("%(message)s")
    console_handler.setFormatter(console_formatter)
    logging.getLogger().addHandler(console_handler)

    # Log server start
    start_message = (
        f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] "
        f"BahtBrowse server started"
    )
    print(start_message)
    logging.info(start_message)

    # Create the web application
    app = web.Application()

    # Add routes with root paths
    app.router.add_get("/", handle_request)
    app.router.add_post("/", handle_request)
    app.router.add_get("/test-connection", handle_test_connection)
    app.router.add_post("/test-connection", handle_test_connection)
    app.router.add_post("/log-console", handle_console_log)

    # Add routes with /browse/ prefix to match Firefox plugin expectations
    app.router.add_get("/browse/", handle_request)
    app.router.add_post("/browse/", handle_request)
    app.router.add_get("/browse/test-connection", handle_test_connection)
    app.router.add_post("/browse/test-connection", handle_test_connection)
    app.router.add_post("/browse/log-console", handle_console_log)

    # Add routes for downloads manager
    app.router.add_get("/downloads", handle_downloads_page)
    app.router.add_get("/downloads-api/list", handle_downloads_list)
    app.router.add_post("/downloads-api/delete", handle_downloads_delete)

    # Add static route for serving downloaded files
    app.router.add_static("/downloads/", "/tmp/downloads", show_index=False)

    # Run the web server
    try:
        web.run_app(app, port=8082)
    except Exception as e:
        error_message = f"An error occurred: {e}"
        print(error_message)
        logging.error(error_message)


if __name__ == "__main__":
    main()
