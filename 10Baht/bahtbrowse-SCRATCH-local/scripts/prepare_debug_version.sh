#!/bin/bash

# <PERSON><PERSON>t to prepare a debug version of the Firefox extension

# Set variables
DEBUG_DIR="debug_build/bahtbrowse_firefox_plugin_debug"

# Print header
echo "=========================================="
echo "  BahtBrowse Firefox Plugin Debug Prep    "
echo "=========================================="
echo ""

# Create debug directory
mkdir -p "${DEBUG_DIR}"

# Clean up any previous debug build
echo "Cleaning up previous debug build..."
rm -rf "${DEBUG_DIR}/*"

# Copy extension files to debug directory
echo "Copying extension files..."
cp -r firefox_plugin/* "${DEBUG_DIR}/"

# Replace manifest.json with debug version
echo "Setting up debug version..."
cp "${DEBUG_DIR}/manifest_debug.json" "${DEBUG_DIR}/manifest.json"

echo ""
echo "Debug version prepared!"
echo "Files are ready in: ${DEBUG_DIR}"
echo ""
echo "To install the debug version in Firefox:"
echo "1. Open Firefox"
echo "2. Go to about:debugging"
echo "3. Click 'This Firefox'"
echo "4. Click 'Load Temporary Add-on...'"
echo "5. Navigate to the debug directory and select manifest.json"
echo ""
echo "After installation, check the browser console (F12) for debug logs"
echo "=========================================="
