#!/bin/bash

# Comprehensive test script for noVNC connection issues
# This script tests each component in the noVNC connection chain:
# Browser Request → Nginx → noVNC HTML/JS → WebSocket Connection → WebSocket Proxy → VNC Server → X11/Firefox

# Set container name - change this to match your container
CONTAINER="bahtbrowse-browsers-firefox-alpine"

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
section() {
    echo -e "\n${BLUE}========== $1 ==========${NC}\n"
}

# Function to print test results
result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[PASS]${NC} $2"
    else
        echo -e "${RED}[FAIL]${NC} $2"
    fi
}

# Function to print info messages
info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

section "STAGE 1: CHECKING X11 AND VNC SERVER"

# Check if X11 server (Xvfb) is running
info "Checking if X11 server (Xvfb) is running..."
docker exec $CONTAINER ps aux | grep Xvfb | grep -v grep > /dev/null
result $? "X11 server (Xvfb) is running"

# Check X11 server command line
info "Checking X11 server command line..."
XVFB_CMD=$(docker exec $CONTAINER ps aux | grep Xvfb | grep -v grep)
echo -e "${YELLOW}[INFO]${NC} X11 server command: $XVFB_CMD"

# Check if VNC server (x11vnc) is running
info "Checking if VNC server (x11vnc) is running..."
docker exec $CONTAINER ps aux | grep x11vnc | grep -v grep > /dev/null
result $? "VNC server (x11vnc) is running"

# Check VNC server command line
info "Checking VNC server command line..."
VNC_CMD=$(docker exec $CONTAINER ps aux | grep x11vnc | grep -v grep)
echo -e "${YELLOW}[INFO]${NC} VNC server command: $VNC_CMD"

# Check if Firefox is running
info "Checking if Firefox is running..."
docker exec $CONTAINER ps aux | grep firefox | grep -v grep > /dev/null
result $? "Firefox is running"

# Check Firefox command line
info "Checking Firefox command line..."
FIREFOX_CMD=$(docker exec $CONTAINER ps aux | grep firefox | grep -v grep)
echo -e "${YELLOW}[INFO]${NC} Firefox command: $FIREFOX_CMD"

section "STAGE 2: CHECKING NOVNC CONFIGURATION"

# Check if noVNC files exist
info "Checking if noVNC files exist..."
docker exec $CONTAINER ls -la /tmp/noVNC/vnc.html > /dev/null 2>&1
result $? "noVNC files exist"

# Check noVNC HTML content
info "Checking noVNC HTML content..."
NOVNC_HTML=$(docker exec $CONTAINER head -20 /tmp/noVNC/vnc.html 2>/dev/null)
if [[ "$NOVNC_HTML" == *"noVNC"* ]]; then
    result 0 "noVNC HTML content looks valid"
else
    result 1 "noVNC HTML content may be invalid"
fi
echo -e "${YELLOW}[INFO]${NC} First 20 lines of noVNC HTML:"
echo "$NOVNC_HTML"

# Check if websockify is running
info "Checking if websockify is running..."
docker exec $CONTAINER ps aux | grep websockify | grep -v grep > /dev/null
result $? "WebSocket proxy (websockify) is running"

# Check websockify command line
info "Checking websockify command line..."
WEBSOCKIFY_CMD=$(docker exec $CONTAINER ps aux | grep websockify | grep -v grep)
echo -e "${YELLOW}[INFO]${NC} Websockify command: $WEBSOCKIFY_CMD"

# Check if websockify is configured correctly
info "Checking if websockify is configured correctly..."
if [[ "$WEBSOCKIFY_CMD" == *"--vnc"* ]]; then
    result 0 "WebSocket proxy is configured with VNC target"
else
    result 1 "WebSocket proxy may not be configured with VNC target"
fi

section "STAGE 3: CHECKING PORT CONFIGURATION"

# Check if port 5900 (VNC) is listening inside container
info "Checking if port 5900 (VNC) is listening inside container..."
VNC_PORT_STATUS=$(docker exec $CONTAINER bash -c 'netstat -tuln | grep 5900')
echo "$VNC_PORT_STATUS"
if [[ -n "$VNC_PORT_STATUS" ]]; then
    result 0 "Port 5900 (VNC) is listening inside container"
else
    result 1 "Port 5900 (VNC) is NOT listening inside container"
fi

# Check if port 6080 (WebSocket) is listening inside container
info "Checking if port 6080 (WebSocket) is listening inside container..."
WS_PORT_STATUS=$(docker exec $CONTAINER bash -c 'netstat -tuln | grep 6080')
echo "$WS_PORT_STATUS"
if [[ -n "$WS_PORT_STATUS" ]]; then
    result 0 "Port 6080 (WebSocket) is listening inside container"
else
    result 1 "Port 6080 (WebSocket) is NOT listening inside container"
fi

# Check if port 80 (Nginx) is listening inside container
info "Checking if port 80 (Nginx) is listening inside container..."
NGINX_PORT_STATUS=$(docker exec $CONTAINER bash -c 'netstat -tuln | grep ":80"')
echo "$NGINX_PORT_STATUS"
if [[ -n "$NGINX_PORT_STATUS" ]]; then
    result 0 "Port 80 (Nginx) is listening inside container"
else
    result 1 "Port 80 (Nginx) is NOT listening inside container"
fi

# Check port mappings
info "Checking port mappings..."
PORT_MAPPINGS=$(docker port $CONTAINER)
echo "$PORT_MAPPINGS"

# Check if port 5901 is mapped
if [[ "$PORT_MAPPINGS" == *"5901"* ]]; then
    result 0 "Port 5901 (VNC) is properly mapped"
else
    result 1 "Port 5901 (VNC) is NOT properly mapped"
fi

# Check if port 6080 is mapped
if [[ "$PORT_MAPPINGS" == *"6080"* ]]; then
    result 0 "Port 6080 (WebSocket) is properly mapped"
else
    result 1 "Port 6080 (WebSocket) is NOT properly mapped"
fi

# Check if port 8001 is mapped
if [[ "$PORT_MAPPINGS" == *"8001"* ]]; then
    result 0 "Port 8001 (Nginx) is properly mapped"
else
    result 1 "Port 8001 (Nginx) is NOT properly mapped"
fi

section "STAGE 4: TESTING DIRECT CONNECTIONS"

# Test direct VNC connection from container to itself
info "Testing direct VNC connection from container to itself..."
docker exec $CONTAINER bash -c 'nc -z localhost 5900'
result $? "Direct VNC connection from container to itself is possible"

# Test WebSocket connection from container to itself
info "Testing WebSocket connection from container to itself..."
docker exec $CONTAINER bash -c 'nc -z localhost 6080'
result $? "WebSocket connection from container to itself is possible"

# Test direct VNC connection from host to container
info "Testing direct VNC connection from host to container..."
nc -z localhost 5901
result $? "Direct VNC connection from host to container is possible"

# Test WebSocket connection from host to container
info "Testing WebSocket connection from host to container..."
nc -z localhost 6080
result $? "WebSocket connection from host to container is possible"

# Test HTTP connection to Nginx
info "Testing HTTP connection to Nginx..."
NGINX_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/)
if [ "$NGINX_STATUS" -eq 200 ]; then
    result 0 "HTTP connection to Nginx is possible (HTTP 200)"
else
    result 1 "HTTP connection to Nginx is not possible (HTTP $NGINX_STATUS)"
fi

# Test HTTP connection to noVNC
info "Testing HTTP connection to noVNC..."
NOVNC_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/vnc/vnc.html)
if [ "$NOVNC_STATUS" -eq 200 ]; then
    result 0 "HTTP connection to noVNC is possible (HTTP 200)"
else
    result 1 "HTTP connection to noVNC is not possible (HTTP $NOVNC_STATUS)"
fi

# Test direct WebSocket connection
info "Testing direct WebSocket connection..."
echo -e "${YELLOW}[INFO]${NC} This test requires a browser. Please open http://localhost:8001/test.html to test WebSocket connections."

# Create a WebSocket test file
cat > ws_test.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #333;
            border-radius: 5px;
        }
        h1 {
            color: #4CAF50;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #555;
            cursor: not-allowed;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #4CAF50;
            color: white;
        }
        .disconnected {
            background-color: #f44336;
            color: white;
        }
        .connecting {
            background-color: #2196F3;
            color: white;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background-color: #222;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Connection Test</h1>

        <div id="status" class="disconnected">Status: Disconnected</div>

        <div>
            <button id="connect1">Connect to ws://localhost:6080/websockify</button>
            <button id="connect2">Connect to ws://localhost:8001/websockify</button>
            <button id="disconnect" disabled>Disconnect</button>
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        const statusElement = document.getElementById('status');
        const logElement = document.getElementById('log');
        const connectButton1 = document.getElementById('connect1');
        const connectButton2 = document.getElementById('connect2');
        const disconnectButton = document.getElementById('disconnect');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += \`<div>[\${timestamp}] \${message}</div>\`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function connect(url) {
            if (ws) {
                ws.close();
            }

            statusElement.className = 'connecting';
            statusElement.textContent = \`Status: Connecting to \${url}...\`;
            log(\`Attempting to connect to \${url}\`);

            try {
                ws = new WebSocket(url);

                ws.onopen = function() {
                    statusElement.className = 'connected';
                    statusElement.textContent = \`Status: Connected to \${url}\`;
                    connectButton1.disabled = true;
                    connectButton2.disabled = true;
                    disconnectButton.disabled = false;
                    log('Connection established');
                };

                ws.onclose = function(event) {
                    statusElement.className = 'disconnected';
                    statusElement.textContent = 'Status: Disconnected';
                    connectButton1.disabled = false;
                    connectButton2.disabled = false;
                    disconnectButton.disabled = true;
                    log(\`Connection closed (code: \${event.code}, reason: \${event.reason || 'none'})\`);
                    ws = null;
                };

                ws.onerror = function(error) {
                    log('WebSocket error');
                    console.error('WebSocket error:', error);
                };

                ws.onmessage = function(event) {
                    log(\`Received data: \${event.data.length} bytes\`);
                };
            } catch (error) {
                statusElement.className = 'disconnected';
                statusElement.textContent = 'Status: Error creating WebSocket';
                log(\`Error: \${error.message}\`);
                console.error('Error creating WebSocket:', error);
            }
        }

        connectButton1.addEventListener('click', function() {
            connect('ws://localhost:6080/websockify');
        });

        connectButton2.addEventListener('click', function() {
            connect('ws://localhost:8001/websockify');
        });

        disconnectButton.addEventListener('click', function() {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>
EOF

docker cp ws_test.html $CONTAINER:/tmp/serve/test.html
echo -e "${YELLOW}[INFO]${NC} WebSocket test file created at http://localhost:8001/test.html"

section "STAGE 5: CHECKING NGINX CONFIGURATION"

# Check nginx configuration
info "Checking nginx configuration..."
NGINX_CONFIG_STATUS=$(docker exec $CONTAINER bash -c 'nginx -t 2>&1')
echo "$NGINX_CONFIG_STATUS"
if [[ "$NGINX_CONFIG_STATUS" == *"successful"* ]]; then
    result 0 "Nginx configuration is valid"
else
    result 1 "Nginx configuration is invalid"
fi

# Check nginx configuration file
info "Checking nginx configuration file..."
NGINX_CONFIG=$(docker exec $CONTAINER cat /etc/nginx/http.d/default.conf)
echo "$NGINX_CONFIG"

# Check nginx WebSocket proxy configuration
info "Checking nginx WebSocket proxy configuration..."
if [[ "$NGINX_CONFIG" == *"proxy_http_version 1.1"* ]]; then
    result 0 "Nginx WebSocket proxy is properly configured with HTTP/1.1"
else
    result 1 "Nginx WebSocket proxy is NOT properly configured with HTTP/1.1"
fi

# Check nginx WebSocket upgrade headers
info "Checking nginx WebSocket upgrade headers..."
if [[ "$NGINX_CONFIG" == *"proxy_set_header Upgrade"* ]]; then
    result 0 "Nginx is configured with WebSocket upgrade headers"
else
    result 1 "Nginx is NOT configured with WebSocket upgrade headers"
fi

# Check nginx WebSocket connection header
info "Checking nginx WebSocket connection header..."
if [[ "$NGINX_CONFIG" == *"proxy_set_header Connection"*"upgrade"* ]]; then
    result 0 "Nginx is configured with WebSocket connection header"
else
    result 1 "Nginx is NOT configured with WebSocket connection header"
fi

# Check nginx WebSocket proxy path
info "Checking nginx WebSocket proxy path..."
if [[ "$NGINX_CONFIG" == *"location /websockify"* ]]; then
    result 0 "Nginx has a location block for /websockify"
else
    result 1 "Nginx does NOT have a location block for /websockify"
fi

# Check nginx proxy pass for WebSocket
info "Checking nginx proxy pass for WebSocket..."
if [[ "$NGINX_CONFIG" == *"proxy_pass http://localhost:6080/websockify"* ]]; then
    result 0 "Nginx is configured to proxy WebSocket connections to port 6080"
else
    result 1 "Nginx is NOT configured to proxy WebSocket connections to port 6080"
fi

# Check nginx noVNC location
info "Checking nginx noVNC location..."
if [[ "$NGINX_CONFIG" == *"location /vnc"* ]]; then
    result 0 "Nginx has a location block for /vnc"
else
    result 1 "Nginx does NOT have a location block for /vnc"
fi

section "STAGE 6: CHECKING APP.PY CONFIGURATION"

# Check if app.py exists
info "Checking if app.py exists..."
docker exec $CONTAINER test -f /tmp/app.py
if [ $? -eq 0 ]; then
    result 0 "app.py exists"
else
    result 1 "app.py does NOT exist"
    echo -e "${YELLOW}[INFO]${NC} Skipping app.py checks"
    APP_EXISTS=false
fi

# Only run these checks if app.py exists
if [ "$APP_EXISTS" != "false" ]; then
    # Check app.py content
    info "Checking app.py content..."
    APP_CONTENT=$(docker exec $CONTAINER cat /tmp/app.py)
    echo "$APP_CONTENT" | head -20

    # Check app.py redirect URL
    info "Checking app.py redirect URL..."
    APP_REDIRECT=$(docker exec $CONTAINER grep -A 5 "redirect_url" /tmp/app.py 2>/dev/null | grep "redirect_url =")
    echo -e "${YELLOW}[INFO]${NC} App redirect URL: $APP_REDIRECT"

    # Check VNC port in app.py
    info "Checking VNC port in app.py..."
    VNC_PORT=$(docker exec $CONTAINER grep "vnc_port =" /tmp/app.py 2>/dev/null)
    echo -e "${YELLOW}[INFO]${NC} VNC port in app.py: $VNC_PORT"

    # Check if app.py is running
    info "Checking if app.py is running..."
    docker exec $CONTAINER ps aux | grep "python3 /tmp/app.py" | grep -v grep > /dev/null
    result $? "app.py is running"
fi

section "STAGE 7: TESTING API REDIRECT"

# Test API redirect
info "Testing API redirect..."
REDIRECT_URL=$(curl -s -I -L http://localhost:8001/api/browse/?url=https://example.com 2>/dev/null | grep -i "^location:" | head -1 | sed 's/^[Ll]ocation: //g' | tr -d '\r')
echo -e "${YELLOW}[INFO]${NC} API redirect URL: $REDIRECT_URL"

# Check if the redirect URL is correct
if [[ "$REDIRECT_URL" == *"vnc"* ]]; then
    result 0 "API redirects to a VNC URL"
else
    result 1 "API does not redirect to a VNC URL"
    echo -e "${YELLOW}[INFO]${NC} This may be expected if the API is not configured to redirect to VNC"
fi

# Test API status
info "Testing API status..."
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/api/status 2>/dev/null)
if [ "$API_STATUS" -eq 200 ]; then
    result 0 "API status endpoint is accessible (HTTP 200)"
else
    result 1 "API status endpoint is not accessible (HTTP $API_STATUS)"
    echo -e "${YELLOW}[INFO]${NC} This may be expected if the API does not have a status endpoint"
fi

section "STAGE 8: TESTING NOVNC ACCESS"

# Test direct access to noVNC page
info "Testing direct access to noVNC page..."
NOVNC_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/vnc/vnc.html)
if [ "$NOVNC_STATUS" -eq 200 ]; then
    result 0 "Direct access to noVNC page is possible (HTTP 200)"
else
    result 1 "Direct access to noVNC page is not possible (HTTP $NOVNC_STATUS)"
fi

# Test access to noVNC with WebSocket parameters
info "Testing access to noVNC with WebSocket parameters..."
NOVNC_PARAMS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8001/vnc/vnc.html?host=localhost&port=6080&autoconnect=1&resize=remote")
if [ "$NOVNC_PARAMS_STATUS" -eq 200 ]; then
    result 0 "Access to noVNC with WebSocket parameters is possible (HTTP 200)"
else
    result 1 "Access to noVNC with WebSocket parameters is not possible (HTTP $NOVNC_PARAMS_STATUS)"
fi

# Test direct access to WebSocket proxy
info "Testing direct access to WebSocket proxy..."
WS_PROXY_STATUS=$(curl -s -I -o /dev/null -w "%{http_code}" http://localhost:6080/)
if [ "$WS_PROXY_STATUS" -eq 200 ] || [ "$WS_PROXY_STATUS" -eq 400 ]; then
    result 0 "Direct access to WebSocket proxy is possible (HTTP $WS_PROXY_STATUS)"
    echo -e "${YELLOW}[INFO]${NC} HTTP 400 is expected for WebSocket proxy when accessed via HTTP"
else
    result 1 "Direct access to WebSocket proxy is not possible (HTTP $WS_PROXY_STATUS)"
fi

# Create a direct noVNC test page
info "Creating a direct noVNC test page..."
cat > direct_novnc.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Direct noVNC Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #1a1a1a;
            color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        header {
            background-color: #333;
            padding: 10px;
            text-align: center;
            border-bottom: 1px solid #444;
        }
        h1 {
            margin: 0;
            font-size: 24px;
        }
        iframe {
            flex: 1;
            border: none;
        }
    </style>
</head>
<body>
    <header>
        <h1>Direct noVNC Connection</h1>
    </header>
    <iframe src="http://localhost:6080/vnc.html?autoconnect=true"></iframe>
</body>
</html>
EOF

docker cp direct_novnc.html $CONTAINER:/tmp/serve/direct_novnc.html
echo -e "${YELLOW}[INFO]${NC} Direct noVNC test page created at http://localhost:8001/direct_novnc.html"

section "STAGE 9: CHECKING LOGS"

# Check X11 logs for errors
info "Checking X11 logs for errors..."
X11_LOGS=$(docker exec $CONTAINER cat /tmp/logs/xvfb.err 2>/dev/null | grep -i -E "error|failed|failure|exception")
if [ -z "$X11_LOGS" ]; then
    result 0 "No errors found in X11 logs"
else
    result 1 "Errors found in X11 logs"
    echo -e "${YELLOW}[INFO]${NC} X11 log errors:"
    echo "$X11_LOGS"
fi

# Check VNC logs for errors
info "Checking VNC logs for errors..."
VNC_LOGS=$(docker exec $CONTAINER cat /tmp/logs/x11vnc.err 2>/dev/null | grep -i -E "error|failed|failure|exception")
if [ -z "$VNC_LOGS" ]; then
    result 0 "No errors found in VNC logs"
else
    result 1 "Errors found in VNC logs"
    echo -e "${YELLOW}[INFO]${NC} VNC log errors:"
    echo "$VNC_LOGS"
fi

# Check noVNC logs for errors
info "Checking noVNC logs for errors..."
NOVNC_LOGS=$(docker exec $CONTAINER cat /tmp/logs/novnc.err 2>/dev/null | grep -i -E "error|failed|failure|exception")
if [ -z "$NOVNC_LOGS" ]; then
    result 0 "No errors found in noVNC logs"
else
    result 1 "Errors found in noVNC logs"
    echo -e "${YELLOW}[INFO]${NC} noVNC log errors:"
    echo "$NOVNC_LOGS"
fi

# Check nginx logs for errors
info "Checking nginx logs for errors..."
NGINX_LOGS=$(docker exec $CONTAINER cat /tmp/logs/nginx.err 2>/dev/null | grep -i -E "error|failed|failure|exception")
if [ -z "$NGINX_LOGS" ]; then
    result 0 "No errors found in nginx logs"
else
    result 1 "Errors found in nginx logs"
    echo -e "${YELLOW}[INFO]${NC} Nginx log errors:"
    echo "$NGINX_LOGS"
fi

# Check Firefox logs for errors
info "Checking Firefox logs for errors..."
FIREFOX_LOGS=$(docker exec $CONTAINER cat /tmp/logs/firefox.err 2>/dev/null | grep -i -E "error|failed|failure|exception")
if [ -z "$FIREFOX_LOGS" ]; then
    result 0 "No errors found in Firefox logs"
else
    result 1 "Errors found in Firefox logs"
    echo -e "${YELLOW}[INFO]${NC} Firefox log errors:"
    echo "$FIREFOX_LOGS"
fi

# Check API logs for errors
info "Checking API logs for errors..."
API_LOGS=$(docker exec $CONTAINER cat /tmp/logs/api.err 2>/dev/null | grep -i -E "error|failed|failure|exception")
if [ -z "$API_LOGS" ]; then
    result 0 "No errors found in API logs"
else
    result 1 "Errors found in API logs"
    echo -e "${YELLOW}[INFO]${NC} API log errors:"
    echo "$API_LOGS"
fi

section "STAGE 10: SUMMARY AND RECOMMENDATIONS"

echo -e "\n${YELLOW}Based on the test results, here are the potential issues:${NC}"

# Check for X11 server issues
if ! docker exec $CONTAINER ps aux | grep Xvfb | grep -v grep > /dev/null; then
    echo -e "${RED}1. X11 server issue: Xvfb is not running${NC}"
    echo -e "   Fix: Check supervisord configuration for Xvfb and restart the service"
fi

# Check for VNC server issues
if ! docker exec $CONTAINER ps aux | grep x11vnc | grep -v grep > /dev/null; then
    echo -e "${RED}2. VNC server issue: x11vnc is not running${NC}"
    echo -e "   Fix: Check supervisord configuration for x11vnc and restart the service"
fi

# Check for Firefox issues
if ! docker exec $CONTAINER ps aux | grep firefox | grep -v grep > /dev/null; then
    echo -e "${RED}3. Browser issue: Firefox is not running${NC}"
    echo -e "   Fix: Check supervisord configuration for Firefox and restart the service"
fi

# Check for WebSocket proxy issues
if ! docker exec $CONTAINER ps aux | grep websockify | grep -v grep > /dev/null; then
    echo -e "${RED}4. WebSocket proxy issue: websockify is not running${NC}"
    echo -e "   Fix: Check supervisord configuration for websockify and restart the service"
fi

# Check for port mismatch
if [[ -n "$VNC_PORT" && "$VNC_PORT" == *"5901"* && $(docker exec $CONTAINER bash -c 'netstat -tuln | grep 5901' 2>/dev/null) == "" ]]; then
    echo -e "${RED}5. Port mismatch: app.py is using port 5901, but VNC server is running on port 5900${NC}"
    echo -e "   Fix: Update app.py to use port 5900 or reconfigure VNC server to use port 5901"
fi

# Check for WebSocket connection issues
if ! nc -z localhost 6080 > /dev/null 2>&1; then
    echo -e "${RED}6. WebSocket connection issue: Cannot connect to WebSocket port 6080${NC}"
    echo -e "   Fix: Check Docker port mappings and make sure port 6080 is properly mapped"
fi

# Check for nginx configuration issues
if ! docker exec $CONTAINER bash -c 'grep -r "proxy_set_header Connection" /etc/nginx' | grep -q "upgrade"; then
    echo -e "${RED}7. Nginx WebSocket configuration issue: Missing or incorrect WebSocket headers${NC}"
    echo -e "   Fix: Update nginx configuration to properly handle WebSocket connections"
fi

# Check for nginx WebSocket proxy path issues
if ! docker exec $CONTAINER bash -c 'grep -r "location /websockify" /etc/nginx' > /dev/null 2>&1; then
    echo -e "${RED}8. Nginx WebSocket proxy path issue: Missing location block for /websockify${NC}"
    echo -e "   Fix: Add a location block for /websockify in nginx configuration"
fi

# Check for noVNC HTML issues
if ! docker exec $CONTAINER test -f /tmp/noVNC/vnc.html; then
    echo -e "${RED}9. noVNC HTML issue: vnc.html file is missing${NC}"
    echo -e "   Fix: Check noVNC installation and make sure vnc.html exists"
fi

echo -e "\n${YELLOW}Recommended actions:${NC}"
echo -e "1. Check the supervisord configuration to ensure all services are properly configured"
echo -e "2. Verify that the WebSocket proxy (websockify) is correctly configured to proxy from port 6080 to 5900"
echo -e "3. Check the nginx configuration for proper WebSocket handling"
echo -e "4. Ensure that all required ports are properly mapped in Docker"
echo -e "5. Check the noVNC installation and make sure all required files exist"
echo -e "6. Check the logs for specific error messages"
echo -e "7. Restart the container after making changes: docker restart $CONTAINER"

echo -e "\n${YELLOW}Testing WebSocket connections:${NC}"
echo -e "1. Open http://localhost:8001/test.html to test WebSocket connections"
echo -e "2. Open http://localhost:8001/direct_novnc.html to test direct noVNC connection"
echo -e "3. Open http://localhost:8001/vnc/vnc.html?host=localhost&port=6080&autoconnect=true to test proxied noVNC connection"
echo -e "4. Open http://localhost:6080/vnc.html?autoconnect=true to test direct noVNC connection"

echo -e "\n${YELLOW}Connection flow:${NC}"
echo -e "Browser Request → Nginx → noVNC HTML/JS → WebSocket Connection → WebSocket Proxy → VNC Server → X11/Firefox"
echo -e "Each component in this chain must be working correctly for the noVNC connection to succeed."
