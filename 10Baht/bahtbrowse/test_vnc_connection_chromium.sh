#!/bin/bash

# Comprehensive test script for BahtBrowse VNC connection issues
# Run this script to diagnose issues with the noVNC connection for Ubuntu Chromium

# Set container name
CONTAINER="bahtbrowse-browser-chromium-ubuntu"

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
section() {
    echo -e "\n${BLUE}========== $1 ==========${NC}\n"
}

# Function to print test results
result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[PASS]${NC} $2"
    else
        echo -e "${RED}[FAIL]${NC} $2"
    fi
}

# Function to print info messages
info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Start testing
section "BASIC CONTAINER TESTS"

# Test 1: Check if container is running
info "Test 1: Checking if container is running..."
docker ps | grep $CONTAINER > /dev/null
result $? "Container $CONTAINER is running"

# Test 2: Check container's network connectivity
info "Test 2: Checking container's network connectivity..."
docker exec $CONTAINER ping -c 1 8.8.8.8 > /dev/null 2>&1
result $? "Container has network connectivity"

section "PROCESS TESTS"

# Test 3: Check if X server is running
info "Test 3: Checking if X server is running..."
docker exec $CONTAINER ps aux | grep Xvfb | grep -v grep > /dev/null
result $? "X server (Xvfb) is running"

# Test 4: Check if VNC server is running
info "Test 4: Checking if VNC server is running..."
docker exec $CONTAINER ps aux | grep x11vnc | grep -v grep > /dev/null
result $? "VNC server (x11vnc) is running"

# Test 5: Check if websockify is running
info "Test 5: Checking if websockify is running..."
docker exec $CONTAINER ps aux | grep websockify | grep -v grep > /dev/null
result $? "WebSocket proxy (websockify) is running"

# Test 6: Check if nginx is running
info "Test 6: Checking if nginx is running..."
docker exec $CONTAINER ps aux | grep nginx | grep -v grep > /dev/null
result $? "Nginx is running"

# Test 7: Check if Chromium is running
info "Test 7: Checking if Chromium is running..."
docker exec $CONTAINER ps aux | grep chromium | grep -v grep > /dev/null
result $? "Chromium is running"

section "PORT TESTS"

# Test 8: Check if port 5900 (VNC) is listening inside container
info "Test 8: Checking if port 5900 (VNC) is listening inside container..."
docker exec $CONTAINER bash -c 'netstat -tuln | grep 5900' > /dev/null 2>&1
result $? "Port 5900 (VNC) is listening inside container"

# Test 9: Check if port 6080 (WebSocket) is listening inside container
info "Test 9: Checking if port 6080 (WebSocket) is listening inside container..."
docker exec $CONTAINER bash -c 'netstat -tuln | grep 6080' > /dev/null 2>&1
result $? "Port 6080 (WebSocket) is listening inside container"

# Test 10: Check if port 80 (HTTP) is listening inside container
info "Test 10: Checking if port 80 (HTTP) is listening inside container..."
docker exec $CONTAINER bash -c 'netstat -tuln | grep 80' > /dev/null 2>&1
result $? "Port 80 (HTTP) is listening inside container"

# Test 11: Check if port 5900 is mapped to host
info "Test 11: Checking if port 5900 is mapped to host..."
docker port $CONTAINER | grep '5900/tcp' > /dev/null
result $? "Port 5900 is mapped to host"

# Test 12: Check if port 6083 is mapped to container's port 6080
info "Test 12: Checking if port 6083 is mapped to container's port 6080..."
docker port $CONTAINER | grep '6080/tcp' > /dev/null
result $? "Port 6083 is mapped to container's port 6080"

# Test 13: Check if port 8003 is mapped to container's port 80
info "Test 13: Checking if port 8003 is mapped to container's port 80..."
docker port $CONTAINER | grep '80/tcp' > /dev/null
result $? "Port 8003 is mapped to container's port 80"

section "CONNECTION TESTS"

# Test 14: Test connection to nginx server
info "Test 14: Testing connection to nginx server..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:8003 | grep -E "200|301|302" > /dev/null
result $? "Nginx server is accessible"

# Test 15: Test WebSocket connection
info "Test 15: Testing WebSocket connection..."
curl -s -I http://localhost:6083 > /dev/null
result $? "WebSocket endpoint is reachable"

section "FILE TESTS"

# Test 16: Check if noVNC files exist
info "Test 16: Checking if noVNC files exist..."
docker exec $CONTAINER ls -la /usr/share/novnc/vnc.html > /dev/null 2>&1
result $? "noVNC files exist"

# Test 17: Check if vnc_lite.html exists
info "Test 17: Checking if vnc_lite.html exists..."
docker exec $CONTAINER ls -la /usr/share/novnc/vnc_lite.html > /dev/null 2>&1
result $? "vnc_lite.html exists"

section "NGINX CONFIGURATION TEST"

# Test 18: Check nginx configuration
info "Test 18: Checking nginx configuration..."
docker exec $CONTAINER bash -c 'nginx -t 2>&1 | grep "successful"' > /dev/null
result $? "Nginx configuration is valid"

# Test 19: Check nginx WebSocket proxy configuration
info "Test 19: Checking nginx WebSocket proxy configuration..."
docker exec $CONTAINER bash -c 'grep -r "proxy_http_version 1.1" /etc/nginx' > /dev/null 2>&1
result $? "Nginx WebSocket proxy is properly configured"

# Test 20: Check nginx WebSocket upgrade headers
info "Test 20: Checking nginx WebSocket upgrade headers..."
docker exec $CONTAINER bash -c 'grep -r "proxy_set_header Upgrade" /etc/nginx' > /dev/null 2>&1
result $? "Nginx is configured with WebSocket upgrade headers"

# Test 21: Check nginx WebSocket connection header
info "Test 21: Checking nginx WebSocket connection header..."
docker exec $CONTAINER bash -c 'grep -r "proxy_set_header Connection \"upgrade\"" /etc/nginx' > /dev/null 2>&1
result $? "Nginx is configured with WebSocket connection header"

section "VNC CONNECTION TESTS"

# Test 22: Test direct VNC connection
info "Test 22: Testing direct VNC connection to port 5900..."
docker exec $CONTAINER bash -c 'nc -z localhost 5900' > /dev/null 2>&1
result $? "Direct VNC connection to port 5900 is possible"

# Test 23: Test WebSocket to VNC connection
info "Test 23: Testing WebSocket to VNC connection..."
docker exec $CONTAINER bash -c 'nc -z localhost 6080' > /dev/null 2>&1
result $? "WebSocket to VNC connection is possible"

section "API TESTS"

# Test 24: Test API endpoint
info "Test 24: Testing API endpoint..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:8086/browse/test-connection | grep 200 > /dev/null
result $? "API endpoint is accessible"

# Test 25: Test API redirect
info "Test 25: Testing API redirect..."
REDIRECT_URL=$(curl -s -I -L http://localhost:8086/browse/?url=https://example.com | grep -i "^location:" | head -1 | sed 's/^[Ll]ocation: //g' | tr -d '\r')
info "Redirect URL: $REDIRECT_URL"
if [[ -n "$REDIRECT_URL" ]]; then
    result 0 "API redirects to a URL"
else
    result 1 "API does not redirect"
fi

section "SUMMARY"

echo -e "\n${YELLOW}If any tests failed, they need to be addressed to fix the VNC connection issue.${NC}"
echo -e "${YELLOW}Pay special attention to the X server, VNC server, and WebSocket proxy tests.${NC}"
echo -e "${YELLOW}Check the logs for specific error messages that might indicate the root cause.${NC}\n"

# Additional manual tests that can't be automated
echo -e "${BLUE}MANUAL TESTS TO PERFORM:${NC}"
echo -e "1. Open http://localhost:8086/browse/?url=https://example.com in your browser"
echo -e "2. Check browser console for JavaScript errors"
echo -e "3. Try connecting with a VNC client directly to localhost:5900"
echo -e "4. Check if the container has enough resources (CPU, memory)"
echo -e "5. Try restarting the container with: docker restart $CONTAINER"
