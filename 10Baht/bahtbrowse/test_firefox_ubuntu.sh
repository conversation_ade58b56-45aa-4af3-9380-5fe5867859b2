#!/bin/bash

# Simple test script for Firefox Ubuntu container
# Created based on project structure analysis

set -e
echo "Starting Firefox Ubuntu container tests..."

# Clean up any existing containers
echo "Cleaning up existing containers..."
docker compose -f docker-compose.firefox-ubuntu.yml down

# Build the container
echo "Building Firefox Ubuntu container..."
docker compose -f docker-compose.firefox-ubuntu.yml build --no-cache

# Start the container
echo "Starting Firefox Ubuntu container..."
docker compose -f docker-compose.firefox-ubuntu.yml up -d

# Wait for container to initialize
echo "Waiting for container to initialize..."
sleep 10

# Check if container is running
echo "Checking container status..."
CONTAINER_ID=$(docker compose -f docker-compose.firefox-ubuntu.yml ps -q)
if [ -z "$CONTAINER_ID" ]; then
  echo "ERROR: Container not running!"
  exit 1
fi

echo "Container is running with ID: $CONTAINER_ID"

# Check for essential processes
echo "Checking for required processes..."

# Check Firefox
docker exec $CONTAINER_ID ps aux | grep -v grep | grep firefox
if [ $? -ne 0 ]; then
  echo "ERROR: Firefox is not running!"
  exit 1
fi

# Check VNC
docker exec $CONTAINER_ID ps aux | grep -v grep | grep vnc
if [ $? -ne 0 ]; then
  echo "ERROR: VNC server is not running!"
  exit 1
fi

# Check Nginx
docker exec $CONTAINER_ID ps aux | grep -v grep | grep nginx
if [ $? -ne 0 ]; then
  echo "ERROR: Nginx is not running!"
  exit 1
fi

# Test port accessibility
echo "Testing port accessibility..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:6080 | grep 200
if [ $? -ne 0 ]; then
  echo "ERROR: Port 6080 is not accessible!"
  exit 1
fi

# Clean up
echo "Tests passed! Cleaning up..."
docker compose -f docker-compose.firefox-ubuntu.yml down

echo "Firefox Ubuntu container tests completed successfully!"
