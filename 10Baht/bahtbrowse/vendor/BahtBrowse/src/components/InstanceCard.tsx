'use client';

import {<PERSON>, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Edit, Play, Square, Trash, Loader2} from 'lucide-react';
import {useState, useEffect} from 'react';
import {startInstance, stopInstance} from '@/app/actions';
import {useToast} from '@/hooks/use-toast';
import { Instance, InstanceStatus } from '@/app/page';

interface InstanceCardProps extends Omit<Instance, 'status'> {
  status: InstanceStatus;
}

const InstanceCard: React.FC<InstanceCardProps> = ({id, name, status, description, instanceUrl}) => {
  const [currentStatus, setCurrentStatus] = useState<InstanceStatus>(status);
  const {toast} = useToast();

  useEffect(() => {
    console.log(`[UI] InstanceCard ${id} mounted. Initial props:`, { id, name, status, description, instanceUrl });
  }, [id, name, status, description, instanceUrl]);

  const handleStart = async () => {
    console.log(`[UI] InstanceCard ${id}: handleStart called for URL ${instanceUrl}. Current visual status: ${currentStatus}`);
    const originalStatus = currentStatus;
    setCurrentStatus('starting');
    try {
      console.log(`[UI] InstanceCard ${id}: Calling startInstance action with URL ${instanceUrl}.`);
      const result = await startInstance(id, instanceUrl);
      console.log(`[UI] InstanceCard ${id}: startInstance action returned:`, result);
      if (result.success) {
        setCurrentStatus('running');
        toast({
          title: 'Instance Started',
          description: `Instance ${name} has been started.`,
        });
        console.log(`[UI] InstanceCard ${id}: Status set to running.`);
      } else {
        setCurrentStatus('stopped');
        toast({
          variant: 'destructive',
          title: 'Error Starting Instance',
          description: `Failed to start instance ${name}: ${result.message || 'Unknown error'}`,
        });
        console.error(`[UI] InstanceCard ${id}: Start failed, reverting status to stopped. Message: ${result.message}`);
      }
    } catch (error: any) {
      console.error(`[UI] InstanceCard ${id}: Exception during startInstance call:`, error);
      setCurrentStatus('stopped');
      toast({
        variant: 'destructive',
        title: 'Error Starting Instance',
        description: `Failed to start instance ${name}: ${error.message || 'Unknown error'}`,
      });
    }
  };

  const handleStop = async () => {
    console.log(`[UI] InstanceCard ${id}: handleStop called. Current visual status: ${currentStatus}`);
    const originalStatus = currentStatus;
    setCurrentStatus('stopping');
    try {
      console.log(`[UI] InstanceCard ${id}: Calling stopInstance action.`);
      const result = await stopInstance(id);
      console.log(`[UI] InstanceCard ${id}: stopInstance action returned:`, result);
      if (result.success) {
        setCurrentStatus('stopped');
        toast({
          title: 'Instance Stopped',
          description: `Instance ${name} has been stopped.`,
        });
        console.log(`[UI] InstanceCard ${id}: Status set to stopped.`);
      } else {
        setCurrentStatus('running');
        toast({
          variant: 'destructive',
          title: 'Error Stopping Instance',
          description: `Failed to stop instance ${name}: ${result.message || 'Unknown error'}`,
        });
        console.error(`[UI] InstanceCard ${id}: Stop failed, reverting status to running. Message: ${result.message}`);
      }
    } catch (error: any) {
      console.error(`[UI] InstanceCard ${id}: Exception during stopInstance call:`, error);
      setCurrentStatus('running');
      toast({
        variant: 'destructive',
        title: 'Error Stopping Instance',
        description: `Failed to stop instance ${name}: ${error.message || 'Unknown error'}`,
      });
    }
  };

  return (
    <Card className="w-full max-w-sm transform perspective-[800px] rotate-x-[-1deg] hover:rotate-x-0 transition-transform duration-300">
      <CardHeader>
        <CardTitle>{name}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <p>Status: {currentStatus}</p>
        {currentStatus === 'running' ? (
          <p>
            URL:{' '}
            <a href={instanceUrl} target="_blank" rel="noopener noreferrer">
              {instanceUrl}
            </a>
          </p>
        ) : null}
        <div className="flex justify-between">
          {(currentStatus === 'running' || currentStatus === 'stopping') ? (
            <Button variant="outline" size="icon" disabled={currentStatus === 'stopping'} onClick={handleStop}>
              {currentStatus === 'stopping' ? <Loader2 className="h-4 w-4 animate-spin"/> :
                <Square className="h-4 w-4"/>}
            </Button>
          ) : (
            currentStatus === 'stopped' || currentStatus === 'starting' ? (
              <Button variant="outline" size="icon" disabled={currentStatus === 'starting'} onClick={handleStart}>
                {currentStatus === 'starting' ? <Loader2 className="h-4 w-4 animate-spin"/> :
                  <Play className="h-4 w-4"/>}
              </Button>
            ) : null
          )}
          <Button variant="outline" size="icon">
            <Edit className="h-4 w-4"/>
          </Button>
          <Button variant="destructive" size="icon">
            <Trash className="h-4 w-4"/>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default InstanceCard;
