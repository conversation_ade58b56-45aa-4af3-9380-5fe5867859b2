'use client'; // Needed for hooks and event handlers

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import {Home, Plus, Settings, PowerOff, RefreshCw, Loader2} from 'lucide-react'; // Import icons
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/dropdown-menu';
import {Button} from '@/components/ui/button';
import { stopAllInstances } from '@/app/actions'; // Import stopAllInstances action
import { useToast } from '@/hooks/use-toast';
import { useState } from 'react';

// Define props for Sidebar
interface SidebarComponentProps {
  instanceCount: number;
  refreshInstances: () => Promise<void>; // Function to trigger status refresh
}

const SidebarComponent: React.FC<SidebarComponentProps> = ({ instanceCount, refreshInstances }) => {
  const { toast } = useToast();
  const [isStoppingAll, setIsStoppingAll] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleStopAll = async () => {
    console.log('[UI Sidebar] Stop All clicked.');
    setIsStoppingAll(true);
    try {
      const result = await stopAllInstances();
      console.log('[UI Sidebar] stopAllInstances result:', result);
      toast({
        title: result.success ? 'Instances Stopped' : 'Error Stopping Instances',
        description: result.message,
        variant: result.success ? 'default' : 'destructive',
      });
      // Refresh the instance list after stopping
      await handleRefresh();
    } catch (error: any) {
      console.error('[UI Sidebar] Error calling stopAllInstances:', error);
      toast({ variant: 'destructive', title: 'Error', description: `Failed to stop instances: ${error.message}` });
    } finally {
      setIsStoppingAll(false);
    }
  };

  const handleRefresh = async () => {
      console.log('[UI Sidebar] Refresh clicked.');
      setIsRefreshing(true);
      try {
          await refreshInstances();
          toast({ title: 'Refreshed', description: 'Instance statuses updated.' });
      } catch (error) {
          console.error('[UI Sidebar] Error calling refreshInstances:', error);
          toast({ variant: 'destructive', title: 'Error', description: 'Failed to refresh instance statuses.' });
      } finally {
          setIsRefreshing(false);
      }
  };

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <SidebarTrigger/>
      </SidebarHeader>
      <SidebarContent>
        {/* Instance Count Display */}
        <div className="p-2 text-center text-xs font-semibold text-muted-foreground">
          INSTANCES: {instanceCount}
        </div>
        <SidebarMenu>
          {/* Refresh Button */}
          <SidebarMenuItem>
            <SidebarMenuButton asChild onClick={handleRefresh} disabled={isRefreshing}>
              <Button variant="ghost" className="justify-start w-full">
                {isRefreshing ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <RefreshCw className="mr-2 h-4 w-4"/>}
                <span>Refresh Status</span>
              </Button>
            </SidebarMenuButton>
          </SidebarMenuItem>

          {/* Stop All Button */}
          <SidebarMenuItem>
            <SidebarMenuButton asChild onClick={handleStopAll} disabled={isStoppingAll}>
              <Button variant="destructive" className="justify-start w-full">
                 {isStoppingAll ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <PowerOff className="mr-2 h-4 w-4"/>}
                <span>Stop All</span>
              </Button>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Button variant="ghost" className="justify-start">
                <Home className="mr-2 h-4 w-4"/>
                <span>Home</span>
              </Button>
            </SidebarMenuButton>
          </SidebarMenuItem>
          {/* Removed Create Instance button here as it's now a card in the grid */}
          {/* <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Button variant="ghost" className="justify-start">
                <Plus className="mr-2 h-4 w-4"/>
                <span>Create Instance</span>
              </Button>
            </SidebarMenuButton>
          </SidebarMenuItem> */}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-start px-2">
              <Avatar className="mr-2 h-5 w-5">
                <AvatarImage src="https://picsum.photos/50/50" alt="Avatar"/>
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
              <span>Clay Nuñez</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" forceMount className="w-48">
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4"/>
              <span>Settings</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarFooter>
    </Sidebar>
  );
};

export default SidebarComponent;
