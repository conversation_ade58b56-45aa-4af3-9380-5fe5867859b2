'use client';

import InstanceCard from '@/components/InstanceCard';
import { PlusCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Instance } from '@/app/page';

interface InstanceGridProps {
  instances: Instance[];
}

const InstanceGrid: React.FC<InstanceGridProps> = ({ instances }) => {
  const handleCreateClick = () => {
    console.log('[UI Grid] Create instance card clicked.');
    alert('Create instance functionality not yet implemented.');
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      <Card
        className="w-full max-w-sm flex flex-col items-center justify-center border-dashed border-2 hover:border-primary hover:cursor-pointer transition-colors min-h-[200px]"
        onClick={handleCreateClick}
      >
        <CardHeader className="items-center">
          <CardTitle className="text-lg">Create New Instance</CardTitle>
        </CardHeader>
        <CardContent>
          <PlusCircle className="h-12 w-12 text-muted-foreground" />
        </CardContent>
      </Card>

      {instances.map((instance) => (
        <InstanceCard
          key={instance.id}
          id={instance.id}
          name={instance.name}
          status={instance.status}
          description={instance.description}
          instanceUrl={instance.instanceUrl}
        />
      ))}
    </div>
  );
};

export default InstanceGrid;
