#!/bin/bash
set -e

# Create a directory for coverage reports
mkdir -p coverage_reports

# Run all tests with coverage
docker run --rm -it \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v $(pwd):/app \
  -v $(pwd)/coverage_reports:/app/coverage_reports \
  bahtbrowse-api \
  bash -c "cd /app && \
    pip install pytest-cov jsonschema selenium requests && \
    python -m pytest \
      --cov=api \
      --cov-report=term \
      --cov-report=html:/app/coverage_reports/html \
      --cov-report=xml:/app/coverage_reports/coverage.xml \
      tests/api/test_fastapi_app.py \
      tests/api/test_error_paths.py \
      tests/api/test_improved_mocking.py \
      tests/api/test_container_lifecycle.py \
      tests/api/test_port_management.py \
      tests/api/test_config_handling.py \
      -v"

echo "Coverage reports generated in coverage_reports directory"
