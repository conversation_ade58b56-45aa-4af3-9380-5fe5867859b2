.PHONY: help lint format typecheck quality-report complexity-report security-scan quality-check test test-api test-e2e coverage build run clean

# Default target
help:
	@echo "BahtBrowse Makefile"
	@echo ""
	@echo "Usage:"
	@echo "  make lint             Run ruff linter on all Python files"
	@echo "  make format           Run ruff formatter on all Python files"
	@echo "  make typecheck        Run mypy type checker on all Python files"
	@echo "  make quality-report   Generate a code quality report"
	@echo "  make complexity-report Analyse code complexity"
	@echo "  make security-scan    Scan for security vulnerabilities"
	@echo "  make quality-check    Run all code quality checks"
	@echo "  make test             Run all tests (API and E2E)"
	@echo "  make test-api         Run API tests"
	@echo "  make test-e2e         Run E2E tests"
	@echo "  make coverage         Run tests with coverage reporting"
	@echo "  make build            Build Docker images"
	@echo "  make run              Run the application (API and UI)"
	@echo "  make clean            Clean up temporary files and Docker containers"
	@echo ""

# Lint Python code
lint:
	@echo "Running ruff linter..."
	ruff check .

# Format Python code
format:
	@echo "Running ruff formatter..."
	ruff format .

# Run type checking
typecheck:
	@echo "Running mypy type checker..."
	mypy api/ scripts/

# Generate code quality report
quality-report:
	@echo "Generating code quality report..."
	python scripts/generate_code_quality_report.py

# Analyse code complexity
complexity-report:
	@echo "Analysing code complexity..."
	python scripts/analyze_complexity.py

# Scan for security vulnerabilities
security-scan:
	@echo "Scanning for security vulnerabilities..."
	python scripts/security_scan.py

# Run all code quality checks
quality-check: lint format typecheck quality-report complexity-report security-scan
	@echo "All code quality checks completed!"

# Run all tests
test: test-api test-e2e

# Run tests with coverage
coverage:
	@echo "Running tests with coverage..."
	docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock -v "$(CURDIR)/coverage_reports:/app/coverage_reports" bahtbrowse-api python3 -m pytest --cov=api --cov-report=html:coverage_reports/html --cov-report=term tests/api/

# Run API tests
test-api:
	@echo "Running API tests..."
	docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/

# Run E2E tests
test-e2e:
	@echo "Running E2E tests..."
	docker run --rm -it \
	  --network host \
	  -v "$(CURDIR)/e2e:/app/e2e:ro" \
	  -v "$(CURDIR)/src:/app/src:ro" \
	  -v "$(CURDIR)/components:/app/components:ro" \
	  -v "$(CURDIR)/hooks:/app/hooks:ro" \
	  -v "$(CURDIR)/lib:/app/lib:ro" \
	  -v "$(CURDIR)/ai:/app/ai:ro" \
	  -v "$(CURDIR)/styles:/app/styles:ro" \
	  -v "$(CURDIR)/playwright.config.ts:/app/playwright.config.ts:ro" \
	  -v "$(CURDIR)/tsconfig.json:/app/tsconfig.json:ro" \
	  -v "$(CURDIR)/tailwind.config.ts:/app/tailwind.config.ts:ro" \
	  -v "$(CURDIR)/postcss.config.mjs:/app/postcss.config.mjs:ro" \
	  -v "$(CURDIR)/next.config.mjs:/app/next.config.mjs:ro" \
	  -v "$(CURDIR)/.env:/app/.env:ro" \
	  -w /app \
	  bahtbrowse-playwright \
	  npx playwright test

# Build Docker images
build:
	@echo "Building Docker images..."
	docker build -t alpine-chromium-novnc:latest .
	docker build -t bahtbrowse-api -f api/Dockerfile.test .
	docker build -t bahtbrowse-playwright -f Dockerfile.playwright .

# Run the application
run:
	@echo "Starting the application..."
	@echo "Starting API server..."
	@uvicorn api.main:app --reload --port 8000 &
	@echo "Starting Next.js frontend..."
	@npm run dev

# Clean up
clean:
	@echo "Cleaning up..."
	@find . -type d -name "__pycache__" -exec rm -rf {} +
	@find . -type d -name ".pytest_cache" -exec rm -rf {} +
	@find . -type d -name ".ruff_cache" -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete
	@find . -type f -name "*.pyo" -delete
	@find . -type f -name "*.pyd" -delete
	@find . -type f -name ".coverage" -delete
	@find . -type d -name "htmlcov" -exec rm -rf {} +
	@find . -type d -name "coverage_reports" -exec rm -rf {} +
	@docker ps -a | grep "bahtbrowser-" | awk '{print $1}' | xargs -r docker rm -f
