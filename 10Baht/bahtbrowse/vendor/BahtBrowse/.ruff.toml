# Ignore specific rules for existing files
[lint]
ignore = [
    "D100",  # Missing docstring in public module
    "D101",  # Missing docstring in public class
    "D103",  # Missing docstring in public function
    "D205",  # 1 blank line required between summary line and description
    "ANN101", # Missing type annotation for `self` in method
    "ANN102", # Missing type annotation for `cls` in classmethod
    "ANN201", # Missing return type annotation for public function
    "E501",  # Line too long
    "G004",  # Logging statement uses f-string
    "G201",  # Logging `.exception(...)` should be used instead of `.error(..., exc_info=True)`
    "B904",  # Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None`
    "TRY300", # Consider moving this statement to an `else` block
    "TRY301", # Abstract `raise` to an inner function
    "TRY401", # Redundant exception object included in `logging.exception` call
    "RET504", # Unnecessary assignment before `return` statement
    "RET505", # Unnecessary `elif` after `return` statement
    "RET506", # Unnecessary `else` after `raise` statement
    "PLR0912", # Too many branches
    "PLR0915", # Too many statements
    "PTH123", # `open()` should be replaced by `Path.open()`
    "PTH118", # `os.path.join()` should be replaced by `Path` with `/` operator
    "PT003",  # `scope='function'` is implied in `@pytest.fixture()`
    "PT004",  # Fixture does not return anything, add leading underscore
    "PT018",  # Assertion should be broken down into multiple parts
    "BLE001", # Do not catch blind exception: `Exception`
    "S311",   # Standard pseudo-random generators are not suitable for cryptographic purposes
    "S102",   # Use of `exec` detected
    "F841",   # Local variable is assigned to but never used
    "F821",   # Undefined name
    "F401",   # Module imported but unused
    "N815",   # Variable in class scope should not be mixedCase
    "ARG001", # Unused function argument
    "ARG005", # Unused lambda argument
    "PERF203", # `try`-`except` within a loop incurs performance overhead
    "PERF401", # Use a list comprehension to create a transformed list
    "SIM117", # Use a single `with` statement with multiple contexts instead of nested `with` statements
    "UP038",  # Use `X | Y` in `isinstance` call instead of `(X, Y)`
    "PIE810", # Call `endswith` once with a `tuple`
    "INP001", # File is part of an implicit namespace package. Add an `__init__.py`
    "ERA001", # Found commented-out code
    "EM101",  # Exception must not use a string literal, assign to variable first
    "PLW2901", # `for` loop variable overwritten by assignment target
    "S113",   # Probable use of requests call without timeout
    "T201",   # `print` found
]

# Exclude test files from linting
exclude = [
    "tests/",
    "e2e/",
]
