# Changelog

All notable changes to the BahtBrowse project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Ruff pre-commit hook for code quality enforcement
- GitHub Actions workflow for automated code quality checks
- Makefile for common development tasks
- CONTRIBUTING.md with detailed guidelines
- Code quality documentation in README.md
- Integration tests following PEP 8, PEP 257, and PEP 484 standards
- Advanced code quality tools:
  - mypy for static type checking
  - bandit for security scanning
  - radon for complexity analysis
  - coverage reporting with HTML reports
- Comprehensive quality-check command
- Code quality dashboard for tracking metrics
- <PERSON><PERSON>per onboarding documentation
- Code quality meeting template
- Pull request template with code quality checklist
- Scripts for generating code quality reports

### Changed
- Improved error handling in API endpoints
- Better Docker client error handling
- Updated README.md with code quality section

### Fixed
- Fixed issues with Path object mocking in tests
- Improved JSON error handling in configuration loading

## [0.2.0] - 2025-04-25

### Added
- Comprehensive test suite for API functionality
  - Error path testing for robust error handling
  - Improved mocking techniques for Docker, filesystem, and network operations
  - Container lifecycle testing (create, start, stop, delete)
  - Port management testing for availability and selection
  - Configuration handling testing
- Status endpoint implementation for checking instance status
- Test coverage reporting
- Script for running all tests with coverage

## [0.1.0] - 2023-04-23

### Added
- Initial release of BahtBrowse
- Basic API for managing browser instances
- Docker container management
- Instance configuration handling
