# BahtBrowse Controller & UI

This project provides a web interface (Next.js) and a backend API (FastAPI) to manage Dockerised browser instances (`alpine-chromium-novnc`).

[![Code Quality](https://github.com/forkrul/BahtBrowse/actions/workflows/code-quality.yml/badge.svg)](https://github.com/forkrul/BahtBrowse/actions/workflows/code-quality.yml)

## Architecture Overview

The system consists of:

1.  **Frontend:** A Next.js application responsible for displaying the user interface, managing UI state, and communicating with the backend API. It runs directly on the host machine.
2.  **Backend API:** A FastAPI application responsible for handling requests from the frontend, interacting with the Docker daemon (via the Docker SDK) to manage containers, and managing instance configurations (currently via `instances.json`). It also runs directly on the host machine.
3.  **Instance Definitions:** A simple `instances.json` file stores the configuration for predefined browser instances.
4.  **Docker Daemon:** The host machine's Docker daemon, which actually creates, starts, stops, and manages the browser containers.
5.  **Browser Containers:** Individual Docker containers based on the `alpine-chromium-novnc:latest` image (built from `Dockerfile`), each running a Chromium browser accessible via noVNC on a specific port.
6.  **Development Environment:** Managed by Nix using `shell.nix` to provide consistent versions of Node.js, Python, FastAPI dependencies, Docker SDK, and the Docker CLI.

```mermaid
graph TD
    User[User] --> Browser[Web Browser];
    Browser --> NextUI[Next.js Frontend (localhost:9002)];
    NextUI -->|HTTP API Calls| FastAPI[FastAPI Backend (localhost:8000)];
    FastAPI -->|Reads| InstancesJSON(instances.json);
    FastAPI -->|Docker SDK| DockerDaemon[Docker Daemon on Host];
    DockerDaemon -->|Manages| Instance1[Browser Container 1<br/>Port: 6080];
    DockerDaemon -->|Manages| Instance2[Browser Container 2<br/>Port: 6081];
    DockerDaemon -->|Manages| Instance3[Browser Container 3<br/>Port: 6082];
    Browser -->|noVNC Access| Instance1;
    Browser -->|noVNC Access| Instance2;
    Browser -->|noVNC Access| Instance3;

    subgraph Containers
        Instance1
        Instance2
        Instance3
    end

    subgraph Host Machine
        NextUI
        FastAPI
        DockerDaemon
        InstancesJSON
    end
```

## Development Setup

1.  **Nix:** Ensure you have Nix installed ([https://nixos.org/download.html](https://nixos.org/download.html)).
2.  **Enter Environment:** Navigate to the project root directory and run:
    ```bash
    nix-shell
    ```
    This command reads the `shell.nix` file, downloads/configures the specified dependencies (Python with FastAPI/Docker SDK, Node.js, Docker CLI), and puts you into a shell where these tools are available.
3.  **Set up pre-commit hooks:** Install and configure pre-commit hooks for code quality:
    ```bash
    pip install pre-commit ruff
    pre-commit install
    ```
4.  **Verify setup:** Run the pre-commit hooks on all files to verify the setup:
    ```bash
    pre-commit run --all-files
    ```

For more details on code quality standards and tools, see the [Code Quality](#code-quality) section below.

## Running the Application

You can run the application using the Makefile or manually:

### Using the Makefile

```bash
# Build all Docker images
make build

# Run the application (API and UI)
make run

# Run linter
make lint

# Run formatter
make format

# Run tests
make test
```

Run `make help` to see all available commands.

### Manual Setup

You need to run two separate processes:

1.  **FastAPI Backend API:**
    *   Open a terminal, navigate to the project root, and enter the Nix environment (`nix-shell`).
    *   Run the FastAPI server using Uvicorn:
        ```bash
        uvicorn api.main:app --reload --port 8000
        ```
    *   The API will be available at `http://localhost:8000`.

2.  **Next.js Frontend:**
    *   Open a *second* terminal, navigate to the project root, and enter the Nix environment (`nix-shell`).
    *   Run the Next.js development server:
        ```bash
        npm run dev
        ```
    *   The UI will be available at `http://localhost:9002`.

## Building the Browser Instance Image

Before the API can start instances, the Docker image needs to be built using the main `Dockerfile`:

```bash
docker build -t alpine-chromium-novnc:latest .
```

## Testing

### API Integration Tests (Python/Pytest)

These tests interact with the FastAPI API endpoints and verify the resulting Docker container states by calling the Docker daemon on the host.

1.  **Prerequisites:**
    *   Build the API+Test Docker image:
        ```bash
        docker build -t bahtbrowse-api -f api/Dockerfile.test .
        ```
    *   Ensure the `alpine-chromium-novnc:latest` image is built (`docker build -t alpine-chromium-novnc:latest .`).
    *   Ensure the Docker daemon is running.
2.  **Run Tests:**
    *   Execute the basic API tests inside the `bahtbrowse-api` container, mounting the Docker socket:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_fastapi_app.py
        ```
    *   Run comprehensive error path tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_error_paths.py
        ```
    *   Run improved mocking tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_improved_mocking.py
        ```
    *   Run container lifecycle tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_container_lifecycle.py
        ```
    *   Run port management tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_port_management.py
        ```
    *   Run configuration handling tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_config_handling.py
        ```
    *   Run error handling tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_error_handling.py
        ```
    *   Run concurrent operations tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_concurrent_operations.py
        ```
    *   Run performance tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_performance.py
        ```
    *   Run persistence tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_persistence.py
        ```
    *   Run security tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_security.py
        ```
    *   Run API validation tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_api_validation.py
        ```
    *   Run browser functionality tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock -e RUN_BROWSER_TESTS=true bahtbrowse-api python3 -m pytest -v tests/api/test_browser_functionality.py
        ```
    *   Run recovery tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock -e RUN_RECOVERY_TESTS=true bahtbrowse-api python3 -m pytest -v tests/api/test_recovery.py
        ```
    *   Run boundary tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/test_boundary.py
        ```
    *   Run all tests:
        ```bash
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v tests/api/
        ```
    *   Run all tests with coverage reporting:
        ```bash
        ./run_all_coverage_tests.sh
        ```
        This will generate HTML coverage reports in the `coverage_reports/html` directory.
    *   Run specific test categories:
        ```bash
        # Run only stress tests
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v -m stress tests/api/

        # Run only long-running tests
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v -m long_running tests/api/

        # Run security scan tests (requires Trivy)
        docker run --rm -it -v /var/run/docker.sock:/var/run/docker.sock bahtbrowse-api python3 -m pytest -v --run-security-scan tests/api/test_security.py::test_container_security_scan
        ```

### UI End-to-End Tests (Playwright)

These tests interact with the live web UI in a browser, simulating user actions.

1.  **Prerequisites:**
    *   Build the Playwright test image:
        ```bash
        docker build -t bahtbrowse-playwright -f Dockerfile.playwright .
        ```
    *   **Start the FastAPI Backend:** (In a container or directly, see below)
        ```bash
        # Option 1: Run backend in dedicated container
        docker run --rm -it -p 8000:8000 -v /var/run/docker.sock:/var/run/docker.sock --name bahtbrowse-api-server bahtbrowse-api
        # Option 2: Run backend directly in Nix shell
        # nix-shell --run "uvicorn api.main:app --reload --port 8000"
        ```
    *   **Start the Next.js Frontend:** (In a Nix shell)
        ```bash
        nix-shell --run "npm run dev"
        ```
    *   Ensure the Docker daemon is running.
2.  **Run Tests:**
    *   In a separate terminal, run the Playwright container. We use `--network host` so the container's browser can access `localhost:9002` (the frontend) AND `localhost:8000` (the backend API).
    ```bash
    docker run --rm -it \
      --network host \
      -v "$(pwd)/e2e:/app/e2e:ro" \
      -v "$(pwd)/src:/app/src:ro" \
      -v "$(pwd)/components:/app/components:ro" \
      -v "$(pwd)/hooks:/app/hooks:ro" \
      -v "$(pwd)/lib:/app/lib:ro" \
      -v "$(pwd)/ai:/app/ai:ro" \
      -v "$(pwd)/styles:/app/styles:ro" \
      -v "$(pwd)/playwright.config.ts:/app/playwright.config.ts:ro" \
      -v "$(pwd)/tsconfig.json:/app/tsconfig.json:ro" \
      -v "$(pwd)/tailwind.config.ts:/app/tailwind.config.ts:ro" \
      -v "$(pwd)/postcss.config.mjs:/app/postcss.config.mjs:ro" \
      -v "$(pwd)/next.config.mjs:/app/next.config.mjs:ro" \
      -v "$(pwd)/.env:/app/.env:ro" \
      -w /app \
      bahtbrowse-playwright \
      npx playwright test
    ```
    *   Run specific test files:
        ```bash
        # Run instance management tests
        docker run --rm -it --network host -v "$(pwd)/e2e:/app/e2e:ro" [...other mounts...] -w /app bahtbrowse-playwright npx playwright test instance-management.spec.ts

        # Run error handling tests
        docker run --rm -it --network host -v "$(pwd)/e2e:/app/e2e:ro" [...other mounts...] -w /app bahtbrowse-playwright npx playwright test error-handling.spec.ts

        # Run accessibility tests
        docker run --rm -it --network host -v "$(pwd)/e2e:/app/e2e:ro" [...other mounts...] -w /app bahtbrowse-playwright npx playwright test accessibility.spec.ts

        # Run internationalization tests
        docker run --rm -it --network host -v "$(pwd)/e2e:/app/e2e:ro" [...other mounts...] -w /app bahtbrowse-playwright npx playwright test internationalization.spec.ts
        ```
    *   *(Note: Adjust volume mounts (`-v`) if test execution requires additional project files not listed above).*
    *   *(Note: For accessibility tests, you need to install the `@axe-core/playwright` package: `npm install --save-dev @axe-core/playwright`)*

## Code Quality

BahtBrowse follows strict code quality standards to ensure maintainability and reliability:

### Python Code Standards

All Python code must follow:
- **PEP 8**: Style Guide for Python Code
- **PEP 257**: Docstring Conventions
- **PEP 484**: Type Hints

We enforce these standards using:
- **Ruff**: A fast Python linter and formatter
- **Pre-commit hooks**: Automatically check code before commits
- **Advanced tools**: mypy, bandit, radon, and coverage reporting

### Code Quality Workflow

The following diagram shows the development workflow with code quality checks:

```mermaid
flowchart TD
    Start([Start Development]) --> CreateBranch[Create Feature Branch]
    CreateBranch --> WriteCode[Write Code]
    WriteCode --> PreCommit{Pre-commit Hooks}
    PreCommit -->|Fail| FixIssues[Fix Code Quality Issues]
    FixIssues --> PreCommit
    PreCommit -->|Pass| Commit[Commit Changes]
    Commit --> Push[Push to GitHub]
    Push --> GHActions{GitHub Actions}
    GHActions -->|Fail| FixCIIssues[Fix CI Issues]
    FixCIIssues --> Push
    GHActions -->|Pass| PR[Create/Update PR]
    PR --> Review[Code Review]
    Review -->|Changes Requested| WriteCode
    Review -->|Approved| Merge[Merge PR]
    Merge --> End([End Development])

    style PreCommit fill:#f9f,stroke:#333,stroke-width:2px
    style GHActions fill:#f9f,stroke:#333,stroke-width:2px
    style Review fill:#bbf,stroke:#333,stroke-width:2px
```

For more detailed diagrams, see [Code Quality Workflow](docs/code_quality_workflow.md).

### Setting Up Pre-commit Hooks

1. Install pre-commit and ruff:
   ```bash
   pip install pre-commit ruff
   ```

2. Install the git hooks:
   ```bash
   pre-commit install
   ```

3. Run the hooks manually on all files:
   ```bash
   pre-commit run --all-files
   ```

### Advanced Code Quality Tools

BahtBrowse uses several advanced tools for code quality:

1. **Static Type Checking**: mypy ensures type safety
2. **Security Scanning**: bandit detects security vulnerabilities
3. **Complexity Analysis**: radon analyses code complexity
4. **Coverage Reporting**: pytest-cov generates test coverage reports

Run all code quality checks with a single command:
```bash
make quality-check
```

### Configuration Files

- `.pre-commit-config.yaml`: Defines pre-commit hooks
- `pyproject.toml`: Configures ruff linter and formatter
- `.ruff.toml`: Provides exceptions for existing code
- `.github/workflows/code-quality.yml`: GitHub Actions workflow for CI/CD
- `mypy.ini`: Configures static type checking
- `.coveragerc`: Configures coverage reporting

### JavaScript/TypeScript Standards

For frontend code, we follow:
- ESLint rules defined in `.eslintrc.json`
- Prettier formatting rules

## Key Directories & Files

-   `shell.nix`: Defines the Nix development environment (primarily for running host processes like frontend).
-   `Dockerfile`: Defines the Docker image for browser instances (`alpine-chromium-novnc`).
-   `supervisord.conf`: Configuration for processes running inside the browser instance container.
-   `instances.json`: Stores definitions of manageable browser instances.
-   `api/`: Contains the FastAPI backend code (`main.py`, `models.py`, `requirements.txt`).
-   `api/Dockerfile.test`: Defines the Docker image for running the API server and its tests.
-   `src/`: Contains the Next.js frontend code.
-   `tests/api/`: Contains Python integration tests for the FastAPI API:
    -   `test_fastapi_app.py`: Basic API functionality tests
    -   `test_error_paths.py`: Tests for error handling paths
    -   `test_improved_mocking.py`: Tests with improved mocking techniques
    -   `test_container_lifecycle.py`: Tests for container lifecycle management
    -   `test_port_management.py`: Tests for port availability and selection
    -   `test_config_handling.py`: Tests for configuration file handling
-   `run_all_coverage_tests.sh`: Script to run all tests with coverage reporting
-   `requirements-test.txt`: Python dependencies for API testing.
-   `e2e/`: Contains Playwright end-to-end tests for the UI.
-   `Dockerfile.playwright`: Defines the Docker image for running Playwright tests.
-   `playwright.config.ts`: Configuration file for Playwright.
-   `CHANGELOG.md`: Documents changes to the project.
-   `.pre-commit-config.yaml`: Defines pre-commit hooks for code quality.
-   `pyproject.toml`: Configures ruff linter and formatter.
-   `.ruff.toml`: Provides exceptions for existing code.
-   `.github/workflows/code-quality.yml`: GitHub Actions workflow for code quality checks.
