[pytest]
markers =
    stress: marks tests as stress tests (deselect with '-m "not stress"')
    long_running: marks tests as long-running tests (deselect with '-m "not long_running"')
    asyncio: marks tests as asyncio tests
    security: marks tests as security tests (deselect with '-m "not security"')
    browser: marks tests that interact with the browser (deselect with '-m "not browser"')
    recovery: marks tests that test system recovery (deselect with '-m "not recovery"')
    boundary: marks tests that test system boundaries (deselect with '-m "not boundary"')

# Configure asyncio tests
asyncio_mode = auto

# Configure test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Configure test output
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S
