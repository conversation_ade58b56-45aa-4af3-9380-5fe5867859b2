# Use the official Playwright image
FROM mcr.microsoft.com/playwright:latest

USER root # Switch to root to install packages

# Install Python 3, pip, and pytest via apt
RUN apt-get update && \
    apt-get install -y --no-install-recommends python3 python3-pip python3-pytest && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app # Set workdir early

# Copy dependency definition files first for layer caching
COPY package.json package-lock.json* yarn.lock* pnpm-lock.yaml* ./
COPY requirements-test.txt ./

# Install Python test dependencies (system-wide)
RUN pip3 install --no-cache-dir -r requirements-test.txt

# Install Node.js dependencies (system-wide as root might be needed for some global tools if any)
RUN npm install

USER pwuser # Switch back to default playwright user

# Copy the rest of the project files is REMOVED.
# Code will be mounted via volumes at runtime.
# COPY --chown=pwuser:pwuser . .

# Playwright browser install (as pwuser)
# Needs to run after npm install which might install playwright
RUN npx playwright install --with-deps

# Expose the port (documentation only)
EXPOSE 9002

# Set the default command (can be overridden)
C<PERSON> ["npx", "playwright", "test"]
