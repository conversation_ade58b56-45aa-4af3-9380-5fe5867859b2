[mypy]
python_version = 3.10
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = True
disallow_untyped_decorators = False
no_implicit_optional = True
strict_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# Per-module options:
[mypy.api.*]
disallow_untyped_defs = True
disallow_incomplete_defs = True

[mypy.tests.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False
