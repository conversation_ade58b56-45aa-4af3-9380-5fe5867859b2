[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 88

# Assume Python 3.10.
target-version = "py310"

[tool.ruff.lint]
# Enable flake8-bugbear (`B`) rules.
select = [
    "E",  # pycodestyle errors
    "F",  # pyflakes
    "B",  # flake8-bugbear
    "I",  # isort
    "N",  # pep8-naming
    "D",  # pydocstyle
    "UP", # pyupgrade
    "ANN", # flake8-annotations
    "S",  # flake8-bandit
    "BLE", # flake8-blind-except
    "A",  # flake8-builtins
    "COM", # flake8-commas
    "C4",  # flake8-comprehensions
    "DTZ", # flake8-datetimez
    "T10", # flake8-debugger
    "EM",  # flake8-errmsg
    "EXE", # flake8-executable
    "ISC", # flake8-implicit-str-concat
    "ICN", # flake8-import-conventions
    "G",   # flake8-logging-format
    "INP", # flake8-no-pep420
    "PIE", # flake8-pie
    "T20", # flake8-print
    "PYI", # flake8-pyi
    "PT",  # flake8-pytest-style
    "Q",   # flake8-quotes
    "RSE", # flake8-raise
    "RET", # flake8-return
    "SLF", # flake8-self
    "SIM", # flake8-simplify
    "TID", # flake8-tidy-imports
    "TCH", # flake8-type-checking
    "ARG", # flake8-unused-arguments
    "PTH", # flake8-use-pathlib
    "ERA", # eradicate
    "PD",  # pandas-vet
    "PGH", # pygrep-hooks
    "PL",  # pylint
    "TRY", # tryceratops
    "FLY", # flynt
    "PERF", # perflint
    "RUF", # ruff-specific rules
]

# Ignore specific rules
ignore = [
    "D203",  # one-blank-line-before-class (conflicts with D211)
    "D212",  # multi-line-summary-first-line (conflicts with D213)
    "ANN101", # Missing type annotation for `self` in method
    "ANN102", # Missing type annotation for `cls` in classmethod
    "PLR0913", # Too many arguments to function call
    "PLR2004", # Magic value used in comparison
    "COM812", # Missing trailing comma in Python 3.6+
    "ISC001", # Conflicts with formatter
]

# Allow autofix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.per-file-ignores]
# Tests can use assert statements and don't need docstrings
"tests/**/*.py" = ["S101", "D100", "D101", "D102", "D103", "D104", "ANN"]
# Don't require docstrings in __init__.py files
"__init__.py" = ["D100", "D104"]

[tool.ruff.lint.isort]
known-first-party = ["api"]

[tool.ruff.lint.flake8-quotes]
docstring-quotes = "double"
inline-quotes = "double"

[tool.ruff.format]
# Use double quotes for strings.
quote-style = "double"
# Indent with 4 spaces.
indent-style = "space"
# Skip magic trailing commas.
skip-magic-trailing-comma = false
