# Code Quality Dashboard

This document tracks code quality metrics for the BahtBrowse project.

## Code Quality Tools

The following diagram shows the code quality tools and their relationships:

```mermaid
flowchart TD
    Code[Source Code] --> Ruff[Ruff Linter/Formatter]
    Code --> Mypy[Mypy Type Checker]
    Code --> Bandit[Bandit Security Scanner]
    Code --> <PERSON><PERSON>[Radon Complexity Analyser]

    Ruff --> Style[Style & Formatting]
    Mypy --> Types[Type Safety]
    Bandit --> Security[Security Vulnerabilities]
    Radon --> Complexity[Code Complexity]

    Style --> QualityReport[Quality Report]
    Types --> QualityReport
    Security --> QualityReport
    Complexity --> QualityReport

    QualityReport --> Dashboard[Code Quality Dashboard]

    style Ruff fill:#f96,stroke:#333,stroke-width:2px
    style Mypy fill:#9cf,stroke:#333,stroke-width:2px
    style Bandit fill:#f66,stroke:#333,stroke-width:2px
    style Radon fill:#6c6,stroke:#333,stroke-width:2px
    style QualityReport fill:#fc9,stroke:#333,stroke-width:2px
    style Dashboard fill:#c9f,stroke:#333,stroke-width:2px
```

## Current Status

Last updated: 2025-04-25

| Metric | Value | Goal | Status |
|--------|-------|------|--------|
| Ruff violations | TBD | 0 | 🔄 |
| Type coverage | TBD | 95% | 🔄 |
| Docstring coverage | TBD | 95% | 🔄 |
| Test coverage | TBD | 90% | 🔄 |

## Files Needing Attention

These files have the most linting issues and should be prioritized for cleanup:

1. TBD - Run `ruff check . --statistics` to identify
2. TBD
3. TBD

## Recent Improvements

| Date | Description | Impact |
|------|-------------|--------|
| 2025-04-25 | Added ruff pre-commit hook | Prevents new code quality issues |
| 2025-04-25 | Added GitHub Actions workflow | Automated code quality checks in CI/CD |
| 2025-04-25 | Created code quality documentation | Better developer awareness |

## How to Contribute to Code Quality

1. **Run the linter before committing**: `make lint`
2. **Fix issues in files you're already modifying**: If you're working on a file, take a few extra minutes to fix linting issues.
3. **Create dedicated PRs for code quality improvements**: Small, focused PRs that improve specific aspects of code quality.
4. **Update this dashboard**: When you make significant improvements, update this dashboard.

## Code Quality Commands

```bash
# Get overall statistics
ruff check . --statistics

# Check a specific file
ruff check path/to/file.py

# Fix issues automatically where possible
ruff check --fix path/to/file.py

# Format a file
ruff format path/to/file.py

# Run all pre-commit hooks
pre-commit run --all-files
```

## Monthly Code Quality Goals

| Month | Goal |
|-------|------|
| May 2025 | Establish baseline metrics |
| June 2025 | Fix top 10 files with most issues |
| July 2025 | Achieve 50% type coverage |
| August 2025 | Achieve 70% docstring coverage |
| September 2025 | Achieve 80% test coverage |
