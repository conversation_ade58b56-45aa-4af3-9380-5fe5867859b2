import logging
import os
import time

import docker
import pytest
import requests
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "browser-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"
TEST_HOST_PORT = 6095


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(docker_client, container_name):
    """Helper to remove a container if it exists."""
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_container(docker_client):
    """Ensures the test container is removed before and after a test."""
    # Run before test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)
    yield
    # Run after test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)


def is_novnc_accessible(url, max_retries=10, retry_delay=5):
    """Check if noVNC is accessible at the given URL."""
    for i in range(max_retries):
        try:
            response = requests.get(url)
            if response.status_code == 200 and "noVNC" in response.text:
                return True
        except requests.RequestException:
            pass

        logging.info(
            f"noVNC not accessible yet, retrying in {retry_delay} seconds (attempt {i+1}/{max_retries})"
        )
        time.sleep(retry_delay)

    return False


@pytest.mark.skipif(
    os.environ.get("RUN_BROWSER_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Browser tests are disabled. Set RUN_BROWSER_TESTS=true to enable.",
)
def test_novnc_connection(client, docker_client, cleanup_test_container):
    """Test that noVNC connection works."""
    logging.info("--- Test: noVNC connection ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Get the noVNC URL
    novnc_url = f"http://localhost:{TEST_HOST_PORT}"
    logging.info(f"noVNC URL: {novnc_url}")

    # Check if noVNC is accessible
    assert is_novnc_accessible(novnc_url), f"noVNC not accessible at {novnc_url}"

    logging.info("noVNC connection test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_BROWSER_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Browser tests are disabled. Set RUN_BROWSER_TESTS=true to enable.",
)
def test_chromium_browser_launch(client, docker_client, cleanup_test_container):
    """Test that Chromium browser launches inside the container."""
    logging.info("--- Test: Chromium browser launch ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to start
    time.sleep(10)

    # Get the container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Check if Chromium is running
    ps_output = container.exec_run("ps aux").output.decode()
    assert (
        "chromium" in ps_output.lower()
    ), "Chromium browser is not running in the container"

    logging.info("Chromium browser launch test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_BROWSER_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Browser tests are disabled. Set RUN_BROWSER_TESTS=true to enable.",
)
def test_browser_navigation(client, docker_client, cleanup_test_container):
    """Test browser navigation inside the container."""
    logging.info("--- Test: Browser navigation ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to start
    time.sleep(10)

    # Get the container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Create a simple HTML file to test navigation
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Navigation Test</title>
    </head>
    <body>
        <h1>Navigation Test Page</h1>
        <p>This page is used to test browser navigation.</p>
        <a href="page2.html" id="link-to-page2">Go to Page 2</a>
    </body>
    </html>
    """

    test_html2 = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Page 2</title>
    </head>
    <body>
        <h1>Page 2</h1>
        <p>This is page 2.</p>
        <p id="success-marker">Navigation successful!</p>
        <a href="index.html" id="link-to-index">Go back to index</a>
    </body>
    </html>
    """

    # Create the HTML files in the container
    container.exec_run("mkdir -p /home/<USER>/test")
    container.exec_run(f"sh -c 'echo \"{test_html}\" > /home/<USER>/test/index.html'")
    container.exec_run(
        f"sh -c 'echo \"{test_html2}\" > /home/<USER>/test/page2.html'"
    )

    # Create a script to navigate between pages
    test_script = """
    #!/bin/sh
    cd /home/<USER>/test
    python -m http.server 8080 &
    sleep 2
    chromium-browser --no-sandbox --headless --dump-dom http://localhost:8080/index.html > /home/<USER>/index_dump.txt
    sleep 2
    chromium-browser --no-sandbox --headless --dump-dom http://localhost:8080/page2.html > /home/<USER>/page2_dump.txt
    """

    script_path = "/home/<USER>/test_navigation.sh"
    container.exec_run(
        f"sh -c 'echo \"{test_script}\" > {script_path} && chmod +x {script_path}'"
    )

    # Run the script
    exec_result = container.exec_run(f"/bin/sh {script_path}")
    logging.info(f"Navigation script execution result: {exec_result.exit_code}")

    # Check the results
    index_dump = container.exec_run("cat /home/<USER>/index_dump.txt").output.decode()
    page2_dump = container.exec_run("cat /home/<USER>/page2_dump.txt").output.decode()

    assert "Navigation Test Page" in index_dump, "Index page content not found"
    assert "Go to Page 2" in index_dump, "Link to page 2 not found"

    assert "Page 2" in page2_dump, "Page 2 content not found"
    assert "Navigation successful" in page2_dump, "Success marker not found on page 2"

    logging.info("Browser navigation test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_BROWSER_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Browser tests are disabled. Set RUN_BROWSER_TESTS=true to enable.",
)
def test_browser_javascript_execution(client, docker_client, cleanup_test_container):
    """Test JavaScript execution in the browser."""
    logging.info("--- Test: Browser JavaScript execution ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to start
    time.sleep(10)

    # Get the container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Create a simple HTML file with JavaScript
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>JavaScript Test</title>
    </head>
    <body>
        <h1>JavaScript Test Page</h1>
        <p id="demo">JavaScript will change this text.</p>
        <button id="test-button" onclick="changeText()">Click me</button>

        <script>
        function changeText() {
            document.getElementById("demo").innerHTML = "JavaScript executed successfully!";
            // Create a marker element to indicate the script ran
            var marker = document.createElement("div");
            marker.id = "js-executed";
            marker.style.display = "none";
            document.body.appendChild(marker);
        }

        // Auto-execute after a delay
        setTimeout(function() {
            changeText();
            // Create another marker for the auto-execution
            var marker = document.createElement("div");
            marker.id = "js-auto-executed";
            marker.style.display = "none";
            document.body.appendChild(marker);
        }, 1000);
        </script>
    </body>
    </html>
    """

    # Create the HTML file in the container
    container.exec_run("mkdir -p /home/<USER>/test")
    container.exec_run(
        f"sh -c 'echo \"{test_html}\" > /home/<USER>/test/js_test.html'"
    )

    # Create a script to load the page and wait for JavaScript execution
    test_script = """
    #!/bin/sh
    cd /home/<USER>/test
    python -m http.server 8080 &
    sleep 2

    # First load the page and wait for auto-execution
    chromium-browser --no-sandbox --headless --disable-gpu --window-size=1280,720 --virtual-time-budget=5000 http://localhost:8080/js_test.html
    sleep 3

    # Dump the DOM after JavaScript execution
    chromium-browser --no-sandbox --headless --dump-dom http://localhost:8080/js_test.html > /home/<USER>/js_test_dump.txt
    """

    script_path = "/home/<USER>/test_js.sh"
    container.exec_run(
        f"sh -c 'echo \"{test_script}\" > {script_path} && chmod +x {script_path}'"
    )

    # Run the script
    exec_result = container.exec_run(f"/bin/sh {script_path}")
    logging.info(f"JavaScript test script execution result: {exec_result.exit_code}")

    # Check the results
    js_test_dump = container.exec_run(
        "cat /home/<USER>/js_test_dump.txt"
    ).output.decode()

    assert (
        "JavaScript Test Page" in js_test_dump
    ), "JavaScript test page content not found"
    assert (
        "JavaScript executed successfully" in js_test_dump
    ), "JavaScript execution result not found"
    assert (
        "js-auto-executed" in js_test_dump
    ), "JavaScript auto-execution marker not found"

    logging.info("Browser JavaScript execution test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_BROWSER_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Browser tests are disabled. Set RUN_BROWSER_TESTS=true to enable.",
)
def test_browser_cookie_handling(client, docker_client, cleanup_test_container):
    """Test cookie handling in the browser."""
    logging.info("--- Test: Browser cookie handling ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to start
    time.sleep(10)

    # Get the container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Create HTML files to test cookie setting and reading
    cookie_setter_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Cookie Setter</title>
    </head>
    <body>
        <h1>Cookie Setter Page</h1>
        <script>
        // Set a test cookie
        document.cookie = "testCookie=testValue; expires=" + new Date(Date.now() + 86400000).toUTCString() + "; path=/";
        document.cookie = "secondCookie=anotherValue; expires=" + new Date(Date.now() + 86400000).toUTCString() + "; path=/";

        // Display the cookies
        document.write("<p>Cookies set: " + document.cookie + "</p>");

        // Create a marker element
        var marker = document.createElement("div");
        marker.id = "cookies-set";
        marker.textContent = document.cookie;
        document.body.appendChild(marker);
        </script>
        <a href="cookie_reader.html">Go to Cookie Reader</a>
    </body>
    </html>
    """

    cookie_reader_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Cookie Reader</title>
    </head>
    <body>
        <h1>Cookie Reader Page</h1>
        <div id="cookie-display">Loading cookies...</div>
        <script>
        // Read and display the cookies
        document.getElementById("cookie-display").textContent = "Cookies read: " + document.cookie;

        // Create a marker element with the cookies
        var marker = document.createElement("div");
        marker.id = "cookies-read";
        marker.textContent = document.cookie;
        document.body.appendChild(marker);

        // Check if the test cookie exists
        if (document.cookie.indexOf("testCookie=testValue") !== -1) {
            var successMarker = document.createElement("div");
            successMarker.id = "cookie-test-success";
            document.body.appendChild(successMarker);
        }
        </script>
    </body>
    </html>
    """

    # Create the HTML files in the container
    container.exec_run("mkdir -p /home/<USER>/test")
    container.exec_run(
        f"sh -c 'echo \"{cookie_setter_html}\" > /home/<USER>/test/cookie_setter.html'"
    )
    container.exec_run(
        f"sh -c 'echo \"{cookie_reader_html}\" > /home/<USER>/test/cookie_reader.html'"
    )

    # Create a script to test cookie handling
    test_script = """
    #!/bin/sh
    cd /home/<USER>/test
    python -m http.server 8080 &
    sleep 2

    # First load the cookie setter page
    chromium-browser --no-sandbox --headless --disable-gpu --window-size=1280,720 http://localhost:8080/cookie_setter.html
    sleep 2

    # Dump the DOM of the cookie setter page
    chromium-browser --no-sandbox --headless --dump-dom http://localhost:8080/cookie_setter.html > /home/<USER>/cookie_setter_dump.txt

    # Then load the cookie reader page
    chromium-browser --no-sandbox --headless --disable-gpu --window-size=1280,720 http://localhost:8080/cookie_reader.html
    sleep 2

    # Dump the DOM of the cookie reader page
    chromium-browser --no-sandbox --headless --dump-dom http://localhost:8080/cookie_reader.html > /home/<USER>/cookie_reader_dump.txt
    """

    script_path = "/home/<USER>/test_cookies.sh"
    container.exec_run(
        f"sh -c 'echo \"{test_script}\" > {script_path} && chmod +x {script_path}'"
    )

    # Run the script
    exec_result = container.exec_run(f"/bin/sh {script_path}")
    logging.info(f"Cookie test script execution result: {exec_result.exit_code}")

    # Check the results
    setter_dump = container.exec_run(
        "cat /home/<USER>/cookie_setter_dump.txt"
    ).output.decode()
    reader_dump = container.exec_run(
        "cat /home/<USER>/cookie_reader_dump.txt"
    ).output.decode()

    assert "Cookie Setter Page" in setter_dump, "Cookie setter page content not found"
    assert "cookies-set" in setter_dump, "Cookie set marker not found"
    assert "testCookie=testValue" in setter_dump, "Test cookie not set"

    assert "Cookie Reader Page" in reader_dump, "Cookie reader page content not found"
    assert "cookies-read" in reader_dump, "Cookie read marker not found"
    assert "testCookie=testValue" in reader_dump, "Test cookie not read"
    assert "cookie-test-success" in reader_dump, "Cookie test success marker not found"

    logging.info("Browser cookie handling test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_BROWSER_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Browser tests are disabled. Set RUN_BROWSER_TESTS=true to enable.",
)
def test_browser_local_storage(client, docker_client, cleanup_test_container):
    """Test localStorage in the browser."""
    logging.info("--- Test: Browser localStorage ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to start
    time.sleep(10)

    # Get the container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Create HTML files to test localStorage setting and reading
    storage_setter_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>localStorage Setter</title>
    </head>
    <body>
        <h1>localStorage Setter Page</h1>
        <div id="storage-result">Setting localStorage...</div>
        <script>
        try {
            // Set localStorage items
            localStorage.setItem("testItem", "testValue");
            localStorage.setItem("jsonItem", JSON.stringify({test: "value", number: 123}));

            // Display the localStorage
            var result = "localStorage set: ";
            for (var i = 0; i < localStorage.length; i++) {
                var key = localStorage.key(i);
                result += key + "=" + localStorage.getItem(key) + "; ";
            }
            document.getElementById("storage-result").textContent = result;

            // Create a marker element
            var marker = document.createElement("div");
            marker.id = "storage-set";
            marker.textContent = result;
            document.body.appendChild(marker);
        } catch (e) {
            document.getElementById("storage-result").textContent = "Error: " + e.message;
        }
        </script>
        <a href="storage_reader.html">Go to localStorage Reader</a>
    </body>
    </html>
    """

    storage_reader_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>localStorage Reader</title>
    </head>
    <body>
        <h1>localStorage Reader Page</h1>
        <div id="storage-display">Loading localStorage...</div>
        <script>
        try {
            // Read and display the localStorage
            var result = "localStorage read: ";
            for (var i = 0; i < localStorage.length; i++) {
                var key = localStorage.key(i);
                result += key + "=" + localStorage.getItem(key) + "; ";
            }
            document.getElementById("storage-display").textContent = result;

            // Create a marker element with the localStorage
            var marker = document.createElement("div");
            marker.id = "storage-read";
            marker.textContent = result;
            document.body.appendChild(marker);

            // Check if the test item exists
            if (localStorage.getItem("testItem") === "testValue") {
                var successMarker = document.createElement("div");
                successMarker.id = "storage-test-success";
                document.body.appendChild(successMarker);
            }

            // Check if the JSON item exists and can be parsed
            var jsonItem = localStorage.getItem("jsonItem");
            if (jsonItem) {
                try {
                    var parsed = JSON.parse(jsonItem);
                    if (parsed.test === "value" && parsed.number === 123) {
                        var jsonSuccessMarker = document.createElement("div");
                        jsonSuccessMarker.id = "json-test-success";
                        document.body.appendChild(jsonSuccessMarker);
                    }
                } catch (e) {
                    console.error("JSON parse error:", e);
                }
            }
        } catch (e) {
            document.getElementById("storage-display").textContent = "Error: " + e.message;
        }
        </script>
    </body>
    </html>
    """

    # Create the HTML files in the container
    container.exec_run("mkdir -p /home/<USER>/test")
    container.exec_run(
        f"sh -c 'echo \"{storage_setter_html}\" > /home/<USER>/test/storage_setter.html'"
    )
    container.exec_run(
        f"sh -c 'echo \"{storage_reader_html}\" > /home/<USER>/test/storage_reader.html'"
    )

    # Create a script to test localStorage
    test_script = """
    #!/bin/sh
    cd /home/<USER>/test
    python -m http.server 8080 &
    sleep 2

    # First load the localStorage setter page
    chromium-browser --no-sandbox --headless --disable-gpu --window-size=1280,720 http://localhost:8080/storage_setter.html
    sleep 2

    # Dump the DOM of the localStorage setter page
    chromium-browser --no-sandbox --headless --dump-dom http://localhost:8080/storage_setter.html > /home/<USER>/storage_setter_dump.txt

    # Then load the localStorage reader page
    chromium-browser --no-sandbox --headless --disable-gpu --window-size=1280,720 http://localhost:8080/storage_reader.html
    sleep 2

    # Dump the DOM of the localStorage reader page
    chromium-browser --no-sandbox --headless --dump-dom http://localhost:8080/storage_reader.html > /home/<USER>/storage_reader_dump.txt
    """

    script_path = "/home/<USER>/test_storage.sh"
    container.exec_run(
        f"sh -c 'echo \"{test_script}\" > {script_path} && chmod +x {script_path}'"
    )

    # Run the script
    exec_result = container.exec_run(f"/bin/sh {script_path}")
    logging.info(f"localStorage test script execution result: {exec_result.exit_code}")

    # Check the results
    setter_dump = container.exec_run(
        "cat /home/<USER>/storage_setter_dump.txt"
    ).output.decode()
    reader_dump = container.exec_run(
        "cat /home/<USER>/storage_reader_dump.txt"
    ).output.decode()

    assert (
        "localStorage Setter Page" in setter_dump
    ), "localStorage setter page content not found"
    assert "storage-set" in setter_dump, "localStorage set marker not found"
    assert "testItem=testValue" in setter_dump, "Test item not set in localStorage"

    assert (
        "localStorage Reader Page" in reader_dump
    ), "localStorage reader page content not found"
    assert "storage-read" in reader_dump, "localStorage read marker not found"
    assert "testItem=testValue" in reader_dump, "Test item not read from localStorage"
    assert (
        "storage-test-success" in reader_dump
    ), "localStorage test success marker not found"
    assert "json-test-success" in reader_dump, "JSON test success marker not found"

    logging.info("Browser localStorage test passed")
