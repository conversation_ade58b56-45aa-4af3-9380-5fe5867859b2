#!/usr/bin/env python3
"""Integration testing module for BahtBrowse API.

This module contains tests for validating the integration between
different components of the BahtBrowse system, including:
- API to Docker integration
- API to frontend integration
- Browser to host integration
- Multiple instance coordination
"""

import logging
import os
import re
import time
from collections.abc import Generator

import docker
import pytest
import requests
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "integration-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"
TEST_HOST_PORT = 6094


@pytest.fixture(scope="module")
def client() -> Generator[TestClient, None, None]:
    """Provide a FastAPI TestClient.

    Returns:
        Generator[TestClient, None, None]: A FastAPI test client
    """
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client() -> docker.DockerClient:
    """Provide a Docker client connection.

    Returns:
        docker.DockerClient: A Docker client

    Raises:
        pytest.fail: If Docker daemon connection fails
    """
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(
    docker_client: docker.DockerClient, container_name: str
) -> None:
    """Remove a container if it exists.

    Args:
        docker_client: Docker client to use
        container_name: Name of the container to remove
    """
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_container(
    docker_client: docker.DockerClient,
) -> Generator[None, None, None]:
    """Ensure the test container is removed before and after a test.

    Args:
        docker_client: Docker client to use
    """
    # Run before test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)
    yield
    # Run after test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)


def is_port_in_use(port: int) -> bool:
    """Check if a port is in use.

    Args:
        port: Port number to check

    Returns:
        bool: True if port is in use, False otherwise
    """
    import socket

    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(("localhost", port)) == 0


def wait_for_port(port: int, timeout: int = 30) -> bool:
    """Wait for a port to be available.

    Args:
        port: Port number to wait for
        timeout: Maximum time to wait in seconds

    Returns:
        bool: True if port became available, False if timeout
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        if is_port_in_use(port):
            return True
        time.sleep(1)
    return False


def test_api_docker_integration(
    client: TestClient, docker_client: docker.DockerClient, cleanup_test_container: None
) -> None:
    """Test integration between the API and Docker.

    Args:
        client: FastAPI test client
        docker_client: Docker client
        cleanup_test_container: Fixture to clean up test containers
    """
    logging.info("--- Test: API to Docker integration ---")

    # Start an instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to start
    time.sleep(5)

    # Verify container exists in Docker
    try:
        container = docker_client.containers.get(TEST_CONTAINER_NAME)
        assert (
            container.status == "running"
        ), f"Container should be running, but status is {container.status}"

        # Verify container properties
        container_info = docker_client.api.inspect_container(TEST_CONTAINER_NAME)

        # Check port mapping
        port_bindings = container_info["HostConfig"]["PortBindings"]
        assert "6080/tcp" in port_bindings, "Container should have port 6080 mapped"
        host_port = int(port_bindings["6080/tcp"][0]["HostPort"])
        assert (
            host_port == TEST_HOST_PORT
        ), f"Container port should be mapped to {TEST_HOST_PORT}, but is mapped to {host_port}"

        # Check container name
        assert (
            container_info["Name"] == f"/{TEST_CONTAINER_NAME}"
        ), f"Container name should be {TEST_CONTAINER_NAME}"

        # Check image
        assert (
            DOCKER_IMAGE_NAME in container_info["Config"]["Image"]
        ), f"Container should use image {DOCKER_IMAGE_NAME}"

        logging.info("Container properties verified")

        # Verify API reports correct status
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        logging.info(f"Status response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200
        assert (
            response.json()["status"] == "running"
        ), f"API should report status as running, but got {response.json()['status']}"

        # Stop the instance
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
        logging.info(f"Stop response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200

        # Wait for container to stop
        time.sleep(5)

        # Verify container is stopped
        container.reload()
        assert (
            container.status != "running"
        ), f"Container should not be running, but status is {container.status}"

        # Verify API reports correct status
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        logging.info(
            f"Status response after stop: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200
        assert (
            response.json()["status"] != "running"
        ), f"API should not report status as running, but got {response.json()['status']}"

    except docker.errors.NotFound:
        pytest.fail(f"Container {TEST_CONTAINER_NAME} not found")

    logging.info("API to Docker integration test passed")


def test_port_allocation(
    client: TestClient, docker_client: docker.DockerClient, cleanup_test_container: None
) -> None:
    """Test port allocation for instances.

    Args:
        client: FastAPI test client
        docker_client: Docker client
        cleanup_test_container: Fixture to clean up test containers
    """
    logging.info("--- Test: Port allocation ---")

    # Get an available port
    response = client.get("/api/ports/available")
    logging.info(
        f"Available port response: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200

    port_data = response.json()
    assert "port" in port_data, "Response should contain a port field"
    port = port_data["port"]
    assert isinstance(port, int), f"Port should be an integer, but got {type(port)}"
    assert port >= 6000, f"Port should be at least 6000, but got {port}"

    # Verify the port is not in use
    assert not is_port_in_use(port), f"Port {port} should not be in use"

    # Start an instance with the allocated port
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": port}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to start
    time.sleep(5)

    # Verify the port is now in use
    assert wait_for_port(
        port
    ), f"Port {port} should be in use after starting the instance"

    # Try to get another available port
    response = client.get("/api/ports/available")
    logging.info(
        f"Second available port response: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200

    second_port_data = response.json()
    second_port = second_port_data["port"]
    assert (
        second_port != port
    ), f"Second port should be different from first port, but both are {port}"

    # Stop the instance
    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
    logging.info(f"Stop response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to stop
    time.sleep(5)

    # Verify the port is no longer in use
    assert not is_port_in_use(
        port
    ), f"Port {port} should not be in use after stopping the instance"

    logging.info("Port allocation test passed")


def test_multiple_instances(
    client: TestClient, docker_client: docker.DockerClient
) -> None:
    """Test running multiple instances simultaneously.

    Args:
        client: FastAPI test client
        docker_client: Docker client
    """
    logging.info("--- Test: Multiple instances ---")

    # Define test instances
    test_instances = [
        {"id": f"{TEST_INSTANCE_ID}-1", "port": 6081},
        {"id": f"{TEST_INSTANCE_ID}-2", "port": 6082},
        {"id": f"{TEST_INSTANCE_ID}-3", "port": 6083},
    ]

    container_names = [f"bahtbrowser-{instance['id']}" for instance in test_instances]

    try:
        # Clean up any existing test containers
        for container_name in container_names:
            _remove_container_if_exists(docker_client, container_name)

        # Start all instances
        for instance in test_instances:
            response = client.post(
                f"/api/instances/{instance['id']}/start",
                json={"port": instance["port"]},
            )
            logging.info(
                f"Start response for {instance['id']}: {response.status_code}, body: {response.text}"
            )
            assert response.status_code == 200

        # Wait for containers to start
        time.sleep(15)

        # Verify all containers are running
        for container_name in container_names:
            try:
                container = docker_client.containers.get(container_name)
                assert (
                    container.status == "running"
                ), f"Container {container_name} should be running, but status is {container.status}"
                logging.info(f"Container {container_name} is running")
            except docker.errors.NotFound:
                pytest.fail(f"Container {container_name} not found")

        # Verify all ports are in use
        for instance in test_instances:
            assert is_port_in_use(
                instance["port"]
            ), f"Port {instance['port']} should be in use"
            logging.info(f"Port {instance['port']} is in use")

        # Get status of all instances
        response = client.get("/api/instances/statuses")
        logging.info(
            f"Statuses response: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200

        statuses = response.json()
        for instance in test_instances:
            assert (
                instance["id"] in statuses
            ), f"Instance {instance['id']} should be in statuses"
            assert (
                statuses[instance["id"]]["status"] == "running"
            ), f"Instance {instance['id']} should be running"

        # Stop all instances
        for instance in test_instances:
            response = client.post(f"/api/instances/{instance['id']}/stop")
            logging.info(
                f"Stop response for {instance['id']}: {response.status_code}, body: {response.text}"
            )
            assert response.status_code == 200

        # Wait for containers to stop
        time.sleep(15)

        # Verify all containers are stopped
        for container_name in container_names:
            try:
                container = docker_client.containers.get(container_name)
                assert (
                    container.status != "running"
                ), f"Container {container_name} should not be running, but status is {container.status}"
                logging.info(f"Container {container_name} is stopped")
            except docker.errors.NotFound:
                logging.info(f"Container {container_name} was removed")

        # Verify all ports are no longer in use
        for instance in test_instances:
            assert not is_port_in_use(
                instance["port"]
            ), f"Port {instance['port']} should not be in use"
            logging.info(f"Port {instance['port']} is not in use")

    finally:
        # Clean up all test containers
        for container_name in container_names:
            _remove_container_if_exists(docker_client, container_name)

    logging.info("Multiple instances test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_BROWSER_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Browser tests are disabled. Set RUN_BROWSER_TESTS=true to enable.",
)
def test_browser_novnc_integration(
    client: TestClient, docker_client: docker.DockerClient, cleanup_test_container: None
) -> None:
    """Test integration between the browser and noVNC.

    Args:
        client: FastAPI test client
        docker_client: Docker client
        cleanup_test_container: Fixture to clean up test containers
    """
    logging.info("--- Test: Browser to noVNC integration ---")

    # Start an instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to start
    time.sleep(10)

    # Verify noVNC is accessible
    novnc_url = f"http://localhost:{TEST_HOST_PORT}"
    try:
        response = requests.get(novnc_url, timeout=5)
        assert response.status_code == 200, f"noVNC should be accessible at {novnc_url}"
        assert "noVNC" in response.text, "Response should contain 'noVNC'"
        logging.info("noVNC is accessible")

        # Check if websocket endpoint is available
        websocket_pattern = re.compile(r'path=(["\'])([^"\']+)\1')
        match = websocket_pattern.search(response.text)
        if match:
            websocket_path = match.group(2)
            logging.info(f"WebSocket path: {websocket_path}")

            # Construct WebSocket URL
            if websocket_path.startswith("/"):
                websocket_url = f"ws://localhost:{TEST_HOST_PORT}{websocket_path}"
            else:
                websocket_url = f"ws://localhost:{TEST_HOST_PORT}/{websocket_path}"

            logging.info(f"WebSocket URL: {websocket_url}")

            # We can't actually test the WebSocket connection here,
            # but we can verify the URL format is correct
            assert (
                "websockify" in websocket_url or "vnc" in websocket_url
            ), "WebSocket URL should contain 'websockify' or 'vnc'"

    except requests.RequestException as e:
        pytest.fail(f"Failed to access noVNC: {e}")

    # Verify browser processes are running in the container
    try:
        container = docker_client.containers.get(TEST_CONTAINER_NAME)
        ps_output = container.exec_run("ps aux").output.decode()

        assert (
            "chromium" in ps_output.lower()
        ), "Chromium browser should be running in the container"
        assert (
            "x11vnc" in ps_output.lower() or "tigervnc" in ps_output.lower()
        ), "VNC server should be running in the container"
        assert (
            "websockify" in ps_output.lower() or "novnc" in ps_output.lower()
        ), "WebSocket proxy should be running in the container"

        logging.info("Browser processes are running")

    except docker.errors.NotFound:
        pytest.fail(f"Container {TEST_CONTAINER_NAME} not found")

    # Stop the instance
    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
    logging.info(f"Stop response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for container to stop
    time.sleep(5)

    # Verify noVNC is no longer accessible
    try:
        response = requests.get(novnc_url, timeout=2)
        assert (
            response.status_code != 200
        ), f"noVNC should not be accessible at {novnc_url} after stopping the instance"
    except requests.RequestException:
        logging.info("noVNC is no longer accessible, as expected")

    logging.info("Browser to noVNC integration test passed")


def test_api_error_handling_integration(client: TestClient) -> None:
    """Test API error handling integration.

    Args:
        client: FastAPI test client
    """
    logging.info("--- Test: API error handling integration ---")

    # Test case 1: Non-existent instance
    response = client.get("/api/instances/non-existent-instance/status")
    logging.info(
        f"Non-existent instance status response: {response.status_code}, body: {response.text}"
    )
    assert response.status_code in [
        200,
        404,
    ], f"Expected 200 or 404 for non-existent instance, got {response.status_code}"

    if response.status_code == 200:
        # If API returns 200, it should indicate the instance is not running
        status_data = response.json()
        assert (
            status_data["status"] in ["stopped", "error", "not_found"]
        ), f"Expected status to be stopped, error, or not_found, got {status_data['status']}"

    # Test case 2: Invalid port
    # First, we need to make sure the instance exists in the configuration
    # Get all instances to check if our test instance is defined
    instances_response = client.get("/api/instances")
    instances = instances_response.json()
    instance_exists = any(
        instance.get("id") == TEST_INSTANCE_ID for instance in instances
    )

    if not instance_exists:
        logging.info(
            f"Test instance {TEST_INSTANCE_ID} not found in configuration, skipping invalid port test"
        )
        # Skip this test if the instance doesn't exist
    else:
        response = client.post(
            f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": "invalid"}
        )
        logging.info(
            f"Invalid port response: {response.status_code}, body: {response.text}"
        )
        assert response.status_code in [
            400,
            422,
            404,
        ], f"Expected 400, 422, or 404 for invalid port, got {response.status_code}"

    # Test case 3: Port out of range
    if not instance_exists:
        logging.info(
            f"Test instance {TEST_INSTANCE_ID} not found in configuration, skipping port out of range test"
        )
        # Skip this test if the instance doesn't exist
    else:
        response = client.post(
            f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": 1}
        )
        logging.info(
            f"Port out of range response: {response.status_code}, body: {response.text}"
        )
        assert (
            response.status_code in [400, 422, 404]
        ), f"Expected 400, 422, or 404 for port out of range, got {response.status_code}"

    # Test case 4: Port already in use
    # First, find a port that's in use
    import socket

    test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    test_socket.bind(("", 0))  # Bind to a random port
    used_port = test_socket.getsockname()[1]

    try:
        if not instance_exists:
            logging.info(
                f"Test instance {TEST_INSTANCE_ID} not found in configuration, skipping port in use test"
            )
            # Skip this test if the instance doesn't exist
        else:
            response = client.post(
                f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": used_port}
            )
            logging.info(
                f"Port in use response: {response.status_code}, body: {response.text}"
            )
            # The API might handle this in different ways
            assert response.status_code in [
                200,
                400,
                409,
                422,
                500,
                404,
            ], f"Unexpected status code for port in use: {response.status_code}"
    finally:
        test_socket.close()

    logging.info("API error handling integration test passed")


def test_instance_lifecycle_integration(
    client: TestClient, docker_client: docker.DockerClient, cleanup_test_container: None
) -> None:
    """Test the complete lifecycle of an instance.

    Args:
        client: FastAPI test client
        docker_client: Docker client
        cleanup_test_container: Fixture to clean up test containers
    """
    logging.info("--- Test: Instance lifecycle integration ---")

    # Step 1: Verify instance is not running initially
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(
        f"Initial status response: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200
    initial_status = response.json()["status"]
    assert (
        initial_status in ["stopped", "not_found", "error"]
    ), f"Expected initial status to be stopped, not_found, or error, got {initial_status}"

    # Step 2: Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Step 3: Wait for instance to start
    time.sleep(10)

    # Step 4: Verify instance is running
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(
        f"Status after start response: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200
    assert (
        response.json()["status"] == "running"
    ), f"Expected status to be running, got {response.json()['status']}"

    # Step 5: Verify container exists in Docker
    try:
        container = docker_client.containers.get(TEST_CONTAINER_NAME)
        assert (
            container.status == "running"
        ), f"Container should be running, but status is {container.status}"
        logging.info("Container is running")
    except docker.errors.NotFound:
        pytest.fail(f"Container {TEST_CONTAINER_NAME} not found")

    # Step 6: Verify port is in use
    assert is_port_in_use(TEST_HOST_PORT), f"Port {TEST_HOST_PORT} should be in use"
    logging.info(f"Port {TEST_HOST_PORT} is in use")

    # Step 7: Stop the instance
    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
    logging.info(f"Stop response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Step 8: Wait for instance to stop
    time.sleep(10)

    # Step 9: Verify instance is stopped
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(
        f"Status after stop response: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200
    assert (
        response.json()["status"] != "running"
    ), f"Expected status to not be running, got {response.json()['status']}"

    # Step 10: Verify container is stopped or removed
    try:
        container = docker_client.containers.get(TEST_CONTAINER_NAME)
        assert (
            container.status != "running"
        ), f"Container should not be running, but status is {container.status}"
        logging.info(f"Container is {container.status}")
    except docker.errors.NotFound:
        logging.info("Container was removed")

    # Step 11: Verify port is no longer in use
    assert not is_port_in_use(
        TEST_HOST_PORT
    ), f"Port {TEST_HOST_PORT} should not be in use"
    logging.info(f"Port {TEST_HOST_PORT} is not in use")

    # Step 12: Start the instance again
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(
        f"Second start response: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200

    # Step 13: Wait for instance to start
    time.sleep(10)

    # Step 14: Verify instance is running again
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(
        f"Status after second start response: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200
    assert (
        response.json()["status"] == "running"
    ), f"Expected status to be running, got {response.json()['status']}"

    # Step 15: Delete the instance
    response = client.delete(f"/api/instances/{TEST_INSTANCE_ID}")
    logging.info(f"Delete response: {response.status_code}, body: {response.text}")
    assert response.status_code in [
        200,
        404,
    ], f"Expected 200 or 404 for delete, got {response.status_code}"

    # Step 16: Wait for instance to be deleted
    time.sleep(5)

    # Step 17: Verify instance is gone
    try:
        container = docker_client.containers.get(TEST_CONTAINER_NAME)
        assert (
            container.status != "running"
        ), f"Container should not be running after delete, but status is {container.status}"
    except docker.errors.NotFound:
        logging.info("Container was removed, as expected")

    logging.info("Instance lifecycle integration test passed")
