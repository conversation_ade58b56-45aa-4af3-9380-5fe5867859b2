import logging
from unittest.mock import MagicMock, patch

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "1"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"
TEST_HOST_PORT = 6080


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def test_docker_daemon_unavailable(client):
    """Test behaviour when Docker daemon is unavailable."""
    logging.info("--- Test: Docker daemon unavailable ---")

    # Mock the Docker client to simulate daemon unavailability
    with patch("docker.from_env") as mock_docker:
        # Configure the mock to raise an exception when used
        mock_client = MagicMock()
        mock_client.containers.run.side_effect = docker.errors.DockerException(
            "Docker daemon unavailable"
        )
        mock_client.containers.get.side_effect = docker.errors.DockerException(
            "Docker daemon unavailable"
        )
        mock_docker.return_value = mock_client

        # Test starting an instance when Docker is unavailable
        with patch("api.main.docker.from_env", return_value=mock_client):
            response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")
            logging.info(
                f"Start response with Docker unavailable: {response.status_code}, body: {response.text}"
            )

            # API should return a 503 Service Unavailable
            assert response.status_code == 503
            data = response.json()
            assert data["success"] is False
            assert "Docker daemon" in data["message"]

        # Test getting instance status when Docker is unavailable
        with patch("api.main.docker.from_env", return_value=mock_client):
            response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
            logging.info(
                f"Status response with Docker unavailable: {response.status_code}, body: {response.text}"
            )

            # API should handle this gracefully
            assert response.status_code == 503
            data = response.json()
            assert "error" in data
            assert "Docker daemon" in data["error"]


def test_invalid_instance_id(client):
    """Test behaviour with invalid instance IDs."""
    logging.info("--- Test: Invalid instance ID ---")

    # Test with invalid characters in ID
    invalid_ids = [
        "instance;id",  # Semicolon (potential command injection)
        "instance&id",  # Ampersand (potential command injection)
        "instance|id",  # Pipe (potential command injection)
        "instance>id",  # Redirect (potential command injection)
        "instance<id",  # Redirect (potential command injection)
        'instance"id',  # Quote (potential string termination)
        "instance'id",  # Quote (potential string termination)
        "instance\\id",  # Backslash (potential escaping)
        "../instance",  # Path traversal
        "/etc/passwd",  # Path traversal
    ]

    for invalid_id in invalid_ids:
        # Test starting an instance with invalid ID
        response = client.post(f"/api/instances/{invalid_id}/start")
        logging.info(
            f"Start response with invalid ID '{invalid_id}': {response.status_code}, body: {response.text}"
        )

        # API should return a 400 Bad Request
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert (
            "invalid" in data["message"].lower()
            or "not found" in data["message"].lower()
        )

        # Test getting status with invalid ID
        response = client.get(f"/api/instances/{invalid_id}/status")
        logging.info(
            f"Status response with invalid ID '{invalid_id}': {response.status_code}, body: {response.text}"
        )

        # API should return a 400 Bad Request
        assert response.status_code == 400
        data = response.json()
        assert "error" in data or "status" in data


def test_malformed_request_data(client):
    """Test behaviour with malformed request data."""
    logging.info("--- Test: Malformed request data ---")

    # Test with various malformed JSON payloads
    malformed_payloads = [
        {"port": "not_a_number"},  # String instead of integer
        {"port": -1},  # Negative port number
        {"port": 99999},  # Port number too large
        {"image": 12345},  # Number instead of string
        {"unknown_field": "value"},  # Unknown field
    ]

    for payload in malformed_payloads:
        # Test starting an instance with malformed payload
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start", json=payload)
        logging.info(
            f"Start response with malformed payload {payload}: {response.status_code}, body: {response.text}"
        )

        # API should return a 400 Bad Request or handle it gracefully
        assert response.status_code in [400, 422]
        data = response.json()
        assert data.get("success", True) is False or "detail" in data


def test_resource_constraints(client, docker_client):
    """Test behaviour when system resources are limited."""
    logging.info("--- Test: Resource constraints ---")

    # Mock the Docker client to simulate resource constraints
    with patch("docker.from_env") as mock_docker:
        # Configure the mock to raise a resource constraint exception
        mock_client = MagicMock()
        mock_client.containers.run.side_effect = docker.errors.APIError(
            "cannot start container: not enough resources",
            response=MagicMock(status_code=500),
        )
        mock_docker.return_value = mock_client

        # Test starting an instance when resources are limited
        with patch("api.main.docker.from_env", return_value=mock_client):
            response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")
            logging.info(
                f"Start response with resource constraints: {response.status_code}, body: {response.text}"
            )

            # API should return a 503 Service Unavailable or 507 Insufficient Storage
            assert response.status_code in [503, 507]
            data = response.json()
            assert data["success"] is False
            assert (
                "resource" in data["message"].lower()
                or "start" in data["message"].lower()
            )
