import logging
import time

import docker
import pytest

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# --- Configuration ---
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"  # Make sure this matches your build
# Use unique names/ports for testing to avoid conflicts
TEST_INSTANCE_ID_API = "py-api-test-1"
TEST_CONTAINER_NAME_API = f"instance-{TEST_INSTANCE_ID_API}"
TEST_HOST_PORT_API = 6101
# -------------------


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()  # Verify connection
        logging.info("Docker client connected successfully.")
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


@pytest.fixture(scope="function")  # Run cleanup after each test function
def cleanup_test_container(docker_client):
    """Ensures the test container is removed before and after a test."""
    # Run before test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME_API)
    yield  # This is where the test runs
    # Run after test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME_API)


def _get_container(docker_client, container_name):
    try:
        return docker_client.containers.get(container_name)
    except docker.errors.NotFound:
        return None
    except Exception as e:
        logging.exception(f"Error getting container {container_name}: {e}")
        return None  # Treat other errors as not found for safety


def _remove_container_if_exists(docker_client, container_name):
    container = _get_container(docker_client, container_name)
    if container:
        try:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
                logging.info(f"Stopped container: {container_name}")
            container.remove(force=True)  # Force remove just in case
            logging.info(f"Removed container: {container_name}")
        except Exception as e:
            logging.error(
                f"Error during cleanup of container {container_name}: {e}",
                exc_info=True,
            )
            pytest.fail(
                f"Failed during cleanup of {container_name}"
            )  # Fail test if cleanup errors
    else:
        logging.info(f"Cleanup: Container {container_name} not found.")


def test_start_instance_effect(docker_client, cleanup_test_container):
    """Tests the effect of starting an instance (container creation and running state)."""
    logging.info(f"--- Test: Starting instance {TEST_CONTAINER_NAME_API} ---")

    # Ensure image exists (fail fast if not)
    try:
        docker_client.images.get(DOCKER_IMAGE_NAME)
        logging.info(f"Required image {DOCKER_IMAGE_NAME} found.")
    except docker.errors.ImageNotFound:
        pytest.fail(f"Docker image {DOCKER_IMAGE_NAME} not found. Build it first.")

    # Simulate the start action: Run the container
    logging.info(
        f"Simulating start: Running container {TEST_CONTAINER_NAME_API} from image {DOCKER_IMAGE_NAME} mapping {TEST_HOST_PORT_API}:8080"
    )
    try:
        container = docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME_API,
            ports={"8080/tcp": TEST_HOST_PORT_API},
            # Add any other necessary options like volumes, environment vars etc.
        )
        logging.info(
            f"Container {TEST_CONTAINER_NAME_API} created with ID: {container.short_id}"
        )

        # Give container a moment to start up
        time.sleep(5)  # Adjust if startup takes longer

        # Verify container exists and is running
        container.reload()
        assert (
            container is not None
        ), f"Container {TEST_CONTAINER_NAME_API} should exist after run."
        assert (
            container.status == "running"
        ), f"Container {TEST_CONTAINER_NAME_API} should be running, but status is {container.status}"
        logging.info(f"Verified: Container {TEST_CONTAINER_NAME_API} is running.")

    except Exception as e:
        logging.error(
            f"Error running container {TEST_CONTAINER_NAME_API}: {e}", exc_info=True
        )
        pytest.fail(f"Failed to run container {TEST_CONTAINER_NAME_API}: {e}")


def test_stop_instance_effect(docker_client, cleanup_test_container):
    """Tests the effect of stopping an instance (container status change)."""
    logging.info(f"--- Test: Stopping instance {TEST_CONTAINER_NAME_API} ---")

    # --- Setup: Ensure container is running ---
    logging.info(
        f"Setup: Ensuring container {TEST_CONTAINER_NAME_API} is running for stop test."
    )
    try:
        container = docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME_API,
            ports={"8080/tcp": TEST_HOST_PORT_API},
        )
        time.sleep(5)  # Wait for startup
        container.reload()
        if container.status != "running":
            pytest.fail(
                f"Setup failed: Container {TEST_CONTAINER_NAME_API} is not running ({container.status})"
            )
        logging.info(f"Setup: Container {TEST_CONTAINER_NAME_API} is running.")
    except Exception as e:
        pytest.fail(
            f"Setup failed: Could not start container {TEST_CONTAINER_NAME_API}: {e}"
        )
    # ----------------------------------------

    # Simulate the stop action
    logging.info(f"Simulating stop: Stopping container {TEST_CONTAINER_NAME_API}")
    try:
        container.stop(timeout=10)
        logging.info(f"Stop command issued for {TEST_CONTAINER_NAME_API}")

        # Verify container exists and is stopped
        container.reload()  # Reload state from daemon
        assert (
            container is not None
        ), f"Container {TEST_CONTAINER_NAME_API} should still exist after stop."
        assert (
            container.status == "exited"
        ), f"Container {TEST_CONTAINER_NAME_API} should be stopped (exited), but status is {container.status}"
        logging.info(
            f"Verified: Container {TEST_CONTAINER_NAME_API} is stopped (status: {container.status})."
        )

    except Exception as e:
        logging.error(
            f"Error stopping container {TEST_CONTAINER_NAME_API}: {e}", exc_info=True
        )
        pytest.fail(f"Failed to stop container {TEST_CONTAINER_NAME_API}: {e}")


def test_start_already_running_instance(docker_client, cleanup_test_container):
    """Tests starting an instance that is already running."""
    logging.info(
        f"--- Test: Starting already running instance {TEST_CONTAINER_NAME_API} ---"
    )

    # Setup: Ensure container is running
    try:
        container = docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME_API,
            ports={"8080/tcp": TEST_HOST_PORT_API},
        )
        time.sleep(5)  # Wait for startup
        container.reload()
        if container.status != "running":
            pytest.fail(
                f"Setup failed: Container {TEST_CONTAINER_NAME_API} is not running ({container.status})"
            )
        logging.info(f"Setup: Container {TEST_CONTAINER_NAME_API} is running.")
    except Exception as e:
        pytest.fail(
            f"Setup failed: Could not start container {TEST_CONTAINER_NAME_API}: {e}"
        )

    # Try to start the container again (should be idempotent)
    logging.info(
        f"Simulating start on already running container: {TEST_CONTAINER_NAME_API}"
    )
    try:
        # Get the container again to ensure we have the latest state
        container = docker_client.containers.get(TEST_CONTAINER_NAME_API)

        # Verify it's still running
        assert (
            container.status == "running"
        ), f"Container should be running, but status is {container.status}"

        # Starting an already running container should be a no-op
        # In a real API, this would return success without doing anything
        logging.info(
            f"Verified: Container {TEST_CONTAINER_NAME_API} is already running."
        )

    except Exception as e:
        logging.error(
            f"Error checking already running container {TEST_CONTAINER_NAME_API}: {e}",
            exc_info=True,
        )
        pytest.fail(
            f"Failed to verify already running container {TEST_CONTAINER_NAME_API}: {e}"
        )


def test_stop_already_stopped_instance(docker_client, cleanup_test_container):
    """Tests stopping an instance that is already stopped."""
    logging.info(
        f"--- Test: Stopping already stopped instance {TEST_CONTAINER_NAME_API} ---"
    )

    # Setup: Create and then stop a container
    try:
        container = docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME_API,
            ports={"8080/tcp": TEST_HOST_PORT_API},
        )
        time.sleep(2)  # Brief wait for startup

        # Stop the container
        container.stop(timeout=5)
        time.sleep(2)  # Wait for stop to complete

        container.reload()
        if container.status != "exited":
            pytest.fail(
                f"Setup failed: Container {TEST_CONTAINER_NAME_API} is not stopped ({container.status})"
            )
        logging.info(f"Setup: Container {TEST_CONTAINER_NAME_API} is stopped.")
    except Exception as e:
        pytest.fail(
            f"Setup failed: Could not create and stop container {TEST_CONTAINER_NAME_API}: {e}"
        )

    # Try to stop the container again (should be idempotent)
    logging.info(
        f"Simulating stop on already stopped container: {TEST_CONTAINER_NAME_API}"
    )
    try:
        # Get the container again to ensure we have the latest state
        container = docker_client.containers.get(TEST_CONTAINER_NAME_API)

        # Verify it's still stopped
        assert (
            container.status == "exited"
        ), f"Container should be stopped, but status is {container.status}"

        # Stopping an already stopped container should be a no-op
        # In a real API, this would return success without doing anything
        container.stop(timeout=5)  # This should be a no-op

        container.reload()
        assert (
            container.status == "exited"
        ), f"Container should still be stopped after second stop, but status is {container.status}"
        logging.info(
            f"Verified: Container {TEST_CONTAINER_NAME_API} remains stopped after second stop command."
        )

    except Exception as e:
        logging.error(
            f"Error checking already stopped container {TEST_CONTAINER_NAME_API}: {e}",
            exc_info=True,
        )
        pytest.fail(
            f"Failed to verify already stopped container {TEST_CONTAINER_NAME_API}: {e}"
        )


def test_delete_instance_effect(docker_client, cleanup_test_container):
    """Tests the effect of deleting an instance (container removal)."""
    logging.info(f"--- Test: Deleting instance {TEST_CONTAINER_NAME_API} ---")

    # Setup: Create a container (can be running or stopped)
    try:
        container = docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME_API,
            ports={"8080/tcp": TEST_HOST_PORT_API},
        )
        time.sleep(2)  # Brief wait for startup
        container.reload()
        logging.info(
            f"Setup: Container {TEST_CONTAINER_NAME_API} created with status {container.status}."
        )
    except Exception as e:
        pytest.fail(
            f"Setup failed: Could not create container {TEST_CONTAINER_NAME_API}: {e}"
        )

    # Simulate delete action (remove the container)
    logging.info(f"Simulating delete: Removing container {TEST_CONTAINER_NAME_API}")
    try:
        container.remove(force=True)  # Force remove to handle running containers

        # Verify container is gone
        container_after = _get_container(docker_client, TEST_CONTAINER_NAME_API)
        assert (
            container_after is None
        ), f"Container {TEST_CONTAINER_NAME_API} should be removed after delete"
        logging.info(
            f"Verified: Container {TEST_CONTAINER_NAME_API} was successfully removed."
        )

    except Exception as e:
        logging.error(
            f"Error removing container {TEST_CONTAINER_NAME_API}: {e}", exc_info=True
        )
        pytest.fail(f"Failed to remove container {TEST_CONTAINER_NAME_API}: {e}")
