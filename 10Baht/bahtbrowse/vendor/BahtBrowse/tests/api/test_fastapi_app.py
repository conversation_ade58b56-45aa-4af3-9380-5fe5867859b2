import asyncio
import logging
import os
import socket
import time

import docker
import pytest
from fastapi.testclient import TestClient

# --- App Import --- #
# Assuming api/bahtbrowse/app.py is mounted at /app/api/bahtbrowse/app.py
# Use a path relative to the container's working directory (/app)
try:
    from api.bahtbrowse.app import app
except ImportError as e:
    # Add more debug info
    logging.exception(
        f"ImportError: {e}. Check api/bahtbrowse/app.py exists and is valid Python."
    )
    # Keep the dummy app for pytest discovery
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.bahtbrowse.app. Using dummy app. API tests will fail."
    )

# --- Docker Setup --- #

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
# Use an ID that exists in instances.json
TEST_INSTANCE_ID = "1"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"
# Use the corresponding port from instances.json
TEST_HOST_PORT = 6080
TEST_INSTANCE_URL = f"http://localhost:{TEST_HOST_PORT}"

NONEXISTENT_INSTANCE_ID = "nonexistent-999"
NONEXISTENT_CONTAINER_NAME = f"bahtbrowser-{NONEXISTENT_INSTANCE_ID}"


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        logging.info("Docker client connected successfully.")
        # Ensure the required image exists before tests run
        try:
            client.images.get(DOCKER_IMAGE_NAME)
            logging.info(f"Required image {DOCKER_IMAGE_NAME} found.")
        except docker.errors.ImageNotFound:
            pytest.fail(
                f"Required Docker image {DOCKER_IMAGE_NAME} not found. Build it first."
            )
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon or find image: {e}")


@pytest.fixture(scope="function")
def cleanup_test_container(docker_client):
    """Ensures the test container is removed before and after a test."""
    # Run before test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)
    yield
    # Run after test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)


def _get_container(docker_client, container_name):
    try:
        return docker_client.containers.get(container_name)
    except docker.errors.NotFound:
        return None
    except Exception as e:
        logging.exception(f"Error getting container {container_name}: {e}")
        return None


def _remove_container_if_exists(docker_client, container_name):
    container = _get_container(docker_client, container_name)
    if container:
        try:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
        except Exception as e:
            logging.error(
                f"Error during cleanup of container {container_name}: {e}",
                exc_info=True,
            )
            # Don't fail the test for cleanup errors, just log
    else:
        logging.info(f"Cleanup: Container {container_name} not found.")


# --- Test Client --- #


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


# --- API Tests --- #


def test_get_instances(client):
    logging.info("--- Test: GET /api/instances ---")
    response = client.get("/api/instances")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

    # Assert based on the content of instances.json
    assert len(data) == 3  # Expecting 3 instances from the file
    assert data[0]["id"] == "1"
    assert data[0]["name"] == "Instance 1"
    assert data[0]["description"] == "SOC Operator Browser"
    assert (
        data[0]["instanceUrl"] == "http://localhost:6080/"
    )  # Pydantic adds trailing slash

    assert data[1]["id"] == "2"
    assert data[1]["instanceUrl"] == "http://localhost:6081/"

    assert data[2]["id"] == "3"
    assert data[2]["instanceUrl"] == "http://localhost:6082/"

    logging.info(f"Received and validated {len(data)} instances.")


def test_start_instance(client, docker_client, cleanup_test_container):
    """Tests starting an instance via API using dynamic port assignment."""
    logging.info(
        f"--- Test: POST /api/instances/{TEST_INSTANCE_ID}/start (dynamic port) --- (Container: {TEST_CONTAINER_NAME})"
    )

    # Ensure container does not exist initially
    assert (
        _get_container(docker_client, TEST_CONTAINER_NAME) is None
    ), "Container should not exist before start"

    # Call Start Endpoint
    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")
    logging.info(
        f"Start response status: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200  # Expect success
    data = response.json()
    assert data["success"] is True
    assert "details" in data
    assert "port" in data["details"]
    assigned_port = data["details"]["port"]
    assert isinstance(assigned_port, int)
    # Check if port is within the expected dynamic range
    port_range_start = int(os.getenv("BAHTBROWSE_PORT_RANGE_START", "6100"))
    port_range_end = int(os.getenv("BAHTBROWSE_PORT_RANGE_END", "6199"))
    assert port_range_start <= assigned_port <= port_range_end
    logging.info(f"Start API call successful, received dynamic port: {assigned_port}")

    # Verify container exists and is running using Docker
    logging.info("Waiting for container to potentially start...")
    time.sleep(5)  # Give container time to start
    container = _get_container(docker_client, TEST_CONTAINER_NAME)
    assert (
        container is not None
    ), f"Container {TEST_CONTAINER_NAME} should exist after start API call"
    container.reload()  # Refresh container state
    assert (
        container.status == "running"
    ), f"Container {TEST_CONTAINER_NAME} status should be running, was {container.status}"
    logging.info(f"Verified container {TEST_CONTAINER_NAME} exists and is running.")

    # Verify Port Mapping
    port_mapping = container.ports.get("8080/tcp")
    assert port_mapping is not None, "Port mapping for 8080/tcp should exist"
    assert len(port_mapping) > 0, "Port mapping list should not be empty"
    assert port_mapping[0].get("HostPort") == str(
        assigned_port
    ), f"Container host port mapping should be {assigned_port}"
    logging.info(f"Verified container port mapping 8080/tcp -> {assigned_port}")

    # Test starting an already running instance (should succeed and return existing port)
    logging.info(
        f"--- Test: POST /api/instances/{TEST_INSTANCE_ID}/start (already running) ---"
    )
    response_rerun = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")
    assert response_rerun.status_code == 200
    data_rerun = response_rerun.json()
    assert data_rerun["success"] is True
    assert (
        data_rerun.get("details", {}).get("port") == assigned_port
    )  # Should return the same port
    logging.info(
        "Start API call on running instance successful and returned correct port."
    )
    container.reload()
    assert container.status == "running", "Container should still be running"


def test_stop_instance(client, docker_client, cleanup_test_container):
    logging.info(
        f"--- Test: POST /api/instances/{TEST_INSTANCE_ID}/stop --- (Container: {TEST_CONTAINER_NAME})"
    )

    # Setup: Start the container directly using Docker
    logging.info("Setup: Starting container directly for stop test.")
    try:
        docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME,
            ports={"8080/tcp": TEST_HOST_PORT},
        )
        time.sleep(5)
        container_pre = _get_container(docker_client, TEST_CONTAINER_NAME)
        assert (
            container_pre and container_pre.status == "running"
        ), "Setup failed: Container not running"
    except Exception as e:
        pytest.fail(f"Setup failed for stop test: {e}")

    # Call the stop API endpoint
    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
    logging.info(f"Stop response status: {response.status_code}, body: {response.text}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert TEST_CONTAINER_NAME in data["message"]
    logging.info("Stop API call successful.")

    # Verify container exists and is stopped using Docker
    logging.info("Waiting for container to potentially stop...")
    time.sleep(5)  # Give container time to stop
    container_post = _get_container(docker_client, TEST_CONTAINER_NAME)
    assert (
        container_post is not None
    ), f"Container {TEST_CONTAINER_NAME} should still exist after stop API call"
    container_post.reload()  # Refresh container state
    assert (
        container_post.status == "exited"
    ), f"Container {TEST_CONTAINER_NAME} status should be exited, was {container_post.status}"
    logging.info(f"Verified container {TEST_CONTAINER_NAME} is stopped.")

    # Test stopping an already stopped instance (should probably succeed idempotently)
    logging.info(
        f"--- Test: POST /api/instances/{TEST_INSTANCE_ID}/stop (already stopped) ---"
    )
    response_rerun = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
    assert response_rerun.status_code == 200
    data_rerun = response_rerun.json()
    assert data_rerun["success"] is True
    # Message might indicate it was already stopped
    logging.info("Stop API call on stopped instance successful (idempotent check).")
    container_post.reload()
    assert container_post.status == "exited", "Container should still be stopped"


def test_get_instance_status(client, docker_client, cleanup_test_container):
    logging.info(
        f"--- Test: GET /api/instances/{TEST_INSTANCE_ID}/status --- (Container: {TEST_CONTAINER_NAME})"
    )

    # Test status when stopped (should be stopped or unknown/error if not found by API)
    response_stopped = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(
        f"Status response (no container): {response_stopped.status_code}, body: {response_stopped.text}"
    )
    assert response_stopped.status_code == 200  # API should handle not found gracefully
    assert response_stopped.json()["status"] == "stopped"
    logging.info("Verified status is 'stopped' when container doesn't exist.")

    # Setup: Start the container directly
    logging.info("Setup: Starting container for status test.")
    try:
        docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME,
            ports={"8080/tcp": TEST_HOST_PORT},
        )
        time.sleep(5)
        container = _get_container(docker_client, TEST_CONTAINER_NAME)
        assert (
            container and container.status == "running"
        ), "Setup failed: Container not running"
    except Exception as e:
        pytest.fail(f"Setup failed for status test: {e}")

    # Test status when running
    response_running = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(
        f"Status response (running): {response_running.status_code}, body: {response_running.text}"
    )
    assert response_running.status_code == 200
    assert response_running.json()["status"] == "running"
    logging.info("Verified status is 'running'.")

    # Stop the container directly
    container = _get_container(docker_client, TEST_CONTAINER_NAME)
    container.stop()
    time.sleep(2)
    container.reload()
    assert (
        container.status == "exited"
    ), f"Container should be stopped, but status is {container.status}"

    # Test status when stopped (exited)
    response_exited = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(
        f"Status response (stopped): {response_exited.status_code}, body: {response_exited.text}"
    )
    assert response_exited.status_code == 200
    assert response_exited.json()["status"] == "stopped"
    logging.info("Verified status is 'stopped' after container exited.")


def test_get_all_statuses(client, docker_client, cleanup_test_container):
    logging.info(
        f"--- Test: GET /api/instances/statuses --- (Container: {TEST_CONTAINER_NAME})"
    )

    # Test with no instances running
    response_none = client.get("/api/instances/statuses")
    logging.info(
        f"All statuses response (none running): {response_none.status_code}, body: {response_none.text}"
    )
    assert response_none.status_code == 200
    statuses_none = response_none.json()
    assert isinstance(statuses_none, dict)
    # Check if TEST_INSTANCE_ID is present and stopped, or absent
    assert (
        statuses_none.get(TEST_INSTANCE_ID, {"status": "stopped"})["status"]
        == "stopped"
    )
    logging.info("Verified status list when no containers running.")

    # Setup: Start the test container
    logging.info("Setup: Starting container for all statuses test.")
    try:
        docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME,
            ports={"8080/tcp": TEST_HOST_PORT},
        )
        time.sleep(5)
        container = _get_container(docker_client, TEST_CONTAINER_NAME)
        assert (
            container and container.status == "running"
        ), "Setup failed: Container not running"
    except Exception as e:
        pytest.fail(f"Setup failed for all statuses test: {e}")

    # Test with one instance running
    response_one = client.get("/api/instances/statuses")
    logging.info(
        f"All statuses response (one running): {response_one.status_code}, body: {response_one.text}"
    )
    assert response_one.status_code == 200
    statuses_one = response_one.json()
    assert isinstance(statuses_one, dict)
    assert statuses_one.get(TEST_INSTANCE_ID, {}).get("status") == "running"
    logging.info("Verified status list with one container running.")

    # Stop the container and check statuses again
    container = _get_container(docker_client, TEST_CONTAINER_NAME)
    container.stop()
    time.sleep(2)
    container.reload()
    assert (
        container.status == "exited"
    ), f"Container should be stopped, but status is {container.status}"

    # Test with one instance stopped
    response_stopped = client.get("/api/instances/statuses")
    logging.info(
        f"All statuses response (one stopped): {response_stopped.status_code}, body: {response_stopped.text}"
    )
    assert response_stopped.status_code == 200
    statuses_stopped = response_stopped.json()
    assert isinstance(statuses_stopped, dict)
    assert statuses_stopped.get(TEST_INSTANCE_ID, {}).get("status") == "stopped"
    logging.info("Verified status list with one container stopped.")


def test_delete_instance(client, docker_client, cleanup_test_container):
    logging.info(
        f"--- Test: DELETE /api/instances/{TEST_INSTANCE_ID} --- (Container: {TEST_CONTAINER_NAME})"
    )

    # Setup: Start the container directly
    logging.info("Setup: Starting container for delete test.")
    try:
        docker_client.containers.run(
            DOCKER_IMAGE_NAME,
            detach=True,
            name=TEST_CONTAINER_NAME,
            ports={"8080/tcp": TEST_HOST_PORT},
        )
        time.sleep(5)
        container = _get_container(docker_client, TEST_CONTAINER_NAME)
        assert container is not None, "Setup failed: Container not created"
        assert (
            container.status == "running"
        ), f"Setup failed: Container not running, status is {container.status}"
    except Exception as e:
        pytest.fail(f"Setup failed for delete test: {e}")

    # Call the delete API endpoint
    response = client.delete(f"/api/instances/{TEST_INSTANCE_ID}")
    logging.info(f"Delete response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    logging.info("Delete API call successful.")

    # Verify container is removed using Docker
    time.sleep(2)  # Give time for container to be removed
    container_after = _get_container(docker_client, TEST_CONTAINER_NAME)
    assert container_after is None, "Container should be removed after delete API call"
    logging.info(f"Verified container {TEST_CONTAINER_NAME} is removed.")

    # Test deleting non-existent instance
    logging.info(
        f"--- Test: DELETE /api/instances/{NONEXISTENT_INSTANCE_ID} --- (Container: {NONEXISTENT_CONTAINER_NAME})"
    )
    response_nonexistent = client.delete(f"/api/instances/{NONEXISTENT_INSTANCE_ID}")
    logging.info(
        f"Delete non-existent response: {response_nonexistent.status_code}, body: {response_nonexistent.text}"
    )
    assert response_nonexistent.status_code == 404  # Expect Not Found
    data_nonexistent = response_nonexistent.json()
    assert data_nonexistent["success"] is False
    logging.info("Verified deleting non-existent instance returns 404.")


def test_get_available_port(client):
    """Tests the endpoint for finding an available port."""
    logging.info("--- Test: GET /api/ports/available ---")

    # Call the endpoint
    response = client.get("/api/instances/ports/available")
    logging.info(
        f"GET /api/ports/available status: {response.status_code}, response: {response.text}"
    )

    # Assert success and structure
    assert response.status_code == 200
    data = response.json()
    assert "port" in data
    assert isinstance(data["port"], int)

    # Assert port is within a reasonable range (as defined in main.py defaults or env vars)
    port = data["port"]
    port_range_start = int(os.getenv("BAHTBROWSE_PORT_RANGE_START", "6100"))
    port_range_end = int(os.getenv("BAHTBROWSE_PORT_RANGE_END", "6199"))
    assert port_range_start <= port <= port_range_end
    logging.info(
        f"Received available port: {port}. Validated range [{port_range_start}-{port_range_end}]."
    )

    # Try to bind to the returned port to double-check (might have race conditions)
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        s.bind(("127.0.0.1", port))
        logging.info(
            f"Successfully bound to reported available port {port} for verification."
        )
        s.close()
    except OSError as e:
        pytest.fail(
            f"Port {port} reported as available but failed to bind locally: {e}"
        )
    except Exception as e:
        pytest.fail(f"Unexpected error binding to reported port {port}: {e}")


# Add test for case where no ports are available (requires manually occupying ports)
# @pytest.mark.skip(reason="Need helper to occupy ports")
# def test_get_available_port_none_available(client):
#     pass


@pytest.mark.asyncio()  # Mark test as async
async def test_start_multiple_instances(client, docker_client):
    """Tests starting multiple instances concurrently, checking for unique ports."""
    num_instances = 10
    instance_ids = [str(i) for i in range(1, num_instances + 1)]  # Use IDs 1 through 10
    container_names = [f"bahtbrowser-{id}" for id in instance_ids]
    logging.info(
        f"--- Test: Starting {num_instances} instances concurrently ({instance_ids}) ---"
    )

    # Ensure containers don't exist initially
    for name in container_names:
        _remove_container_if_exists(docker_client, name)
        assert (
            _get_container(docker_client, name) is None
        ), f"Container {name} should not exist before start"

    # Define async function to call the API endpoint
    async def start_one_instance(instance_id):
        loop = asyncio.get_event_loop()
        # TestClient is synchronous, run its requests in a thread pool
        response = await loop.run_in_executor(
            None,
            lambda: client.post(f"/api/instances/{instance_id}/start"),
        )
        return instance_id, response

    # Run start requests concurrently
    tasks = [start_one_instance(id) for id in instance_ids]
    results = await asyncio.gather(*tasks)

    assigned_ports = set()
    failed_starts = []

    # Process results
    for instance_id, response in results:
        logging.info(
            f"Instance {instance_id} start response status: {response.status_code}"
        )
        if response.status_code == 200:
            data = response.json()
            if data["success"] and "details" in data and "port" in data["details"]:
                port = data["details"]["port"]
                assigned_ports.add(port)
                logging.info(
                    f"Instance {instance_id} started successfully on port {port}."
                )
            else:
                failed_starts.append(
                    (
                        instance_id,
                        f"API reported failure: {data.get('message', 'Unknown')}",
                    )
                )
        else:
            failed_starts.append(
                (instance_id, f"API returned status {response.status_code}")
            )

    # Assert all started successfully
    assert len(failed_starts) == 0, f"Failed to start instances: {failed_starts}"
    # Assert all ports are unique
    assert (
        len(assigned_ports) == num_instances
    ), f"Expected {num_instances} unique ports, but got {len(assigned_ports)}: {assigned_ports}"

    # Verify ports are within range (check first assigned port as example)
    port_range_start = int(os.getenv("BAHTBROWSE_PORT_RANGE_START", "6100"))
    port_range_end = int(os.getenv("BAHTBROWSE_PORT_RANGE_END", "6199"))
    if assigned_ports:
        first_port = next(iter(assigned_ports))
        assert (
            port_range_start <= first_port <= port_range_end
        ), f"Assigned port {first_port} outside range [{port_range_start}-{port_range_end}]"

    logging.info(
        f"Successfully started {num_instances} instances with unique ports: {sorted(list(assigned_ports))}"
    )

    # Verify containers are running
    logging.info("Verifying all container statuses...")
    time.sleep(5)  # Give containers time to settle
    running_containers = 0
    for name in container_names:
        container = _get_container(docker_client, name)
        if container and container.status == "running":
            running_containers += 1
        else:
            logging.error(
                f"Container {name} not found or not running (status: {container.status if container else 'Not Found'})"
            )

    assert (
        running_containers == num_instances
    ), f"Expected {num_instances} running containers, but found {running_containers}"
    logging.info(f"Verified {running_containers} containers are running.")

    # Cleanup
    logging.info("Cleaning up test containers...")
    for name in container_names:
        _remove_container_if_exists(docker_client, name)
    logging.info("Cleanup complete.")


# ... (other skipped tests) ...
