import json
import logging
import os
import shutil
import tempfile
import time
from unittest.mock import patch

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "config-test-1"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(docker_client, container_name):
    """Helper to remove a container if it exists."""
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_container(docker_client):
    """Ensures the test container is removed before and after a test."""
    # Run before test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)
    yield
    # Run after test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)


@pytest.fixture()
def temp_config_file():
    """Creates a temporary configuration file."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a temporary config file
    config_file = os.path.join(temp_dir, "instances.json")

    # Write initial config
    with open(config_file, "w") as f:
        json.dump(
            [
                {
                    "id": TEST_INSTANCE_ID,
                    "name": "Config Test Instance",
                    "description": "Test instance for configuration tests",
                    "port": 6090,
                },
            ],
            f,
        )

    yield config_file

    # Cleanup
    shutil.rmtree(temp_dir)


def test_configuration_file_changes(
    client, docker_client, cleanup_test_container, temp_config_file
):
    """Test how the system handles changes to the configuration file."""
    logging.info("--- Test: Configuration file changes ---")

    # Patch the app to use our temporary config file
    with patch("api.main.INSTANCES_FILE", temp_config_file):
        # Verify the test instance is available
        response = client.get("/api/instances")
        logging.info(
            f"Get instances response: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200
        instances = response.json()
        assert any(instance["id"] == TEST_INSTANCE_ID for instance in instances)

        # Start the instance
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")
        logging.info(f"Start response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200

        # Wait for instance to start
        time.sleep(5)

        # Verify instance is running
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        logging.info(f"Status response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200
        assert response.json()["status"] == "running"

        # Update the configuration file with a new instance
        with open(temp_config_file, "w") as f:
            json.dump(
                [
                    {
                        "id": TEST_INSTANCE_ID,
                        "name": "Updated Config Test Instance",
                        "description": "Updated test instance",
                        "port": 6091,
                    },
                    {
                        "id": "config-test-2",
                        "name": "New Config Test Instance",
                        "description": "New test instance",
                        "port": 6092,
                    },
                ],
                f,
            )

        # Verify the updated configuration is reflected
        response = client.get("/api/instances")
        logging.info(
            f"Get instances after update response: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200
        updated_instances = response.json()
        assert len(updated_instances) == 2
        assert any(instance["id"] == "config-test-2" for instance in updated_instances)

        # Verify the original instance is still running
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        logging.info(
            f"Status response after update: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200
        assert response.json()["status"] == "running"

        # Stop the instance
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
        logging.info(f"Stop response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200


def test_invalid_configuration(
    client, docker_client, cleanup_test_container, temp_config_file
):
    """Test behaviour with invalid configuration files."""
    logging.info("--- Test: Invalid configuration ---")

    # Write invalid JSON to the config file
    with open(temp_config_file, "w") as f:
        f.write("This is not valid JSON")

    # Patch the app to use our invalid config file
    with patch("api.main.INSTANCES_FILE", temp_config_file):
        # Verify the API handles invalid config gracefully
        response = client.get("/api/instances")
        logging.info(
            f"Get instances with invalid config response: {response.status_code}, body: {response.text}"
        )
        assert response.status_code in [200, 500]

        if response.status_code == 200:
            # If the API returns 200, it should return an empty list or error message
            instances = response.json()
            assert isinstance(instances, list) and len(instances) == 0
        else:
            # If the API returns 500, it should include an error message
            data = response.json()
            assert "error" in data

    # Write a config with missing required fields
    with open(temp_config_file, "w") as f:
        json.dump(
            [
                {
                    "id": TEST_INSTANCE_ID,
                    # Missing "name" field
                    "description": "Test instance with missing fields",
                    # Missing "port" field
                },
            ],
            f,
        )

    # Patch the app to use our config with missing fields
    with patch("api.main.INSTANCES_FILE", temp_config_file):
        # Verify the API handles missing fields gracefully
        response = client.get("/api/instances")
        logging.info(
            f"Get instances with missing fields response: {response.status_code}, body: {response.text}"
        )
        assert response.status_code in [200, 422]

        if response.status_code == 200:
            # If the API returns 200, it should either skip the invalid instance or fill in defaults
            instances = response.json()
            if len(instances) > 0:
                instance = next(
                    (i for i in instances if i["id"] == TEST_INSTANCE_ID), None
                )
                if instance:
                    assert "name" in instance  # Should have default name
                    assert "port" in instance  # Should have default port

        # Try to start the instance with missing fields
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")
        logging.info(
            f"Start with missing fields response: {response.status_code}, body: {response.text}"
        )
        # The API should either handle this gracefully or return an error
        assert response.status_code in [200, 400, 422, 500]


def test_dynamic_configuration_updates(
    client, docker_client, cleanup_test_container, temp_config_file
):
    """Test updating configuration while the system is running."""
    logging.info("--- Test: Dynamic configuration updates ---")

    # Patch the app to use our temporary config file
    with patch("api.main.INSTANCES_FILE", temp_config_file):
        # Start the instance
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")
        logging.info(f"Start response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200

        # Wait for instance to start
        time.sleep(5)

        # Update the configuration file while the instance is running
        with open(temp_config_file, "w") as f:
            json.dump(
                [
                    {
                        "id": TEST_INSTANCE_ID,
                        "name": "Updated While Running",
                        "description": "Updated while the instance is running",
                        "port": 6093,
                    },
                ],
                f,
            )

        # Verify the instance is still running
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        logging.info(
            f"Status response after dynamic update: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200
        assert response.json()["status"] == "running"

        # Verify the updated configuration is reflected in the instances list
        response = client.get("/api/instances")
        logging.info(
            f"Get instances after dynamic update response: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200
        instances = response.json()
        instance = next((i for i in instances if i["id"] == TEST_INSTANCE_ID), None)
        assert instance is not None
        assert instance["name"] == "Updated While Running"

        # Stop the instance
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
        logging.info(f"Stop response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200
