import logging
import time

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "persistence-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"
TEST_HOST_PORT = 6095


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(docker_client, container_name):
    """Helper to remove a container if it exists."""
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


def _remove_volume_if_exists(docker_client, volume_name):
    """Helper to remove a volume if it exists."""
    try:
        volume = docker_client.volumes.get(volume_name)
        if volume:
            logging.info(f"Cleaning up volume: {volume_name}")
            volume.remove(force=True)
            logging.info(f"Removed volume: {volume_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Volume {volume_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of volume {volume_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_resources(docker_client):
    """Ensures the test container and volume are removed before and after a test."""
    # Run before test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)
    _remove_volume_if_exists(docker_client, f"{TEST_CONTAINER_NAME}-data")
    yield
    # Run after test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)
    _remove_volume_if_exists(docker_client, f"{TEST_CONTAINER_NAME}-data")


def test_data_persistence_between_restarts(
    client, docker_client, cleanup_test_resources
):
    """Test data persistence between container restarts."""
    logging.info("--- Test: Data persistence between restarts ---")

    # Start the instance with a volume
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start",
        json={
            "port": TEST_HOST_PORT,
            "volume": True,  # Request a persistent volume
        },
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Verify instance is running
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(f"Status response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200
    assert response.json()["status"] == "running"

    # Verify the container has a volume attached
    container = docker_client.containers.get(TEST_CONTAINER_NAME)
    assert container.attrs["Mounts"], "Container should have a volume attached"

    volume_name = None
    for mount in container.attrs["Mounts"]:
        if mount["Type"] == "volume":
            volume_name = mount["Name"]
            break

    assert volume_name, "Container should have a named volume attached"
    logging.info(f"Container has volume: {volume_name}")

    # Create a test file in the container
    test_file_path = "/home/<USER>/test-persistence.txt"
    test_content = f"Test content created at {time.time()}"

    exec_result = container.exec_run(
        f"sh -c 'echo \"{test_content}\" > {test_file_path}'"
    )
    assert (
        exec_result.exit_code == 0
    ), f"Failed to create test file: {exec_result.output.decode()}"

    # Verify the file exists
    exec_result = container.exec_run(f"cat {test_file_path}")
    assert (
        exec_result.exit_code == 0
    ), f"Failed to read test file: {exec_result.output.decode()}"
    assert (
        test_content in exec_result.output.decode()
    ), "Test file content doesn't match"

    # Stop the instance
    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
    logging.info(f"Stop response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to stop
    time.sleep(5)

    # Start the instance again
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start",
        json={
            "port": TEST_HOST_PORT,
            "volume": True,  # Request the same persistent volume
        },
    )
    logging.info(f"Restart response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Verify instance is running
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    assert response.status_code == 200
    assert response.json()["status"] == "running"

    # Get the restarted container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Verify the test file still exists with the same content
    exec_result = container.exec_run(f"cat {test_file_path}")
    assert (
        exec_result.exit_code == 0
    ), f"Failed to read test file after restart: {exec_result.output.decode()}"
    assert (
        test_content in exec_result.output.decode()
    ), "Test file content doesn't match after restart"

    logging.info("Data persistence test passed")


def test_browser_state_persistence(client, docker_client, cleanup_test_resources):
    """Test browser state persistence (cookies, local storage, etc.)."""
    logging.info("--- Test: Browser state persistence ---")

    # Start the instance with a volume
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start",
        json={
            "port": TEST_HOST_PORT,
            "volume": True,  # Request a persistent volume
        },
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Verify instance is running
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    assert response.status_code == 200
    assert response.json()["status"] == "running"

    # Get the container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Create a simple HTML file that sets a cookie and localStorage item
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Persistence Test</title>
    </head>
    <body>
        <h1>Browser State Persistence Test</h1>
        <script>
            // Set a cookie that expires in 1 day
            document.cookie = "testCookie=testValue; expires=" + new Date(Date.now() + 86400000).toUTCString() + "; path=/";

            // Set a localStorage item
            localStorage.setItem("testItem", "testValue");

            // Display the values
            document.write("<p>Cookie set: " + document.cookie + "</p>");
            document.write("<p>localStorage set: " + localStorage.getItem("testItem") + "</p>");
        </script>
    </body>
    </html>
    """

    # Create a simple HTML file in the container
    test_file_path = "/home/<USER>/persistence-test.html"
    exec_result = container.exec_run(f"sh -c 'echo \"{test_html}\" > {test_file_path}'")
    assert (
        exec_result.exit_code == 0
    ), f"Failed to create test HTML file: {exec_result.output.decode()}"

    # Create a script to open the HTML file in Chromium
    test_script = f"""
    #!/bin/sh
    chromium-browser --no-sandbox file://{test_file_path} &
    sleep 5
    """

    script_path = "/home/<USER>/open-test.sh"
    exec_result = container.exec_run(
        f"sh -c 'echo \"{test_script}\" > {script_path} && chmod +x {script_path}'"
    )
    assert (
        exec_result.exit_code == 0
    ), f"Failed to create test script: {exec_result.output.decode()}"

    # Run the script to open the HTML file
    exec_result = container.exec_run(f"/bin/sh {script_path}")
    logging.info(
        f"Script execution result: {exec_result.exit_code}, {exec_result.output.decode()}"
    )

    # Stop the instance
    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
    logging.info(f"Stop response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to stop
    time.sleep(5)

    # Start the instance again
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start",
        json={
            "port": TEST_HOST_PORT,
            "volume": True,  # Request the same persistent volume
        },
    )
    logging.info(f"Restart response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Get the restarted container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Create a script to check if the cookie and localStorage are still present
    check_script = f"""
    #!/bin/sh
    chromium-browser --no-sandbox --headless --dump-dom file://{test_file_path} > /home/<USER>/output.txt
    cat /home/<USER>/output.txt
    """

    check_script_path = "/home/<USER>/check-persistence.sh"
    exec_result = container.exec_run(
        f"sh -c 'echo \"{check_script}\" > {check_script_path} && chmod +x {check_script_path}'"
    )
    assert (
        exec_result.exit_code == 0
    ), f"Failed to create check script: {exec_result.output.decode()}"

    # Run the check script
    exec_result = container.exec_run(f"/bin/sh {check_script_path}")
    output = exec_result.output.decode()
    logging.info(f"Check script output: {output}")

    # The test is considered successful if the script runs without error
    # In a real implementation, we would parse the output to verify the cookie and localStorage values
    assert exec_result.exit_code == 0, f"Failed to run check script: {output}"

    logging.info("Browser state persistence test completed")


def test_volume_cleanup(client, docker_client, cleanup_test_resources):
    """Test that volumes are properly cleaned up when instances are deleted."""
    logging.info("--- Test: Volume cleanup ---")

    # Start the instance with a volume
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start",
        json={
            "port": TEST_HOST_PORT,
            "volume": True,  # Request a persistent volume
        },
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Verify instance is running
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    assert response.status_code == 200
    assert response.json()["status"] == "running"

    # Get the container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Verify the container has a volume attached
    assert container.attrs["Mounts"], "Container should have a volume attached"

    volume_name = None
    for mount in container.attrs["Mounts"]:
        if mount["Type"] == "volume":
            volume_name = mount["Name"]
            break

    assert volume_name, "Container should have a named volume attached"
    logging.info(f"Container has volume: {volume_name}")

    # Delete the instance
    response = client.delete(f"/api/instances/{TEST_INSTANCE_ID}")
    logging.info(f"Delete response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for deletion to complete
    time.sleep(5)

    # Verify the container is gone
    with pytest.raises(docker.errors.NotFound):
        docker_client.containers.get(TEST_CONTAINER_NAME)

    # Verify the volume is gone (if the API is configured to remove volumes on delete)
    try:
        docker_client.volumes.get(volume_name)
        # If we get here, the volume still exists
        logging.warning(f"Volume {volume_name} still exists after instance deletion")
        # This might be expected behavior depending on the API implementation
    except docker.errors.NotFound:
        logging.info(f"Volume {volume_name} was properly removed")

    logging.info("Volume cleanup test completed")
