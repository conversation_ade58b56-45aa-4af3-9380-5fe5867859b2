import logging
from unittest.mock import MagicMock, mock_open, patch

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import (
        app,
        get_container,
        get_docker_client,
        read_instance_definitions,
    )
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "error-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with Test<PERSON>lient(app) as c:
        yield c


def test_read_instance_definitions_file_not_found():
    """Test read_instance_definitions when file is not found."""
    logging.info("--- Test: read_instance_definitions file not found ---")

    # Mock the Path object itself instead of just the is_file method
    mock_path = MagicMock()
    mock_path.is_file.return_value = False

    with patch("api.main.INSTANCES_CONFIG_PATH", mock_path):
        with TestClient(app) as client:
            # Call the endpoint that uses read_instance_definitions
            response = client.get("/api/instances")

            # Should return an empty list, not an error
            assert response.status_code == 200
            assert response.json() == []


def test_read_instance_definitions_json_decode_error():
    """Test read_instance_definitions with invalid JSON."""
    logging.info("--- Test: read_instance_definitions JSON decode error ---")

    # Mock the Path object itself
    mock_path = MagicMock()
    mock_path.is_file.return_value = True

    with patch("api.main.INSTANCES_CONFIG_PATH", mock_path):
        with patch("builtins.open", mock_open(read_data='{"invalid": json')):
            with TestClient(app) as client:
                # Call the endpoint that uses read_instance_definitions
                response = client.get("/api/instances")

                # Should return a 500 error
                assert response.status_code == 500
                assert (
                    "Invalid JSON" in response.json()["detail"]
                    or "JSONDecodeError" in response.json()["detail"]
                )


def test_read_instance_definitions_general_error():
    """Test read_instance_definitions with a general error."""
    logging.info("--- Test: read_instance_definitions general error ---")

    # Mock the Path object itself
    mock_path = MagicMock()
    mock_path.is_file.return_value = True

    # Create a more specific mock for open that only affects our target file
    original_open = open

    def mock_open_effect(*args, **kwargs):
        # If this is our config file being opened, raise an exception
        if args and str(mock_path) in str(args[0]):
            raise Exception("Test exception")
        # Otherwise, use the original open function
        return original_open(*args, **kwargs)

    with patch("api.main.INSTANCES_CONFIG_PATH", mock_path):
        with patch("builtins.open", side_effect=mock_open_effect):
            with TestClient(app) as client:
                # Call the endpoint that uses read_instance_definitions
                response = client.get("/api/instances")

                # Should return a 500 error
                assert response.status_code == 500
                assert "Test exception" in response.json()["detail"]


def test_get_docker_client_error():
    """Test get_docker_client when Docker daemon is unavailable."""
    logging.info("--- Test: get_docker_client error ---")

    # First, we need to mock get_instance_definition to return a valid instance
    mock_instance = MagicMock()
    mock_instance.id = TEST_INSTANCE_ID

    # Mock docker.from_env to raise an exception
    with patch("api.main.get_instance_definition", return_value=mock_instance):
        with patch(
            "docker.from_env",
            side_effect=docker.errors.DockerException("Docker daemon unavailable"),
        ):
            with TestClient(app) as client:
                # Call an endpoint that uses get_docker_client
                response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")

                # Should return a 500 error
                assert response.status_code == 500
                assert "Docker daemon" in response.json()["detail"]


def test_get_container_not_found():
    """Test get_container when container is not found."""
    logging.info("--- Test: get_container not found ---")

    # Mock Docker client
    mock_client = MagicMock()
    mock_client.containers.get.side_effect = docker.errors.NotFound(
        "Container not found"
    )

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_client):
        with TestClient(app) as client:
            # Call an endpoint that uses get_container
            response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")

            # Should return a status of "stopped"
            assert response.status_code == 200
            assert response.json()["status"] == "stopped"


def test_get_container_error():
    """Test get_container when an error occurs."""
    logging.info("--- Test: get_container error ---")

    # Mock Docker client
    mock_client = MagicMock()
    mock_client.containers.get.side_effect = Exception("Test exception")

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_client):
        with TestClient(app) as client:
            # Call an endpoint that uses get_container
            response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")

            # Should return a 500 error
            assert response.status_code == 500
            assert "Test exception" in response.json()["detail"]


def test_image_not_found():
    """Test behavior when Docker image is not found."""
    logging.info("--- Test: Image not found ---")

    # Mock Docker client
    mock_client = MagicMock()
    mock_client.images.get.side_effect = docker.errors.ImageNotFound("Image not found")

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_client):
        # Mock get_instance_definition to return a valid definition
        mock_definition = MagicMock()
        mock_definition.id = TEST_INSTANCE_ID
        with patch("api.main.get_instance_definition", return_value=mock_definition):
            with TestClient(app) as client:
                # Call the start endpoint
                response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")

                # Should return a 500 error
                assert response.status_code == 500
                assert (
                    "image" in response.json()["detail"].lower()
                    and "not found" in response.json()["detail"].lower()
                )


def test_instance_not_found():
    """Test behavior when instance definition is not found."""
    logging.info("--- Test: Instance not found ---")

    # Mock get_instance_definition to return None
    with patch("api.main.get_instance_definition", return_value=None):
        with TestClient(app) as client:
            # Call the start endpoint
            response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")

            # Should return a 404 error
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()


def test_container_start_error():
    """Test behavior when container fails to start."""
    logging.info("--- Test: Container start error ---")

    # Mock Docker client
    mock_client = MagicMock()
    mock_client.images.get.return_value = MagicMock()  # Image exists
    mock_client.containers.run.side_effect = docker.errors.APIError(
        "Failed to start container"
    )

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_client):
        # Mock get_instance_definition to return a valid definition
        mock_definition = MagicMock()
        mock_definition.id = TEST_INSTANCE_ID
        with patch("api.main.get_instance_definition", return_value=mock_definition):
            # Mock find_available_port to return a valid port
            with patch("api.main.find_available_port", return_value=6080):
                with TestClient(app) as client:
                    # Call the start endpoint
                    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")

                    # Should return a 500 error
                    assert response.status_code == 500
                    assert "Failed to start container" in response.json()["detail"]


def test_no_available_ports():
    """Test behavior when no ports are available."""
    logging.info("--- Test: No available ports ---")

    # Mock Docker client
    mock_client = MagicMock()
    mock_client.images.get.return_value = MagicMock()  # Image exists

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_client):
        # Mock get_instance_definition to return a valid definition
        mock_definition = MagicMock()
        mock_definition.id = TEST_INSTANCE_ID
        with patch("api.main.get_instance_definition", return_value=mock_definition):
            # Mock find_available_port to return None (no ports available)
            with patch("api.main.find_available_port", return_value=None):
                with TestClient(app) as client:
                    # Call the start endpoint
                    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")

                    # Should return a 503 error
                    assert response.status_code == 503
                    assert "No available ports" in response.json()["detail"]
