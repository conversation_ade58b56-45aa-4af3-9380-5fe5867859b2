import asyncio
import logging
import time
from concurrent.futures import ThreadPoolExecutor

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_PREFIX = "concurrent-test-"
MAX_CONCURRENT_OPERATIONS = 10


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(docker_client, container_name):
    """Helper to remove a container if it exists."""
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_containers(docker_client):
    """Ensures all test containers are removed before and after a test."""
    # Run before test
    for i in range(MAX_CONCURRENT_OPERATIONS):
        container_name = f"bahtbrowser-{TEST_INSTANCE_PREFIX}{i}"
        _remove_container_if_exists(docker_client, container_name)
    yield
    # Run after test
    for i in range(MAX_CONCURRENT_OPERATIONS):
        container_name = f"bahtbrowser-{TEST_INSTANCE_PREFIX}{i}"
        _remove_container_if_exists(docker_client, container_name)


def test_concurrent_start_operations(client, docker_client, cleanup_test_containers):
    """Test behaviour when multiple start operations happen concurrently."""
    logging.info("--- Test: Concurrent start operations ---")

    # Create a list of instance IDs
    instance_ids = [
        f"{TEST_INSTANCE_PREFIX}{i}" for i in range(MAX_CONCURRENT_OPERATIONS)
    ]

    # Function to start an instance
    def start_instance(instance_id):
        response = client.post(f"/api/instances/{instance_id}/start")
        return instance_id, response

    # Start instances concurrently
    results = []
    with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_OPERATIONS) as executor:
        futures = [
            executor.submit(start_instance, instance_id) for instance_id in instance_ids
        ]
        for future in futures:
            instance_id, response = future.result()
            logging.info(
                f"Start response for {instance_id}: {response.status_code}, body: {response.text}"
            )
            results.append((instance_id, response))

    # Verify results
    success_count = 0
    for instance_id, response in results:
        # The API should either succeed or return a conflict error
        assert response.status_code in [200, 409, 503]
        if response.status_code == 200:
            data = response.json()
            assert data["success"] is True
            assert "details" in data
            assert "port" in data["details"]
            success_count += 1

    # At least some operations should succeed
    assert success_count > 0, "No concurrent start operations succeeded"
    logging.info(f"Successfully started {success_count} instances concurrently")

    # Verify containers are running
    running_containers = 0
    for instance_id in instance_ids:
        container_name = f"bahtbrowser-{instance_id}"
        try:
            container = docker_client.containers.get(container_name)
            if container.status == "running":
                running_containers += 1
        except docker.errors.NotFound:
            pass

    assert (
        running_containers == success_count
    ), f"Expected {success_count} running containers, but found {running_containers}"
    logging.info(f"Verified {running_containers} containers are running")


def test_concurrent_mixed_operations(client, docker_client, cleanup_test_containers):
    """Test behaviour when different operations (start, stop, status) happen concurrently."""
    logging.info("--- Test: Concurrent mixed operations ---")

    # Create a list of instance IDs
    instance_ids = [
        f"{TEST_INSTANCE_PREFIX}{i}" for i in range(MAX_CONCURRENT_OPERATIONS)
    ]

    # Start half of the instances first
    for i in range(MAX_CONCURRENT_OPERATIONS // 2):
        instance_id = instance_ids[i]
        response = client.post(f"/api/instances/{instance_id}/start")
        logging.info(
            f"Initial start response for {instance_id}: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200

    # Wait for instances to start
    time.sleep(5)

    # Define operations to perform concurrently
    operations = []
    for i, instance_id in enumerate(instance_ids):
        if i < MAX_CONCURRENT_OPERATIONS // 2:
            # For already started instances, stop them
            operations.append(("stop", instance_id))
        else:
            # For not yet started instances, start them
            operations.append(("start", instance_id))

    # Add status checks for all instances
    for instance_id in instance_ids:
        operations.append(("status", instance_id))

    # Function to perform an operation
    def perform_operation(op_type, instance_id):
        if op_type == "start":
            response = client.post(f"/api/instances/{instance_id}/start")
        elif op_type == "stop":
            response = client.post(f"/api/instances/{instance_id}/stop")
        elif op_type == "status":
            response = client.get(f"/api/instances/{instance_id}/status")
        return op_type, instance_id, response

    # Perform operations concurrently
    results = []
    with ThreadPoolExecutor(max_workers=len(operations)) as executor:
        futures = [
            executor.submit(perform_operation, op_type, instance_id)
            for op_type, instance_id in operations
        ]
        for future in futures:
            op_type, instance_id, response = future.result()
            logging.info(
                f"{op_type.capitalize()} response for {instance_id}: {response.status_code}, body: {response.text}"
            )
            results.append((op_type, instance_id, response))

    # Verify results
    for op_type, instance_id, response in results:
        # All operations should return a valid response
        assert response.status_code in [200, 404, 409, 503]
        if response.status_code == 200:
            data = response.json()
            if op_type in ["start", "stop"]:
                assert data["success"] is True
            elif op_type == "status":
                assert "status" in data

    logging.info("Successfully performed mixed concurrent operations")


@pytest.mark.asyncio()
async def test_concurrent_status_checks(client, docker_client, cleanup_test_containers):
    """Test behaviour when many status checks happen concurrently."""
    logging.info("--- Test: Concurrent status checks ---")

    # Create a list of instance IDs
    instance_ids = [
        f"{TEST_INSTANCE_PREFIX}{i}" for i in range(MAX_CONCURRENT_OPERATIONS)
    ]

    # Start all instances first
    for instance_id in instance_ids:
        response = client.post(f"/api/instances/{instance_id}/start")
        logging.info(
            f"Initial start response for {instance_id}: {response.status_code}, body: {response.text}"
        )

    # Wait for instances to start
    time.sleep(5)

    # Define async function to check status
    async def check_status(instance_id):
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None,
            lambda: client.get(f"/api/instances/{instance_id}/status"),
        )
        return instance_id, response

    # Perform many status checks concurrently (more than the number of instances)
    tasks = []
    for _ in range(50):  # 50 concurrent status checks
        for instance_id in instance_ids:
            tasks.append(check_status(instance_id))

    results = await asyncio.gather(*tasks)

    # Verify results
    for instance_id, response in results:
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        # Status should be either "running" or "starting"
        assert data["status"] in ["running", "starting"]

    logging.info("Successfully performed concurrent status checks")


def test_rate_limiting(client):
    """Test behaviour under high request rates."""
    logging.info("--- Test: Rate limiting ---")

    # Perform many requests in a short time
    num_requests = 100
    responses = []

    start_time = time.time()
    for i in range(num_requests):
        response = client.get("/api/instances/statuses")
        responses.append(response)
    end_time = time.time()

    # Calculate requests per second
    duration = end_time - start_time
    requests_per_second = num_requests / duration
    logging.info(
        f"Performed {num_requests} requests in {duration:.2f} seconds ({requests_per_second:.2f} req/s)"
    )

    # Check if any requests were rate limited
    rate_limited = sum(1 for r in responses if r.status_code == 429)
    success = sum(1 for r in responses if r.status_code == 200)

    logging.info(f"Rate limited responses: {rate_limited}/{num_requests}")
    logging.info(f"Successful responses: {success}/{num_requests}")

    # Either some requests should be rate limited, or all should succeed
    assert (
        rate_limited > 0 or success == num_requests
    ), "Expected either rate limiting or all requests to succeed"
