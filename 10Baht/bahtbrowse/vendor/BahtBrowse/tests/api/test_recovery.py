import json
import logging
import os
import shutil
import signal
import tempfile
import time
from unittest.mock import patch

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "recovery-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"
TEST_HOST_PORT = 6096


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(docker_client, container_name):
    """Helper to remove a container if it exists."""
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_container(docker_client):
    """Ensures the test container is removed before and after a test."""
    # Run before test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)
    yield
    # Run after test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)


@pytest.fixture()
def temp_config_file():
    """Creates a temporary configuration file."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a temporary config file
    config_file = os.path.join(temp_dir, "instances.json")

    # Write initial config
    with open(config_file, "w") as f:
        json.dump(
            [
                {
                    "id": TEST_INSTANCE_ID,
                    "name": "Recovery Test Instance",
                    "description": "Test instance for recovery tests",
                    "port": TEST_HOST_PORT,
                },
            ],
            f,
        )

    yield config_file

    # Cleanup
    shutil.rmtree(temp_dir)


@pytest.mark.skipif(
    os.environ.get("RUN_RECOVERY_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Recovery tests are disabled. Set RUN_RECOVERY_TESTS=true to enable.",
)
def test_recovery_after_docker_daemon_restart(
    client, docker_client, cleanup_test_container
):
    """Test system recovery after Docker daemon restart."""
    logging.info("--- Test: Recovery after Docker daemon restart ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Verify instance is running
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    assert response.status_code == 200
    assert response.json()["status"] == "running"

    # Simulate Docker daemon restart
    # Note: This requires root privileges and will affect the entire Docker system
    # In a real test environment, this should be done in a controlled way
    logging.info("Simulating Docker daemon restart...")

    try:
        # Option 1: Use systemctl (requires root)
        # subprocess.run(["sudo", "systemctl", "restart", "docker"], check=True)

        # Option 2: Mock the Docker client to simulate a restart
        with patch("docker.from_env") as mock_docker:
            # Configure the mock to raise an exception and then recover
            mock_client = docker_client
            original_containers_get = mock_client.containers.get

            # First call raises an exception (daemon down)
            mock_client.containers.get = lambda name: exec(
                'raise docker.errors.APIError("daemon is down")'
            )

            # Check status during "outage"
            with patch("api.main.docker.from_env", return_value=mock_client):
                response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
                logging.info(
                    f"Status during outage: {response.status_code}, body: {response.text}"
                )

                # API should handle the outage gracefully
                assert response.status_code in [200, 503]
                if response.status_code == 200:
                    assert response.json()["status"] in ["unknown", "error"]

            # Restore normal operation (daemon back up)
            mock_client.containers.get = original_containers_get

            # Check status after "recovery"
            response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
            logging.info(
                f"Status after recovery: {response.status_code}, body: {response.text}"
            )
            assert response.status_code == 200
            assert response.json()["status"] == "running"

        logging.info("Docker daemon restart recovery test passed")

    except Exception as e:
        logging.exception(f"Error during Docker daemon restart test: {e}")
        pytest.skip(f"Docker daemon restart test failed: {e}")


@pytest.mark.skipif(
    os.environ.get("RUN_RECOVERY_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Recovery tests are disabled. Set RUN_RECOVERY_TESTS=true to enable.",
)
def test_recovery_after_api_restart(
    client, docker_client, cleanup_test_container, temp_config_file
):
    """Test system recovery after API service restart."""
    logging.info("--- Test: Recovery after API restart ---")

    # Patch the app to use our temporary config file
    with patch("api.main.INSTANCES_FILE", temp_config_file):
        # Start the instance
        response = client.post(
            f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
        )
        logging.info(f"Start response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200

        # Wait for instance to start
        time.sleep(10)

        # Verify instance is running
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        assert response.status_code == 200
        assert response.json()["status"] == "running"

        # Simulate API service restart
        logging.info("Simulating API service restart...")

        # In a real test, we would restart the actual API service
        # For this test, we'll simulate it by creating a new client
        with TestClient(app) as new_client:
            # Check status after "restart"
            response = new_client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
            logging.info(
                f"Status after API restart: {response.status_code}, body: {response.text}"
            )
            assert response.status_code == 200
            assert response.json()["status"] == "running"

            # Verify we can still control the instance
            response = new_client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
            logging.info(
                f"Stop response after API restart: {response.status_code}, body: {response.text}"
            )
            assert response.status_code == 200

            # Wait for instance to stop
            time.sleep(5)

            # Verify instance is stopped
            response = new_client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
            assert response.status_code == 200
            assert response.json()["status"] == "stopped"

        logging.info("API service restart recovery test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_RECOVERY_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Recovery tests are disabled. Set RUN_RECOVERY_TESTS=true to enable.",
)
def test_recovery_after_container_crash(client, docker_client, cleanup_test_container):
    """Test system recovery after container crash."""
    logging.info("--- Test: Recovery after container crash ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Verify instance is running
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    assert response.status_code == 200
    assert response.json()["status"] == "running"

    # Get the container
    container = docker_client.containers.get(TEST_CONTAINER_NAME)

    # Simulate container crash by killing the process
    logging.info("Simulating container crash...")
    container.kill(signal=signal.SIGKILL)

    # Wait for Docker to register the container as stopped
    time.sleep(5)

    # Verify the container is stopped
    container.reload()
    assert (
        container.status != "running"
    ), f"Container should be stopped, but status is {container.status}"

    # Check if the API correctly reports the status
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    logging.info(
        f"Status after container crash: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200
    assert response.json()["status"] in [
        "stopped",
        "error",
    ], f"Expected status 'stopped' or 'error', got {response.json()['status']}"

    # Try to restart the instance
    response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")
    logging.info(
        f"Restart response after crash: {response.status_code}, body: {response.text}"
    )
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Verify instance is running again
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    assert response.status_code == 200
    assert response.json()["status"] == "running"

    logging.info("Container crash recovery test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_RECOVERY_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Recovery tests are disabled. Set RUN_RECOVERY_TESTS=true to enable.",
)
def test_recovery_after_config_corruption(
    client, docker_client, cleanup_test_container, temp_config_file
):
    """Test system recovery after configuration file corruption."""
    logging.info("--- Test: Recovery after config corruption ---")

    # Patch the app to use our temporary config file
    with patch("api.main.INSTANCES_FILE", temp_config_file):
        # Start the instance
        response = client.post(
            f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
        )
        logging.info(f"Start response: {response.status_code}, body: {response.text}")
        assert response.status_code == 200

        # Wait for instance to start
        time.sleep(10)

        # Verify instance is running
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        assert response.status_code == 200
        assert response.json()["status"] == "running"

        # Corrupt the configuration file
        logging.info("Corrupting configuration file...")
        with open(temp_config_file, "w") as f:
            f.write("This is not valid JSON")

        # Check if the API handles the corrupted config gracefully
        response = client.get("/api/instances")
        logging.info(
            f"Get instances with corrupted config: {response.status_code}, body: {response.text}"
        )

        # The API should either return an error or an empty list
        assert response.status_code in [200, 500]

        # Check if we can still get the status of the running instance
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        logging.info(
            f"Status with corrupted config: {response.status_code}, body: {response.text}"
        )

        # The API should still be able to get the status from Docker
        assert response.status_code == 200
        assert response.json()["status"] == "running"

        # Restore the configuration file
        logging.info("Restoring configuration file...")
        with open(temp_config_file, "w") as f:
            json.dump(
                [
                    {
                        "id": TEST_INSTANCE_ID,
                        "name": "Recovery Test Instance",
                        "description": "Test instance for recovery tests",
                        "port": TEST_HOST_PORT,
                    },
                ],
                f,
            )

        # Check if the API recovers
        response = client.get("/api/instances")
        logging.info(
            f"Get instances after config restore: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200
        instances = response.json()
        assert any(instance["id"] == TEST_INSTANCE_ID for instance in instances)

        # Verify we can still control the instance
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")
        logging.info(
            f"Stop response after config restore: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200

        logging.info("Configuration corruption recovery test passed")


@pytest.mark.skipif(
    os.environ.get("RUN_RECOVERY_TESTS", "").lower() not in ("true", "1", "yes"),
    reason="Recovery tests are disabled. Set RUN_RECOVERY_TESTS=true to enable.",
)
def test_recovery_after_network_partition(
    client, docker_client, cleanup_test_container
):
    """Test system recovery after network partition."""
    logging.info("--- Test: Recovery after network partition ---")

    # Start the instance
    response = client.post(
        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": TEST_HOST_PORT}
    )
    logging.info(f"Start response: {response.status_code}, body: {response.text}")
    assert response.status_code == 200

    # Wait for instance to start
    time.sleep(10)

    # Verify instance is running
    response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
    assert response.status_code == 200
    assert response.json()["status"] == "running"

    # Simulate network partition by mocking the Docker client
    logging.info("Simulating network partition...")

    with patch("docker.from_env") as mock_docker:
        # Configure the mock to raise a connection error
        mock_client = docker_client
        original_containers_get = mock_client.containers.get

        # Mock connection error
        def mock_get(name):
            raise requests.exceptions.ConnectionError("Connection refused")

        mock_client.containers.get = mock_get

        # Check status during "network partition"
        with patch("api.main.docker.from_env", return_value=mock_client):
            response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
            logging.info(
                f"Status during network partition: {response.status_code}, body: {response.text}"
            )

            # API should handle the network partition gracefully
            assert response.status_code in [200, 503]
            if response.status_code == 200:
                assert response.json()["status"] in ["unknown", "error"]

        # Restore normal operation (network partition resolved)
        mock_client.containers.get = original_containers_get

        # Check status after "recovery"
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")
        logging.info(
            f"Status after network recovery: {response.status_code}, body: {response.text}"
        )
        assert response.status_code == 200
        assert response.json()["status"] == "running"

    logging.info("Network partition recovery test passed")
