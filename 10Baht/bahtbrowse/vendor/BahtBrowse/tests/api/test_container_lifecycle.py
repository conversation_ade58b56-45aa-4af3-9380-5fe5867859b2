import logging
from unittest.mock import MagicMock, PropertyMock, patch

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import Fast<PERSON>I

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "lifecycle-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture()
def mock_docker_client():
    """Provides a mocked Docker client."""
    mock_client = MagicMock()

    # Mock containers collection
    mock_containers = MagicMock()
    mock_client.containers = mock_containers

    # Mock images collection
    mock_images = MagicMock()
    mock_client.images = mock_images

    # Mock ping method
    mock_client.ping.return_value = True

    return mock_client


@pytest.fixture()
def mock_instance_definition():
    """Provides a mock instance definition."""
    mock_def = MagicMock()
    mock_def.id = TEST_INSTANCE_ID
    mock_def.name = "Lifecycle Test Instance"
    mock_def.description = "Test instance for container lifecycle tests"
    mock_def.port = 6080
    return mock_def


def test_container_create(client, mock_docker_client, mock_instance_definition):
    """Test container creation."""
    logging.info("--- Test: Container creation ---")

    # Mock Docker client
    mock_docker_client.images.get.return_value = MagicMock()  # Image exists

    # Mock container creation
    mock_container = MagicMock()
    type(mock_container).id = PropertyMock(return_value="mock-container-id")
    type(mock_container).name = PropertyMock(return_value=TEST_CONTAINER_NAME)
    type(mock_container).status = PropertyMock(return_value="created")
    mock_docker_client.containers.run.return_value = mock_container

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Mock get_instance_definition to return our mock definition
        with patch(
            "api.main.get_instance_definition", return_value=mock_instance_definition
        ):
            # Mock find_available_port to return a valid port
            with patch("api.main.find_available_port", return_value=6080):
                # Call the start endpoint
                response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")

                # Verify Docker client was used correctly
                mock_docker_client.images.get.assert_called_once_with(DOCKER_IMAGE_NAME)
                mock_docker_client.containers.run.assert_called_once()

                # Verify the container was created with the correct parameters
                run_args = mock_docker_client.containers.run.call_args[1]
                assert run_args["name"] == TEST_CONTAINER_NAME
                assert run_args["detach"] is True
                assert "6080/tcp" in run_args["ports"]

                # Should return success
                assert response.status_code == 200
                assert response.json()["success"] is True
                assert "details" in response.json()
                assert response.json()["details"]["port"] == 6080


def test_container_start_existing(client, mock_docker_client, mock_instance_definition):
    """Test starting an existing container."""
    logging.info("--- Test: Start existing container ---")

    # Mock Docker client
    mock_docker_client.images.get.return_value = MagicMock()  # Image exists

    # Mock existing container
    mock_container = MagicMock()
    type(mock_container).id = PropertyMock(return_value="mock-container-id")
    type(mock_container).name = PropertyMock(return_value=TEST_CONTAINER_NAME)
    type(mock_container).status = PropertyMock(return_value="exited")
    mock_docker_client.containers.get.return_value = mock_container

    # Mock container.start method
    mock_container.start.return_value = None

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Mock get_instance_definition to return our mock definition
        with patch(
            "api.main.get_instance_definition", return_value=mock_instance_definition
        ):
            # Call the start endpoint
            response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")

            # Verify Docker client was used correctly
            mock_docker_client.containers.get.assert_called_once_with(
                TEST_CONTAINER_NAME
            )
            mock_container.start.assert_called_once()

            # Should return success
            assert response.status_code == 200
            assert response.json()["success"] is True


def test_container_already_running(
    client, mock_docker_client, mock_instance_definition
):
    """Test starting a container that's already running."""
    logging.info("--- Test: Container already running ---")

    # Mock Docker client
    mock_docker_client.images.get.return_value = MagicMock()  # Image exists

    # Mock existing container
    mock_container = MagicMock()
    type(mock_container).id = PropertyMock(return_value="mock-container-id")
    type(mock_container).name = PropertyMock(return_value=TEST_CONTAINER_NAME)
    type(mock_container).status = PropertyMock(return_value="running")
    mock_docker_client.containers.get.return_value = mock_container

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Mock get_instance_definition to return our mock definition
        with patch(
            "api.main.get_instance_definition", return_value=mock_instance_definition
        ):
            # Call the start endpoint
            response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start")

            # Verify Docker client was used correctly
            mock_docker_client.containers.get.assert_called_once_with(
                TEST_CONTAINER_NAME
            )

            # Container.start should not be called
            mock_container.start.assert_not_called()

            # Should return success (or already running message)
            assert response.status_code == 200
            assert response.json()["success"] is True
            assert "already running" in response.json()["message"].lower()


def test_container_stop(client, mock_docker_client):
    """Test stopping a container."""
    logging.info("--- Test: Container stop ---")

    # Mock existing container
    mock_container = MagicMock()
    type(mock_container).id = PropertyMock(return_value="mock-container-id")
    type(mock_container).name = PropertyMock(return_value=TEST_CONTAINER_NAME)
    type(mock_container).status = PropertyMock(return_value="running")
    mock_docker_client.containers.get.return_value = mock_container

    # Mock container.stop method
    mock_container.stop.return_value = None

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Call the stop endpoint
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")

        # Verify Docker client was used correctly
        mock_docker_client.containers.get.assert_called_once_with(TEST_CONTAINER_NAME)
        mock_container.stop.assert_called_once()

        # Should return success
        assert response.status_code == 200
        assert response.json()["success"] is True


def test_container_stop_not_running(client, mock_docker_client):
    """Test stopping a container that's not running."""
    logging.info("--- Test: Container stop not running ---")

    # Mock Docker client to raise NotFound
    mock_docker_client.containers.get.side_effect = docker.errors.NotFound(
        "Container not found"
    )

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Call the stop endpoint
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/stop")

        # Verify Docker client was used correctly
        mock_docker_client.containers.get.assert_called_once_with(TEST_CONTAINER_NAME)

        # Should return success (container already stopped)
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert (
            "not running" in response.json()["message"].lower()
            or "already stopped" in response.json()["message"].lower()
        )


def test_container_delete(client, mock_docker_client):
    """Test deleting a container."""
    logging.info("--- Test: Container delete ---")

    # Mock existing container
    mock_container = MagicMock()
    type(mock_container).id = PropertyMock(return_value="mock-container-id")
    type(mock_container).name = PropertyMock(return_value=TEST_CONTAINER_NAME)
    type(mock_container).status = PropertyMock(return_value="exited")
    mock_docker_client.containers.get.return_value = mock_container

    # Mock container.remove method
    mock_container.remove.return_value = None

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Call the delete endpoint
        response = client.delete(f"/api/instances/{TEST_INSTANCE_ID}")

        # Verify Docker client was used correctly
        mock_docker_client.containers.get.assert_called_once_with(TEST_CONTAINER_NAME)
        mock_container.remove.assert_called_once()

        # Should return success
        assert response.status_code == 200
        assert response.json()["success"] is True


def test_container_delete_running(client, mock_docker_client):
    """Test deleting a running container."""
    logging.info("--- Test: Container delete running ---")

    # Mock existing container
    mock_container = MagicMock()
    type(mock_container).id = PropertyMock(return_value="mock-container-id")
    type(mock_container).name = PropertyMock(return_value=TEST_CONTAINER_NAME)
    type(mock_container).status = PropertyMock(return_value="running")
    mock_docker_client.containers.get.return_value = mock_container

    # Mock container methods
    mock_container.stop.return_value = None
    mock_container.remove.return_value = None

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Call the delete endpoint
        response = client.delete(f"/api/instances/{TEST_INSTANCE_ID}")

        # Verify Docker client was used correctly
        mock_docker_client.containers.get.assert_called_once_with(TEST_CONTAINER_NAME)
        mock_container.stop.assert_called_once()
        mock_container.remove.assert_called_once()

        # Should return success
        assert response.status_code == 200
        assert response.json()["success"] is True


def test_container_status(client, mock_docker_client):
    """Test getting container status."""
    logging.info("--- Test: Container status ---")

    # Mock existing container
    mock_container = MagicMock()
    type(mock_container).id = PropertyMock(return_value="mock-container-id")
    type(mock_container).name = PropertyMock(return_value=TEST_CONTAINER_NAME)
    type(mock_container).status = PropertyMock(return_value="running")
    mock_docker_client.containers.get.return_value = mock_container

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Call the status endpoint
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")

        # Verify Docker client was used correctly
        mock_docker_client.containers.get.assert_called_once_with(TEST_CONTAINER_NAME)

        # Should return running status
        assert response.status_code == 200
        assert response.json()["status"] == "running"


def test_container_status_not_found(client, mock_docker_client):
    """Test getting status of a non-existent container."""
    logging.info("--- Test: Container status not found ---")

    # Mock Docker client to raise NotFound
    mock_docker_client.containers.get.side_effect = docker.errors.NotFound(
        "Container not found"
    )

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Call the status endpoint
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")

        # Verify Docker client was used correctly
        mock_docker_client.containers.get.assert_called_once_with(TEST_CONTAINER_NAME)

        # Should return stopped status
        assert response.status_code == 200
        assert response.json()["status"] == "stopped"
