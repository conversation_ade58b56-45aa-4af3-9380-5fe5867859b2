import errno
import logging
from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app, find_available_port, is_port_available
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
TEST_INSTANCE_ID = "port-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


def test_is_port_available_success():
    """Test is_port_available when port is available."""
    logging.info("--- Test: is_port_available success ---")

    # Mock socket to simulate port availability
    mock_socket = MagicMock()
    mock_socket.bind.return_value = None

    with patch("socket.socket", return_value=mock_socket):
        # Call is_port_available directly
        result = is_port_available(6080)

        # Verify socket was used correctly
        mock_socket.bind.assert_called_once_with(("127.0.0.1", 6080))
        mock_socket.close.assert_called_once()

        # Should return True
        assert result is True


def test_is_port_available_in_use():
    """Test is_port_available when port is in use."""
    logging.info("--- Test: is_port_available in use ---")

    # Mock socket to simulate port in use
    mock_socket = MagicMock()
    mock_socket.bind.side_effect = OSError(errno.EADDRINUSE, "Address already in use")

    with patch("socket.socket", return_value=mock_socket):
        # Call is_port_available directly
        result = is_port_available(6080)

        # Verify socket was used correctly
        mock_socket.bind.assert_called_once_with(("127.0.0.1", 6080))
        mock_socket.close.assert_called_once()

        # Should return False
        assert result is False


def test_is_port_available_other_error():
    """Test is_port_available with other socket error."""
    logging.info("--- Test: is_port_available other error ---")

    # Mock socket to simulate other error
    mock_socket = MagicMock()
    mock_socket.bind.side_effect = OSError(errno.EINVAL, "Invalid argument")

    with patch("socket.socket", return_value=mock_socket):
        # Call is_port_available directly
        result = is_port_available(6080)

        # Verify socket was used correctly
        mock_socket.bind.assert_called_once_with(("127.0.0.1", 6080))
        mock_socket.close.assert_called_once()

        # Should return False
        assert result is False


def test_is_port_available_exception():
    """Test is_port_available with general exception."""
    logging.info("--- Test: is_port_available exception ---")

    # Mock socket to simulate exception
    mock_socket = MagicMock()
    mock_socket.bind.side_effect = Exception("Test exception")

    with patch("socket.socket", return_value=mock_socket):
        # Call is_port_available directly
        result = is_port_available(6080)

        # Verify socket was used correctly
        mock_socket.bind.assert_called_once_with(("127.0.0.1", 6080))
        mock_socket.close.assert_called_once()

        # Should return False
        assert result is False


def test_find_available_port_success():
    """Test find_available_port when ports are available."""
    logging.info("--- Test: find_available_port success ---")

    # Mock is_port_available to return True for port 6080 and False for others
    def mock_is_port_available(port, host="127.0.0.1"):
        return port == 6080

    with patch("api.main.is_port_available", side_effect=mock_is_port_available):
        # Call find_available_port directly
        port = find_available_port(6000, 6100)

        # Should return 6080
        assert port == 6080


def test_find_available_port_none_available():
    """Test find_available_port when no ports are available."""
    logging.info("--- Test: find_available_port none available ---")

    # Mock is_port_available to always return False
    with patch("api.main.is_port_available", return_value=False):
        # Call find_available_port directly
        port = find_available_port(6000, 6100)

        # Should return None
        assert port is None


def test_find_available_port_first_available():
    """Test find_available_port returns first available port."""
    logging.info("--- Test: find_available_port first available ---")

    # Mock is_port_available to return True for the first port
    with patch("api.main.is_port_available", return_value=True):
        # Call find_available_port directly
        port = find_available_port(6000, 6100)

        # Should return the first port in the range
        assert port == 6000


def test_find_available_port_specific_port():
    """Test find_available_port with a specific port."""
    logging.info("--- Test: find_available_port specific port ---")

    # Mock is_port_available to return True
    with patch("api.main.is_port_available", return_value=True):
        # Call find_available_port directly with a specific port
        port = find_available_port(6080, 6080)

        # Should return the specified port
        assert port == 6080


def test_find_available_port_invalid_range():
    """Test find_available_port with an invalid range."""
    logging.info("--- Test: find_available_port invalid range ---")

    # Call find_available_port directly with an invalid range
    port = find_available_port(6100, 6000)

    # Should return None
    assert port is None


def test_start_with_specific_port(client):
    """Test starting an instance with a specific port."""
    logging.info("--- Test: Start with specific port ---")

    # Mock Docker client
    mock_client = MagicMock()
    mock_client.images.get.return_value = MagicMock()  # Image exists
    mock_client.containers.run.return_value = MagicMock()  # Container created

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_client):
        # Mock get_instance_definition to return a valid definition
        mock_definition = MagicMock()
        mock_definition.id = TEST_INSTANCE_ID
        with patch("api.main.get_instance_definition", return_value=mock_definition):
            # Mock is_port_available to return True
            with patch("api.main.is_port_available", return_value=True):
                # Call the start endpoint with a specific port
                response = client.post(
                    f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": 6080}
                )

                # Verify the port was used
                run_args = mock_client.containers.run.call_args[1]
                assert run_args["ports"]["6080/tcp"] == 6080

                # Should return success
                assert response.status_code == 200
                assert response.json()["success"] is True
                assert response.json()["details"]["port"] == 6080


def test_start_with_unavailable_port(client):
    """Test starting an instance with an unavailable port."""
    logging.info("--- Test: Start with unavailable port ---")

    # Mock Docker client
    mock_client = MagicMock()
    mock_client.images.get.return_value = MagicMock()  # Image exists

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_client):
        # Mock get_instance_definition to return a valid definition
        mock_definition = MagicMock()
        mock_definition.id = TEST_INSTANCE_ID
        with patch("api.main.get_instance_definition", return_value=mock_definition):
            # Mock is_port_available to return False for the requested port
            with patch("api.main.is_port_available", return_value=False):
                # Mock find_available_port to return None (no ports available)
                with patch("api.main.find_available_port", return_value=None):
                    # Call the start endpoint with a specific port
                    response = client.post(
                        f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": 6080}
                    )

                    # Should return an error
                    assert response.status_code == 503
                    assert response.json()["success"] is False
                    assert (
                        "port" in response.json()["detail"].lower()
                        and "unavailable" in response.json()["detail"].lower()
                    )


def test_start_with_fallback_port(client):
    """Test starting an instance with a fallback port."""
    logging.info("--- Test: Start with fallback port ---")

    # Mock Docker client
    mock_client = MagicMock()
    mock_client.images.get.return_value = MagicMock()  # Image exists
    mock_client.containers.run.return_value = MagicMock()  # Container created

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_client):
        # Mock get_instance_definition to return a valid definition
        mock_definition = MagicMock()
        mock_definition.id = TEST_INSTANCE_ID
        with patch("api.main.get_instance_definition", return_value=mock_definition):
            # Mock is_port_available to return False for the requested port
            def mock_is_port_available(port, host="127.0.0.1"):
                return port != 6080

            with patch(
                "api.main.is_port_available", side_effect=mock_is_port_available
            ):
                # Call the start endpoint with a specific port
                response = client.post(
                    f"/api/instances/{TEST_INSTANCE_ID}/start", json={"port": 6080}
                )

                # Verify a different port was used
                run_args = mock_client.containers.run.call_args[1]
                assert run_args["ports"]["6080/tcp"] != 6080

                # Should return success
                assert response.status_code == 200
                assert response.json()["success"] is True
                assert response.json()["details"]["port"] != 6080
