import logging
import statistics
import time
from concurrent.futures import ThreadPoolExecutor

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_PREFIX = "perf-test-"
MAX_INSTANCES = 20  # Maximum number of instances to create for stress testing


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(docker_client, container_name):
    """Helper to remove a container if it exists."""
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_containers(docker_client):
    """Ensures all test containers are removed before and after a test."""
    # Run before test
    for i in range(MAX_INSTANCES):
        container_name = f"bahtbrowser-{TEST_INSTANCE_PREFIX}{i}"
        _remove_container_if_exists(docker_client, container_name)
    yield
    # Run after test
    for i in range(MAX_INSTANCES):
        container_name = f"bahtbrowser-{TEST_INSTANCE_PREFIX}{i}"
        _remove_container_if_exists(docker_client, container_name)


def test_api_response_time(client):
    """Test API response time under normal load."""
    logging.info("--- Test: API response time ---")

    # Test endpoints to measure
    endpoints = [
        "/api/instances",
        "/api/instances/statuses",
        f"/api/instances/{TEST_INSTANCE_PREFIX}0/status",
    ]

    # Number of requests per endpoint
    num_requests = 10

    results = {}

    for endpoint in endpoints:
        response_times = []

        for _ in range(num_requests):
            start_time = time.time()
            response = client.get(endpoint)
            end_time = time.time()

            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            response_times.append(response_time)

            # Ensure the request was successful
            assert response.status_code == 200

        # Calculate statistics
        avg_time = statistics.mean(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        p95_time = sorted(response_times)[int(num_requests * 0.95)]

        results[endpoint] = {
            "avg": avg_time,
            "min": min_time,
            "max": max_time,
            "p95": p95_time,
        }

        logging.info(
            f"Endpoint {endpoint}: Avg={avg_time:.2f}ms, Min={min_time:.2f}ms, Max={max_time:.2f}ms, P95={p95_time:.2f}ms"
        )

    # Assert reasonable response times (adjust thresholds as needed)
    for endpoint, stats in results.items():
        assert stats["avg"] < 500, f"Average response time for {endpoint} exceeds 500ms"
        assert stats["p95"] < 1000, f"P95 response time for {endpoint} exceeds 1000ms"

    logging.info("API response time test passed")


def test_load_testing(client):
    """Test API performance under load."""
    logging.info("--- Test: Load testing ---")

    # Test endpoint
    endpoint = "/api/instances/statuses"

    # Number of concurrent requests
    num_concurrent = 50

    # Number of requests per thread
    num_requests_per_thread = 10

    # Function to make requests
    def make_requests():
        response_times = []
        for _ in range(num_requests_per_thread):
            start_time = time.time()
            response = client.get(endpoint)
            end_time = time.time()

            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            response_times.append((response.status_code, response_time))

            # Small delay to prevent overwhelming the server
            time.sleep(0.1)

        return response_times

    # Make concurrent requests
    all_results = []
    with ThreadPoolExecutor(max_workers=num_concurrent) as executor:
        futures = [executor.submit(make_requests) for _ in range(num_concurrent)]
        for future in futures:
            all_results.extend(future.result())

    # Analyze results
    success_count = sum(1 for status, _ in all_results if status == 200)
    total_count = len(all_results)
    success_rate = success_count / total_count

    response_times = [time for _, time in all_results if time > 0]
    avg_time = statistics.mean(response_times) if response_times else 0
    p95_time = (
        sorted(response_times)[int(len(response_times) * 0.95)] if response_times else 0
    )

    logging.info(
        f"Load test results: Success rate={success_rate:.2%}, Avg time={avg_time:.2f}ms, P95 time={p95_time:.2f}ms"
    )

    # Assert reasonable performance
    assert success_rate >= 0.95, "Success rate under load is below 95%"
    assert avg_time < 1000, "Average response time under load exceeds 1000ms"

    logging.info("Load testing passed")


@pytest.mark.stress()
def test_stress_testing(client, docker_client, cleanup_test_containers):
    """Test system behaviour under stress (many instances)."""
    logging.info("--- Test: Stress testing ---")

    # Create a list of instance IDs
    instance_ids = [f"{TEST_INSTANCE_PREFIX}{i}" for i in range(MAX_INSTANCES)]

    # Function to start an instance
    def start_instance(instance_id):
        start_time = time.time()
        response = client.post(f"/api/instances/{instance_id}/start")
        end_time = time.time()

        return instance_id, response, end_time - start_time

    # Start instances sequentially
    started_instances = []
    for instance_id in instance_ids:
        logging.info(f"Starting instance {instance_id}")
        instance_id, response, duration = start_instance(instance_id)

        logging.info(
            f"Start response for {instance_id}: {response.status_code}, took {duration:.2f}s"
        )

        if response.status_code == 200:
            started_instances.append(instance_id)

        # If we can't start more instances, break
        if response.status_code in [503, 507]:
            logging.info(
                f"Cannot start more instances, stopping at {len(started_instances)} instances"
            )
            break

    # Verify we could start a reasonable number of instances
    assert len(started_instances) > 0, "Could not start any instances"
    logging.info(f"Successfully started {len(started_instances)} instances")

    # Wait for instances to start
    time.sleep(10)

    # Check status of all started instances
    running_instances = 0
    for instance_id in started_instances:
        response = client.get(f"/api/instances/{instance_id}/status")
        if response.status_code == 200 and response.json().get("status") == "running":
            running_instances += 1

    logging.info(
        f"{running_instances} out of {len(started_instances)} instances are running"
    )

    # Verify a reasonable number of instances are running
    assert running_instances > 0, "No instances are running"
    assert (
        running_instances >= len(started_instances) * 0.8
    ), "Less than 80% of started instances are running"

    # Stop all instances
    for instance_id in started_instances:
        logging.info(f"Stopping instance {instance_id}")
        response = client.post(f"/api/instances/{instance_id}/stop")
        logging.info(f"Stop response for {instance_id}: {response.status_code}")

    logging.info("Stress testing passed")


@pytest.mark.long_running()
def test_long_running_stability(client, docker_client, cleanup_test_containers):
    """Test stability over an extended period."""
    logging.info("--- Test: Long-running stability ---")

    # Start a few instances
    num_instances = 3
    instance_ids = [f"{TEST_INSTANCE_PREFIX}{i}" for i in range(num_instances)]

    # Start instances
    for instance_id in instance_ids:
        response = client.post(f"/api/instances/{instance_id}/start")
        logging.info(f"Start response for {instance_id}: {response.status_code}")
        assert response.status_code == 200

    # Wait for instances to start
    time.sleep(10)

    # Run for a longer period (e.g., 5 minutes)
    test_duration = 300  # seconds
    check_interval = 30  # seconds

    start_time = time.time()
    end_time = start_time + test_duration

    while time.time() < end_time:
        # Check status of all instances
        for instance_id in instance_ids:
            response = client.get(f"/api/instances/{instance_id}/status")
            logging.info(
                f"Status of {instance_id}: {response.status_code}, {response.json() if response.status_code == 200 else ''}"
            )
            assert response.status_code == 200
            assert response.json().get("status") == "running"

        # Check overall status
        response = client.get("/api/instances/statuses")
        assert response.status_code == 200

        # Wait for next check
        time.sleep(check_interval)

    # Verify all instances are still running after the test period
    for instance_id in instance_ids:
        response = client.get(f"/api/instances/{instance_id}/status")
        assert response.status_code == 200
        assert response.json().get("status") == "running"

    # Stop all instances
    for instance_id in instance_ids:
        response = client.post(f"/api/instances/{instance_id}/stop")
        logging.info(f"Stop response for {instance_id}: {response.status_code}")
        assert response.status_code == 200

    logging.info(f"Long-running stability test passed after {test_duration} seconds")
