import json
import logging
from unittest.mock import MagicMock, mock_open, patch

import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app, get_instance_definition, read_instance_definitions
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
TEST_INSTANCE_ID = "config-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


def test_read_instance_definitions_success():
    """Test read_instance_definitions with valid config."""
    logging.info("--- Test: read_instance_definitions success ---")

    # Create mock instance data
    mock_instance_data = json.dumps(
        [
            {
                "id": TEST_INSTANCE_ID,
                "name": "Config Test Instance",
                "description": "Test instance for config tests",
                "port": 6080,
            },
        ]
    )

    # Mock Path.is_file to return True
    with patch("api.main.INSTANCES_CONFIG_PATH.is_file", return_value=True):
        # Mock open to return our mock instance data
        with patch("builtins.open", mock_open(read_data=mock_instance_data)):
            # Call read_instance_definitions directly
            instances = read_instance_definitions()

            # Should return our mock instance
            assert len(instances) == 1
            assert instances[0].id == TEST_INSTANCE_ID
            assert instances[0].name == "Config Test Instance"
            assert instances[0].description == "Test instance for config tests"
            assert instances[0].port == 6080


def test_read_instance_definitions_file_not_found():
    """Test read_instance_definitions when file is not found."""
    logging.info("--- Test: read_instance_definitions file not found ---")

    # Mock Path.is_file to return False
    with patch("api.main.INSTANCES_CONFIG_PATH.is_file", return_value=False):
        # Call read_instance_definitions directly
        instances = read_instance_definitions()

        # Should return an empty list
        assert instances == []


def test_read_instance_definitions_invalid_json():
    """Test read_instance_definitions with invalid JSON."""
    logging.info("--- Test: read_instance_definitions invalid JSON ---")

    # Mock Path.is_file to return True
    with patch("api.main.INSTANCES_CONFIG_PATH.is_file", return_value=True):
        # Mock open to return invalid JSON
        with patch("builtins.open", mock_open(read_data='{"invalid": json')):
            # Call read_instance_definitions directly
            with pytest.raises(json.JSONDecodeError):
                read_instance_definitions()


def test_read_instance_definitions_empty_file():
    """Test read_instance_definitions with empty file."""
    logging.info("--- Test: read_instance_definitions empty file ---")

    # Mock Path.is_file to return True
    with patch("api.main.INSTANCES_CONFIG_PATH.is_file", return_value=True):
        # Mock open to return empty string
        with patch("builtins.open", mock_open(read_data="")):
            # Call read_instance_definitions directly
            with pytest.raises(json.JSONDecodeError):
                read_instance_definitions()


def test_read_instance_definitions_invalid_structure():
    """Test read_instance_definitions with invalid structure."""
    logging.info("--- Test: read_instance_definitions invalid structure ---")

    # Create mock instance data with invalid structure
    mock_instance_data = json.dumps(
        {
            "id": TEST_INSTANCE_ID,
            "name": "Config Test Instance",
        }
    )  # Should be a list, not an object

    # Mock Path.is_file to return True
    with patch("api.main.INSTANCES_CONFIG_PATH.is_file", return_value=True):
        # Mock open to return our mock instance data
        with patch("builtins.open", mock_open(read_data=mock_instance_data)):
            # Call read_instance_definitions directly
            instances = read_instance_definitions()

            # Should return an empty list
            assert instances == []


def test_get_instance_definition_found():
    """Test get_instance_definition when instance is found."""
    logging.info("--- Test: get_instance_definition found ---")

    # Create mock instance data
    mock_instances = [
        MagicMock(id=TEST_INSTANCE_ID, name="Config Test Instance"),
        MagicMock(id="other-id", name="Other Instance"),
    ]

    # Mock read_instance_definitions to return our mock instances
    with patch("api.main.read_instance_definitions", return_value=mock_instances):
        # Call get_instance_definition directly
        instance = get_instance_definition(TEST_INSTANCE_ID)

        # Should return the matching instance
        assert instance is not None
        assert instance.id == TEST_INSTANCE_ID
        assert instance.name == "Config Test Instance"


def test_get_instance_definition_not_found():
    """Test get_instance_definition when instance is not found."""
    logging.info("--- Test: get_instance_definition not found ---")

    # Create mock instance data
    mock_instances = [
        MagicMock(id="other-id", name="Other Instance"),
    ]

    # Mock read_instance_definitions to return our mock instances
    with patch("api.main.read_instance_definitions", return_value=mock_instances):
        # Call get_instance_definition directly
        instance = get_instance_definition(TEST_INSTANCE_ID)

        # Should return None
        assert instance is None


def test_get_instance_definition_empty_list():
    """Test get_instance_definition with empty list."""
    logging.info("--- Test: get_instance_definition empty list ---")

    # Mock read_instance_definitions to return an empty list
    with patch("api.main.read_instance_definitions", return_value=[]):
        # Call get_instance_definition directly
        instance = get_instance_definition(TEST_INSTANCE_ID)

        # Should return None
        assert instance is None


def test_get_instances_endpoint(client):
    """Test GET /api/instances endpoint."""
    logging.info("--- Test: GET /api/instances endpoint ---")

    # Create mock instance data
    mock_instances = [
        MagicMock(
            id=TEST_INSTANCE_ID,
            name="Config Test Instance",
            description="Test instance for config tests",
            port=6080,
        ),
    ]

    # Mock read_instance_definitions to return our mock instances
    with patch("api.main.read_instance_definitions", return_value=mock_instances):
        # Call the endpoint
        response = client.get("/api/instances")

        # Should return our mock instance
        assert response.status_code == 200
        instances = response.json()
        assert len(instances) == 1
        assert instances[0]["id"] == TEST_INSTANCE_ID
        assert instances[0]["name"] == "Config Test Instance"
        assert instances[0]["description"] == "Test instance for config tests"
        assert "instanceUrl" in instances[0]


def test_get_instances_endpoint_empty(client):
    """Test GET /api/instances endpoint with empty list."""
    logging.info("--- Test: GET /api/instances endpoint empty ---")

    # Mock read_instance_definitions to return an empty list
    with patch("api.main.read_instance_definitions", return_value=[]):
        # Call the endpoint
        response = client.get("/api/instances")

        # Should return an empty list
        assert response.status_code == 200
        assert response.json() == []


def test_get_instances_endpoint_error(client):
    """Test GET /api/instances endpoint with error."""
    logging.info("--- Test: GET /api/instances endpoint error ---")

    # Mock read_instance_definitions to raise an exception
    with patch(
        "api.main.read_instance_definitions", side_effect=Exception("Test exception")
    ):
        # Call the endpoint
        response = client.get("/api/instances")

        # Should return a 500 error
        assert response.status_code == 500
        assert "Test exception" in response.json()["detail"]


def test_get_instance_endpoint(client):
    """Test GET /api/instances/{instance_id} endpoint."""
    logging.info("--- Test: GET /api/instances/{instance_id} endpoint ---")

    # Create mock instance
    mock_instance = MagicMock(
        id=TEST_INSTANCE_ID,
        name="Config Test Instance",
        description="Test instance for config tests",
        port=6080,
    )

    # Mock get_instance_definition to return our mock instance
    with patch("api.main.get_instance_definition", return_value=mock_instance):
        # Call the endpoint
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}")

        # Should return our mock instance
        assert response.status_code == 200
        instance = response.json()
        assert instance["id"] == TEST_INSTANCE_ID
        assert instance["name"] == "Config Test Instance"
        assert instance["description"] == "Test instance for config tests"
        assert "instanceUrl" in instance


def test_get_instance_endpoint_not_found(client):
    """Test GET /api/instances/{instance_id} endpoint when instance is not found."""
    logging.info("--- Test: GET /api/instances/{instance_id} endpoint not found ---")

    # Mock get_instance_definition to return None
    with patch("api.main.get_instance_definition", return_value=None):
        # Call the endpoint
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}")

        # Should return a 404 error
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()


def test_get_instance_endpoint_error(client):
    """Test GET /api/instances/{instance_id} endpoint with error."""
    logging.info("--- Test: GET /api/instances/{instance_id} endpoint error ---")

    # Mock get_instance_definition to raise an exception
    with patch(
        "api.main.get_instance_definition", side_effect=Exception("Test exception")
    ):
        # Call the endpoint
        response = client.get(f"/api/instances/{TEST_INSTANCE_ID}")

        # Should return a 500 error
        assert response.status_code == 500
        assert "Test exception" in response.json()["detail"]
