import errno
import json
import logging
from pathlib import Path
from unittest.mock import MagicMock, PropertyMock, mock_open, patch

import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import (
        app,
        find_available_port,
        get_container,
        get_docker_client,
        is_port_available,
        read_instance_definitions,
    )
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "mock-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture()
def mock_docker_client():
    """Provides a mocked Docker client."""
    mock_client = MagicMock()

    # Mock containers collection
    mock_containers = MagicMock()
    mock_client.containers = mock_containers

    # Mock images collection
    mock_images = MagicMock()
    mock_client.images = mock_images

    # Mock ping method
    mock_client.ping.return_value = True

    return mock_client


@pytest.fixture()
def mock_container():
    """Provides a mocked Docker container."""
    mock_container = MagicMock()

    # Mock status property
    type(mock_container).status = PropertyMock(return_value="running")

    # Mock id property
    type(mock_container).id = PropertyMock(return_value="mock-container-id")

    # Mock name property
    type(mock_container).name = PropertyMock(return_value=TEST_CONTAINER_NAME)

    # Mock attrs property
    mock_attrs = {
        "State": {
            "Status": "running",
            "Running": True,
            "Paused": False,
            "Restarting": False,
            "OOMKilled": False,
            "Dead": False,
            "Pid": 1234,
            "ExitCode": 0,
            "Error": "",
        },
        "HostConfig": {
            "PortBindings": {
                "6080/tcp": [{"HostIp": "", "HostPort": "6080"}],
            },
        },
        "NetworkSettings": {
            "Ports": {
                "6080/tcp": [{"HostIp": "0.0.0.0", "HostPort": "6080"}],
            },
        },
    }
    type(mock_container).attrs = PropertyMock(return_value=mock_attrs)

    # Mock reload method
    mock_container.reload.return_value = None

    # Mock stop method
    mock_container.stop.return_value = None

    # Mock remove method
    mock_container.remove.return_value = None

    return mock_container


def test_docker_client_mocking(mock_docker_client):
    """Test that Docker client mocking works correctly."""
    logging.info("--- Test: Docker client mocking ---")

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        # Set up the mock to return a specific container
        mock_container = MagicMock()
        mock_container.status = "running"
        mock_docker_client.containers.get.return_value = mock_container

        with TestClient(app) as client:
            # Call an endpoint that uses get_docker_client
            response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")

            # Verify the Docker client was used correctly
            mock_docker_client.containers.get.assert_called_once_with(
                TEST_CONTAINER_NAME
            )

            # Should return a status of "running"
            assert response.status_code == 200
            assert response.json()["status"] == "running"


def test_container_mocking(mock_docker_client, mock_container):
    """Test that container mocking works correctly."""
    logging.info("--- Test: Container mocking ---")

    # Set up the mock client to return our mock container
    mock_docker_client.containers.get.return_value = mock_container

    # Mock get_docker_client to return our mock client
    with patch("api.main.get_docker_client", return_value=mock_docker_client):
        with TestClient(app) as client:
            # Call an endpoint that uses get_container
            response = client.get(f"/api/instances/{TEST_INSTANCE_ID}/status")

            # Verify the container was accessed correctly
            mock_docker_client.containers.get.assert_called_once_with(
                TEST_CONTAINER_NAME
            )

            # Should return a status of "running"
            assert response.status_code == 200
            assert response.json()["status"] == "running"


def test_filesystem_mocking():
    """Test that filesystem mocking works correctly."""
    logging.info("--- Test: Filesystem mocking ---")

    # Create a mock Path object
    mock_path = MagicMock(spec=Path)
    mock_path.is_file.return_value = True

    # Create mock instance data
    mock_instance_data = json.dumps(
        [
            {
                "id": TEST_INSTANCE_ID,
                "name": "Mock Test Instance",
                "description": "Test instance for mocking tests",
                "port": 6080,
            },
        ]
    )

    # Mock INSTANCES_CONFIG_PATH
    with patch("api.main.INSTANCES_CONFIG_PATH", mock_path):
        # Mock open to return our mock instance data
        with patch("builtins.open", mock_open(read_data=mock_instance_data)):
            with TestClient(app) as client:
                # Call the endpoint that reads instance definitions
                response = client.get("/api/instances")

                # Verify the file was checked and read
                mock_path.is_file.assert_called_once()

                # Should return our mock instance
                assert response.status_code == 200
                instances = response.json()
                assert len(instances) == 1
                assert instances[0]["id"] == TEST_INSTANCE_ID


def test_socket_mocking():
    """Test that socket mocking works correctly."""
    logging.info("--- Test: Socket mocking ---")

    # Create a mock socket
    mock_socket = MagicMock()

    # Test with a successful bind
    mock_socket.bind.return_value = None

    with patch("socket.socket", return_value=mock_socket):
        # Call is_port_available directly
        result = is_port_available(6080)

        # Verify the socket was used correctly
        mock_socket.bind.assert_called_once_with(("127.0.0.1", 6080))
        mock_socket.close.assert_called_once()

        # Should return True
        assert result is True

    # Test with EADDRINUSE error
    mock_socket.reset_mock()
    mock_socket.bind.side_effect = OSError(errno.EADDRINUSE, "Address already in use")

    with patch("socket.socket", return_value=mock_socket):
        # Call is_port_available directly
        result = is_port_available(6080)

        # Verify the socket was used correctly
        mock_socket.bind.assert_called_once_with(("127.0.0.1", 6080))
        mock_socket.close.assert_called_once()

        # Should return False
        assert result is False

    # Test with other OSError
    mock_socket.reset_mock()
    mock_socket.bind.side_effect = OSError(errno.EINVAL, "Invalid argument")

    with patch("socket.socket", return_value=mock_socket):
        # Call is_port_available directly
        result = is_port_available(6080)

        # Verify the socket was used correctly
        mock_socket.bind.assert_called_once_with(("127.0.0.1", 6080))
        mock_socket.close.assert_called_once()

        # Should return False
        assert result is False

    # Test with general exception
    mock_socket.reset_mock()
    mock_socket.bind.side_effect = Exception("Test exception")

    with patch("socket.socket", return_value=mock_socket):
        # Call is_port_available directly
        result = is_port_available(6080)

        # Verify the socket was used correctly
        mock_socket.bind.assert_called_once_with(("127.0.0.1", 6080))
        mock_socket.close.assert_called_once()

        # Should return False
        assert result is False


def test_find_available_port_mocking():
    """Test that find_available_port mocking works correctly."""
    logging.info("--- Test: find_available_port mocking ---")

    # Mock is_port_available to return True for port 6080 and False for others
    def mock_is_port_available(port, host="127.0.0.1"):
        return port == 6080

    with patch("api.main.is_port_available", side_effect=mock_is_port_available):
        # Call find_available_port directly
        port = find_available_port(6000, 6100)

        # Should return 6080
        assert port == 6080

    # Mock is_port_available to always return False
    with patch("api.main.is_port_available", return_value=False):
        # Call find_available_port directly
        port = find_available_port(6000, 6100)

        # Should return None
        assert port is None
