import logging
import re
import time

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_ID = "security-test"
TEST_CONTAINER_NAME = f"bahtbrowser-{TEST_INSTANCE_ID}"


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(docker_client, container_name):
    """Helper to remove a container if it exists."""
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_container(docker_client):
    """Ensures the test container is removed before and after a test."""
    # Run before test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)
    yield
    # Run after test
    _remove_container_if_exists(docker_client, TEST_CONTAINER_NAME)


def test_sql_injection_prevention(client):
    """Test prevention of SQL injection attacks."""
    logging.info("--- Test: SQL injection prevention ---")

    # Test with SQL injection attempts in instance ID
    sql_injection_ids = [
        "1; DROP TABLE instances;--",
        "1' OR '1'='1",
        '1" OR "1"="1',
        "1) OR (1=1",
        "1; SELECT * FROM instances;--",
    ]

    for injection_id in sql_injection_ids:
        # Test various endpoints with injection attempts
        endpoints = [
            f"/api/instances/{injection_id}",
            f"/api/instances/{injection_id}/start",
            f"/api/instances/{injection_id}/stop",
            f"/api/instances/{injection_id}/status",
        ]

        for endpoint in endpoints:
            if endpoint.endswith("/start") or endpoint.endswith("/stop"):
                response = client.post(endpoint)
            else:
                response = client.get(endpoint)

            logging.info(
                f"Response for SQL injection attempt '{injection_id}' at {endpoint}: {response.status_code}"
            )

            # API should return 400 Bad Request or 404 Not Found, not 500 Server Error
            assert (
                response.status_code in [400, 404]
            ), f"Endpoint {endpoint} with injection '{injection_id}' returned {response.status_code}"

            # Check that the response doesn't contain SQL errors
            if response.status_code != 404:  # Only check content if not 404
                content = response.text.lower()
                assert "sql" not in content, f"Response contains 'sql': {content}"
                assert "syntax" not in content, f"Response contains 'syntax': {content}"
                assert (
                    "error" not in content or "not found" in content
                ), f"Response contains unexpected error: {content}"

    logging.info("SQL injection prevention test passed")


def test_command_injection_prevention(client, docker_client, cleanup_test_container):
    """Test prevention of command injection attacks."""
    logging.info("--- Test: Command injection prevention ---")

    # Test with command injection attempts in instance ID
    command_injection_ids = [
        "1; ls -la;",
        "1 && ls -la",
        "1 | cat /etc/passwd",
        "1 `cat /etc/passwd`",
        "1 $(cat /etc/passwd)",
        "1\ncat /etc/passwd",
    ]

    for injection_id in command_injection_ids:
        # Test start endpoint with injection attempts
        response = client.post(f"/api/instances/{injection_id}/start")
        logging.info(
            f"Start response for command injection attempt '{injection_id}': {response.status_code}"
        )

        # API should return 400 Bad Request or 404 Not Found, not 500 Server Error
        assert (
            response.status_code in [400, 404]
        ), f"Start endpoint with injection '{injection_id}' returned {response.status_code}"

    # Test with command injection in request body
    injection_payloads = [
        {"port": "8080; ls -la"},
        {"port": 8080, "command": "ls -la"},
        {"image": "alpine-chromium-novnc:latest; cat /etc/passwd"},
        {"image": "alpine-chromium-novnc:latest && cat /etc/passwd"},
    ]

    for payload in injection_payloads:
        response = client.post(f"/api/instances/{TEST_INSTANCE_ID}/start", json=payload)
        logging.info(
            f"Start response for command injection in payload {payload}: {response.status_code}"
        )

        # API should return 400 Bad Request or 422 Unprocessable Entity, not 500 Server Error
        assert response.status_code in [
            400,
            422,
        ], f"Start endpoint with payload {payload} returned {response.status_code}"

    logging.info("Command injection prevention test passed")


def test_xss_prevention(client):
    """Test prevention of Cross-Site Scripting (XSS) attacks."""
    logging.info("--- Test: XSS prevention ---")

    # Test with XSS attempts in instance ID
    xss_injection_ids = [
        "<script>alert('XSS')</script>",
        "<img src='x' onerror='alert(\"XSS\")'>",
        "<svg/onload=alert('XSS')>",
        "javascript:alert('XSS')",
        "1';alert('XSS');//",
    ]

    for injection_id in xss_injection_ids:
        # Test various endpoints with XSS attempts
        endpoints = [
            f"/api/instances/{injection_id}",
            f"/api/instances/{injection_id}/status",
        ]

        for endpoint in endpoints:
            response = client.get(endpoint)
            logging.info(
                f"Response for XSS attempt '{injection_id}' at {endpoint}: {response.status_code}"
            )

            # API should return 400 Bad Request or 404 Not Found, not 500 Server Error
            assert (
                response.status_code in [400, 404]
            ), f"Endpoint {endpoint} with XSS '{injection_id}' returned {response.status_code}"

            if response.status_code == 200:
                # If 200 is returned, ensure the response is properly escaped
                content = response.text
                assert (
                    "<script>" not in content
                ), f"Response contains unescaped script tag: {content}"
                assert (
                    "onerror=" not in content
                ), f"Response contains unescaped event handler: {content}"
                assert (
                    "onload=" not in content
                ), f"Response contains unescaped event handler: {content}"
                assert (
                    "javascript:" not in content
                ), f"Response contains unescaped javascript protocol: {content}"

    logging.info("XSS prevention test passed")


def test_path_traversal_prevention(client):
    """Test prevention of path traversal attacks."""
    logging.info("--- Test: Path traversal prevention ---")

    # Test with path traversal attempts in instance ID
    path_traversal_ids = [
        "../../../etc/passwd",
        "..%2F..%2F..%2Fetc%2Fpasswd",  # URL encoded
        "..\\..\\..\\windows\\system32\\config\\sam",
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",  # Double URL encoded
        "1/../../etc/passwd",
    ]

    for injection_id in path_traversal_ids:
        # Test various endpoints with path traversal attempts
        endpoints = [
            f"/api/instances/{injection_id}",
            f"/api/instances/{injection_id}/status",
        ]

        for endpoint in endpoints:
            response = client.get(endpoint)
            logging.info(
                f"Response for path traversal attempt '{injection_id}' at {endpoint}: {response.status_code}"
            )

            # API should return 400 Bad Request or 404 Not Found, not 500 Server Error
            assert (
                response.status_code in [400, 404]
            ), f"Endpoint {endpoint} with path traversal '{injection_id}' returned {response.status_code}"

            # Check that the response doesn't contain file contents
            if response.status_code != 404:  # Only check content if not 404
                content = response.text.lower()
                assert (
                    "root:" not in content
                ), f"Response may contain file contents: {content}"
                assert (
                    "password" not in content
                ), f"Response may contain sensitive data: {content}"

    logging.info("Path traversal prevention test passed")


def test_container_isolation(client, docker_client, cleanup_test_container):
    """Test that containers are properly isolated from the host and each other."""
    logging.info("--- Test: Container isolation ---")

    # Start two test instances
    instance_id_1 = f"{TEST_INSTANCE_ID}-1"
    instance_id_2 = f"{TEST_INSTANCE_ID}-2"
    container_name_1 = f"bahtbrowser-{instance_id_1}"
    container_name_2 = f"bahtbrowser-{instance_id_2}"

    try:
        # Start first instance
        response = client.post(
            f"/api/instances/{instance_id_1}/start", json={"port": 6091}
        )
        logging.info(f"Start response for instance 1: {response.status_code}")
        assert response.status_code == 200

        # Start second instance
        response = client.post(
            f"/api/instances/{instance_id_2}/start", json={"port": 6092}
        )
        logging.info(f"Start response for instance 2: {response.status_code}")
        assert response.status_code == 200

        # Wait for instances to start
        time.sleep(10)

        # Get containers
        container_1 = docker_client.containers.get(container_name_1)
        container_2 = docker_client.containers.get(container_name_2)

        # Test 1: Verify containers have different network namespaces
        ip_1 = container_1.exec_run("ip addr show").output.decode()
        ip_2 = container_2.exec_run("ip addr show").output.decode()

        # Extract IP addresses
        ip_pattern = re.compile(r"inet (\d+\.\d+\.\d+\.\d+)")
        ips_1 = ip_pattern.findall(ip_1)
        ips_2 = ip_pattern.findall(ip_2)

        logging.info(f"Container 1 IPs: {ips_1}")
        logging.info(f"Container 2 IPs: {ips_2}")

        # Containers should have different IPs (except for 127.0.0.1)
        non_localhost_ips_1 = [ip for ip in ips_1 if not ip.startswith("127.")]
        non_localhost_ips_2 = [ip for ip in ips_2 if not ip.startswith("127.")]

        assert set(non_localhost_ips_1).isdisjoint(
            set(non_localhost_ips_2)
        ), "Containers should have different IPs"

        # Test 2: Verify containers cannot see each other's processes
        ps_1 = container_1.exec_run("ps aux").output.decode()
        ps_2 = container_2.exec_run("ps aux").output.decode()

        # Count number of processes
        process_count_1 = len(ps_1.strip().split("\n")) - 1  # Subtract header line
        process_count_2 = len(ps_2.strip().split("\n")) - 1  # Subtract header line

        logging.info(f"Container 1 process count: {process_count_1}")
        logging.info(f"Container 2 process count: {process_count_2}")

        # Each container should only see its own processes (a small number)
        assert (
            process_count_1 < 20
        ), f"Container 1 can see too many processes: {process_count_1}"
        assert (
            process_count_2 < 20
        ), f"Container 2 can see too many processes: {process_count_2}"

        # Test 3: Verify containers have limited capabilities
        cap_1 = container_1.exec_run("capsh --print").output.decode()

        # Check for dangerous capabilities
        dangerous_caps = ["cap_sys_admin", "cap_net_admin", "cap_sys_ptrace"]
        for cap in dangerous_caps:
            assert cap not in cap_1, f"Container has dangerous capability: {cap}"

        logging.info("Container isolation test passed")

    finally:
        # Clean up containers
        _remove_container_if_exists(docker_client, container_name_1)
        _remove_container_if_exists(docker_client, container_name_2)


@pytest.mark.skipif(
    not pytest.config.getoption("--run-security-scan", default=False),
    reason="Only run when explicitly requested with --run-security-scan",
)
def test_container_security_scan(docker_client):
    """Test container image for security vulnerabilities using Trivy."""
    logging.info("--- Test: Container security scan ---")

    try:
        # Check if Trivy is installed
        result = docker_client.containers.run(
            "alpine:latest",
            "which trivy",
            remove=True,
            volumes={"/usr/local/bin": {"bind": "/usr/local/bin", "mode": "ro"}},
        )
        if "trivy" not in result.decode():
            pytest.skip("Trivy not installed, skipping security scan")
    except Exception:
        pytest.skip("Could not check for Trivy, skipping security scan")

    try:
        # Run Trivy scan on the container image
        result = docker_client.containers.run(
            "aquasec/trivy:latest",
            f"image --severity HIGH,CRITICAL {DOCKER_IMAGE_NAME}",
            remove=True,
            volumes={
                "/var/run/docker.sock": {"bind": "/var/run/docker.sock", "mode": "ro"}
            },
        )

        output = result.decode()
        logging.info(f"Trivy scan results: {output}")

        # Parse results
        if "HIGH: 0, CRITICAL: 0" in output:
            logging.info("No HIGH or CRITICAL vulnerabilities found")
        else:
            # This is not a failure, just a warning
            logging.warning(
                "HIGH or CRITICAL vulnerabilities found in the container image"
            )

            # Extract vulnerability count
            match = re.search(r"HIGH: (\d+), CRITICAL: (\d+)", output)
            if match:
                high_count = int(match.group(1))
                critical_count = int(match.group(2))
                logging.warning(
                    f"Found {high_count} HIGH and {critical_count} CRITICAL vulnerabilities"
                )

        logging.info("Container security scan completed")

    except Exception as e:
        logging.exception(f"Error running security scan: {e}")
        pytest.skip(f"Error running security scan: {e}")


def test_cors_headers(client):
    """Test CORS headers are properly set."""
    logging.info("--- Test: CORS headers ---")

    # Test with various origins
    origins = [
        "http://localhost:9002",  # Expected frontend origin
        "http://example.com",  # External origin
        "null",  # No origin
    ]

    for origin in origins:
        # Test preflight request
        headers = {
            "Origin": origin,
            "Access-Control-Request-Method": "GET",
            "Access-Control-Request-Headers": "Content-Type",
        }
        response = client.options("/api/instances", headers=headers)
        logging.info(
            f"CORS preflight response for origin '{origin}': {response.status_code}"
        )

        # Check CORS headers
        cors_headers = {
            "Access-Control-Allow-Origin": None,
            "Access-Control-Allow-Methods": None,
            "Access-Control-Allow-Headers": None,
        }

        for header in cors_headers:
            cors_headers[header] = response.headers.get(header)
            logging.info(f"{header}: {cors_headers[header]}")

        # For the frontend origin, CORS should be allowed
        if origin == "http://localhost:9002":
            assert (
                cors_headers["Access-Control-Allow-Origin"] == origin
            ), f"CORS origin header not set correctly for {origin}"
            assert (
                cors_headers["Access-Control-Allow-Methods"] is not None
            ), "CORS methods header not set"
            assert (
                cors_headers["Access-Control-Allow-Headers"] is not None
            ), "CORS headers header not set"

        # For external origins, CORS might be restricted depending on the configuration
        # This is just a check that the API handles CORS consistently

    logging.info("CORS headers test completed")


def test_api_authentication(client):
    """Test API authentication if implemented."""
    logging.info("--- Test: API authentication ---")

    # Check if the API requires authentication
    # This is a basic check - the actual implementation will depend on the authentication method used

    # Test with no authentication
    response = client.get("/api/instances")

    # If authentication is required, the API should return 401 Unauthorized
    # If not, it should return 200 OK
    if response.status_code == 401:
        logging.info("API requires authentication")

        # Test with invalid authentication
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/instances", headers=headers)
        assert response.status_code == 401, "API should reject invalid authentication"

        # Note: Testing with valid authentication would require knowing the authentication method
        # and having valid credentials, which is beyond the scope of this test
    else:
        logging.info("API does not require authentication")

    logging.info("API authentication test completed")
