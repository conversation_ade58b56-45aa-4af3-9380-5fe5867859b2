import logging
import random
import string
import time
from concurrent.futures import ThreadPoolExecutor

import docker
import pytest
from fastapi.testclient import TestClient

# Import app from main.py
try:
    from api.main import app
except ImportError as e:
    logging.exception(
        f"ImportError: {e}. Check api/main.py exists and is valid Python."
    )
    from fastapi import FastAPI

    app = FastAPI()
    logging.warning(
        "Could not import 'app' from api.main. Using dummy app. API tests will fail."
    )

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)

# Constants
DOCKER_IMAGE_NAME = "alpine-chromium-novnc:latest"
TEST_INSTANCE_PREFIX = "boundary-test-"
MAX_INSTANCES = 50  # Maximum number of instances to create for boundary testing


@pytest.fixture(scope="module")
def client():
    """Provides a FastAPI TestClient."""
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="module")
def docker_client():
    """Provides a Docker client connection."""
    try:
        client = docker.from_env()
        client.ping()
        return client
    except Exception as e:
        pytest.fail(f"Failed to connect to Docker daemon: {e}")


def _remove_container_if_exists(docker_client, container_name):
    """Helper to remove a container if it exists."""
    try:
        container = docker_client.containers.get(container_name)
        if container:
            logging.info(
                f"Cleaning up container: {container_name} (Status: {container.status})"
            )
            if container.status == "running":
                container.stop(timeout=5)
            container.remove(force=True)
            logging.info(f"Removed container: {container_name}")
    except docker.errors.NotFound:
        logging.info(f"Cleanup: Container {container_name} not found.")
    except Exception as e:
        logging.error(
            f"Error during cleanup of container {container_name}: {e}", exc_info=True
        )


@pytest.fixture(scope="function")
def cleanup_test_containers(docker_client):
    """Ensures all test containers are removed before and after a test."""
    # Run before test
    for i in range(MAX_INSTANCES):
        container_name = f"bahtbrowser-{TEST_INSTANCE_PREFIX}{i}"
        _remove_container_if_exists(docker_client, container_name)
    yield
    # Run after test
    for i in range(MAX_INSTANCES):
        container_name = f"bahtbrowser-{TEST_INSTANCE_PREFIX}{i}"
        _remove_container_if_exists(docker_client, container_name)


def test_maximum_instances(client, docker_client, cleanup_test_containers):
    """Test the maximum number of instances that can be created."""
    logging.info("--- Test: Maximum instances ---")

    # Create a list of instance IDs
    instance_ids = [f"{TEST_INSTANCE_PREFIX}{i}" for i in range(MAX_INSTANCES)]

    # Start instances sequentially until we hit a limit
    started_instances = []
    for i, instance_id in enumerate(instance_ids):
        logging.info(f"Starting instance {i+1}/{MAX_INSTANCES}: {instance_id}")

        response = client.post(f"/api/instances/{instance_id}/start")
        logging.info(
            f"Start response for {instance_id}: {response.status_code}, body: {response.text}"
        )

        if response.status_code == 200:
            started_instances.append(instance_id)
        else:
            logging.info(
                f"Failed to start instance {instance_id}: {response.status_code}"
            )
            break

        # Check if we've started a reasonable number of instances
        if i >= 10 and i % 5 == 0:
            # Check system resources
            try:
                # Get Docker info to check system resources
                info = docker_client.info()
                containers_running = info.get("ContainersRunning", 0)
                logging.info(f"Containers running: {containers_running}")

                # If we're running low on resources, stop the test
                if containers_running >= 20:  # Arbitrary limit for testing
                    logging.info(
                        f"Reached resource limit with {len(started_instances)} instances"
                    )
                    break
            except Exception as e:
                logging.exception(f"Error checking Docker info: {e}")

    # Log the number of instances we were able to start
    logging.info(f"Successfully started {len(started_instances)} instances")

    # Verify a reasonable number of instances were started
    assert len(started_instances) > 0, "Could not start any instances"

    # Check the status of all started instances
    for instance_id in started_instances:
        response = client.get(f"/api/instances/{instance_id}/status")
        assert response.status_code == 200
        status = response.json().get("status")
        assert status in [
            "running",
            "starting",
        ], f"Instance {instance_id} has unexpected status: {status}"

    # Stop all instances
    for instance_id in started_instances:
        logging.info(f"Stopping instance {instance_id}")
        response = client.post(f"/api/instances/{instance_id}/stop")
        logging.info(f"Stop response for {instance_id}: {response.status_code}")

    logging.info("Maximum instances test completed")


def test_port_range_boundaries(client, docker_client, cleanup_test_containers):
    """Test the boundaries of the port range."""
    logging.info("--- Test: Port range boundaries ---")

    # Test with various port values
    port_tests = [
        (1, False),  # Too low
        (1023, False),  # Reserved port
        (1024, True),  # Minimum non-reserved port
        (6080, True),  # Normal port
        (8080, True),  # Common port
        (49151, True),  # Maximum registered port
        (49152, True),  # Minimum dynamic port
        (65535, True),  # Maximum valid port
        (65536, False),  # Invalid port (too high)
        (0, False),  # Invalid port (zero)
        (-1, False),  # Invalid port (negative)
        (None, True),  # No port specified (should use default)
    ]

    instance_id = f"{TEST_INSTANCE_PREFIX}port"

    for port, should_succeed in port_tests:
        # Clean up any existing container
        container_name = f"bahtbrowser-{instance_id}"
        _remove_container_if_exists(docker_client, container_name)

        # Prepare request payload
        payload = {}
        if port is not None:
            payload["port"] = port

        # Try to start an instance with the port
        logging.info(f"Testing port {port}")
        response = client.post(f"/api/instances/{instance_id}/start", json=payload)
        logging.info(
            f"Start response for port {port}: {response.status_code}, body: {response.text}"
        )

        if should_succeed:
            assert (
                response.status_code == 200
            ), f"Expected success for port {port}, got {response.status_code}"

            # Wait for instance to start
            time.sleep(5)

            # Stop the instance
            response = client.post(f"/api/instances/{instance_id}/stop")
            assert response.status_code == 200

            # Wait for instance to stop
            time.sleep(2)
        else:
            assert (
                response.status_code != 200
            ), f"Expected failure for port {port}, got {response.status_code}"

    logging.info("Port range boundaries test completed")


def test_instance_id_length_boundaries(client, docker_client, cleanup_test_containers):
    """Test the boundaries of instance ID length."""
    logging.info("--- Test: Instance ID length boundaries ---")

    # Test with various ID lengths
    id_length_tests = [
        (0, False),  # Empty ID
        (1, True),  # Single character
        (10, True),  # Normal length
        (50, True),  # Long but reasonable
        (100, True),  # Very long
        (200, False),  # Extremely long (may exceed Docker container name limits)
    ]

    for length, should_succeed in id_length_tests:
        # Generate a random ID of the specified length
        if length == 0:
            instance_id = ""
        else:
            instance_id = "".join(
                random.choices(string.ascii_lowercase + string.digits, k=length)
            )

        # Add prefix to avoid conflicts
        instance_id = f"{TEST_INSTANCE_PREFIX}{instance_id}"

        # Try to start an instance with the ID
        logging.info(f"Testing ID length {length}")
        response = client.post(f"/api/instances/{instance_id}/start")
        logging.info(
            f"Start response for ID length {length}: {response.status_code}, body: {response.text}"
        )

        if should_succeed:
            assert (
                response.status_code == 200
            ), f"Expected success for ID length {length}, got {response.status_code}"

            # Wait for instance to start
            time.sleep(5)

            # Stop the instance
            response = client.post(f"/api/instances/{instance_id}/stop")
            assert response.status_code == 200

            # Wait for instance to stop
            time.sleep(2)

            # Delete the instance
            response = client.delete(f"/api/instances/{instance_id}")
            assert response.status_code == 200
        else:
            assert (
                response.status_code != 200
            ), f"Expected failure for ID length {length}, got {response.status_code}"

    logging.info("Instance ID length boundaries test completed")


def test_payload_size_boundaries(client, docker_client, cleanup_test_containers):
    """Test the boundaries of request payload size."""
    logging.info("--- Test: Payload size boundaries ---")

    instance_id = f"{TEST_INSTANCE_PREFIX}payload"

    # Test with various payload sizes
    payload_size_tests = [
        (0, True),  # Empty payload
        (1, True),  # Tiny payload
        (10, True),  # Small payload
        (100, True),  # Normal payload
        (1000, True),  # Large payload
        (10000, False),  # Very large payload (may exceed limits)
    ]

    for size, should_succeed in payload_size_tests:
        # Generate a payload of the specified size
        if size == 0:
            payload = {}
        else:
            # Create a payload with a large description field
            payload = {
                "description": "".join(
                    random.choices(string.ascii_lowercase + string.digits, k=size)
                ),
            }

        # Try to start an instance with the payload
        logging.info(f"Testing payload size {size}")
        response = client.post(f"/api/instances/{instance_id}/start", json=payload)
        logging.info(
            f"Start response for payload size {size}: {response.status_code}, body: {response.text}"
        )

        if should_succeed:
            assert (
                response.status_code == 200
            ), f"Expected success for payload size {size}, got {response.status_code}"

            # Wait for instance to start
            time.sleep(5)

            # Stop the instance
            response = client.post(f"/api/instances/{instance_id}/stop")
            assert response.status_code == 200

            # Wait for instance to stop
            time.sleep(2)
        else:
            assert (
                response.status_code != 200
            ), f"Expected failure for payload size {size}, got {response.status_code}"

    logging.info("Payload size boundaries test completed")


def test_request_rate_boundaries(client):
    """Test the boundaries of request rate."""
    logging.info("--- Test: Request rate boundaries ---")

    # Test with various request rates
    rate_tests = [
        (1, 1, True),  # 1 request per second
        (10, 1, True),  # 10 requests per second
        (50, 1, True),  # 50 requests per second
        (100, 1, False),  # 100 requests per second (may exceed rate limits)
    ]

    for rate, duration, should_succeed in rate_tests:
        # Calculate the total number of requests
        num_requests = rate * duration

        # Make the requests
        logging.info(
            f"Testing request rate {rate} req/s for {duration}s ({num_requests} requests)"
        )

        start_time = time.time()
        responses = []

        with ThreadPoolExecutor(max_workers=rate) as executor:
            futures = [
                executor.submit(client.get, "/api/instances")
                for _ in range(num_requests)
            ]
            for future in futures:
                responses.append(future.result())

        end_time = time.time()
        actual_duration = end_time - start_time
        actual_rate = num_requests / actual_duration

        # Count successful responses
        success_count = sum(1 for r in responses if r.status_code == 200)
        success_rate = success_count / num_requests

        logging.info(
            f"Made {num_requests} requests in {actual_duration:.2f}s ({actual_rate:.2f} req/s)"
        )
        logging.info(
            f"Success rate: {success_rate:.2%} ({success_count}/{num_requests})"
        )

        if should_succeed:
            assert (
                success_rate > 0.9
            ), f"Expected high success rate for {rate} req/s, got {success_rate:.2%}"
        else:
            # If rate limiting is implemented, we expect some requests to fail
            # If not, this test will pass anyway
            logging.info(
                f"Rate limiting test at {rate} req/s: {'Passed' if success_rate < 1.0 else 'No rate limiting detected'}"
            )

    logging.info("Request rate boundaries test completed")


def test_concurrent_request_boundaries(client):
    """Test the boundaries of concurrent requests."""
    logging.info("--- Test: Concurrent request boundaries ---")

    # Test with various concurrency levels
    concurrency_tests = [
        (1, True),  # Single request
        (10, True),  # 10 concurrent requests
        (50, True),  # 50 concurrent requests
        (100, False),  # 100 concurrent requests (may exceed limits)
    ]

    for concurrency, should_succeed in concurrency_tests:
        # Make the concurrent requests
        logging.info(f"Testing concurrency level {concurrency}")

        responses = []
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [
                executor.submit(client.get, "/api/instances")
                for _ in range(concurrency)
            ]
            for future in futures:
                responses.append(future.result())

        # Count successful responses
        success_count = sum(1 for r in responses if r.status_code == 200)
        success_rate = success_count / concurrency

        logging.info(
            f"Success rate: {success_rate:.2%} ({success_count}/{concurrency})"
        )

        if should_succeed:
            assert (
                success_rate > 0.9
            ), f"Expected high success rate for concurrency {concurrency}, got {success_rate:.2%}"
        else:
            # If concurrency limiting is implemented, we expect some requests to fail
            # If not, this test will pass anyway
            logging.info(
                f"Concurrency test at level {concurrency}: {'Passed' if success_rate < 1.0 else 'No concurrency limiting detected'}"
            )

    logging.info("Concurrent request boundaries test completed")


def test_resource_limit_boundaries(client, docker_client, cleanup_test_containers):
    """Test the boundaries of container resource limits."""
    logging.info("--- Test: Resource limit boundaries ---")

    instance_id = f"{TEST_INSTANCE_PREFIX}resource"

    # Test with various resource limits
    resource_tests = [
        ({"memory": "10m"}, False),  # Too little memory
        ({"memory": "100m"}, True),  # Minimum viable memory
        ({"memory": "512m"}, True),  # Normal memory
        ({"memory": "2g"}, True),  # Large memory
        ({"cpu_quota": 1000}, False),  # Too little CPU
        ({"cpu_quota": 10000}, True),  # Minimum viable CPU
        ({"cpu_quota": 100000}, True),  # Normal CPU
        ({"memory": "100m", "cpu_quota": 10000}, True),  # Minimum viable resources
        ({"memory": "4g", "cpu_quota": 400000}, True),  # Large resources
    ]

    for resources, should_succeed in resource_tests:
        # Clean up any existing container
        container_name = f"bahtbrowser-{instance_id}"
        _remove_container_if_exists(docker_client, container_name)

        # Try to start an instance with the resource limits
        logging.info(f"Testing resource limits {resources}")
        response = client.post(f"/api/instances/{instance_id}/start", json=resources)
        logging.info(
            f"Start response for resources {resources}: {response.status_code}, body: {response.text}"
        )

        if should_succeed:
            assert (
                response.status_code == 200
            ), f"Expected success for resources {resources}, got {response.status_code}"

            # Wait for instance to start
            time.sleep(5)

            # Check if the container is running
            try:
                container = docker_client.containers.get(container_name)
                assert (
                    container.status == "running"
                ), f"Container should be running, but status is {container.status}"

                # Check if the resource limits were applied
                container_info = docker_client.api.inspect_container(container_name)
                host_config = container_info["HostConfig"]

                if "memory" in resources:
                    memory_bytes = docker.utils.parse_bytes(resources["memory"])
                    assert (
                        host_config.get("Memory") == memory_bytes
                    ), "Memory limit not applied correctly"

                if "cpu_quota" in resources:
                    assert (
                        host_config.get("CpuQuota") == resources["cpu_quota"]
                    ), "CPU quota not applied correctly"

                logging.info(f"Resource limits applied correctly: {resources}")
            except docker.errors.NotFound:
                pytest.fail(f"Container {container_name} not found after starting")
            except Exception as e:
                logging.exception(f"Error checking container resources: {e}")
                pytest.fail(f"Error checking container resources: {e}")

            # Stop the instance
            response = client.post(f"/api/instances/{instance_id}/stop")
            assert response.status_code == 200

            # Wait for instance to stop
            time.sleep(2)
        else:
            assert (
                response.status_code != 200
            ), f"Expected failure for resources {resources}, got {response.status_code}"

    logging.info("Resource limit boundaries test completed")
