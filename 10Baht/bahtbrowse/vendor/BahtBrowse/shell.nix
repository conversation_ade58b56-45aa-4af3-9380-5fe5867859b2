{ pkgs ? import <nixpkgs> {} }:

let
  pythonPackages = ps: with ps; [
    fastapi
    uvicorn # ASGI server
    docker # Docker SDK
    python-dotenv # For .env file
    # Add other Python dependencies for the API here
  ];
  pythonEnv = pkgs.python3.withPackages pythonPackages;
in
pkgs.mkShell {
  buildInputs = [
    pythonEnv
    pkgs.nodejs # Still needed for the Next.js frontend
    pkgs.docker # Docker CLI (needed by Python SDK implicitly sometimes, good to have)
    # Add other system dependencies here if needed
  ];

  shellHook = ''
    echo "Entering Nix shell for BahtBrowse Controller/API..."
    # You can add custom shell commands here, e.g., setting environment variables
    # export MY_VARIABLE="example"
    # Ensure PYTHONPATH includes the project root if needed for imports
    export PYTHONPATH="${pkgs.lib.makeSearchPath "${pkgs.python3}" pythonPackages}:$PYTHONPATH"
  '';
}
