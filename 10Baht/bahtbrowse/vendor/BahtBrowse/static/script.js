const startButton = document.getElementById('startButton');
const stopButton = document.getElementById('stopButton');
const launchButton = document.getElementById('launchButton');
const messageDiv = document.getElementById('message');

// Function to handle API calls
async function callApi(endpoint) {
    messageDiv.textContent = 'Sending request...';
    try {
        const response = await fetch(`/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
            // No body needed for these simple POST requests
        });
        const data = await response.json();
        if (response.ok) {
            messageDiv.textContent = `Success: ${data.message}`;
        } else {
            messageDiv.textContent = `Error: ${data.detail || 'Unknown error'}`;
        }
    } catch (error) {
        console.error('Error calling API:', error);
        messageDiv.textContent = `Error: Could not connect to API (${error.message})`;
    }
}

// Add event listeners
startButton.addEventListener('click', () => callApi('start'));
stopButton.addEventListener('click', () => callApi('stop'));
launchButton.addEventListener('click', () => callApi('launch'));
