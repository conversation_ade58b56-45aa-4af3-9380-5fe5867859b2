FROM alpine:latest

# Install core dependencies first, including bash for chaining
RUN apk add --no-cache \
    bash \
    chromium \
    xvfb \
    x11vnc \
    fluxbox \
    supervisor \
    nodejs \
    npm \
    ttf-freefont \
    mesa-dri-gallium \
    dbus

# Install Python, pip, websockify, then clean up Python
RUN apk add --no-cache python3 py3-pip && \
    pip3 install --no-cache-dir --break-system-packages websockify && \
    apk del python3 py3-pip

# Set up supervisor configuration
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set up noVNC - download and extract a release
ARG NOVNC_VERSION=1.4.0
WORKDIR /opt
RUN wget -qO- "https://github.com/novnc/noVNC/archive/v${NOVNC_VERSION}.tar.gz" | tar xz && \
    mv noVNC-${NOVNC_VERSION} noVNC && \
    # Create a simple index.html that points to vnc.html
    echo "<!DOCTYPE html><html><head><title>noVNC</title></head><body><script>window.location.href = 'vnc.html?host=' + window.location.hostname + '&port=' + window.location.port;</script></body></html>" > /opt/noVNC/index.html

# Set the working directory (optional, supervisor handles paths)
WORKDIR /root

# Expose the port websockify will listen on (for noVNC)
EXPOSE 8080

# USER USER_NAME # Consider running as a non-root user

# Start supervisord
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

# --- Lines below might be removable if main.py/requirements.txt are no longer needed ---
# # Set the working directory in the container
# WORKDIR /app
#
# # Copy the dependencies file to the working directory
# COPY requirements.txt .
#
# # Install any needed packages specified in requirements.txt
# # Use --no-cache-dir to reduce image size
# RUN pip install --no-cache-dir --trusted-host pypi.python.org -r requirements.txt
#
# # Copy the content of the local src directory to the working directory in the container
# COPY main.py .
#
# # Make port 9000 available to the world outside this container
# EXPOSE 9000 # This might conflict or be unnecessary now
#
# # Define environment variable
# ENV NAME World
#
# # Run main.py when the container launches
# # Use --host 0.0.0.0 to make it accessible from outside the container
# CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "9000"] # This is replaced by supervisor CMD
