#!/usr/bin/env python3
# Test file to check compliance with PEP 8, PEP 257, and PEP 484

def add_numbers(a,b):
    """add two numbers together
    
    Args:
        a: first number
        b: second number
    """
    return a+b

class TestClass:
    def test_method(self, param1):
        print("This is a test")
        return param1

def process_data(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
    return result
