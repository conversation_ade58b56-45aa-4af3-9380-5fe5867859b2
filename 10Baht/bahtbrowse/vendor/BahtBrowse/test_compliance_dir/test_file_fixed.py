"""Test file to check compliance with PEP 8, PEP 257, and PEP 484 standards.

This module demonstrates proper compliance with Python coding standards.
"""

from typing import List


def add_numbers(a: int, b: int) -> int:
    """Add two numbers together.

    Args:
        a: First number to add.
        b: Second number to add.

    Returns:
        The sum of the two numbers.
    """
    return a + b


class TestClass:
    """Test class demonstrating proper docstrings and type annotations.

    This class contains example methods with proper documentation.
    """

    def test_method(self, param1: str) -> str:
        """Process the input parameter and return it.

        Args:
            param1: The input string to process.

        Returns:
            The processed string.
        """
        # Using logging instead of print
        # import logging
        # logging.info("This is a test")
        return param1


def process_data(data: List[int]) -> List[int]:
    """Process a list of integers by doubling positive values.

    Args:
        data: A list of integers to process.

    Returns:
        A new list containing doubled values of positive integers.
    """
    # Using list comprehension instead of a for loop
    return [item * 2 for item in data if item > 0]
