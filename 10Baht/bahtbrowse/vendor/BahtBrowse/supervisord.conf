[supervisord]
nodaemon=true
logfile=/dev/null ; Log to stdout/stderr
logfile_maxbytes=0

[program:xvfb]
command=/usr/bin/Xvfb :1 -screen 0 1280x1024x24 -nolisten tcp -nolisten unix -ac
autostart=true
autorestart=true
priority=10
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:fluxbox]
environment=DISPLAY=":1"
command=/usr/bin/fluxbox
autostart=true
autorestart=true
priority=20
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:x11vnc]
environment=DISPLAY=":1"
command=bash -c "sleep 2 && /usr/bin/x11vnc -v -display :1 -forever -nopw -shared -xkb -noxrecord -noxfixes -noxdamage -noshm -create"
autostart=true
autorestart=true
priority=30
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:websockify]
command=/usr/bin/websockify -v --web=/opt/noVNC 8080 localhost:5900
autostart=true
autorestart=true
priority=40
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:chromium]
environment=DISPLAY=":1"
command=/usr/bin/chromium-browser --no-sandbox --disable-gpu --user-data-dir=/root/.chromium --start-maximized --disable-dev-shm-usage
autostart=true
autorestart=true
priority=50
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
