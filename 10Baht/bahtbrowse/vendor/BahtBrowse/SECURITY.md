# Security Policy

## Supported Versions

We currently support the following versions with security updates:

| Version | Supported          |
| ------- | ------------------ |
| 0.1.x   | :white_check_mark: |

## Reporting a Vulnerability

If you discover a security vulnerability within BahtBrowse, please send an <NAME_EMAIL>. All security vulnerabilities will be promptly addressed.

Please include the following information in your report:

- Type of vulnerability
- Full path of the affected file(s)
- Location of the affected source code (tag/branch/commit or direct URL)
- Any special configuration required to reproduce the issue
- Step-by-step instructions to reproduce the issue
- Proof-of-concept or exploit code (if possible)
- Impact of the vulnerability

We take all security vulnerabilities seriously. Once we receive your report, we will:

1. Confirm receipt of your vulnerability report
2. Assess the impact and severity of the vulnerability
3. Develop and test a fix
4. Release a patch as soon as possible

## Security Best Practices

When deploying BahtBrowse, please follow these security best practices:

1. Keep all dependencies up to date
2. Use secure and unique passwords for all services
3. Implement proper access controls
4. Regularly backup your data
5. Monitor your systems for suspicious activity
