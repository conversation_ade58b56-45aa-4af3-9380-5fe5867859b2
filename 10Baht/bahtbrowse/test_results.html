<!DOCTYPE html>
<html>
<head>
    <title>BahtBrowse Test Results</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #1a1a1a;
            color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        header {
            background-color: #333;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #444;
        }
        h1 {
            margin: 0;
            font-size: 24px;
        }
        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 16px;
            text-align: center;
        }
        .button:hover {
            background-color: #45a049;
        }
        .info-box {
            background-color: #333;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-result.success {
            background-color: #4CAF50;
            color: white;
        }
        .test-result.warning {
            background-color: #FFC107;
            color: black;
        }
        .test-result.error {
            background-color: #F44336;
            color: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #444;
        }
        tr:hover {
            background-color: #444;
        }
    </style>
</head>
<body>
    <header>
        <h1>BahtBrowse Test Results</h1>
    </header>
    <div class="container">
        <div class="info-box">
            <h2>Test Summary</h2>
            <div class="test-result success">
                <strong>Process Tests:</strong> All processes are running correctly.
            </div>
            <div class="test-result success">
                <strong>Port Tests:</strong> All required ports are open and accessible.
            </div>
            <div class="test-result success">
                <strong>File Tests:</strong> All required files exist.
            </div>
            <div class="test-result success">
                <strong>HTTP Endpoint Tests:</strong> All HTTP endpoints are accessible.
            </div>
            <div class="test-result warning">
                <strong>WebSocket Tests:</strong> Direct WebSocket connection works, but proxied WebSocket connection has issues.
            </div>
            <div class="test-result warning">
                <strong>Log Analysis:</strong> Some non-critical errors found in logs.
            </div>
            <div class="test-result warning">
                <strong>API Tests:</strong> API endpoint is not accessible through nginx proxy.
            </div>
        </div>
        
        <div class="info-box">
            <h2>Process Tests</h2>
            <table>
                <tr>
                    <th>Process</th>
                    <th>Status</th>
                    <th>Details</th>
                </tr>
                <tr>
                    <td>Xvfb</td>
                    <td>Running</td>
                    <td>Display :1, 1920x1080x24</td>
                </tr>
                <tr>
                    <td>x11vnc</td>
                    <td>Running</td>
                    <td>Listening on port 5900</td>
                </tr>
                <tr>
                    <td>noVNC</td>
                    <td>Running</td>
                    <td>Listening on port 6080</td>
                </tr>
                <tr>
                    <td>nginx</td>
                    <td>Running</td>
                    <td>Listening on port 80</td>
                </tr>
                <tr>
                    <td>Firefox</td>
                    <td>Running</td>
                    <td>Using display :1</td>
                </tr>
            </table>
        </div>
        
        <div class="info-box">
            <h2>Port Tests</h2>
            <table>
                <tr>
                    <th>Port</th>
                    <th>Status</th>
                    <th>Details</th>
                </tr>
                <tr>
                    <td>8001</td>
                    <td>Open</td>
                    <td>nginx HTTP</td>
                </tr>
                <tr>
                    <td>5901</td>
                    <td>Open</td>
                    <td>VNC server</td>
                </tr>
                <tr>
                    <td>6080</td>
                    <td>Open</td>
                    <td>noVNC WebSocket proxy</td>
                </tr>
            </table>
        </div>
        
        <div class="info-box">
            <h2>HTTP Endpoint Tests</h2>
            <table>
                <tr>
                    <th>Endpoint</th>
                    <th>Status</th>
                    <th>Details</th>
                </tr>
                <tr>
                    <td>http://localhost:8001/</td>
                    <td>200 OK</td>
                    <td>Landing page</td>
                </tr>
                <tr>
                    <td>http://localhost:8001/working_connection.html</td>
                    <td>200 OK</td>
                    <td>Working connection page</td>
                </tr>
                <tr>
                    <td>http://localhost:8001/vnc/vnc.html</td>
                    <td>200 OK</td>
                    <td>Proxied VNC page</td>
                </tr>
                <tr>
                    <td>http://localhost:6080/vnc.html</td>
                    <td>200 OK</td>
                    <td>Direct VNC page</td>
                </tr>
            </table>
        </div>
        
        <div class="info-box">
            <h2>WebSocket Tests</h2>
            <p>Click the links below to test WebSocket connections:</p>
            <a href="http://localhost:8001/direct_ws_test.html" class="button" target="_blank">Test Direct WebSocket Connection</a>
            <a href="http://localhost:8001/proxied_ws_test.html" class="button" target="_blank">Test Proxied WebSocket Connection</a>
        </div>
        
        <div class="info-box">
            <h2>Log Analysis</h2>
            <div class="test-result warning">
                <strong>X11VNC Error Logs:</strong> Some non-critical errors found.
            </div>
            <pre>07/05/2025 15:36:46   errors, etc) it may be disabled:
07/05/2025 15:36:47   errors, etc) it may be disabled via: '-noscr'</pre>
            
            <div class="test-result warning">
                <strong>Firefox Error Logs:</strong> Some non-critical errors found.
            </div>
            <pre>Exiting due to channel error.
Exiting due to channel error.
Exiting due to channel error.</pre>
        </div>
        
        <div class="info-box">
            <h2>Connection Tests</h2>
            <p>Click the links below to test different connection methods:</p>
            <a href="http://localhost:6080/vnc.html?autoconnect=true&resize=remote" class="button" target="_blank">Direct WebSocket Connection</a>
            <a href="http://localhost:8001/vnc/vnc.html?host=localhost&port=6080&path=websockify&autoconnect=true&resize=remote" class="button" target="_blank">Proxied WebSocket Connection</a>
            <a href="http://localhost:8001/working_connection.html" class="button" target="_blank">Embedded VNC Connection</a>
        </div>
    </div>
</body>
</html>
