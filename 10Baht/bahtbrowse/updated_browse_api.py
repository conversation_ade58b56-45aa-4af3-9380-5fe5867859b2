#!/usr/bin/env python3
"""Simple API server for BahtBrowse browse functionality.

This module provides a simple web server that handles
the /browse endpoint for the BahtBrowse container.
"""

import logging
import sys
from aiohttp import web
from aiohttp.web import Request, Response

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("/tmp/logs/browse_api.log"),
    ],
)
logger = logging.getLogger("browse_api")

async def browse(request: Request) -> Response:
    """Handle the /browse endpoint.

    Args:
        request: The HTTP request

    Returns:
        A redirect response to the noVNC interface
    """
    # Get the URL parameter
    url = request.query.get("url")
    if not url:
        return web.json_response(
            {"error": "Missing 'url' parameter"}, status=400
        )
    
    logger.info(f"Received browse request for URL: {url}")
    
    # Construct the redirect URL with noVNC parameters
    redirect_url = f"/vnc/vnc.html?host=localhost&port=6080&path=websockify&autoconnect=true&resize=remote"
    
    logger.info(f"Redirecting to: {redirect_url}")
    
    # Return a redirect response
    return web.HTTPFound(redirect_url)

async def api_browse(request: Request) -> Response:
    """Handle the /api/browse endpoint.

    Args:
        request: The HTTP request

    Returns:
        A redirect response to the noVNC interface
    """
    # Get the URL parameter
    url = request.query.get("url")
    if not url:
        return web.json_response(
            {"error": "Missing 'url' parameter"}, status=400
        )
    
    logger.info(f"Received API browse request for URL: {url}")
    
    # Construct the redirect URL with noVNC parameters
    redirect_url = f"/vnc/vnc.html?host=localhost&port=6080&path=websockify&autoconnect=true&resize=remote"
    
    logger.info(f"Redirecting to: {redirect_url}")
    
    # Return a redirect response
    return web.HTTPFound(redirect_url)

async def index(request: Request) -> Response:
    """Handle the / endpoint.

    Args:
        request: The HTTP request

    Returns:
        A simple response with API information
    """
    return web.json_response({
        "name": "BahtBrowse Browse API",
        "version": "1.0.0",
        "endpoints": [
            {
                "path": "/browse",
                "method": "GET",
                "description": "Browse a URL in the BahtBrowse container",
                "parameters": [
                    {
                        "name": "url",
                        "type": "string",
                        "required": True,
                        "description": "The URL to browse"
                    }
                ]
            },
            {
                "path": "/api/browse",
                "method": "GET",
                "description": "Browse a URL in the BahtBrowse container (API endpoint)",
                "parameters": [
                    {
                        "name": "url",
                        "type": "string",
                        "required": True,
                        "description": "The URL to browse"
                    }
                ]
            }
        ]
    })

def main() -> None:
    """Initialize and start the API server."""
    # Create the web application
    app = web.Application()
    
    # Add routes
    app.router.add_get("/", index)
    app.router.add_get("/browse", browse)
    app.router.add_get("/api/browse", api_browse)
    
    # Start the server
    web.run_app(app, host="0.0.0.0", port=8083)

if __name__ == "__main__":
    main()
