<!DOCTYPE html>
<html>
<head>
    <title>BahtBrowse Working Connection</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #1a1a1a;
            color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        header {
            background-color: #333;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #444;
        }
        h1 {
            margin: 0;
            font-size: 24px;
        }
        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 16px;
            text-align: center;
        }
        .button:hover {
            background-color: #45a049;
        }
        .info-box {
            background-color: #333;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .connection-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            padding: 10px;
        }
        iframe {
            flex: 1;
            border: none;
            min-height: 500px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <header>
        <h1>BahtBrowse Working Connection</h1>
    </header>
    <div class="container">
        <div class="info-box">
            <h2>Connection Options</h2>
            <p>Choose one of the following connection methods:</p>
            <div class="connection-options">
                <a href="http://localhost:6080/vnc.html?autoconnect=true&resize=remote" class="button" target="_blank">Direct WebSocket Connection</a>
                <a href="http://localhost:8001/vnc/vnc.html?host=localhost&port=6080&path=websockify&autoconnect=true&resize=remote" class="button" target="_blank">Proxied WebSocket Connection</a>
                <a href="http://localhost:8001/new_direct_vnc.html" class="button" target="_blank">Embedded VNC Connection</a>
            </div>
        </div>
        <div class="info-box">
            <h2>Test Results</h2>
            <p>Based on our tests, the following components are working correctly:</p>
            <ul>
                <li>X11 Server (Xvfb) - Running on display :1</li>
                <li>VNC Server (x11vnc) - Running on port 5900</li>
                <li>WebSocket Proxy (websockify) - Running on port 6080</li>
                <li>Nginx - Running on port 80 (mapped to 8001)</li>
                <li>Firefox - Running on the X11 display</li>
            </ul>
            <p>The direct WebSocket connection works reliably, bypassing the Nginx proxy.</p>
            <p>The proxied WebSocket connection works when configured correctly.</p>
            <p>The embedded VNC connection works when using an iframe with the direct WebSocket connection.</p>
        </div>
        <div class="info-box">
            <h2>Direct Connection</h2>
            <p>This iframe connects directly to the WebSocket proxy on port 6080:</p>
            <iframe src="http://localhost:6080/vnc.html?autoconnect=true&resize=remote"></iframe>
        </div>
    </div>
</body>
</html>
