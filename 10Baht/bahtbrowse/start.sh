#!/bin/bash
# Set locale environment variables
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8
export LANGUAGE=en_US.UTF-8

service nginx start

Xvfb :1 -screen 0 1920x1080x24 &
sleep 1

export DISPLAY=:1

x11vnc -display :1 -nopw -listen localhost -xkb -forever &

websockify -D --web=/usr/share/novnc 6080 localhost:5900

openbox &

export PYTHONPATH=/tmp:$PYTHONPATH

/tmp/start_debug.sh &

python3 /tmp/session_validator.py > /tmp/logs/session_validator.log 2>&1 &

chromium-browser --user-data-dir=/tmp/chromium_profile --no-sandbox --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer &

tail -f /dev/null
