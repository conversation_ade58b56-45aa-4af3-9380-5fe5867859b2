{# Template for the search page. #}
{%- extends "layout.html" %}
{% set title = _('Search') %}
{%- block scripts %}
    {{ super() }}
    <script src="{{ pathto('_static/searchtools.js', 1) }}"></script>
    <script src="{{ pathto('_static/language_data.js', 1) }}"></script>
{%- endblock %}
{% block extrahead %}
    <script src="{{ pathto('searchindex.js', 1) }}" defer="defer"></script>
    <meta name="robots" content="noindex" />
    {{ super() }}
{% endblock %}
{% block body %}
  <h1 id="search-documentation">{{ _('Search') }}</h1>
  {% block scriptwarning %}
  <noscript>
  <div class="admonition warning">
  <p>
    {% trans %}Please activate JavaScript to enable the search
    functionality.{% endtrans %}
  </p>
  </div>
  </noscript>
  {% endblock %}
  {% block searchtext %}
  <p>
    {% trans %}Searching for multiple words only shows matches that contain
    all words.{% endtrans %}
  </p>
  {% endblock %}
  {% block searchbox %}
  <form action="" method="get">
    <input type="text" name="q" aria-labelledby="search-documentation" value="" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
    <input type="submit" value="{{ _('search') }}" />
    <span id="search-progress" style="padding-left: 10px"></span>
  </form>
  {% endblock %}
  {% block searchresults %}
  <div id="search-results"></div>
  {% endblock %}
{% endblock %}
