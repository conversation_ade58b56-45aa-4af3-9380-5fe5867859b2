{# Template for a "single" page of a split index. #}
{% macro indexentries(firstname, links) %}
  {%- if links -%}
    <a href="{{ links[0][1] }}">
    {%- if links[0][0] %}<strong>{% endif -%}
    {{ firstname|e }}
    {%- if links[0][0] %}</strong>{% endif -%}
    </a>

    {%- for ismain, link in links[1:] -%}
      , <a href="{{ link }}">{% if ismain %}<strong>{% endif -%}
      [{{ loop.index }}]
      {%- if ismain %}</strong>{% endif -%}
      </a>
    {%- endfor %}
  {%- else %}
    {{ firstname|e }}
  {%- endif %}
{% endmacro %}

{%- extends "layout.html" %}
{% set title = _('Index') %}
{% block body %}

{# We use ``&#x2013;`` instead of ``&ndash;`` for XHTML compatibility #}
<h1 id="index">{% trans key=key %}Index &#x2013; {{ key }}{% endtrans %}</h1>

<table style="width: 100%" class="indextable"><tr>
  {%- for column in entries|slice(2) if column %}
  <td style="width: 33%; vertical-align: top;"><ul>
    {%- for entryname, (links, subitems, _) in column %}
      <li>{{ indexentries(entryname, links) }}
      {%- if subitems %}
      <ul>
      {%- for subentryname, subentrylinks in subitems %}
        <li>{{ indexentries(subentryname, subentrylinks) }}</li>
      {%- endfor %}
      </ul>
      {%- endif -%}</li>
    {%- endfor %}
  </ul></td>
  {%- endfor %}
</tr></table>

{% endblock %}

{% block sidebarrel %}
   <h4>{{ _('Index') }}</h4>
   <p>{% for key, dummy in genindexentries -%}
   <a href="{{ pathto('genindex-' + key) }}"><strong>{{ key }}</strong></a>
     {% if not loop.last %}| {% endif %}
   {%- endfor %}</p>

   <p><a href="{{ pathto('genindex-all') }}"><strong>{{ _('Full index on one page') }}</strong></a></p>
   {{ super() }}
{% endblock %}
