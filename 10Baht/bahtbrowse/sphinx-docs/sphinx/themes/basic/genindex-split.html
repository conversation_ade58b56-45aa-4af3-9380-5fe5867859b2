{# Template for a "split" index overview page. #}
{%- extends "layout.html" %}
{% set title = _('Index') %}
{% block body %}

   <h1 id="index">{{ _('Index') }}</h1>

   <p>{{ _('Index pages by letter') }}:</p>

   <div class="genindex-jumpbox">
   <p>{% for key, dummy in genindexentries -%}
   <a href="{{ pathto('genindex-' + key) }}"><strong>{{ key }}</strong></a>
     {% if not loop.last %}| {% endif %}
   {%- endfor %}</p>

   <p><a href="{{ pathto('genindex-all') }}"><strong>{{ _('Full index on one page') }}</strong>
                                               ({{ _('can be huge') }})</a></p>
   </div>

{% endblock %}

{% block sidebarrel %}
{% if split_index %}
   <h4>Index</h4>
   <p>{% for key, dummy in genindexentries -%}
   <a href="{{ pathto('genindex-' + key) }}"><strong>{{ key }}</strong></a>
     {% if not loop.last %}| {% endif %}
   {%- endfor %}</p>

   <p><a href="{{ pathto('genindex-all') }}"><strong>{{ _('Full index on one page') }}</strong></a></p>
{% endif %}
   {{ super() }}
{% endblock %}
