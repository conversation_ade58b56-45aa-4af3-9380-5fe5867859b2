\NeedsTeXFormat{LaTeX2e}
\ProvidesPackage{sphinxpackagefootnote}%
 [2024/05/17 v7.3.x Sphinx custom footnotehyper package (Sphinx team)]
%%
%% Package: sphinxpackagefootnote
%% Version: based on footnotehyper.sty 2021/02/04 v1.1d
%%          https://www.ctan.org/pkg/footnotehyper
%% License: the one applying to Sphinx
%%
% Provides support for footnote mark-up from Sphinx latex writer:
% - "footnote" and "footnotetext"  environments allowing verbatim material
% - "savenotes" environment for wrapping environments, such as for tables
%   which have problems with LaTeX footnotes
% - hyperlinks
% 
% Sphinx uses exclusively this mark-up for footnotes:
% - \begin{footnote}[N]
% - \begin{footnotetext}[N]
% - \sphinxfootnotemark[N]
% where N is a number.
%
%% Some small differences from upstream footnotehyper.sty:
%% - a tabulary compatibility layer (partial but enough for Sphinx),
%% - usage of \spx@opt@BeforeFootnote
%% - usage of \sphinxunactivateextrasandspace from sphinx.sty,
%% - \sphinxlongtablepatch
%%
%% Starting with Sphinx v4.5.0, inherited footnotehyper macros for
%% footnote/footnotetext receive some Sphinx specific extras to
%% implement "intelligent" footnote marks checking page numbers.
%%
%% All footnotes output from Sphinx are hyperlinked. With "savenotes"
%% footnotes may appear on page distinct from footnote mark, the latter
%% will indicate page number of the footnote.
\newif\iffootnotehyperparse\footnotehyperparsetrue
\DeclareOption*{\PackageWarning{sphinxpackagefootnote}{Option `\CurrentOption' is unknown}}%
\ProcessOptions\relax
\newbox\FNH@notes
\newtoks\FNH@toks % 1.1c
\newdimen\FNH@width
\let\FNH@colwidth\columnwidth
\newif\ifFNH@savingnotes
\AtBeginDocument {%
    \let\FNH@latex@footnote    \footnote
    \let\FNH@latex@footnotetext\footnotetext
    \let\FNH@H@@footnotetext   \@footnotetext
    \let\FNH@H@@mpfootnotetext \@mpfootnotetext
    \newenvironment{savenotes}
        {\FNH@savenotes\ignorespaces}{\FNH@spewnotes\ignorespacesafterend}%
    \let\spewnotes      \FNH@spewnotes
    \let\footnote       \FNH@footnote
    \let\footnotetext   \FNH@footnotetext
    \let\endfootnote    \FNH@endfntext
    \let\endfootnotetext\FNH@endfntext
    % always True branch taken with Sphinx
    \@ifpackageloaded{hyperref}
     {\ifHy@hyperfootnotes
         \let\FNH@H@@footnotetext\H@@footnotetext
         \let\FNH@H@@mpfootnotetext\H@@mpfootnotetext
      \else
         \let\FNH@hyper@fntext\FNH@nohyp@fntext
      \fi}%
     {\let\FNH@hyper@fntext\FNH@nohyp@fntext}%
}%
\def\FNH@hyper@fntext{\FNH@fntext\FNH@hyper@fntext@i}%
\def\FNH@nohyp@fntext{\FNH@fntext\FNH@nohyp@fntext@i}%
\def\FNH@fntext #1{%
  \ifx\ifmeasuring@\@undefined
    \expandafter\@secondoftwo\else\expandafter\@firstofone\fi
% these two lines modified for Sphinx (tabulary compatibility):
    {\ifmeasuring@\expandafter\@gobbletwo\else\expandafter\@firstofone\fi}%
    {\ifx\equation$\expandafter\@gobbletwo\fi #1}%$
}%
\long\def\FNH@hyper@fntext@i#1{%
  \global\setbox\FNH@notes\vbox
  {\unvbox\FNH@notes
   \FNH@startnote
   \@makefntext
    {\rule\z@\footnotesep\ignorespaces
     \ifHy@nesting\expandafter\ltx@firstoftwo
             \else\expandafter\ltx@secondoftwo
     \fi
     {\expandafter\hyper@@anchor\expandafter{\Hy@footnote@currentHref}{#1}}%
     {\Hy@raisedlink
       {\expandafter\hyper@@anchor\expandafter{\Hy@footnote@currentHref}%
       {\relax}}%
      \let\@currentHref\Hy@footnote@currentHref
      \let\@currentlabelname\@empty
      #1}%
     \@finalstrut\strutbox
    }%
   \FNH@endnote
  }%
}%
\long\def\FNH@nohyp@fntext@i#1{%
  \global\setbox\FNH@notes\vbox
  {\unvbox\FNH@notes
   \FNH@startnote
   \@makefntext{\rule\z@\footnotesep\ignorespaces#1\@finalstrut\strutbox}%
   \FNH@endnote
  }%
}%
\def\FNH@startnote{%
  \hsize\FNH@colwidth
  \interlinepenalty\interfootnotelinepenalty
  \reset@font\footnotesize
  \floatingpenalty\@MM
  \@parboxrestore
  \protected@edef\@currentlabel{\csname p@\@mpfn\endcsname\@thefnmark}%
  \color@begingroup
}%
\def\FNH@endnote{\color@endgroup}%
\def\FNH@savenotes{%
  \begingroup
  \ifFNH@savingnotes\else
    \FNH@savingnotestrue
    \let\@footnotetext    \FNH@hyper@fntext
    \let\@mpfootnotetext  \FNH@hyper@fntext
    \let\H@@mpfootnotetext\FNH@nohyp@fntext
    \FNH@width\columnwidth
    \let\FNH@colwidth\FNH@width
    \global\setbox\FNH@notes\box\voidb@x
    \let\FNH@thempfn\thempfn
    \let\FNH@mpfn\@mpfn
    \ifx\@minipagerestore\relax\let\@minipagerestore\@empty\fi
    \expandafter\def\expandafter\@minipagerestore\expandafter{%
      \@minipagerestore
      \let\thempfn\FNH@thempfn
      \let\@mpfn\FNH@mpfn
    }%
  \fi
}%
\def\FNH@spewnotes {%
  \if@endpe\ifx\par\@@par\FNH@toks{}\else
     \FNH@toks\expandafter{\expandafter
              \def\expandafter\par\expandafter{\par}\@endpetrue}%
     \expandafter\expandafter\expandafter
     \FNH@toks
     \expandafter\expandafter\expandafter
     {\expandafter\the\expandafter\FNH@toks
      \expandafter\def\expandafter\@par\expandafter{\@par}}%
     \expandafter\expandafter\expandafter
     \FNH@toks
     \expandafter\expandafter\expandafter
     {\expandafter\the\expandafter\FNH@toks
      \expandafter\everypar\expandafter{\the\everypar}}\fi
  \else\FNH@toks{}\fi
  \expandafter
  \endgroup\the\FNH@toks
  \ifFNH@savingnotes\else
   \ifvoid\FNH@notes\else
    \begingroup
     \let\@makefntext\@empty
     \let\@finalstrut\@gobble
     \let\rule\@gobbletwo
     \ifx\@footnotetext\@mpfootnotetext
        \expandafter\FNH@H@@mpfootnotetext
     \else
        \expandafter\FNH@H@@footnotetext
     \fi{\unvbox\FNH@notes}%
    \endgroup
   \fi
  \fi
}%
\def\FNH@footnote@envname    {footnote}%
\def\FNH@footnotetext@envname{footnotetext}%
\def\FNH@footnote{%
% this line added for Sphinx:
    \spx@opt@BeforeFootnote
    \ifx\@currenvir\FNH@footnote@envname
        \expandafter\FNH@footnoteenv
    \else
        \expandafter\FNH@latex@footnote
    \fi
}%
\def\FNH@footnoteenv{%
% this line added for Sphinx (footnotes in parsed literal blocks):
    \catcode13=5 \sphinxunactivateextrasandspace
    \@ifnextchar[%
      \FNH@footnoteenv@i %]
      {\stepcounter\@mpfn
       \protected@xdef\@thefnmark{\thempfn}%
       \@footnotemark
       \def\FNH@endfntext@fntext{\@footnotetext}%
       \FNH@startfntext}%
}%
\def\FNH@footnoteenv@i[#1]{%
    \begingroup
     % This legacy code from LaTeX core restricts #1 to be digits only
     % This limitation could be lifted but legacy Sphinx anyhow obeys it
     \csname c@\@mpfn\endcsname #1\relax
     \unrestored@protected@xdef\@thefnmark{\thempfn}%
    \endgroup
% -- Sphinx specific:
% currently commented out due to 
% https://github.com/sphinx-doc/sphinx/pull/10191#issuecomment-1038807448
% Memo: memoir class detection of successive footnote marks (to separate them
%       by commas) is broken by \refstepcounter and also by \label, and some
%       mitigation such as in \sphinxfootref would be needed
      % \global\let\spx@saved@thefnmark\@thefnmark
      % % this is done to access robustly the page number where footnote mark is
      % \refstepcounter{sphinxfootnotemark}\label{footnotemark.\thesphinxfootnotemark}%
      % % if possible, compare page numbers of mark and footnote to define \@thefnmark 
      % \ltx@ifundefined{r@\thesphinxscope.footnote.#1}%
      % {}%                           one more latex run is needed
      % {\sphinx@xdef@thefnmark{#1}}% check of page numbers possible
% --
    \@footnotemark
    \def\FNH@endfntext@fntext{\@footnotetext}%
% -- Sphinx specific:
      % we need to reset \@thefnmark as it is used by \FNH@startfntext via
      % \FNH@startnote to set \@currentlabel which will be used by \label
% currently commented out (see above)
      % \global\let\@thefnmark\spx@saved@thefnmark
% --
    \FNH@startfntext
% -- again Sphinx specific
      % \@currentlabel as needed by \label got set by \FNH@startnote
      % insert this at start of footnote text then the label will allow
      % to robustly know on which page the footnote text ends up
% currently only of use for extra footnote marks so in case footnote multiply referred
      \phantomsection\label{\thesphinxscope.footnote.#1}%
}%
\def\FNH@footnotetext{%
    \ifx\@currenvir\FNH@footnotetext@envname
        \expandafter\FNH@footnotetextenv
    \else
        \expandafter\FNH@latex@footnotetext
    \fi
}%
\def\FNH@footnotetextenv{%
    \@ifnextchar[%
      \FNH@footnotetextenv@i %]
      {\protected@xdef\@thefnmark{\thempfn}%
       \def\FNH@endfntext@fntext{\@footnotetext}%
       \FNH@startfntext}%
}%
\def\FNH@footnotetextenv@i[#1]{%
    \begingroup
     \csname c@\@mpfn\endcsname #1\relax
     \unrestored@protected@xdef\@thefnmark{\thempfn}%
    \endgroup
    \ifFNH@savingnotes
      \def\FNH@endfntext@fntext{\FNH@nohyp@fntext}%
    \else
      \def\FNH@endfntext@fntext{\FNH@H@@footnotetext}%
    \fi
    \FNH@startfntext
% -- Sphinx specific addition
    \phantomsection\label{\thesphinxscope.footnote.#1}%
}%
\def\FNH@startfntext{%
  \setbox\z@\vbox\bgroup
    \FNH@startnote
    \FNH@prefntext
    \rule\z@\footnotesep\ignorespaces
}%
\def\FNH@endfntext {%
    \@finalstrut\strutbox
    \FNH@postfntext
    \FNH@endnote
    \egroup
  \begingroup
    \let\@makefntext\@empty\let\@finalstrut\@gobble\let\rule\@gobbletwo
    \FNH@endfntext@fntext {\unvbox\z@}%
  \endgroup
}%
\let\FNH@prefntext\@empty
\let\FNH@postfntext\@empty
\AtBeginDocument{\iffootnotehyperparse\expandafter\FNH@check\fi}%
\def\FNH@safeif#1{%
   \iftrue\csname if#1\endcsname\csname fi\endcsname\expandafter\@firstoftwo
   \else\csname fi\endcsname\expandafter\@secondoftwo
   \fi
}%
\def\FNH@check{%
   \ifx\@makefntextFB\@undefined\expandafter\FNH@check@
                           \else\expandafter\FNH@frenchb@
   \fi
}%
\def\FNH@frenchb@{%
   \def\FNH@prefntext{%
     \localleftbox{}%
     \let\FBeverypar@save\FBeverypar@quote
     \let\FBeverypar@quote\relax
     \FNH@safeif{FB@koma}%
       {\FNH@safeif{FBFrenchFootnotes}%
          {\ifx\footnote\thanks
             \let\@@makefnmark\@@makefnmarkTH
             \@makefntextTH{} % space as in french.ldf
           \else
             \let\@@makefnmark\@@makefnmarkFB
             \@makefntextFB{} % space as in french.ldf
             \fi
          }{\let\@@makefnmark\@@makefnmarkORI
             \@makefntextORI{}% no space as in french.ldf
          }%
       }%
       {\FNH@safeif{FBFrenchFootnotes}%
          {\@makefntextFB{}}%
          {\@makefntextORI{}}%
       }%
   }%
   \def\FNH@postfntext{%
     \let\FBeverypar@quote\FBeverypar@save
     \localleftbox{\FBeveryline@quote}%
   }%
}%
\def\FNH@check@{%
    \expandafter\FNH@check@a\@makefntext{1.2!3?4,}%
                \FNH@@@1.2!3?4,\FNH@@@\relax
}%
\long\def\FNH@check@a #11.2!3?4,#2\FNH@@@#3{%
    \ifx\relax#3\expandafter\FNH@checkagain@
    \else
      \def\FNH@prefntext{#1}\def\FNH@postfntext{#2}%
      \expandafter\FNH@check@b
    \fi
}%
\def\FNH@checkagain@{%
    \expandafter\FNH@checkagain@a
    \detokenize\expandafter{\@makefntext{1.2!3?4,}}\relax\FNH@@@
}%
\edef\FNH@temp{\noexpand\FNH@checkagain@a ##1\string{1.2!3?4,\string}}%
\expandafter\def\FNH@temp#2#3\FNH@@@{%
    \ifx\relax#2%
      \def\FNH@prefntext{\@makefntext{}}%
    \else\FNH@bad@makefntext@alert
    \fi
}%
\def\FNH@check@b #1\relax{%
    \expandafter\expandafter\expandafter\FNH@check@c
    \expandafter\meaning\expandafter\FNH@prefntext
    \meaning\FNH@postfntext1.2!3?4,\FNH@check@c\relax
}%
\def\FNH@check@c #11.2!3?4,#2#3\relax{%
    \ifx\FNH@check@c#2\else\FNH@bad@makefntext@alert\fi
}%
% slight reformulation for Sphinx
\def\FNH@bad@makefntext@alert{%
  \sphinxbuildwarning{badfootnotes}%
  \PackageWarningNoLine{sphinxpackagefootnote}%
    {Footnotes will be sub-optimal, sorry. This is due to the document class or^^J
  some package modifying macro \string\@makefntext.^^J
  You can try to report this incompatibility at^^J
  https://github.com/sphinx-doc/sphinx with this info:}%
    \typeout{\meaning\@makefntext}%
    \let\FNH@prefntext\@empty\let\FNH@postfntext\@empty
}%
% this macro from original footnote.sty is not used anymore by Sphinx
% but for simplicity sake let's just keep it as is
\def\makesavenoteenv{\@ifnextchar[\FNH@msne@ii\FNH@msne@i}%]
\def\FNH@msne@i #1{%
  \expandafter\let\csname FNH$#1\expandafter\endcsname %$
                  \csname #1\endcsname
  \expandafter\let\csname endFNH$#1\expandafter\endcsname %$
                  \csname end#1\endcsname
  \FNH@msne@ii[#1]{FNH$#1}%$
}%
\def\FNH@msne@ii[#1]#2{%
  \expandafter\edef\csname#1\endcsname{%
    \noexpand\savenotes
    \expandafter\noexpand\csname#2\endcsname
  }%
  \expandafter\edef\csname end#1\endcsname{%
    \expandafter\noexpand\csname end#2\endcsname
    \noexpand\expandafter
    \noexpand\spewnotes
    \noexpand\if@endpe\noexpand\@endpetrue\noexpand\fi
  }%
}%
%
% some extras for Sphinx :
% \sphinxfootnotemark:
% - if in section titles will auto-remove itself from TOC
\def\sphinxfootnotemark [#1]%
   {\ifx\thepage\relax\else\sphinxfootref{#1}\fi}%
\newcounter{sphinxfootnotemark}
\renewcommand\thesphinxfootnotemark{\number\value{sphinxfootnotemark}}
% - compares page number of footnote mark versus the one of footnote text
\def\sphinx@xdef@thefnmark#1{%
   \expandafter\expandafter\expandafter\sphinx@footref@get
               \csname r@\thesphinxscope.footnote.#1\endcsname\relax
   \expandafter\expandafter\expandafter\sphinx@footmark@getpage
               \csname r@footnotemark.\thesphinxfootnotemark\endcsname\thepage\relax
   \protected@xdef\@thefnmark{%
     \ifx\spx@footmarkpage\spx@footrefpage
       \spx@footreflabel
     \else
       % the macro \sphinxthefootnotemark is in sphinx.sty
       \sphinxthefootnotemark{\spx@footreflabel}{\spx@footrefpage}%
     \fi
     }%
}%
\def\sphinx@footref@get #1#2#3#4#5\relax{%
    \def\spx@footreflabel{#1}%
    \def\spx@footrefpage {#2}%
    \def\spx@footrefHref {#4}%
}%
\def\sphinx@footmark@getpage #1#2#3\relax{%
    \edef\spx@footmarkpage{#2}%
}%
\protected\def\sphinxfootref#1{% #1 always is explicit number in Sphinx
  \spx@opt@BeforeFootnote
  % each of \refstepcounter and \label interferes with memoir class detection
  % of successive footnote marks, so we move them to inside \@makefnmark
  \let\spx@saved@makefnmark\@makefnmark
  \ltx@ifundefined{r@\thesphinxscope.footnote.#1}%
    {\gdef\@thefnmark{?}% on first LaTeX run
     \refstepcounter{sphinxfootnotemark}\label{footnotemark.\thesphinxfootnotemark}%
    }%
    {\def\@makefnmark{% will be used by \H@@footnotemark
       \refstepcounter{sphinxfootnotemark}\label{footnotemark.\thesphinxfootnotemark}%
       \sphinx@xdef@thefnmark{#1}% also defines \spx@footrefHref
                                 % must be executed after \refstepcounter
       \hyper@linkstart{link}{\spx@footrefHref}%
       \spx@saved@makefnmark
       \hyper@linkend
      }%
    }%
  \H@@footnotemark
  \let\@makefnmark\spx@saved@makefnmark
}%  
\AtBeginDocument{%
   % let hyperref less complain
   \pdfstringdefDisableCommands{\def\sphinxfootnotemark [#1]{}}%
   % to obtain hyperlinked footnotes in longtable environment we must replace
   % hyperref's patch of longtable's patch of \@footnotetext by our own
   \let\LT@p@ftntext\FNH@hyper@fntext
   % this *requires* longtable to be used always wrapped in savenotes environment
}%
\endinput
%%
%% End of file `sphinxpackagefootnote.sty'.
