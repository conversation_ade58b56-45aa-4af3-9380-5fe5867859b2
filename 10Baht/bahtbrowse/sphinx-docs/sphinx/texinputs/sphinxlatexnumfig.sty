%% NUMBERING OF FIGURES, TABLES, AND LITERAL BLOCKS
%
% change this info string if making any custom modification
\ProvidesPackage{sphinxlatexnumfig}[2024/07/31 v8.1.0 numbering]

% Requires: remreset (old LaTeX only)
% relates to numfig and numfig_secnum_depth configuration variables

% LaTeX 2018-04-01 and later provides \@removefromreset
\ltx@ifundefined{@removefromreset}
    {\RequirePackage{remreset}}
    {}% avoid warning
% Everything is delayed to \begin{document} to allow hyperref patches into
%   \newcounter to solve duplicate label problems for internal hyperlinks to
%   code listings (literalblock counter).  User or extension re-definitions of
%   \theliteralblock, et al., thus have also to be delayed. (changed at 3.5.0)
\AtBeginDocument{%
\ltx@ifundefined{c@chapter}
   {\newcounter{literalblock}}%
   {\newcounter{literalblock}[chapter]%
    \def\theliteralblock{\ifnum\c@chapter>\z@\arabic{chapter}.\fi
                         \arabic{literalblock}}%
    }%
\ifspx@opt@nonumfigreset
    \ltx@ifundefined{c@chapter}{}{%
      \@removefromreset{figure}{chapter}%
      \@removefromreset{table}{chapter}%
      \@removefromreset{literalblock}{chapter}%
      \ifspx@opt@mathnumfig
        \@removefromreset{equation}{chapter}%
      \fi
    }%
    \def\thefigure{\arabic{figure}}%
    \def\thetable {\arabic{table}}%
    \def\theliteralblock{\arabic{literalblock}}%
    \ifspx@opt@mathnumfig
      \def\theequation{\arabic{equation}}%
    \fi
\else
% See apologetic comments on TeX wizardry at bottom of file.
% The reason for this one is to catch case where there will be only
% the number with no prefix from enclosing sectioning (can happen
% with latex_toplevel_sectioning='part').
\def\spx@preAthefigure{\expandafter\spx@magicsep@s\romannumeral-`0}
\let\spx@preBthefigure\@empty
% \ifspx@opt@usespart  % <-- LaTeX writer could pass such a 'usespart' boolean
%                      %     as sphinx.sty package option
% If document uses \part, (triggered in Sphinx by latex_toplevel_sectioning)
% LaTeX core per default does not reset chapter or section
% counters at each part.
% But if we modify this, we need to redefine \thechapter, \thesection to
% include the part number and this will cause problems in table of contents
% because of too wide numbering. Simplest is to do nothing.
% \fi
\ifnum\spx@opt@numfigreset>0
    \ltx@ifundefined{c@chapter}
      {}
      {\g@addto@macro\spx@preAthefigure{\ifnum\c@chapter>\z@\arabic{chapter}\spx@magicsep}%
       \g@addto@macro\spx@preBthefigure{\fi}}%
\fi
\ifnum\spx@opt@numfigreset>1
    \@addtoreset{figure}{section}%
    \@addtoreset{table}{section}%
    \@addtoreset{literalblock}{section}%
    \ifspx@opt@mathnumfig
      \@addtoreset{equation}{section}%
    \fi%
    \g@addto@macro\spx@preAthefigure{\ifnum\c@section>\z@\arabic{section}\spx@magicsep}%
    \g@addto@macro\spx@preBthefigure{\fi}%
\fi
\ifnum\spx@opt@numfigreset>2
    \@addtoreset{figure}{subsection}%
    \@addtoreset{table}{subsection}%
    \@addtoreset{literalblock}{subsection}%
    \ifspx@opt@mathnumfig
      \@addtoreset{equation}{subsection}%
    \fi%
    \g@addto@macro\spx@preAthefigure{\ifnum\c@subsection>\z@\arabic{subsection}\spx@magicsep}%
    \g@addto@macro\spx@preBthefigure{\fi}%
\fi
\ifnum\spx@opt@numfigreset>3
    \@addtoreset{figure}{subsubsection}%
    \@addtoreset{table}{subsubsection}%
    \@addtoreset{literalblock}{subsubsection}%
    \ifspx@opt@mathnumfig
      \@addtoreset{equation}{subsubsection}%
    \fi%
    \g@addto@macro\spx@preAthefigure{\ifnum\c@subsubsection>\z@\arabic{subsubsection}\spx@magicsep}%
    \g@addto@macro\spx@preBthefigure{\fi}%
\fi
\ifnum\spx@opt@numfigreset>4
    \@addtoreset{figure}{paragraph}%
    \@addtoreset{table}{paragraph}%
    \@addtoreset{literalblock}{paragraph}%
    \ifspx@opt@mathnumfig
      \@addtoreset{equation}{paragraph}%
    \fi%
    \g@addto@macro\spx@preAthefigure{\ifnum\c@subparagraph>\z@\arabic{subparagraph}\spx@magicsep}%
    \g@addto@macro\spx@preBthefigure{\fi}%
\fi
\ifnum\spx@opt@numfigreset>5
    \@addtoreset{figure}{subparagraph}%
    \@addtoreset{table}{subparagraph}%
    \@addtoreset{literalblock}{subparagraph}%
    \ifspx@opt@mathnumfig
      \@addtoreset{equation}{subparagraph}%
    \fi%
    \g@addto@macro\spx@preAthefigure{\ifnum\c@subsubparagraph>\z@\arabic{subsubparagraph}\spx@magicsep}%
    \g@addto@macro\spx@preBthefigure{\fi}%
\fi
\expandafter\g@addto@macro
\expandafter\spx@preAthefigure\expandafter{\spx@preBthefigure}%
\let\thefigure\spx@preAthefigure
\let\thetable\spx@preAthefigure
\let\theliteralblock\spx@preAthefigure
\g@addto@macro\thefigure{\arabic{figure}}%
\g@addto@macro\thetable{\arabic{table}}%
\g@addto@macro\theliteralblock{\arabic{literalblock}}%
  \ifspx@opt@mathnumfig
    \let\theequation\spx@preAthefigure
    \g@addto@macro\theequation{E}%
  \fi
\fi
}% end of big \AtBeginDocument

% Sorry for TeX wizardry here.  We need to keep expandability.  Explaining
% the mechanism is not really feasible to non TeX-experts, but the idea
% is to force next `\ifnum` conditional so we can check what comes next.
% All cases are accounted for (i.e. not an equation, or an equation at top
% level, or an equation in some section at some depth).
\def\spx@magicsep{\expandafter\spx@magicsep@i\romannumeral-`0}
\def\spx@magicsep@i#1{\if#1E\spx@opt@mathnumsep\arabic{equation}\else.#1\fi}
%
\def\spx@magicsep@s#1{\if#1E\arabic{equation}\else#1\fi}
\endinput
