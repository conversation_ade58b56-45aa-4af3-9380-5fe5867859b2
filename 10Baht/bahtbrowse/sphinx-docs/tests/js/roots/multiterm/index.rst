Main Page
=========

This is the main page of the ``multiterm`` test project.

This document is used as a test fixture to check that the search functionality
included when projects are built into an HTML output format can successfully
match this document when a search query containing multiple terms is performed.

At the time-of-writing this message, the application doesn't support "phrase
queries" -- queries that require all of the contained terms to appear adjacent
to each other and in the same order in the document as in the query; perhaps it
will do in future?
