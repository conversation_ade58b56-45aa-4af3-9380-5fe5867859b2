Relevance
=========

In some domains, it can be straightforward to determine whether a search result
is relevant to the user's query.

For example, if we are in a software programming language domain, and a user
has issued a query for the term ``printf``, then we could consider a document
in the corpus that describes a built-in language function with the same name
as (highly) relevant.  A document that only happens to mention the ``printf``
function name as part of some example code that appears on the page would
also be relevant, but likely less relevant than the one that describes the
function itself in detail.
