.. cpp:type:: _class __class

	- :any:`_class`
	- :cpp:any:`_class`
	- :cpp:class:`_class`
	- :cpp:struct:`_class`
	- :cpp:type:`_class`

.. cpp:type:: _struct __struct

	- :any:`_struct`
	- :cpp:any:`_struct`
	- :cpp:class:`_struct`
	- :cpp:struct:`_struct`
	- :cpp:type:`_struct`

.. cpp:type:: _union __union

	- :any:`_union`
	- :cpp:any:`_union`
	- :cpp:union:`_union`
	- :cpp:type:`_union`

.. cpp:member:: void __function = _function

	- :any:`_function`
	- :cpp:any:`_function`
	- :cpp:func:`_function`
	- :cpp:type:`_function`

.. cpp:member:: void __member = _member

	- :any:`_member`
	- :cpp:any:`_member`
	- :cpp:member:`_member`
	- :cpp:var:`_member`

.. cpp:member:: void __var = _var

	- :any:`_var`
	- :cpp:any:`_var`
	- :cpp:member:`_var`
	- :cpp:var:`_var`

.. cpp:type:: _type __type

	- :any:`_type`
	- :cpp:any:`_type`
	- :cpp:type:`_type`

.. cpp:function:: template<_concept T> void __concept()

	- :any:`_concept`
	- :cpp:any:`_concept`
	- :cpp:concept:`_concept`

.. cpp:type:: _enum __enum

	- :any:`_enum`
	- :cpp:any:`_enum`
	- :cpp:enum:`_enum`
	- :cpp:type:`_enum`

.. cpp:type:: _enumStruct __enumStruct

	- :any:`_enumStruct`
	- :cpp:any:`_enumStruct`
	- :cpp:enum:`_enumStruct`
	- :cpp:type:`_enumStruct`

.. cpp:type:: _enumClass __enumClass

	- :any:`_enumClass`
	- :cpp:any:`_enumClass`
	- :cpp:enum:`_enumClass`
	- :cpp:type:`_enumClass`

.. cpp:member:: void __enumerator = _enumerator

	- :any:`_enumerator`
	- :cpp:any:`_enumerator`
	- :cpp:enumerator:`_enumerator`

.. cpp:member:: void __scopedEnumerator = _enumStruct::_scopedEnumerator

	- :any:`_enumStruct::_scopedEnumerator`
	- :cpp:any:`_enumStruct::_scopedEnumerator`
	- :cpp:enumerator:`_enumStruct::_scopedEnumerator`

.. cpp:member:: void __enumerator2 = _enum::_enumerator

	- :any:`_enum::_enumerator`
	- :cpp:any:`_enum::_enumerator`
	- :cpp:enumerator:`_enum::_enumerator`

.. cpp:member:: void __functionParam = _functionParam::param

	- :any:`_functionParam::param`
	- :cpp:any:`_functionParam::param`
	- :cpp:member:`_functionParam::param`
	- :cpp:var:`_functionParam::param`

.. cpp:type:: _templateParam::TParam __templateParam

	- :any:`_templateParam::TParam`
	- :cpp:any:`_templateParam::TParam`
	- :cpp:type:`_templateParam::TParam`
	- :cpp:member:`_templateParam::TParam`
	- :cpp:var:`_templateParam::TParam`
	- :cpp:class:`_templateParam::TParam`
	- :cpp:struct:`_templateParam::TParam`
	- :cpp:union:`_templateParam::TParam`
