:tocdepth: 2

i18n with docfields
===================

.. single TypedField

.. class:: Cls1
   :no-index:

   :param param: description of parameter param

.. grouped TypedFields

.. class:: Cls2
   :no-index:

   :param foo: description of parameter foo
   :param bar: description of parameter bar


.. single GroupedField

.. class:: Cls3(values)
   :no-index:

   :raises ValueError: if the values are out of range

.. grouped GroupedFields

.. class:: Cls4(values)
   :no-index:

   :raises TypeError: if the values are not valid
   :raises ValueError: if the values are out of range


.. single Field

.. class:: Cls5
   :no-index:

   :returns: a new :class:`Cls3` instance

.. Field is never grouped

