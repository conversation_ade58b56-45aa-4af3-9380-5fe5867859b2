:tocdepth: 2

.. _implicit-target:

section and label
==================

.. This section's label and section title are different.
.. This case, the section have 2 target id.

:ref:`implicit-target` point to ``implicit-target`` and
`section and label`_ point to ``section-and-label``.


.. _explicit-target:

explicit-target
================

.. This section's label equals to section title.
.. This case, a duplicated target id is generated by docutils.

:ref:`explicit-target` point to ``explicit-target`` and
`explicit-target`_ point to duplicated id like ``id1``.


implicit section name
======================

.. This section have no label.
.. This case, the section have one id.

`implicit section name`_ point to ``implicit-section-name``.

duplicated sub section
------------------------

.. This section have no label, but name will be duplicated by next section.
.. This case, the section have one id.

`duplicated sub section`_ is broken link.

.. There is no way to link to this section's ``duplicated-sub-section``` by
.. using formal reStructuredText markup.

duplicated sub section
------------------------

.. This section have no label, but the section was a duplicate name.
.. This case, a duplicated target id is generated by docutils.

.. There is no way to link to this section's duplicated id like ``id2`` by
.. using formal reStructuredText markup.


.. _bridge label: `label bridged target section`_
.. _bridge label2: `section and label`_

label bridged target section
=============================

.. This section is targeted through label definition.

`bridge label`_ is not translatable but linked to translated section title.

`bridge label2`_ point to ``section and label`` and `bridge label`_ point to ``label bridged target section``. The second appeared `bridge label2`_ point to correct target.

