roles
-----

* :cpp:class:`Sphinx`
* :cpp:member:`Sphinx::version`
* :cpp:var:`version`
* :cpp:type:`List`
* :cpp:enum:`MyEnum`

* ref function without parens :cpp:func:`paren_1`
* ref function with parens :cpp:func:`paren_2()`
* ref function without parens, explicit title :cpp:func:`paren_3_title <paren_3>`
* ref function with parens, explicit title :cpp:func:`paren_4_title <paren_4()>`
* ref op call without parens :cpp:func:`paren_5::operator()`
* ref op call with parens :cpp:func:`paren_6::operator()()`
* ref op call without parens, explicit title :cpp:func:`paren_7_title <paren_7::operator()>`
* ref op call with parens, explicit title :cpp:func:`paren_8_title <paren_8::operator()()>`
