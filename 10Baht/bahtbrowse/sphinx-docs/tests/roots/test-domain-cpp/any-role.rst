any role
--------

* :cpp:any:`Sphinx`
* :cpp:any:`Sphinx::version`
* :cpp:any:`version`
* :cpp:any:`List`
* :cpp:any:`MyEnum`

* ref function without parens :cpp:any:`paren_1`
* ref function with parens :cpp:any:`paren_2()`
* ref function without parens, explicit title :cpp:any:`paren_3_title <paren_3>`
* ref function with parens, explicit title :cpp:any:`paren_4_title <paren_4()>`
* ref op call without parens :cpp:any:`paren_5::operator()`
* ref op call with parens :cpp:any:`paren_6::operator()()`
* ref op call without parens, explicit title :cpp:any:`paren_7_title <paren_7::operator()>`
* ref op call with parens, explicit title :cpp:any:`paren_8_title <paren_8::operator()()>`
