.. default-domain:: cpp

.. namespace:: RolesTargetsWarn

.. class:: Class

	class
	struct
	:union:`Class`
	:func:`Class`
	:member:`Class`
	:var:`Class`
	type
	:concept:`Class`
	:enum:`Class`
	:enumerator:`Class`

.. union:: Union

	:class:`Union`
	:struct:`Union`
	union
	:func:`Union`
	:member:`Union`
	:var:`Union`
	type
	:concept:`Union`
	:enum:`Union`
	:enumerator:`Union`

.. function:: void Function()

	:class:`Function`
	:struct:`Function`
	:union:`Function`
	func
	:member:`Function`
	:var:`Function`
	type
	:concept:`Function`
	:enum:`Function`
	:enumerator:`Function`

.. var:: int Variable

	:class:`Variable`
	:struct:`Variable`
	:union:`Variable`
	:func:`Variable`
	member
	var
	:type:`Variable`
	:concept:`Variable`
	:enum:`Variable`
	:enumerator:`Variable`

.. type:: Type = void

	:class:`Type`
	:struct:`Type`
	:union:`Type`
	:func:`Type`
	:member:`Type`
	:var:`Type`
	type
	:concept:`Type`
	:enum:`Type`
	:enumerator:`Type`

.. concept:: template<typename T> Concept

	:class:`Concept`
	:struct:`Concept`
	:union:`Concept`
	:func:`Concept`
	:member:`Concept`
	:var:`Concept`
	:type:`Concept`
	concept
	:enum:`Concept`
	:enumerator:`Concept`

.. enum-struct:: Enum

	:class:`Enum`
	:struct:`Enum`
	:union:`Enum`
	:func:`Enum`
	:member:`Enum`
	:var:`Enum`
	type
	:concept:`Enum`
	enum
	:enumerator:`Enum`

	.. enumerator:: Enumerator

	:class:`Enumerator`
	:struct:`Enumerator`
	:union:`Enumerator`
	:func:`Enumerator`
	:member:`Enumerator`
	:var:`Enumerator`
	:type:`Enumerator`
	:concept:`Enumerator`
	:enum:`Enumerator`
	enumerator

.. class:: template<typename TParamType, \
                    int TParamVar, \
                    template<typename> typename TParamTemplate \
                     > ClassTemplate

	class
	struct
	union
	:func:`TParamType`
	member
	var
	type
	:concept:`TParamType`
	:enum:`TParamType`
	:enumerator:`TParamType`

	class
	struct
	union
	:func:`TParamVar`
	member
	var
	type
	:concept:`TParamVar`
	:enum:`TParamVar`
	:enumerator:`TParamVar`

	class
	struct
	union
	:func:`TParamTemplate`
	member
	var
	type
	:concept:`TParamTemplate`
	:enum:`TParamTemplate`
	:enumerator:`TParamTemplate`

.. function:: void FunctionParams(int FunctionParam)

	:class:`FunctionParam`
	:struct:`FunctionParam`
	:union:`FunctionParam`
	:func:`FunctionParam`
	member
	var
	:type:`FunctionParam`
	:concept:`FunctionParam`
	:enum:`FunctionParam`
	:enumerator:`FunctionParam`
