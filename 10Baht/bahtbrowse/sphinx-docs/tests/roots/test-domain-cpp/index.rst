test-domain-cpp
===============

directives
----------

.. cpp:class:: public Sphinx

   The description of Sphinx class.

.. cpp:function:: int hello(char *name)

   The description of hello function.

.. cpp:member:: float Sphinx::version

   The description of Sphinx::version.

.. cpp:var:: int version

   The description of version.

.. cpp:type:: std::vector<int> List

   The description of List type.

.. cpp:enum:: MyEnum

   An unscoped enum.

   .. cpp:enumerator:: A

.. cpp:enum-class:: MyScopedEnum

   A scoped enum.

   .. cpp:enumerator:: B

.. cpp:enum-struct:: protected MyScopedVisibilityEnum : std::underlying_type<MySpecificEnum>::type

   A scoped enum with non-default visibility, and with a specified underlying type.

   .. cpp:enumerator:: B


.. cpp:function:: void paren_1(int, float)
.. cpp:function:: void paren_2(int, float)
.. cpp:function:: void paren_3(int, float)
.. cpp:function:: void paren_4(int, float)
.. cpp:function:: void paren_5::operator()(int)
.. cpp:function:: void paren_6::operator()(int)
.. cpp:function:: void paren_7::operator()(int)
.. cpp:function:: void paren_8::operator()(int)
