:mod:`utils` --- Fake utilities module for tests
================================================

.. module:: utils
   :synopsis: Utility functions

--------------

The :mod:`utils` module is a pretend python module for changes testing.


Classes
-------

.. class:: Path

   Class for handling paths.

   .. versionadded:: 0.5

      Innovative new way to handle paths.

    .. deprecated:: 0.6

       So, that was a bad idea it turns out.
