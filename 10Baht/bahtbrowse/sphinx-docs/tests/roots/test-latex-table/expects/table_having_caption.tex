\label{\detokenize{tabular:table-having-caption}}

\begin{savenotes}\sphinxattablestart
\sphinxthistablewithglobalstyle
\centering
\sphinxcapstartof{table}
\sphinxthecaptionisattop
\sphinxcaption{caption for table}\label{\detokenize{tabular:id1}}
\sphinxaftertopcaption
\begin{tabulary}{\linewidth}[t]{|T|T|}
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
header1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header2
\\
\sphinxmidrule
\sphinxtableatstartofbodyhook
\sphinxAtStartPar
cell1\sphinxhyphen{}1
&
\sphinxAtStartPar
cell1\sphinxhyphen{}2
\\
\sphinxhline
\sphinxAtStartPar
cell2\sphinxhyphen{}1
&
\sphinxAtStartPar
cell2\sphinxhyphen{}2
\\
\sphinxhline
\sphinxAtStartPar
cell3\sphinxhyphen{}1
&
\sphinxAtStartPar
cell3\sphinxhyphen{}2
\\
\sphinxbottomrule
\end{tabulary}
\sphinxtableafterendhook\par
\sphinxattableend\end{savenotes}
