\label{\detokenize{longtable:longtable-having-both-stub-columns-and-problematic-cell}}

\begin{savenotes}
\sphinxatlongtablestart
\sphinxthistablewithglobalstyle
\makeatletter
  \LTleft \@totalleftmargin plus1fill
  \LTright\dimexpr\columnwidth-\@totalleftmargin-\linewidth\relax plus1fill
\makeatother
\begin{longtable}{|*{3}{\X{1}{3}|}}
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
header1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header2
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header3
\\
\sphinxmidrule
\endfirsthead

\multicolumn{3}{c}{\sphinxnorowcolor
    \makebox[0pt]{\sphinxtablecontinued{\tablename\ \thetable{} \textendash{} continued from previous page}}%
}\\
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
header1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header2
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header3
\\
\sphinxmidrule
\endhead

\sphinxbottomrule
\multicolumn{3}{r}{\sphinxnorowcolor
    \makebox[0pt][r]{\sphinxtablecontinued{continues on next page}}%
}\\
\endfoot

\endlastfoot
\sphinxtableatstartofbodyhook
\sphinxstyletheadfamily \begin{itemize}
\item {} 
\sphinxAtStartPar
instub1\sphinxhyphen{}1a

\item {} 
\sphinxAtStartPar
instub1\sphinxhyphen{}1b

\end{itemize}
&\sphinxstyletheadfamily 
\sphinxAtStartPar
instub1\sphinxhyphen{}2
&
\sphinxAtStartPar
notinstub1\sphinxhyphen{}3
\\
\sphinxhline\sphinxstyletheadfamily 
\sphinxAtStartPar
cell2\sphinxhyphen{}1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
cell2\sphinxhyphen{}2
&
\sphinxAtStartPar
cell2\sphinxhyphen{}3
\\
\sphinxbottomrule
\end{longtable}
\sphinxtableafterendhook
\sphinxatlongtableend
\end{savenotes}
