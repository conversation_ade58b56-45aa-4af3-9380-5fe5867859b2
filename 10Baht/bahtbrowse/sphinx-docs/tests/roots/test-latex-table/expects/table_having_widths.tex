\label{\detokenize{tabular:table-having-widths-option}}

\begin{savenotes}\sphinxattablestart
\sphinxthistablewithglobalstyle
\sphinxthistablewithbooktabsstyle
\sphinxthistablewithcolorrowsstyle
\centering
\phantomsection\label{\detokenize{tabular:namedtabular}}\label{\detokenize{tabular:mytabular}}\nobreak
\begin{tabular}[t]{\X{30}{100}\X{70}{100}}
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
header1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header2
\\
\sphinxmidrule
\sphinxtableatstartofbodyhook
\sphinxAtStartPar
cell1\sphinxhyphen{}1
&
\sphinxAtStartPar
cell1\sphinxhyphen{}2
\\
\sphinxhline
\sphinxAtStartPar
cell2\sphinxhyphen{}1
&
\sphinxAtStartPar
cell2\sphinxhyphen{}2
\\
\sphinxhline
\sphinxAtStartPar
cell3\sphinxhyphen{}1
&
\sphinxAtStartPar
cell3\sphinxhyphen{}2
\\
\sphinxbottomrule
\end{tabular}
\sphinxtableafterendhook\par
\sphinxattableend\end{savenotes}

\sphinxAtStartPar
See {\hyperref[\detokenize{tabular:mytabular}]{\sphinxcrossref{\DUrole{std}{\DUrole{std-ref}{this}}}}}, same as {\hyperref[\detokenize{tabular:namedtabular}]{\sphinxcrossref{namedtabular}}}.
