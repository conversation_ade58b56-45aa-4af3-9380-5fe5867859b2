\label{\detokenize{tabular:table-having-both-stub-columns-and-problematic-cell}}

\begin{savenotes}\sphinxattablestart
\sphinxthistablewithglobalstyle
\centering
\begin{tabular}[t]{|*{3}{\X{1}{3}|}}
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
header1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header2
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header3
\\
\sphinxmidrule
\sphinxtableatstartofbodyhook\sphinxstyletheadfamily \begin{itemize}
\item {} 
\sphinxAtStartPar
instub1\sphinxhyphen{}1a

\item {} 
\sphinxAtStartPar
instub1\sphinxhyphen{}1b

\end{itemize}
&\sphinxstyletheadfamily 
\sphinxAtStartPar
instub1\sphinxhyphen{}2
&
\sphinxAtStartPar
notinstub1\sphinxhyphen{}3
\\
\sphinxhline\sphinxstyletheadfamily 
\sphinxAtStartPar
cell2\sphinxhyphen{}1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
cell2\sphinxhyphen{}2
&
\sphinxAtStartPar
cell2\sphinxhyphen{}3
\\
\sphinxbottomrule
\end{tabular}
\sphinxtableafterendhook\par
\sphinxattableend\end{savenotes}
