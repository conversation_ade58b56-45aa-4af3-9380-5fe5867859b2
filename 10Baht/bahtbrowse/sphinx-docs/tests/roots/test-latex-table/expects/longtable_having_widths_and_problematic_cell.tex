\label{\detokenize{longtable:longtable-having-both-widths-and-problematic-cell}}

\begin{savenotes}
\sphinxatlongtablestart
\sphinxthistablewithglobalstyle
\makeatletter
  \LTleft \@totalleftmargin plus1fill
  \LTright\dimexpr\columnwidth-\@totalleftmargin-\linewidth\relax plus1fill
\makeatother
\begin{longtable}{|\X{30}{100}|\X{70}{100}|}
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
header1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header2
\\
\sphinxmidrule
\endfirsthead

\multicolumn{2}{c}{\sphinxnorowcolor
    \makebox[0pt]{\sphinxtablecontinued{\tablename\ \thetable{} \textendash{} continued from previous page}}%
}\\
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
header1
&\sphinxstyletheadfamily 
\sphinxAtStartPar
header2
\\
\sphinxmidrule
\endhead

\sphinxbottomrule
\multicolumn{2}{r}{\sphinxnorowcolor
    \makebox[0pt][r]{\sphinxtablecontinued{continues on next page}}%
}\\
\endfoot

\endlastfoot
\sphinxtableatstartofbodyhook
\begin{itemize}
\item {} 
\sphinxAtStartPar
item1

\item {} 
\sphinxAtStartPar
item2

\end{itemize}
&
\sphinxAtStartPar
cell1\sphinxhyphen{}2
\\
\sphinxhline
\sphinxAtStartPar
cell2\sphinxhyphen{}1
&
\sphinxAtStartPar
cell2\sphinxhyphen{}2
\\
\sphinxhline
\sphinxAtStartPar
cell3\sphinxhyphen{}1
&
\sphinxAtStartPar
cell3\sphinxhyphen{}2
\\
\sphinxbottomrule
\end{longtable}
\sphinxtableafterendhook
\sphinxatlongtableend
\end{savenotes}
