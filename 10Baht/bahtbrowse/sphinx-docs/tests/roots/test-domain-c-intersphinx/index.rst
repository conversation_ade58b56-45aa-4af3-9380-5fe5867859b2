.. c:member:: void __member = _member

	- :any:`_member`
	- :c:member:`_member`
	- :c:var:`_member`
	- :c:data:`_member`

.. c:member:: void __var = _var

	- :any:`_var`
	- :c:member:`_var`
	- :c:var:`_var`
	- :c:data:`_var`

.. c:member:: void __function = _function

	- :any:`_function`
	- :c:func:`_function`
	- :c:type:`_function`

.. c:member:: void __macro = _macro

	- :any:`_macro`
	- :c:macro:`_macro`

.. c:type:: _struct __struct
            struct _struct __structTagged

	- :any:`_struct`
	- :c:struct:`_struct`
	- :c:type:`_struct`

.. c:type:: _union __union
            union _union __unionTagged

	- :any:`_union`
	- :c:union:`_union`
	- :c:type:`_union`

.. c:type:: _enum __enum
            enum _enum __enumTagged

	- :any:`_enum`
	- :c:enum:`_enum`
	- :c:type:`_enum`

.. c:member:: void __enumerator = _enumerator

	- :any:`_enumerator`
	- :c:enumerator:`_enumerator`

.. c:type:: _type __type

	- :any:`_type`
	- :c:type:`_type`

.. c:member:: void __functionParam = _functionParam.param

	- :any:`_functionParam.param`
	- :c:member:`_functionParam.param`
	- :c:var:`_functionParam.param`
	- :c:data:`_functionParam.param`
