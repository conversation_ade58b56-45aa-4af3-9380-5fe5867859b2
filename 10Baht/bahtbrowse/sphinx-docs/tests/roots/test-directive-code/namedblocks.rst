Named Blocks
============

References to named blocks
--------------------------

See :ref:`the ruby code <some ruby code>` and
also :ref:`the python code <some python code>`.


Named Code block
----------------

.. code-block:: ruby
   :name: some ruby code

   def ruby?
       false
   end


Named Literal Include
---------------------

.. literalinclude:: literal.inc
   :language: python
   :name: some python code

