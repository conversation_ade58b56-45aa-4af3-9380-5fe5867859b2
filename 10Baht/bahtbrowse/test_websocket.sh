#!/bin/bash

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log() {
    local level=$1
    local message=$2
    local color=$NC
    
    case $level in
        "INFO") color=$BLUE ;;
        "SUCCESS") color=$GREEN ;;
        "WARNING") color=$YELLOW ;;
        "ERROR") color=$RED ;;
    esac
    
    echo -e "${color}[$level] $message${NC}"
}

# Function to check if a port is open
check_port() {
    local host=$1
    local port=$2
    local description=$3
    
    log "INFO" "Checking if port $port is open for $description"
    
    if nc -z -w 5 $host $port; then
        log "SUCCESS" "Port $port is open for $description"
        return 0
    else
        log "ERROR" "Port $port is closed for $description"
        return 1
    fi
}

# Function to check HTTP endpoint
check_http_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    log "INFO" "Checking HTTP endpoint: $description ($url)"
    
    # Make HTTP request
    local status=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    # Check if status matches expected status
    if [ "$status" -eq "$expected_status" ]; then
        log "SUCCESS" "HTTP endpoint $description: HTTP $status (Expected: $expected_status)"
        return 0
    else
        log "ERROR" "HTTP endpoint $description: HTTP $status (Expected: $expected_status)"
        return 1
    fi
}

# Function to check WebSocket connection
check_websocket() {
    local url=$1
    local description=$2
    
    log "INFO" "Checking WebSocket connection: $description ($url)"
    
    # Create a temporary WebSocket client
    local temp_file=$(mktemp)
    cat > $temp_file << EOF
#!/usr/bin/env python3
import asyncio
import websockets
import sys

async def test_websocket():
    try:
        async with websockets.connect("$url", ping_interval=None, close_timeout=5) as websocket:
            print("Connected to $url")
            await websocket.send("PING")
            response = await asyncio.wait_for(websocket.recv(), timeout=5)
            print(f"Received: {response}")
            return True
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_websocket())
    sys.exit(0 if result else 1)
EOF
    
    # Make the script executable
    chmod +x $temp_file
    
    # Run the WebSocket client
    if $temp_file; then
        log "SUCCESS" "WebSocket connection $description: Connected successfully"
        rm $temp_file
        return 0
    else
        log "ERROR" "WebSocket connection $description: Failed to connect"
        rm $temp_file
        return 1
    fi
}

# Function to check Docker container status
check_container() {
    local container=$1
    
    log "INFO" "Checking if container $container is running"
    
    if docker ps | grep -q $container; then
        log "SUCCESS" "Container $container is running"
        return 0
    else
        log "ERROR" "Container $container is not running"
        return 1
    fi
}

# Main function
main() {
    log "INFO" "Starting WebSocket connection tests"
    
    # Check if the container is running
    check_container "firefox-ubuntu"
    
    # Check if required ports are open
    check_port "localhost" 8001 "nginx HTTP"
    check_port "localhost" 5901 "VNC server"
    check_port "localhost" 6081 "noVNC WebSocket proxy"
    
    # Check HTTP endpoints
    check_http_endpoint "http://localhost:8001/" 200 "Landing page"
    check_http_endpoint "http://localhost:8001/vnc/vnc.html" 200 "noVNC HTML interface"
    
    # Check WebSocket connections
    check_websocket "ws://localhost:6081/websockify" "Direct WebSocket connection"
    check_websocket "ws://localhost:8001/websockify" "Proxied WebSocket connection"
    
    log "INFO" "WebSocket connection tests completed"
}

# Run the main function
main
