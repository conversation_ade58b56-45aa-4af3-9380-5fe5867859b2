<!DOCTYPE html>
<html>
<head>
    <title>BahtBrowse VNC</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #f0f0f0;
            font-family: Arial, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        #header {
            background-color: #333;
            padding: 10px;
            text-align: center;
            border-bottom: 1px solid #444;
        }
        #content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        iframe {
            flex: 1;
            border: none;
            width: 100%;
            height: 100%;
        }
        h1 {
            margin: 0;
            font-size: 1.5em;
        }
        #status {
            padding: 5px 10px;
            background-color: #444;
            text-align: center;
        }
        .connected {
            color: #4CAF50;
        }
        .disconnected {
            color: #F44336;
        }
    </style>
</head>
<body>
    <div id="header">
        <h1>BahtBrowse VNC</h1>
    </div>
    <div id="status" class="disconnected">Connecting...</div>
    <div id="content">
        <iframe id="vnc-iframe" src="/vnc/vnc.html?host=localhost&port=8001&path=websockify&autoconnect=true&resize=remote"></iframe>
    </div>

    <script>
        // Monitor connection status
        const iframe = document.getElementById('vnc-iframe');
        const statusElement = document.getElementById('status');
        
        // Check connection status periodically
        function checkConnectionStatus() {
            try {
                const iframeWindow = iframe.contentWindow;
                if (iframeWindow && iframeWindow.UI && iframeWindow.UI.rfb) {
                    if (iframeWindow.UI.rfb._connectionState === 'connected') {
                        statusElement.textContent = 'Connected';
                        statusElement.className = 'connected';
                    } else {
                        statusElement.textContent = 'Disconnected';
                        statusElement.className = 'disconnected';
                    }
                }
            } catch (e) {
                console.error('Error checking connection status:', e);
            }
        }
        
        // Check status every 2 seconds
        setInterval(checkConnectionStatus, 2000);
        
        // Handle iframe load event
        iframe.addEventListener('load', function() {
            statusElement.textContent = 'Loading VNC client...';
            // Initial connection attempt
            setTimeout(checkConnectionStatus, 3000);
        });
    </script>
</body>
</html>
