#!/bin/bash

# Comprehensive test script for noVNC connection
# Tests each step in the connection chain:
# Browser Request → Nginx → noVNC HTML/JS → WebSocket Connection → WebSocket Proxy → VNC Server → X11/Firefox

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Container name
CONTAINER="bahtbrowse-browsers-firefox-alpine"

# Log file
LOG_FILE="novnc_test_$(date +%Y%m%d_%H%M%S).log"

# Function to log messages
log() {
    local level=$1
    local message=$2
    local color=$NC
    
    case $level in
        "INFO") color=$BLUE ;;
        "SUCCESS") color=$GREEN ;;
        "WARNING") color=$YELLOW ;;
        "ERROR") color=$RED ;;
    esac
    
    echo -e "${color}[$level] $message${NC}"
    echo "[$level] $message" >> $LOG_FILE
}

# Function to run a test and log the result
run_test() {
    local test_name=$1
    local command=$2
    local expected_status=$3
    
    log "INFO" "Running test: $test_name"
    echo "$ $command" >> $LOG_FILE
    
    # Run the command and capture output and status
    output=$(eval $command 2>&1)
    status=$?
    
    # Log the output
    echo "$output" >> $LOG_FILE
    
    # Check if the status matches the expected status
    if [ $status -eq $expected_status ]; then
        log "SUCCESS" "Test passed: $test_name"
        return 0
    else
        log "ERROR" "Test failed: $test_name (expected status $expected_status, got $status)"
        return 1
    fi
}

# Function to check if a port is open
check_port() {
    local host=$1
    local port=$2
    local description=$3
    
    log "INFO" "Checking if port $port is open for $description"
    
    if nc -z -w 5 $host $port; then
        log "SUCCESS" "Port $port is open for $description"
        return 0
    else
        log "ERROR" "Port $port is closed for $description"
        return 1
    fi
}

# Function to check HTTP endpoint
check_http_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    log "INFO" "Checking HTTP endpoint: $description ($url)"
    
    # Make HTTP request
    local status=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    # Check if status matches expected status
    if [ "$status" -eq "$expected_status" ]; then
        log "SUCCESS" "HTTP endpoint $description: HTTP $status (Expected: $expected_status)"
        return 0
    else
        log "ERROR" "HTTP endpoint $description: HTTP $status (Expected: $expected_status)"
        return 1
    fi
}

# Function to check if a process is running
check_process() {
    local process_name=$1
    local process_check=$2
    
    log "INFO" "Checking if process '$process_name' is running"
    
    output=$(docker exec $CONTAINER bash -c "$process_check" 2>&1)
    status=$?
    
    echo "$output" >> $LOG_FILE
    
    if [ $status -eq 0 ] && [ ! -z "$output" ]; then
        log "SUCCESS" "Process '$process_name' is running"
        return 0
    else
        log "ERROR" "Process '$process_name' is not running"
        return 1
    fi
}

# Function to check if a file exists
check_file() {
    local file_path=$1
    local description=$2
    
    log "INFO" "Checking if file '$file_path' exists for $description"
    
    if docker exec $CONTAINER bash -c "[ -f $file_path ]"; then
        log "SUCCESS" "File '$file_path' exists for $description"
        return 0
    else
        log "ERROR" "File '$file_path' does not exist for $description"
        return 1
    fi
}

# Function to create a WebSocket test file
create_websocket_test() {
    local ws_url=$1
    local test_file=$2
    local description=$3
    
    log "INFO" "Creating WebSocket test file for $description: $ws_url"
    
    cat > $test_file << EOF
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test: $description</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #333;
            border-radius: 5px;
        }
        h1 {
            color: #4CAF50;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #4CAF50;
            color: white;
        }
        .disconnected {
            background-color: #f44336;
            color: white;
        }
        .connecting {
            background-color: #2196F3;
            color: white;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background-color: #222;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Test: $description</h1>
        
        <div id="status" class="connecting">Status: Connecting...</div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        const statusElement = document.getElementById('status');
        const logElement = document.getElementById('log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += \`<div>[\${timestamp}] \${message}</div>\`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function connectWebSocket() {
            log("Attempting to connect to $ws_url");
            
            try {
                const ws = new WebSocket("$ws_url");
                
                ws.onopen = function() {
                    statusElement.className = 'connected';
                    statusElement.textContent = 'Status: Connected';
                    log("WebSocket connection established");
                };
                
                ws.onclose = function(event) {
                    statusElement.className = 'disconnected';
                    statusElement.textContent = 'Status: Disconnected';
                    log(\`WebSocket connection closed (code: \${event.code}, reason: \${event.reason || 'none'})\`);
                };
                
                ws.onerror = function(error) {
                    statusElement.className = 'disconnected';
                    statusElement.textContent = 'Status: Error';
                    log("WebSocket error occurred");
                    console.error("WebSocket error:", error);
                };
                
                ws.onmessage = function(event) {
                    log(\`Received data: \${event.data.length} bytes\`);
                };
            } catch (error) {
                statusElement.className = 'disconnected';
                statusElement.textContent = 'Status: Error';
                log(\`Error: \${error.message}\`);
                console.error('Error creating WebSocket:', error);
            }
        }
        
        // Connect when the page loads
        window.addEventListener('load', connectWebSocket);
    </script>
</body>
</html>
EOF
    
    # Copy the test file to the container
    docker cp $test_file $CONTAINER:/tmp/serve/$test_file
    
    log "SUCCESS" "WebSocket test file created at http://localhost:8001/$test_file"
}

# Function to check logs for errors
check_logs() {
    local log_file=$1
    local description=$2
    local error_pattern=${3:-"error|failed|failure|exception"}
    
    log "INFO" "Checking $description logs for errors"
    
    output=$(docker exec $CONTAINER bash -c "cat $log_file 2>/dev/null | grep -i -E '$error_pattern'")
    
    if [ -n "$output" ]; then
        log "WARNING" "Found potential errors in $description logs:"
        echo "$output" >> $LOG_FILE
        echo "$output" | head -5
        return 1
    else
        log "SUCCESS" "No errors found in $description logs"
        return 0
    fi
}

# Function to create a diagnostic report
create_diagnostic_report() {
    local report_file="novnc_diagnostic_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log "INFO" "Creating diagnostic report in $report_file"
    
    echo "BahtBrowse noVNC Diagnostic Report" > $report_file
    echo "Date: $(date)" >> $report_file
    echo "Container: $CONTAINER" >> $report_file
    echo "" >> $report_file
    
    echo "=== Container Status ===" >> $report_file
    docker inspect $CONTAINER --format='{{.State.Status}}' >> $report_file
    echo "" >> $report_file
    
    echo "=== Process List ===" >> $report_file
    docker exec $CONTAINER ps aux >> $report_file
    echo "" >> $report_file
    
    echo "=== Network Connections ===" >> $report_file
    docker exec $CONTAINER netstat -tuln >> $report_file
    echo "" >> $report_file
    
    echo "=== Supervisord Configuration ===" >> $report_file
    docker exec $CONTAINER cat /etc/supervisord.conf >> $report_file
    echo "" >> $report_file
    
    echo "=== Nginx Configuration ===" >> $report_file
    docker exec $CONTAINER cat /etc/nginx/http.d/default.conf >> $report_file
    echo "" >> $report_file
    
    echo "=== X11VNC Logs ===" >> $report_file
    docker exec $CONTAINER cat /tmp/logs/x11vnc.log >> $report_file
    echo "" >> $report_file
    
    echo "=== X11VNC Error Logs ===" >> $report_file
    docker exec $CONTAINER cat /tmp/logs/x11vnc.err >> $report_file
    echo "" >> $report_file
    
    echo "=== noVNC Logs ===" >> $report_file
    docker exec $CONTAINER cat /tmp/logs/novnc.log >> $report_file
    echo "" >> $report_file
    
    echo "=== noVNC Error Logs ===" >> $report_file
    docker exec $CONTAINER cat /tmp/logs/novnc.err >> $report_file
    echo "" >> $report_file
    
    echo "=== Firefox Logs ===" >> $report_file
    docker exec $CONTAINER cat /tmp/logs/firefox.log >> $report_file
    echo "" >> $report_file
    
    echo "=== Firefox Error Logs ===" >> $report_file
    docker exec $CONTAINER cat /tmp/logs/firefox.err >> $report_file
    echo "" >> $report_file
    
    echo "=== Nginx Logs ===" >> $report_file
    docker exec $CONTAINER cat /tmp/logs/nginx.log >> $report_file
    echo "" >> $report_file
    
    echo "=== Nginx Error Logs ===" >> $report_file
    docker exec $CONTAINER cat /tmp/logs/nginx.err >> $report_file
    echo "" >> $report_file
    
    log "SUCCESS" "Diagnostic report created in $report_file"
}

# Main function
main() {
    log "INFO" "Starting noVNC Connection Test Suite"
    
    # Stage 1: Check if the container is running
    log "INFO" "=== Stage 1: Container Status ==="
    run_test "Container Status" "docker inspect $CONTAINER --format='{{.State.Status}}' | grep -q running" 0
    
    # Stage 2: Check if required processes are running
    log "INFO" "=== Stage 2: Process Status ==="
    check_process "Xvfb" "ps aux | grep Xvfb | grep -v grep"
    check_process "x11vnc" "ps aux | grep x11vnc | grep -v grep"
    check_process "noVNC" "ps aux | grep websockify | grep -v grep"
    check_process "nginx" "ps aux | grep nginx | grep -v grep"
    check_process "Firefox" "ps aux | grep firefox | grep -v grep"
    
    # Stage 3: Check if required ports are open
    log "INFO" "=== Stage 3: Port Status ==="
    check_port "localhost" 8001 "nginx HTTP"
    check_port "localhost" 5901 "VNC server"
    check_port "localhost" 6080 "noVNC WebSocket proxy"
    
    # Stage 4: Check if required files exist
    log "INFO" "=== Stage 4: File Status ==="
    check_file "/tmp/noVNC/vnc.html" "noVNC HTML interface"
    check_file "/etc/nginx/http.d/default.conf" "nginx configuration"
    check_file "/etc/supervisord.conf" "supervisord configuration"
    
    # Stage 5: Check HTTP endpoints
    log "INFO" "=== Stage 5: HTTP Endpoints ==="
    check_http_endpoint "http://localhost:8001/" 200 "Landing page"
    check_http_endpoint "http://localhost:8001/working_connection.html" 200 "Working connection page"
    check_http_endpoint "http://localhost:8001/vnc/vnc.html" 200 "Proxied VNC page"
    check_http_endpoint "http://localhost:6080/vnc.html" 200 "Direct VNC page"
    
    # Stage 6: Test WebSocket connections
    log "INFO" "=== Stage 6: WebSocket Connections ==="
    create_websocket_test "ws://localhost:6080/websockify" "direct_ws_test.html" "Direct WebSocket connection"
    create_websocket_test "ws://localhost:8001/websockify" "proxied_ws_test.html" "Proxied WebSocket connection"
    
    # Stage 7: Check logs for errors
    log "INFO" "=== Stage 7: Log Analysis ==="
    check_logs "/tmp/logs/x11vnc.log" "X11VNC"
    check_logs "/tmp/logs/x11vnc.err" "X11VNC error"
    check_logs "/tmp/logs/novnc.log" "noVNC"
    check_logs "/tmp/logs/novnc.err" "noVNC error"
    check_logs "/tmp/logs/nginx.log" "nginx"
    check_logs "/tmp/logs/nginx.err" "nginx error"
    check_logs "/tmp/logs/firefox.log" "Firefox"
    check_logs "/tmp/logs/firefox.err" "Firefox error"
    
    # Stage 8: Create diagnostic report
    log "INFO" "=== Stage 8: Diagnostic Report ==="
    create_diagnostic_report
    
    log "INFO" "Test suite completed. See $LOG_FILE for details."
    log "INFO" "To test WebSocket connections, access the test pages created in Stage 6."
    log "INFO" "Direct WebSocket test: http://localhost:8001/direct_ws_test.html"
    log "INFO" "Proxied WebSocket test: http://localhost:8001/proxied_ws_test.html"
}

# Run the main function
main
