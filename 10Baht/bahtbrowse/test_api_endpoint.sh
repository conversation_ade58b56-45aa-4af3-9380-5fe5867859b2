#!/bin/bash

# Test script for the API endpoint
# Tests the /api/browse endpoint

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log() {
    local level=$1
    local message=$2
    local color=$NC

    case $level in
        "INFO") color=$BLUE ;;
        "SUCCESS") color=$GREEN ;;
        "WARNING") color=$YELLOW ;;
        "ERROR") color=$RED ;;
    esac

    echo -e "${color}[$level] $message${NC}"
}

# Function to test API endpoint
test_api_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3

    log "INFO" "Testing API endpoint: $description ($url)"

    # Make HTTP request
    local response=$(curl -s -I "$url")
    local status=$(echo "$response" | grep -i "^HTTP" | awk '{print $2}')

    # Check if status matches expected status
    if [ "$status" -eq "$expected_status" ]; then
        log "SUCCESS" "API endpoint $description: HTTP $status (Expected: $expected_status)"

        # If it's a redirect, check the Location header
        if [ "$status" -eq 302 ]; then
            local location=$(echo "$response" | grep -i "^Location" | sed 's/^Location: //i' | tr -d '\r')
            log "INFO" "Redirect location: $location"

            # Check if the location contains 'vnc'
            if [[ "$location" == *"vnc"* ]]; then
                log "SUCCESS" "Redirect location contains 'vnc'"
            else
                log "ERROR" "Redirect location does not contain 'vnc'"
            fi
        fi

        return 0
    else
        log "ERROR" "API endpoint $description: HTTP $status (Expected: $expected_status)"
        return 1
    fi
}

# Function to test API with different URLs
test_api_with_urls() {
    local base_url=$1
    local urls=("https://example.com" "https://google.com" "https://github.com")

    log "INFO" "Testing API with different URLs"

    for url in "${urls[@]}"; do
        test_api_endpoint "${base_url}?url=${url}" 302 "Browse $url"
    done
}

# Function to test API with invalid parameters
test_api_invalid_params() {
    local base_url=$1

    log "INFO" "Testing API with invalid parameters"

    # Test without URL parameter
    test_api_endpoint "$base_url" 400 "No URL parameter"

    # Test with empty URL parameter
    test_api_endpoint "${base_url}?url=" 400 "Empty URL parameter"

    # Test with invalid URL parameter
    test_api_endpoint "${base_url}?url=invalid" 302 "Invalid URL parameter"
}

# Main function
main() {
    log "INFO" "Starting API endpoint tests"

    # Test API endpoint with different URLs
    test_api_with_urls "http://localhost:8001/api/browse"

    # Test API endpoint with invalid parameters
    test_api_invalid_params "http://localhost:8001/api/browse"

    log "INFO" "API endpoint tests completed"
}

# Run the main function
main
