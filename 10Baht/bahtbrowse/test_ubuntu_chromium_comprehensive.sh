#!/bin/bash
# Comprehensive test script for Ubuntu Chromium container
# This script tests all components of the BahtBrowse Ubuntu Chromium container

CONTAINER="bahtbrowse-browser-chromium-ubuntu"
LOG_FILE="ubuntu_chromium_test_$(date +%Y%m%d_%H%M%S).log"

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
section() {
    echo -e "\n${BLUE}========== $1 ==========${NC}\n" | tee -a "$LOG_FILE"
}

# Function to print test results
result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[PASS]${NC} $2" | tee -a "$LOG_FILE"
    else
        echo -e "${RED}[FAIL]${NC} $2" | tee -a "$LOG_FILE"
        FAILED_TESTS=$((FAILED_TESTS+1))
    fi
}

# Function to print info messages
info() {
    echo -e "${YELLOW}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Initialize log file
echo "Ubuntu Chromium Comprehensive Test - $(date)" > "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"

# Initialize failed tests counter
FAILED_TESTS=0

section "CONTAINER STATUS"

# Test 1: Check if container is running
info "Test 1: Checking if container is running..."
docker ps | grep $CONTAINER > /dev/null
result $? "Container $CONTAINER is running"

# Test 2: Get container details
info "Test 2: Getting container details..."
CONTAINER_DETAILS=$(docker inspect --format='{{.State.Status}} - Started: {{.State.StartedAt}}' $CONTAINER)
echo "Container details: $CONTAINER_DETAILS" | tee -a "$LOG_FILE"

section "PROCESS STATUS"

# Test 3: Check running processes
info "Test 3: Checking running processes..."
PROCESSES=$(docker exec $CONTAINER ps aux)
echo "$PROCESSES" >> "$LOG_FILE"
echo "Process count: $(echo "$PROCESSES" | wc -l)" | tee -a "$LOG_FILE"

# Test 4: Check if X server is running
info "Test 4: Checking if X server is running..."
docker exec $CONTAINER ps aux | grep Xvfb | grep -v grep > /dev/null
result $? "X server (Xvfb) is running"

# Test 5: Check if VNC server is running
info "Test 5: Checking if VNC server is running..."
docker exec $CONTAINER ps aux | grep x11vnc | grep -v grep > /dev/null
result $? "VNC server (x11vnc) is running"

# Test 6: Check if WebSockify is running
info "Test 6: Checking if WebSockify is running..."
docker exec $CONTAINER ps aux | grep websockify | grep -v grep > /dev/null
result $? "WebSockify is running"

# Test 7: Check if nginx is running
info "Test 7: Checking if nginx is running..."
docker exec $CONTAINER ps aux | grep nginx | grep -v grep > /dev/null
result $? "Nginx is running"

# Test 8: Check if Chromium is running
info "Test 8: Checking if Chromium is running..."
docker exec $CONTAINER ps aux | grep chromium-browser | grep -v grep > /dev/null
result $? "Chromium is running"

section "NETWORK STATUS"

# Test 9: Check network interfaces
info "Test 9: Checking network interfaces..."
INTERFACES=$(docker exec $CONTAINER ip addr)
echo "$INTERFACES" >> "$LOG_FILE"
echo "Interface count: $(echo "$INTERFACES" | grep -c "^[0-9]")" | tee -a "$LOG_FILE"

# Test 10: Check listening ports
info "Test 10: Checking listening ports..."
PORTS=$(docker exec $CONTAINER netstat -tuln)
echo "$PORTS" >> "$LOG_FILE"
echo "Listening port count: $(echo "$PORTS" | grep -c "LISTEN")" | tee -a "$LOG_FILE"

# Test 11: Check port mappings
info "Test 11: Checking port mappings..."
PORT_MAPPINGS=$(docker port $CONTAINER)
echo "$PORT_MAPPINGS" >> "$LOG_FILE"
echo "Port mapping count: $(echo "$PORT_MAPPINGS" | wc -l)" | tee -a "$LOG_FILE"

section "X SERVER STATUS"

# Test 12: Check X server display
info "Test 12: Checking X server display..."
DISPLAY=$(docker exec $CONTAINER bash -c "echo \$DISPLAY")
echo "X server display: $DISPLAY" | tee -a "$LOG_FILE"
if [[ "$DISPLAY" == ":1" ]]; then
    result 0 "X server display is set correctly"
else
    result 1 "X server display is not set correctly"
fi

section "VNC SERVER STATUS"

# Test 13: Check VNC server configuration
info "Test 13: Checking VNC server configuration..."
VNC_CONFIG=$(docker exec $CONTAINER ps aux | grep x11vnc)
echo "$VNC_CONFIG" >> "$LOG_FILE"
if [[ "$VNC_CONFIG" == *"-display :1"* && "$VNC_CONFIG" == *"-listen localhost"* ]]; then
    result 0 "VNC server is configured correctly"
else
    result 1 "VNC server is not configured correctly"
fi

# Test 14: Check VNC port accessibility
info "Test 14: Checking VNC port accessibility..."
docker exec $CONTAINER bash -c "nc -z 127.0.0.1 5900" > /dev/null 2>&1
result $? "VNC port is accessible"

section "WEBSOCKIFY STATUS"

# Test 15: Check WebSockify configuration
info "Test 15: Checking WebSockify configuration..."
WEBSOCKIFY_CONFIG=$(docker exec $CONTAINER ps aux | grep websockify)
echo "$WEBSOCKIFY_CONFIG" >> "$LOG_FILE"
if [[ "$WEBSOCKIFY_CONFIG" == *"6080 127.0.0.1:5900"* ]]; then
    result 0 "WebSockify is configured correctly"
else
    result 1 "WebSockify is not configured correctly"
fi

# Test 16: Check WebSocket port accessibility
info "Test 16: Checking WebSocket port accessibility..."
docker exec $CONTAINER bash -c "nc -z 127.0.0.1 6080" > /dev/null 2>&1
result $? "WebSocket port is accessible"

# Test 17: Check WebSocket port accessibility from host
info "Test 17: Checking WebSocket port accessibility from host..."
nc -z localhost 6083 > /dev/null 2>&1
result $? "WebSocket port is accessible from host"

section "NGINX STATUS"

# Test 18: Check nginx configuration
info "Test 18: Checking nginx configuration..."
docker exec $CONTAINER nginx -t > /dev/null 2>&1
result $? "Nginx configuration is valid"

# Test 19: Check nginx WebSocket configuration
info "Test 19: Checking nginx WebSocket configuration..."
NGINX_WEBSOCKET=$(docker exec $CONTAINER bash -c "grep -r proxy_http_version /etc/nginx")
echo "$NGINX_WEBSOCKET" >> "$LOG_FILE"
if [[ "$NGINX_WEBSOCKET" == *"proxy_http_version 1.1"* ]]; then
    result 0 "Nginx WebSocket configuration is correct"
else
    result 1 "Nginx WebSocket configuration is incorrect"
fi

section "CHROMIUM STATUS"

# Test 20: Check Chromium version
info "Test 20: Checking Chromium version..."
CHROMIUM_VERSION=$(docker exec $CONTAINER chromium-browser --version)
echo "Chromium version: $CHROMIUM_VERSION" | tee -a "$LOG_FILE"

# Test 21: Check Chromium launch command
info "Test 21: Checking Chromium launch command..."
CHROMIUM_CMD=$(docker exec $CONTAINER ps aux | grep chromium-browser | grep -v grep)
echo "$CHROMIUM_CMD" >> "$LOG_FILE"
if [[ "$CHROMIUM_CMD" == *"--no-sandbox"* ]]; then
    result 0 "Chromium is launched with --no-sandbox flag"
else
    result 1 "Chromium is not launched with --no-sandbox flag"
fi

section "APP.PY STATUS"

# Test 22: Check app.py process
info "Test 22: Checking app.py process..."
docker exec $CONTAINER ps aux | grep "python3 /tmp/app.py" | grep -v grep > /dev/null
result $? "app.py is running"

# Test 23: Check app.py logs
info "Test 23: Checking app.py logs..."
APP_LOGS=$(docker exec $CONTAINER bash -c "cat /tmp/logs/app.log 2>/dev/null | tail -10")
echo "$APP_LOGS" >> "$LOG_FILE"
if [[ "$APP_LOGS" == *"Created new session"* ]]; then
    result 0 "app.py logs contain session creation"
else
    result 1 "app.py logs do not contain session creation"
fi

section "NOVNC CONNECTION TEST"

# Test 24: Check direct access to noVNC page
info "Test 24: Checking direct access to noVNC page..."
NOVNC_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8003/vnc.html)
if [[ "$NOVNC_STATUS" == "200" ]]; then
    result 0 "Direct access to noVNC page is possible (HTTP $NOVNC_STATUS)"
else
    result 1 "Direct access to noVNC page is not possible (HTTP $NOVNC_STATUS)"
fi

# Test 25: Check access to noVNC with WebSocket parameters
info "Test 25: Checking access to noVNC with WebSocket parameters..."
NOVNC_PARAMS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8003/vnc.html?host=localhost&port=5900")
if [[ "$NOVNC_PARAMS_STATUS" == "200" ]]; then
    result 0 "Access to noVNC with WebSocket parameters is possible (HTTP $NOVNC_PARAMS_STATUS)"
else
    result 1 "Access to noVNC with WebSocket parameters is not possible (HTTP $NOVNC_PARAMS_STATUS)"
fi

# Test 26: Check API redirect
info "Test 26: Checking API redirect..."
REDIRECT_URL=$(curl -s -I -L "http://localhost:8086/browse/?url=https://example.com&browser=chromium" | grep -i "^location:" | head -1 | sed 's/^[Ll]ocation: //g' | tr -d '\r')
info "API redirect URL: $REDIRECT_URL"
if [[ "$REDIRECT_URL" == *"vnc"* && "$REDIRECT_URL" == *"host=localhost"* && "$REDIRECT_URL" == *"port=5900"* ]]; then
    result 0 "API redirects to a URL with correct VNC parameters"
else
    result 1 "API does not redirect to a URL with correct VNC parameters"
fi

# Test 27: Check API redirect content
info "Test 27: Checking API redirect content..."
REDIRECT_CONTENT=$(curl -s -L "http://localhost:8086/browse/?url=https://example.com&browser=chromium")
if [[ "$REDIRECT_CONTENT" == *"noVNC"* ]]; then
    result 0 "API redirect content contains 'noVNC'"
else
    result 1 "API redirect content does not contain 'noVNC'"
fi

section "SUMMARY"

# Print summary
if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}All tests passed!${NC}" | tee -a "$LOG_FILE"
else
    echo -e "\n${RED}$FAILED_TESTS tests failed!${NC}" | tee -a "$LOG_FILE"
fi

echo -e "\nTest completed. Check $LOG_FILE for detailed results." | tee -a "$LOG_FILE"
echo -e "If you're still experiencing noVNC connection issues, check the following:" | tee -a "$LOG_FILE"
echo -e "1. X server configuration and logs" | tee -a "$LOG_FILE"
echo -e "2. VNC server configuration and logs" | tee -a "$LOG_FILE"
echo -e "3. Websockify configuration and logs" | tee -a "$LOG_FILE"
echo -e "4. Nginx configuration and logs" | tee -a "$LOG_FILE"
echo -e "5. Browser console errors" | tee -a "$LOG_FILE"
echo -e "6. Network connectivity between components" | tee -a "$LOG_FILE"

exit $FAILED_TESTS
