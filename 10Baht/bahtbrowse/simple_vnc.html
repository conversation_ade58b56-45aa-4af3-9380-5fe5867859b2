<!DOCTYPE html>
<html>
<head>
    <title>Simple noVNC</title>
    <meta charset="utf-8">
    <script src="core/util/events.js"></script>
    <script src="core/util/logging.js"></script>
    <script src="core/util/browser.js"></script>
    <script src="core/util/eventtarget.js"></script>
    <script src="core/util/inflator.js"></script>
    <script src="core/util/strings.js"></script>
    <script src="core/util/element.js"></script>
    <script src="core/display.js"></script>
    <script src="core/input/keysym.js"></script>
    <script src="core/input/keysymdef.js"></script>
    <script src="core/input/keyboard.js"></script>
    <script src="core/input/mouse.js"></script>
    <script src="core/input/gesturehandler.js"></script>
    <script src="core/encodings.js"></script>
    <script src="core/websock.js"></script>
    <script src="core/rfb.js"></script>
    <script>
        window.onload = function() {
            // Create RFB object
            var rfb;
            var host = window.location.hostname;
            var port = window.location.port;
            var protocol = window.location.protocol === "https:" ? "wss" : "ws";
            var url = protocol + "://" + host + ":" + port + "/websockify";
            
            console.log("Connecting to: " + url);
            document.getElementById('status').textContent = "Connecting to: " + url;
            
            // Create RFB object
            rfb = new RFB(document.getElementById('screen'), url);
            
            rfb.addEventListener("connect", function() {
                document.getElementById('status').textContent = "Connected";
            });
            
            rfb.addEventListener("disconnect", function(e) {
                document.getElementById('status').textContent = "Disconnected: " + e.detail.reason;
            });
            
            // Connect
            rfb.connect();
        };
    </script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #333;
            color: white;
            font-family: Arial, sans-serif;
        }
        #container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        #status {
            padding: 10px;
            background-color: #222;
        }
        #screen {
            flex: 1;
            background-color: black;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="status">Initializing...</div>
        <div id="screen"></div>
    </div>
</body>
</html>
