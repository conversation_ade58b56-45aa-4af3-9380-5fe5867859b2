const { test, expect } = require('@playwright/test');
const axios = require('axios');

// Configuration
const config = {
  // Host configuration
  host: 'firefox-alpine', // Use the service name from docker-compose
  nginxPort: 80,
  websocketPort: 6080,
  vncPort: 5900,

  // Test URLs
  landingPageUrl: 'http://firefox-alpine/',
  directVncUrl: 'http://firefox-alpine:6080/vnc.html?autoconnect=true&resize=remote',
  proxiedVncUrl: 'http://firefox-alpine/vnc/vnc.html?host=firefox-alpine&port=6080&path=websockify&autoconnect=true&resize=remote',

  // API URLs
  apiUrl: 'http://firefox-alpine/api/browse?url=https://example.com',

  // Test timeouts
  defaultTimeout: 60000,
  navigationTimeout: 30000,
  connectionTimeout: 10000,
};

// Test suite
test.describe('RBI URL Redirection Tests', () => {
  // Increase timeouts for all tests
  test.setTimeout(config.defaultTimeout);

  // Test 1: Check if API redirects to VNC page
  test('API should redirect to VNC page when given a URL', async () => {
    console.log('Testing API redirect for example.com...');

    try {
      // Make a request to the API
      const response = await axios.get(config.apiUrl, {
        maxRedirects: 0,
        validateStatus: (status) => status >= 200 && status < 400,
      });

      // Check if response is a redirect
      expect(response.status).toBe(302);

      // Check if redirect URL contains 'vnc'
      const redirectUrl = response.headers.location;
      console.log('Redirect URL:', redirectUrl);
      expect(redirectUrl).toContain('vnc');
    } catch (error) {
      if (error.response && error.response.status === 302) {
        // Check if redirect URL contains 'vnc'
        const redirectUrl = error.response.headers.location;
        console.log('Redirect URL:', redirectUrl);
        expect(redirectUrl).toContain('vnc');
      } else {
        console.error('API redirect test failed:', error.message);
        throw error;
      }
    }
  });

  // Test 2: Check if example.com loads in the VNC session
  test('example.com should load in the VNC session', async ({ page }) => {
    console.log('Testing if example.com loads in the VNC session...');

    // First, make a request to the API to navigate to example.com
    console.log('Using API to navigate to example.com...');
    try {
      await axios.get(config.apiUrl, {
        maxRedirects: 0,
        validateStatus: (status) => status >= 200 && status < 400,
      });
    } catch (error) {
      console.log('API request completed with redirect as expected');
    }

    // Now navigate to the VNC page to see the result
    console.log('Navigating to VNC page to see example.com...');
    await page.goto(config.directVncUrl, { timeout: config.navigationTimeout });

    // Wait for the VNC connection to establish
    await page.waitForSelector('#noVNC_canvas', { timeout: config.connectionTimeout });

    // Wait for the VNC session to be fully loaded
    await page.waitForTimeout(5000);

    // Take a screenshot for verification
    await page.screenshot({ path: 'test-results/vnc-session.png' });

    console.log('VNC session loaded. Screenshot saved as test-results/vnc-session.png');

    // Check if the VNC canvas is visible
    const canvasVisible = await page.isVisible('#noVNC_canvas');
    expect(canvasVisible).toBeTruthy();

    // Wait for example.com to load in the VNC session
    console.log('Waiting for example.com to load in the VNC session...');
    await page.waitForTimeout(10000);

    // Take another screenshot after waiting
    await page.screenshot({ path: 'test-results/example-com-in-vnc.png' });

    console.log('Second screenshot saved as test-results/example-com-in-vnc.png');

    // Look for visual indicators that example.com has loaded
    // This is challenging because we can't access the DOM inside the VNC session
    // We're relying on screenshots for verification

    console.log('Test completed. Check the screenshots to verify example.com loaded in the VNC session.');
  });

  // Test 3: Full end-to-end test of browsing example.com in RBI
  test('Full end-to-end test of browsing example.com in RBI', async ({ page }) => {
    console.log('Testing full end-to-end flow for browsing example.com in RBI...');

    // Step 1: Navigate to the landing page
    await page.goto(config.landingPageUrl, { timeout: config.navigationTimeout });
    console.log('Landed on the BahtBrowse landing page');
    await page.screenshot({ path: 'test-results/landing-page.png' });

    // Step 2: Create a URL to browse example.com through the API
    const browseUrl = `${config.landingPageUrl}api/browse?url=https://example.com`;
    console.log(`Navigating to browse URL: ${browseUrl}`);

    // Step 3: Navigate to the browse URL
    await page.goto(browseUrl, { timeout: config.navigationTimeout });
    console.log('Navigation completed');

    // Step 4: We should now be redirected to the VNC page
    // Wait for the VNC connection to establish
    try {
      await page.waitForSelector('#noVNC_canvas', { timeout: config.connectionTimeout });
      console.log('VNC canvas found');
    } catch (error) {
      console.log('Could not find VNC canvas, taking screenshot for debugging');
      await page.screenshot({ path: 'test-results/vnc-connection-failed.png' });
      throw error;
    }

    // Step 5: Wait for the VNC session to be fully loaded
    console.log('Waiting for VNC session to fully load...');
    await page.waitForTimeout(10000);
    await page.screenshot({ path: 'test-results/vnc-session-loaded.png' });

    // Step 6: Check if the VNC canvas is visible
    const canvasVisible = await page.isVisible('#noVNC_canvas');
    expect(canvasVisible).toBeTruthy();
    console.log('VNC canvas is visible');

    // Step 7: Wait for example.com to load in the VNC session
    console.log('Waiting for example.com to load in the VNC session...');
    await page.waitForTimeout(15000);

    // Step 8: Take a final screenshot showing example.com in the VNC session
    await page.screenshot({ path: 'test-results/example-com-in-vnc-e2e.png' });

    console.log('End-to-end test completed. Check the screenshots to verify example.com loaded in the VNC session.');

    // Note: We can't directly verify the content of example.com in the VNC session
    // because we can't access the DOM inside the VNC session through Playwright.
    // We're relying on screenshots for verification.
  });
});
