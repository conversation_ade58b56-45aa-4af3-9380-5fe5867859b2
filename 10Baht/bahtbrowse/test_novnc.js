const { test, expect } = require('@playwright/test');
const axios = require('axios');
const WebSocket = require('ws');
const net = require('net');

// Configuration
const config = {
  // Host configuration
  host: 'host.docker.internal', // Use host.docker.internal to access the host from Docker
  nginxPort: 8001,
  websocketPort: 6080,
  vncPort: 5901,
  
  // Test URLs
  landingPageUrl: 'http://host.docker.internal:8001/',
  directVncUrl: 'http://host.docker.internal:8001/vnc_direct.html',
  proxiedVncUrl: 'http://host.docker.internal:8001/vnc/vnc.html?host=host.docker.internal&port=6080&path=websockify&autoconnect=true',
  externalVncUrl: 'http://host.docker.internal:6080/vnc.html?autoconnect=true',
  
  // API URLs
  apiUrl: 'http://host.docker.internal:8001/api/browse/?url=https://example.com',
  
  // WebSocket URLs
  directWebSocketUrl: 'ws://host.docker.internal:6080/websockify',
  proxiedWebSocketUrl: 'ws://host.docker.internal:8001/websockify',
  
  // Test timeouts
  defaultTimeout: 30000,
  connectionTimeout: 5000,
};

// Helper function to check if a port is open
async function isPortOpen(host, port, timeout = 1000) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    
    socket.setTimeout(timeout);
    socket.on('connect', () => {
      socket.destroy();
      resolve(true);
    });
    
    socket.on('timeout', () => {
      socket.destroy();
      resolve(false);
    });
    
    socket.on('error', () => {
      socket.destroy();
      resolve(false);
    });
    
    socket.connect(port, host);
  });
}

// Helper function to test WebSocket connection
async function testWebSocketConnection(url, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(url);
    let connected = false;
    
    const timer = setTimeout(() => {
      if (!connected) {
        ws.terminate();
        reject(new Error(`WebSocket connection to ${url} timed out after ${timeout}ms`));
      }
    }, timeout);
    
    ws.on('open', () => {
      connected = true;
      clearTimeout(timer);
      ws.close();
      resolve(true);
    });
    
    ws.on('error', (error) => {
      clearTimeout(timer);
      reject(error);
    });
  });
}

// Test suite
test.describe('noVNC Connection Tests', () => {
  // Test 1: Check if required ports are open
  test('Required ports should be open', async () => {
    console.log('Testing if required ports are open...');
    
    // Check Nginx port
    const nginxPortOpen = await isPortOpen(config.host, config.nginxPort);
    console.log(`Nginx port ${config.nginxPort} is ${nginxPortOpen ? 'open' : 'closed'}`);
    expect(nginxPortOpen).toBeTruthy();
    
    // Check WebSocket port
    const websocketPortOpen = await isPortOpen(config.host, config.websocketPort);
    console.log(`WebSocket port ${config.websocketPort} is ${websocketPortOpen ? 'open' : 'closed'}`);
    expect(websocketPortOpen).toBeTruthy();
    
    // Check VNC port
    const vncPortOpen = await isPortOpen(config.host, config.vncPort);
    console.log(`VNC port ${config.vncPort} is ${vncPortOpen ? 'open' : 'closed'}`);
    expect(vncPortOpen).toBeTruthy();
  });
  
  // Test 2: Check if landing page is accessible
  test('Landing page should be accessible', async ({ page }) => {
    console.log('Testing if landing page is accessible...');
    
    // Navigate to landing page
    const response = await page.goto(config.landingPageUrl);
    
    // Check if page loaded successfully
    expect(response.status()).toBe(200);
    
    // Check if page contains expected content
    await expect(page.locator('h1')).toContainText('BahtBrowse');
    await expect(page.locator('.info-box')).toContainText('Welcome to BahtBrowse');
    
    // Check if connection buttons exist
    await expect(page.locator('a.button')).toHaveCount(3);
  });
  
  // Test 3: Check if direct VNC page is accessible
  test('Direct VNC page should be accessible', async ({ page }) => {
    console.log('Testing if direct VNC page is accessible...');
    
    // Navigate to direct VNC page
    const response = await page.goto(config.directVncUrl);
    
    // Check if page loaded successfully
    expect(response.status()).toBe(200);
    
    // Check if page contains expected content
    await expect(page.locator('h1')).toContainText('BahtBrowse Direct VNC Connection');
    
    // Check if iframe exists
    await expect(page.locator('iframe')).toBeVisible();
  });
  
  // Test 4: Check if proxied VNC page is accessible
  test('Proxied VNC page should be accessible', async ({ page }) => {
    console.log('Testing if proxied VNC page is accessible...');
    
    // Navigate to proxied VNC page
    const response = await page.goto(config.proxiedVncUrl);
    
    // Check if page loaded successfully
    expect(response.status()).toBe(200);
    
    // Check if page contains noVNC content
    await expect(page.locator('title')).toContainText('noVNC');
  });
  
  // Test 5: Check if external VNC page is accessible
  test('External VNC page should be accessible', async ({ page }) => {
    console.log('Testing if external VNC page is accessible...');
    
    // Navigate to external VNC page
    const response = await page.goto(config.externalVncUrl);
    
    // Check if page loaded successfully
    expect(response.status()).toBe(200);
    
    // Check if page contains noVNC content
    await expect(page.locator('title')).toContainText('noVNC');
  });
  
  // Test 6: Test direct WebSocket connection
  test('Direct WebSocket connection should work', async () => {
    console.log('Testing direct WebSocket connection...');
    
    try {
      await testWebSocketConnection(config.directWebSocketUrl);
      console.log('Direct WebSocket connection successful');
    } catch (error) {
      console.error('Direct WebSocket connection failed:', error.message);
      throw error;
    }
  });
  
  // Test 7: Test proxied WebSocket connection
  test('Proxied WebSocket connection should work', async () => {
    console.log('Testing proxied WebSocket connection...');
    
    try {
      await testWebSocketConnection(config.proxiedWebSocketUrl);
      console.log('Proxied WebSocket connection successful');
    } catch (error) {
      console.error('Proxied WebSocket connection failed:', error.message);
      throw error;
    }
  });
  
  // Test 8: Test API redirect
  test('API should redirect to VNC page', async () => {
    console.log('Testing API redirect...');
    
    try {
      // Make a request to the API
      const response = await axios.get(config.apiUrl, {
        maxRedirects: 0,
        validateStatus: (status) => status >= 200 && status < 400,
      });
      
      // Check if response is a redirect
      expect(response.status).toBe(302);
      
      // Check if redirect URL contains 'vnc'
      const redirectUrl = response.headers.location;
      console.log('Redirect URL:', redirectUrl);
      expect(redirectUrl).toContain('vnc');
    } catch (error) {
      if (error.response && error.response.status === 302) {
        // Check if redirect URL contains 'vnc'
        const redirectUrl = error.response.headers.location;
        console.log('Redirect URL:', redirectUrl);
        expect(redirectUrl).toContain('vnc');
      } else {
        console.error('API redirect test failed:', error.message);
        throw error;
      }
    }
  });
  
  // Test 9: Test VNC connection in iframe
  test('VNC connection in iframe should work', async ({ page }) => {
    console.log('Testing VNC connection in iframe...');
    
    // Navigate to direct VNC page
    await page.goto(config.directVncUrl);
    
    // Wait for iframe to load
    await page.waitForTimeout(5000);
    
    // Check if iframe loaded successfully
    const frameElement = await page.locator('iframe');
    expect(await frameElement.isVisible()).toBeTruthy();
    
    // Switch to iframe context
    const frame = await frameElement.contentFrame();
    
    // Check if noVNC canvas is visible in the iframe
    if (frame) {
      await frame.waitForSelector('#noVNC_canvas', { timeout: config.defaultTimeout });
      const canvasVisible = await frame.isVisible('#noVNC_canvas');
      expect(canvasVisible).toBeTruthy();
    } else {
      throw new Error('Could not access iframe content');
    }
  });
  
  // Test 10: Test full connection flow
  test('Full connection flow should work', async ({ page }) => {
    console.log('Testing full connection flow...');
    
    // Navigate to landing page
    await page.goto(config.landingPageUrl);
    
    // Click on direct connection button
    await page.click('a.button:has-text("Direct Connection")');
    
    // Wait for navigation to complete
    await page.waitForURL(config.directVncUrl);
    
    // Wait for iframe to load
    await page.waitForTimeout(5000);
    
    // Check if iframe loaded successfully
    const frameElement = await page.locator('iframe');
    expect(await frameElement.isVisible()).toBeTruthy();
    
    // Switch to iframe context
    const frame = await frameElement.contentFrame();
    
    // Check if noVNC canvas is visible in the iframe
    if (frame) {
      await frame.waitForSelector('#noVNC_canvas', { timeout: config.defaultTimeout });
      const canvasVisible = await frame.isVisible('#noVNC_canvas');
      expect(canvasVisible).toBeTruthy();
    } else {
      throw new Error('Could not access iframe content');
    }
  });
});
