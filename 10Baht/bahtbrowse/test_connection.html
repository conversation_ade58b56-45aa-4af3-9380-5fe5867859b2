<!DOCTYPE html>
<html>
<head>
    <title>BahtBrowse Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        input {
            padding: 8px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>BahtBrowse Connection Test</h1>
    
    <div>
        <label for="host">Host:</label>
        <input type="text" id="host" value="localhost">
    </div>
    <div style="margin-top: 10px;">
        <label for="port">Port:</label>
        <input type="text" id="port" value="8082">
    </div>
    
    <div style="margin-top: 20px;">
        <button onclick="testConnection()">Test Connection</button>
    </div>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        function testConnection() {
            const host = document.getElementById('host').value;
            const port = document.getElementById('port').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = `Testing connection to ${host}:${port}...`;
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            
            const testUrl = `http://${host}:${port}/browse/test-connection`;
            
            fetch(testUrl, {
                method: 'GET',
                headers: {
                    'X-Test-Connection': 'true'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error(`Server responded with status: ${response.status}`);
                }
            })
            .then(data => {
                resultDiv.innerHTML = `<strong>Connection successful!</strong><br>
                                      BahtBrowse service is available at ${host}:${port}<br>
                                      Response: <pre>${JSON.stringify(data, null, 2)}</pre>`;
                resultDiv.className = 'result success';
            })
            .catch(error => {
                resultDiv.innerHTML = `<strong>Connection failed!</strong><br>
                                      BahtBrowse service is not available at ${host}:${port}<br>
                                      Error: ${error.message}`;
                resultDiv.className = 'result error';
            });
        }
    </script>
</body>
</html>
