#!/bin/bash

# Script to test HTTP endpoints for noVNC connection

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log() {
    local level=$1
    local message=$2
    local color=$NC
    
    case $level in
        "INFO") color=$BLUE ;;
        "SUCCESS") color=$GREEN ;;
        "WARNING") color=$YELLOW ;;
        "ERROR") color=$RED ;;
    esac
    
    echo -e "${color}[$level] $message${NC}"
}

# Function to test HTTP endpoint
test_http_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    log "INFO" "Testing $description: $url"
    
    # Make HTTP request
    local status=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    # Check if status matches expected status
    if [ "$status" -eq "$expected_status" ]; then
        log "SUCCESS" "$description: HTTP $status (Expected: $expected_status)"
        return 0
    else
        log "ERROR" "$description: HTTP $status (Expected: $expected_status)"
        return 1
    fi
}

# Function to test if a port is open
test_port() {
    local host=$1
    local port=$2
    local description=$3
    
    log "INFO" "Testing if $description port is open: $host:$port"
    
    # Check if port is open
    nc -z -w 5 "$host" "$port"
    
    # Check if port is open
    if [ $? -eq 0 ]; then
        log "SUCCESS" "$description port is open: $host:$port"
        return 0
    else
        log "ERROR" "$description port is closed: $host:$port"
        return 1
    fi
}

# Function to test API redirect
test_api_redirect() {
    local url=$1
    local expected_redirect=$2
    local description=$3
    
    log "INFO" "Testing $description: $url"
    
    # Make HTTP request and get redirect URL
    local redirect_url=$(curl -s -I -L "$url" | grep -i "^location:" | head -1 | sed 's/^[Ll]ocation: //g' | tr -d '\r')
    
    # Check if redirect URL contains expected string
    if [[ "$redirect_url" == *"$expected_redirect"* ]]; then
        log "SUCCESS" "$description: Redirect to $redirect_url (Contains: $expected_redirect)"
        return 0
    else
        log "ERROR" "$description: Redirect to $redirect_url (Expected to contain: $expected_redirect)"
        return 1
    fi
}

# Main function
main() {
    log "INFO" "Starting HTTP endpoint tests for noVNC connection"
    
    # Test 1: Check if required ports are open
    log "INFO" "=== Test 1: Required Ports ==="
    test_port "localhost" 8001 "Nginx HTTP"
    test_port "localhost" 6080 "WebSocket proxy"
    test_port "localhost" 5901 "VNC server"
    
    # Test 2: Check if landing page is accessible
    log "INFO" "=== Test 2: Landing Page ==="
    test_http_endpoint "http://localhost:8001/" 200 "Landing page"
    
    # Test 3: Check if direct VNC page is accessible
    log "INFO" "=== Test 3: Direct VNC Page ==="
    test_http_endpoint "http://localhost:8001/vnc_direct.html" 200 "Direct VNC page"
    
    # Test 4: Check if proxied VNC page is accessible
    log "INFO" "=== Test 4: Proxied VNC Page ==="
    test_http_endpoint "http://localhost:8001/vnc/vnc.html" 200 "Proxied VNC page"
    
    # Test 5: Check if external VNC page is accessible
    log "INFO" "=== Test 5: External VNC Page ==="
    test_http_endpoint "http://localhost:6080/vnc.html" 200 "External VNC page"
    
    # Test 6: Check if API redirect works
    log "INFO" "=== Test 6: API Redirect ==="
    test_api_redirect "http://localhost:8001/api/browse/?url=https://example.com" "vnc" "API redirect"
    
    log "INFO" "HTTP endpoint tests completed"
}

# Run the main function
main
