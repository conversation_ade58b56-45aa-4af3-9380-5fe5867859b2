"""Setup script for the BahtBrowse backend project."""

from setuptools import find_packages, setup

setup(
    name="bahtbrowse",
    version="1.1.0",
    description="Secure containerised browser solution",
    author="10Baht Team",
    author_email="<EMAIL>",
    packages=find_packages(),
    package_data={
        "": ["*.js", "*.html", "*.css"],
    },
    install_requires=[
        "aiohttp>=3.8.0",
        "validators>=0.18.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.18.0",
            "pytest-cov>=3.0.0",
            "flake8>=4.0.0",
            "mypy>=0.900",
            "black>=22.0.0",
        ],
    },
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)
