#!/bin/bash

# BahtBrowse Firefox Plugin Simple Build Script
# This script builds the Firefox plugin without minification

# Ensure the script exits on any error
set -e

echo "======================================================"
echo "BahtBrowse Firefox Extension - Simple Build Script"
echo "======================================================"

# Create the XPI file directly
echo "Creating XPI file..."
zip -r bahtbrowse_bouncer.xpi background.js icons/ manifest.json popup/ options/

echo "======================================================"
echo "Build completed successfully!"
echo "XPI file created at: bahtbrowse_bouncer.xpi"
echo "======================================================"
