#!/bin/bash

# Set variables
CONTAINER_NAME="bahtbrowse-browsers-firefox-alpine"
API_URL="http://localhost:8001/api/browse?url=https://example.com"
VNC_URL="http://localhost:6080/vnc.html?autoconnect=true&resize=remote"

# Check if the container is running
echo "Checking if the Firefox container is running..."
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "✅ Container $CONTAINER_NAME is running."
else
    echo "❌ Container $CONTAINER_NAME is not running."
    echo "Starting the container..."
    docker compose -f docker-compose.playwright.yml up -d firefox-alpine
    sleep 5
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo "✅ Container $CONTAINER_NAME is now running."
    else
        echo "❌ Failed to start container $CONTAINER_NAME."
        exit 1
    fi
fi

# Test the API endpoint
echo "Testing the API endpoint for redirecting to example.com..."
RESPONSE=$(curl -s -I "$API_URL")
if echo "$RESPONSE" | grep -q "302 Found"; then
    echo "✅ API endpoint returned a 302 redirect as expected."
    LOCATION=$(echo "$RESPONSE" | grep -i "Location" | cut -d' ' -f2-)
    echo "   Redirect location: $LOCATION"
else
    echo "❌ API endpoint did not return a 302 redirect."
    echo "   Response:"
    echo "$RESPONSE"
    exit 1
fi

# Open the VNC URL in a browser
echo "Opening the VNC URL in a browser..."
echo "VNC URL: $VNC_URL"
echo "Please check if example.com loads in the VNC session."

# Open the browser
if command -v xdg-open > /dev/null; then
    xdg-open "$VNC_URL"
elif command -v open > /dev/null; then
    open "$VNC_URL"
else
    echo "❌ Could not open browser automatically."
    echo "   Please manually open: $VNC_URL"
fi

# Wait for user confirmation
echo ""
echo "Test completed."
echo "Please verify that example.com loads in the VNC session."
echo "You can also try the API endpoint directly by visiting:"
echo "$API_URL"
