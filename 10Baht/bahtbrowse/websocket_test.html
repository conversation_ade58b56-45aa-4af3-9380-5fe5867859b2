<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <script>
        window.onload = function() {
            var output = document.getElementById('output');
            var wsUri = "ws://" + window.location.hostname + ":" + window.location.port + "/websockify";
            var websocket;
            
            function init() {
                output.innerHTML = "Connecting to " + wsUri;
                testWebSocket();
            }
            
            function testWebSocket() {
                websocket = new WebSocket(wsUri);
                websocket.onopen = function(evt) { onOpen(evt) };
                websocket.onclose = function(evt) { onClose(evt) };
                websocket.onmessage = function(evt) { onMessage(evt) };
                websocket.onerror = function(evt) { onError(evt) };
            }
            
            function onOpen(evt) {
                output.innerHTML += "<p>CONNECTED</p>";
                doSend("WebSocket rocks");
            }
            
            function onClose(evt) {
                output.innerHTML += "<p>DISCONNECTED</p>";
            }
            
            function onMessage(evt) {
                output.innerHTML += "<p>RESPONSE: " + evt.data + "</p>";
                websocket.close();
            }
            
            function onError(evt) {
                output.innerHTML += "<p>ERROR: " + evt.data + "</p>";
            }
            
            function doSend(message) {
                output.innerHTML += "<p>SENT: " + message + "</p>";
                websocket.send(message);
            }
            
            init();
        };
    </script>
</head>
<body>
    <h2>WebSocket Test</h2>
    <div id="output"></div>
</body>
</html>
