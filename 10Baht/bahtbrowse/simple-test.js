const { chromium } = require('playwright');

(async () => {
  console.log('Starting test...');
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  console.log('Navigating to Playwright website...');
  await page.goto('https://playwright.dev/');
  
  const title = await page.title();
  console.log(`Page title: ${title}`);
  
  if (title.includes('Playwright')) {
    console.log('✅ Test passed: Title contains "Playwright"');
  } else {
    console.log('❌ Test failed: Title does not contain "Playwright"');
  }
  
  await browser.close();
  console.log('Test completed.');
})();
