{ pkgs ? import <nixpkgs> {} }:

let
  pythonEnv = pkgs.python3.withPackages (ps: with ps; [
    pytest
    pytest-cov
    flask
    celery
    redis
    docker
    requests
    rich
    typer
    fastapi
    uvicorn
    aiohttp
    psutil
    pydantic
    jinja2
    sphinx
    sphinx-rtd-theme
    sphinx-autobuild
    # Using pip for sphinxcontrib-mermaid as it's not in nixpkgs
  ]);
in
pkgs.mkShell {
  buildInputs = with pkgs; [
    pythonEnv
    nodejs
    docker
    docker-compose
  ];

  shellHook = ''
    echo "Entering bahtBrowse development environment..."
    echo "Python version: $(python --version)"
    export PYTHONPATH=$PWD:$PYTHONPATH

    # Install sphinxcontrib-mermaid using pip
    pip install --user sphinxcontrib-mermaid sphinx-rtd-dark-mode

    echo "Sphinx packages installed."
  '';
}
