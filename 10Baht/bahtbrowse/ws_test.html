<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #333;
            border-radius: 5px;
        }
        h1 {
            color: #4CAF50;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #555;
            cursor: not-allowed;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #4CAF50;
            color: white;
        }
        .disconnected {
            background-color: #f44336;
            color: white;
        }
        .connecting {
            background-color: #2196F3;
            color: white;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background-color: #222;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Connection Test</h1>

        <div id="status" class="disconnected">Status: Disconnected</div>

        <div>
            <button id="connect1">Connect to ws://localhost:6080/websockify</button>
            <button id="connect2">Connect to ws://localhost:8001/websockify</button>
            <button id="disconnect" disabled>Disconnect</button>
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        const statusElement = document.getElementById('status');
        const logElement = document.getElementById('log');
        const connectButton1 = document.getElementById('connect1');
        const connectButton2 = document.getElementById('connect2');
        const disconnectButton = document.getElementById('disconnect');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function connect(url) {
            if (ws) {
                ws.close();
            }

            statusElement.className = 'connecting';
            statusElement.textContent = `Status: Connecting to ${url}...`;
            log(`Attempting to connect to ${url}`);

            try {
                ws = new WebSocket(url);

                ws.onopen = function() {
                    statusElement.className = 'connected';
                    statusElement.textContent = `Status: Connected to ${url}`;
                    connectButton1.disabled = true;
                    connectButton2.disabled = true;
                    disconnectButton.disabled = false;
                    log('Connection established');
                };

                ws.onclose = function(event) {
                    statusElement.className = 'disconnected';
                    statusElement.textContent = 'Status: Disconnected';
                    connectButton1.disabled = false;
                    connectButton2.disabled = false;
                    disconnectButton.disabled = true;
                    log(`Connection closed (code: ${event.code}, reason: ${event.reason || 'none'})`);
                    ws = null;
                };

                ws.onerror = function(error) {
                    log('WebSocket error');
                    console.error('WebSocket error:', error);
                };

                ws.onmessage = function(event) {
                    log(`Received data: ${event.data.length} bytes`);
                };
            } catch (error) {
                statusElement.className = 'disconnected';
                statusElement.textContent = 'Status: Error creating WebSocket';
                log(`Error: ${error.message}`);
                console.error('Error creating WebSocket:', error);
            }
        }

        connectButton1.addEventListener('click', function() {
            connect('ws://localhost:6080/websockify');
        });

        connectButton2.addEventListener('click', function() {
            connect('ws://localhost:8001/websockify');
        });

        disconnectButton.addEventListener('click', function() {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>
