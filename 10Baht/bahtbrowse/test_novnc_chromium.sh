#!/bin/bash
# Test script for noVNC connection with Ubuntu Chromium

CONTAINER="bahtbrowse-browser-chromium-ubuntu"

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
section() {
    echo -e "\n${BLUE}========== $1 ==========${NC}\n"
}

# Function to print test results
result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[PASS]${NC} $2"
    else
        echo -e "${RED}[FAIL]${NC} $2"
    fi
}

# Function to print info messages
info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

section "TESTING NOVNC CONNECTION"

# Test 1: Check if container is running
info "Test 1: Checking if container is running..."
docker ps | grep $CONTAINER > /dev/null
result $? "Container $CONTAINER is running"

# Test 2: Check if X server is running
info "Test 2: Checking if X server is running..."
docker exec $CONTAINER ps aux | grep Xvfb | grep -v grep > /dev/null
result $? "X server (Xvfb) is running"

# Test 3: Check if VNC server is running
info "Test 3: Checking if VNC server is running..."
docker exec $CONTAINER ps aux | grep x11vnc | grep -v grep > /dev/null
result $? "VNC server (x11vnc) is running"

# Test 4: Check if WebSockify is running
info "Test 4: Checking if WebSockify is running..."
docker exec $CONTAINER ps aux | grep websockify | grep -v grep > /dev/null
result $? "WebSockify is running"

# Test 5: Check if nginx is running
info "Test 5: Checking if nginx is running..."
docker exec $CONTAINER ps aux | grep nginx | grep -v grep > /dev/null
result $? "Nginx is running"

# Test 6: Check if VNC port is accessible
info "Test 6: Checking if VNC port is accessible..."
docker exec $CONTAINER bash -c "nc -z 127.0.0.1 5900" > /dev/null 2>&1
result $? "VNC port is accessible"

# Test 7: Check if WebSocket port is accessible
info "Test 7: Checking if WebSocket port is accessible..."
docker exec $CONTAINER bash -c "nc -z 127.0.0.1 6080" > /dev/null 2>&1
result $? "WebSocket port is accessible"

# Test 8: Check if WebSocket port is accessible from host
info "Test 8: Checking if WebSocket port is accessible from host..."
nc -z localhost 6083 > /dev/null 2>&1
result $? "WebSocket port is accessible from host"

# Test 9: Check if noVNC page is accessible
info "Test 9: Checking if noVNC page is accessible..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:8003/vnc.html | grep -q "200"
result $? "noVNC page is accessible"

# Test 10: Check if API redirect works
info "Test 10: Checking if API redirect works..."
REDIRECT_URL=$(curl -s -I -L "http://localhost:8086/browse/?url=https://example.com&browser=chromium" | grep -i "^location:" | head -1 | sed 's/^[Ll]ocation: //g' | tr -d '\r')
echo -e "${YELLOW}[INFO]${NC} Redirect URL: $REDIRECT_URL"
if [[ "$REDIRECT_URL" == *"vnc.html"* ]]; then
    result 0 "API redirect works"
else
    result 1 "API redirect does not work"
fi

section "SUMMARY"

echo -e "\n${YELLOW}If all tests passed, the noVNC connection should now be working.${NC}"
echo -e "${YELLOW}Try accessing the following URL in your browser:${NC}"
echo -e "  http://localhost:8086/browse/?url=https://example.com&browser=chromium"
