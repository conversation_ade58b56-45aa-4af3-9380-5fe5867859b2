# Ubuntu Chromium Container Fixes

This document explains the fixes applied to the BahtBrowse Ubuntu Chromium container to resolve noVNC connection issues.

## Issues Identified

1. **X Server (Xvfb) Not Running**
   - The X server was not running in the container, which is required for Chromium to render to a virtual display.
   - This caused Chromium to fail with "Missing X server or $DISPLAY" errors.

2. **VNC Server (x11vnc) Not Running**
   - The VNC server was not running in the container, which is required for noVNC to connect to the virtual display.
   - This caused the noVNC client to show "Failed to connect to server" errors.

3. **Port Mismatch**
   - The app.py was redirecting to port 5901, but the VNC server was running on port 5900.
   - This caused the noVNC client to try to connect to the wrong port.

4. **Chromium Launch Issues**
   - Chromium was failing to launch due to missing the `--no-sandbox` flag.
   - This caused Chromium to fail with "The platform failed to initialize" errors.

## Fixes Applied

### 1. Enhanced Startup Script

We created an enhanced startup script (`fixed_start.sh`) that:

- Properly starts the X server (Xvfb) and sets the DISPLAY environment variable
- Starts the VNC server (x11vnc) with the correct parameters
- Starts WebSockify with the correct parameters
- Adds comprehensive logging for all components
- Includes error handling and fallback options
- Adds the `--no-sandbox` flag to Chromium

```bash
# Start X server (Xvfb)
Xvfb :1 -screen 0 1920x1080x24 > /tmp/logs/Xvfb.log 2>&1 &
export DISPLAY=:1

# Start VNC server (x11vnc)
x11vnc -display $DISPLAY -nopw -listen localhost -xkb -forever > /tmp/logs/x11vnc.log 2>&1 &

# Start WebSockify
websockify -D --web=/usr/share/novnc 6080 localhost:5900 > /tmp/logs/websockify.log 2>&1
```

### 2. Fixed Port Mismatch in app.py

We fixed the port mismatch in app.py by changing the redirect URL to use port 5900 instead of 5901:

```python
redirect_url = (
    f"http://{host_without_port}:{proxy_port}/vnc1/vnc.html?host={host_without_port}&port=5900&autoconnect=1&resize=remote&session={session_id}"
)
```

### 3. Added --no-sandbox Flag to Chromium

We added the `--no-sandbox` flag to all Chromium launch commands in app.py:

```python
command = (
    f"/usr/bin/chromium-browser --no-sandbox --user-data-dir={profile_dir} "
    f"--no-first-run --no-default-browser-check "
    f"--incognito --kiosk "
    f'"{cache_buster_url}" &'
)
```

### 4. Added Comprehensive Logging

We added comprehensive logging to all components to make debugging easier:

- X server logs: `/tmp/logs/Xvfb.log`
- VNC server logs: `/tmp/logs/x11vnc.log`
- WebSockify logs: `/tmp/logs/websockify.log`
- Chromium logs: `/tmp/logs/chromium.log`
- app.py logs: `/tmp/logs/app.log`
- Startup logs: `/tmp/logs/startup.log`

## Verification

After applying these fixes, we verified that:

1. The X server (Xvfb) is running
2. The VNC server (x11vnc) is running
3. WebSockify is running
4. app.py is running with the correct port configuration
5. Chromium is running with the `--no-sandbox` flag
6. The noVNC connection is working

## How to Apply These Fixes

We've created a comprehensive fix script (`fix_ubuntu_chromium_container.sh`) that applies all these fixes to the container:

```bash
# Run the fix script
./fix_ubuntu_chromium_container.sh
```

This script:

1. Copies the enhanced startup script to the container
2. Fixes the port mismatch in app.py
3. Adds the `--no-sandbox` flag to Chromium
4. Makes the changes permanent by replacing the original startup script
5. Restarts the container to apply all changes
6. Verifies that all components are running correctly

## Testing the Connection

After applying the fixes, you can test the noVNC connection by opening the following URL in your browser:

```
http://localhost:8086/browse/?url=https://example.com&browser=chromium
```

This should redirect you to the noVNC interface, which should connect to the VNC server and display the Chromium browser.

## Troubleshooting

If you encounter any issues, check the logs:

```bash
docker exec bahtbrowse-browser-chromium-ubuntu cat /tmp/logs/startup.log
docker exec bahtbrowse-browser-chromium-ubuntu cat /tmp/logs/app.log
docker exec bahtbrowse-browser-chromium-ubuntu cat /tmp/logs/Xvfb.log
docker exec bahtbrowse-browser-chromium-ubuntu cat /tmp/logs/x11vnc.log
docker exec bahtbrowse-browser-chromium-ubuntu cat /tmp/logs/websockify.log
```

You can also run the comprehensive test script to diagnose any issues:

```bash
./test_chrome_extensive.sh
```
