#!/usr/bin/env python3
"""
Simple FastAPI server to serve Swagger UI for BahtBrowse API.
"""

import logging
import os
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="BahtBrowse API Documentation")

# Create static directory if it doesn't exist
os.makedirs("static", exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
async def root():
    """Redirect to Swagger UI."""
    return RedirectResponse(url="/static/index.html")

@app.get("/docs")
async def docs():
    """Redirect to Swagger UI."""
    return RedirectResponse(url="/static/index.html")

@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "healthy"}

if __name__ == "__main__":
    # Run the server
    port = 8088  # Use a different port to avoid conflicts
    logger.info(f"Starting Swagger UI server on port {port}")
    uvicorn.run("swagger_server:app", host="0.0.0.0", port=port, reload=True)
