#!/bin/bash

# Extensive test script for Chrome/Chromium in BahtBrowse
# This script performs a comprehensive test of the Chrome browser and noVNC connection

# Set container name
CONTAINER="bahtbrowse-browser-chromium-ubuntu"

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Log file
LOG_FILE="chrome_test_$(date +%Y%m%d_%H%M%S).log"

# Function to print section headers
section() {
    echo -e "\n${BLUE}========== $1 ==========${NC}\n" | tee -a $LOG_FILE
}

# Function to print test results
result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[PASS]${NC} $2" | tee -a $LOG_FILE
    else
        echo -e "${RED}[FAIL]${NC} $2" | tee -a $LOG_FILE
    fi
}

# Function to print info messages
info() {
    echo -e "${YELLOW}[INFO]${NC} $1" | tee -a $LOG_FILE
}

# Start logging
echo "Starting Chrome extensive test at $(date)" > $LOG_FILE
echo "Container: $CONTAINER" >> $LOG_FILE
echo "----------------------------------------" >> $LOG_FILE

section "CONTAINER STATUS"

# Check if container is running
info "Checking if container is running..."
docker ps | grep $CONTAINER > /dev/null
result $? "Container $CONTAINER is running"

# Get container details
info "Container details:"
docker inspect --format='{{.State.Status}} - Started: {{.State.StartedAt}}' $CONTAINER | tee -a $LOG_FILE

section "PROCESS STATUS"

# Check running processes
info "Checking running processes..."
docker exec $CONTAINER ps aux | tee -a $LOG_FILE

# Check if X server is running
info "Checking if X server is running..."
docker exec $CONTAINER ps aux | grep Xvfb | grep -v grep > /dev/null
result $? "X server (Xvfb) is running"

# Check if VNC server is running
info "Checking if VNC server is running..."
docker exec $CONTAINER ps aux | grep x11vnc | grep -v grep > /dev/null
result $? "VNC server (x11vnc) is running"

# Check if websockify is running
info "Checking if websockify is running..."
docker exec $CONTAINER ps aux | grep websockify | grep -v grep > /dev/null
result $? "WebSocket proxy (websockify) is running"

# Check if Chromium is running
info "Checking if Chromium is running..."
docker exec $CONTAINER ps aux | grep chromium | grep -v grep > /dev/null
result $? "Chromium is running"

section "NETWORK STATUS"

# Check network interfaces
info "Checking network interfaces..."
docker exec $CONTAINER ip addr | tee -a $LOG_FILE

# Check listening ports
info "Checking listening ports..."
docker exec $CONTAINER netstat -tuln | tee -a $LOG_FILE

# Check port mappings
info "Checking port mappings..."
docker port $CONTAINER | tee -a $LOG_FILE

section "X SERVER STATUS"

# Check X server display
info "Checking X server display..."
docker exec $CONTAINER bash -c 'echo $DISPLAY' | tee -a $LOG_FILE

# Check X server logs
info "Checking X server logs..."
docker exec $CONTAINER bash -c 'cat /tmp/logs/Xvfb.log 2>/dev/null || echo "X server log not found"' | tee -a $LOG_FILE

section "VNC SERVER STATUS"

# Check VNC server configuration
info "Checking VNC server configuration..."
docker exec $CONTAINER bash -c 'ps aux | grep x11vnc | grep -v grep' | tee -a $LOG_FILE

# Check VNC server logs
info "Checking VNC server logs..."
docker exec $CONTAINER bash -c 'cat /tmp/logs/x11vnc.log 2>/dev/null || echo "VNC server log not found"' | tee -a $LOG_FILE

# Test VNC connection
info "Testing VNC connection..."
docker exec $CONTAINER bash -c 'nc -z localhost 5900' > /dev/null 2>&1
result $? "VNC connection test"

section "WEBSOCKIFY STATUS"

# Check websockify configuration
info "Checking websockify configuration..."
docker exec $CONTAINER bash -c 'ps aux | grep websockify | grep -v grep' | tee -a $LOG_FILE

# Check websockify logs
info "Checking websockify logs..."
docker exec $CONTAINER bash -c 'cat /tmp/logs/websockify.log 2>/dev/null || echo "Websockify log not found"' | tee -a $LOG_FILE

# Test websockify connection
info "Testing websockify connection..."
docker exec $CONTAINER bash -c 'nc -z localhost 6080' > /dev/null 2>&1
result $? "Websockify connection test"

section "NGINX STATUS"

# Check nginx configuration
info "Checking nginx configuration..."
docker exec $CONTAINER bash -c 'nginx -t 2>&1' | tee -a $LOG_FILE

# Check nginx configuration files
info "Checking nginx configuration files..."
docker exec $CONTAINER bash -c 'cat /etc/nginx/sites-available/default' | tee -a $LOG_FILE

# Check nginx logs
info "Checking nginx logs..."
docker exec $CONTAINER bash -c 'cat /var/log/nginx/error.log 2>/dev/null || echo "Nginx error log not found"' | tee -a $LOG_FILE

section "CHROMIUM STATUS"

# Check Chromium version
info "Checking Chromium version..."
docker exec $CONTAINER bash -c 'chromium-browser --version' | tee -a $LOG_FILE

# Check Chromium logs
info "Checking Chromium logs..."
docker exec $CONTAINER bash -c 'cat /tmp/logs/chromium.log 2>/dev/null || echo "Chromium log not found"' | tee -a $LOG_FILE

section "APP.PY STATUS"

# Check app.py process
info "Checking app.py process..."
docker exec $CONTAINER bash -c 'ps aux | grep "python3 /tmp/app.py" | grep -v grep' | tee -a $LOG_FILE

# Check app.py logs
info "Checking app.py logs..."
docker exec $CONTAINER bash -c 'cat /tmp/logs/app.log | tail -n 50' | tee -a $LOG_FILE

section "NOVNC CONNECTION TEST"

# Test direct access to noVNC page
info "Testing direct access to noVNC page..."
NOVNC_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8003/vnc.html)
if [ "$NOVNC_STATUS" -eq 200 ]; then
    result 0 "Direct access to noVNC page is possible (HTTP 200)"
else
    result 1 "Direct access to noVNC page is not possible (HTTP $NOVNC_STATUS)"
fi

# Test access to noVNC with WebSocket parameters
info "Testing access to noVNC with WebSocket parameters..."
NOVNC_PARAMS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8003/vnc.html?host=localhost&port=6083&autoconnect=1&resize=remote")
if [ "$NOVNC_PARAMS_STATUS" -eq 200 ]; then
    result 0 "Access to noVNC with WebSocket parameters is possible (HTTP 200)"
else
    result 1 "Access to noVNC with WebSocket parameters is not possible (HTTP $NOVNC_PARAMS_STATUS)"
fi

# Test API redirect
info "Testing API redirect..."
REDIRECT_URL=$(curl -s -I -L "http://localhost:8086/browse/?url=https://example.com&browser=chromium" | grep -i "^location:" | head -1 | sed 's/^[Ll]ocation: //g' | tr -d '\r')
echo -e "${YELLOW}[INFO]${NC} API redirect URL: $REDIRECT_URL" | tee -a $LOG_FILE
if [[ -n "$REDIRECT_URL" ]]; then
    result 0 "API redirects to a URL when using Chromium"
else
    result 1 "API does not redirect when using Chromium"
fi

# Test API redirect content
info "Testing API redirect content..."
REDIRECT_CONTENT=$(curl -s -L "http://localhost:8086/browse/?url=https://example.com&browser=chromium")
if [[ "$REDIRECT_CONTENT" == *"noVNC"* ]]; then
    result 0 "API redirect content contains 'noVNC'"
else
    result 1 "API redirect content does not contain 'noVNC'"
fi

section "BROWSER CONSOLE ERRORS"

# Check for browser console errors
info "Checking for browser console errors..."
docker exec $CONTAINER bash -c 'cat /tmp/logs/browser_console.log 2>/dev/null || echo "Browser console log not found"' | tee -a $LOG_FILE

section "SUMMARY"

echo -e "\n${YELLOW}Test completed. Check $LOG_FILE for detailed results.${NC}"
echo -e "${YELLOW}If you're still experiencing noVNC connection issues, check the following:${NC}"
echo -e "1. X server configuration and logs"
echo -e "2. VNC server configuration and logs"
echo -e "3. Websockify configuration and logs"
echo -e "4. Nginx configuration and logs"
echo -e "5. Browser console errors"
echo -e "6. Network connectivity between components"
