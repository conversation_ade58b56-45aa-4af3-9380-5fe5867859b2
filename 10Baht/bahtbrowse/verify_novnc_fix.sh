#!/bin/bash
# Simple script to verify the noVNC connection fix

echo "Testing noVNC connection..."

# Check if container is running
echo "Checking if container is running..."
docker ps | grep bahtbrowse-browser-chromium-ubuntu
if [ $? -ne 0 ]; then
    echo "Container is not running!"
    exit 1
fi

# Check if WebSockify is running
echo "Checking if WebSockify is running..."
docker exec bahtbrowse-browser-chromium-ubuntu ps aux | grep websockify | grep -v grep
if [ $? -ne 0 ]; then
    echo "WebSockify is not running!"
    exit 1
fi

# Check if VNC server is running
echo "Checking if VNC server is running..."
docker exec bahtbrowse-browser-chromium-ubuntu ps aux | grep x11vnc | grep -v grep
if [ $? -ne 0 ]; then
    echo "VNC server is not running!"
    exit 1
fi

# Check if nginx is running
echo "Checking if nginx is running..."
docker exec bahtbrowse-browser-chromium-ubuntu ps aux | grep nginx | grep -v grep
if [ $? -ne 0 ]; then
    echo "Nginx is not running!"
    exit 1
fi

# Test direct access to noVNC page
echo "Testing direct access to noVNC page..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8003/vnc.html)
echo "HTTP status: $HTTP_STATUS"
if [ "$HTTP_STATUS" != "200" ]; then
    echo "Direct access to noVNC page failed!"
    exit 1
fi

# Test API redirect
echo "Testing API redirect..."
REDIRECT_URL=$(curl -s -I -L "http://localhost:8086/browse/?url=https://example.com&browser=chromium" | grep -i "^location:" | head -1 | sed 's/^[Ll]ocation: //g' | tr -d '\r')
echo "Redirect URL: $REDIRECT_URL"
if [[ "$REDIRECT_URL" != *"vnc"* ]]; then
    echo "API redirect failed!"
    exit 1
fi

echo "All tests passed! The noVNC connection should now be working."
echo "Try accessing the following URL in your browser:"
echo "  http://localhost:8086/browse/?url=https://example.com&browser=chromium"

exit 0
