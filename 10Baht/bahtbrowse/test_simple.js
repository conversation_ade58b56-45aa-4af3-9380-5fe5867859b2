const { test, expect } = require('@playwright/test');

test('Landing page should be accessible', async ({ page }) => {
  console.log('Testing if landing page is accessible...');
  
  // Navigate to landing page
  const response = await page.goto('http://localhost:8001/');
  
  // Check if page loaded successfully
  expect(response.status()).toBe(200);
  
  // Check if page contains expected content
  await expect(page.locator('h1')).toContainText('BahtBrowse');
});

test('Direct VNC page should be accessible', async ({ page }) => {
  console.log('Testing if direct VNC page is accessible...');
  
  // Navigate to direct VNC page
  const response = await page.goto('http://localhost:8001/vnc_direct.html');
  
  // Check if page loaded successfully
  expect(response.status()).toBe(200);
  
  // Check if page contains expected content
  await expect(page.locator('h1')).toContainText('BahtBrowse Direct VNC Connection');
  
  // Check if iframe exists
  await expect(page.locator('iframe')).toBeVisible();
});

test('WebSocket connection should work', async ({ page }) => {
  console.log('Testing WebSocket connection...');
  
  // Navigate to WebSocket test page
  await page.goto('http://localhost:8001/ws_test.html');
  
  // Wait for WebSocket connection to be established
  await page.waitForTimeout(5000);
  
  // Check if WebSocket connection is successful
  const statusElement = await page.locator('#status');
  const statusText = await statusElement.textContent();
  
  console.log('WebSocket status:', statusText);
});
