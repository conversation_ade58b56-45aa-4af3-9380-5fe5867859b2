<!DOCTYPE html>
<html lang="en">
<head>
    <title>BahtBrowse VNC</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="icon" type="image/x-icon" href="app/images/icons/favicon.ico">
    <!-- Use the base.css file that exists in the noVNC installation -->
    <link rel="stylesheet" href="app/styles/base.css">
    <script type="module" crossorigin="anonymous" src="app/ui.js"></script>
    <script>
        // Set default connect parameters
        window.addEventListener('load', function() {
            // Extract the hostname from the URL
            const hostname = window.location.hostname;
            
            // Set default connection parameters
            const urlParams = new URLSearchParams(window.location.search);
            if (!urlParams.has('host')) {
                document.getElementById('noVNC_setting_host').value = hostname;
            }
            if (!urlParams.has('port')) {
                document.getElementById('noVNC_setting_port').value = window.location.port || 80;
            }
            if (!urlParams.has('path')) {
                document.getElementById('noVNC_setting_path').value = 'websockify';
            }
            if (urlParams.has('autoconnect') && urlParams.get('autoconnect') === 'true') {
                // Auto-connect after a short delay
                setTimeout(function() {
                    document.getElementById('noVNC_connect_button').click();
                }, 500);
            }
            
            // Set URL parameter if provided
            if (urlParams.has('url')) {
                const url = urlParams.get('url');
                if (url) {
                    // Find Firefox and navigate to the URL
                    const rfb = UI.rfb;
                    if (rfb) {
                        rfb.sendKey(0xFF08); // Backspace
                        setTimeout(function() {
                            rfb.sendString(url);
                            setTimeout(function() {
                                rfb.sendKey(0xFF0D); // Enter
                            }, 100);
                        }, 100);
                    }
                }
            }
        });
    </script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, Helvetica, sans-serif;
            background-color: #2a2a2a;
            color: #fff;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        html {
            height: 100%;
        }

        #noVNC_status_bar {
            background-color: #333;
            color: #fff;
            padding: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #444;
        }

        #noVNC_status {
            flex: 1;
        }

        #noVNC_status_text {
            font-weight: bold;
        }

        #noVNC_buttons {
            display: flex;
            align-items: center;
        }

        .noVNC_button {
            width: 24px;
            height: 24px;
            margin: 0 5px;
            cursor: pointer;
            filter: invert(1);
        }

        #noVNC_connect_button {
            background-color: #0f0;
            color: #000;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
        }

        #noVNC_connect_button:hover {
            background-color: #0c0;
        }

        input[type="text"] {
            background-color: #444;
            color: #fff;
            border: 1px solid #555;
            padding: 3px;
            border-radius: 3px;
        }

        input[type="checkbox"] {
            margin-right: 5px;
        }

        input[type="range"] {
            width: 100px;
        }
    </style>
</head>
<body>
    <div id="noVNC_container">
        <div id="noVNC_status_bar" class="noVNC_status_bar">
            <div id="noVNC_status">
                <div id="noVNC_status_text">Loading...</div>
            </div>
            <div id="noVNC_buttons">
                <input type="image" alt="Disconnect" src="app/images/disconnect.svg"
                    id="noVNC_disconnect_button" class="noVNC_button"
                    title="Disconnect">
                <input type="image" alt="Fullscreen" src="app/images/fullscreen.svg"
                    id="noVNC_fullscreen_button" class="noVNC_button"
                    title="Fullscreen">
                <input type="image" alt="Clipboard" src="app/images/clipboard.svg"
                    id="noVNC_clipboard_button" class="noVNC_button"
                    title="Clipboard">
                <input type="image" alt="Settings" src="app/images/settings.svg"
                    id="noVNC_settings_button" class="noVNC_button"
                    title="Settings">
            </div>
        </div>
        <div id="noVNC_control_bar_anchor"></div>
        <div id="noVNC_control_bar">
            <div id="noVNC_control_bar_handle" title="Hide/Show the control bar"><div></div></div>
            <div class="noVNC_scroll">
                <h1 class="noVNC_logo" translate="no"><span>no</span><br>VNC</h1>
                <div id="noVNC_control_bar_hint"></div>
                <div id="noVNC_client_settings">
                    <ul>
                        <li class="noVNC_heading">
                            <div>Connection</div>
                            <div><input id="noVNC_connect_button" type="button" value="Connect" class="noVNC_submit"></div>
                        </li>
                        <li>
                            <label for="noVNC_setting_host">Host:</label>
                            <input id="noVNC_setting_host" type="text" value="localhost">
                        </li>
                        <li>
                            <label for="noVNC_setting_port">Port:</label>
                            <input id="noVNC_setting_port" type="text" value="80">
                        </li>
                        <li>
                            <label for="noVNC_setting_path">Path:</label>
                            <input id="noVNC_setting_path" type="text" value="websockify">
                        </li>
                        <li class="noVNC_heading">
                            <div>Settings</div>
                        </li>
                        <li>
                            <label><input id="noVNC_setting_reconnect" type="checkbox"> Automatic Reconnect</label>
                        </li>
                        <li>
                            <label><input id="noVNC_setting_shared" type="checkbox" checked> Shared Mode</label>
                        </li>
                        <li>
                            <label><input id="noVNC_setting_view_only" type="checkbox"> View Only</label>
                        </li>
                        <li>
                            <label for="noVNC_setting_compression">Compression level:</label>
                            <input id="noVNC_setting_compression" type="range" min="0" max="9" value="2">
                        </li>
                        <li>
                            <label for="noVNC_setting_quality">Quality:</label>
                            <input id="noVNC_setting_quality" type="range" min="0" max="9" value="6">
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <canvas id="noVNC_canvas" width="0" height="0">
            Canvas not supported.
        </canvas>
    </div>
</body>
</html>
