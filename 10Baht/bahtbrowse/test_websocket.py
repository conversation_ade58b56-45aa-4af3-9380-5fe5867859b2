#!/usr/bin/env python3
import asyncio
import websockets
import sys

async def test_websocket(uri):
    try:
        async with websockets.connect(uri) as websocket:
            print(f"Connected to {uri}")
            await websocket.send("Hello, WebSocket!")
            response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
            print(f"Received: {response}")
            return True
    except Exception as e:
        print(f"Error connecting to {uri}: {e}")
        return False

async def main():
    uri = "ws://localhost:8003/websockify"
    success = await test_websocket(uri)
    if success:
        print("WebSocket connection successful!")
        sys.exit(0)
    else:
        print("WebSocket connection failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
