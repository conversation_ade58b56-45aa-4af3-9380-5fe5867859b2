#!/usr/bin/env python3
"""
Test script to diagnose redirect issues in BahtBrowse.

This script tests the redirect flow from the app.py server to the VNC interface
by simulating requests and following redirects. It includes full integration
testing with Redis for session management.
"""

import argparse
import json
import logging
import os
import sys
import time
import urllib.parse
import uuid

import pytest
import redis
import requests

# Set up pytest markers
pytestmark = [pytest.mark.redis, pytest.mark.integration]

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger("redirect-test")

# Redis configuration
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = int(os.environ.get("REDIS_PORT", 6379))
REDIS_DB = int(os.environ.get("REDIS_DB", 0))
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD", None)

# Create Redis client
try:
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        db=REDIS_DB,
        password=REDIS_PASSWORD,
        decode_responses=True,
    )
    # Test connection
    redis_client.ping()
    logger.info(f"Connected to Redis at {REDIS_HOST}:{REDIS_PORT}")
except redis.ConnectionError as e:
    logger.error(f"Failed to connect to Redis: {str(e)}")
    redis_client = None


def store_session_in_redis(session_id, url_to_browse):
    """
    Store session information in Redis.

    Args:
        session_id: The session ID to store
        url_to_browse: The URL to browse

    Returns:
        bool: True if successful, False otherwise
    """
    if redis_client is None:
        logger.error("Redis client is not available")
        return False

    try:
        # Create session data
        session_data = {
            "session_id": session_id,
            "url": url_to_browse,
            "created_at": time.time(),
            "status": "active",
            "last_activity": time.time(),
        }

        # Store session data in Redis
        redis_client.hset(f"bahtbrowse:session:{session_id}", mapping=session_data)

        # Set expiration (30 minutes)
        redis_client.expire(f"bahtbrowse:session:{session_id}", 1800)

        # Add to active sessions set
        redis_client.sadd("bahtbrowse:sessions:active", session_id)

        logger.info(f"Stored session {session_id} in Redis")
        return True
    except Exception as e:
        logger.error(f"Error storing session in Redis: {str(e)}")
        return False


def get_session_from_redis(session_id):
    """
    Get session information from Redis.

    Args:
        session_id: The session ID to retrieve

    Returns:
        dict: Session data or None if not found
    """
    if redis_client is None:
        logger.error("Redis client is not available")
        return None

    try:
        # Get session data from Redis
        session_data = redis_client.hgetall(f"bahtbrowse:session:{session_id}")

        if not session_data:
            logger.warning(f"Session {session_id} not found in Redis")
            return None

        logger.info(f"Retrieved session {session_id} from Redis")
        return session_data
    except Exception as e:
        logger.error(f"Error retrieving session from Redis: {str(e)}")
        return None


def update_session_activity(session_id):
    """
    Update session last activity timestamp.

    Args:
        session_id: The session ID to update

    Returns:
        bool: True if successful, False otherwise
    """
    if redis_client is None:
        logger.error("Redis client is not available")
        return False

    try:
        # Update last activity timestamp
        redis_client.hset(
            f"bahtbrowse:session:{session_id}", "last_activity", time.time()
        )

        # Reset expiration (30 minutes)
        redis_client.expire(f"bahtbrowse:session:{session_id}", 1800)

        logger.info(f"Updated session {session_id} activity in Redis")
        return True
    except Exception as e:
        logger.error(f"Error updating session activity in Redis: {str(e)}")
        return False


def test_redirect(host, port, url_to_browse):
    """
    Test the redirect flow from app.py to VNC interface.

    Args:
        host: The hostname to connect to
        port: The port to connect to
        url_to_browse: The URL to browse (will be passed to app.py)
    """
    logger.info(f"Testing redirect flow for URL: {url_to_browse}")

    # Encode the URL for use in query parameters
    encoded_url = urllib.parse.quote(url_to_browse)

    # Construct the initial request URL (to app.py)
    initial_url = f"http://{host}:{port}/browse/?url={encoded_url}"
    logger.info(f"Making initial request to: {initial_url}")

    try:
        # Make the request with allow_redirects=False to see the redirect
        response = requests.get(initial_url, allow_redirects=False)

        if 300 <= response.status_code < 400:
            redirect_url = response.headers.get("Location")
            logger.info(f"Received redirect to: {redirect_url}")

            # Extract the session ID from the redirect URL
            if "session=" in redirect_url:
                session_id = redirect_url.split("session=")[1].split("&")[0]
                logger.info(f"Extracted session ID: {session_id}")

                # Store session in Redis if available
                if redis_client is not None:
                    store_session_in_redis(session_id, url_to_browse)

                    # Verify session was stored
                    session_data = get_session_from_redis(session_id)
                    if session_data:
                        logger.info(f"Session data in Redis: {session_data}")

                logger.info("Redirect test successful - session ID was generated")
            else:
                logger.error("No session ID found in redirect URL")

            logger.info("Not following redirect as it would require port mapping")
            logger.info("Test completed successfully")
            return True

        else:
            logger.error(f"No redirect received. Status code: {response.status_code}")
            logger.error(f"Response content: {response.text[:200]}...")
            return False

    except Exception as e:
        logger.error(f"Error testing redirect: {str(e)}")
        return False


def cleanup_redis_test_data():
    """Clean up test data from Redis."""
    if redis_client is None:
        logger.warning("Redis client is not available, skipping cleanup")
        return

    try:
        # Get all test sessions
        test_sessions = redis_client.smembers("bahtbrowse:sessions:active")

        # Delete each test session
        for session_id in test_sessions:
            redis_client.delete(f"bahtbrowse:session:{session_id}")

        # Delete the active sessions set
        redis_client.delete("bahtbrowse:sessions:active")

        logger.info("Cleaned up test data from Redis")
    except Exception as e:
        logger.error(f"Error cleaning up test data from Redis: {str(e)}")


@pytest.fixture
def redis_connection():
    """Fixture to provide a Redis connection for tests."""
    if redis_client is None:
        pytest.skip("Redis is not available")
    return redis_client


def test_redis_connection(redis_connection):
    """Test that Redis connection is working."""
    assert redis_connection.ping() is True
    logger.info("Redis connection test passed")


def test_session_storage():
    """Test storing and retrieving session data in Redis."""
    if redis_client is None:
        pytest.skip("Redis is not available")

    # Generate a test session ID
    session_id = f"test-{uuid.uuid4()}"
    test_url = "https://example.com"

    # Store session
    result = store_session_in_redis(session_id, test_url)
    assert result is True

    # Retrieve session
    session_data = get_session_from_redis(session_id)
    assert session_data is not None
    assert session_data["session_id"] == session_id
    assert session_data["url"] == test_url
    assert "created_at" in session_data
    assert "last_activity" in session_data

    # Update activity
    time.sleep(1)  # Wait a second to ensure timestamp changes
    result = update_session_activity(session_id)
    assert result is True

    # Verify activity was updated
    updated_session = get_session_from_redis(session_id)
    assert float(updated_session["last_activity"]) > float(session_data["last_activity"])

    logger.info("Session storage test passed")


def test_full_redirect_flow():
    """Test the full redirect flow with Redis integration."""
    host = "127.0.0.1"
    port = 8082  # Default port, can be changed via environment variable
    url_to_browse = "https://example.com"

    # Override from environment variables if available
    if os.environ.get("BAHTBROWSE_HOST"):
        host = os.environ.get("BAHTBROWSE_HOST")
    if os.environ.get("BAHTBROWSE_PORT"):
        port = int(os.environ.get("BAHTBROWSE_PORT"))

    # Skip if server is not running
    try:
        requests.get(f"http://{host}:{port}/", timeout=2)
    except requests.RequestException:
        pytest.skip(f"BahtBrowse server not running at {host}:{port}")

    # Run the redirect test
    result = test_redirect(host, port, url_to_browse)
    assert result is True

    logger.info("Full redirect flow test passed")


def main():
    """Parse arguments and run the test."""
    parser = argparse.ArgumentParser(description="Test BahtBrowse redirect flow")
    parser.add_argument("--host", default="127.0.0.1", help="Host to connect to")
    parser.add_argument("--port", type=int, default=8082, help="Port to connect to")
    parser.add_argument("--url", default="https://example.com", help="URL to browse")
    parser.add_argument("--cleanup", action="store_true", help="Clean up test data from Redis")

    args = parser.parse_args()

    if args.cleanup:
        cleanup_redis_test_data()
    else:
        test_redirect(args.host, args.port, args.url)


if __name__ == "__main__":
    main()
