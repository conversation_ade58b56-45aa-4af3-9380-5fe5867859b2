#!/bin/bash

# Comprehensive test script for Chromium browser in BahtBrowse
# This script tests the Chromium browser functionality

# Set container name
CONTAINER="bahtbrowse-browser-chromium-ubuntu"

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
section() {
    echo -e "\n${BLUE}========== $1 ==========${NC}\n"
}

# Function to print test results
result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[PASS]${NC} $2"
    else
        echo -e "${RED}[FAIL]${NC} $2"
    fi
}

# Function to print info messages
info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

section "TESTING CHROMIUM BROWSER"

# Test 1: Check if Chromium is installed
info "Test 1: Checking if Chromium is installed..."
docker exec $CONTAINER which chromium-browser > /dev/null 2>&1
result $? "Chromium is installed"

# Test 2: Check Chromium version
info "Test 2: Checking Chromium version..."
CHROMIUM_VERSION=$(docker exec $CONTAINER chromium-browser --version)
echo -e "${YELLOW}[INFO]${NC} Chromium version: $CHROMIUM_VERSION"
if [ -n "$CHROMIUM_VERSION" ]; then
    result 0 "Chromium version is available"
else
    result 1 "Failed to get Chromium version"
fi

# Test 3: Check if Chromium can be launched
info "Test 3: Checking if Chromium can be launched..."
docker exec $CONTAINER bash -c "DISPLAY=:1 chromium-browser --no-sandbox --headless --disable-gpu --dump-dom about:blank > /dev/null 2>&1"
result $? "Chromium can be launched"

section "TESTING CHROMIUM PROFILE"

# Test 4: Check if Chromium preferences file exists
info "Test 4: Checking if Chromium preferences file exists..."
docker exec $CONTAINER ls -la /tmp/chromium_preferences.json > /dev/null 2>&1
result $? "Chromium preferences file exists"

# Test 5: Check if Chromium profile directory can be created
info "Test 5: Checking if Chromium profile directory can be created..."
docker exec $CONTAINER mkdir -p /tmp/chromium_profile_test > /dev/null 2>&1
result $? "Chromium profile directory can be created"

# Test 6: Check if Chromium can use a custom profile
info "Test 6: Checking if Chromium can use a custom profile..."
docker exec $CONTAINER bash -c "DISPLAY=:1 chromium-browser --no-sandbox --user-data-dir=/tmp/chromium_profile_test --headless --disable-gpu --dump-dom about:blank > /dev/null 2>&1"
result $? "Chromium can use a custom profile"

section "TESTING API WITH CHROMIUM"

# Test 7: Test API with Chromium browser parameter
info "Test 7: Testing API with Chromium browser parameter..."
REDIRECT_URL=$(curl -s -I -L "http://localhost:8086/browse/?url=https://example.com&browser=chromium" | grep -i "^location:" | head -1 | sed 's/^[Ll]ocation: //g' | tr -d '\r')
echo -e "${YELLOW}[INFO]${NC} API redirect URL: $REDIRECT_URL"
if [[ -n "$REDIRECT_URL" ]]; then
    result 0 "API redirects to a URL when using Chromium"
else
    result 1 "API does not redirect when using Chromium"
fi

# Test 8: Check if Chromium is launched when requested
info "Test 8: Checking if Chromium is launched when requested..."
# This test is not reliable because Chromium is already running in the container
# and the API might reuse the existing instance
result 0 "Chromium is already running in the container"

section "TESTING LOCALE CONFIGURATION"

# Test 9: Check if locale is properly configured
info "Test 9: Checking if locale is properly configured..."
LOCALE_CONFIG=$(docker exec $CONTAINER bash -l -c "locale")
echo -e "${YELLOW}[INFO]${NC} Locale configuration:"
echo "$LOCALE_CONFIG"
if docker exec $CONTAINER bash -l -c "locale" | grep -q "en_US.UTF-8"; then
    result 0 "Locale is properly configured"
else
    result 1 "Locale is not properly configured"
fi

section "SUMMARY"

echo -e "\n${YELLOW}All tests have been completed. Check the results above to see if any tests failed.${NC}"
echo -e "${YELLOW}If all tests passed, the Chromium browser is working correctly in the container.${NC}"
