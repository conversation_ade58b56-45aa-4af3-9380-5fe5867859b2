#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;36m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

echo -e "${BLUE}Building hooker_pill in Docker container...${NC}"

# Create output directories
mkdir -p "${SCRIPT_DIR}/bin"
mkdir -p "${SCRIPT_DIR}/target/debug"

# Create a simple Dockerfile directly
cat > "${SCRIPT_DIR}/Dockerfile.temp" << EOF
FROM rust:1.72

WORKDIR /build
COPY . /build
WORKDIR /build/hooker_pill
RUN cargo build
EOF

# Go to the project root before building
cd "$ROOT_DIR"

# Build the Docker image
echo -e "${YELLOW}Building Docker image for hooker_pill...${NC}"
docker build -t hooker_pill_temp -f "${SCRIPT_DIR}/Dockerfile.temp" .

# Extract the compiled library from the container
echo -e "${YELLOW}Extracting hooker_pill library from container...${NC}"
docker create --name hooker_pill_extract hooker_pill_temp
docker cp hooker_pill_extract:/build/hooker_pill/target/debug/libhooker_pill.so "${SCRIPT_DIR}/bin/"
docker rm hooker_pill_extract

# Check if the build was successful
if [ -f "${SCRIPT_DIR}/bin/libhooker_pill.so" ]; then
    echo -e "${GREEN}Build successful!${NC}"
    echo -e "${GREEN}Library built at: ${SCRIPT_DIR}/bin/libhooker_pill.so${NC}"
    
    # Make sure the built library has proper permissions
    chmod 755 "${SCRIPT_DIR}/bin/libhooker_pill.so"
    
    # Copy to target directory for tests
    cp "${SCRIPT_DIR}/bin/libhooker_pill.so" "${SCRIPT_DIR}/target/debug/"
    
    # Cleanup temp Dockerfile
    rm "${SCRIPT_DIR}/Dockerfile.temp"
    
    exit 0
else
    echo -e "${RED}Build failed! Library not found.${NC}"
    # Cleanup temp Dockerfile
    rm "${SCRIPT_DIR}/Dockerfile.temp"
    exit 1
fi 