{ pkgs ? import <nixpkgs> {} }:

with pkgs;

let
  # Option 2: Modified shell.nix to handle library paths
  
  # Set to true to use host system's VirtualBox and Vagrant
  useHostVirtualBox = true;
  
  # Define packages based on the useHostVirtualBox flag
  virtualBoxPackages = if useHostVirtualBox then [] else [ virtualbox vagrant ];
  
  # Define libraries
  libraries = [
    # Add libraries that hooker_pill needs
    stdenv.cc.cc.lib
    libelf
  ];
    
  # Function to set library paths in environment
  fixLibraryPaths = if useHostVirtualBox then ''
    # If using host VirtualBox, provide a helper function
    function use_host_vbox() {
      # Save original paths
      local ORIGINAL_PATH="$PATH"
      local ORIGINAL_LD_LIBRARY_PATH="$LD_LIBRARY_PATH"
      
      # Modify PATH
      export PATH=$(echo "$PATH" | tr ':' '\n' | grep -v "nix" | tr '\n' ':')
      export PATH="$PATH:/usr/bin:/bin:/usr/local/bin"
      
      # Set host library path
      export LD_LIBRARY_PATH="/usr/lib:/lib:/usr/lib/x86_64-linux-gnu:/lib/x86_64-linux-gnu"
      
      # Unset NIX variables
      local ORIGINAL_NIX_PATH="$NIX_PATH"
      local ORIGINAL_NIX_PROFILES="$NIX_PROFILES"
      local ORIGINAL_NIX_SSL_CERT_FILE="$NIX_SSL_CERT_FILE"
      local ORIGINAL_NIX_STORE="$NIX_STORE"
      
      unset NIX_PATH
      unset NIX_PROFILES
      unset NIX_SSL_CERT_FILE
      unset NIX_STORE
      
      # Run the command
      "$@"
      local RESULT=$?
      
      # Restore original paths
      export PATH="$ORIGINAL_PATH"
      export LD_LIBRARY_PATH="$ORIGINAL_LD_LIBRARY_PATH"
      export NIX_PATH="$ORIGINAL_NIX_PATH"
      export NIX_PROFILES="$ORIGINAL_NIX_PROFILES"
      export NIX_SSL_CERT_FILE="$ORIGINAL_NIX_SSL_CERT_FILE"
      export NIX_STORE="$ORIGINAL_NIX_STORE"
      
      return $RESULT
    }
    
    # Create aliases for vagrant and VBoxManage
    alias vagrant="use_host_vbox vagrant"
    alias VBoxManage="use_host_vbox VBoxManage"
    
    echo "VirtualBox/Vagrant configured to use host system libraries"
    echo "Use 'vagrant' or 'VBoxManage' commands as normal"
  '' else "";
in
pkgs.mkShell {
  buildInputs = with pkgs; [
    # Rust toolchain
    rustc
    cargo
    
    # Build essentials
    gcc
    gcc.cc.lib
    glibc
    glibc.dev
    pkg-config
    gnumake
    cmake
    
    # Linux dependencies
    libbpf
    elfutils
    linuxHeaders
    iproute2
    
    # LLVM tools
    llvm
    clang
    lld
    
    # Development tools
    gdb
    lldb
    
    # Libraries needed for the project
    openssl
    openssl.dev
    zlib
    zlib.dev
    
    # Dependencies for libsecret
    glib
    libsecret
  ] 
  # Add VirtualBox packages only if not using host system's VirtualBox
  ++ virtualBoxPackages
  # Add libraries
  ++ libraries;

  # Environment variables
  shellHook = ''
    # Set up library paths
    export LD_LIBRARY_PATH=${pkgs.lib.makeLibraryPath libraries}:$LD_LIBRARY_PATH
    export LIBRARY_PATH=${pkgs.lib.makeLibraryPath libraries}:$LIBRARY_PATH
    export C_INCLUDE_PATH=${pkgs.glibc.dev}/include:${pkgs.zlib.dev}/include:${pkgs.openssl.dev}/include:${pkgs.linuxHeaders}/include:$C_INCLUDE_PATH
    export CPLUS_INCLUDE_PATH=${pkgs.glibc.dev}/include:${pkgs.zlib.dev}/include:${pkgs.openssl.dev}/include:${pkgs.linuxHeaders}/include:$CPLUS_INCLUDE_PATH
    export PKG_CONFIG_PATH=${pkgs.openssl.dev}/lib/pkgconfig:${pkgs.zlib.dev}/lib/pkgconfig:$PKG_CONFIG_PATH
    export RUST_BACKTRACE=1
    export RUST_LOG=info
    
    # Setup VirtualBox environment variables
    export VBOX_USER_HOME="$PWD/.vbox"
    mkdir -p "$VBOX_USER_HOME"
    
    # Configure Vagrant to use the right VirtualBox
    export VAGRANT_DEFAULT_PROVIDER="virtualbox"
    
    # Create a .cargo/config.toml file if it doesn't exist
    mkdir -p .cargo
    cat > .cargo/config.toml << EOF
[build]
rustflags = ["-C", "link-arg=-fuse-ld=lld"]

[target.x86_64-unknown-linux-gnu]
linker = "gcc"
EOF

    echo "=========================================================="
    echo "Hooker Pill Development Environment"
    echo "=========================================================="
    echo "Rust: $(rustc --version | head -1)"
    echo "GCC: $(gcc --version | head -1)"
    
    # Set up VirtualBox/Vagrant to use host system libraries if needed
    ${fixLibraryPaths}
    
    echo "=========================================================="
  '';
} 