[package]
name = "hooker_pill"
version = "0.1.0"
edition = "2021"
authors = ["Inspector Gadget Team"]
description = "System call interception library for Inspector Gadget"
license = "MIT"

[lib]
name = "hooker_pill"
crate-type = ["cdylib"]

[[bin]]
name = "percpumap_test"
path = "src/percpumap_test.rs"

[[bin]]
name = "elasticsearch_logger_pill"
path = "src/elasticsearch_logger_pill.rs"

[[bin]]
name = "binary_evaluation_test"
path = "src/binary_evaluation_test.rs"

[dependencies]
libc = "0.2"
log = "0.4"
env_logger = "0.10"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = "0.4"
inspector_gadget = { path = ".." }
bincode = "1.3"
num_cpus = "1.15"
rand = "0.8"
tokio = { version = "1.28", features = ["full"] } 