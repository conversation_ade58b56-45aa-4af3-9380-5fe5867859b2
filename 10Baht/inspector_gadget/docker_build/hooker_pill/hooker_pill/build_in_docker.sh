#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;36m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

echo -e "${BLUE}Building hooker_pill in Docker container...${NC}"

# Create target directory if it doesn't exist
mkdir -p "${SCRIPT_DIR}/target/debug"

# Build the Docker image
echo -e "${YELLOW}Building Docker image for hooker_pill build...${NC}"
docker build -t hooker_pill_builder -f Dockerfile.build .

# Run the build in the container
echo -e "${YELLOW}Running build in container...${NC}"
docker run --rm \
    -v "${SCRIPT_DIR}/target:/hooker_pill/target" \
    hooker_pill_builder

# Check if the build was successful
if [ -f "${SCRIPT_DIR}/target/debug/libhooker_pill.so" ]; then
    echo -e "${GREEN}Build successful!${NC}"
    echo -e "${GREEN}Library built at: ${SCRIPT_DIR}/target/debug/libhooker_pill.so${NC}"
    exit 0
else
    echo -e "${RED}Build failed! Library not found.${NC}"
    exit 1
fi 