# Inspector Gadget Hooker Pills

This directory contains various "hooker pills" - small, focused applications that demonstrate specific features of the Inspector Gadget framework.

## Elasticsearch Logger Pill

The Elasticsearch Logger Pill demonstrates how to use the central logging capabilities of Inspector Gadget with Elasticsearch integration. It generates synthetic data for all hooker types and exports it to Elasticsearch for visualization.

### Prerequisites

- Elasticsearch 7.10+ running and accessible
- Kibana 7.10+ (for visualization)
- Rust 1.60+ with Cargo

### Running the Elasticsearch Logger Pill

```bash
# Start Elasticsearch (if not already running)
docker run -d --name elasticsearch -p 9200:9200 -p 9300:9300 -e "discovery.type=single-node" elasticsearch:7.10.0

# Start Kibana (if not already running)
docker run -d --name kibana -p 5601:5601 --link elasticsearch:elasticsearch kibana:7.10.0

# Run the Elasticsearch Logger Pill
cargo run --bin elasticsearch_logger_pill -- http://localhost:9200 1000
```

Parameters:
- First parameter: Elasticsearch URL (default: http://localhost:9200)
- Second parameter: Number of events to generate per hooker (default: 1000)

### Importing Kibana Dashboard

1. Open Kibana in your browser: http://localhost:5601
2. Navigate to Management > Stack Management > Saved Objects
3. Click "Import" and select the `kibana_dashboard.json` file from this directory
4. Click "Import" to load the dashboard

### Viewing the Dashboard

1. Navigate to Dashboard
2. Open the "Inspector Gadget Overview" dashboard
3. You should see visualizations for:
   - Security Events by Type
   - Network Connections
   - Syscall Statistics
   - Function Call Statistics
   - Policy Violations

## Per-CPU Map Test

The Per-CPU Map Test demonstrates the performance benefits of using Per-CPU Maps for concurrent data access.

### Running the Per-CPU Map Test

```bash
cargo run --bin percpumap_test
```

This will run a series of benchmarks comparing Per-CPU Maps with traditional maps, showing the performance benefits in multi-core environments.

## How Per-CPU Maps Benefit Binary Evaluation

### For Malicious Software Detection

1. **High-throughput Monitoring**: Per-CPU Maps allow Inspector Gadget to monitor system calls, network connections, and security events at high throughput without becoming a bottleneck, which is crucial for detecting fast-moving malware.

2. **Reduced Blind Spots**: Traditional monitoring tools often miss events during high-load periods due to contention. Per-CPU Maps minimize these blind spots by distributing the monitoring load across CPUs.

3. **Pattern Recognition**: Malware often exhibits distinctive patterns of system calls or network connections. Per-CPU Maps enable efficient collection and aggregation of this data for analysis.

4. **Real-time Detection**: The low-latency characteristics of Per-CPU Maps allow for near real-time detection of suspicious activities, reducing the window of opportunity for attackers.

5. **Scalable Monitoring**: As the number of cores increases, the monitoring capability scales linearly, allowing for comprehensive coverage even on high-core-count systems.

### For Benevolent Software Analysis

1. **Performance Profiling**: Per-CPU Maps enable detailed profiling of application behavior with minimal overhead, helping developers identify bottlenecks and optimize performance.

2. **Resource Usage Analysis**: By efficiently tracking system calls and resource usage, Per-CPU Maps provide insights into how applications utilize system resources.

3. **Dependency Mapping**: Function call tracing with Per-CPU Maps helps map application dependencies and understand execution flows, aiding in documentation and maintenance.

4. **Regression Testing**: Comparing system call patterns before and after code changes can help identify regressions or unexpected behavior changes.

5. **Security Validation**: Even for benevolent software, security validation is important. Per-CPU Maps help verify that applications only access resources they're supposed to.

## Implementation Details

The Per-CPU Maps implementation in Inspector Gadget provides several key benefits:

1. **Lock-Free Operations**: Each CPU has its own data area, eliminating contention.
2. **Reduced Cache Coherency Traffic**: Minimizes cross-CPU cache invalidation.
3. **Efficient Aggregation**: Combine data from all CPUs when needed.
4. **Scalable with CPU Count**: Performance scales linearly with the number of CPU cores. 