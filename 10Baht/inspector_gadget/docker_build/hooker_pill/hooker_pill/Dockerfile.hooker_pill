FROM rust:1.72-slim

# Install dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libssl-dev \
    libbpf-dev \
    clang \
    llvm \
    libelf-dev \
    linux-headers-amd64 \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set working directory to /workspace
WORKDIR /workspace

# The build context will be the entire inspector_gadget project
# This will be handled by running the build from the parent directory

# Build the hooker_pill
CMD ["bash", "-c", "cd hooker_pill && cargo build && cp target/debug/libhooker_pill.so /output/"] 