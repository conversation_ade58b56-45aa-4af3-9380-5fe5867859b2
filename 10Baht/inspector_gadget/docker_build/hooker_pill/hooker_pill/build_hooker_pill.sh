#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;36m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

echo -e "${BLUE}Building hooker_pill in Docker container...${NC}"

# Create output directories
OUTPUT_DIR="${SCRIPT_DIR}/bin"
mkdir -p "${OUTPUT_DIR}"

# Move to the root directory
cd "$ROOT_DIR"

# Build the Docker image with entire project as context
echo -e "${YELLOW}Building Docker image for hooker_pill...${NC}"
docker build -t hooker_pill_builder -f "${SCRIPT_DIR}/Dockerfile.hooker_pill" .

# Run the build in the container with mounted output directory
echo -e "${YELLOW}Running build in container...${NC}"
docker run --rm \
    -v "${OUTPUT_DIR}:/output" \
    hooker_pill_builder

# Check if the build was successful
if [ -f "${OUTPUT_DIR}/libhooker_pill.so" ]; then
    echo -e "${GREEN}Build successful!${NC}"
    echo -e "${GREEN}Library built at: ${OUTPUT_DIR}/libhooker_pill.so${NC}"
    
    # Make sure the built library has proper permissions
    chmod 755 "${OUTPUT_DIR}/libhooker_pill.so"
    
    # Also create a copy in the target directory for tests to find
    mkdir -p "${SCRIPT_DIR}/target/debug"
    cp "${OUTPUT_DIR}/libhooker_pill.so" "${SCRIPT_DIR}/target/debug/"
    
    exit 0
else
    echo -e "${RED}Build failed! Library not found.${NC}"
    exit 1
fi 