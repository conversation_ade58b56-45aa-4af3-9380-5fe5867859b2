#!/bin/bash

# Run Binary Evaluation Test with Inspector <PERSON><PERSON><PERSON>
# This script runs the binary evaluation test program with all hookers attached

set -e

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "This script must be run as root to attach eBPF programs"
  echo "Please run with sudo"
  exit 1
fi

# Build the binary evaluation test program
echo "Building binary evaluation test program..."
cargo build --bin binary_evaluation_test

# Start Elasticsearch if not already running
if ! curl -s http://localhost:9200 > /dev/null; then
  echo "Starting Elasticsearch container..."
  docker run -d --name elasticsearch -p 9200:9200 -p 9300:9300 -e "discovery.type=single-node" elasticsearch:7.10.0
  
  # Wait for Elasticsearch to start
  echo "Waiting for Elasticsearch to start..."
  until curl -s http://localhost:9200 > /dev/null; do
    sleep 2
  done
  echo "Elasticsearch is running"
else
  echo "Elasticsearch is already running"
fi

# Start Kibana if not already running
if ! curl -s http://localhost:5601 > /dev/null; then
  echo "Starting Kibana container..."
  docker run -d --name kibana -p 5601:5601 --link elasticsearch:elasticsearch kibana:7.10.0
  echo "Kibana is starting (this may take a minute)..."
else
  echo "Kibana is already running"
fi

# Load the hooker configuration
echo "Loading hooker configuration..."
CONFIG_PATH="./config/binary_evaluation_config.json"

# Start the Inspector Gadget monitoring service
echo "Starting Inspector Gadget monitoring service..."
cargo run --bin inspector_gadget_service -- --config $CONFIG_PATH &
INSPECTOR_PID=$!

# Give the service time to initialize
echo "Waiting for Inspector Gadget service to initialize..."
sleep 5

# Run the binary evaluation test in normal mode
echo "Running binary evaluation test in normal mode..."
./target/debug/binary_evaluation_test normal 50

# Run the binary evaluation test in suspicious mode
echo "Running binary evaluation test in suspicious mode..."
./target/debug/binary_evaluation_test suspicious 50

# Stop the Inspector Gadget monitoring service
echo "Stopping Inspector Gadget monitoring service..."
kill $INSPECTOR_PID

echo "Test completed. You can view the results in Kibana at http://localhost:5601"
echo "Navigate to Dashboard > Inspector Gadget Overview" 