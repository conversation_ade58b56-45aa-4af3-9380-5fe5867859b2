.PHONY: start stop import clean check test-export

# Start Elasticsearch and Kibana
start:
	@echo "Starting Elasticsearch and Kibana..."
	docker-compose up -d
	@echo "Waiting for services to become available..."
	@echo "This may take a minute or two..."
	@for i in $$(seq 1 12); do \
		echo "Checking Elasticsearch... (attempt $$i/12)"; \
		if curl -s http://localhost:9200 > /dev/null; then \
			echo "Elasticsearch is up!"; \
			break; \
		fi; \
		sleep 10; \
	done
	@for i in $$(seq 1 12); do \
		echo "Checking Kibana... (attempt $$i/12)"; \
		if curl -s http://localhost:5601/api/status > /dev/null; then \
			echo "Kibana is up!"; \
			break; \
		fi; \
		sleep 10; \
	done
	@echo "Services are ready!"
	@echo "Kibana URL: http://localhost:5601"

# Import dashboards
import:
	@echo "Importing dashboards..."
	./import_dashboards.sh
	@echo "Dashboards have been imported!"
	@echo "Visit http://localhost:5601/app/kibana#/dashboards to view them"

# Start, wait, and import
all: start import
	@echo "All done! Services are running and dashboards are imported."
	@echo "Visit http://localhost:5601/app/kibana#/dashboards to view them"

# Stop services
stop:
	@echo "Stopping Elasticsearch and Kibana..."
	docker-compose down
	@echo "Services stopped"

# Clean data volumes
clean:
	@echo "Stopping services and removing volumes..."
	docker-compose down -v
	@echo "Cleanup complete"

# Check service status
check:
	@echo "Checking Elasticsearch status..."
	@if curl -s http://localhost:9200 > /dev/null; then \
		echo "Elasticsearch is running"; \
	else \
		echo "Elasticsearch is not running"; \
	fi
	@echo "Checking Kibana status..."
	@if curl -s http://localhost:5601/api/status > /dev/null; then \
		echo "Kibana is running"; \
	else \
		echo "Kibana is not running"; \
	fi

# Test export with example data
test-export:
	@echo "Running Inspector Gadget example to export test data..."
	@cd .. && cargo run --example elasticsearch_example
	@echo "Done! Data has been exported to Elasticsearch." 