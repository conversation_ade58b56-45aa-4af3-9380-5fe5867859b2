#!/bin/bash

# Script to import Inspector Gadget dashboards into Kibana
# Usage: ./import_dashboards.sh [kibana_url]

# Default Kibana URL
KIBANA_URL=${1:-"http://localhost:5601"}

echo "Importing Inspector Gadget dashboards to Kibana at $KIBANA_URL"

# Check if Kibana is available
echo "Checking if Kibana is available..."
if ! curl -s -o /dev/null -w "%{http_code}" "$KIBANA_URL/api/status" | grep -q "200\|201"; then
    echo "Error: Cannot connect to Kibana at $KIBANA_URL"
    echo "Please make sure Kibana is running and accessible."
    exit 1
fi

echo "Kibana is available. Importing dashboards..."

# Import each dashboard
for dashboard_file in $(find ./dashboards -name "*.json"); do
    dashboard_name=$(basename "$dashboard_file" .json)
    echo "Importing $dashboard_name from $dashboard_file..."
    
    # Import the dashboard using Kibana API
    response=$(curl -s -X POST \
        "$KIBANA_URL/api/kibana/dashboards/import" \
        -H 'kbn-xsrf: true' \
        -H 'Content-Type: application/json' \
        --data-binary @"$dashboard_file")
    
    # Check if import was successful
    if echo "$response" | grep -q "success"; then
        echo "Successfully imported $dashboard_name"
    else
        echo "Failed to import $dashboard_name. Response:"
        echo "$response"
    fi
done

echo "Dashboard import complete."
echo "You can now access the dashboards in Kibana via:"
echo "$KIBANA_URL/app/kibana#/dashboards" 