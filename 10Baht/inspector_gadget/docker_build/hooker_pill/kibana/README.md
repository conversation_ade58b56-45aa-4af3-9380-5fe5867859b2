# Inspector Gadget - Kibana Dashboards

This directory contains Kibana dashboard configurations for visualizing data collected by <PERSON>.

## Dashboards Overview

The following dashboards are provided:

1. **System Statistics Dashboard** - Visualizes system metrics like CPU usage, memory usage, network I/O, and disk I/O.
2. **Syscall Monitoring Dashboard** - Provides insights into system call patterns, frequencies, and distributions.
3. **Anomaly Detection Dashboard** - Displays detected anomalies, their severity, and distribution.

## Prerequisites

- Elasticsearch 7.10.0+
- Kibana 7.10.0+

## Setup

### Using Docker Compose

1. Start Elasticsearch and Kibana using the provided docker-compose.yml:

```bash
docker-compose -f docker-compose.yml up -d
```

2. Wait for both services to be fully available (may take a couple of minutes).

3. Import the dashboards:

```bash
./import_dashboards.sh
```

### With Existing Elasticsearch/Kibana

If you already have Elasticsearch and Kibana running:

```bash
./import_dashboards.sh http://your-kibana-url:5601
```

## Dashboard Details

### System Statistics Dashboard

This dashboard provides real-time monitoring of system resources including:

- CPU usage over time
- Memory usage over time
- Network I/O (bytes sent/received)
- Disk I/O (bytes read/written)

### Syscall Monitoring Dashboard

Monitor system call activity with:

- Distribution of system calls by type
- System call frequency over time
- Table of recent system calls with details
- Heatmap showing system call patterns by process

### Anomaly Detection Dashboard

Track security anomalies with:

- Severity distribution of detected anomalies
- Source distribution of anomalies
- Detailed anomaly information
- Timeline of anomaly occurrences

## Customization

The dashboards can be customized directly in Kibana:

1. Navigate to Dashboard section in Kibana
2. Select the dashboard you want to modify
3. Click "Edit" in the top-right corner
4. Modify, add, or remove panels as needed
5. Save your changes

## Data Requirements

For the dashboards to display properly, your data should have the following fields:

### System Statistics
- `@timestamp` - The timestamp of the metrics
- `cpu_usage` - CPU usage percentage
- `memory_usage` - Memory usage percentage
- `network.bytes_sent` - Network bytes sent
- `network.bytes_received` - Network bytes received
- `disk.bytes_read` - Disk bytes read
- `disk.bytes_written` - Disk bytes written

### Syscall Data
- `@timestamp` - When the syscall occurred
- `syscall_name` - Name of the system call
- `process_id` - ID of the process making the call
- `return_value` - Return value of the syscall

### Anomaly Data
- `@timestamp` - When the anomaly was detected
- `event_type` - Should be "anomaly" for anomaly events
- `severity` - Severity level of the anomaly
- `source` - Source of the anomaly
- `data.description` - Description of the anomaly 