#!/bin/bash

# <PERSON><PERSON>t to fix missing .await calls on futures

echo "Starting async/await fixes..."

# Set the working directory to the project root
cd "$(dirname "$0")/../.." || exit 1

# Fix missing .await on log_event calls
find src -name "*.rs" -exec grep -l "log_event.*)" {} \; | while read -r file; do
    echo "Processing $file for log_event..."
    
    # Add .await to log_event calls that don't already have it
    # This is a simplistic approach - might not catch all cases or could over-match
    sed -i 's/\(log_event([^)]*)\)\.unwrap/\1.await.unwrap/g' "$file"
    sed -i 's/\(log_event([^)]*)\);/\1.await;/g' "$file"
    sed -i 's/\(log_event([^)]*)\)}/\1.await}/g' "$file"
    
    # Handle if let Err pattern
    sed -i 's/if let Err(\([a-zA-Z0-9_]*\)) = \(.*log_event([^)]*)\)[ ]*{/if let Err(\1) = \2.await {/g' "$file"
done

# Fix missing .await on add_security_event calls
find src -name "*.rs" -exec grep -l "add_security_event" {} \; | while read -r file; do
    echo "Processing $file for add_security_event..."
    
    # Add .await to calls that use the ? operator
    sed -i 's/\(add_security_event([^)]*)\)?;/\1.await?;/g' "$file"
    
    # Add .await to regular calls
    sed -i 's/\(add_security_event([^)]*)\);/\1.await;/g' "$file"
done

# Fix missing .await on record_syscall_exit
find src -name "*.rs" -exec grep -l "record_syscall_exit" {} \; | while read -r file; do
    echo "Processing $file for record_syscall_exit..."
    
    # Add .await before .unwrap()
    sed -i 's/\(record_syscall_exit([^)]*)\)\.unwrap/\1.await.unwrap/g' "$file"
    
    # Add .await to calls with ? operator
    sed -i 's/\(record_syscall_exit([^)]*)\)?;/\1.await?;/g' "$file"
    
    # Add .await to regular calls
    sed -i 's/\(record_syscall_exit([^)]*)\);/\1.await;/g' "$file"
done

# Handle any return statements with futures
find src -name "*.rs" -exec grep -l "return " {} \; | while read -r file; do
    if grep -q "async fn" "$file"; then
        echo "Processing $file for async returns..."
        
        # This is a complex pattern that might need manual review after applying
        sed -i 's/return self\.\([a-zA-Z0-9_]*\)(\([^)]*\));/return self.\1(\2).await;/g' "$file"
    fi
done

echo "Async/await fixes completed!" 