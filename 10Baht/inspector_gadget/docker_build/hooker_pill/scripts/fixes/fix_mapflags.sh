#!/bin/bash

# <PERSON>ript to fix MapFlags type mismatches in the codebase
# This script will replace raw integers used in map.update() calls with MapFlags types

echo "Starting MapFlags fixes..."

# Set the working directory to the project root
cd "$(dirname "$0")/../.." || exit 1

# Find files with .update(..., 0) pattern and replace with .update(..., MapFlags(0))
find src -name "*.rs" -exec grep -l "\.update.*,[ ]*0" {} \; | while read -r file; do
    echo "Processing $file..."
    # Replace patterns like map.update(..., 0) with map.update(..., MapFlags(0))
    sed -i 's/\(\.update(.*\),[ ]*0\([^0-9]\)/\1, crate::ebpf::maps::libbpf_stubs::MapFlags(0)\2/g' "$file"
    
    # Also catch where the pattern is at the end of a line
    sed -i 's/\(\.update(.*\),[ ]*0$/\1, crate::ebpf::maps::libbpf_stubs::MapFlags(0)/g' "$file"
done

echo "MapFlags fixes completed!" 