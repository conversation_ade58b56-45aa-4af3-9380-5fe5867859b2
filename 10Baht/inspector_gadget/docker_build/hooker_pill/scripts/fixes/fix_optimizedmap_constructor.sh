#!/bin/bash

# Script to fix TypedOptimizedPerCpuMap constructor calls in the codebase
# The constructor should take a single usize parameter, but some code passes two parameters

echo "Starting TypedOptimizedPerCpuMap constructor fixes..."

# Set the working directory to the project root
cd "$(dirname "$0")/../.." || exit 1

# Find all TypedOptimizedPerCpuMap::new calls with incorrect args pattern
find src -name "*.rs" -exec grep -l "TypedOptimizedPerCpuMap::.*new(" {} \; | while read -r file; do
    echo "Processing $file..."
    
    # Replace pattern where string literal is first arg and config field is second
    # This converts: TypedOptimizedPerCpuMap::new("name", config.field) to TypedOptimizedPerCpuMap::new(config.field)
    sed -i 's/TypedOptimizedPerCpuMap::<[^>]*>::new(\s*"[^"]*",\s*\([^)]*\))/TypedOptimizedPerCpuMap::<[^>]*>::new(\1)/g' "$file"
    
    # If using a MapConfig struct as the argument, we need to modify it to use a numeric value
    # This is more complex and might need manual intervention, but we'll flag these cases
    grep -n "TypedOptimizedPerCpuMap.*::new(config" "$file" > /dev/null
    if [ $? -eq 0 ]; then
        echo "  WARNING: Found TypedOptimizedPerCpuMap constructor with config argument in $file"
        echo "  This may need manual fixing to replace 'config' with a numeric max_entries value"
    fi
done

echo "TypedOptimizedPerCpuMap constructor fixes completed!" 