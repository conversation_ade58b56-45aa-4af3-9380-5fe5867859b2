#!/bin/bash

# Main script to run all the fixes

echo "Running all automatic code fixes..."

# Set the working directory to the project root
cd "$(dirname "$0")/../.." || exit 1

# Make all scripts executable
chmod +x scripts/fixes/*.sh

# First, add Default implementations for config structs
echo -e "\n=== Adding Default trait implementations ==="
./scripts/fixes/create_default_configs.sh

# Fix MapFlags type mismatches
echo -e "\n=== Fixing MapFlags type mismatches ==="
./scripts/fixes/fix_mapflags.sh

# Fix TypedOptimizedPerCpuMap constructor calls
echo -e "\n=== Fixing TypedOptimizedPerCpuMap constructor calls ==="
./scripts/fixes/fix_optimizedmap_constructor.sh

# Fix Hooker constructor calls
echo -e "\n=== Fixing Hooker constructor calls ==="
./scripts/fixes/fix_hooker_constructors.sh

# Fix async/await issues
echo -e "\n=== Fixing async/await issues ==="
./scripts/fixes/fix_async_await.sh

echo -e "\n=== All automated fixes completed! ==="
echo "You may still need to make manual adjustments for some errors."
echo "Run 'cargo check' to see if there are remaining errors to fix." 