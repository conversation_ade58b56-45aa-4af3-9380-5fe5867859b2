#!/bin/bash

# <PERSON><PERSON>t to add Default implementations for config structs that need them

echo "Starting Default trait implementations..."

# Set the working directory to the project root
cd "$(dirname "$0")/../.." || exit 1

# Add Default implementation for XdpHookerConfig
echo "Adding Default implementation for XdpHookerConfig..."
XDP_HOOKER_CONFIG_FILE=$(find src -name "*.rs" -exec grep -l "struct XdpHookerConfig" {} \; | head -1)
if [ -n "$XDP_HOOKER_CONFIG_FILE" ]; then
    echo "Found XdpHookerConfig in $XDP_HOOKER_CONFIG_FILE"
    
    # Check if it already has a Default implementation
    if ! grep -q "impl Default for XdpHookerConfig" "$XDP_HOOKER_CONFIG_FILE"; then
        # Find the end of the struct definition
        LINE_NUM=$(grep -n "struct XdpHookerConfig" "$XDP_HOOKER_CONFIG_FILE" | cut -d: -f1)
        LAST_LINE=$(grep -n "}" "$XDP_HOOKER_CONFIG_FILE" | awk -v line="$LINE_NUM" '$1 > line' | head -1 | cut -d: -f1)
        
        # Append Default implementation after the struct
        cat >> "$XDP_HOOKER_CONFIG_FILE.tmp" << EOF
impl Default for XdpHookerConfig {
    fn default() -> Self {
        Self {
            name: "default_xdp_hooker".to_string(),
            buffer_size: 1024,
            max_events: 1000,
        }
    }
}

EOF
        
        # Insert the implementation at the right location
        sed -i "${LAST_LINE}r ${XDP_HOOKER_CONFIG_FILE}.tmp" "$XDP_HOOKER_CONFIG_FILE"
        rm "${XDP_HOOKER_CONFIG_FILE}.tmp"
    else
        echo "XdpHookerConfig already has a Default implementation"
    fi
else
    echo "Could not find XdpHookerConfig struct definition"
fi

# Add Default implementation for UprobeHookerConfig
echo "Adding Default implementation for UprobeHookerConfig..."
UPROBE_HOOKER_CONFIG_FILE=$(find src -name "*.rs" -exec grep -l "struct UprobeHookerConfig" {} \; | head -1)
if [ -n "$UPROBE_HOOKER_CONFIG_FILE" ]; then
    echo "Found UprobeHookerConfig in $UPROBE_HOOKER_CONFIG_FILE"
    
    # Check if it already has a Default implementation
    if ! grep -q "impl Default for UprobeHookerConfig" "$UPROBE_HOOKER_CONFIG_FILE"; then
        # Find the end of the struct definition
        LINE_NUM=$(grep -n "struct UprobeHookerConfig" "$UPROBE_HOOKER_CONFIG_FILE" | cut -d: -f1)
        LAST_LINE=$(grep -n "}" "$UPROBE_HOOKER_CONFIG_FILE" | awk -v line="$LINE_NUM" '$1 > line' | head -1 | cut -d: -f1)
        
        # Append Default implementation after the struct
        cat >> "$UPROBE_HOOKER_CONFIG_FILE.tmp" << EOF
impl Default for UprobeHookerConfig {
    fn default() -> Self {
        Self {
            name: "default_uprobe_hooker".to_string(),
            buffer_size: 1024,
            max_events: 1000,
        }
    }
}

EOF
        
        # Insert the implementation at the right location
        sed -i "${LAST_LINE}r ${UPROBE_HOOKER_CONFIG_FILE}.tmp" "$UPROBE_HOOKER_CONFIG_FILE"
        rm "${UPROBE_HOOKER_CONFIG_FILE}.tmp"
    else
        echo "UprobeHookerConfig already has a Default implementation"
    fi
else
    echo "Could not find UprobeHookerConfig struct definition"
fi

echo "Default trait implementations completed!" 