#!/bin/bash

# Simple script to manually fix a few MapFlags issues
# Instead of using complex regex, we'll manually fix specific files with known issues

echo "Starting simple MapFlags fixes..."

# Set the working directory to the project root
cd "$(dirname "$0")/../.." || exit 1

# Fix in src/ebpf/maps/hashmap.rs
echo "Fixing MapFlags in src/ebpf/maps/hashmap.rs"
if [ -f src/ebpf/maps/hashmap.rs ]; then
  sed -i 's/map\.update(&key, &value, 0)/map.update(&key, &value, crate::ebpf::maps::libbpf_stubs::MapFlags(0))/g' src/ebpf/maps/hashmap.rs
  sed -i 's/map\.update(&"test_key"\.to_string(), &42, 0)/map.update(&"test_key".to_string(), &42, crate::ebpf::maps::libbpf_stubs::MapFlags(0))/g' src/ebpf/maps/hashmap.rs
fi

# Fix in src/ebpf/maps/lrumap.rs
echo "Fixing MapFlags in src/ebpf/maps/lrumap.rs"
if [ -f src/ebpf/maps/lrumap.rs ]; then
  sed -i 's/map\.update(&key, &value, 0)/map.update(&key, &value, crate::ebpf::maps::libbpf_stubs::MapFlags(0))/g' src/ebpf/maps/lrumap.rs
fi

echo "Simple MapFlags fixes completed!" 