#!/bin/bash

# <PERSON><PERSON>t to fix Hooker constructor calls in the codebase
# These constructors expect two parameters but are often called with one

echo "Starting Hooker constructor fixes..."

# Set the working directory to the project root
cd "$(dirname "$0")/../.." || exit 1

# Fix XdpHooker::new calls
find src -name "*.rs" -exec grep -l "XdpHooker::new" {} \; | while read -r file; do
    echo "Processing $file..."
    
    # Replace XdpHooker::new(single_arg) with XdpHooker::new(&XdpHookerConfig::default(), single_arg)
    sed -i 's/XdpHooker::new(\([^)]*\))/XdpHooker::new(\&XdpHookerConfig::default(), \1)/g' "$file"
done

# Fix UprobeHooker::new calls
find src -name "*.rs" -exec grep -l "UprobeHooker::new" {} \; | while read -r file; do
    echo "Processing $file..."
    
    # Replace UprobeHooker::new(single_arg) with <PERSON><PERSON>Hooker::new(&UprobeHookerConfig::default(), single_arg)
    sed -i 's/UprobeHooker::new(\([^)]*\))/UprobeHooker::new(\&UprobeHookerConfig::default(), \1)/g' "$file"
done

echo "Hooker constructor fixes completed!" 