#!/bin/bash

# Script to test Per-CPU Maps implementation and report to Elasticsearch
# This script compiles and runs the Per-CPU Maps test with Elasticsearch reporting

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}  Testing Per-CPU Maps with Elasticsearch  ${NC}"
echo -e "${BLUE}=======================================${NC}"

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Create a simple test program in /tmp to avoid workspace issues
TEST_DIR="/tmp/percpumap_test_es"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR/src"

echo -e "${YELLOW}Creating test program with Elasticsearch reporting...${NC}"

# Create Cargo.toml with empty workspace table and compatible dependency versions
cat > "$TEST_DIR/Cargo.toml" << EOF
[package]
name = "percpumap_test_es"
version = "0.1.0"
edition = "2021"

[workspace]

[dependencies]
num_cpus = "1.15"
reqwest = { version = "0.11.18", features = ["json", "blocking"], default-features = false }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = "0.4.26"
EOF

# Create src/main.rs
cat > "$TEST_DIR/src/main.rs" << 'EOF'
use std::sync::Arc;
use std::thread;
use std::collections::HashMap;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use serde::{Serialize, Deserialize};
use reqwest::blocking::Client;
use chrono::{Utc, DateTime};

// Mock implementation for testing without actual eBPF maps
// This allows us to test the API design and functionality

#[derive(Debug, Default, Clone, PartialEq, Serialize, Deserialize)]
struct TestKey {
    id: u32,
}

#[derive(Debug, Default, Clone, Copy, PartialEq, Serialize, Deserialize)]
struct TestValue {
    count: u64,
    timestamp: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct PerCpuMapStats {
    lookups: u64,
    hits: u64,
    misses: u64,
    updates: u64,
    deletes: u64,
    errors: u64,
}

impl PerCpuMapStats {
    fn new() -> Self {
        Self {
            lookups: 0,
            hits: 0,
            misses: 0,
            updates: 0,
            deletes: 0,
            errors: 0,
        }
    }
}

struct OptimizedPerCpuMap {
    data: Vec<Vec<(TestKey, TestValue)>>,
    stats: PerCpuMapStats,
    num_cpus: usize,
}

impl OptimizedPerCpuMap {
    fn create(max_entries: usize) -> Self {
        let num_cpus = num_cpus::get();
        let data = vec![Vec::with_capacity(max_entries); num_cpus];
        
        Self {
            data,
            stats: PerCpuMapStats::new(),
            num_cpus,
        }
    }
    
    fn num_cpus(&self) -> usize {
        self.num_cpus
    }
    
    fn stats(&self) -> &PerCpuMapStats {
        &self.stats
    }
    
    fn lookup_cpu(&mut self, key: &TestKey, cpu: usize) -> Option<TestValue> {
        self.stats.lookups += 1;
        
        if cpu >= self.num_cpus {
            self.stats.errors += 1;
            return None;
        }
        
        for (k, v) in &self.data[cpu] {
            if k == key {
                self.stats.hits += 1;
                return Some(*v);
            }
        }
        
        self.stats.misses += 1;
        None
    }
    
    fn update_cpu(&mut self, key: &TestKey, value: &TestValue, cpu: usize) -> bool {
        self.stats.updates += 1;
        
        if cpu >= self.num_cpus {
            self.stats.errors += 1;
            return false;
        }
        
        // Check if key exists
        for (i, (k, _)) in self.data[cpu].iter().enumerate() {
            if k == key {
                // Update existing entry
                self.data[cpu][i] = (key.clone(), *value);
                return true;
            }
        }
        
        // Add new entry
        self.data[cpu].push((key.clone(), *value));
        true
    }
    
    fn delete(&mut self, key: &TestKey) -> bool {
        self.stats.deletes += 1;
        
        let mut deleted = false;
        
        for cpu in 0..self.num_cpus {
            let cpu_data = &mut self.data[cpu];
            let initial_len = cpu_data.len();
            
            cpu_data.retain(|(k, _)| k != key);
            
            if cpu_data.len() < initial_len {
                deleted = true;
            }
        }
        
        deleted
    }
    
    fn lookup_all_cpus(&mut self, key: &TestKey) -> Vec<Option<TestValue>> {
        self.stats.lookups += 1;
        
        let mut result = Vec::with_capacity(self.num_cpus);
        
        for cpu in 0..self.num_cpus {
            result.push(self.lookup_cpu(key, cpu));
        }
        
        result
    }
    
    fn sum(&mut self, key: &TestKey) -> Option<u64> {
        let values = self.lookup_all_cpus(key);
        
        let sum = values.iter()
            .filter_map(|v| v.map(|val| val.count))
            .sum();
        
        if values.iter().any(|v| v.is_some()) {
            Some(sum)
        } else {
            None
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
struct TestResult {
    test_name: String,
    success: bool,
    timestamp: DateTime<Utc>,
    duration_ms: u64,
    details: Option<String>,
    stats: Option<PerCpuMapStats>,
}

struct ElasticsearchReporter {
    client: Client,
    base_url: String,
    index: String,
}

impl ElasticsearchReporter {
    fn new(host: &str, port: u16, index: &str) -> Self {
        let base_url = format!("http://{}:{}", host, port);
        let client = Client::new();
        
        Self {
            client,
            base_url,
            index: index.to_string(),
        }
    }
    
    fn report_test_result(&self, result: TestResult) -> Result<(), Box<dyn std::error::Error>> {
        let url = format!("{}/{}/_doc", self.base_url, self.index);
        
        let response = self.client.post(&url)
            .json(&result)
            .send()?;
            
        if !response.status().is_success() {
            println!("Failed to report to Elasticsearch: {}", response.status());
            println!("Response: {}", response.text()?);
            return Err("Failed to report to Elasticsearch".into());
        }
        
        Ok(())
    }
    
    fn ensure_index_exists(&self) -> Result<(), Box<dyn std::error::Error>> {
        let url = format!("{}/{}", self.base_url, self.index);
        
        let response = self.client.head(&url).send()?;
        
        if response.status().is_success() {
            return Ok(());
        }
        
        // Create index if it doesn't exist
        let create_url = format!("{}/{}", self.base_url, self.index);
        let mapping = r#"{
            "mappings": {
                "properties": {
                    "test_name": { "type": "keyword" },
                    "success": { "type": "boolean" },
                    "timestamp": { "type": "date" },
                    "duration_ms": { "type": "long" },
                    "details": { "type": "text" },
                    "stats": {
                        "properties": {
                            "lookups": { "type": "long" },
                            "hits": { "type": "long" },
                            "misses": { "type": "long" },
                            "updates": { "type": "long" },
                            "deletes": { "type": "long" },
                            "errors": { "type": "long" }
                        }
                    }
                }
            }
        }"#;
        
        let response = self.client.put(&create_url)
            .header("Content-Type", "application/json")
            .body(mapping)
            .send()?;
            
        if !response.status().is_success() {
            println!("Failed to create Elasticsearch index: {}", response.status());
            println!("Response: {}", response.text()?);
            return Err("Failed to create Elasticsearch index".into());
        }
        
        Ok(())
    }
}

// Test basic operations
fn test_basic_operations(reporter: &ElasticsearchReporter) -> bool {
    println!("Testing basic Per-CPU Map operations...");
    let start_time = Instant::now();
    
    // Create a map
    let mut map = OptimizedPerCpuMap::create(1024);
    
    let key = TestKey { id: 42 };
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let value = TestValue { count: 100, timestamp: now };
    
    // Get current CPU
    let cpu = 0; // Always use CPU 0 for basic tests
    
    // Test initial lookup (should be None)
    if map.lookup_cpu(&key, cpu).is_some() {
        let result = TestResult {
            test_name: "basic_operations".to_string(),
            success: false,
            timestamp: Utc::now(),
            duration_ms: start_time.elapsed().as_millis() as u64,
            details: Some("Initial lookup returned a value when it shouldn't".to_string()),
            stats: Some(map.stats().clone()),
        };
        
        let _ = reporter.report_test_result(result);
        println!("ERROR: Initial lookup returned a value when it shouldn't");
        return false;
    }
    
    // Test update
    if !map.update_cpu(&key, &value, cpu) {
        let result = TestResult {
            test_name: "basic_operations".to_string(),
            success: false,
            timestamp: Utc::now(),
            duration_ms: start_time.elapsed().as_millis() as u64,
            details: Some("Update failed".to_string()),
            stats: Some(map.stats().clone()),
        };
        
        let _ = reporter.report_test_result(result);
        println!("ERROR: Update failed");
        return false;
    }
    
    // Test lookup after update
    match map.lookup_cpu(&key, cpu) {
        Some(v) => {
            if v != value {
                let result = TestResult {
                    test_name: "basic_operations".to_string(),
                    success: false,
                    timestamp: Utc::now(),
                    duration_ms: start_time.elapsed().as_millis() as u64,
                    details: Some(format!("Lookup returned wrong value: {:?}", v)),
                    stats: Some(map.stats().clone()),
                };
                
                let _ = reporter.report_test_result(result);
                println!("ERROR: Lookup returned wrong value: {:?}", v);
                return false;
            }
        },
        None => {
            let result = TestResult {
                test_name: "basic_operations".to_string(),
                success: false,
                timestamp: Utc::now(),
                duration_ms: start_time.elapsed().as_millis() as u64,
                details: Some("Lookup after update returned None".to_string()),
                stats: Some(map.stats().clone()),
            };
            
            let _ = reporter.report_test_result(result);
            println!("ERROR: Lookup after update returned None");
            return false;
        }
    }
    
    // Test delete
    if !map.delete(&key) {
        let result = TestResult {
            test_name: "basic_operations".to_string(),
            success: false,
            timestamp: Utc::now(),
            duration_ms: start_time.elapsed().as_millis() as u64,
            details: Some("Delete returned false".to_string()),
            stats: Some(map.stats().clone()),
        };
        
        let _ = reporter.report_test_result(result);
        println!("ERROR: Delete returned false");
        return false;
    }
    
    // Test lookup after delete
    if map.lookup_cpu(&key, cpu).is_some() {
        let result = TestResult {
            test_name: "basic_operations".to_string(),
            success: false,
            timestamp: Utc::now(),
            duration_ms: start_time.elapsed().as_millis() as u64,
            details: Some("Lookup after delete returned a value when it shouldn't".to_string()),
            stats: Some(map.stats().clone()),
        };
        
        let _ = reporter.report_test_result(result);
        println!("ERROR: Lookup after delete returned a value when it shouldn't");
        return false;
    }
    
    let result = TestResult {
        test_name: "basic_operations".to_string(),
        success: true,
        timestamp: Utc::now(),
        duration_ms: start_time.elapsed().as_millis() as u64,
        details: None,
        stats: Some(map.stats().clone()),
    };
    
    let _ = reporter.report_test_result(result);
    println!("Basic operations test passed");
    true
}

// Test concurrent access
fn test_concurrent_access(reporter: &ElasticsearchReporter) -> bool {
    println!("Testing concurrent access to Per-CPU Map...");
    let start_time = Instant::now();
    
    // Create a map
    let map = Arc::new(std::sync::Mutex::new(OptimizedPerCpuMap::create(1024)));
    
    let num_threads = 4;
    let operations_per_thread = 100;
    
    let mut handles = vec![];
    
    for thread_id in 0..num_threads {
        let map_clone = map.clone();
        
        let handle = thread::spawn(move || {
            let cpu = thread_id % num_cpus::get();
            let mut success = true;
            
            for i in 0..operations_per_thread {
                let key = TestKey { id: ((thread_id * operations_per_thread + i) as u32) };
                let value = TestValue { count: (key.id as u64 * 2), timestamp: 0 };
                
                // Update
                {
                    let mut map = map_clone.lock().unwrap();
                    if !map.update_cpu(&key, &value, cpu) {
                        println!("Thread {}: Update failed", thread_id);
                        success = false;
                        break;
                    }
                }
                
                // Lookup
                {
                    let mut map = map_clone.lock().unwrap();
                    match map.lookup_cpu(&key, cpu) {
                        Some(v) => {
                            if v.count != value.count {
                                println!("Thread {}: Lookup returned wrong value: {} != {}", 
                                         thread_id, v.count, value.count);
                                success = false;
                                break;
                            }
                        },
                        None => {
                            println!("Thread {}: Lookup returned None", thread_id);
                            success = false;
                            break;
                        }
                    }
                }
            }
            
            success
        });
        
        handles.push(handle);
    }
    
    let mut success = true;
    
    for handle in handles {
        match handle.join() {
            Ok(thread_success) => {
                if !thread_success {
                    success = false;
                }
            },
            Err(_) => {
                println!("Thread panicked");
                success = false;
            }
        }
    }
    
    let stats = map.lock().unwrap().stats().clone();
    
    let result = TestResult {
        test_name: "concurrent_access".to_string(),
        success,
        timestamp: Utc::now(),
        duration_ms: start_time.elapsed().as_millis() as u64,
        details: if success { None } else { Some("Concurrent access test failed".to_string()) },
        stats: Some(stats),
    };
    
    let _ = reporter.report_test_result(result);
    
    if success {
        println!("Concurrent access test passed");
    } else {
        println!("Concurrent access test failed");
    }
    
    success
}

// Test aggregation
fn test_aggregation(reporter: &ElasticsearchReporter) -> bool {
    println!("Testing Per-CPU Map aggregation...");
    let start_time = Instant::now();
    
    // Create a map
    let mut map = OptimizedPerCpuMap::create(1024);
    
    let key = TestKey { id: 42 };
    let num_cpus = std::cmp::min(4, num_cpus::get()); // Test up to 4 CPUs
    
    // Insert different values for each CPU
    for cpu in 0..num_cpus {
        let value = TestValue { 
            count: (cpu as u64 + 1) * 100, 
            timestamp: 0 
        };
        
        if !map.update_cpu(&key, &value, cpu) {
            let result = TestResult {
                test_name: "aggregation".to_string(),
                success: false,
                timestamp: Utc::now(),
                duration_ms: start_time.elapsed().as_millis() as u64,
                details: Some(format!("Failed to update CPU {}", cpu)),
                stats: Some(map.stats().clone()),
            };
            
            let _ = reporter.report_test_result(result);
            println!("ERROR: Failed to update CPU {}", cpu);
            return false;
        }
    }
    
    // Test sum aggregation
    match map.sum(&key) {
        Some(sum) => {
            let expected = (1..=num_cpus as u64).map(|cpu| cpu * 100).sum();
            if sum == expected {
                println!("Sum aggregation correct: {}", sum);
            } else {
                let result = TestResult {
                    test_name: "aggregation".to_string(),
                    success: false,
                    timestamp: Utc::now(),
                    duration_ms: start_time.elapsed().as_millis() as u64,
                    details: Some(format!("Sum aggregation incorrect: {} != {}", sum, expected)),
                    stats: Some(map.stats().clone()),
                };
                
                let _ = reporter.report_test_result(result);
                println!("ERROR: Sum aggregation incorrect: {} != {}", sum, expected);
                return false;
            }
        },
        None => {
            let result = TestResult {
                test_name: "aggregation".to_string(),
                success: false,
                timestamp: Utc::now(),
                duration_ms: start_time.elapsed().as_millis() as u64,
                details: Some("Sum aggregation returned None".to_string()),
                stats: Some(map.stats().clone()),
            };
            
            let _ = reporter.report_test_result(result);
            println!("ERROR: Sum aggregation returned None");
            return false;
        }
    }
    
    let result = TestResult {
        test_name: "aggregation".to_string(),
        success: true,
        timestamp: Utc::now(),
        duration_ms: start_time.elapsed().as_millis() as u64,
        details: None,
        stats: Some(map.stats().clone()),
    };
    
    let _ = reporter.report_test_result(result);
    println!("Aggregation test passed");
    true
}

fn main() {
    println!("Starting Per-CPU Map Tests with Elasticsearch Reporting");
    
    // Create Elasticsearch reporter
    let reporter = ElasticsearchReporter::new("localhost", 9200, "percpumap_test_results");
    
    // Ensure index exists
    if let Err(e) = reporter.ensure_index_exists() {
        println!("Warning: Failed to ensure Elasticsearch index exists: {}", e);
        println!("Will continue with tests, but results may not be reported correctly.");
    }
    
    let mut success = true;
    
    // Run tests
    if !test_basic_operations(&reporter) {
        success = false;
    }
    
    if !test_concurrent_access(&reporter) {
        success = false;
    }
    
    if !test_aggregation(&reporter) {
        success = false;
    }
    
    // Report overall result
    let result = TestResult {
        test_name: "overall_result".to_string(),
        success,
        timestamp: Utc::now(),
        duration_ms: 0, // Not applicable for overall result
        details: None,
        stats: None,
    };
    
    if let Err(e) = reporter.report_test_result(result) {
        println!("Warning: Failed to report overall result to Elasticsearch: {}", e);
    }
    
    if success {
        println!("All Per-CPU Map tests passed");
    } else {
        println!("Some Per-CPU Map tests failed");
        std::process::exit(1);
    }
}
EOF

# Compile and run the test program using nix-shell
echo -e "${YELLOW}Compiling and running test program using nix-shell...${NC}"
nix-shell --run "cd $TEST_DIR && cargo build --release && cargo run --release"

# Check the result
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Per-CPU Maps Implementation Test Successful${NC}"
    
    # Check Elasticsearch for results
    echo -e "${YELLOW}Checking Elasticsearch for test results...${NC}"
    curl -s "http://localhost:9200/percpumap_test_results/_search?q=test_name:overall_result&pretty" | grep -q '"success" : true'
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Elasticsearch confirms successful test results${NC}"
        exit 0
    else
        echo -e "${RED}Could not confirm test results in Elasticsearch${NC}"
        exit 1
    fi
else
    echo -e "${RED}Per-CPU Maps Implementation Test Failed${NC}"
    exit 1
fi 