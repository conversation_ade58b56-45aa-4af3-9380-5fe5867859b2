syntax = "proto3";

package inspector.gadget.v1;

// Service for executing system calls in the Vagrant VM
service SystemCall {
  // Execute a command in the VM
  rpc ExecuteCommand (CommandRequest) returns (CommandResponse) {}
  
  // Stream system call events from the VM
  rpc StreamSyscalls (StreamRequest) returns (stream SyscallEvent) {}
}

// Request to execute a command
message CommandRequest {
  // Command to execute (e.g., "create_file", "read_file", etc.)
  string command = 1;
  
  // JSON-encoded arguments for the command
  string args = 2;
}

// Response from executing a command
message CommandResponse {
  // Status of the command execution (SUCCESS, ERROR)
  string status = 1;
  
  // Output of the command
  string output = 2;
}

// Request to stream system call events
message StreamRequest {
  // Filter for specific system calls (empty means all)
  repeated string syscall_filter = 1;
  
  // Maximum number of events to stream (0 means unlimited)
  int32 max_events = 2;
  
  // Duration in seconds to stream (0 means unlimited)
  int32 duration_seconds = 3;
}

// System call event
message SyscallEvent {
  // Timestamp of the system call
  string timestamp = 1;
  
  // Name of the system call
  string name = 2;
  
  // Arguments to the system call
  repeated string args = 3;
  
  // Result of the system call
  int64 result = 4;
  
  // Count of this system call (sequence number)
  int32 count = 5;
  
  // Process ID that made the system call
  int32 process_id = 6;
  
  // Thread ID that made the system call
  int32 thread_id = 7;
} 