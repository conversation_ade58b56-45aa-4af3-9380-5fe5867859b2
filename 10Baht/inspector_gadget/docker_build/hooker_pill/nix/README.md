# Nix Development Environment

This directory contains the Nix configuration for the Inspector Gadget project. It provides a reproducible development environment with all the necessary dependencies.

## Usage

### Basic Usage

To enter the development environment, run:

```bash
cd /path/to/inspector_gadget
nix-shell ./nix/shell.nix
```

This will create a shell with all the required dependencies for building and testing Inspector Gadget.

### Using in Scripts

To run a command within the nix-shell environment, use:

```bash
nix-shell ./nix/shell.nix --run "your_command_here"
```

For example, to build the project:

```bash
nix-shell ./nix/shell.nix --run "cargo build"
```

## Components

The nix-shell environment includes:

- Rust toolchain (rustc, cargo, rustfmt, clippy)
- C/C++ toolchain (gcc, gdb, make, cmake)
- Python with required packages
- Development tools (git)
- Required libraries (openssl, libbpf)

## Local Nix Store

The configuration uses a local Nix store in `.nix_local/` to avoid polluting the system Nix store. This directory is excluded from git.

## Customization

To add more dependencies or customize the environment, edit the `shell.nix` file.

## Troubleshooting

If you encounter issues with the nix-shell environment:

1. Make sure you have <PERSON> installed: `curl -L https://nixos.org/nix/install | sh`
2. Try removing the `.nix_local` directory and running nix-shell again
3. Check for error messages in the nix-shell output 