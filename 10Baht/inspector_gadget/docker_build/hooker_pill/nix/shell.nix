let
  # Use a specific nixpkgs commit for stability
  nixpkgs = fetchTarball {
    url = "https://github.com/NixOS/nixpkgs/archive/nixos-23.11.tar.gz";
    sha256 = "sha256:1f5d2g1p6nfwycpmrnnmc2xmcszp804adp16knjvdkj8nz36y1fg";
  };
  pkgs = import nixpkgs {};
in

pkgs.mkShell {
  name = "inspector-gadget-env";
  
  buildInputs = with pkgs; [
    # Rust
    rustc
    cargo
    
    # Python with essential packages
    (python310.withPackages (ps: with ps; [
      click
      rich
      pyyaml
      jinja2
      requests
      elasticsearch
      pip
      grpcio
      protobuf
    ]))
    
    # Essential libraries
    openssl
    pkg-config
    
    # Basic tools
    curl
    jq
  ];

  shellHook = ''
    # Set up environment variables
    export PYTHONPATH="$PWD:$PYTHONPATH"
    export RUST_BACKTRACE=1
    
    echo "========================================================"
    echo "Inspector Gadget Development Environment"
    echo "========================================================"
    echo "Rust: $(rustc --version)"
    echo "Python: $(python --version)"
    echo "========================================================"
  '';
} 