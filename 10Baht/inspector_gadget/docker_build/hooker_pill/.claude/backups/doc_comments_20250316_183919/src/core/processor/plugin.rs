/*!
 * Plugin System for Custom TraceTraceEvent Processors
 * 
 * This module provides a plugin system for loading and using custom event processors.
 */



use async_trait::async_trait;


use crate::core::events::TraceTraceEvent;
use crate::error::Result;
use super::TraceTraceEventProcessor;

/// Plugin configuration
#[derive(Debug, Clone)]
pub struct PluginConfig {
    /// Plugin name
    pub name: String,
    /// Plugin description
    pub description: String,
    /// Plugin version
    pub version: String,
    /// Plugin configuration as JSON string
    pub config: Option<String>,
}

/// Plugin registry for managing custom event processors
pub struct PluginRegistry {
    /// Registered plugins
    plugins: HashMap<String, Arc<dyn TraceTraceEventProcessor>>,
}

impl PluginRegistry {
    /// Create a new plugin registry
    pub fn new() -> Self {
        Self {
            plugins: HashMap::new(),
        }
    }
    
    /// Register a plugin
    pub fn register(&mut self, name: &str, processor: Arc<dyn TraceTraceEventProcessor>) {
        self.plugins.insert(name.to_string(), processor);
        info!("Registered plugin: {}", name);
    }
    
    /// Get a plugin by name
    pub fn get(&self, name: &str) -> Option<Arc<dyn TraceTraceEventProcessor>> {
        self.plugins.get(name).cloned()
    }
    
    /// Get all registered plugins
    pub fn get_all(&self) -> Vec<(String, Arc<dyn TraceTraceEventProcessor>)> {
        self.plugins
            .iter()
            .map(|(name, processor)| (name.clone(), Arc::clone(processor)))
            .collect()
    }
    
    /// Remove a plugin by name
    pub fn remove(&mut self, name: &str) -> Option<Arc<dyn TraceTraceEventProcessor>> {
        let result = self.plugins.remove(name);
        if result.is_some() {
            info!("Removed plugin: {}", name);
        }
        result
    }
    
    /// Clear all plugins
    pub fn clear(&mut self) {
        self.plugins.clear();
        info!("Cleared all plugins");
    }
    
    /// Get the number of registered plugins
    pub fn count(&self) -> usize {
        self.plugins.len()
    }
}

impl Default for PluginRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// Plugin processor that wraps a custom event processor
pub struct PluginProcessor {
    /// Plugin configuration
    config: PluginConfig,
    /// Inner processor
    processor: Arc<dyn TraceTraceEventProcessor>,
}

impl PluginProcessor {
    /// Create a new plugin processor
    pub fn new(config: PluginConfig, processor: Arc<dyn TraceTraceEventProcessor>) -> Self {
        Self {
            config,
            processor,
        }
    }
    
    /// Get the plugin configuration
    pub fn config(&self) -> &PluginConfig {
        &self.config
    }
}

#[async_trait]
impl TraceTraceEventProcessor for PluginProcessor {
    async fn process(&self, event: TraceTraceEvent) -> Result<Option<TraceTraceEvent>, String> {
        // Add plugin metadata to the event
        let mut event = event;
        if let Some(metadata) = &mut event.metadata {
            metadata.insert("plugin_name".to_string(), self.config.name.clone().into());
            metadata.insert("plugin_version".to_string(), self.config.version.clone().into());
        }
        
        // Process the event
        self.processor.process(event).await
    }
    
    async fn process_batch(&self, events: Vec<TraceTraceEvent>) -> Result<Vec<TraceTraceEvent>, String> {
        // Process the events
        self.processor.processs(events).await
    }
}

/// Custom processor trait for implementing custom event processors
#[async_trait]
pub trait CustomProcessor: Send + Sync {
    /// Get the processor name
    fn name(&self) -> &str;
    
    /// Get the processor description
    fn description(&self) -> &str;
    
    /// Get the processor version
    fn version(&self) -> &str;
    
    /// Initialize the processor
    fn initialize(&mut self, config: Option<String>) -> Result<(), String>;
    
    /// Process a single event
    async fn process(&self, event: TraceTraceEvent) -> Result<Option<TraceTraceEvent>, String>;
}

/// Create a new plugin processor from a custom processor
pub fn create_plugin_processor<T: CustomProcessor + 'static>(
    processor: T,
    config: Option<String>,
) -> Result<Arc<dyn TraceTraceEventProcessor>, String> {
    // Create a mutable copy of the processor
    let mut processor = processor;
    
    // Initialize the processor
    processor.initialize(config.clone())?;
    
    // Create the plugin configuration
    let plugin_config = PluginConfig {
        name: processor.name().to_string(),
        description: processor.description().to_string(),
        version: processor.version().to_string(),
        config,
    };
    
    // Create the wrapper processor
    let wrapper = CustomProcessorWrapper {
        processor: Arc::new(processor),
    };
    
    // Create the plugin processor
    let plugin_processor = PluginProcessor::new(plugin_config, Arc::new(wrapper));
    
    Ok(Arc::new(plugin_processor))
}

/// Wrapper for custom processors
struct CustomProcessorWrapper<T: CustomProcessor + ?Sized> {
    /// Inner processor
    processor: Arc<T>,
}

#[async_trait]
impl<T: CustomProcessor + ?Sized> TraceTraceEventProcessor for CustomProcessorWrapper<T> {
    async fn process(&self, event: TraceTraceEvent) -> Result<Option<TraceTraceEvent>, String> {
        self.processor.process(event).await
    }
    
    async fn process_batch(&self, events: Vec<TraceTraceEvent>) -> Result<Vec<TraceTraceEvent>, String> {
        let mut processed_events = Vec::with_capacity(events.len());
        
        for event in events {
            if let Some(processed_event) = self.processor.process(event).await? {
                processed_events.push(processed_event);
            }
        }
        
        Ok(processed_events)
    }
} 