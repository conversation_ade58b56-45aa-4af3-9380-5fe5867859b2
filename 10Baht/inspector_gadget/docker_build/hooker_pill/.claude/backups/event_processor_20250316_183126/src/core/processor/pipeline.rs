/*!
 * Pipeline Processor Implementation
 * 
 * This module provides a pipeline-based event processor that chains together
 * multiple processing steps, including normalization, enrichment, filtering,
 * and correlation.
 */

use std::sync::Arc;
use async_trait::async_trait;
use log::{debug, trace};

use crate::core::events::TraceEvent;
use crate::error::Result;
use super::{
    EventProcessor,
    SyscallNormalizer, SyscallNormalizerConfig,
    ContextEnricher, ContextEnricherConfig,
    EventFilter, EventFilterConfig,
    EventCorrelator, EventCorrelatorConfig,
};

/// Pipeline processor configuration
#[derive(Debug, Clone)]
pub struct PipelineProcessorConfig {
    /// Normalization configuration
    pub normalizer_config: Option<SyscallNormalizerConfig>,
    /// Enrichment configuration
    pub enricher_config: Option<ContextEnricherConfig>,
    /// Filter configuration
    pub filter_config: Option<EventFilterConfig>,
    /// Correlation configuration
    pub correlator_config: Option<EventCorrelatorConfig>,
}

impl Default for PipelineProcessorConfig {
    fn default() -> Self {
        Self {
            normalizer_config: None,
            enricher_config: None,
            filter_config: None,
            correlator_config: None,
        }
    }
}

/// Pipeline processor
pub struct PipelineProcessor {
    /// Normalizer
    normalizer: Option<Arc<SyscallNormalizer>>,
    /// Enricher
    enricher: Option<Arc<ContextEnricher>>,
    /// Filter
    filter: Option<Arc<EventFilter>>,
    /// Correlator
    correlator: Option<Arc<EventCorrelator>>,
}

impl PipelineProcessor {
    /// Create a new pipeline processor
    pub fn new(config: PipelineProcessorConfig) -> Self {
        let normalizer = config.normalizer_config.map(|c| Arc::new(SyscallNormalizer::new(c)));
        let enricher = config.enricher_config.map(|c| Arc::new(ContextEnricher::new(c)));
        let filter = config.filter_config.map(|c| Arc::new(EventFilter::new(c)));
        let correlator = config.correlator_config.map(|c| Arc::new(EventCorrelator::new(c)));
        
        Self {
            normalizer,
            enricher,
            filter,
            correlator,
        }
    }
    
    /// Create a new pipeline processor with default configuration
    pub fn with_defaults() -> Self {
        Self::new(PipelineProcessorConfig::default())
    }
}

#[async_trait]
impl EventProcessor for PipelineProcessor {
    async fn process(&self, event: TraceEvent) -> Result<Option<TraceEvent>> {
        trace!("Processing event: {}", event.id);
        
        // Apply normalizer
        let event = if let Some(normalizer) = &self.normalizer {
            match normalizer.process(event).await? {
                Some(e) => e,
                None => return Ok(None),
            }
        } else {
            event
        };
        
        // Apply enricher
        let event = if let Some(enricher) = &self.enricher {
            match enricher.process(event).await? {
                Some(e) => e,
                None => return Ok(None),
            }
        } else {
            event
        };
        
        // Apply filter
        let event = if let Some(filter) = &self.filter {
            match filter.process(event).await? {
                Some(e) => e,
                None => return Ok(None),
            }
        } else {
            event
        };
        
        // Apply correlator
        let event = if let Some(correlator) = &self.correlator {
            match correlator.process(event).await? {
                Some(e) => e,
                None => return Ok(None),
            }
        } else {
            event
        };
        
        debug!("Event processed: {}", event.id);
        Ok(Some(event))
    }
    
    async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
        trace!("Processing batch of {} events", events.len());
        
        let mut result = Vec::with_capacity(events.len());
        
        for event in events {
            if let Some(processed) = self.process(event).await? {
                result.push(processed);
            }
        }
        
        debug!("Batch processed: {} events in, {} events out", events.len(), result.len());
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{EventData, EventSeverity, EventType, TraceEvent};
    use crate::core::processor::FilterRule;

    #[tokio::test]
    async fn test_pipeline_processor() {
        // Create a filter configuration
        let filter_config = EventFilterConfig {
            rules: vec![
                FilterRule::IncludePattern("test".to_string()),
            ],
        };
        
        // Create a pipeline configuration
        let config = PipelineProcessorConfig {
            normalizer_config: None,
            enricher_config: None,
            filter_config: Some(filter_config),
            correlator_config: None,
        };
        
        // Create a pipeline processor
        let processor = PipelineProcessor::new(config);
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("other".to_string()),
            EventData::Custom(serde_json::json!({"other": "value"})),
            1,
            1,
            EventSeverity::Info,
            "other",
            vec![],
        );
        
        // Process events
        let result1 = processor.process(event1.clone()).await.unwrap();
        let result2 = processor.process(event2.clone()).await.unwrap();
        
        // Check results
        assert!(result1.is_some());
        assert!(result2.is_none());
        
        // Process batch
        let batch = vec![event1.clone(), event2.clone()];
        let results = processor.process_batch(batch).await.unwrap();
        
        // Check results
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].id, event1.id);
    }
} 