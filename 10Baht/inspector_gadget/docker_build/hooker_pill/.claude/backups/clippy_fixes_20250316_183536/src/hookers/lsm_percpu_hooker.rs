use crate::ebpf::maps::TypedOptimizedPerCpuMap;
use crate::hookers::lsm_hooker::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>smHookerConfig};
use serde::{Serialize, Deserialize};
use std::error::Error;
use std::sync::Arc;
use std::time::{Duration, Instant};
use log::{debug, error, info, warn};

/// Statistics for LSM events by process ID
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, Serialize, Deserialize)]
pub struct LsmEventStats {
    /// Number of file access events
    pub file_access_count: u64,
    /// Number of network access events
    pub network_access_count: u64,
    /// Number of process creation events
    pub process_creation_count: u64,
    /// Number of capability check events
    pub capability_check_count: u64,
    /// Number of policy violations
    pub policy_violation_count: u64,
    /// Last event timestamp
    pub last_event_timestamp: u64,
}

impl Default for LsmEventStats {
    fn default() -> Self {
        Self {
            file_access_count: 0,
            network_access_count: 0,
            process_creation_count: 0,
            capability_check_count: 0,
            policy_violation_count: 0,
            last_event_timestamp: 0,
        }
    }
}

impl std::ops::Add for LsmEventStats {
    type Output = Self;
    
    fn add(self, other: Self) -> Self {
        Self {
            file_access_count: self.file_access_count + other.file_access_count,
            network_access_count: self.network_access_count + other.network_access_count,
            process_creation_count: self.process_creation_count + other.process_creation_count,
            capability_check_count: self.capability_check_count + other.capability_check_count,
            policy_violation_count: self.policy_violation_count + other.policy_violation_count,
            last_event_timestamp: std::cmp::max(self.last_event_timestamp, other.last_event_timestamp),
        }
    }
}

/// LSM hooker with Per-CPU Maps for improved performance
pub struct LsmPerCpuHooker {
    /// Inner LSM hooker
    inner: LsmHooker,
    /// Map to store event statistics by process ID
    event_stats: Arc<TypedOptimizedPerCpuMap<u32, LsmEventStats>>,
    /// Map to store event statistics by user ID
    user_stats: Arc<TypedOptimizedPerCpuMap<u32, LsmEventStats>>,
    /// Map to store event statistics by namespace ID
    namespace_stats: Arc<TypedOptimizedPerCpuMap<u32, LsmEventStats>>,
    /// Last statistics export time
    last_export_time: Instant,
    /// Statistics export interval
    export_interval: Duration,
}

impl LsmPerCpuHooker {
    /// Creates a new LSM hooker with Per-CPU Maps
    pub fn new(config: LsmHookerConfig) -> Result<Self, Box<dyn Error>> {
        // Create the inner LSM hooker
        let inner = LsmHooker::new(config)?;
        
        // Create the Per-CPU Maps
        let event_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, LsmEventStats>::create(10240)?);
        let user_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, LsmEventStats>::create(1024)?);
        let namespace_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, LsmEventStats>::create(256)?);
        
        Ok(Self {
            inner,
            event_stats,
            user_stats,
            namespace_stats,
            last_export_time: Instant::now(),
            export_interval: Duration::from_secs(60),
        })
    }
    
    /// Handles an LSM event
    pub fn handle_event(&self, event: &LsmEvent) -> Result<(), Box<dyn Error>> {
        // Process the event with the inner hooker
        self.inner.handle_event(event)?;
        
        // Update statistics in the Per-CPU Maps
        self.update_event_stats(event)?;
        
        // Export statistics if the export interval has elapsed
        if self.last_export_time.elapsed() >= self.export_interval {
            self.export_statistics()?;
        }
        
        Ok(())
    }
    
    /// Updates event statistics in the Per-CPU Maps
    fn update_event_stats(&self, event: &LsmEvent) -> Result<(), Box<dyn Error>> {
        // Get the current timestamp
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Update process statistics
        let pid = event.process_id;
        let mut pid_stats = self.event_stats.lookup(pid).unwrap_or_default();
        
        // Update user statistics
        let uid = event.user_id;
        let mut uid_stats = self.user_stats.lookup(uid).unwrap_or_default();
        
        // Update namespace statistics
        let ns_id = event.namespace_id.unwrap_or(0);
        let mut ns_stats = self.namespace_stats.lookup(ns_id).unwrap_or_default();
        
        // Update the appropriate counters based on event type
        match event.event_type.as_str() {
            "file_access" => {
                pid_stats.file_access_count += 1;
                uid_stats.file_access_count += 1;
                ns_stats.file_access_count += 1;
            },
            "network_access" => {
                pid_stats.network_access_count += 1;
                uid_stats.network_access_count += 1;
                ns_stats.network_access_count += 1;
            },
            "process_creation" => {
                pid_stats.process_creation_count += 1;
                uid_stats.process_creation_count += 1;
                ns_stats.process_creation_count += 1;
            },
            "capability_check" => {
                pid_stats.capability_check_count += 1;
                uid_stats.capability_check_count += 1;
                ns_stats.capability_check_count += 1;
            },
            _ => {}
        }
        
        // Update policy violation counter if applicable
        if event.is_violation {
            pid_stats.policy_violation_count += 1;
            uid_stats.policy_violation_count += 1;
            ns_stats.policy_violation_count += 1;
        }
        
        // Update timestamps
        pid_stats.last_event_timestamp = timestamp;
        uid_stats.last_event_timestamp = timestamp;
        ns_stats.last_event_timestamp = timestamp;
        
        // Store the updated statistics
        self.event_stats.update(pid, pid_stats)?;
        self.user_stats.update(uid, uid_stats)?;
        self.namespace_stats.update(ns_id, ns_stats)?;
        
        Ok(())
    }
    
    /// Exports statistics to external systems (e.g., Elasticsearch)
    fn export_statistics(&self) -> Result<(), Box<dyn Error>> {
        // This would be implemented to export statistics to external systems
        // For now, we'll just log some aggregated statistics
        
        // Get top 10 processes by total event count
        let mut processes = Vec::new();
        for pid in 1..10240 {
            if let Some(stats) = self.event_stats.lookup_aggregated(pid) {
                let total_events = stats.file_access_count
                    + stats.network_access_count
                    + stats.process_creation_count
                    + stats.capability_check_count;
                
                if total_events > 0 {
                    processes.push((pid, total_events, stats));
                }
            }
        }
        
        // Sort by total event count
        processes.sort_by(|a, b| b.1.cmp(&a.1));
        
        // Log the top 10 processes
        info!("Top processes by event count:");
        for (i, (pid, total, stats)) in processes.iter().take(10).enumerate() {
            info!(
                "{}. PID {}: {} events ({} file, {} network, {} process, {} capability, {} violations)",
                i + 1,
                pid,
                total,
                stats.file_access_count,
                stats.network_access_count,
                stats.process_creation_count,
                stats.capability_check_count,
                stats.policy_violation_count
            );
        }
        
        // Get top 5 users by policy violations
        let mut users = Vec::new();
        for uid in 0..1024 {
            if let Some(stats) = self.user_stats.lookup_aggregated(uid) {
                if stats.policy_violation_count > 0 {
                    users.push((uid, stats.policy_violation_count, stats));
                }
            }
        }
        
        // Sort by policy violation count
        users.sort_by(|a, b| b.1.cmp(&a.1));
        
        // Log the top 5 users
        info!("Top users by policy violations:");
        for (i, (uid, violations, stats)) in users.iter().take(5).enumerate() {
            info!(
                "{}. UID {}: {} violations (out of {} total events)",
                i + 1,
                uid,
                violations,
                stats.file_access_count
                    + stats.network_access_count
                    + stats.process_creation_count
                    + stats.capability_check_count
            );
        }
        
        Ok(())
    }
    
    /// Gets aggregated statistics for a process
    pub fn get_process_stats(&self, pid: u32) -> Option<LsmEventStats> {
        self.event_stats.lookup_aggregated(pid)
    }
    
    /// Gets aggregated statistics for a user
    pub fn get_user_stats(&self, uid: u32) -> Option<LsmEventStats> {
        self.user_stats.lookup_aggregated(uid)
    }
    
    /// Gets aggregated statistics for a namespace
    pub fn get_namespace_stats(&self, ns_id: u32) -> Option<LsmEventStats> {
        self.namespace_stats.lookup_aggregated(ns_id)
    }
    
    /// Gets the inner LSM hooker
    pub fn inner(&self) -> &LsmHooker {
        &self.inner
    }
    
    /// Gets map statistics
    pub fn get_map_stats(&self) -> (u64, u64, u64) {
        let event_stats = self.event_stats.get_stats();
        let user_stats = self.user_stats.get_stats();
        let namespace_stats = self.namespace_stats.get_stats();
        
        let lookups = event_stats.lookups.load(std::sync::atomic::Ordering::Relaxed)
            + user_stats.lookups.load(std::sync::atomic::Ordering::Relaxed)
            + namespace_stats.lookups.load(std::sync::atomic::Ordering::Relaxed);
            
        let updates = event_stats.updates.load(std::sync::atomic::Ordering::Relaxed)
            + user_stats.updates.load(std::sync::atomic::Ordering::Relaxed)
            + namespace_stats.updates.load(std::sync::atomic::Ordering::Relaxed);
            
        let misses = event_stats.misses.load(std::sync::atomic::Ordering::Relaxed)
            + user_stats.misses.load(std::sync::atomic::Ordering::Relaxed)
            + namespace_stats.misses.load(std::sync::atomic::Ordering::Relaxed);
            
        (lookups, updates, misses)
    }
} 