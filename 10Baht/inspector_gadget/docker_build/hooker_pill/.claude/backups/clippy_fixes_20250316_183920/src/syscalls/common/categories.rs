/*!
 * Syscall Categories
 * 
 * This module defines categories for system calls to group them by functionality.
 */


use serde::{Serialize, Deserialize};

/// Categories of system calls
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SyscallCategory {
    /// File system operations (open, read, write, close, etc.)
    FileSystem,
    /// Process management (fork, exec, exit, etc.)
    Process,
    /// Memory management (mmap, munmap, brk, etc.)
    Memory,
    /// Network operations (socket, connect, send, recv, etc.)
    Network,
    /// Inter-process communication (pipe, signal, etc.)
    IPC,
    /// Time-related operations (clock_gettime, nanosleep, etc.)
    Time,
    /// System information (uname, sysinfo, etc.)
    SystemInfo,
    /// Security operations (chmod, chown, etc.)
    Security,
    /// I/O operations (poll, select, epoll, etc.)
    IO,
    /// Thread operations (clone with CLONE_THREAD, etc.)
    Thread,
    /// Synchronization primitives (futex, etc.)
    Synchronization,
    /// Module operations (init_module, etc.)
    Module,
    /// Device operations (ioctl, etc.)
    Device,
    /// Other or uncategorized syscalls
    Other,
}

impl SyscallCategory {
    /// Get a human-readable description of the category
    pub fn description(&self) -> &'static str {
        match self {
            Self::FileSystem => "File system operations",
            Self::Process => "Process management",
            Self::Memory => "Memory management",
            Self::Network => "Network operations",
            Self::IPC => "Inter-process communication",
            Self::Time => "Time-related operations",
            Self::SystemInfo => "System information",
            Self::Security => "Security operations",
            Self::IO => "I/O operations",
            Self::Thread => "Thread operations",
            Self::Synchronization => "Synchronization primitives",
            Self::Module => "Module operations",
            Self::Device => "Device operations",
            Self::Other => "Other or uncategorized syscalls",
        }
    }
    
    /// Get all available categories
    pub fn all() -> &'static [SyscallCategory] {
        &[
            Self::FileSystem,
            Self::Process,
            Self::Memory,
            Self::Network,
            Self::IPC,
            Self::Time,
            Self::SystemInfo,
            Self::Security,
            Self::IO,
            Self::Thread,
            Self::Synchronization,
            Self::Module,
            Self::Device,
            Self::Other,
        ]
    }
    
    /// Get categories that are security-sensitive
    pub fn security_sensitive() -> &'static [SyscallCategory] {
        &[
            Self::Process,
            Self::Network,
            Self::Security,
            Self::Module,
        ]
    }
}

impl fmt::Display for SyscallCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::FileSystem => write!(f, "FileSystem"),
            Self::Process => write!(f, "Process"),
            Self::Memory => write!(f, "Memory"),
            Self::Network => write!(f, "Network"),
            Self::IPC => write!(f, "IPC"),
            Self::Time => write!(f, "Time"),
            Self::SystemInfo => write!(f, "SystemInfo"),
            Self::Security => write!(f, "Security"),
            Self::IO => write!(f, "IO"),
            Self::Thread => write!(f, "Thread"),
            Self::Synchronization => write!(f, "Synchronization"),
            Self::Module => write!(f, "Module"),
            Self::Device => write!(f, "Device"),
            Self::Other => write!(f, "Other"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_category_description() {
        assert_eq!(
            SyscallCategory::FileSystem.description(),
            "File system operations"
        );
        assert_eq!(
            SyscallCategory::Process.description(),
            "Process management"
        );
        assert_eq!(
            SyscallCategory::Memory.description(),
            "Memory management"
        );
    }
    
    #[test]
    fn test_all_categories() {
        let all = SyscallCategory::all();
        assert_eq!(all.len(), 14);
        assert!(all.contains(&SyscallCategory::FileSystem));
        assert!(all.contains(&SyscallCategory::Process));
        assert!(all.contains(&SyscallCategory::Memory));
        assert!(all.contains(&SyscallCategory::Network));
    }
    
    #[test]
    fn test_security_sensitive_categories() {
        let sensitive = SyscallCategory::security_sensitive();
        assert!(sensitive.contains(&SyscallCategory::Process));
        assert!(sensitive.contains(&SyscallCategory::Network));
        assert!(sensitive.contains(&SyscallCategory::Security));
        assert!(sensitive.contains(&SyscallCategory::Module));
        assert!(!sensitive.contains(&SyscallCategory::Time));
    }
    
    #[test]
    fn test_category_display() {
        assert_eq!(format!("{}", SyscallCategory::FileSystem), "FileSystem");
        assert_eq!(format!("{}", SyscallCategory::Process), "Process");
        assert_eq!(format!("{}", SyscallCategory::Memory), "Memory");
    }
} 