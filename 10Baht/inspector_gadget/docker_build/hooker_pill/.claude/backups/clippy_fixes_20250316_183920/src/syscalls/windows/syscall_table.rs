/*!
 * Windows Syscall Table
 * 
 * This module provides functionality for loading syscall definitions
 * from the Windows syscall table.
 */



use crate::platforms::Platform;
use crate::syscalls::common::{Syscall, SyscallCategory, register_syscall};

/// Load syscall definitions from the Windows syscall table
pub fn load_syscalls_from_table() {
    // In a real implementation, this would load syscall definitions from a file or database
    // For now, we'll just load some additional syscalls
    
    info!("Loading additional Windows syscall definitions");
    
    // Load additional syscalls
    load_additional_syscalls();
}

/// Load additional syscall definitions
fn load_additional_syscalls() {
    // Registry syscalls
    register_syscall(
        Syscall::new(
            0x002A,
            "NtEnumerateKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Enumerate subkeys of a registry key"),
    );
    
    register_syscall(
        Syscall::new(
            0x002B,
            "NtEnumerateValueKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Enumerate values of a registry key"),
    );
    
    register_syscall(
        Syscall::new(
            0x002C,
            "NtFlushKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Flush a registry key to disk"),
    );
    
    register_syscall(
        Syscall::new(
            0x002D,
            "NtNotifyChangeKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Monitor registry key changes"),
    );
    
    // Network syscalls
    register_syscall(
        Syscall::new(
            0x002E,
            "NtCreatePort",
            Platform::Windows,
            SyscallCategory::Network,
        )
        .with_description("Create a communication port"),
    );
    
    register_syscall(
        Syscall::new(
            0x002F,
            "NtConnectPort",
            Platform::Windows,
            SyscallCategory::Network,
        )
        .with_description("Connect to a communication port"),
    );
    
    register_syscall(
        Syscall::new(
            0x0030,
            "NtListenPort",
            Platform::Windows,
            SyscallCategory::Network,
        )
        .with_description("Listen on a communication port"),
    );
    
    register_syscall(
        Syscall::new(
            0x0031,
            "NtAcceptConnectPort",
            Platform::Windows,
            SyscallCategory::Network,
        )
        .with_description("Accept a connection on a communication port"),
    );
    
    register_syscall(
        Syscall::new(
            0x0032,
            "NtCompleteConnectPort",
            Platform::Windows,
            SyscallCategory::Network,
        )
        .with_description("Complete a connection to a communication port"),
    );
    
    // Process and thread syscalls
    register_syscall(
        Syscall::new(
            0x0033,
            "NtGetContextThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Get thread context"),
    );
    
    register_syscall(
        Syscall::new(
            0x0034,
            "NtSetContextThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Set thread context"),
    );
    
    register_syscall(
        Syscall::new(
            0x0035,
            "NtQueueApcThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Queue an APC to a thread"),
    );
    
    register_syscall(
        Syscall::new(
            0x0036,
            "NtYieldExecution",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Yield execution to another thread"),
    );
    
    register_syscall(
        Syscall::new(
            0x0037,
            "NtDelayExecution",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Delay thread execution"),
    );
    
    // Memory syscalls
    register_syscall(
        Syscall::new(
            0x0038,
            "NtLockVirtualMemory",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Lock virtual memory"),
    );
    
    register_syscall(
        Syscall::new(
            0x0039,
            "NtUnlockVirtualMemory",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Unlock virtual memory"),
    );
    
    register_syscall(
        Syscall::new(
            0x003A,
            "NtMapViewOfSection",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Map a view of a section"),
    );
    
    register_syscall(
        Syscall::new(
            0x003B,
            "NtUnmapViewOfSection",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Unmap a view of a section"),
    );
    
    register_syscall(
        Syscall::new(
            0x003C,
            "NtCreateSection",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Create a section object"),
    );
    
    // File system syscalls
    register_syscall(
        Syscall::new(
            0x003D,
            "NtQueryDirectoryFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Query directory entries"),
    );
    
    register_syscall(
        Syscall::new(
            0x003E,
            "NtQueryInformationFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Query file information"),
    );
    
    register_syscall(
        Syscall::new(
            0x003F,
            "NtSetInformationFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Set file information"),
    );
    
    register_syscall(
        Syscall::new(
            0x0040,
            "NtQueryVolumeInformationFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Query volume information"),
    );
    
    register_syscall(
        Syscall::new(
            0x0041,
            "NtCreateSymbolicLinkObject",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Create a symbolic link"),
    );
    
    // Security syscalls
    register_syscall(
        Syscall::new(
            0x0042,
            "NtDuplicateToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Duplicate a token"),
    );
    
    register_syscall(
        Syscall::new(
            0x0043,
            "NtQuerySecurityObject",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Query security information"),
    );
    
    register_syscall(
        Syscall::new(
            0x0044,
            "NtSetSecurityObject",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Set security information"),
    );
    
    register_syscall(
        Syscall::new(
            0x0045,
            "NtPrivilegeCheck",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Check token privileges"),
    );
    
    // Synchronization syscalls
    register_syscall(
        Syscall::new(
            0x0046,
            "NtCreateMutant",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Create a mutant object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0047,
            "NtOpenMutant",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Open a mutant object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0048,
            "NtReleaseMutant",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Release a mutant object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0049,
            "NtCreateEvent",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Create an event object"),
    );
    
    register_syscall(
        Syscall::new(
            0x004A,
            "NtOpenEvent",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Open an event object"),
    );
    
    register_syscall(
        Syscall::new(
            0x004B,
            "NtSetEvent",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Set an event object"),
    );
    
    register_syscall(
        Syscall::new(
            0x004C,
            "NtResetEvent",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Reset an event object"),
    );
    
    register_syscall(
        Syscall::new(
            0x004D,
            "NtCreateSemaphore",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Create a semaphore object"),
    );
    
    register_syscall(
        Syscall::new(
            0x004E,
            "NtOpenSemaphore",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Open a semaphore object"),
    );
    
    register_syscall(
        Syscall::new(
            0x004F,
            "NtReleaseSemaphore",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Release a semaphore object"),
    );
    
    // Add more syscalls as needed...
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::syscalls::common::mapping::{get_syscall, get_syscalls_by_category};
    
    #[test]
    fn test_load_additional_syscalls() {
        // Load additional syscalls
        load_additional_syscalls();
        
        // Check that some syscalls are registered
        let syscall = get_syscall(Platform::Windows, 0x002A); // NtEnumerateKey
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtEnumerateKey");
        
        let syscall = get_syscall(Platform::Windows, 0x0033); // NtGetContextThread
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtGetContextThread");
        
        let syscall = get_syscall(Platform::Windows, 0x003A); // NtMapViewOfSection
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtMapViewOfSection");
        
        let syscall = get_syscall(Platform::Windows, 0x0046); // NtCreateMutant
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtCreateMutant");
    }
    
    #[test]
    fn test_syscall_categories() {
        // Load additional syscalls
        load_additional_syscalls();
        
        // Check syscalls by category
        let registry_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Registry);
        assert!(!registry_syscalls.is_empty());
        
        let network_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Network);
        assert!(!network_syscalls.is_empty());
        
        let thread_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Thread);
        assert!(!thread_syscalls.is_empty());
        
        let memory_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Memory);
        assert!(!memory_syscalls.is_empty());
        
        let fs_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::FileSystem);
        assert!(!fs_syscalls.is_empty());
        
        let security_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Security);
        assert!(!security_syscalls.is_empty());
        
        let sync_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Synchronization);
        assert!(!sync_syscalls.is_empty());
    }
} 