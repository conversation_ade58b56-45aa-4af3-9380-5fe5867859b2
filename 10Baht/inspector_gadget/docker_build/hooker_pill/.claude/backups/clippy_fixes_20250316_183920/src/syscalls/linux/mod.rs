/*!
 * Linux Syscall Interception
 * 
 * This module provides functionality for intercepting and analyzing
 * Linux system calls using eBPF.
 */


use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Mutex;
use std::thread;

use std::collections::VecDeque;



use crate::platforms::Platform;
use crate::syscalls::common::{
    SyscallEvent, SyscallCategory, SyscallFilter, SyscallParameterValue,
    ParameterDirection, Syscall, init_syscall_registry, register_syscall,
};
use crate::syscalls::SyscallInterceptor;

pub mod ebpf;
mod syscall_table;

/// Linux-specific syscall error
#[derive(Debug, Error)]
pub enum LinuxSyscallError {
    /// eBPF initialization error
    #[error("eBPF initialization error: {0}")]
    EbpfInitError(String),
    
    /// Syscall interception error
    #[error("Syscall interception error: {0}")]
    InterceptionError(String),
    
    /// Unsupported operation
    #[error("Unsupported operation: {0}")]
    UnsupportedOperation(String),
}

/// Linux syscall interceptor
pub struct LinuxSyscallInterceptor {
    /// eBPF interceptor
    ebpf_interceptor: ebpf::EbpfInterceptor,
    /// Running flag
    running: Arc<AtomicBool>,
    /// Collected events (memory cache)
    events_cache: Arc<Mutex<VecDeque<SyscallEvent>>>,
    /// Current filter
    filter: SyscallFilter,
    /// Statistics
    statistics: Arc<Mutex<crate::syscalls::SyscallStatistics>>,
    /// Event callback
    event_callback: Option<Box<dyn Fn(&SyscallEvent) -> crate::error::Result<()> + Send + Sync>>,
    /// Background processing thread handle
    processing_thread: Option<thread::JoinHandle<()>>,
    /// Elasticsearch enabled flag
    elasticsearch_enabled: bool,
    /// Per-CPU Maps enabled flag
    percpu_maps_enabled: bool,
    /// Maximum cache size (default: 10000 events)
    max_cache_size: usize,
    /// Elasticsearch batch size (default: 100 events)
    elasticsearch_batch_size: usize,
    /// Elasticsearch flush interval in milliseconds (default: 1000ms)
    elasticsearch_flush_interval_ms: u64,
    /// Last Elasticsearch flush time
    last_flush_time: Arc<Mutex<std::time::Instant>>,
}

impl LinuxSyscallInterceptor {
    /// Create a new Linux syscall interceptor
    pub fn new() -> Result<Self, LinuxSyscallError> {
        // Initialize the syscall registry
        init_linux_syscalls();
        
        let running = Arc::new(AtomicBool::new(false));
        
        // Create the eBPF interceptor
        let ebpf_interceptor = ebpf::EbpfInterceptor::new(Arc::clone(&running))
            .map_err(|e| LinuxSyscallError::EbpfInitError(e.to_string()))?;
        
        // Create a new interceptor instance
        let mut interceptor = Self {
            ebpf_interceptor,
            running,
            events_cache: Arc::new(Mutex::new(VecDeque::with_capacity(10000))),
            filter: SyscallFilter::new(),
            statistics: Arc::new(Mutex::new(crate::syscalls::SyscallStatistics::new())),
            event_callback: None,
            processing_thread: None,
            elasticsearch_enabled: false,
            percpu_maps_enabled: false,
            max_cache_size: 10000,
            elasticsearch_batch_size: 100,
            elasticsearch_flush_interval_ms: 1000,
            last_flush_time: Arc::new(Mutex::new(std::time::Instant::now())),
        };
        
        // Set up a default event callback to update statistics
        interceptor.set_default_event_callback();
        
        Ok(interceptor)
    }
    
    /// Create a new Linux syscall interceptor with Elasticsearch integration
    pub fn new_with_elasticsearch() -> Result<Self, LinuxSyscallError> {
        let mut interceptor = Self::new()?;
        interceptor.elasticsearch_enabled = true;
        
        // Set up Elasticsearch integration if available
        #[cfg(feature = "elasticsearch")]
        {
            info!("Elasticsearch integration enabled");
            interceptor.enable_elasticsearch_logging()?;
        }
        
        #[cfg(not(feature = "elasticsearch"))]
        {
            warn!("Elasticsearch feature not enabled, falling back to memory cache");
        }
        
        Ok(interceptor)
    }
    
    /// Configure cache and Elasticsearch settings
    pub fn configure_logging(&mut self, max_cache_size: usize, batch_size: usize, flush_interval_ms: u64) {
        self.max_cache_size = max_cache_size;
        self.elasticsearch_batch_size = batch_size;
        self.elasticsearch_flush_interval_ms = flush_interval_ms;
        
        info!("Configured logging: cache_size={}, batch_size={}, flush_interval={}ms", 
              max_cache_size, batch_size, flush_interval_ms);
    }
    
    /// Enable Elasticsearch logging
    #[cfg(feature = "elasticsearch")]
    fn enable_elasticsearch_logging(&mut self) -> Result<(), LinuxSyscallError> {
        use crate::logging::ElasticsearchLogger;
        
        match ElasticsearchLogger::new(None) {
            Ok(logger) => {
                info!("Elasticsearch logger initialized");
                let logger = Arc::new(logger);
                
                // Create a background thread for flushing events to Elasticsearch
                let events_cache = Arc::clone(&self.events_cache);
                let running = Arc::clone(&self.running);
                let batch_size = self.elasticsearch_batch_size;
                let flush_interval = Duration::from_millis(self.elasticsearch_flush_interval_ms);
                let last_flush_time = Arc::clone(&self.last_flush_time);
                let logger_clone = logger.clone();
                
                let flush_thread = thread::spawn(move || {
                    info!("Elasticsearch flush thread started");
                    
                    while running.load(Ordering::SeqCst) {
                        // Check if it's time to flush
                        let should_flush = {
                            if let Ok(mut last_time) = last_flush_time.lock() {
                                let now = std::time::Instant::now();
                                let elapsed = now.duration_since(*last_time);
                                
                                if elapsed >= flush_interval {
                                    *last_time = now;
                                    true
                                } else {
                                    false
                                }
                            } else {
                                false
                            }
                        };
                        
                        // Check if we have enough events to flush
                        let events_to_flush = if should_flush {
                            if let Ok(mut cache) = events_cache.lock() {
                                let count = std::cmp::min(cache.len(), batch_size);
                                
                                if count > 0 {
                                    let mut events = Vec::with_capacity(count);
                                    for _ in 0..count {
                                        if let Some(event) = cache.pop_front() {
                                            events.push(event);
                                        }
                                    }
                                    Some(events)
                                } else {
                                    None
                                }
                            } else {
                                None
                            }
                        } else {
                            None
                        };
                        
                        // Flush events to Elasticsearch
                        if let Some(events) = events_to_flush {
                            #[cfg(feature = "tokio")]
                            {
                                let logger = logger_clone.clone();
                                let events_clone = events.clone();
                                
                                tokio::spawn(async move {
                                    for event in events_clone {
                                        if let Err(e) = logger.log(&event).await {
                                            error!("Failed to log event to Elasticsearch: {}", e);
                                        }
                                    }
                                });
                            }
                            
                            debug!("Flushed {} events to Elasticsearch", events.len());
                        }
                        
                        // Sleep for a short time
                        thread::sleep(Duration::from_millis(100));
                    }
                    
                    info!("Elasticsearch flush thread stopped");
                });
                
                self.processing_thread = Some(flush_thread);
                self.elasticsearch_enabled = true;
                
                Ok(())
            },
            Err(e) => {
                error!("Failed to initialize Elasticsearch logger: {}", e);
                Err(LinuxSyscallError::EbpfInitError(format!("Failed to initialize Elasticsearch logger: {}", e)))
            }
        }
    }
    
    /// Check if eBPF is available on this system
    pub fn is_ebpf_available() -> bool {
        ebpf::is_ebpf_available()
    }
    
    /// Update statistics for a syscall event
    fn update_statistics(&self, event: &SyscallEvent) {
        if let Ok(mut stats) = self.statistics.lock() {
            // Update total count
            stats.total_count += 1;
            
            // Update category counts
            let category_count = stats.category_counts.entry(event.category).or_insert(0);
            *category_count += 1;
            
            // Update syscall counts
            let syscall_count = stats.syscall_counts.entry(event.syscall_id).or_insert(0);
            *syscall_count += 1;
            
            // Update process counts
            let process_count = stats.process_counts.entry(event.process_id).or_insert(0);
            *process_count += 1;
            
            // Update failed count if applicable
            if event.return_value < 0 {
                stats.failed_count += 1;
            }
            
            // Update end time
            stats.end_time = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();
        }
    }
    
    /// Process events from the eBPF interceptor with batch processing
    fn process_events(&self) -> crate::error::Result<()> {
        // Collect events from the eBPF interceptor
        let events = self.ebpf_interceptor.collect_events()
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to collect events: {}", e)
                )
            ))?;
        
        if events.is_empty() {
            return Ok(());
        }
        
        // Filter events based on the current filter
        let filtered_events: Vec<SyscallEvent> = events.into_iter()
            .filter(|event| self.filter.matches(event))
            .collect();
        
        if filtered_events.is_empty() {
            return Ok(());
        }
        
        // Process each event
        for event in &filtered_events {
            // Update statistics
            self.update_statistics(event);
            
            // Add to memory cache
            if let Ok(mut cache) = self.events_cache.lock() {
                cache.push_back(event.clone());
                
                // Trim cache if it exceeds the maximum size
                // We don't want to lose events, but we need to prevent memory issues
                while cache.len() > self.max_cache_size {
                    cache.pop_front();
                    warn!("Event cache overflow, oldest event dropped");
                }
            }
            
            // Call the event callback if set
            if let Some(callback) = &self.event_callback {
                if let Err(e) = callback(event) {
                    error!("Event callback error: {}", e);
                }
            }
        }
        
        // Force flush to Elasticsearch if we have enough events
        #[cfg(feature = "elasticsearch")]
        if self.elasticsearch_enabled {
            if let Ok(cache) = self.events_cache.lock() {
                if cache.len() >= self.elasticsearch_batch_size {
                    if let Ok(mut last_time) = self.last_flush_time.lock() {
                        *last_time = std::time::Instant::now() - Duration::from_millis(self.elasticsearch_flush_interval_ms);
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// Start a background thread for processing events
    fn start_background_processing(&mut self) -> crate::error::Result<()> {
        if self.processing_thread.is_some() {
            return Ok(());
        }
        
        let running = Arc::clone(&self.running);
        let interceptor = Arc::new(self.clone());
        
        // Create a thread for processing events
        let thread = thread::spawn(move || {
            info!("Background event processing thread started");
            
            while running.load(Ordering::SeqCst) {
                // Process events
                if let Err(e) = interceptor.process_events() {
                    error!("Error processing events: {}", e);
                }
                
                // Sleep for a short time
                thread::sleep(Duration::from_millis(100));
            }
            
            info!("Background event processing thread stopped");
        });
        
        self.processing_thread = Some(thread);
        Ok(())
    }
    
    /// Set up a default event callback to update statistics
    fn set_default_event_callback(&mut self) {
        let statistics = Arc::clone(&self.statistics);
        
        // Create a callback that updates statistics for each event
        let callback = Box::new(move |event: &SyscallEvent| -> crate::error::Result<()> {
            if let Ok(mut stats) = statistics.lock() {
                stats.update(event);
            }
            Ok(())
        });
        
        // Set the callback
        self.event_callback = Some(callback);
    }
    
    /// Clear the events buffer
    pub fn clear_events(&self) -> crate::error::Result<()> {
        if let Ok(mut events) = self.events_cache.lock() {
            events.clear();
            Ok(())
        } else {
            Err(crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    "Failed to lock events cache mutex".to_string()
                )
            ))
        }
    }
    
    /// Clone the interceptor
    fn clone(&self) -> Self {
        Self {
            ebpf_interceptor: self.ebpf_interceptor.clone(),
            running: Arc::clone(&self.running),
            events_cache: Arc::clone(&self.events_cache),
            filter: self.filter.clone(),
            statistics: Arc::clone(&self.statistics),
            event_callback: None, // Can't clone the callback
            processing_thread: None,
            elasticsearch_enabled: self.elasticsearch_enabled,
            percpu_maps_enabled: self.percpu_maps_enabled,
            max_cache_size: self.max_cache_size,
            elasticsearch_batch_size: self.elasticsearch_batch_size,
            elasticsearch_flush_interval_ms: self.elasticsearch_flush_interval_ms,
            last_flush_time: Arc::clone(&self.last_flush_time),
        }
    }
    
    /// Configure XDP hooker integration
    #[cfg(feature = "xdp")]
    pub fn configure_xdp_integration(&mut self, interface: &str) -> crate::error::Result<()> {
        use crate::hookers::XdpPerCpuHooker;
        
        info!("Configuring XDP integration for interface: {}", interface);
        
        // Create a new XDP hooker
        let xdp_hooker = XdpPerCpuHooker::new(interface)
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to create XDP hooker: {}", e)
                )
            ))?;
        
        // Start intercepting network packets
        xdp_hooker.start_intercepting()
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to start XDP intercepting: {}", e)
                )
            ))?;
        
        // Create a callback that processes XDP events
        let statistics = Arc::clone(&self.statistics);
        let events = Arc::clone(&self.events_cache);
        let callback = Box::new(move |event: &crate::hookers::XdpEvent| -> crate::error::Result<()> {
            // Convert XDP event to syscall event
            let syscall_event = SyscallEvent::new(
                event.id,
                event.timestamp,
                event.process_id,
                event.thread_id,
                0, // No syscall ID for XDP events
                "xdp_packet",
                SyscallCategory::Network,
            );
            
            // Add parameters
            syscall_event.add_parameter(
                "action",
                SyscallParameterValue::String(event.action.to_string()),
                ParameterDirection::In,
            );
            
            syscall_event.add_parameter(
                "interface",
                SyscallParameterValue::String(event.interface.clone()),
                ParameterDirection::In,
            );
            
            // Store the event
            if let Ok(mut cache) = events.lock() {
                cache.push_back(syscall_event.clone());
            }
            
            // Update statistics
            if let Ok(mut stats) = statistics.lock() {
                stats.update(&syscall_event);
            }
            
            Ok(())
        });
        
        // Set the callback on the XDP hooker
        xdp_hooker.set_event_callback(callback)
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to set XDP event callback: {}", e)
                )
            ))?;
        
        info!("XDP integration configured successfully");
        Ok(())
    }
    
    /// Configure tracepoint hooker integration
    #[cfg(feature = "tracepoint")]
    pub fn configure_tracepoint_integration(&mut self, category: &str, name: &str) -> crate::error::Result<()> {
        use crate::hookers::TracepointPerCpuHooker;
        
        info!("Configuring tracepoint integration for {}/{}", category, name);
        
        // Create a new tracepoint hooker
        let tracepoint_hooker = TracepointPerCpuHooker::new(category, name)
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to create tracepoint hooker: {}", e)
                )
            ))?;
        
        // Start intercepting tracepoint events
        tracepoint_hooker.start_intercepting()
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to start tracepoint intercepting: {}", e)
                )
            ))?;
        
        // Create a callback that processes tracepoint events
        let statistics = Arc::clone(&self.statistics);
        let events = Arc::clone(&self.events_cache);
        let callback = Box::new(move |event: &crate::hookers::TracepointEvent| -> crate::error::Result<()> {
            // Convert tracepoint event to syscall event
            let syscall_event = SyscallEvent::new(
                event.id,
                event.timestamp,
                event.process_id,
                event.thread_id,
                0, // No syscall ID for tracepoint events
                &format!("tracepoint_{}/{}", event.category, event.name),
                SyscallCategory::Other,
            );
            
            // Add parameters
            for (key, value) in &event.data {
                syscall_event.add_parameter(
                    key,
                    SyscallParameterValue::String(value.clone()),
                    ParameterDirection::In,
                );
            }
            
            // Store the event
            if let Ok(mut cache) = events.lock() {
                cache.push_back(syscall_event.clone());
            }
            
            // Update statistics
            if let Ok(mut stats) = statistics.lock() {
                stats.update(&syscall_event);
            }
            
            Ok(())
        });
        
        // Set the callback on the tracepoint hooker
        tracepoint_hooker.set_event_callback(callback)
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to set tracepoint event callback: {}", e)
                )
            ))?;
        
        info!("Tracepoint integration configured successfully");
        Ok(())
    }
    
    /// Enable Per-CPU Maps for better performance
    #[cfg(feature = "percpu_maps")]
    pub fn enable_percpu_maps(&mut self) -> crate::error::Result<()> {
        use crate::maps::TypedOptimizedPerCpuMap;
        
        info!("Enabling Per-CPU Maps for syscall interception");
        
        // Check if Per-CPU Maps are already enabled
        if self.percpu_maps_enabled {
            debug!("Per-CPU Maps are already enabled");
            return Ok(());
        }
        
        // Create a new Per-CPU Map for syscall statistics
        let map_name = format!("syscall_stats_{}", std::process::id());
        let map = TypedOptimizedPerCpuMap::<crate::syscalls::SyscallStatistics>::new(
            &map_name,
            crate::maps::MapFlags::default(),
        ).map_err(|e| crate::error::Error::Platform(
            crate::error::PlatformError::Linux(
                format!("Failed to create Per-CPU Map: {}", e)
            )
        ))?;
        
        // Initialize the map with default statistics
        let default_stats = crate::syscalls::SyscallStatistics::new();
        map.update_all_cpus(&default_stats).map_err(|e| crate::error::Error::Platform(
            crate::error::PlatformError::Linux(
                format!("Failed to initialize Per-CPU Map: {}", e)
            )
        ))?;
        
        // Create a callback that updates the Per-CPU Map
        let map_clone = map.clone();
        let callback = Box::new(move |event: &SyscallEvent| -> crate::error::Result<()> {
            // Get the current CPU ID
            let cpu_id = unsafe { libc::sched_getcpu() };
            if cpu_id < 0 {
                return Err(crate::error::Error::Platform(
                    crate::error::PlatformError::Linux(
                        "Failed to get current CPU ID".to_string()
                    )
                ));
            }
            
            // Update the statistics for the current CPU
            map_clone.update_cpu(cpu_id as u32, |stats| {
                // Update total count
                stats.total_count += 1;
                
                // Update category counts
                let category_count = stats.category_counts.entry(event.category).or_insert(0);
                *category_count += 1;
                
                // Update syscall counts
                let syscall_count = stats.syscall_counts.entry(event.syscall_id).or_insert(0);
                *syscall_count += 1;
                
                // Update process counts
                let process_count = stats.process_counts.entry(event.process_id).or_insert(0);
                *process_count += 1;
                
                // Update failed count if applicable
                if event.return_value < 0 {
                    stats.failed_count += 1;
                }
                
                // Update end time
                stats.end_time = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs();
            }).map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to update Per-CPU Map: {}", e)
                )
            ))?;
            
            Ok(())
        });
        
        // Set the callback
        self.event_callback = Some(callback);
        self.percpu_maps_enabled = true;
        
        info!("Per-CPU Maps enabled successfully");
        Ok(())
    }
    
    /// Get aggregated statistics from Per-CPU Maps
    #[cfg(feature = "percpu_maps")]
    pub fn get_percpu_statistics(&self) -> crate::error::Result<crate::syscalls::SyscallStatistics> {
        use crate::maps::TypedOptimizedPerCpuMap;
        
        // Check if Per-CPU Maps are enabled
        if !self.percpu_maps_enabled {
            return self.get_statistics();
        }
        
        // Get the Per-CPU Map
        let map_name = format!("syscall_stats_{}", std::process::id());
        let map = TypedOptimizedPerCpuMap::<crate::syscalls::SyscallStatistics>::open(
            &map_name,
            crate::maps::MapFlags::default(),
        ).map_err(|e| crate::error::Error::Platform(
            crate::error::PlatformError::Linux(
                format!("Failed to open Per-CPU Map: {}", e)
            )
        ))?;
        
        // Get statistics from all CPUs
        let cpu_stats = map.lookup_all_cpus().map_err(|e| crate::error::Error::Platform(
            crate::error::PlatformError::Linux(
                format!("Failed to lookup Per-CPU Map: {}", e)
            )
        ))?;
        
        // Aggregate statistics
        let mut aggregated_stats = crate::syscalls::SyscallStatistics::new();
        
        for (cpu_id, stats) in cpu_stats {
            // Update total count
            aggregated_stats.total_count += stats.total_count;
            
            // Update failed count
            aggregated_stats.failed_count += stats.failed_count;
            
            // Update category counts
            for (category, count) in &stats.category_counts {
                let category_count = aggregated_stats.category_counts.entry(*category).or_insert(0);
                *category_count += count;
            }
            
            // Update syscall counts
            for (syscall_id, count) in &stats.syscall_counts {
                let syscall_count = aggregated_stats.syscall_counts.entry(*syscall_id).or_insert(0);
                *syscall_count += count;
            }
            
            // Update process counts
            for (process_id, count) in &stats.process_counts {
                let process_count = aggregated_stats.process_counts.entry(*process_id).or_insert(0);
                *process_count += count;
            }
            
            // Update end time (use the latest)
            if stats.end_time > aggregated_stats.end_time {
                aggregated_stats.end_time = stats.end_time;
            }
        }
        
        Ok(aggregated_stats)
    }
    
    /// Enable Per-CPU Maps with Elasticsearch integration
    #[cfg(all(feature = "percpu_maps", feature = "elasticsearch"))]
    pub fn enable_percpu_maps_with_elasticsearch(&mut self) -> crate::error::Result<()> {
        use crate::maps::TypedOptimizedPerCpuMap;
        use crate::logging::ElasticsearchLogger;
        
        info!("Enabling Per-CPU Maps with Elasticsearch integration");
        
        // Check if Per-CPU Maps are already enabled
        if self.percpu_maps_enabled {
            debug!("Per-CPU Maps are already enabled");
            return Ok(());
        }
        
        // Initialize Elasticsearch logger
        let logger = ElasticsearchLogger::new(None)
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to initialize Elasticsearch logger: {}", e)
                )
            ))?;
        
        let logger = Arc::new(logger);
        
        // Create a new Per-CPU Map for syscall events
        let map_name = format!("syscall_events_{}", std::process::id());
        let events_map = TypedOptimizedPerCpuMap::<Vec<SyscallEvent>>::new(
            &map_name,
            crate::maps::MapFlags::default(),
        ).map_err(|e| crate::error::Error::Platform(
            crate::error::PlatformError::Linux(
                format!("Failed to create Per-CPU Map: {}", e)
            )
        ))?;
        
        // Initialize the map with empty vectors
        events_map.update_all_cpus(&Vec::new()).map_err(|e| crate::error::Error::Platform(
            crate::error::PlatformError::Linux(
                format!("Failed to initialize Per-CPU Map: {}", e)
            )
        ))?;
        
        // Create a background thread for collecting and flushing events
        let running = Arc::clone(&self.running);
        let events_map_clone = events_map.clone();
        let logger_clone = logger.clone();
        let batch_size = self.elasticsearch_batch_size;
        let flush_interval = Duration::from_millis(self.elasticsearch_flush_interval_ms);
        let last_flush_time = Arc::clone(&self.last_flush_time);
        
        let flush_thread = thread::spawn(move || {
            info!("Per-CPU Maps Elasticsearch flush thread started");
            
            while running.load(Ordering::SeqCst) {
                // Check if it's time to flush
                let should_flush = {
                    if let Ok(mut last_time) = last_flush_time.lock() {
                        let now = std::time::Instant::now();
                        let elapsed = now.duration_since(*last_time);
                        
                        if elapsed >= flush_interval {
                            *last_time = now;
                            true
                        } else {
                            false
                        }
                    } else {
                        false
                    }
                };
                
                if should_flush {
                    // Collect events from all CPUs
                    if let Ok(cpu_events) = events_map_clone.lookup_all_cpus() {
                        let mut all_events = Vec::new();
                        
                        // Gather events from all CPUs
                        for (cpu_id, events) in cpu_events {
                            if !events.is_empty() {
                                debug!("Collected {} events from CPU {}", events.len(), cpu_id);
                                all_events.extend(events);
                                
                                // Clear the events for this CPU
                                if let Err(e) = events_map_clone.update_cpu(cpu_id, |_| Vec::new()) {
                                    error!("Failed to clear events for CPU {}: {}", cpu_id, e);
                                }
                            }
                        }
                        
                        // Process events in batches
                        if !all_events.is_empty() {
                            let total_events = all_events.len();
                            let mut processed = 0;
                            
                            while processed < total_events {
                                let end = std::cmp::min(processed + batch_size, total_events);
                                let batch = &all_events[processed..end];
                                
                                // Send batch to Elasticsearch
                                #[cfg(feature = "tokio")]
                                {
                                    let logger = logger_clone.clone();
                                    let batch_clone: Vec<SyscallEvent> = batch.to_vec();
                                    
                                    tokio::spawn(async move {
                                        for event in batch_clone {
                                            if let Err(e) = logger.log(&event).await {
                                                error!("Failed to log event to Elasticsearch: {}", e);
                                            }
                                        }
                                    });
                                }
                                
                                processed = end;
                            }
                            
                            info!("Flushed {} events to Elasticsearch", total_events);
                        }
                    }
                }
                
                // Sleep for a short time
                thread::sleep(Duration::from_millis(100));
            }
            
            info!("Per-CPU Maps Elasticsearch flush thread stopped");
        });
        
        self.processing_thread = Some(flush_thread);
        self.elasticsearch_enabled = true;
        self.percpu_maps_enabled = true;
        
        // Create a callback that adds events to the Per-CPU Map
        let events_map_clone = events_map.clone();
        let callback = Box::new(move |event: &SyscallEvent| -> crate::error::Result<()> {
            // Get the current CPU ID
            let cpu_id = unsafe { libc::sched_getcpu() };
            if cpu_id < 0 {
                return Err(crate::error::Error::Platform(
                    crate::error::PlatformError::Linux(
                        "Failed to get current CPU ID".to_string()
                    )
                ));
            }
            
            // Add the event to the Per-CPU Map
            events_map_clone.update_cpu(cpu_id as u32, |events| {
                events.push(event.clone());
            }).map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to update Per-CPU Map: {}", e)
                )
            ))?;
            
            Ok(())
        });
        
        // Set the callback
        self.event_callback = Some(callback);
        
        info!("Per-CPU Maps with Elasticsearch integration enabled successfully");
        Ok(())
    }
}

impl SyscallInterceptor for LinuxSyscallInterceptor {
    fn attach_to_process(&mut self, pid: u32) -> crate::error::Result<()> {
        if self.running.load(Ordering::SeqCst) {
            warn!("Syscall interceptor is already running");
            return Ok(());
        }
        
        info!("Attaching to process with PID: {}", pid);
        
        // Initialize the eBPF interceptor
        self.ebpf_interceptor.start()
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to start eBPF interceptor: {}", e)
                )
            ))?;
        
        // Set the running flag
        self.running.store(true, Ordering::SeqCst);
        
        // Start background processing
        self.start_background_processing()?;
        
        Ok(())
    }
    
    fn trace_binary(&mut self, path: &std::path::Path) -> crate::error::Result<()> {
        if self.running.load(Ordering::SeqCst) {
            warn!("Syscall interceptor is already running");
            return Ok(());
        }
        
        info!("Tracing binary: {}", path.display());
        
        // Check if the binary exists
        if !path.exists() {
            return Err(crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Binary not found: {}", path.display())
                )
            ));
        }
        
        // Initialize the eBPF interceptor
        self.ebpf_interceptor.start()
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to start eBPF interceptor: {}", e)
                )
            ))?;
        
        // Set the running flag
        self.running.store(true, Ordering::SeqCst);
        
        // Start background processing
        self.start_background_processing()?;
        
        Ok(())
    }
    
    fn apply_filter(&mut self, filter: SyscallFilter) -> crate::error::Result<()> {
        debug!("Applying syscall filter: {:?}", filter);
        self.filter = filter;
        Ok(())
    }
    
    fn collect_events(&self) -> crate::error::Result<Vec<SyscallEvent>> {
        if !self.running.load(Ordering::SeqCst) {
            warn!("Syscall interceptor is not running");
            return Ok(Vec::new());
        }
        
        // Return a copy of the events from the cache
        if let Ok(mut cache) = self.events_cache.lock() {
            let events: Vec<SyscallEvent> = cache.iter().cloned().collect();
            
            // Don't clear the cache - Elasticsearch flush thread will handle that
            // This ensures no events are lost if Elasticsearch is temporarily unavailable
            
            Ok(events)
        } else {
            // If we can't lock the events mutex, return an empty vector
            warn!("Failed to lock events cache mutex");
            Ok(Vec::new())
        }
    }
    
    fn stop(&mut self) -> crate::error::Result<()> {
        if !self.running.load(Ordering::SeqCst) {
            return Ok(());
        }
        
        info!("Stopping syscall interceptor");
        
        // Stop the eBPF interceptor
        self.ebpf_interceptor.stop()
            .map_err(|e| crate::error::Error::Platform(
                crate::error::PlatformError::Linux(
                    format!("Failed to stop eBPF interceptor: {}", e)
                )
            ))?;
        
        // Set the running flag
        self.running.store(false, Ordering::SeqCst);
        
        // Wait for the processing thread to finish
        if let Some(thread) = self.processing_thread.take() {
            if let Err(e) = thread.join() {
                error!("Failed to join processing thread: {:?}", e);
            }
        }
        
        // Final flush of events to Elasticsearch
        #[cfg(feature = "elasticsearch")]
        if self.elasticsearch_enabled {
            info!("Performing final flush of events to Elasticsearch");
            
            use crate::logging::ElasticsearchLogger;
            
            if let Ok(logger) = ElasticsearchLogger::new(None) {
                if let Ok(cache) = self.events_cache.lock() {
                    let events: Vec<SyscallEvent> = cache.iter().cloned().collect();
                    
                    #[cfg(feature = "tokio")]
                    {
                        let logger = Arc::new(logger);
                        
                        // Create a new runtime for the final flush
                        let rt = tokio::runtime::Runtime::new().unwrap();
                        rt.block_on(async {
                            for event in events {
                                if let Err(e) = logger.log(&event).await {
                                    error!("Failed to log event to Elasticsearch during final flush: {}", e);
                                }
                            }
                        });
                    }
                    
                    info!("Final flush completed: {} events", events.len());
                }
            }
        }
        
        Ok(())
    }
    
    fn get_statistics(&self) -> crate::error::Result<crate::syscalls::SyscallStatistics> {
        // If Per-CPU Maps are enabled, use them for statistics
        #[cfg(feature = "percpu_maps")]
        if self.percpu_maps_enabled {
            return self.get_percpu_statistics();
        }
        
        // Otherwise, return a copy of the current statistics
        if let Ok(stats) = self.statistics.lock() {
            Ok(stats.clone())
        } else {
            // If we can't lock the statistics mutex, return a new statistics object
            warn!("Failed to lock statistics mutex");
            Ok(crate::syscalls::SyscallStatistics::new())
        }
    }
    
    fn set_event_callback(&mut self, callback: Box<dyn Fn(&SyscallEvent) -> crate::error::Result<()> + Send + Sync>) -> crate::error::Result<()> {
        debug!("Setting event callback");
        self.event_callback = Some(callback);
        Ok(())
    }
}

/// Initialize Linux syscall definitions
pub fn init_linux_syscalls() {
    // Register common Linux syscalls
    
    // File system syscalls
    register_syscall(
        Syscall::new(
            0,
            "read",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Read from a file descriptor"),
    );
    
    register_syscall(
        Syscall::new(
            1,
            "write",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Write to a file descriptor"),
    );
    
    register_syscall(
        Syscall::new(
            2,
            "open",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Open a file or device"),
    );
    
    register_syscall(
        Syscall::new(
            3,
            "close",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Close a file descriptor"),
    );
    
    register_syscall(
        Syscall::new(
            4,
            "stat",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Get file status"),
    );
    
    register_syscall(
        Syscall::new(
            5,
            "fstat",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Get file status by file descriptor"),
    );
    
    register_syscall(
        Syscall::new(
            6,
            "lstat",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Get file status, don't follow symlinks"),
    );
    
    register_syscall(
        Syscall::new(
            8,
            "lseek",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Reposition read/write file offset"),
    );
    
    register_syscall(
        Syscall::new(
            9,
            "mmap",
            Platform::Linux,
            SyscallCategory::Memory,
        )
        .with_description("Map files or devices into memory"),
    );
    
    // Process syscalls
    register_syscall(
        Syscall::new(
            56,
            "clone",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Create a child process"),
    );
    
    register_syscall(
        Syscall::new(
            57,
            "fork",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Create a child process"),
    );
    
    register_syscall(
        Syscall::new(
            58,
            "vfork",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Create a child process and block parent"),
    );
    
    register_syscall(
        Syscall::new(
            59,
            "execve",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Execute program"),
    );
    
    register_syscall(
        Syscall::new(
            60,
            "exit",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Terminate the calling process"),
    );
    
    register_syscall(
        Syscall::new(
            61,
            "wait4",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Wait for process to change state"),
    );
    
    register_syscall(
        Syscall::new(
            62,
            "kill",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Send signal to a process"),
    );
    
    // Memory syscalls
    register_syscall(
        Syscall::new(
            10,
            "mprotect",
            Platform::Linux,
            SyscallCategory::Memory,
        )
        .with_description("Set protection on a region of memory"),
    );
    
    register_syscall(
        Syscall::new(
            11,
            "munmap",
            Platform::Linux,
            SyscallCategory::Memory,
        )
        .with_description("Unmap pages of memory"),
    );
    
    register_syscall(
        Syscall::new(
            12,
            "brk",
            Platform::Linux,
            SyscallCategory::Memory,
        )
        .with_description("Change data segment size"),
    );
    
    // Network syscalls
    register_syscall(
        Syscall::new(
            41,
            "socket",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Create an endpoint for communication"),
    );
    
    register_syscall(
        Syscall::new(
            42,
            "connect",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Initiate a connection on a socket"),
    );
    
    register_syscall(
        Syscall::new(
            43,
            "accept",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Accept a connection on a socket"),
    );
    
    register_syscall(
        Syscall::new(
            44,
            "sendto",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Send a message on a socket"),
    );
    
    register_syscall(
        Syscall::new(
            45,
            "recvfrom",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Receive a message from a socket"),
    );
    
    register_syscall(
        Syscall::new(
            46,
            "sendmsg",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Send a message on a socket"),
    );
    
    register_syscall(
        Syscall::new(
            47,
            "recvmsg",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Receive a message from a socket"),
    );
    
    register_syscall(
        Syscall::new(
            48,
            "shutdown",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Shut down part of a full-duplex connection"),
    );
    
    register_syscall(
        Syscall::new(
            49,
            "bind",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Bind a name to a socket"),
    );
    
    register_syscall(
        Syscall::new(
            50,
            "listen",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Listen for connections on a socket"),
    );
    
    register_syscall(
        Syscall::new(
            51,
            "getsockname",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Get socket name"),
    );
    
    register_syscall(
        Syscall::new(
            52,
            "getpeername",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Get name of connected peer socket"),
    );
    
    register_syscall(
        Syscall::new(
            53,
            "socketpair",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Create a pair of connected sockets"),
    );
    
    register_syscall(
        Syscall::new(
            54,
            "setsockopt",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Set options on sockets"),
    );
    
    register_syscall(
        Syscall::new(
            55,
            "getsockopt",
            Platform::Linux,
            SyscallCategory::Network,
        )
        .with_description("Get options on sockets"),
    );
    
    // Security syscalls
    register_syscall(
        Syscall::new(
            105,
            "setuid",
            Platform::Linux,
            SyscallCategory::Security,
        )
        .with_description("Set user identity"),
    );
    
    register_syscall(
        Syscall::new(
            106,
            "setgid",
            Platform::Linux,
            SyscallCategory::Security,
        )
        .with_description("Set group identity"),
    );
    
    register_syscall(
        Syscall::new(
            107,
            "geteuid",
            Platform::Linux,
            SyscallCategory::Security,
        )
        .with_description("Get user identity"),
    );
    
    register_syscall(
        Syscall::new(
            108,
            "getegid",
            Platform::Linux,
            SyscallCategory::Security,
        )
        .with_description("Get group identity"),
    );
    
    register_syscall(
        Syscall::new(
            113,
            "setreuid",
            Platform::Linux,
            SyscallCategory::Security,
        )
        .with_description("Set real and/or effective user ID"),
    );
    
    register_syscall(
        Syscall::new(
            114,
            "setregid",
            Platform::Linux,
            SyscallCategory::Security,
        )
        .with_description("Set real and/or effective group ID"),
    );
    
    // Time syscalls
    register_syscall(
        Syscall::new(
            228,
            "clock_gettime",
            Platform::Linux,
            SyscallCategory::Time,
        )
        .with_description("Get time from specified clock"),
    );
    
    register_syscall(
        Syscall::new(
            229,
            "clock_getres",
            Platform::Linux,
            SyscallCategory::Time,
        )
        .with_description("Get resolution of specified clock"),
    );
    
    register_syscall(
        Syscall::new(
            230,
            "clock_nanosleep",
            Platform::Linux,
            SyscallCategory::Time,
        )
        .with_description("High-resolution sleep with specifiable clock"),
    );
    
    // Load additional syscalls from the syscall table
    syscall_table::load_syscalls_from_table();
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    #[cfg(target_os = "linux")]
    fn test_linux_syscall_registry() {
        // Initialize the registry
        init_linux_syscalls();
        
        // Check that some syscalls are registered
        let syscall = crate::syscalls::common::mapping::get_syscall(
            Platform::Linux,
            0, // read
        );
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "read");
        
        let syscall = crate::syscalls::common::mapping::get_syscall(
            Platform::Linux,
            9, // mmap
        );
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "mmap");
        
        let syscall = crate::syscalls::common::mapping::get_syscall(
            Platform::Linux,
            59, // execve
        );
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "execve");
    }
    
    #[test]
    #[cfg(target_os = "linux")]
    fn test_ebpf_availability() {
        // Check if eBPF is available
        let ebpf_available = LinuxSyscallInterceptor::is_ebpf_available();
        
        // This is just a basic test to ensure the function runs
        // The actual result will depend on the system
        println!("eBPF available: {}", ebpf_available);
    }
} 