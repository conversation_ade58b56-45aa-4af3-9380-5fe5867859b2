use clap::{Parser, Subcommand};
use inspector_gadget::{Platform, Result, TraceEvent, BinaryFormat, BinaryInfo};
use std::path::PathBuf;

use tokio::time;

/// Inspector Gadget - Cross-platform Binary Analysis Framework
#[derive(Parser)]
#[clap(author, version, about)]
struct Cli {
    /// Subcommand to execute
    #[clap(subcommand)]
    command: Commands,
}

/// Available commands
#[derive(Subcommand)]
enum Commands {
    /// Show information about the current platform
    Info,
    
    /// Analyze a binary file without executing it
    Analyze {
        /// Path to the binary to analyze
        #[clap(index = 1)]
        binary: PathBuf,
        
        /// Perform security checks on the binary
        #[clap(short, long)]
        security: bool,
        
        /// Output file to save results (JSON format)
        #[clap(short, long)]
        output: Option<PathBuf>,
    },
    
    /// Trace a binary and analyze its behavior
    Trace {
        /// Path to the binary to trace
        #[clap(index = 1)]
        binary: PathBuf,
        
        /// Arguments to pass to the binary
        #[clap(index = 2, num_args = 1..)]
        args: Vec<String>,
        
        /// Duration to trace (in seconds)
        #[clap(short, long, default_value = "30")]
        duration: u64,
        
        /// Output file to save results (JSON format)
        #[clap(short, long)]
        output: Option<PathBuf>,
    },
    
    /// Attach to a running process
    Attach {
        /// Process ID to attach to
        #[clap(index = 1)]
        pid: u32,
        
        /// Duration to trace (in seconds)
        #[clap(short, long, default_value = "30")]
        duration: u64,
        
        /// Output file to save results (JSON format)
        #[clap(short, long)]
        output: Option<PathBuf>,
    },
    
    /// Log all syscalls from a process or command
    LogSyscalls {
        /// Process ID to monitor (if not specified, a command will be executed)
        #[clap(index = 1)]
        pid_or_command: String,
        
        /// Arguments to pass to the command (if pid_or_command is a command)
        #[clap(index = 2, num_args = 1..)]
        args: Vec<String>,
        
        /// Log file to save syscall logs (default: syscalls.log)
        #[clap(short, long)]
        log_file: Option<PathBuf>,
        
        /// Log to stdout
        #[clap(short, long, default_value = "true")]
        stdout: bool,
        
        /// Log to stderr
        #[clap(short, long, default_value = "false")]
        stderr: bool,
        
        /// Log to Elasticsearch
        #[clap(long, default_value = "false")]
        elasticsearch: bool,
    },
    
    /// Start Elasticsearch container for logging
    StartElasticsearch {
        /// Port for Elasticsearch (default: 9200)
        #[clap(long, default_value = "9200")]
        es_port: u16,
        
        /// Port for Kibana (default: 5601)
        #[clap(long, default_value = "5601")]
        kibana_port: u16,
    },
    
    /// Stop Elasticsearch container
    StopElasticsearch,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    if std::env::var("RUST_LOG").is_err() {
        std::env::set_var("RUST_LOG", "info");
    }
    env_logger::init();
    
    let cli = Cli::parse();
    
    match cli.command {
        Commands::Info => {
            show_platform_info().await?;
        }
        Commands::Analyze { binary, security, output } => {
            analyze_binary(binary, security, output).await?;
        }
        Commands::Trace { binary, args, duration, output } => {
            trace_binary(binary, args, duration, output).await?;
        }
        Commands::Attach { pid, duration, output } => {
            attach_to_process(pid, duration, output).await?;
        }
        Commands::LogSyscalls { pid_or_command, args, log_file, stdout, stderr, elasticsearch } => {
            handle_log_syscalls(pid_or_command, args, log_file, stdout, stderr, elasticsearch).await?;
        }
        Commands::StartElasticsearch { es_port, kibana_port } => {
            start_elasticsearch(es_port, kibana_port).await?;
        }
        Commands::StopElasticsearch => {
            stop_elasticsearch().await?;
        }
    }
    
    Ok(())
}

/// Display information about the current platform
async fn show_platform_info() -> Result<()> {
    use inspector_gadget::platforms;
    
    let platform = platforms::detect_platform();
    println!("Detected platform: {:?}", platform);
    
    match platform {
        Platform::Windows => {
            println!("Windows-specific information:");
            println!("  eBPF for Windows available: {}", platforms::windows::is_ebpf_available());
        }
        Platform::Linux => {
            println!("Linux-specific information:");
            println!("  eBPF available: {}", platforms::linux::is_ebpf_available());
        }
        Platform::Unsupported => {
            println!("This platform is not supported by Inspector Gadget.");
        }
    }
    
    if let Ok(os_info) = platforms::get_os_info() {
        println!("OS Version: {}", os_info.version);
        println!("Architecture: {}", os_info.architecture);
    }
    
    Ok(())
}

/// Analyze a binary file without executing it
async fn analyze_binary(binary: PathBuf, security: bool, output: Option<PathBuf>) -> Result<()> {
    use inspector_gadget::binary;
    
    println!("Analyzing binary: {:?}", binary);
    
    // Load the binary
    let binary_file = binary::load_binary(&binary)?;
    
    // Get basic information
    let info = binary_file.info();
    
    // Print basic information
    println!("Binary format: {:?}", info.format);
    println!("Architecture: {}", info.architecture);
    println!("64-bit: {}", info.is_64bit);
    println!("Executable: {}", info.is_executable);
    println!("Library: {}", info.is_library);
    println!("Entry point: 0x{:x}", info.entry_point);
    
    // Get sections
    let sections = binary_file.sections();
    println!("\nSections ({}):", sections.len());
    for section in &sections {
        println!("  {} - Size: {} bytes, Address: 0x{:x}, Executable: {}, Writable: {}",
            section.name, section.size, section.address, section.is_executable, section.is_writable);
    }
    
    // Get imports and exports
    let imports = binary_file.imports();
    let exports = binary_file.exports();
    
    println!("\nImports ({}):", imports.len());
    for (i, import) in imports.iter().enumerate().take(10) {
        println!("  {}", import);
    }
    if imports.len() > 10 {
        println!("  ... and {} more", imports.len() - 10);
    }
    
    println!("\nExports ({}):", exports.len());
    for (i, export) in exports.iter().enumerate().take(10) {
        println!("  {}", export);
    }
    if exports.len() > 10 {
        println!("  ... and {} more", exports.len() - 10);
    }
    
    // Extract metadata
    let metadata = binary::analysis::extract_metadata(binary_file.as_ref());
    println!("\nMetadata:");
    println!("  Total size: {} bytes", metadata.total_size);
    println!("  Code size: {} bytes", metadata.code_size);
    println!("  Data size: {} bytes", metadata.data_size);
    
    // Perform security checks if requested
    if security {
        let checks = binary::analysis::perform_security_checks(binary_file.as_ref());
        println!("\nSecurity Checks:");
        for check in &checks {
            let status = if check.passed { "✅ PASS" } else { "❌ FAIL" };
            println!("  {} - {}", status, check.name);
            println!("    Description: {}", check.description);
            if let Some(details) = &check.details {
                println!("    Details: {}", details);
            }
        }
    }
    
    // Save to file if requested
    if let Some(output_path) = output {
        println!("\nSaving analysis to {:?}", output_path);
        
        // Create a serializable structure for the analysis
        let analysis = serde_json::json!({
            "info": {
                "format": format!("{:?}", info.format),
                "architecture": info.architecture,
                "is_64bit": info.is_64bit,
                "is_executable": info.is_executable,
                "is_library": info.is_library,
                "entry_point": format!("0x{:x}", info.entry_point),
            },
            "sections": sections.iter().map(|s| {
                serde_json::json!({
                    "name": s.name,
                    "size": s.size,
                    "address": format!("0x{:x}", s.address),
                    "is_executable": s.is_executable,
                    "is_writable": s.is_writable,
                    "is_code": s.is_code,
                    "is_data": s.is_data,
                })
            }).collect::<Vec<_>>(),
            "imports": imports,
            "exports": exports,
            "metadata": {
                "total_size": metadata.total_size,
                "code_size": metadata.code_size,
                "data_size": metadata.data_size,
                "additional_info": metadata.additional_info,
            },
        });
        
        let json = serde_json::to_string_pretty(&analysis)?;
        std::fs::write(output_path, json)?;
    }
    
    Ok(())
}

/// Trace a binary and analyze its behavior
async fn trace_binary(binary: PathBuf, args: Vec<String>, duration: u64, output: Option<PathBuf>) -> Result<()> {
    println!("Tracing binary: {:?} with args: {:?}", binary, args);
    
    // Initialize the tracer
    let mut tracer = inspector_gadget::init().await?;
    
    // Start tracing the binary
    tracer.trace_binary(&binary).await?;
    
    // Collect events for the specified duration
    println!("Collecting events for {} seconds...", duration);
    time::sleep(Duration::from_secs(duration)).await;
    
    // Get the collected events
    let events = tracer.collect_events().await?;
    
    // Stop tracing
    tracer.stop().await?;
    
    // Display or save the results
    process_events(events, output).await?;
    
    Ok(())
}

/// Attach to a running process and analyze its behavior
async fn attach_to_process(pid: u32, duration: u64, output: Option<PathBuf>) -> Result<()> {
    println!("Attaching to process: {}", pid);
    
    // Initialize the tracer
    let mut tracer = inspector_gadget::init().await?;
    
    // Attach to the process
    tracer.attach_to_process(pid).await?;
    
    // Collect events for the specified duration
    println!("Collecting events for {} seconds...", duration);
    time::sleep(Duration::from_secs(duration)).await;
    
    // Get the collected events
    let events = tracer.collect_events().await?;
    
    // Stop tracing
    tracer.stop().await?;
    
    // Display or save the results
    process_events(events, output).await?;
    
    Ok(())
}

/// Process and display or save the collected events
async fn process_events(events: Vec<TraceEvent>, output: Option<PathBuf>) -> Result<()> {
    println!("Collected {} events", events.len());
    
    if let Some(output_path) = output {
        println!("Saving events to {:?}", output_path);
        let json = serde_json::to_string_pretty(&events)?;
        std::fs::write(output_path, json)?;
    } else {
        // Display summary of events
        let mut syscall_count = 0;
        let mut network_count = 0;
        let mut file_count = 0;
        let mut process_count = 0;
        let mut other_count = 0;
        
        for event in &events {
            match event.event_type {
                inspector_gadget::EventType::Syscall => syscall_count += 1,
                inspector_gadget::EventType::Network => network_count += 1,
                inspector_gadget::EventType::FileOperation => file_count += 1,
                inspector_gadget::EventType::ProcessExec | inspector_gadget::EventType::ProcessExit => process_count += 1,
                _ => other_count += 1,
            }
        }
        
        println!("Event summary:");
        println!("  System calls: {}", syscall_count);
        println!("  Network operations: {}", network_count);
        println!("  File operations: {}", file_count);
        println!("  Process events: {}", process_count);
        println!("  Other events: {}", other_count);
    }
    
    Ok(())
}

/// Handle the log-syscalls command
async fn handle_log_syscalls(
    pid_or_command: String,
    args: Vec<String>,
    log_file: Option<PathBuf>,
    stdout: bool,
    stderr: bool,
    elasticsearch: bool,
) -> Result<()> {
    use inspector_gadget::syscalls::{init_syscall_interceptor, log_all_syscalls};
    
    
    // Initialize the syscall interceptor for the current platform
    let mut interceptor = init_syscall_interceptor(Platform::current())?;
    
    // Configure the interceptor to log all syscalls
    log_all_syscalls(
        &mut *interceptor,
        log_file.as_ref().map(|p| p.to_str().unwrap_or("syscalls.log")),
        stdout,
        stderr,
        elasticsearch,
    )?;
    
    // Check if the argument is a PID or a command
    if let Ok(pid) = pid_or_command.parse::<u32>() {
        // Attach to an existing process
        println!("Attaching to process with PID: {}", pid);
        interceptor.attach_to_process(pid)?;
        
        // Wait for user input to stop
        println!("Press Enter to stop monitoring...");
        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;
    } else {
        // Run a command and monitor it
        println!("Running command: {} {:?}", pid_or_command, args);
        
        // Start the command
        let mut child = Command::new(&pid_or_command)
            .args(&args)
            .spawn()?;
        
        // Get the PID of the child process
        let pid = child.id();
        println!("Child process started with PID: {}", pid);
        
        // Attach to the child process
        interceptor.attach_to_process(pid)?;
        
        // Wait for the child process to exit
        let status = child.wait()?;
        println!("Child process exited with status: {:?}", status);
    }
    
    // Stop the interceptor
    interceptor.stop()?;
    
    println!("Syscall monitoring stopped.");
    if let Some(log_file) = log_file {
        println!("Check {} for the complete log.", log_file.display());
    }
    
    Ok(())
}

/// Start Elasticsearch container
async fn start_elasticsearch(es_port: u16, kibana_port: u16) -> Result<()> {
    
    use std::env;
    
    // Set environment variables
    env::set_var("ES_PORT", es_port.to_string());
    env::set_var("KIBANA_PORT", kibana_port.to_string());
    
    // Run the start script
    let output = Command::new(".dockerwrapper/start-elasticsearch.sh")
        .output()?;
    
    if !output.status.success() {
        let error = String::from_utf8_lossy(&output.stderr);
        println!("Failed to start Elasticsearch: {}", error);
        return Err(inspector_gadget::Error::Process(format!("Failed to start Elasticsearch: {}", error)));
    }
    
    println!("Elasticsearch started successfully.");
    println!("Elasticsearch: http://localhost:{}", es_port);
    println!("Kibana: http://localhost:{}", kibana_port);
    
    Ok(())
}

/// Stop Elasticsearch container
async fn stop_elasticsearch() -> Result<()> {
    
    
    // Run the stop script
    let output = Command::new(".dockerwrapper/stop-elasticsearch.sh")
        .output()?;
    
    if !output.status.success() {
        let error = String::from_utf8_lossy(&output.stderr);
        println!("Failed to stop Elasticsearch: {}", error);
        return Err(inspector_gadget::Error::Process(format!("Failed to stop Elasticsearch: {}", error)));
    }
    
    println!("Elasticsearch stopped successfully.");
    
    Ok(())
}
