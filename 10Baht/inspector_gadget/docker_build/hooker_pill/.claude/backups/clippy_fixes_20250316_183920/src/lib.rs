/*!
 * Inspector Gadget - Cross-platform Binary Analysis Framework
 * 
 * A framework for analyzing binary behavior across Windows and Linux platforms
 * using eBPF technology for efficient system call and activity tracing.
 */

// Public modules
pub mod platforms;
pub mod binary;
pub mod syscalls;
pub mod core;
pub mod analysis;
pub mod error;
pub mod logging;
pub mod ebpf;
pub mod hookers;
pub mod elasticsearch;

// Re-exports
pub use crate::error::{Error, Result};
pub use crate::core::{EventType, EventData, TraceEvent, Tracer};
pub use crate::platforms::Platform;
pub use crate::binary::{BinaryFormat, BinaryInfo, BinarySection};
pub use crate::syscalls::{Syscall, SyscallEvent, SyscallFilter, SyscallCategory};
pub use crate::logging::{ElasticsearchLogger, ElasticsearchConfig};
pub use hookers::{
    HookerError,
    HookerStats,
    Hooker,
    lsm_hooker::LsmHooker,
    xdp_hooker::XdpHooker,
    tracepoint_hooker::TracepointHooker,
    uprobe_hooker::UprobeHooker,
};


/// Version of the library
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Name of the library
pub const NAME: &str = env!("CARGO_PKG_NAME");

/// Description of the library
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

/// Initialize the library
pub fn init() -> std::result::Result<(), error::Error> {
    // Initialize logging
    env_logger::init();
    
    // Initialize other components
    
    Ok(())
}

/// Configuration for the binary analysis framework
#[derive(Debug, Clone)]
pub struct Config {
    /// Enable system call tracing
    pub enable_syscall_tracing: bool,
    /// Enable network activity monitoring
    pub enable_network_monitoring: bool,
    /// Enable file operation tracking
    pub enable_file_tracking: bool,
    /// Enable process execution monitoring
    pub enable_process_monitoring: bool,
}

impl Default for Config {
    fn default() -> Self {
        Config {
            enable_syscall_tracing: true,
            enable_network_monitoring: true,
            enable_file_tracking: true,
            enable_process_monitoring: true,
        }
    }
}

/// Version information
pub fn version() -> &'static str {
    env!("CARGO_PKG_VERSION")
}
