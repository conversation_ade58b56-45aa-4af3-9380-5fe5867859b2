/*!
 * Syscall Normalizer Implementation
 * 
 * This module provides normalization capabilities for syscall events,
 * ensuring consistent representation across different platforms.
 */

use std::collections::HashMap;
use std::sync::Arc;
use async_trait::async_trait;
use log::{debug, trace};
use serde_json::Value;

use crate::core::events::{EventData, EventType, TraceEvent};
use crate::error::Result;
use super::EventProcessor;

/// Syscall normalizer configuration
#[derive(Debug, Clone)]
pub struct SyscallNormalizerConfig {
    /// Syscall name mappings (platform-specific name -> normalized name)
    pub syscall_mappings: HashMap<String, String>,
    /// Parameter name mappings (platform-specific name -> normalized name)
    pub parameter_mappings: HashMap<String, String>,
    /// Parameter value transformations
    pub value_transformations: HashMap<String, Vec<(String, String)>>,
}

impl Default for SyscallNormalizerConfig {
    fn default() -> Self {
        Self {
            syscall_mappings: HashMap::new(),
            parameter_mappings: HashMap::new(),
            value_transformations: HashMap::new(),
        }
    }
}

/// Syscall normalizer
pub struct SyscallNormalizer {
    /// Configuration
    config: SyscallNormalizerConfig,
}

impl SyscallNormalizer {
    /// Create a new syscall normalizer
    pub fn new(config: SyscallNormalizerConfig) -> Self {
        Self {
            config,
        }
    }
    
    /// Create a new syscall normalizer with default configuration
    pub fn with_defaults() -> Self {
        Self::new(SyscallNormalizerConfig::default())
    }
    
    /// Normalize a syscall event
    fn normalize_syscall(&self, event: &mut TraceEvent) -> Result<()> {
        // Only process syscall events
        if let Some(syscall_name) = &event.syscall_name {
            // Normalize syscall name
            if let Some(normalized_name) = self.config.syscall_mappings.get(syscall_name) {
                trace!("Normalizing syscall name: {} -> {}", syscall_name, normalized_name);
                event.event_type = EventType::Syscall(normalized_name.clone());
            }
            
            // Normalize parameters
            if let EventData::Syscall(params) = &mut event.data {
                let mut normalized_params = serde_json::Map::new();
                
                for (key, value) in params.as_object().unwrap() {
                    // Normalize parameter name
                    let normalized_key = self.config.parameter_mappings
                        .get(key)
                        .map(|s| s.clone())
                        .unwrap_or_else(|| key.clone());
                    
                    // Normalize parameter value
                    let normalized_value = self.normalize_value(key, value.clone())?;
                    
                    normalized_params.insert(normalized_key, normalized_value);
                }
                
                *params = Value::Object(normalized_params);
            }
        }
        
        Ok(())
    }
    
    /// Normalize a parameter value
    fn normalize_value(&self, param_name: &str, value: Value) -> Result<Value> {
        // Check if we have transformations for this parameter
        if let Some(transformations) = self.config.value_transformations.get(param_name) {
            // Apply transformations
            if let Value::String(s) = &value {
                for (pattern, replacement) in transformations {
                    if s == pattern {
                        trace!("Transforming value: {} -> {}", s, replacement);
                        return Ok(Value::String(replacement.clone()));
                    }
                }
            } else if let Value::Number(n) = &value {
                if let Some(n) = n.as_i64() {
                    for (pattern, replacement) in transformations {
                        if pattern.parse::<i64>().ok() == Some(n) {
                            trace!("Transforming value: {} -> {}", n, replacement);
                            return Ok(Value::String(replacement.clone()));
                        }
                    }
                }
            }
        }
        
        Ok(value)
    }
}

#[async_trait]
impl EventProcessor for SyscallNormalizer {
    async fn process(&self, mut event: TraceEvent) -> Result<Option<TraceEvent>> {
        self.normalize_syscall(&mut event)?;
        debug!("Normalized event: {}", event.id);
        Ok(Some(event))
    }
    
    async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
        let mut result = Vec::with_capacity(events.len());
        
        for mut event in events {
            self.normalize_syscall(&mut event)?;
            result.push(event);
        }
        
        debug!("Normalized {} events", result.len());
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{EventSeverity, TraceEvent};
    
    #[tokio::test]
    async fn test_syscall_name_normalization() {
        // Create a normalizer with syscall name mappings
        let mut config = SyscallNormalizerConfig::default();
        config.syscall_mappings.insert("NtCreateFile".to_string(), "open".to_string());
        let normalizer = SyscallNormalizer::new(config);
        
        // Create a test event
        let event = TraceEvent::now(
            EventType::Syscall("NtCreateFile".to_string()),
            EventData::Syscall(serde_json::json!({
                "path": "/tmp/test.txt",
                "flags": "O_RDWR",
            })),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Process event
        let result = normalizer.process(event).await.unwrap().unwrap();
        
        // Check result
        if let EventType::Syscall(syscall_name) = &result.event_type {
            assert_eq!(syscall_name, "open");
        } else {
            panic!("Expected syscall event");
        }
    }
    
    #[tokio::test]
    async fn test_parameter_name_normalization() {
        // Create a normalizer with parameter name mappings
        let mut config = SyscallNormalizerConfig::default();
        config.parameter_mappings.insert("ObjectAttributes".to_string(), "path".to_string());
        let normalizer = SyscallNormalizer::new(config);
        
        // Create a test event
        let event = TraceEvent::now(
            EventType::Syscall("NtCreateFile".to_string()),
            EventData::Syscall(serde_json::json!({
                "ObjectAttributes": "/tmp/test.txt",
                "DesiredAccess": "GENERIC_READ | GENERIC_WRITE",
            })),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Process event
        let result = normalizer.process(event).await.unwrap().unwrap();
        
        // Check result
        if let EventData::Syscall(params) = &result.data {
            assert!(params.as_object().unwrap().contains_key("path"));
            assert!(!params.as_object().unwrap().contains_key("ObjectAttributes"));
            assert_eq!(params["path"], "/tmp/test.txt");
        } else {
            panic!("Expected syscall data");
        }
    }
    
    #[tokio::test]
    async fn test_value_transformation() {
        // Create a normalizer with value transformations
        let mut config = SyscallNormalizerConfig::default();
        config.value_transformations.insert(
            "flags".to_string(),
            vec![
                ("0x0001".to_string(), "O_RDONLY".to_string()),
                ("0x0002".to_string(), "O_WRONLY".to_string()),
                ("0x0003".to_string(), "O_RDWR".to_string()),
            ],
        );
        let normalizer = SyscallNormalizer::new(config);
        
        // Create a test event
        let event = TraceEvent::now(
            EventType::Syscall("open".to_string()),
            EventData::Syscall(serde_json::json!({
                "path": "/tmp/test.txt",
                "flags": "0x0003",
            })),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Process event
        let result = normalizer.process(event).await.unwrap().unwrap();
        
        // Check result
        if let EventData::Syscall(params) = &result.data {
            assert_eq!(params["flags"], "O_RDWR");
        } else {
            panic!("Expected syscall data");
        }
    }
} 