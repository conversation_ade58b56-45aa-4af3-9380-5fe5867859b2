//! Event types for the core module

use serde::{Serialize, Deserialize};



/// Unique identifier for events
pub type EventId = u64;

/// Event type enum
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum EventType {
    /// Syscall event
    Syscall,
    /// Network event
    Network,
    /// File event
    File,
    /// Process event
    Process,
    /// Custom event
    Custom(String),
}

/// Generic event interface
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Event {
    /// Unique identifier for the event
    pub id: EventId,
    /// Event type
    pub event_type: EventType,
    /// Event timestamp
    pub timestamp: u64,
    /// Event metadata
    pub metadata: Option<HashMap<String, serde_json::Value>>,
    /// Event data
    pub data: serde_json::Value,
}

impl fmt::Display for Event {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Event[{}]: {:?}", self.id, self.event_type)
    }
}

/// Trace event for syscall interception
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TraceEvent {
    /// Unique identifier for the event
    pub id: EventId,
    /// Event type
    pub event_type: String,
    /// Event timestamp
    pub timestamp: u64,
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// User ID
    pub uid: u32,
    /// Command name
    pub comm: String,
    /// Syscall number
    pub syscall: u32,
    /// Syscall name
    pub syscall_name: Option<String>,
    /// Arguments
    pub args: Vec<u64>,
    /// Return value
    pub ret: i64,
    /// Additional data
    pub data: Option<serde_json::Value>,
}

impl fmt::Display for TraceEvent {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "TraceEvent[{}]: {} (syscall: {})", 
               self.id, 
               self.event_type,
               self.syscall_name.as_ref().unwrap_or(&format!("{}", self.syscall)))
    }
}
