use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use std::time::{Duration, Instant};

use serde::{Serialize, Deserialize};
use parking_lot::RwLock;
use log::{debug, error, info, warn};

use crate::ebpf::maps::percpumap::{TypedOptimizedPerCpuMap, PerCpuMapStats};
use crate::ebpf::maps::MapError;
use crate::elasticsearch::ElasticsearchLogger;
use crate::hookers::{HookerError, HookerStats};

/// Function call information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionCallInfo {
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// Function address
    pub function_addr: u64,
    /// Function name
    pub function_name: String,
    /// Return address
    pub return_addr: u64,
    /// Stack pointer
    pub stack_ptr: u64,
    /// Entry timestamp
    pub entry_time: u64,
    /// Exit timestamp
    pub exit_time: u64,
    /// Duration in microseconds
    pub duration_us: u64,
    /// Return value
    pub return_value: u64,
}

/// Function call statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionCallStats {
    /// Function address
    pub function_addr: u64,
    /// Function name
    pub function_name: String,
    /// Number of calls
    pub calls: u64,
    /// Total duration in microseconds
    pub total_duration_us: u64,
    /// Minimum duration in microseconds
    pub min_duration_us: u64,
    /// Maximum duration in microseconds
    pub max_duration_us: u64,
    /// Average duration in microseconds
    pub avg_duration_us: u64,
}

/// Function parameter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionParameter {
    /// Parameter index
    pub index: u32,
    /// Parameter name
    pub name: String,
    /// Parameter type
    pub param_type: String,
    /// Parameter value
    pub value: String,
}

/// Function call with parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionCallWithParams {
    /// Function call information
    pub call_info: FunctionCallInfo,
    /// Parameters
    pub parameters: Vec<FunctionParameter>,
}

/// Call graph node
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CallGraphNode {
    /// Function address
    pub function_addr: u64,
    /// Function name
    pub function_name: String,
    /// Caller addresses
    pub callers: HashSet<u64>,
    /// Callee addresses
    pub callees: HashSet<u64>,
    /// Call count
    pub call_count: u64,
}

/// Uprobe Hooker statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UprobeHookerStats {
    /// Function call info map statistics
    pub function_call_info_stats: PerCpuMapStats,
    /// Function call stats map statistics
    pub function_call_stats_stats: PerCpuMapStats,
    /// Function parameters map statistics
    pub function_parameters_stats: PerCpuMapStats,
    /// Call graph map statistics
    pub call_graph_stats: PerCpuMapStats,
    /// Number of function calls traced
    pub function_calls_traced: usize,
    /// Number of function returns traced
    pub function_returns_traced: usize,
    /// Number of call graph nodes
    pub call_graph_nodes: usize,
    /// Start time
    pub start_time: Instant,
    /// Last update time
    pub last_update_time: Instant,
}

impl Default for UprobeHookerStats {
    fn default() -> Self {
        Self {
            function_call_info_stats: PerCpuMapStats::default(),
            function_call_stats_stats: PerCpuMapStats::default(),
            function_parameters_stats: PerCpuMapStats::default(),
            call_graph_stats: PerCpuMapStats::default(),
            function_calls_traced: 0,
            function_returns_traced: 0,
            call_graph_nodes: 0,
            start_time: Instant::now(),
            last_update_time: Instant::now(),
        }
    }
}

impl HookerStats for UprobeHookerStats {
    fn uptime(&self) -> Duration {
        self.start_time.elapsed()
    }
    
    fn last_update(&self) -> Duration {
        self.last_update_time.elapsed()
    }
}

/// Uprobe Hooker
pub struct UprobeHooker {
    /// Function call info map
    function_call_info_map: TypedOptimizedPerCpuMap<u64, FunctionCallInfo>,
    /// Function call stats map
    function_call_stats_map: TypedOptimizedPerCpuMap<u64, FunctionCallStats>,
    /// Function parameters map
    function_parameters_map: TypedOptimizedPerCpuMap<u64, Vec<FunctionParameter>>,
    /// Call graph map
    call_graph_map: TypedOptimizedPerCpuMap<u64, CallGraphNode>,
    /// Statistics
    stats: Arc<RwLock<UprobeHookerStats>>,
    /// Elasticsearch logger
    elasticsearch_logger: Option<Arc<ElasticsearchLogger>>,
}

impl UprobeHooker {
    /// Create a new Uprobe Hooker
    pub fn new(elasticsearch_logger: Option<Arc<ElasticsearchLogger>>) -> Result<Self, HookerError> {
        // Create maps
        let function_call_info_map = TypedOptimizedPerCpuMap::<u64, FunctionCallInfo>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        let function_call_stats_map = TypedOptimizedPerCpuMap::<u64, FunctionCallStats>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        let function_parameters_map = TypedOptimizedPerCpuMap::<u64, Vec<FunctionParameter>>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        let call_graph_map = TypedOptimizedPerCpuMap::<u64, CallGraphNode>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        // Create statistics
        let stats = Arc::new(RwLock::new(UprobeHookerStats::default()));
        
        Ok(Self {
            function_call_info_map,
            function_call_stats_map,
            function_parameters_map,
            call_graph_map,
            stats,
            elasticsearch_logger,
        })
    }
    
    /// Record function entry
    pub fn handle_function_entry(&self, pid: u32, tid: u32, function_addr: u64, function_name: &str, return_addr: u64, stack_ptr: u64, parameters: &[FunctionParameter]) -> Result<(), HookerError> {
        // Get current time
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Create key for function call info
        let key = self.create_function_call_key(pid, tid, function_addr);
        
        // Create function call info
        let info = FunctionCallInfo {
            pid,
            tid,
            function_addr,
            function_name: function_name.to_string(),
            return_addr,
            stack_ptr,
            entry_time: current_time,
            exit_time: 0, // Will be set on exit
            duration_us: 0, // Will be calculated on exit
            return_value: 0, // Will be set on exit
        };
        
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, tid);
        
        // Update function call info map
        self.function_call_info_map.update_cpu(&key, &info, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update function parameters map
        if !parameters.is_empty() {
            self.function_parameters_map.update_cpu(&key, &parameters.to_vec(), cpu)
                .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        }
        
        // Update call graph
        self.update_call_graph(pid, function_addr, function_name, return_addr)?;
        
        // Update statistics
        let mut stats = self.stats.write();
        stats.function_calls_traced += 1;
        stats.last_update_time = Instant::now();
        
        debug!("Recorded function entry: pid={}, tid={}, function={} ({})", pid, tid, function_addr, function_name);
        
        Ok(())
    }
    
    /// Record function exit
    pub fn handle_function_exit(&self, pid: u32, tid: u32, function_addr: u64, return_value: u64) -> Result<(), HookerError> {
        // Get current time
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Create key for function call info
        let key = self.create_function_call_key(pid, tid, function_addr);
        
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, tid);
        
        // Get function call info
        let mut info = match self.function_call_info_map.lookup_cpu(&key, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(i) => i,
            None => {
                warn!("No function entry found for exit: pid={}, tid={}, function={}", pid, tid, function_addr);
                return Ok(());
            }
        };
        
        // Update function call info
        info.exit_time = current_time;
        info.duration_us = (current_time - info.entry_time) * 1_000_000; // Convert seconds to microseconds
        info.return_value = return_value;
        
        // Update function call info map
        self.function_call_info_map.update_cpu(&key, &info, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update function call stats
        self.update_function_call_stats(function_addr, &info.function_name, info.duration_us)?;
        
        // Log to Elasticsearch if available
        if let Some(logger) = &self.elasticsearch_logger {
            // Get parameters if available
            let parameters = self.function_parameters_map.lookup_cpu(&key, cpu)
                .map_err(|e| HookerError::MapOperationError(e.to_string()))?
                .unwrap_or_default();
            
            // Create function call with parameters
            let call_with_params = FunctionCallWithParams {
                call_info: info.clone(),
                parameters,
            };
            
            if let Err(e) = logger.log_event("function_calls", &call_with_params) {
                error!("Failed to log function call to Elasticsearch: {}", e);
            }
        }
        
        // Update statistics
        let mut stats = self.stats.write();
        stats.function_returns_traced += 1;
        stats.last_update_time = Instant::now();
        
        debug!("Recorded function exit: pid={}, tid={}, function={}, return={}", pid, tid, function_addr, return_value);
        
        Ok(())
    }
    
    /// Update function call statistics
    fn update_function_call_stats(&self, function_addr: u64, function_name: &str, duration_us: u64) -> Result<(), HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_function(function_addr);
        
        // Get current stats
        let mut stats = match self.function_call_stats_map.lookup_cpu(&function_addr, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(s) => s,
            None => {
                // Create new stats
                FunctionCallStats {
                    function_addr,
                    function_name: function_name.to_string(),
                    calls: 0,
                    total_duration_us: 0,
                    min_duration_us: u64::MAX,
                    max_duration_us: 0,
                    avg_duration_us: 0,
                }
            }
        };
        
        // Update stats
        stats.calls += 1;
        stats.total_duration_us += duration_us;
        stats.min_duration_us = stats.min_duration_us.min(duration_us);
        stats.max_duration_us = stats.max_duration_us.max(duration_us);
        stats.avg_duration_us = stats.total_duration_us / stats.calls;
        
        // Update stats map
        self.function_call_stats_map.update_cpu(&function_addr, &stats, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        Ok(())
    }
    
    /// Update call graph
    fn update_call_graph(&self, pid: u32, function_addr: u64, function_name: &str, return_addr: u64) -> Result<(), HookerError> {
        // Get CPU ID for function
        let cpu = self.get_cpu_for_function(function_addr);
        
        // Get current node
        let mut node = match self.call_graph_map.lookup_cpu(&function_addr, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(n) => n,
            None => {
                // Create new node
                CallGraphNode {
                    function_addr,
                    function_name: function_name.to_string(),
                    callers: HashSet::new(),
                    callees: HashSet::new(),
                    call_count: 0,
                }
            }
        };
        
        // Update node
        node.call_count += 1;
        
        // Add caller if return address is valid
        if return_addr > 0 {
            node.callers.insert(return_addr);
            
            // Update caller node
            self.update_caller_node(return_addr, function_addr)?;
        }
        
        // Update node map
        self.call_graph_map.update_cpu(&function_addr, &node, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update statistics
        let mut stats = self.stats.write();
        stats.call_graph_nodes += 1;
        stats.last_update_time = Instant::now();
        
        Ok(())
    }
    
    /// Update caller node
    fn update_caller_node(&self, caller_addr: u64, callee_addr: u64) -> Result<(), HookerError> {
        // Get CPU ID for caller
        let cpu = self.get_cpu_for_function(caller_addr);
        
        // Get current node
        let mut node = match self.call_graph_map.lookup_cpu(&caller_addr, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(n) => n,
            None => {
                // Create new node
                CallGraphNode {
                    function_addr: caller_addr,
                    function_name: format!("func_{:x}", caller_addr), // Unknown name
                    callers: HashSet::new(),
                    callees: HashSet::new(),
                    call_count: 0,
                }
            }
        };
        
        // Add callee
        node.callees.insert(callee_addr);
        
        // Update node map
        self.call_graph_map.update_cpu(&caller_addr, &node, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        Ok(())
    }
    
    /// Get function call information
    pub fn get_function_call_info(&self, pid: u32, tid: u32, function_addr: u64) -> Result<Option<FunctionCallInfo>, HookerError> {
        // Create key for function call info
        let key = self.create_function_call_key(pid, tid, function_addr);
        
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, tid);
        
        // Lookup function call info
        self.function_call_info_map.lookup_cpu(&key, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Get function call statistics
    pub fn get_function_call_stats(&self, function_addr: u64) -> Result<Option<FunctionCallStats>, HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_function(function_addr);
        
        // Lookup function call stats
        self.function_call_stats_map.lookup_cpu(&function_addr, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Get function parameters
    pub fn get_function_parameters(&self, pid: u32, tid: u32, function_addr: u64) -> Result<Option<Vec<FunctionParameter>>, HookerError> {
        // Create key for function parameters
        let key = self.create_function_call_key(pid, tid, function_addr);
        
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, tid);
        
        // Lookup function parameters
        self.function_parameters_map.lookup_cpu(&key, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Get call graph node
    pub fn get_call_graph_node(&self, function_addr: u64) -> Result<Option<CallGraphNode>, HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_function(function_addr);
        
        // Lookup call graph node
        self.call_graph_map.lookup_cpu(&function_addr, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Get total function call statistics
    pub fn get_total_function_call_stats(&self) -> Result<HashMap<u64, FunctionCallStats>, HookerError> {
        let mut result = HashMap::new();
        
        // This is a simplified implementation
        // In a real implementation, we would need to scan all maps
        // and aggregate statistics for all functions
        
        // For now, we'll just simulate aggregating some functions
        for function_addr in 0..10 { // Simulate 10 functions
            let stats = self.function_call_stats_map.aggregate(&function_addr, |values| {
                if values.is_empty() {
                    return None;
                }
                
                let mut total = values[0].clone();
                
                for i in 1..values.len() {
                    total.calls += values[i].calls;
                    total.total_duration_us += values[i].total_duration_us;
                    total.min_duration_us = total.min_duration_us.min(values[i].min_duration_us);
                    total.max_duration_us = total.max_duration_us.max(values[i].max_duration_us);
                }
                
                if total.calls > 0 {
                    total.avg_duration_us = total.total_duration_us / total.calls;
                }
                
                Some(total)
            }).map_err(|e| HookerError::MapOperationError(e.to_string()))?;
            
            if let Some(s) = stats {
                result.insert(function_addr, s);
            }
        }
        
        Ok(result)
    }
    
    /// Export function call statistics to Elasticsearch
    pub fn export_stats_to_elasticsearch(&self) -> Result<usize, HookerError> {
        // Check if Elasticsearch logger is available
        let logger = match &self.elasticsearch_logger {
            Some(l) => l,
            None => return Err(HookerError::OperationError("Elasticsearch logger not configured".to_string())),
        };
        
        let mut exported_count = 0;
        
        // Get total function call statistics
        let stats = self.get_total_function_call_stats()?;
        
        // Export each function's statistics
        for (_, stat) in stats {
            if let Err(e) = logger.log_event("function_stats", &stat) {
                error!("Failed to export function statistics to Elasticsearch: {}", e);
            } else {
                exported_count += 1;
            }
        }
        
        Ok(exported_count)
    }
    
    /// Get statistics for all maps
    pub fn get_stats(&self) -> UprobeHookerStats {
        let mut stats = self.stats.write();
        
        // Update map statistics
        stats.function_call_info_stats = self.function_call_info_map.stats().clone();
        stats.function_call_stats_stats = self.function_call_stats_map.stats().clone();
        stats.function_parameters_stats = self.function_parameters_map.stats().clone();
        stats.call_graph_stats = self.call_graph_map.stats().clone();
        
        stats.clone()
    }
    
    /// Create a key for function call info
    fn create_function_call_key(&self, pid: u32, tid: u32, function_addr: u64) -> u64 {
        ((pid as u64) << 48) | ((tid as u64) << 32) | function_addr
    }
    
    /// Get CPU ID for a thread
    fn get_cpu_for_thread(&self, pid: u32, tid: u32) -> usize {
        // Simple hash function to distribute threads across CPUs
        ((pid as usize) ^ (tid as usize)) % self.function_call_info_map.num_cpus()
    }
    
    /// Get CPU ID for a function
    fn get_cpu_for_function(&self, function_addr: u64) -> usize {
        // Simple hash function to distribute functions across CPUs
        (function_addr as usize) % self.function_call_stats_map.num_cpus()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_uprobe_hooker_basic() {
        // Create Elasticsearch logger
        let elasticsearch_logger = ElasticsearchLogger::new("http://localhost:9200", "test_index")
            .ok()
            .map(Arc::new);
        
        // Create Uprobe Hooker
        let hooker = UprobeHooker::new(elasticsearch_logger).unwrap();
        
        // Create parameters
        let parameters = vec![
            FunctionParameter {
                index: 0,
                name: "arg1".to_string(),
                param_type: "int".to_string(),
                value: "42".to_string(),
            },
            FunctionParameter {
                index: 1,
                name: "arg2".to_string(),
                param_type: "char*".to_string(),
                value: "hello".to_string(),
            },
        ];
        
        // Test function entry
        hooker.handle_function_entry(1000, 1000, 0x1000, "test_function", 0x2000, 0x3000, &parameters).unwrap();
        
        // Test function exit
        hooker.handle_function_exit(1000, 1000, 0x1000, 0x42).unwrap();
        
        // Test function call info
        let info = hooker.get_function_call_info(1000, 1000, 0x1000).unwrap().unwrap();
        assert_eq!(info.pid, 1000);
        assert_eq!(info.tid, 1000);
        assert_eq!(info.function_addr, 0x1000);
        assert_eq!(info.function_name, "test_function");
        assert_eq!(info.return_addr, 0x2000);
        assert_eq!(info.stack_ptr, 0x3000);
        assert_eq!(info.return_value, 0x42);
        
        // Test function call stats
        let stats = hooker.get_function_call_stats(0x1000).unwrap().unwrap();
        assert_eq!(stats.function_addr, 0x1000);
        assert_eq!(stats.function_name, "test_function");
        assert_eq!(stats.calls, 1);
        
        // Test function parameters
        let params = hooker.get_function_parameters(1000, 1000, 0x1000).unwrap().unwrap();
        assert_eq!(params.len(), 2);
        assert_eq!(params[0].index, 0);
        assert_eq!(params[0].name, "arg1");
        assert_eq!(params[0].param_type, "int");
        assert_eq!(params[0].value, "42");
        
        // Test call graph
        let node = hooker.get_call_graph_node(0x1000).unwrap().unwrap();
        assert_eq!(node.function_addr, 0x1000);
        assert_eq!(node.function_name, "test_function");
        assert_eq!(node.call_count, 1);
        assert!(node.callers.contains(&0x2000));
        
        // Test statistics
        let stats = hooker.get_stats();
        assert_eq!(stats.function_calls_traced, 1);
        assert_eq!(stats.function_returns_traced, 1);
        assert_eq!(stats.call_graph_nodes, 1);
    }
} 
/// Uprobe event data
#[derive(Debug, Clone)]
pub struct UprobeEvent {
    /// Event ID
    pub id: u64,
    /// Timestamp
    pub timestamp: u64,
    /// Event data
    pub data: Vec<u8>,
}

/// Configuration for UprobeHooker
#[derive(Debug, Clone)]
pub struct UprobeHookerConfig {
    /// Name of the hooker
    pub name: String,
    /// Buffer size
    pub buffer_size: usize,
    /// Maximum events
    pub max_events: usize,
}

impl Default for UprobeHookerConfig {
    fn default() -> Self {
        Self {
            name: "uprobe_hooker".to_string(),
            buffer_size: 4096,
            max_events: 1000,
        }
    }
}
