

use std::time::{Duration, Instant};

use serde::{Serialize, Deserialize};
use parking_lot::RwLock;


use crate::ebpf::maps::percpumap::{TypedOptimizedPerCpuMap, PerCpuMapStats};
use crate::ebpf::maps::MapError;
use crate::elasticsearch::ElasticsearchLogger;
use crate::hookers::{HookerError, HookerStats};

/// Syscall information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyscallInfo {
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// User ID
    pub uid: u32,
    /// Syscall number
    pub syscall_nr: u32,
    /// Syscall name
    pub syscall_name: String,
    /// Arguments
    pub args: Vec<u64>,
    /// Return value
    pub ret: i64,
    /// Entry timestamp
    pub entry_time: u64,
    /// Exit timestamp
    pub exit_time: u64,
    /// Duration in microseconds
    pub duration_us: u64,
}

/// Syscall statistics
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SyscallStats {
    /// Syscall number
    pub syscall_nr: u32,
    /// Syscall name
    pub syscall_name: String,
    /// Number of calls
    pub calls: u64,
    /// Number of errors
    pub errors: u64,
    /// Total duration in microseconds
    pub total_duration_us: u64,
    /// Minimum duration in microseconds
    pub min_duration_us: u64,
    /// Maximum duration in microseconds
    pub max_duration_us: u64,
    /// Average duration in microseconds
    pub avg_duration_us: u64,
}

/// Syscall sequence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyscallSequence {
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// Sequence of syscall numbers
    pub sequence: Vec<u32>,
    /// Start timestamp
    pub start_time: u64,
    /// End timestamp
    pub end_time: u64,
}

/// Tracepoint Hooker statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TracepointHookerStats {
    /// Syscall info map statistics
    pub syscall_info_stats: PerCpuMapStats,
    /// Syscall stats map statistics
    pub syscall_stats_stats: PerCpuMapStats,
    /// Syscall sequence map statistics
    pub syscall_sequence_stats: PerCpuMapStats,
    /// Number of syscalls traced
    pub syscalls_traced: usize,
    /// Number of syscall errors
    pub syscall_errors: usize,
    /// Number of sequences recorded
    pub sequences_recorded: usize,
    /// Start time
    pub start_time: Instant,
    /// Last update time
    pub last_update_time: Instant,
}

impl Default for TracepointHookerStats {
    fn default() -> Self {
        Self {
            syscall_info_stats: PerCpuMapStats::default(),
            syscall_stats_stats: PerCpuMapStats::default(),
            syscall_sequence_stats: PerCpuMapStats::default(),
            syscalls_traced: 0,
            syscall_errors: 0,
            sequences_recorded: 0,
            start_time: Instant::now(),
            last_update_time: Instant::now(),
        }
    }
}

impl HookerStats for TracepointHookerStats {
    fn uptime(&self) -> Duration {
        self.start_time.elapsed()
    }
    
    fn last_update(&self) -> Duration {
        self.last_update_time.elapsed()
    }
}

/// Tracepoint Hooker
pub struct TracepointHooker {
    /// Syscall info map
    syscall_info_map: TypedOptimizedPerCpuMap<u64, SyscallInfo>,
    /// Syscall stats map
    syscall_stats_map: TypedOptimizedPerCpuMap<u32, SyscallStats>,
    /// Syscall sequence map
    syscall_sequence_map: TypedOptimizedPerCpuMap<u32, SyscallSequence>,
    /// Statistics
    stats: Arc<RwLock<TracepointHookerStats>>,
    /// Elasticsearch logger
    elasticsearch_logger: Option<Arc<ElasticsearchLogger>>,
}

impl TracepointHooker {
    /// Create a new Tracepoint Hooker
    pub fn new(elasticsearch_logger: Option<Arc<ElasticsearchLogger>>) -> Result<Self, HookerError> {
        // Create maps
        let syscall_info_map = TypedOptimizedPerCpuMap::<u64, SyscallInfo>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        let syscall_stats_map = TypedOptimizedPerCpuMap::<u32, SyscallStats>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        let syscall_sequence_map = TypedOptimizedPerCpuMap::<u32, SyscallSequence>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        // Create statistics
        let stats = Arc::new(RwLock::new(TracepointHookerStats::default()));
        
        Ok(Self {
            syscall_info_map,
            syscall_stats_map,
            syscall_sequence_map,
            stats,
            elasticsearch_logger,
        })
    }
    
    /// Record syscall entry
    pub fn record_syscall_entry(&self, pid: u32, tid: u32, uid: u32, syscall_nr: u32, syscall_name: &str, args: &[u64]) -> Result<(), HookerError> {
        // Get current time
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Create key for syscall info
        let key = self.create_syscall_key(pid, tid, syscall_nr);
        
        // Create syscall info
        let info = SyscallInfo {
            pid,
            tid,
            uid,
            syscall_nr,
            syscall_name: syscall_name.to_string(),
            args: args.to_vec(),
            ret: 0, // Will be set on exit
            entry_time: current_time,
            exit_time: 0, // Will be set on exit
            duration_us: 0, // Will be calculated on exit
        };
        
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, tid);
        
        // Update syscall info map
        self.syscall_info_map.update_cpu(&key, &info, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update syscall sequence
        self.update_syscall_sequence(pid, tid, syscall_nr, current_time)?;
        
        // Update statistics
        let mut stats = self.stats.write();
        stats.syscalls_traced += 1;
        stats.last_update_time = Instant::now();
        
        debug!("Recorded syscall entry: pid={}, tid={}, syscall={} ({})", pid, tid, syscall_nr, syscall_name);
        
        Ok(())
    }
    
    /// Record syscall exit
    pub fn record_syscall_exit(&self, pid: u32, tid: u32, syscall_nr: u32, ret: i64) -> Result<(), HookerError> {
        // Get current time
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Create key for syscall info
        let key = self.create_syscall_key(pid, tid, syscall_nr);
        
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, tid);
        
        // Get syscall info
        let mut info = match self.syscall_info_map.lookup_cpu(&key, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(i) => i,
            None => {
                warn!("No syscall entry found for exit: pid={}, tid={}, syscall={}", pid, tid, syscall_nr);
                return Ok(());
            }
        };
        
        // Update syscall info
        info.ret = ret;
        info.exit_time = current_time;
        info.duration_us = (current_time - info.entry_time) * 1_000_000; // Convert seconds to microseconds
        
        // Update syscall info map
        self.syscall_info_map.update_cpu(&key, &info, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update syscall stats
        self.update_syscall_stats(syscall_nr, &info.syscall_name, ret, info.duration_us)?;
        
        // Log to Elasticsearch if available
        if let Some(logger) = &self.elasticsearch_logger {
            if let Err(e) = logger.log_event("syscall_info", &info) {
                error!("Failed to log syscall info to Elasticsearch: {}", e);
            }
        }
        
        // Update statistics
        let mut stats = self.stats.write();
        if ret < 0 {
            stats.syscall_errors += 1;
        }
        stats.last_update_time = Instant::now();
        
        debug!("Recorded syscall exit: pid={}, tid={}, syscall={}, ret={}", pid, tid, syscall_nr, ret);
        
        Ok(())
    }
    
    /// Update syscall sequence
    fn update_syscall_sequence(&self, pid: u32, tid: u32, syscall_nr: u32, timestamp: u64) -> Result<(), HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, tid);
        
        // Get current sequence
        let mut sequence = match self.syscall_sequence_map.lookup_cpu(&pid, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(s) => s,
            None => {
                // Create new sequence
                SyscallSequence {
                    pid,
                    tid,
                    sequence: Vec::new(),
                    start_time: timestamp,
                    end_time: timestamp,
                }
            }
        };
        
        // Add syscall to sequence
        sequence.sequence.push(syscall_nr);
        sequence.end_time = timestamp;
        
        // Limit sequence length
        if sequence.sequence.len() > 100 {
            sequence.sequence.remove(0);
        }
        
        // Update sequence map
        self.syscall_sequence_map.update_cpu(&pid, &sequence, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update statistics
        let mut stats = self.stats.write();
        stats.sequences_recorded += 1;
        stats.last_update_time = Instant::now();
        
        Ok(())
    }
    
    /// Update syscall statistics
    fn update_syscall_stats(&self, syscall_nr: u32, syscall_name: &str, ret: i64, duration_us: u64) -> Result<(), HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_syscall(syscall_nr);
        
        // Get current stats
        let mut stats = match self.syscall_stats_map.lookup_cpu(&syscall_nr, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(s) => s,
            None => {
                // Create new stats
                SyscallStats {
                    syscall_nr,
                    syscall_name: syscall_name.to_string(),
                    calls: 0,
                    errors: 0,
                    total_duration_us: 0,
                    min_duration_us: u64::MAX,
                    max_duration_us: 0,
                    avg_duration_us: 0,
                }
            }
        };
        
        // Update stats
        stats.calls += 1;
        if ret < 0 {
            stats.errors += 1;
        }
        stats.total_duration_us += duration_us;
        stats.min_duration_us = stats.min_duration_us.min(duration_us);
        stats.max_duration_us = stats.max_duration_us.max(duration_us);
        stats.avg_duration_us = stats.total_duration_us / stats.calls;
        
        // Update stats map
        self.syscall_stats_map.update_cpu(&syscall_nr, &stats, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        Ok(())
    }
    
    /// Get syscall information
    pub fn get_syscall_info(&self, pid: u32, tid: u32, syscall_nr: u32) -> Result<Option<SyscallInfo>, HookerError> {
        // Create key for syscall info
        let key = self.create_syscall_key(pid, tid, syscall_nr);
        
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, tid);
        
        // Lookup syscall info
        self.syscall_info_map.lookup_cpu(&key, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Get syscall statistics
    pub fn get_syscall_stats(&self, syscall_nr: u32) -> Result<Option<SyscallStats>, HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_syscall(syscall_nr);
        
        // Lookup syscall stats
        self.syscall_stats_map.lookup_cpu(&syscall_nr, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Get syscall sequence
    pub fn get_syscall_sequence(&self, pid: u32) -> Result<Option<SyscallSequence>, HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_thread(pid, 0);
        
        // Lookup syscall sequence
        self.syscall_sequence_map.lookup_cpu(&pid, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Get total syscall statistics
    pub fn get_total_syscall_stats(&self) -> Result<HashMap<u32, SyscallStats>, HookerError> {
        let mut result = HashMap::new();
        
        // Aggregate syscall stats for all syscalls
        for syscall_nr in 0..512 { // Assuming max 512 syscalls
            let stats = self.syscall_stats_map.aggregate(&syscall_nr, |values| {
                if values.is_empty() {
                    return None;
                }
                
                let mut total = values[0].clone();
                
                for i in 1..values.len() {
                    total.calls += values[i].calls;
                    total.errors += values[i].errors;
                    total.total_duration_us += values[i].total_duration_us;
                    total.min_duration_us = total.min_duration_us.min(values[i].min_duration_us);
                    total.max_duration_us = total.max_duration_us.max(values[i].max_duration_us);
                }
                
                if total.calls > 0 {
                    total.avg_duration_us = total.total_duration_us / total.calls;
                }
                
                Some(total)
            }).map_err(|e| HookerError::MapOperationError(e.to_string()))?;
            
            if let Some(s) = stats {
                result.insert(syscall_nr, s);
            }
        }
        
        Ok(result)
    }
    
    /// Export syscall statistics to Elasticsearch
    pub fn export_stats_to_elasticsearch(&self) -> Result<usize, HookerError> {
        // Check if Elasticsearch logger is available
        let logger = match &self.elasticsearch_logger {
            Some(l) => l,
            None => return Err(HookerError::OperationError("Elasticsearch logger not configured".to_string())),
        };
        
        let mut exported_count = 0;
        
        // Get total syscall statistics
        let stats = self.get_total_syscall_stats()?;
        
        // Export each syscall's statistics
        for (_, stat) in stats {
            if let Err(e) = logger.log_event("syscall_stats", &stat) {
                error!("Failed to export syscall statistics to Elasticsearch: {}", e);
            } else {
                exported_count += 1;
            }
        }
        
        Ok(exported_count)
    }
    
    /// Get statistics for all maps
    pub fn get_stats(&self) -> TracepointHookerStats {
        let mut stats = self.stats.write();
        
        // Update map statistics
        stats.syscall_info_stats = self.syscall_info_map.stats().clone();
        stats.syscall_stats_stats = self.syscall_stats_map.stats().clone();
        stats.syscall_sequence_stats = self.syscall_sequence_map.stats().clone();
        
        stats.clone()
    }
    
    /// Create a key for syscall info
    fn create_syscall_key(&self, pid: u32, tid: u32, syscall_nr: u32) -> u64 {
        ((pid as u64) << 32) | ((tid as u64) << 16) | (syscall_nr as u64)
    }
    
    /// Get CPU ID for a thread
    fn get_cpu_for_thread(&self, pid: u32, tid: u32) -> usize {
        // Simple hash function to distribute threads across CPUs
        ((pid as usize) ^ (tid as usize)) % self.syscall_info_map.num_cpus()
    }
    
    /// Get CPU ID for a syscall
    fn get_cpu_for_syscall(&self, syscall_nr: u32) -> usize {
        // Simple hash function to distribute syscalls across CPUs
        (syscall_nr as usize) % self.syscall_stats_map.num_cpus()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_tracepoint_hooker_basic() {
        // Create Elasticsearch logger
        let elasticsearch_logger = ElasticsearchLogger::new("http://localhost:9200", "test_index")
            .ok()
            .map(Arc::new);
        
        // Create Tracepoint Hooker
        let hooker = TracepointHooker::new(elasticsearch_logger).unwrap();
        
        // Test syscall entry
        hooker.record_syscall_entry(1000, 1000, 1000, 1, "write", &[1, 2, 3]).unwrap();
        
        // Test syscall exit
        hooker.record_syscall_exit(1000, 1000, 1, 42).unwrap();
        
        // Test syscall info
        let info = hooker.get_syscall_info(1000, 1000, 1).unwrap().unwrap();
        assert_eq!(info.pid, 1000);
        assert_eq!(info.tid, 1000);
        assert_eq!(info.uid, 1000);
        assert_eq!(info.syscall_nr, 1);
        assert_eq!(info.syscall_name, "write");
        assert_eq!(info.args, vec![1, 2, 3]);
        assert_eq!(info.ret, 42);
        
        // Test syscall stats
        let stats = hooker.get_syscall_stats(1).unwrap().unwrap();
        assert_eq!(stats.syscall_nr, 1);
        assert_eq!(stats.syscall_name, "write");
        assert_eq!(stats.calls, 1);
        assert_eq!(stats.errors, 0);
        
        // Test syscall sequence
        let sequence = hooker.get_syscall_sequence(1000).unwrap().unwrap();
        assert_eq!(sequence.pid, 1000);
        assert_eq!(sequence.tid, 1000);
        assert_eq!(sequence.sequence, vec![1]);
        
        // Test statistics
        let stats = hooker.get_stats();
        assert_eq!(stats.syscalls_traced, 1);
        assert_eq!(stats.syscall_errors, 0);
        assert_eq!(stats.sequences_recorded, 1);
    }
} 
/// Tracepoint event data
#[derive(Debug, Clone)]
pub struct TracepointEvent {
    /// Event ID
    pub id: u64,
    /// Timestamp
    pub timestamp: u64,
    /// Event data
    pub data: Vec<u8>,
}

/// Configuration for TracepointHooker
#[derive(Debug, Clone)]
pub struct TracepointHookerConfig {
    /// Name of the hooker
    pub name: String,
    /// Buffer size
    pub buffer_size: usize,
    /// Maximum events
    pub max_events: usize,
}

impl Default for TracepointHookerConfig {
    fn default() -> Self {
        Self {
            name: "tracepoint_hooker".to_string(),
            buffer_size: 4096,
            max_events: 1000,
        }
    }
}
