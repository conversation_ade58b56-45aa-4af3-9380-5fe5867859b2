/*!
 * Syscall Analysis Module
 * 
 * This module provides functionality for analyzing syscall events,
 * detecting patterns, and identifying anomalies in syscall behavior.
 */





use crate::platforms::Platform;
use crate::syscalls::common::{Syscall, SyscallEvent, SyscallCategory, SyscallParameter, SyscallParameterValue};

/// Report generated from syscall analysis
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct SyscallAnalysisReport {
    /// Total number of syscalls analyzed
    pub total_syscalls: usize,
    /// Number of successful syscalls
    pub successful_syscalls: usize,
    /// Number of failed syscalls
    pub failed_syscalls: usize,
    /// Syscalls by category
    pub syscalls_by_category: HashMap<SyscallCategory, usize>,
    /// Most frequently used syscalls
    pub most_frequent_syscalls: Vec<(String, usize)>,
    /// Syscalls with highest failure rate
    pub highest_failure_rate: Vec<(String, f64)>,
    /// Syscalls with longest average duration
    pub longest_duration_syscalls: Vec<(String, u64)>,
    /// Security-sensitive syscalls
    pub security_sensitive_syscalls: Vec<SyscallEvent>,
    /// Detected patterns
    pub detected_patterns: Vec<PatternMatch>,
    /// Anomalies detected
    pub anomalies: Vec<SyscallAnomaly>,
}

impl SyscallAnalysisReport {
    /// Create a new empty analysis report
    pub fn new() -> Self {
        Self {
            total_syscalls: 0,
            successful_syscalls: 0,
            failed_syscalls: 0,
            syscalls_by_category: HashMap::new(),
            most_frequent_syscalls: Vec::new(),
            highest_failure_rate: Vec::new(),
            longest_duration_syscalls: Vec::new(),
            security_sensitive_syscalls: Vec::new(),
            detected_patterns: Vec::new(),
            anomalies: Vec::new(),
        }
    }
}

impl Default for SyscallAnalysisReport {
    fn default() -> Self {
        Self::new()
    }
}

/// A pattern match in syscall events
#[derive(Debug, Clone)]
pub struct PatternMatch {
    /// Name of the pattern
    pub name: String,
    /// Description of the pattern
    pub description: String,
    /// Confidence level (0.0 to 1.0)
    pub confidence: f64,
    /// Syscall events that matched the pattern
    pub events: Vec<SyscallEvent>,
}

/// An anomaly detected in syscall events
#[derive(Debug, Clone)]
pub struct SyscallAnomaly {
    /// Type of anomaly
    pub anomaly_type: AnomalyType,
    /// Description of the anomaly
    pub description: String,
    /// Severity level (0.0 to 1.0)
    pub severity: f64,
    /// Syscall events related to the anomaly
    pub events: Vec<SyscallEvent>,
}

/// Types of syscall anomalies
#[derive(Debug, Clone, PartialEq)]
pub enum AnomalyType {
    /// Unusual syscall frequency
    UnusualFrequency,
    /// Unusual syscall parameters
    UnusualParameters,
    /// Unusual syscall sequence
    UnusualSequence,
    /// Unusual syscall timing
    UnusualTiming,
    /// Unusual syscall failure rate
    UnusualFailureRate,
    /// Potential privilege escalation
    PrivilegeEscalation,
    /// Potential information leak
    InformationLeak,
    /// Potential resource exhaustion
    ResourceExhaustion,
    /// Other anomaly type
    Other(String),
}

/// Analyze syscall events and generate a report
pub fn analyze_syscalls(events: &[SyscallEvent]) -> SyscallAnalysisReport {
    let mut report = SyscallAnalysisReport::new();
    
    // Skip analysis if there are no events
    if events.is_empty() {
        return report;
    }
    
    // Basic statistics
    report.total_syscalls = events.len();
    report.successful_syscalls = events.iter().filter(|e| e.success).count();
    report.failed_syscalls = events.iter().filter(|e| !e.success).count();
    
    // Syscalls by category
    let mut category_counts = HashMap::new();
    for event in events {
        *category_counts.entry(event.category).or_insert(0) += 1;
    }
    report.syscalls_by_category = category_counts;
    
    // Most frequent syscalls
    let mut syscall_counts = HashMap::new();
    for event in events {
        *syscall_counts.entry(event.syscall_name.clone()).or_insert(0) += 1;
    }
    
    let mut syscall_counts_vec: Vec<(String, usize)> = syscall_counts.into_iter().collect();
    syscall_counts_vec.sort_by(|a, b| b.1.cmp(&a.1));
    report.most_frequent_syscalls = syscall_counts_vec.into_iter().take(10).collect();
    
    // Syscalls with highest failure rate
    let mut syscall_failures = HashMap::new();
    let mut syscall_totals = HashMap::new();
    
    for event in events {
        *syscall_totals.entry(event.syscall_name.clone()).or_insert(0) += 1;
        if !event.success {
            *syscall_failures.entry(event.syscall_name.clone()).or_insert(0) += 1;
        }
    }
    
    let mut failure_rates: Vec<(String, f64)> = syscall_totals
        .iter()
        .filter_map(|(name, total)| {
            let failures = syscall_failures.get(name).unwrap_or(&0);
            if *total >= 5 && *failures > 0 {
                // Only include syscalls with at least 5 calls and some failures
                Some((name.clone(), *failures as f64 / *total as f64))
            } else {
                None
            }
        })
        .collect();
    
    failure_rates.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
    report.highest_failure_rate = failure_rates.into_iter().take(10).collect();
    
    // Syscalls with longest average duration
    let mut syscall_durations = HashMap::new();
    let mut syscall_duration_counts = HashMap::new();
    
    for event in events {
        *syscall_duration_counts.entry(event.syscall_name.clone()).or_insert(0) += 1;
        *syscall_durations.entry(event.syscall_name.clone()).or_insert(0) += event.duration_ns;
    }
    
    let mut avg_durations: Vec<(String, u64)> = syscall_durations
        .iter()
        .filter_map(|(name, total_duration)| {
            let count = syscall_duration_counts.get(name).unwrap_or(&1);
            if *count >= 5 {
                // Only include syscalls with at least 5 calls
                Some((name.clone(), total_duration / count))
            } else {
                None
            }
        })
        .collect();
    
    avg_durations.sort_by(|a, b| b.1.cmp(&a.1));
    report.longest_duration_syscalls = avg_durations.into_iter().take(10).collect();
    
    // Security-sensitive syscalls
    report.security_sensitive_syscalls = events
        .iter()
        .filter(|e| {
            matches!(
                e.category,
                SyscallCategory::Security | SyscallCategory::Process | SyscallCategory::Module
            )
        })
        .cloned()
        .collect();
    
    // Detect patterns
    report.detected_patterns = detect_patterns(events);
    
    // Detect anomalies
    report.anomalies = detect_anomalies(events);
    
    report
}

/// Detect patterns in syscall events
fn detect_patterns(events: &[SyscallEvent]) -> Vec<PatternMatch> {
    let mut patterns = Vec::new();
    
    // Skip pattern detection if there are too few events
    if events.len() < 5 {
        return patterns;
    }
    
    // Detect file enumeration pattern
    if let Some(pattern) = detect_file_enumeration_pattern(events) {
        patterns.push(pattern);
    }
    
    // Detect network connection pattern
    if let Some(pattern) = detect_network_connection_pattern(events) {
        patterns.push(pattern);
    }
    
    // Detect process creation pattern
    if let Some(pattern) = detect_process_creation_pattern(events) {
        patterns.push(pattern);
    }
    
    // Detect registry access pattern
    if let Some(pattern) = detect_registry_access_pattern(events) {
        patterns.push(pattern);
    }
    
    patterns
}

/// Detect file enumeration pattern
fn detect_file_enumeration_pattern(events: &[SyscallEvent]) -> Option<PatternMatch> {
    // Look for sequences of file operations that suggest enumeration
    let file_ops = events
        .iter()
        .filter(|e| e.category == SyscallCategory::FileSystem)
        .collect::<Vec<_>>();
    
    // If we have at least 5 file operations, check for enumeration pattern
    if file_ops.len() >= 5 {
        // Check if there are multiple FindFirstFile/FindNextFile or similar operations
        let find_ops = file_ops
            .iter()
            .filter(|e| {
                e.syscall_name.contains("Find") || e.syscall_name.contains("Query") || e.syscall_name.contains("Enum")
            })
            .count();
        
        if find_ops >= 3 {
            return Some(PatternMatch {
                name: "File Enumeration".to_string(),
                description: "Multiple file enumeration operations detected".to_string(),
                confidence: 0.7,
                events: file_ops.iter().map(|e| (*e).clone()).collect(),
            });
        }
    }
    
    None
}

/// Detect network connection pattern
fn detect_network_connection_pattern(events: &[SyscallEvent]) -> Option<PatternMatch> {
    // Look for sequences of network operations that suggest connection establishment
    let network_ops = events
        .iter()
        .filter(|e| e.category == SyscallCategory::Network)
        .collect::<Vec<_>>();
    
    // If we have at least 3 network operations, check for connection pattern
    if network_ops.len() >= 3 {
        // Check if there are socket, connect, and send/recv operations
        let socket_ops = network_ops
            .iter()
            .filter(|e| e.syscall_name.contains("socket") || e.syscall_name.contains("Socket"))
            .count();
        
        let connect_ops = network_ops
            .iter()
            .filter(|e| e.syscall_name.contains("connect") || e.syscall_name.contains("Connect"))
            .count();
        
        let io_ops = network_ops
            .iter()
            .filter(|e| {
                e.syscall_name.contains("send") || e.syscall_name.contains("recv") ||
                e.syscall_name.contains("Send") || e.syscall_name.contains("Recv")
            })
            .count();
        
        if socket_ops > 0 && connect_ops > 0 && io_ops > 0 {
            return Some(PatternMatch {
                name: "Network Connection".to_string(),
                description: "Network connection establishment and data transfer detected".to_string(),
                confidence: 0.8,
                events: network_ops.iter().map(|e| (*e).clone()).collect(),
            });
        }
    }
    
    None
}

/// Detect process creation pattern
fn detect_process_creation_pattern(events: &[SyscallEvent]) -> Option<PatternMatch> {
    // Look for process creation operations
    let process_ops = events
        .iter()
        .filter(|e| e.category == SyscallCategory::Process)
        .collect::<Vec<_>>();
    
    // Check for process creation syscalls
    let create_ops = process_ops
        .iter()
        .filter(|e| {
            e.syscall_name.contains("CreateProcess") || 
            e.syscall_name.contains("NtCreateProcess") ||
            e.syscall_name.contains("fork") ||
            e.syscall_name.contains("clone") ||
            e.syscall_name.contains("execve")
        })
        .count();
    
    if create_ops > 0 {
        return Some(PatternMatch {
            name: "Process Creation".to_string(),
            description: "Process creation operations detected".to_string(),
            confidence: 0.9,
            events: process_ops
                .iter()
                .filter(|e| {
                    e.syscall_name.contains("CreateProcess") || 
                    e.syscall_name.contains("NtCreateProcess") ||
                    e.syscall_name.contains("fork") ||
                    e.syscall_name.contains("clone") ||
                    e.syscall_name.contains("execve")
                })
                .map(|e| (*e).clone())
                .collect(),
        });
    }
    
    None
}

/// Detect registry access pattern
fn detect_registry_access_pattern(events: &[SyscallEvent]) -> Option<PatternMatch> {
    // Look for registry operations (Windows-specific)
    let registry_ops = events
        .iter()
        .filter(|e| {
            e.syscall_name.contains("Registry") || 
            e.syscall_name.contains("Reg") ||
            e.syscall_name.contains("Key") ||
            e.syscall_name.contains("NtCreateKey") ||
            e.syscall_name.contains("NtOpenKey") ||
            e.syscall_name.contains("NtSetValueKey") ||
            e.syscall_name.contains("NtQueryValueKey")
        })
        .collect::<Vec<_>>();
    
    if registry_ops.len() >= 3 {
        return Some(PatternMatch {
            name: "Registry Access".to_string(),
            description: "Multiple registry access operations detected".to_string(),
            confidence: 0.8,
            events: registry_ops.iter().map(|e| (*e).clone()).collect(),
        });
    }
    
    None
}

/// Detect anomalies in syscall events
fn detect_anomalies(events: &[SyscallEvent]) -> Vec<SyscallAnomaly> {
    let mut anomalies = Vec::new();
    
    // Skip anomaly detection if there are too few events
    if events.len() < 10 {
        return anomalies;
    }
    
    // Detect unusual syscall frequency
    if let Some(anomaly) = detect_unusual_frequency(events) {
        anomalies.push(anomaly);
    }
    
    // Detect unusual syscall parameters
    if let Some(anomaly) = detect_unusual_parameters(events) {
        anomalies.push(anomaly);
    }
    
    // Detect unusual syscall sequence
    if let Some(anomaly) = detect_unusual_sequence(events) {
        anomalies.push(anomaly);
    }
    
    // Detect potential privilege escalation
    if let Some(anomaly) = detect_privilege_escalation(events) {
        anomalies.push(anomaly);
    }
    
    anomalies
}

/// Detect unusual syscall frequency
fn detect_unusual_frequency(events: &[SyscallEvent]) -> Option<SyscallAnomaly> {
    // Count syscalls by name
    let mut syscall_counts = HashMap::new();
    for event in events {
        *syscall_counts.entry(event.syscall_name.clone()).or_insert(0) += 1;
    }
    
    // Calculate average and standard deviation
    let total_syscalls = events.len();
    let avg_count = total_syscalls as f64 / syscall_counts.len() as f64;
    
    let variance: f64 = syscall_counts
        .values()
        .map(|&count| {
            let diff = count as f64 - avg_count;
            diff * diff
        })
        .sum::<f64>() / syscall_counts.len() as f64;
    
    let std_dev = variance.sqrt();
    
    // Find syscalls with unusually high frequency (more than 3 standard deviations)
    let threshold = avg_count + 3.0 * std_dev;
    
    let high_frequency_syscalls: Vec<(String, usize)> = syscall_counts
        .into_iter()
        .filter(|(_, count)| *count as f64 > threshold && *count > 10)
        .collect();
    
    if !high_frequency_syscalls.is_empty() {
        let syscall_names: Vec<String> = high_frequency_syscalls
            .iter()
            .map(|(name, _)| name.clone())
            .collect();
        
        let related_events: Vec<SyscallEvent> = events
            .iter()
            .filter(|e| syscall_names.contains(&e.syscall_name))
            .cloned()
            .collect();
        
        let description = format!(
            "Unusually high frequency of syscalls: {}",
            syscall_names.join(", ")
        );
        
        return Some(SyscallAnomaly {
            anomaly_type: AnomalyType::UnusualFrequency,
            description,
            severity: 0.6,
            events: related_events,
        });
    }
    
    None
}

/// Detect unusual syscall parameters
fn detect_unusual_parameters(events: &[SyscallEvent]) -> Option<SyscallAnomaly> {
    // Look for suspicious parameters in security-sensitive syscalls
    let suspicious_events: Vec<&SyscallEvent> = events
        .iter()
        .filter(|e| {
            // Check for suspicious parameters in process creation
            if e.syscall_name.contains("CreateProcess") || 
               e.syscall_name.contains("NtCreateProcess") ||
               e.syscall_name.contains("execve") {
                
                // Check for suspicious command line parameters
                if let Some(SyscallParameterValue::String(cmd)) = e.get_parameter_value("CommandLine")
                    .or_else(|| e.get_parameter_value("FileName"))
                    .or_else(|| e.get_parameter_value("pathname")) {
                    
                    // Check for suspicious commands or paths
                    if cmd.contains("powershell") && cmd.contains("-enc") ||
                       cmd.contains("cmd.exe") && cmd.contains("/c") ||
                       cmd.contains("bash") && cmd.contains("-c") ||
                       cmd.contains("/tmp/") ||
                       cmd.contains("\\Temp\\") {
                        return true;
                    }
                }
            }
            
            // Check for suspicious memory permissions
            if e.syscall_name.contains("VirtualProtect") || 
               e.syscall_name.contains("NtProtectVirtualMemory") ||
               e.syscall_name.contains("mprotect") {
                
                // Check for executable memory
                if let Some(SyscallParameterValue::Integer(prot)) = e.get_parameter_value("NewProtect")
                    .or_else(|| e.get_parameter_value("Protection"))
                    .or_else(|| e.get_parameter_value("prot")) {
                    
                    // Check for executable protection (PAGE_EXECUTE or PROT_EXEC)
                    if (*prot & 0x10) != 0 || (*prot & 0x04) != 0 {
                        return true;
                    }
                }
            }
            
            false
        })
        .collect();
    
    if !suspicious_events.is_empty() {
        let description = format!(
            "Suspicious parameters detected in {} syscalls",
            suspicious_events.len()
        );
        
        return Some(SyscallAnomaly {
            anomaly_type: AnomalyType::UnusualParameters,
            description,
            severity: 0.8,
            events: suspicious_events.iter().map(|e| (*e).clone()).collect(),
        });
    }
    
    None
}

/// Detect unusual syscall sequence
fn detect_unusual_sequence(events: &[SyscallEvent]) -> Option<SyscallAnomaly> {
    // Look for suspicious sequences of syscalls
    
    // Check for memory write followed by execution
    let mut memory_write_events = Vec::new();
    let mut execute_events = Vec::new();
    
    for event in events {
        // Memory write events
        if event.syscall_name.contains("WriteProcessMemory") ||
           event.syscall_name.contains("NtWriteVirtualMemory") ||
           event.syscall_name.contains("memfd_create") {
            memory_write_events.push(event.clone());
        }
        
        // Execute events
        if event.syscall_name.contains("CreateRemoteThread") ||
           event.syscall_name.contains("NtCreateThreadEx") ||
           event.syscall_name.contains("NtQueueApcThread") {
            execute_events.push(event.clone());
        }
    }
    
    // If we have both memory writes and executes, this could be code injection
    if !memory_write_events.is_empty() && !execute_events.is_empty() {
        let mut sequence_events = memory_write_events;
        sequence_events.extend(execute_events);
        
        return Some(SyscallAnomaly {
            anomaly_type: AnomalyType::UnusualSequence,
            description: "Potential code injection detected: memory write followed by execution".to_string(),
            severity: 0.9,
            events: sequence_events,
        });
    }
    
    None
}

/// Detect potential privilege escalation
fn detect_privilege_escalation(events: &[SyscallEvent]) -> Option<SyscallAnomaly> {
    // Look for syscalls related to privilege escalation
    let priv_events: Vec<&SyscallEvent> = events
        .iter()
        .filter(|e| {
            e.syscall_name.contains("AdjustTokenPrivileges") ||
            e.syscall_name.contains("NtAdjustPrivilegesToken") ||
            e.syscall_name.contains("setuid") ||
            e.syscall_name.contains("setgid") ||
            e.syscall_name.contains("setresuid") ||
            e.syscall_name.contains("setresgid") ||
            e.syscall_name.contains("setcap")
        })
        .collect();
    
    if !priv_events.is_empty() {
        return Some(SyscallAnomaly {
            anomaly_type: AnomalyType::PrivilegeEscalation,
            description: "Potential privilege escalation detected".to_string(),
            severity: 0.9,
            events: priv_events.iter().map(|e| (*e).clone()).collect(),
        });
    }
    
    None
}

#[cfg(test)]
mod tests {
    use super::*;
    
    /// Create a test syscall event
    fn create_test_event(
        id: u64,
        syscall_id: u32,
        name: &str,
        category: SyscallCategory,
        success: bool,
        duration_ns: u64,
    ) -> SyscallEvent {
        let mut event = SyscallEvent::new(id, 1000, syscall_id);
        event.syscall_name = name.to_string();
        event.category = category;
        event.success = success;
        event.duration_ns = duration_ns;
        event
    }
    
    #[test]
    fn test_analyze_syscalls() {
        // Create test events
        let events = vec![
            create_test_event(1, 1, "open", SyscallCategory::FileSystem, true, 100),
            create_test_event(2, 2, "read", SyscallCategory::FileSystem, true, 200),
            create_test_event(3, 3, "write", SyscallCategory::FileSystem, true, 300),
            create_test_event(4, 4, "close", SyscallCategory::FileSystem, true, 100),
            create_test_event(5, 5, "socket", SyscallCategory::Network, true, 500),
            create_test_event(6, 6, "connect", SyscallCategory::Network, false, 1000),
            create_test_event(7, 7, "fork", SyscallCategory::Process, true, 2000),
            create_test_event(8, 8, "execve", SyscallCategory::Process, true, 3000),
            create_test_event(9, 9, "mmap", SyscallCategory::Memory, true, 400),
            create_test_event(10, 10, "munmap", SyscallCategory::Memory, true, 300),
        ];
        
        // Analyze syscalls
        let report = analyze_syscalls(&events);
        
        // Check basic statistics
        assert_eq!(report.total_syscalls, 10);
        assert_eq!(report.successful_syscalls, 9);
        assert_eq!(report.failed_syscalls, 1);
        
        // Check syscalls by category
        assert_eq!(report.syscalls_by_category.len(), 4);
        assert_eq!(report.syscalls_by_category.get(&SyscallCategory::FileSystem), Some(&4));
        assert_eq!(report.syscalls_by_category.get(&SyscallCategory::Network), Some(&2));
        assert_eq!(report.syscalls_by_category.get(&SyscallCategory::Process), Some(&2));
        assert_eq!(report.syscalls_by_category.get(&SyscallCategory::Memory), Some(&2));
        
        // Check security-sensitive syscalls
        assert_eq!(report.security_sensitive_syscalls.len(), 2);
        assert!(report.security_sensitive_syscalls.iter().any(|e| e.syscall_name == "fork"));
        assert!(report.security_sensitive_syscalls.iter().any(|e| e.syscall_name == "execve"));
    }
    
    #[test]
    fn test_detect_patterns() {
        // Create test events for process creation pattern
        let process_events = vec![
            create_test_event(1, 1, "fork", SyscallCategory::Process, true, 100),
            create_test_event(2, 2, "execve", SyscallCategory::Process, true, 200),
            create_test_event(3, 3, "wait4", SyscallCategory::Process, true, 300),
        ];
        
        let patterns = detect_patterns(&process_events);
        assert_eq!(patterns.len(), 1);
        assert_eq!(patterns[0].name, "Process Creation");
        
        // Create test events for network connection pattern
        let network_events = vec![
            create_test_event(1, 1, "socket", SyscallCategory::Network, true, 100),
            create_test_event(2, 2, "connect", SyscallCategory::Network, true, 200),
            create_test_event(3, 3, "send", SyscallCategory::Network, true, 300),
            create_test_event(4, 4, "recv", SyscallCategory::Network, true, 400),
            create_test_event(5, 5, "close", SyscallCategory::Network, true, 100),
        ];
        
        let patterns = detect_patterns(&network_events);
        assert_eq!(patterns.len(), 1);
        assert_eq!(patterns[0].name, "Network Connection");
    }
    
    #[test]
    fn test_detect_anomalies() {
        // Create test events for privilege escalation
        let priv_events = vec![
            create_test_event(1, 1, "open", SyscallCategory::FileSystem, true, 100),
            create_test_event(2, 2, "read", SyscallCategory::FileSystem, true, 200),
            create_test_event(3, 3, "setuid", SyscallCategory::Process, true, 300),
            create_test_event(4, 4, "setgid", SyscallCategory::Process, true, 400),
            create_test_event(5, 5, "execve", SyscallCategory::Process, true, 500),
            create_test_event(6, 6, "close", SyscallCategory::FileSystem, true, 100),
            create_test_event(7, 7, "open", SyscallCategory::FileSystem, true, 100),
            create_test_event(8, 8, "read", SyscallCategory::FileSystem, true, 200),
            create_test_event(9, 9, "write", SyscallCategory::FileSystem, true, 300),
            create_test_event(10, 10, "close", SyscallCategory::FileSystem, true, 100),
        ];
        
        let anomalies = detect_anomalies(&priv_events);
        assert_eq!(anomalies.len(), 1);
        assert_eq!(anomalies[0].anomaly_type, AnomalyType::PrivilegeEscalation);
    }
} 