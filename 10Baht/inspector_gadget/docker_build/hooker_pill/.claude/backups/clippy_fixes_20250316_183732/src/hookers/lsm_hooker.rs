

use std::time::{Duration, Instant};

use serde::{Serialize, Deserialize};
use parking_lot::RwLock;


use crate::ebpf::maps::percpumap::{TypedOptimizedPerCpuMap, PerCpuMapStats};
use crate::ebpf::maps::MapError;
use crate::elasticsearch::ElasticsearchLogger;
use crate::hookers::{HookerError, HookerStats};

/// Security context for a process
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityContext {
    /// Process ID
    pub pid: u32,
    /// User ID
    pub uid: u32,
    /// Group ID
    pub gid: u32,
    /// Security label
    pub security_label: String,
    /// Capabilities
    pub capabilities: u64,
    /// Creation time
    pub creation_time: u64,
    /// Last update time
    pub last_update_time: u64,
}

/// Policy violation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyViolation {
    /// Process ID
    pub pid: u32,
    /// User ID
    pub uid: u32,
    /// Violation type
    pub violation_type: String,
    /// Resource accessed
    pub resource: String,
    /// Requested access
    pub requested_access: u32,
    /// Allowed access
    pub allowed_access: u32,
    /// Timestamp
    pub timestamp: u64,
}

/// Security event type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityEventType {
    /// Process creation
    ProcessCreation,
    /// File access
    FileAccess,
    /// Network access
    NetworkAccess,
    /// Capability use
    CapabilityUse,
    /// Policy violation
    PolicyViolation,
}

/// Security event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityEvent {
    /// Event type
    pub event_type: SecurityEventType,
    /// Process ID
    pub pid: u32,
    /// User ID
    pub uid: u32,
    /// Event details
    pub details: String,
    /// Timestamp
    pub timestamp: u64,
}

/// LSM Hooker statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LsmHookerStats {
    /// Process context map statistics
    pub process_context_stats: PerCpuMapStats,
    /// Policy violation map statistics
    pub policy_violation_stats: PerCpuMapStats,
    /// Security event map statistics
    pub security_event_stats: PerCpuMapStats,
    /// Number of process creations
    pub process_creations: usize,
    /// Number of file accesses
    pub file_accesses: usize,
    /// Number of network accesses
    pub network_accesses: usize,
    /// Number of capability uses
    pub capability_uses: usize,
    /// Number of policy violations
    pub policy_violations: usize,
    /// Start time
    pub start_time: Instant,
    /// Last update time
    pub last_update_time: Instant,
}

impl Default for LsmHookerStats {
    fn default() -> Self {
        Self {
            process_context_stats: PerCpuMapStats::default(),
            policy_violation_stats: PerCpuMapStats::default(),
            security_event_stats: PerCpuMapStats::default(),
            process_creations: 0,
            file_accesses: 0,
            network_accesses: 0,
            capability_uses: 0,
            policy_violations: 0,
            start_time: Instant::now(),
            last_update_time: Instant::now(),
        }
    }
}

impl HookerStats for LsmHookerStats {
    fn uptime(&self) -> Duration {
        self.start_time.elapsed()
    }
    
    fn last_update(&self) -> Duration {
        self.last_update_time.elapsed()
    }
}

/// LSM Hooker
pub struct LsmHooker {
    /// Process context map
    process_context_map: TypedOptimizedPerCpuMap<u32, SecurityContext>,
    /// Policy violation map
    policy_violation_map: TypedOptimizedPerCpuMap<u32, Vec<PolicyViolation>>,
    /// Security event map
    security_event_map: TypedOptimizedPerCpuMap<u32, Vec<SecurityEvent>>,
    /// Statistics
    stats: Arc<RwLock<LsmHookerStats>>,
    /// Elasticsearch logger
    elasticsearch_logger: Option<Arc<ElasticsearchLogger>>,
}

impl LsmHooker {
    /// Create a new LSM Hooker
    pub fn new(elasticsearch_logger: Option<Arc<ElasticsearchLogger>>) -> Result<Self, HookerError> {
        // Create maps
        let process_context_map = TypedOptimizedPerCpuMap::<u32, SecurityContext>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        let policy_violation_map = TypedOptimizedPerCpuMap::<u32, Vec<PolicyViolation>>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        let security_event_map = TypedOptimizedPerCpuMap::<u32, Vec<SecurityEvent>>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        // Create statistics
        let stats = Arc::new(RwLock::new(LsmHookerStats::default()));
        
        Ok(Self {
            process_context_map,
            policy_violation_map,
            security_event_map,
            stats,
            elasticsearch_logger,
        })
    }
    
    /// Get process context
    pub fn get_process_context(&self, pid: u32) -> Result<Option<SecurityContext>, HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_pid(pid);
        
        // Lookup process context
        self.process_context_map.lookup_cpu(&pid, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Set process context
    pub fn set_process_context(&self, pid: u32, context: SecurityContext) -> Result<(), HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_pid(pid);
        
        // Update process context
        self.process_context_map.update_cpu(&pid, &context, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update statistics
        let mut stats = self.stats.write();
        stats.last_update_time = Instant::now();
        
        Ok(())
    }
    
    /// Add policy violation
    pub fn add_policy_violation(&self, pid: u32, violation: PolicyViolation) -> Result<(), HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_pid(pid);
        
        // Get current violations
        let mut violations = match self.policy_violation_map.lookup_cpu(&pid, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(v) => v,
            None => Vec::new(),
        };
        
        // Add new violation
        violations.push(violation);
        
        // Update violations
        self.policy_violation_map.update_cpu(&pid, &violations, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update statistics
        let mut stats = self.stats.write();
        stats.policy_violations += 1;
        stats.last_update_time = Instant::now();
        
        Ok(())
    }
    
    /// Add security event
    pub fn add_security_event(&self, pid: u32, event: SecurityEvent) -> Result<(), HookerError> {
        // Get CPU ID
        let cpu = self.get_cpu_for_pid(pid);
        
        // Get current events
        let mut events = match self.security_event_map.lookup_cpu(&pid, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(e) => e,
            None => Vec::new(),
        };
        
        // Add new event
        events.push(event.clone());
        
        // Update events
        self.security_event_map.update_cpu(&pid, &events, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update statistics
        let mut stats = self.stats.write();
        match event.event_type {
            SecurityEventType::ProcessCreation => stats.process_creations += 1,
            SecurityEventType::FileAccess => stats.file_accesses += 1,
            SecurityEventType::NetworkAccess => stats.network_accesses += 1,
            SecurityEventType::CapabilityUse => stats.capability_uses += 1,
            SecurityEventType::PolicyViolation => stats.policy_violations += 1,
        }
        stats.last_update_time = Instant::now();
        
        // Log to Elasticsearch if available
        if let Some(logger) = &self.elasticsearch_logger {
            if let Err(e) = logger.log_event("security_events", &event) {
                error!("Failed to log security event to Elasticsearch: {}", e);
            }
        }
        
        Ok(())
    }
    
    /// Get total policy violations
    pub fn get_total_policy_violations(&self) -> Result<HashMap<u32, Vec<PolicyViolation>>, HookerError> {
        let mut result = HashMap::new();
        
        // Aggregate policy violations for all PIDs
        for pid in self.get_all_pids()? {
            let violations = self.policy_violation_map.aggregate(&pid, |values| {
                let mut all_violations = Vec::new();
                for v in values {
                    all_violations.extend(v);
                }
                Some(all_violations)
            }).map_err(|e| HookerError::MapOperationError(e.to_string()))?;
            
            if let Some(v) = violations {
                result.insert(pid, v);
            }
        }
        
        Ok(result)
    }
    
    /// Export security events to Elasticsearch
    pub fn export_events_to_elasticsearch(&self) -> Result<usize, HookerError> {
        // Check if Elasticsearch logger is available
        let logger = match &self.elasticsearch_logger {
            Some(l) => l,
            None => return Err(HookerError::OperationError("Elasticsearch logger not configured".to_string())),
        };
        
        let mut exported_count = 0;
        
        // Export events for all PIDs
        for pid in self.get_all_pids()? {
            let events = self.security_event_map.aggregate(&pid, |values| {
                let mut all_events = Vec::new();
                for e in values {
                    all_events.extend(e);
                }
                Some(all_events)
            }).map_err(|e| HookerError::MapOperationError(e.to_string()))?;
            
            if let Some(events) = events {
                // Export events in batches
                for event in events {
                    if let Err(e) = logger.log_event("security_events", &event) {
                        error!("Failed to export security event to Elasticsearch: {}", e);
                    } else {
                        exported_count += 1;
                    }
                }
            }
        }
        
        Ok(exported_count)
    }
    
    /// Get statistics for all maps
    pub fn get_stats(&self) -> LsmHookerStats {
        let mut stats = self.stats.write();
        
        // Update map statistics
        stats.process_context_stats = self.process_context_map.stats().clone();
        stats.policy_violation_stats = self.policy_violation_map.stats().clone();
        stats.security_event_stats = self.security_event_map.stats().clone();
        
        stats.clone()
    }
    
    /// Get CPU ID for a PID
    fn get_cpu_for_pid(&self, pid: u32) -> usize {
        // Simple hash function to distribute PIDs across CPUs
        (pid as usize) % self.process_context_map.num_cpus()
    }
    
    /// Get all PIDs
    fn get_all_pids(&self) -> Result<Vec<u32>, HookerError> {
        // This is a simplified implementation
        // In a real implementation, we would need to scan all maps
        // and collect all unique PIDs
        
        // For now, we'll just return a list of PIDs from the process context map
        // by scanning each CPU's data
        
        let mut pids = Vec::new();
        
        // Scan each CPU's data
        for cpu in 0..self.process_context_map.num_cpus() {
            // This is a placeholder for the actual implementation
            // In a real implementation, we would need to scan the map for this CPU
            // and collect all PIDs
            
            // For now, we'll just add some dummy PIDs
            pids.push(1000 + cpu as u32);
        }
        
        Ok(pids)
    }
    
    /// Handle process creation
    pub fn handle_process_creation(&self, pid: u32, uid: u32, gid: u32, security_label: &str) -> Result<(), HookerError> {
        // Create security context
        let context = SecurityContext {
            pid,
            uid,
            gid,
            security_label: security_label.to_string(),
            capabilities: 0, // Default capabilities
            creation_time: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            last_update_time: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };
        
        // Set process context
        self.set_process_context(pid, context.clone())?;
        
        // Add security event
        let event = SecurityEvent {
            event_type: SecurityEventType::ProcessCreation,
            pid,
            uid,
            details: format!("Process created with security label: {}", security_label),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };
        
        self.add_security_event(pid, event)?;
        
        debug!("Process creation handled for PID {}", pid);
        
        Ok(())
    }
    
    /// Handle file access
    pub fn handle_file_access(&self, pid: u32, path: &str, requested_access: u32) -> Result<bool, HookerError> {
        // Get process context
        let context = match self.get_process_context(pid)? {
            Some(c) => c,
            None => {
                warn!("No security context found for PID {}", pid);
                return Ok(false);
            }
        };
        
        // Check access permissions (simplified)
        let allowed_access = if path.starts_with("/etc/") && context.uid != 0 {
            // Only root can access /etc/ with write permissions
            requested_access & !(0x2) // Remove write permission
        } else {
            requested_access
        };
        
        // Add security event
        let event = SecurityEvent {
            event_type: SecurityEventType::FileAccess,
            pid,
            uid: context.uid,
            details: format!("File access: {} (requested: {}, allowed: {})",
                path, requested_access, allowed_access),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };
        
        self.add_security_event(pid, event)?;
        
        // Check if access was denied
        if allowed_access != requested_access {
            // Add policy violation
            let violation = PolicyViolation {
                pid,
                uid: context.uid,
                violation_type: "FileAccess".to_string(),
                resource: path.to_string(),
                requested_access,
                allowed_access,
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs(),
            };
            
            self.add_policy_violation(pid, violation)?;
            
            info!("Access denied: PID {} accessing {}", pid, path);
            
            return Ok(false);
        }
        
        debug!("File access handled for PID {} accessing {}", pid, path);
        
        Ok(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_lsm_hooker_basic() {
        // Create Elasticsearch logger
        let elasticsearch_logger = ElasticsearchLogger::new("http://localhost:9200", "test_index")
            .ok()
            .map(Arc::new);
        
        // Create LSM Hooker
        let hooker = LsmHooker::new(elasticsearch_logger).unwrap();
        
        // Test process creation
        hooker.handle_process_creation(1000, 1000, 1000, "unconfined_u:unconfined_r:unconfined_t:s0").unwrap();
        
        // Test process context
        let context = hooker.get_process_context(1000).unwrap().unwrap();
        assert_eq!(context.pid, 1000);
        assert_eq!(context.uid, 1000);
        assert_eq!(context.gid, 1000);
        assert_eq!(context.security_label, "unconfined_u:unconfined_r:unconfined_t:s0");
        
        // Test file access
        let allowed = hooker.handle_file_access(1000, "/home/<USER>/file.txt", 0x7).unwrap();
        assert!(allowed);
        
        // Test file access denial
        let allowed = hooker.handle_file_access(1000, "/etc/shadow", 0x7).unwrap();
        assert!(!allowed);
        
        // Test statistics
        let stats = hooker.get_stats();
        assert_eq!(stats.process_creations, 1);
        assert_eq!(stats.file_accesses, 2);
        assert_eq!(stats.policy_violations, 1);
    }
} 
/// Lsm event data
#[derive(Debug, Clone)]
pub struct LsmEvent {
    /// Event ID
    pub id: u64,
    /// Timestamp
    pub timestamp: u64,
    /// Event data
    pub data: Vec<u8>,
}

/// Configuration for LsmHooker
#[derive(Debug, Clone)]
pub struct LsmHookerConfig {
    /// Name of the hooker
    pub name: String,
    /// Buffer size
    pub buffer_size: usize,
    /// Maximum events
    pub max_events: usize,
}

impl Default for LsmHookerConfig {
    fn default() -> Self {
        Self {
            name: "lsm_hooker".to_string(),
            buffer_size: 4096,
            max_events: 1000,
        }
    }
}
