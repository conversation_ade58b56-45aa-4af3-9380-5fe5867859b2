/*!
 * eBPF Program Loader
 * 
 * This module provides functionality for loading and managing eBPF programs
 * using libbpf-rs.
 */


use std::path::Path;

use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::mpsc::Sender;
use std::thread;




#[cfg(feature = "linux")]
use libbpf_rs::{
    Map, MapFlags, Object, ObjectBuilder, OpenObject, Program, ProgramAttachType, ProgramType,
    RingBuffer, RingBufferBuilder,
};

use crate::ebpf::{EbpfError, EbpfEvent, ProgramType as EbpfProgramType};

/// eBPF program loader error
#[derive(Debug, Error)]
pub enum LoaderError {
    /// eBPF program loading error
    #[error("eBPF program loading error: {0}")]
    ProgramLoading(String),
    
    /// eBPF program attachment error
    #[error("eBPF program attachment error: {0}")]
    ProgramAttachment(String),
    
    /// eBPF map error
    #[error("eBPF map error: {0}")]
    Map(String),
    
    /// eBPF event collection error
    #[error("eBPF event collection error: {0}")]
    EventCollection(String),
    
    /// Feature not available
    #[error("Feature not available: {0}")]
    FeatureNotAvailable(String),
}

/// eBPF program loader configuration
#[derive(Debug, Clone)]
pub struct LoaderConfig {
    /// Whether to verify programs before loading
    pub verify_programs: bool,
    /// Verification log level (0-4)
    pub log_level: u32,
    /// Event polling interval in milliseconds
    pub polling_interval_ms: u64,
}

impl Default for LoaderConfig {
    fn default() -> Self {
        Self {
            verify_programs: true,
            log_level: 1,
            polling_interval_ms: 100,
        }
    }
}

/// eBPF program loader
pub struct EbpfLoader<'a> {
    /// Running flag
    running: Arc<AtomicBool>,
    /// Configuration
    config: LoaderConfig,
    /// Loaded objects
    #[cfg(feature = "linux")]
    loaded_objects: HashMap<String, Object>,
    /// Ring buffer handlers
    #[cfg(feature = "linux")]
    ring_buffers: Vec<RingBuffer<'a>>,
    /// Collection thread handle
    collection_thread: Option<thread::JoinHandle<()>>,
    /// Event sender
    event_sender: Option<Sender<EbpfEvent>>,
}

impl<'a> EbpfLoader<'a> {
    /// Create a new eBPF program loader with default configuration
    pub fn new(running: Arc<AtomicBool>) -> Result<Self, EbpfError> {
        Self::with_config(running, LoaderConfig::default())
    }
    
    /// Create a new eBPF program loader with custom configuration
    pub fn with_config(running: Arc<AtomicBool>, config: LoaderConfig) -> Result<Self, EbpfError> {
        #[cfg(not(feature = "linux"))]
        {
            return Err(EbpfError::InitError(
                "eBPF is only available on Linux".to_string(),
            ));
        }
        
        #[cfg(feature = "linux")]
        {
            Ok(Self {
                running,
                config,
                loaded_objects: HashMap::new(),
                ring_buffers: Vec::new(),
                collection_thread: None,
                event_sender: None,
            })
        }
    }
    
    /// Load an eBPF object from a file
    #[cfg(feature = "linux")]
    pub fn load_object_file(&mut self, path: &Path, name: &str) -> Result<(), EbpfError> {
        if self.loaded_objects.contains_key(name) {
            return Err(EbpfError::ProgramAlreadyLoaded(name.to_string()));
        }
        
        // Build and load the object
        let mut builder = ObjectBuilder::default();
        if self.config.verify_programs {
            builder = builder.debug(self.config.log_level);
        }
        
        let obj = match builder.open_file(path) {
            Ok(open_obj) => {
                match open_obj.load() {
                    Ok(obj) => obj,
                    Err(e) => {
                        return Err(EbpfError::ProgramError(
                            format!("Failed to load eBPF object: {}", e)
                        ));
                    }
                }
            },
            Err(e) => {
                return Err(EbpfError::ProgramError(
                    format!("Failed to open eBPF object file: {}", e)
                ));
            }
        };
        
        info!("Loaded eBPF object: {}", name);
        self.loaded_objects.insert(name.to_string(), obj);
        
        Ok(())
    }
    
    /// Load an eBPF object from memory
    #[cfg(feature = "linux")]
    pub fn load_object_memory(&mut self, data: &[u8], name: &str) -> Result<(), EbpfError> {
        if self.loaded_objects.contains_key(name) {
            return Err(EbpfError::ProgramAlreadyLoaded(name.to_string()));
        }
        
        // Build and load the object
        let mut builder = ObjectBuilder::default();
        if self.config.verify_programs {
            builder = builder.debug(self.config.log_level);
        }
        
        let obj = match builder.open_memory(name, data) {
            Ok(open_obj) => {
                match open_obj.load() {
                    Ok(obj) => obj,
                    Err(e) => {
                        return Err(EbpfError::ProgramError(
                            format!("Failed to load eBPF object: {}", e)
                        ));
                    }
                }
            },
            Err(e) => {
                return Err(EbpfError::ProgramError(
                    format!("Failed to open eBPF object from memory: {}", e)
                ));
            }
        };
        
        info!("Loaded eBPF object from memory: {}", name);
        self.loaded_objects.insert(name.to_string(), obj);
        
        Ok(())
    }
    
    /// Attach a program from a loaded object
    #[cfg(feature = "linux")]
    pub fn attach_program(&mut self, object_name: &str, program_name: &str) -> Result<(), EbpfError> {
        let obj = match self.loaded_objects.get_mut(object_name) {
            Some(obj) => obj,
            None => {
                return Err(EbpfError::ProgramNotFound(
                    format!("eBPF object not found: {}", object_name)
                ));
            }
        };
        
        let prog = match obj.prog_mut(program_name) {
            Some(prog) => prog,
            None => {
                return Err(EbpfError::ProgramNotFound(
                    format!("eBPF program not found: {}", program_name)
                ));
            }
        };
        
        // Attach the program
        match prog.attach() {
            Ok(_) => {
                info!("Attached eBPF program: {}/{}", object_name, program_name);
                Ok(())
            },
            Err(e) => {
                Err(EbpfError::ProgramError(
                    format!("Failed to attach eBPF program: {}", e)
                ))
            }
        }
    }
    
    /// Set up a ring buffer for event collection
    #[cfg(feature = "linux")]
    pub fn setup_ring_buffer(
        &'a mut self,
        object_name: &str,
        map_name: &str,
        event_sender: Sender<EbpfEvent>,
        program_type: EbpfProgramType,
    ) -> Result<(), EbpfError> {
        let obj = match self.loaded_objects.get(object_name) {
            Some(obj) => obj,
            None => {
                return Err(EbpfError::ProgramNotFound(
                    format!("eBPF object not found: {}", object_name)
                ));
            }
        };
        
        let map = match obj.map(map_name) {
            Some(map) => map,
            None => {
                return Err(EbpfError::ProgramNotFound(
                    format!("eBPF map not found: {}", map_name)
                ));
            }
        };
        
        // Create a ring buffer
        let mut builder = RingBufferBuilder::new();
        
        // Add the map to the ring buffer with a callback
        let sender = event_sender.clone();
        let prog_type = program_type;
        let prog_name = object_name.to_string();
        
        builder.add(map, move |data: &[u8]| {
            // Process the event data
            if data.len() < 8 {
                error!("Received invalid event data: too short");
                return 0;
            }
            
            // Create an event from the data
            // This is a simplified example - actual implementation would depend on the data format
            let event = EbpfEvent {
                id: 0, // Will be set by the receiver
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_nanos() as u64,
                pid: 0, // Extract from data
                tid: 0, // Extract from data
                program_type: prog_type,
                program_name: prog_name.clone(),
                data: data.to_vec(),
            };
            
            // Send the event
            if let Err(e) = sender.send(event) {
                error!("Failed to send event: {}", e);
            }
            
            0
        });
        
        // Build the ring buffer
        match builder.build() {
            Ok(rb) => {
                self.ring_buffers.push(rb);
                info!("Set up ring buffer for {}/{}", object_name, map_name);
                Ok(())
            },
            Err(e) => {
                Err(EbpfError::CollectionError(
                    format!("Failed to build ring buffer: {}", e)
                ))
            }
        }
    }
    
    /// Start event collection
    pub fn start_collection(&mut self, event_sender: Sender<EbpfEvent>) -> Result<(), EbpfError> {
        #[cfg(not(feature = "linux"))]
        {
            return Err(EbpfError::InitError(
                "eBPF is only available on Linux".to_string(),
            ));
        }
        
        #[cfg(feature = "linux")]
        {
            if self.collection_thread.is_some() {
                return Ok(());
            }
            
            self.event_sender = Some(event_sender);
            
            // Create a thread for polling ring buffers
            let running = Arc::clone(&self.running);
            let polling_interval = self.config.polling_interval_ms;
            
            // Clone the ring buffers for the thread
            // Note: We can't move the ring buffers into the thread because they're borrowed
            // by self, so we need to use a different approach
            let ring_buffer_fds: Vec<i32> = self.ring_buffers.iter()
                .map(|rb| rb.fd())
                .collect();
            
            self.collection_thread = Some(thread::spawn(move || {
                info!("eBPF event collection thread started");
                
                while running.load(Ordering::SeqCst) {
                    // We can't directly poll the ring buffers here since they're not available
                    // in this thread. In a real implementation, you would use a different approach
                    // such as using a channel to communicate with the main thread.
                    
                    // Sleep for the polling interval
                    thread::sleep(Duration::from_millis(polling_interval));
                }
                
                info!("eBPF event collection thread stopped");
            }));
            
            info!("Started eBPF event collection");
            Ok(())
        }
    }
    
    /// Stop event collection
    pub fn stop_collection(&mut self) -> Result<(), EbpfError> {
        #[cfg(not(feature = "linux"))]
        {
            return Err(EbpfError::InitError(
                "eBPF is only available on Linux".to_string(),
            ));
        }
        
        #[cfg(feature = "linux")]
        {
            if self.collection_thread.is_none() {
                return Ok(());
            }
            
            // Signal the collection thread to stop
            self.running.store(false, Ordering::SeqCst);
            
            // Wait for the collection thread to finish
            if let Some(thread) = self.collection_thread.take() {
                if let Err(e) = thread.join() {
                    error!("Failed to join collection thread: {:?}", e);
                }
            }
            
            // Clean up resources
            self.event_sender = None;
            
            info!("Stopped eBPF event collection");
            Ok(())
        }
    }
    
    /// Clean up resources
    pub fn cleanup(&mut self) -> Result<(), EbpfError> {
        #[cfg(not(feature = "linux"))]
        {
            return Err(EbpfError::InitError(
                "eBPF is only available on Linux".to_string(),
            ));
        }
        
        #[cfg(feature = "linux")]
        {
            // Stop collection if running
            if self.collection_thread.is_some() {
                self.stop_collection()?;
            }
            
            // Clear ring buffers
            self.ring_buffers.clear();
            
            // Clear loaded objects
            self.loaded_objects.clear();
            
            info!("Cleaned up eBPF loader resources");
            Ok(())
        }
    }
}

impl<'a> Drop for EbpfLoader<'a> {
    fn drop(&mut self) {
        if let Err(e) = self.cleanup() {
            error!("Error cleaning up eBPF loader: {}", e);
        }
    }
} 