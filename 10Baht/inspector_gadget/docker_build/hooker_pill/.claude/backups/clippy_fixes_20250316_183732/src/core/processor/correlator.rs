/*!
 * Event Correlator Implementation
 * 
 * This module provides correlation capabilities for events, identifying
 * related events and generating higher-level events.
 */

use std::collections::{HashMap, VecDeque};


use async_trait::async_trait;

use serde_json::Value;
use tokio::sync::Mutex;


use crate::core::events::{EventType, TraceEvent};
use crate::analysis::EventData;
use crate::analysis::EventSeverity;
use crate::error::Result;
use super::EventProcessor;

/// Correlation rule
#[derive(Debug, Clone)]
pub enum CorrelationRule {
    /// Sequence of events
    Sequence {
        /// Name of the rule
        name: String,
        /// Event types to match in sequence
        event_types: Vec<EventType>,
        /// Time window in seconds
        time_window: u64,
        /// Severity of the correlated event
        severity: EventSeverity,
    },
    /// Threshold of events
    Threshold {
        /// Name of the rule
        name: String,
        /// Event type to match
        event_type: EventType,
        /// Threshold count
        threshold: usize,
        /// Time window in seconds
        time_window: u64,
        /// Severity of the correlated event
        severity: EventSeverity,
    },
    /// Absence of events
    Absence {
        /// Name of the rule
        name: String,
        /// Event type to check for absence
        event_type: EventType,
        /// Time window in seconds
        time_window: u64,
        /// Severity of the correlated event
        severity: EventSeverity,
    },
}

/// Event correlator configuration
#[derive(Debug, Clone)]
pub struct EventCorrelatorConfig {
    /// Correlation rules
    pub rules: Vec<CorrelationRule>,
    /// Maximum events to keep in memory
    pub max_events: usize,
    /// Maximum time to keep events in memory (in seconds)
    pub max_time: u64,
}

impl Default for EventCorrelatorConfig {
    fn default() -> Self {
        Self {
            rules: Vec::new(),
            max_events: 10000,
            max_time: 3600,
        }
    }
}

/// Event correlator
pub struct EventCorrelator {
    /// Configuration
    config: EventCorrelatorConfig,
    /// Event history
    history: Arc<Mutex<VecDeque<TraceEvent>>>,
    /// Sequence state
    sequence_state: Arc<Mutex<HashMap<String, Vec<TraceEvent>>>>,
    /// Threshold state
    threshold_state: Arc<Mutex<HashMap<String, Vec<TraceEvent>>>>,
    /// Absence state
    absence_state: Arc<Mutex<HashMap<String, u64>>>,
}

impl EventCorrelator {
    /// Create a new event correlator
    pub fn new(config: EventCorrelatorConfig) -> Self {
        let correlator = Self {
            config,
            history: Arc::new(Mutex::new(VecDeque::new())),
            sequence_state: Arc::new(Mutex::new(HashMap::new())),
            threshold_state: Arc::new(Mutex::new(HashMap::new())),
            absence_state: Arc::new(Mutex::new(HashMap::new())),
        };
        
        // Start background cleanup task
        correlator.start_cleanup_task();
        
        correlator
    }
    
    /// Create a new event correlator with default configuration
    pub fn with_defaults() -> Self {
        Self::new(EventCorrelatorConfig::default())
    }
    
    /// Start background cleanup task
    fn start_cleanup_task(&self) {
        let history = self.history.clone();
        let sequence_state = self.sequence_state.clone();
        let threshold_state = self.threshold_state.clone();
        let absence_state = self.absence_state.clone();
        let max_time = self.config.max_time;
        
        tokio::spawn(async move {
            loop {
                // Sleep for a while
                tokio::time::sleep(Duration::from_secs(60)).await;
                
                // Get current time
                let now = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                
                // Clean up history
                {
                    let mut history = history.lock().await;
                    while let Some(event) = history.front() {
                        if now - event.timestamp > max_time {
                            history.pop_front();
                        } else {
                            break;
                        }
                    }
                }
                
                // Clean up sequence state
                {
                    let mut sequence_state = sequence_state.lock().await;
                    let mut to_remove = Vec::new();
                    
                    for (key, events) in sequence_state.iter() {
                        if let Some(event) = events.first() {
                            if now - event.timestamp > max_time {
                                to_remove.push(key.clone());
                            }
                        }
                    }
                    
                    for key in to_remove {
                        sequence_state.remove(&key);
                    }
                }
                
                // Clean up threshold state
                {
                    let mut threshold_state = threshold_state.lock().await;
                    let mut to_remove = Vec::new();
                    
                    for (key, events) in threshold_state.iter() {
                        if let Some(event) = events.first() {
                            if now - event.timestamp > max_time {
                                to_remove.push(key.clone());
                            }
                        }
                    }
                    
                    for key in to_remove {
                        threshold_state.remove(&key);
                    }
                }
                
                // Clean up absence state
                {
                    let mut absence_state = absence_state.lock().await;
                    let mut to_remove = Vec::new();
                    
                    for (key, timestamp) in absence_state.iter() {
                        if now - *timestamp > max_time {
                            to_remove.push(key.clone());
                        }
                    }
                    
                    for key in to_remove {
                        absence_state.remove(&key);
                    }
                }
            }
        });
    }
    
    /// Correlate an event
    async fn correlate_event(&self, event: &TraceEvent) -> Result<Vec<TraceEvent>> {
        let mut correlated_events = Vec::new();
        
        // Add event to history
        {
            let mut history = self.history.lock().await;
            history.push_back(event.clone());
            
            // Trim history if needed
            while history.len() > self.config.max_events {
                history.pop_front();
            }
        }
        
        // Apply correlation rules
        for rule in &self.config.rules {
            match rule {
                CorrelationRule::Sequence { name, event_types, time_window, severity } => {
                    if let Some(correlated) = self.apply_sequence_rule(name, event_types, *time_window, *severity, event).await? {
                        correlated_events.push(correlated);
                    }
                }
                CorrelationRule::Threshold { name, event_type, threshold, time_window, severity } => {
                    if let Some(correlated) = self.apply_threshold_rule(name, event_type, *threshold, *time_window, *severity, event).await? {
                        correlated_events.push(correlated);
                    }
                }
                CorrelationRule::Absence { name, event_type, time_window, severity } => {
                    if let Some(correlated) = self.apply_absence_rule(name, event_type, *time_window, *severity, event).await? {
                        correlated_events.push(correlated);
                    }
                }
            }
        }
        
        Ok(correlated_events)
    }
    
    /// Apply a sequence rule
    async fn apply_sequence_rule(
        &self,
        name: &str,
        event_types: &[EventType],
        time_window: u64,
        severity: EventSeverity,
        event: &TraceEvent,
    ) -> Result<Option<TraceEvent>> {
        // Check if this event matches the first event type in the sequence
        if event_types.is_empty() || event.event_type != event_types[0] {
            // Check if this event matches the next event type in an existing sequence
            let mut sequence_state = self.sequence_state.lock().await;
            
            if let Some(events) = sequence_state.get_mut(name) {
                // Check if this event matches the next event type in the sequence
                let next_index = events.len();
                if next_index < event_types.len() && event.event_type == event_types[next_index] {
                    // Check time window
                    if event.timestamp - events[0].timestamp <= time_window {
                        // Add event to sequence
                        events.push(event.clone());
                        
                        // Check if sequence is complete
                        if events.len() == event_types.len() {
                            // Create correlated event
                            let correlated = self.create_correlated_event(
                                name,
                                EventType::Custom(format!("Sequence:{}", name)),
                                severity,
                                events,
                            );
                            
                            // Remove sequence
                            sequence_state.remove(name);
                            
                            return Ok(Some(correlated));
                        }
                    } else {
                        // Time window exceeded, remove sequence
                        sequence_state.remove(name);
                    }
                }
            }
            
            // Not part of a sequence
            return Ok(None);
        }
        
        // Start a new sequence
        let mut sequence_state = self.sequence_state.lock().await;
        sequence_state.insert(name.to_string(), vec![event.clone()]);
        
        // If the sequence has only one event, it's already complete
        if event_types.len() == 1 {
            // Create correlated event
            let events = sequence_state.remove(name).unwrap();
            let correlated = self.create_correlated_event(
                name,
                EventType::Custom(format!("Sequence:{}", name)),
                severity,
                &events,
            );
            
            return Ok(Some(correlated));
        }
        
        Ok(None)
    }
    
    /// Apply a threshold rule
    async fn apply_threshold_rule(
        &self,
        name: &str,
        event_type: &EventType,
        threshold: usize,
        time_window: u64,
        severity: EventSeverity,
        event: &TraceEvent,
    ) -> Result<Option<TraceEvent>> {
        // Check if this event matches the event type
        if event.event_type != *event_type {
            return Ok(None);
        }
        
        // Add event to threshold state
        let mut threshold_state = self.threshold_state.lock().await;
        let events = threshold_state.entry(name.to_string()).or_insert_with(Vec::new);
        
        // Add event
        events.push(event.clone());
        
        // Remove events outside time window
        let now = event.timestamp;
        events.retain(|e| now - e.timestamp <= time_window);
        
        // Check if threshold is reached
        if events.len() >= threshold {
            // Create correlated event
            let correlated = self.create_correlated_event(
                name,
                EventType::Custom(format!("Threshold:{}", name)),
                severity,
                events,
            );
            
            // Reset threshold state
            threshold_state.remove(name);
            
            Ok(Some(correlated))
        } else {
            Ok(None)
        }
    }
    
    /// Apply an absence rule
    async fn apply_absence_rule(
        &self,
        name: &str,
        event_type: &EventType,
        time_window: u64,
        severity: EventSeverity,
        event: &TraceEvent,
    ) -> Result<Option<TraceEvent>> {
        // Check if this event matches the event type
        if event.event_type == *event_type {
            // Update absence state
            let mut absence_state = self.absence_state.lock().await;
            absence_state.insert(name.to_string(), event.timestamp);
            return Ok(None);
        }
        
        // Check if we need to generate an absence event
        let absence_state = self.absence_state.lock().await;
        let last_seen = absence_state.get(name);
        
        if let Some(last_seen) = last_seen {
            // Check if time window has passed
            if event.timestamp - *last_seen > time_window {
                // Create correlated event
                let correlated = TraceEvent::now(
                    EventType::Custom(format!("Absence:{}", name)),
                    EventData::Custom(serde_json::json!({
                        "rule": name,
                        "event_type": format!("{:?}", event_type),
                        "time_window": time_window,
                        "last_seen": last_seen,
                    })),
                    event.pid,
                    event.uid,
                    severity,
                    "correlation",
                    vec!["correlation".to_string(), "absence".to_string()],
                );
                
                return Ok(Some(correlated));
            }
        }
        
        Ok(None)
    }
    
    /// Create a correlated event
    fn create_correlated_event(
        &self,
        name: &str,
        event_type: EventType,
        severity: EventSeverity,
        events: &[TraceEvent],
    ) -> TraceEvent {
        // Get the first event for PID and UID
        let first_event = &events[0];
        
        // Create event data
        let mut data = serde_json::Map::new();
        data.insert("rule".to_string(), Value::String(name.to_string()));
        data.insert("count".to_string(), Value::Number(events.len().into()));
        
        let event_ids: Vec<Value> = events.iter()
            .map(|e| Value::String(e.id.clone()))
            .collect();
        data.insert("event_ids".to_string(), Value::Array(event_ids));
        
        // Create correlated event
        TraceEvent::now(
            event_type,
            EventData::Custom(Value::Object(data)),
            first_event.pid,
            first_event.uid,
            severity,
            "correlation",
            vec!["correlation".to_string()],
        )
    }
}

#[async_trait]
impl EventProcessor for EventCorrelator {
    async fn process(&self, event: TraceEvent) -> Result<Option<TraceEvent>> {
        // Correlate event
        let correlated_events = self.correlate_event(&event).await?;
        
        // If we have correlated events, return the first one
        // and add the rest to the history
        if !correlated_events.is_empty() {
            let mut history = self.history.lock().await;
            
            for correlated in correlated_events.iter().skip(1) {
                history.push_back(correlated.clone());
            }
            
            debug!("Generated correlated event: {}", correlated_events[0].id);
            Ok(Some(correlated_events[0].clone()))
        } else {
            // No correlated events, return the original event
            Ok(Some(event))
        }
    }
    
    async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
        let mut result = Vec::with_capacity(events.len());
        
        for event in events {
            // Correlate event
            let correlated_events = self.correlate_event(&event).await?;
            
            // Add original event
            result.push(event);
            
            // Add correlated events
            result.extend(correlated_events);
        }
        
        debug!("Processed batch of {} events, generated {} correlated events", 
               events.len(), result.len() - events.len());
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{EventType, TraceEvent};
use crate::analysis::EventData;
use crate::analysis::EventSeverity;
    
    #[tokio::test]
    async fn test_sequence_correlation() {
        // Create a correlator with a sequence rule
        let config = EventCorrelatorConfig {
            rules: vec![
                CorrelationRule::Sequence {
                    name: "test_sequence".to_string(),
                    event_types: vec![
                        EventType::Custom("step1".to_string()),
                        EventType::Custom("step2".to_string()),
                        EventType::Custom("step3".to_string()),
                    ],
                    time_window: 60,
                    severity: EventSeverity::Warning,
                },
            ],
            max_events: 1000,
            max_time: 3600,
        };
        let correlator = EventCorrelator::new(config);
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("step1".to_string()),
            EventData::Custom(serde_json::json!({"step": 1})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("step2".to_string()),
            EventData::Custom(serde_json::json!({"step": 2})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event3 = TraceEvent::now(
            EventType::Custom("step3".to_string()),
            EventData::Custom(serde_json::json!({"step": 3})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Process events
        let result1 = correlator.process(event1.clone()).await.unwrap().unwrap();
        let result2 = correlator.process(event2.clone()).await.unwrap().unwrap();
        let result3 = correlator.process(event3.clone()).await.unwrap().unwrap();
        
        // Check results
        assert_eq!(result1.id, event1.id);
        assert_eq!(result2.id, event2.id);
        
        // The third event should trigger a correlation
        assert_ne!(result3.id, event3.id);
        assert_eq!(result3.severity, EventSeverity::Warning);
        
        if let EventType::Custom(name) = &result3.event_type {
            assert_eq!(name, "Sequence:test_sequence");
        } else {
            panic!("Expected custom event type");
        }
        
        if let EventData::Custom(data) = &result3.data {
            assert_eq!(data["rule"], "test_sequence");
            assert_eq!(data["count"], 3);
            assert!(data["event_ids"].is_array());
            assert_eq!(data["event_ids"].as_array().unwrap().len(), 3);
        } else {
            panic!("Expected custom event data");
        }
    }
    
    #[tokio::test]
    async fn test_threshold_correlation() {
        // Create a correlator with a threshold rule
        let config = EventCorrelatorConfig {
            rules: vec![
                CorrelationRule::Threshold {
                    name: "test_threshold".to_string(),
                    event_type: EventType::Custom("login_failure".to_string()),
                    threshold: 3,
                    time_window: 60,
                    severity: EventSeverity::Warning,
                },
            ],
            max_events: 1000,
            max_time: 3600,
        };
        let correlator = EventCorrelator::new(config);
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("login_failure".to_string()),
            EventData::Custom(serde_json::json!({"user": "test"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("login_failure".to_string()),
            EventData::Custom(serde_json::json!({"user": "test"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event3 = TraceEvent::now(
            EventType::Custom("login_failure".to_string()),
            EventData::Custom(serde_json::json!({"user": "test"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Process events
        let result1 = correlator.process(event1.clone()).await.unwrap().unwrap();
        let result2 = correlator.process(event2.clone()).await.unwrap().unwrap();
        let result3 = correlator.process(event3.clone()).await.unwrap().unwrap();
        
        // Check results
        assert_eq!(result1.id, event1.id);
        assert_eq!(result2.id, event2.id);
        
        // The third event should trigger a correlation
        assert_ne!(result3.id, event3.id);
        assert_eq!(result3.severity, EventSeverity::Warning);
        
        if let EventType::Custom(name) = &result3.event_type {
            assert_eq!(name, "Threshold:test_threshold");
        } else {
            panic!("Expected custom event type");
        }
        
        if let EventData::Custom(data) = &result3.data {
            assert_eq!(data["rule"], "test_threshold");
            assert_eq!(data["count"], 3);
            assert!(data["event_ids"].is_array());
            assert_eq!(data["event_ids"].as_array().unwrap().len(), 3);
        } else {
            panic!("Expected custom event data");
        }
    }
} 