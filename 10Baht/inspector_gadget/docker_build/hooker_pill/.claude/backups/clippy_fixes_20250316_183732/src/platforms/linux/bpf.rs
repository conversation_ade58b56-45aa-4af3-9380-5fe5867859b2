/*!
 * BPF functionality for Linux platform
 * 
 * This module provides BPF (Berkeley Packet Filter) functionality for the Linux platform.
 */

use std::fs;
use std::path::Path;

use crate::error::{Error, Result};

/// BPF program type
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum BpfProgramType {
    /// Kprobe BPF program
    Kprobe,
    /// Tracepoint BPF program
    Tracepoint,
    /// Perf event BPF program
    PerfEvent,
    /// Socket filter BPF program
    SocketFilter,
    /// XDP BPF program
    Xdp,
}

/// BPF program
pub struct BpfProgram {
    /// Program type
    program_type: BpfProgramType,
    /// Program name
    name: String,
    /// Program file descriptor
    fd: i32,
}

impl BpfProgram {
    /// Create a new BPF program
    pub fn new(program_type: BpfProgramType, name: impl Into<String>, fd: i32) -> Self {
        BpfProgram {
            program_type,
            name: name.into(),
            fd,
        }
    }
    
    /// Get the program type
    pub fn program_type(&self) -> BpfProgramType {
        self.program_type
    }
    
    /// Get the program name
    pub fn name(&self) -> &str {
        &self.name
    }
    
    /// Get the program file descriptor
    pub fn fd(&self) -> i32 {
        self.fd
    }
}

/// BPF map type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BpfMapType {
    /// Hash map
    Hash,
    /// Array map
    Array,
    /// Program array map
    ProgramArray,
    /// Perf event array map
    PerfEventArray,
    /// Stack trace map
    StackTrace,
}

/// BPF map
pub struct BpfMap {
    /// Map type
    map_type: BpfMapType,
    /// Map name
    name: String,
    /// Map file descriptor
    fd: i32,
    /// Key size in bytes
    key_size: usize,
    /// Value size in bytes
    value_size: usize,
    /// Maximum number of entries
    max_entries: usize,
}

impl BpfMap {
    /// Create a new BPF map
    pub fn new(
        map_type: BpfMapType,
        name: impl Into<String>,
        fd: i32,
        key_size: usize,
        value_size: usize,
        max_entries: usize,
    ) -> Self {
        BpfMap {
            map_type,
            name: name.into(),
            fd,
            key_size,
            value_size,
            max_entries,
        }
    }
    
    /// Get the map type
    pub fn map_type(&self) -> BpfMapType {
        self.map_type
    }
    
    /// Get the map name
    pub fn name(&self) -> &str {
        &self.name
    }
    
    /// Get the map file descriptor
    pub fn fd(&self) -> i32 {
        self.fd
    }
    
    /// Get the key size
    pub fn key_size(&self) -> usize {
        self.key_size
    }
    
    /// Get the value size
    pub fn value_size(&self) -> usize {
        self.value_size
    }
    
    /// Get the maximum number of entries
    pub fn max_entries(&self) -> usize {
        self.max_entries
    }
}

/// Check if BPF is supported on the system
pub fn is_bpf_supported() -> bool {
    // Check if the BPF filesystem is mounted
    if Path::new("/sys/fs/bpf").exists() {
        return true;
    }
    
    // Check if the kernel has BPF syscall
    if let Ok(content) = fs::read_to_string("/proc/kallsyms") {
        if content.contains("bpf_") {
            return true;
        }
    }
    
    false
}

/// Get the BPF features supported by the kernel
pub fn get_bpf_features() -> Result<Vec<String>> {
    let mut features = Vec::new();
    
    // Check for various BPF features
    if Path::new("/sys/fs/bpf").exists() {
        features.push("bpf-fs".to_string());
    }
    
    if Path::new("/sys/kernel/debug/tracing").exists() {
        features.push("tracing".to_string());
    }
    
    if let Ok(content) = fs::read_to_string("/proc/kallsyms") {
        if content.contains("bpf_prog_load") {
            features.push("prog-load".to_string());
        }
        
        if content.contains("bpf_map_create") {
            features.push("map-create".to_string());
        }
        
        if content.contains("bpf_attach_kprobe") {
            features.push("kprobe".to_string());
        }
        
        if content.contains("bpf_attach_tracepoint") {
            features.push("tracepoint".to_string());
        }
    }
    
    Ok(features)
}

/// Get the kernel version
pub fn get_kernel_version() -> Result<String> {
    let output = Command::new("uname")
        .arg("-r")
        .output()
        .map_err(|e| Error::Process(format!("Failed to execute uname: {}", e)))?;
    
    let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
    Ok(version)
}

/// Check if the kernel version supports BPF
pub fn is_kernel_version_supported(version: &str) -> bool {
    // BPF was introduced in Linux 3.18, but many features were added in 4.x
    let parts: Vec<&str> = version.split('.').collect();
    
    if parts.is_empty() {
        return false;
    }
    
    if let Ok(major) = parts[0].parse::<u32>() {
        if major >= 4 {
            return true;
        }
        
        if major == 3 && parts.len() > 1 {
            if let Ok(minor) = parts[1].parse::<u32>() {
                return minor >= 18;
            }
        }
    }
    
    false
} 