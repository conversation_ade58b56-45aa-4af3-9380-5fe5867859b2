/*!
 * Platform-specific implementations for Inspector Gadget
 * 
 * This module contains platform-specific code for different operating systems.
 */

use crate::error::{<PERSON><PERSON><PERSON>, Result};
use crate::core::Tracer;


pub mod linux;
pub mod windows;

#[cfg(test)]
mod tests;

/// Supported platforms for binary analysis
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum PlatformType {
    /// Linux platform
    Linux,
    /// Windows platform
    Windows,
    /// Unknown platform
    Unknown,
}

impl fmt::Display for PlatformType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            PlatformType::Linux => write!(f, "Linux"),
            PlatformType::Windows => write!(f, "Windows"),
            PlatformType::Unknown => write!(f, "Unknown"),
        }
    }
}

/// Platform trait for platform-specific operations
pub trait Platform: Send + Sync {
    /// Get the platform type
    fn platform_type(&self) -> PlatformType;
    
    /// Get the platform name
    fn name(&self) -> &'static str;
    
    /// Check if the platform is supported on the current system
    fn is_supported(&self) -> bool;
    
    /// Get platform-specific information
    fn info(&self) -> String;
}

/// Initialize the platform-specific tracer
pub fn init_platform_tracer() -> Result<Box<dyn Tracer>> {
    #[cfg(target_os = "linux")]
    {
        log::info!("Initializing Linux tracer");
        return linux::init_tracer();
    }
    
    #[cfg(target_os = "windows")]
    {
        log::info!("Initializing Windows tracer");
        return windows::init_tracer();
    }
    
    #[cfg(not(any(target_os = "linux", target_os = "windows")))]
    {
        return Err(crate::error::Error::UnsupportedPlatform(
            format!("Unsupported platform: {}", std::env::consts::OS)
        ));
    }
}

/// Detect the current platform
pub fn detect_platform() -> PlatformType {
    #[cfg(target_os = "linux")]
    return PlatformType::Linux;
    
    #[cfg(target_os = "windows")]
    return PlatformType::Windows;
    
    #[cfg(not(any(target_os = "linux", target_os = "windows")))]
    return PlatformType::Unknown;
}

/// Get a platform instance for the current system
pub fn current_platform() -> Box<dyn Platform> {
    match detect_platform() {
        PlatformType::Linux => Box::new(linux::LinuxPlatform::new()),
        PlatformType::Windows => Box::new(windows::WindowsPlatform::new()),
        PlatformType::Unknown => Box::new(UnknownPlatform {}),
    }
}

/// Unknown platform implementation
struct UnknownPlatform {}

impl Platform for UnknownPlatform {
    fn platform_type(&self) -> PlatformType {
        PlatformType::Unknown
    }
    
    fn name(&self) -> &'static str {
        "Unknown"
    }
    
    fn is_supported(&self) -> bool {
        false
    }
    
    fn info(&self) -> String {
        format!("Unsupported platform: {}", std::env::consts::OS)
    }
} 