
use std::error::Error;



use serde::{Serialize, Deserialize};
use reqwest::{Client, ClientBuilder};


/// Elasticsearch error
#[derive(Debug)]
pub enum ElasticsearchError {
    /// Connection error
    ConnectionError(String),
    /// Authentication error
    AuthenticationError(String),
    /// Index error
    IndexError(String),
    /// Query error
    QueryError(String),
    /// Serialization error
    SerializationError(String),
    /// HTTP error
    HttpError(String),
}

impl fmt::Display for ElasticsearchError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ElasticsearchError::ConnectionError(msg) => write!(f, "Connection error: {}", msg),
            ElasticsearchError::AuthenticationError(msg) => write!(f, "Authentication error: {}", msg),
            ElasticsearchError::IndexError(msg) => write!(f, "Index error: {}", msg),
            ElasticsearchError::QueryError(msg) => write!(f, "Query error: {}", msg),
            ElasticsearchError::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
            ElasticsearchError::HttpError(msg) => write!(f, "HTTP error: {}", msg),
        }
    }
}

impl Error for ElasticsearchError {}

/// Elasticsearch logger
pub struct ElasticsearchLogger {
    /// Elasticsearch URL
    url: String,
    /// Index prefix
    index_prefix: String,
    /// HTTP client
    client: Client,
    /// Bulk size
    bulk_size: usize,
    /// Bulk queue
    bulk_queue: Arc<parking_lot::RwLock<Vec<String>>>,
}

impl ElasticsearchLogger {
    /// Create a new Elasticsearch logger
    pub fn new(url: &str, index_prefix: &str) -> Result<Self, ElasticsearchError> {
        // Create HTTP client
        let client = ClientBuilder::new()
            .timeout(Duration::from_secs(10))
            .build()
            .map_err(|e| ElasticsearchError::ConnectionError(e.to_string()))?;
        
        Ok(Self {
            url: url.to_string(),
            index_prefix: index_prefix.to_string(),
            client,
            bulk_size: 1000,
            bulk_queue: Arc::new(parking_lot::RwLock::new(Vec::with_capacity(1000))),
        })
    }
    
    /// Create a new Elasticsearch logger with authentication
    pub fn new_with_auth(url: &str, index_prefix: &str, username: &str, password: &str) -> Result<Self, ElasticsearchError> {
        // Create HTTP client with authentication
        let client = ClientBuilder::new()
            .timeout(Duration::from_secs(10))
            .basic_auth(username, Some(password))
            .build()
            .map_err(|e| ElasticsearchError::ConnectionError(e.to_string()))?;
        
        Ok(Self {
            url: url.to_string(),
            index_prefix: index_prefix.to_string(),
            client,
            bulk_size: 1000,
            bulk_queue: Arc::new(parking_lot::RwLock::new(Vec::with_capacity(1000))),
        })
    }
    
    /// Set bulk size
    pub fn set_bulk_size(&mut self, bulk_size: usize) {
        self.bulk_size = bulk_size;
    }
    
    /// Log an event to Elasticsearch
    pub fn log_event<T: Serialize>(&self, index_suffix: &str, event: &T) -> Result<(), ElasticsearchError> {
        // Create index name
        let index_name = format!("{}-{}", self.index_prefix, index_suffix);
        
        // Serialize event
        let event_json = serde_json::to_string(event)
            .map_err(|e| ElasticsearchError::SerializationError(e.to_string()))?;
        
        // Create index document
        let index_doc = format!(
            "{{ \"index\": {{ \"_index\": \"{}\" }} }}\n{}",
            index_name, event_json
        );
        
        // Add to bulk queue
        let mut queue = self.bulk_queue.write();
        queue.push(index_doc);
        
        // Check if we need to flush
        if queue.len() >= self.bulk_size {
            drop(queue); // Release lock before flushing
            self.flush_bulk_queue()?;
        }
        
        Ok(())
    }
    
    /// Flush bulk queue
    pub fn flush_bulk_queue(&self) -> Result<(), ElasticsearchError> {
        // Get bulk queue
        let mut queue = self.bulk_queue.write();
        
        // Check if queue is empty
        if queue.is_empty() {
            return Ok(());
        }
        
        // Create bulk request body
        let body = queue.join("\n") + "\n";
        
        // Clear queue
        queue.clear();
        
        // Release lock before sending request
        drop(queue);
        
        // Send bulk request
        let url = format!("{}/{}/_bulk", self.url, self.index_prefix);
        
        // Send request asynchronously
        let client = self.client.clone();
        let url_clone = url.clone();
        
        // Use tokio to run the async request
        tokio::spawn(async move {
            match client.post(&url_clone)
                .header("Content-Type", "application/x-ndjson")
                .body(body)
                .send()
                .await {
                Ok(response) => {
                    if !response.status().is_success() {
                        let status = response.status();
                        let text = response.text().await.unwrap_or_default();
                        error!("Elasticsearch bulk request failed: {} - {}", status, text);
                    } else {
                        debug!("Elasticsearch bulk request succeeded");
                    }
                }
                Err(e) => {
                    error!("Elasticsearch bulk request error: {}", e);
                }
            }
        });
        
        Ok(())
    }
    
    /// Check if Elasticsearch is available
    pub async fn check_availability(&self) -> Result<bool, ElasticsearchError> {
        // Send request to Elasticsearch
        match self.client.get(&self.url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    Ok(true)
                } else {
                    let status = response.status();
                    let text = response.text().await.unwrap_or_default();
                    Err(ElasticsearchError::ConnectionError(format!("{} - {}", status, text)))
                }
            }
            Err(e) => {
                Err(ElasticsearchError::ConnectionError(e.to_string()))
            }
        }
    }
    
    /// Create index template
    pub async fn create_index_template(&self, template_name: &str, template: &str) -> Result<(), ElasticsearchError> {
        // Send request to Elasticsearch
        let url = format!("{}/_template/{}", self.url, template_name);
        
        match self.client.put(&url)
            .header("Content-Type", "application/json")
            .body(template.to_string())
            .send()
            .await {
            Ok(response) => {
                if response.status().is_success() {
                    Ok(())
                } else {
                    let status = response.status();
                    let text = response.text().await.unwrap_or_default();
                    Err(ElasticsearchError::IndexError(format!("{} - {}", status, text)))
                }
            }
            Err(e) => {
                Err(ElasticsearchError::ConnectionError(e.to_string()))
            }
        }
    }
} 