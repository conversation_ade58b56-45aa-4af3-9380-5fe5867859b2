/*!
 * Process information for Linux platform
 * 
 * This module provides functionality for accessing process information on Linux.
 */

use std::fs;



use crate::error::{<PERSON><PERSON><PERSON>, Result};

/// Process information
#[derive(Debug, Clone)]
pub struct ProcessInfo {
    /// Process ID
    pub pid: u32,
    /// Parent process ID
    pub ppid: u32,
    /// Process name
    pub name: String,
    /// Process state
    pub state: char,
    /// Process command line
    pub cmdline: String,
    /// Process executable path
    pub exe: String,
    /// Process current working directory
    pub cwd: String,
    /// Process environment variables
    pub environ: HashMap<String, String>,
    /// Process user ID
    pub uid: u32,
    /// Process group ID
    pub gid: u32,
    /// Process memory usage (RSS in KB)
    pub memory_usage: u64,
    /// Process CPU usage (percentage)
    pub cpu_usage: f64,
    /// Process start time (seconds since boot)
    pub start_time: u64,
    /// Process threads
    pub threads: Vec<u32>,
    /// Process open files
    pub open_files: Vec<String>,
}

impl ProcessInfo {
    /// Create a new process information instance
    pub fn new(pid: u32) -> Self {
        ProcessInfo {
            pid,
            ppid: 0,
            name: String::new(),
            state: '?',
            cmdline: String::new(),
            exe: String::new(),
            cwd: String::new(),
            environ: HashMap::new(),
            uid: 0,
            gid: 0,
            memory_usage: 0,
            cpu_usage: 0.0,
            start_time: 0,
            threads: Vec::new(),
            open_files: Vec::new(),
        }
    }
    
    /// Get the process information for a process ID
    pub fn get(pid: u32) -> Result<Self> {
        let mut info = ProcessInfo::new(pid);
        
        // Read process status
        info.read_status()?;
        
        // Read process command line
        info.read_cmdline()?;
        
        // Read process executable
        info.read_exe()?;
        
        // Read process current working directory
        info.read_cwd()?;
        
        // Read process environment variables
        info.read_environ()?;
        
        // Read process threads
        info.read_threads()?;
        
        // Read process open files
        info.read_open_files()?;
        
        Ok(info)
    }
    
    /// Read process status
    fn read_status(&mut self) -> Result<()> {
        let status_path = format!("/proc/{}/status", self.pid);
        let status = fs::read_to_string(&status_path)
            .map_err(|e| Error::Process(format!("Failed to read process status: {}", e)))?;
        
        for line in status.lines() {
            let parts: Vec<&str> = line.splitn(2, ':').collect();
            if parts.len() != 2 {
                continue;
            }
            
            let key = parts[0].trim();
            let value = parts[1].trim();
            
            match key {
                "Name" => self.name = value.to_string(),
                "State" => {
                    if !value.is_empty() {
                        self.state = value.chars().next().unwrap_or('?');
                    }
                },
                "PPid" => {
                    if let Ok(ppid) = value.parse::<u32>() {
                        self.ppid = ppid;
                    }
                },
                "Uid" => {
                    let uids: Vec<&str> = value.split_whitespace().collect();
                    if !uids.is_empty() {
                        if let Ok(uid) = uids[0].parse::<u32>() {
                            self.uid = uid;
                        }
                    }
                },
                "Gid" => {
                    let gids: Vec<&str> = value.split_whitespace().collect();
                    if !gids.is_empty() {
                        if let Ok(gid) = gids[0].parse::<u32>() {
                            self.gid = gid;
                        }
                    }
                },
                "VmRSS" => {
                    let parts: Vec<&str> = value.split_whitespace().collect();
                    if !parts.is_empty() {
                        if let Ok(rss) = parts[0].parse::<u64>() {
                            self.memory_usage = rss;
                        }
                    }
                },
                _ => {}
            }
        }
        
        Ok(())
    }
    
    /// Read process command line
    fn read_cmdline(&mut self) -> Result<()> {
        let cmdline_path = format!("/proc/{}/cmdline", self.pid);
        let mut cmdline = fs::read_to_string(&cmdline_path)
            .map_err(|e| Error::Process(format!("Failed to read process command line: {}", e)))?;
        
        // Replace null bytes with spaces
        cmdline = cmdline.replace('\0', " ").trim().to_string();
        
        self.cmdline = cmdline;
        
        Ok(())
    }
    
    /// Read process executable
    fn read_exe(&mut self) -> Result<()> {
        let exe_path = format!("/proc/{}/exe", self.pid);
        let exe = fs::read_link(&exe_path)
            .map_err(|e| Error::Process(format!("Failed to read process executable: {}", e)))?;
        
        self.exe = exe.to_string_lossy().to_string();
        
        Ok(())
    }
    
    /// Read process current working directory
    fn read_cwd(&mut self) -> Result<()> {
        let cwd_path = format!("/proc/{}/cwd", self.pid);
        let cwd = fs::read_link(&cwd_path)
            .map_err(|e| Error::Process(format!("Failed to read process current working directory: {}", e)))?;
        
        self.cwd = cwd.to_string_lossy().to_string();
        
        Ok(())
    }
    
    /// Read process environment variables
    fn read_environ(&mut self) -> Result<()> {
        let environ_path = format!("/proc/{}/environ", self.pid);
        let environ = fs::read_to_string(&environ_path)
            .map_err(|e| Error::Process(format!("Failed to read process environment variables: {}", e)))?;
        
        for var in environ.split('\0') {
            if var.is_empty() {
                continue;
            }
            
            let parts: Vec<&str> = var.splitn(2, '=').collect();
            if parts.len() != 2 {
                continue;
            }
            
            self.environ.insert(parts[0].to_string(), parts[1].to_string());
        }
        
        Ok(())
    }
    
    /// Read process threads
    fn read_threads(&mut self) -> Result<()> {
        let task_path = format!("/proc/{}/task", self.pid);
        let entries = fs::read_dir(&task_path)
            .map_err(|e| Error::Process(format!("Failed to read process threads: {}", e)))?;
        
        for entry in entries {
            let entry = entry.map_err(|e| Error::Process(format!("Failed to read thread entry: {}", e)))?;
            let file_name = entry.file_name();
            let file_name_str = file_name.to_string_lossy();
            
            if let Ok(tid) = file_name_str.parse::<u32>() {
                self.threads.push(tid);
            }
        }
        
        Ok(())
    }
    
    /// Read process open files
    fn read_open_files(&mut self) -> Result<()> {
        let fd_path = format!("/proc/{}/fd", self.pid);
        let entries = fs::read_dir(&fd_path)
            .map_err(|e| Error::Process(format!("Failed to read process open files: {}", e)))?;
        
        for entry in entries {
            let entry = entry.map_err(|e| Error::Process(format!("Failed to read file entry: {}", e)))?;
            let path = entry.path();
            
            if let Ok(target) = fs::read_link(&path) {
                self.open_files.push(target.to_string_lossy().to_string());
            }
        }
        
        Ok(())
    }
}

/// Get all process IDs
pub fn get_all_pids() -> Result<Vec<u32>> {
    let mut pids = Vec::new();
    
    let entries = fs::read_dir("/proc")
        .map_err(|e| Error::Process(format!("Failed to read /proc directory: {}", e)))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| Error::Process(format!("Failed to read directory entry: {}", e)))?;
        let file_name = entry.file_name();
        let file_name_str = file_name.to_string_lossy();
        
        if let Ok(pid) = file_name_str.parse::<u32>() {
            pids.push(pid);
        }
    }
    
    Ok(pids)
}

/// Get process information for all processes
pub fn get_all_processes() -> Result<Vec<ProcessInfo>> {
    let pids = get_all_pids()?;
    let mut processes = Vec::new();
    
    for pid in pids {
        if let Ok(info) = ProcessInfo::get(pid) {
            processes.push(info);
        }
    }
    
    Ok(processes)
}

/// Find processes by name
pub fn find_processes_by_name(name: &str) -> Result<Vec<ProcessInfo>> {
    let all_processes = get_all_processes()?;
    let mut matching_processes = Vec::new();
    
    for process in all_processes {
        if process.name.contains(name) || process.cmdline.contains(name) {
            matching_processes.push(process);
        }
    }
    
    Ok(matching_processes)
}

/// Find processes by executable path
pub fn find_processes_by_exe(exe: &str) -> Result<Vec<ProcessInfo>> {
    let all_processes = get_all_processes()?;
    let mut matching_processes = Vec::new();
    
    for process in all_processes {
        if process.exe.contains(exe) {
            matching_processes.push(process);
        }
    }
    
    Ok(matching_processes)
}

/// Find child processes of a parent process
pub fn find_child_processes(ppid: u32) -> Result<Vec<ProcessInfo>> {
    let all_processes = get_all_processes()?;
    let mut child_processes = Vec::new();
    
    for process in all_processes {
        if process.ppid == ppid {
            child_processes.push(process);
        }
    }
    
    Ok(child_processes)
}

/// Get the process tree
pub fn get_process_tree() -> Result<HashMap<u32, Vec<u32>>> {
    let all_processes = get_all_processes()?;
    let mut tree = HashMap::new();
    
    for process in all_processes {
        tree.entry(process.ppid).or_insert_with(Vec::new).push(process.pid);
    }
    
    Ok(tree)
} 