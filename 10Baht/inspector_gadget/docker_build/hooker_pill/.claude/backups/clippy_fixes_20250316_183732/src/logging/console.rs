//! Console logger implementation
//!
//! This module provides a simple console logger for Inspector Gadget.

use crate::core::events::TraceEvent;
use crate::error::Result;


/// Console logger configuration
#[derive(Debug, Clone)]
pub struct ConsoleConfig {
    /// Whether to include timestamps in log messages
    pub include_timestamps: bool,
    /// Whether to include process IDs in log messages
    pub include_pids: bool,
    /// Whether to include syscall numbers in log messages
    pub include_syscall_numbers: bool,
}

impl Default for ConsoleConfig {
    fn default() -> Self {
        Self {
            include_timestamps: true,
            include_pids: true,
            include_syscall_numbers: true,
        }
    }
}

/// Console logger
#[derive(Debug)]
pub struct ConsoleLogger {
    /// Configuration
    pub config: ConsoleConfig,
}

impl ConsoleLogger {
    /// Create a new console logger
    pub fn new(config: ConsoleConfig) -> Self {
        Self { config }
    }
    
    /// Log an event
    pub fn log(&self, event: &TraceEvent) -> Result<()> {
        println!("{}", self.format_event(event));
        Ok(())
    }
    
    /// Format an event for console output
    fn format_event(&self, event: &TraceEvent) -> String {
        let mut parts = Vec::new();
        
        if self.config.include_timestamps {
            parts.push(format!("[{}]", event.timestamp));
        }
        
        if self.config.include_pids {
            parts.push(format!("PID: {}", event.pid));
        }
        
        let syscall_info = if let Some(syscall_name) = &event.syscall_name {
            if self.config.include_syscall_numbers {
                format!("{}({})", syscall_name, event.syscall)
            } else {
                syscall_name.clone()
            }
        } else {
            format!("syscall_{}", event.syscall)
        };
        
        parts.push(syscall_info);
        
        if event.args.len() > 0 {
            let args = event.args
                .iter()
                .map(|arg| format!("{:#x}", arg))
                .collect::<Vec<_>>()
                .join(", ");
            parts.push(format!("args: [{}]", args));
        }
        
        parts.push(format!("ret: {}", event.ret));
        
        parts.join(" ")
    }
}
