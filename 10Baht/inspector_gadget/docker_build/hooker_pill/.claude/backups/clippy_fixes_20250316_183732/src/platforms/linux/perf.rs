/*!
 * Perf event functionality for Linux platform
 * 
 * This module provides perf event functionality for the Linux platform.
 */

use std::fs;
use std::path::Path;

use crate::error::{Error, Result};

/// Perf event type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PerfEventType {
    /// Hardware event
    Hardware,
    /// Software event
    Software,
    /// Tracepoint event
    Tracepoint,
    /// Breakpoint event
    Breakpoint,
    /// Dynamic PMU event
    DynamicPmu,
}

/// Perf event
pub struct PerfEvent {
    /// Event type
    event_type: PerfEventType,
    /// Event name
    name: String,
    /// Event ID
    id: u64,
    /// File descriptor
    fd: i32,
}

impl PerfEvent {
    /// Create a new perf event
    pub fn new(event_type: PerfEventType, name: impl Into<String>, id: u64, fd: i32) -> Self {
        PerfEvent {
            event_type,
            name: name.into(),
            id,
            fd,
        }
    }
    
    /// Get the event type
    pub fn event_type(&self) -> PerfEventType {
        self.event_type
    }
    
    /// Get the event name
    pub fn name(&self) -> &str {
        &self.name
    }
    
    /// Get the event ID
    pub fn id(&self) -> u64 {
        self.id
    }
    
    /// Get the file descriptor
    pub fn fd(&self) -> i32 {
        self.fd
    }
}

/// Perf event attribute
#[derive(Debug, Clone)]
pub struct PerfEventAttr {
    /// Event type
    pub event_type: PerfEventType,
    /// Event config
    pub config: u64,
    /// Sample period
    pub sample_period: u64,
    /// Sample frequency
    pub sample_freq: u64,
    /// Wakeup events
    pub wakeup_events: u32,
    /// Wakeup watermark
    pub wakeup_watermark: u32,
    /// Precise IP
    pub precise_ip: u8,
    /// Exclude kernel
    pub exclude_kernel: bool,
    /// Exclude user
    pub exclude_user: bool,
    /// Exclude hv
    pub exclude_hv: bool,
    /// Exclude idle
    pub exclude_idle: bool,
}

impl Default for PerfEventAttr {
    fn default() -> Self {
        PerfEventAttr {
            event_type: PerfEventType::Software,
            config: 0,
            sample_period: 0,
            sample_freq: 0,
            wakeup_events: 1,
            wakeup_watermark: 0,
            precise_ip: 0,
            exclude_kernel: false,
            exclude_user: false,
            exclude_hv: false,
            exclude_idle: false,
        }
    }
}

/// Perf event builder
pub struct PerfEventBuilder {
    /// Event attributes
    attr: PerfEventAttr,
    /// Process ID
    pid: i32,
    /// CPU
    cpu: i32,
    /// Group file descriptor
    group_fd: i32,
    /// Flags
    flags: u64,
}

impl PerfEventBuilder {
    /// Create a new perf event builder
    pub fn new() -> Self {
        PerfEventBuilder {
            attr: PerfEventAttr::default(),
            pid: -1,
            cpu: -1,
            group_fd: -1,
            flags: 0,
        }
    }
    
    /// Set the event type
    pub fn event_type(mut self, event_type: PerfEventType) -> Self {
        self.attr.event_type = event_type;
        self
    }
    
    /// Set the event config
    pub fn config(mut self, config: u64) -> Self {
        self.attr.config = config;
        self
    }
    
    /// Set the sample period
    pub fn sample_period(mut self, sample_period: u64) -> Self {
        self.attr.sample_period = sample_period;
        self
    }
    
    /// Set the sample frequency
    pub fn sample_freq(mut self, sample_freq: u64) -> Self {
        self.attr.sample_freq = sample_freq;
        self
    }
    
    /// Set the wakeup events
    pub fn wakeup_events(mut self, wakeup_events: u32) -> Self {
        self.attr.wakeup_events = wakeup_events;
        self
    }
    
    /// Set the wakeup watermark
    pub fn wakeup_watermark(mut self, wakeup_watermark: u32) -> Self {
        self.attr.wakeup_watermark = wakeup_watermark;
        self
    }
    
    /// Set the precise IP
    pub fn precise_ip(mut self, precise_ip: u8) -> Self {
        self.attr.precise_ip = precise_ip;
        self
    }
    
    /// Set whether to exclude kernel
    pub fn exclude_kernel(mut self, exclude_kernel: bool) -> Self {
        self.attr.exclude_kernel = exclude_kernel;
        self
    }
    
    /// Set whether to exclude user
    pub fn exclude_user(mut self, exclude_user: bool) -> Self {
        self.attr.exclude_user = exclude_user;
        self
    }
    
    /// Set whether to exclude hv
    pub fn exclude_hv(mut self, exclude_hv: bool) -> Self {
        self.attr.exclude_hv = exclude_hv;
        self
    }
    
    /// Set whether to exclude idle
    pub fn exclude_idle(mut self, exclude_idle: bool) -> Self {
        self.attr.exclude_idle = exclude_idle;
        self
    }
    
    /// Set the process ID
    pub fn pid(mut self, pid: i32) -> Self {
        self.pid = pid;
        self
    }
    
    /// Set the CPU
    pub fn cpu(mut self, cpu: i32) -> Self {
        self.cpu = cpu;
        self
    }
    
    /// Set the group file descriptor
    pub fn group_fd(mut self, group_fd: i32) -> Self {
        self.group_fd = group_fd;
        self
    }
    
    /// Set the flags
    pub fn flags(mut self, flags: u64) -> Self {
        self.flags = flags;
        self
    }
    
    /// Build the perf event
    pub fn build(self) -> Result<PerfEvent> {
        // This is a placeholder for actual perf event creation
        // In a real implementation, this would call perf_event_open syscall
        
        // Simulate perf event creation
        let fd = 999; // Placeholder file descriptor
        let id = 123456; // Placeholder event ID
        
        Ok(PerfEvent::new(
            self.attr.event_type,
            "perf_event",
            id,
            fd,
        ))
    }
}

/// Check if perf events are supported on the system
pub fn is_perf_supported() -> bool {
    // Check if the perf_event_open syscall is available
    if let Ok(content) = fs::read_to_string("/proc/kallsyms") {
        if content.contains("perf_event_open") {
            return true;
        }
    }
    
    // Check if the perf_event subsystem is available
    if Path::new("/sys/kernel/debug/tracing/events/syscalls").exists() {
        return true;
    }
    
    false
}

/// Get the perf features supported by the kernel
pub fn get_perf_features() -> Result<Vec<String>> {
    let mut features = Vec::new();
    
    // Check for various perf features
    if Path::new("/sys/kernel/debug/tracing/events/syscalls").exists() {
        features.push("syscall-tracing".to_string());
    }
    
    if Path::new("/sys/kernel/debug/tracing/events/sched").exists() {
        features.push("scheduler-tracing".to_string());
    }
    
    if Path::new("/sys/kernel/debug/tracing/events/irq").exists() {
        features.push("irq-tracing".to_string());
    }
    
    if Path::new("/sys/kernel/debug/tracing/events/kmem").exists() {
        features.push("kmem-tracing".to_string());
    }
    
    if Path::new("/sys/kernel/debug/tracing/events/block").exists() {
        features.push("block-tracing".to_string());
    }
    
    if Path::new("/sys/kernel/debug/tracing/events/net").exists() {
        features.push("net-tracing".to_string());
    }
    
    Ok(features)
}

/// Get the perf event types supported by the kernel
pub fn get_perf_event_types() -> Result<Vec<String>> {
    let mut event_types = Vec::new();
    
    // Check for various perf event types
    if Path::new("/sys/devices/cpu").exists() {
        event_types.push("cpu".to_string());
    }
    
    if Path::new("/sys/devices/software").exists() {
        event_types.push("software".to_string());
    }
    
    if Path::new("/sys/devices/tracepoint").exists() {
        event_types.push("tracepoint".to_string());
    }
    
    if Path::new("/sys/devices/breakpoint").exists() {
        event_types.push("breakpoint".to_string());
    }
    
    Ok(event_types)
} 