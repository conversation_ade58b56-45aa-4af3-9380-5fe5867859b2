/*!
 * Linux Syscall Table
 * 
 * This module provides functionality for loading syscall definitions
 * from the Linux syscall table.
 */



use crate::platforms::Platform;
use crate::syscalls::common::{Syscall, SyscallCategory, register_syscall};

/// Load syscall definitions from the Linux syscall table
pub fn load_syscalls_from_table() {
    // In a real implementation, this would load syscall definitions from a file or database
    // For now, we'll just load some additional syscalls
    
    info!("Loading additional Linux syscall definitions");
    
    // Load additional syscalls
    load_additional_syscalls();
}

/// Load additional syscall definitions
fn load_additional_syscalls() {
    // IPC syscalls
    register_syscall(
        Syscall::new(
            16,
            "ioctl",
            Platform::Linux,
            SyscallCategory::IO,
        )
        .with_description("Control device"),
    );
    
    register_syscall(
        Syscall::new(
            17,
            "pread64",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Read from a file descriptor at a given offset"),
    );
    
    register_syscall(
        Syscall::new(
            18,
            "pwrite64",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Write to a file descriptor at a given offset"),
    );
    
    register_syscall(
        Syscall::new(
            19,
            "readv",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Read data into multiple buffers"),
    );
    
    register_syscall(
        Syscall::new(
            20,
            "writev",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Write data from multiple buffers"),
    );
    
    register_syscall(
        Syscall::new(
            21,
            "access",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Check user's permissions for a file"),
    );
    
    register_syscall(
        Syscall::new(
            22,
            "pipe",
            Platform::Linux,
            SyscallCategory::IPC,
        )
        .with_description("Create pipe"),
    );
    
    register_syscall(
        Syscall::new(
            23,
            "select",
            Platform::Linux,
            SyscallCategory::IO,
        )
        .with_description("Synchronous I/O multiplexing"),
    );
    
    register_syscall(
        Syscall::new(
            24,
            "sched_yield",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Yield the processor"),
    );
    
    register_syscall(
        Syscall::new(
            25,
            "mremap",
            Platform::Linux,
            SyscallCategory::Memory,
        )
        .with_description("Remap a virtual memory address"),
    );
    
    register_syscall(
        Syscall::new(
            26,
            "msync",
            Platform::Linux,
            SyscallCategory::Memory,
        )
        .with_description("Synchronize a file with a memory map"),
    );
    
    register_syscall(
        Syscall::new(
            27,
            "mincore",
            Platform::Linux,
            SyscallCategory::Memory,
        )
        .with_description("Determine whether pages are resident in memory"),
    );
    
    register_syscall(
        Syscall::new(
            28,
            "madvise",
            Platform::Linux,
            SyscallCategory::Memory,
        )
        .with_description("Give advice about use of memory"),
    );
    
    register_syscall(
        Syscall::new(
            29,
            "shmget",
            Platform::Linux,
            SyscallCategory::IPC,
        )
        .with_description("Allocate a shared memory segment"),
    );
    
    register_syscall(
        Syscall::new(
            30,
            "shmat",
            Platform::Linux,
            SyscallCategory::IPC,
        )
        .with_description("Attach shared memory segment"),
    );
    
    register_syscall(
        Syscall::new(
            31,
            "shmctl",
            Platform::Linux,
            SyscallCategory::IPC,
        )
        .with_description("Shared memory control"),
    );
    
    register_syscall(
        Syscall::new(
            32,
            "dup",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Duplicate a file descriptor"),
    );
    
    register_syscall(
        Syscall::new(
            33,
            "dup2",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Duplicate a file descriptor"),
    );
    
    register_syscall(
        Syscall::new(
            34,
            "pause",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Wait for signal"),
    );
    
    register_syscall(
        Syscall::new(
            35,
            "nanosleep",
            Platform::Linux,
            SyscallCategory::Time,
        )
        .with_description("High-resolution sleep"),
    );
    
    register_syscall(
        Syscall::new(
            36,
            "getitimer",
            Platform::Linux,
            SyscallCategory::Time,
        )
        .with_description("Get value of an interval timer"),
    );
    
    register_syscall(
        Syscall::new(
            37,
            "alarm",
            Platform::Linux,
            SyscallCategory::Time,
        )
        .with_description("Set an alarm clock for delivery of a signal"),
    );
    
    register_syscall(
        Syscall::new(
            38,
            "setitimer",
            Platform::Linux,
            SyscallCategory::Time,
        )
        .with_description("Set value of an interval timer"),
    );
    
    register_syscall(
        Syscall::new(
            39,
            "getpid",
            Platform::Linux,
            SyscallCategory::Process,
        )
        .with_description("Get process identification"),
    );
    
    register_syscall(
        Syscall::new(
            40,
            "sendfile",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Transfer data between file descriptors"),
    );
    
    // Thread syscalls
    register_syscall(
        Syscall::new(
            186,
            "gettid",
            Platform::Linux,
            SyscallCategory::Thread,
        )
        .with_description("Get thread identification"),
    );
    
    register_syscall(
        Syscall::new(
            187,
            "futex",
            Platform::Linux,
            SyscallCategory::Synchronization,
        )
        .with_description("Fast user-space locking"),
    );
    
    register_syscall(
        Syscall::new(
            188,
            "sched_setaffinity",
            Platform::Linux,
            SyscallCategory::Thread,
        )
        .with_description("Set a thread's CPU affinity mask"),
    );
    
    register_syscall(
        Syscall::new(
            189,
            "sched_getaffinity",
            Platform::Linux,
            SyscallCategory::Thread,
        )
        .with_description("Get a thread's CPU affinity mask"),
    );
    
    // Synchronization syscalls
    register_syscall(
        Syscall::new(
            202,
            "futex",
            Platform::Linux,
            SyscallCategory::Synchronization,
        )
        .with_description("Fast user-space locking"),
    );
    
    register_syscall(
        Syscall::new(
            203,
            "sched_setaffinity",
            Platform::Linux,
            SyscallCategory::Thread,
        )
        .with_description("Set a thread's CPU affinity mask"),
    );
    
    register_syscall(
        Syscall::new(
            204,
            "sched_getaffinity",
            Platform::Linux,
            SyscallCategory::Thread,
        )
        .with_description("Get a thread's CPU affinity mask"),
    );
    
    // Device syscalls
    register_syscall(
        Syscall::new(
            85,
            "creat",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Create a new file or rewrite an existing one"),
    );
    
    register_syscall(
        Syscall::new(
            86,
            "link",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Make a new name for a file"),
    );
    
    register_syscall(
        Syscall::new(
            87,
            "unlink",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Delete a name and possibly the file it refers to"),
    );
    
    register_syscall(
        Syscall::new(
            88,
            "symlink",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Make a new name for a file"),
    );
    
    register_syscall(
        Syscall::new(
            89,
            "readlink",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Read value of a symbolic link"),
    );
    
    register_syscall(
        Syscall::new(
            90,
            "chmod",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Change permissions of a file"),
    );
    
    register_syscall(
        Syscall::new(
            91,
            "fchmod",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Change permissions of a file"),
    );
    
    register_syscall(
        Syscall::new(
            92,
            "chown",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Change ownership of a file"),
    );
    
    register_syscall(
        Syscall::new(
            93,
            "fchown",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Change ownership of a file"),
    );
    
    register_syscall(
        Syscall::new(
            94,
            "lchown",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Change ownership of a file"),
    );
    
    register_syscall(
        Syscall::new(
            95,
            "umask",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Set file mode creation mask"),
    );
    
    // Add more syscalls as needed...
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::syscalls::common::mapping::{get_syscall, get_syscalls_by_category};
    
    #[test]
    fn test_load_additional_syscalls() {
        // Load additional syscalls
        load_additional_syscalls();
        
        // Check that some syscalls are registered
        let syscall = get_syscall(Platform::Linux, 16); // ioctl
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "ioctl");
        
        let syscall = get_syscall(Platform::Linux, 22); // pipe
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "pipe");
        
        let syscall = get_syscall(Platform::Linux, 35); // nanosleep
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "nanosleep");
        
        let syscall = get_syscall(Platform::Linux, 39); // getpid
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "getpid");
        
        let syscall = get_syscall(Platform::Linux, 186); // gettid
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "gettid");
    }
    
    #[test]
    fn test_syscall_categories() {
        // Load additional syscalls
        load_additional_syscalls();
        
        // Check syscalls by category
        let fs_syscalls = get_syscalls_by_category(Platform::Linux, SyscallCategory::FileSystem);
        assert!(!fs_syscalls.is_empty());
        
        let ipc_syscalls = get_syscalls_by_category(Platform::Linux, SyscallCategory::IPC);
        assert!(!ipc_syscalls.is_empty());
        
        let time_syscalls = get_syscalls_by_category(Platform::Linux, SyscallCategory::Time);
        assert!(!time_syscalls.is_empty());
        
        let thread_syscalls = get_syscalls_by_category(Platform::Linux, SyscallCategory::Thread);
        assert!(!thread_syscalls.is_empty());
        
        let sync_syscalls = get_syscalls_by_category(Platform::Linux, SyscallCategory::Synchronization);
        assert!(!sync_syscalls.is_empty());
    }
} 