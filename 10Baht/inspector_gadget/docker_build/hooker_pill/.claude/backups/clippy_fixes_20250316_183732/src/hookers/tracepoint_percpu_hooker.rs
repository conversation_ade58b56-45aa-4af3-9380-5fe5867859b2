use crate::ebpf::maps::TypedOptimizedPerCpuMap;
use crate::hookers::tracepoint_hooker::{<PERSON><PERSON>Hooker, <PERSON>pointEvent, TracepointHookerConfig};
use serde::{Serialize, Deserialize};
use std::error::Error;

use std::time::{Duration, Instant};


/// Statistics for Tracepoint events by subsystem and event name
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TracepointEventStats {
    /// Number of events
    pub event_count: u64,
    /// Total duration of events (in nanoseconds)
    pub total_duration_ns: u64,
    /// Minimum duration of events (in nanoseconds)
    pub min_duration_ns: u64,
    /// Maximum duration of events (in nanoseconds)
    pub max_duration_ns: u64,
    /// Number of events with errors
    pub error_count: u64,
    /// Last event timestamp
    pub last_event_timestamp: u64,
}

impl Default for TracepointEventStats {
    fn default() -> Self {
        Self {
            event_count: 0,
            total_duration_ns: 0,
            min_duration_ns: u64::MAX,
            max_duration_ns: 0,
            error_count: 0,
            last_event_timestamp: 0,
        }
    }
}

impl std::ops::Add for TracepointEventStats {
    type Output = Self;
    
    fn add(self, other: Self) -> Self {
        Self {
            event_count: self.event_count + other.event_count,
            total_duration_ns: self.total_duration_ns + other.total_duration_ns,
            min_duration_ns: std::cmp::min(self.min_duration_ns, other.min_duration_ns),
            max_duration_ns: std::cmp::max(self.max_duration_ns, other.max_duration_ns),
            error_count: self.error_count + other.error_count,
            last_event_timestamp: std::cmp::max(self.last_event_timestamp, other.last_event_timestamp),
        }
    }
}

/// Key for tracepoint event statistics
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct TracepointKey {
    /// Subsystem ID (hash of subsystem name)
    pub subsystem_id: u32,
    /// Event ID (hash of event name)
    pub event_id: u32,
}

/// Tracepoint hooker with Per-CPU Maps for improved performance
pub struct TracepointPerCpuHooker {
    /// Inner Tracepoint hooker
    inner: TracepointHooker,
    /// Map to store event statistics by tracepoint key
    event_stats: Arc<TypedOptimizedPerCpuMap<TracepointKey, TracepointEventStats>>,
    /// Map to store event statistics by process ID
    process_stats: Arc<TypedOptimizedPerCpuMap<u32, TracepointEventStats>>,
    /// Map to store event statistics by user ID
    user_stats: Arc<TypedOptimizedPerCpuMap<u32, TracepointEventStats>>,
    /// Last statistics export time
    last_export_time: Instant,
    /// Statistics export interval
    export_interval: Duration,
}

impl TracepointPerCpuHooker {
    /// Creates a new Tracepoint hooker with Per-CPU Maps
    pub fn new(config: TracepointHookerConfig) -> Result<Self, Box<dyn Error>> {
        // Create the inner Tracepoint hooker
        let inner = TracepointHooker::new(config)?;
        
        // Create the Per-CPU Maps
        let event_stats = Arc::new(TypedOptimizedPerCpuMap::<TracepointKey, TracepointEventStats>::create(10240)?);
        let process_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, TracepointEventStats>::create(1024)?);
        let user_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, TracepointEventStats>::create(256)?);
        
        Ok(Self {
            inner,
            event_stats,
            process_stats,
            user_stats,
            last_export_time: Instant::now(),
            export_interval: Duration::from_secs(60),
        })
    }
    
    /// Handles a Tracepoint event
    pub fn handle_event(&self, event: &TracepointEvent) -> Result<(), Box<dyn Error>> {
        // Process the event with the inner hooker
        self.inner.handle_event(event)?;
        
        // Update statistics in the Per-CPU Maps
        self.update_event_stats(event)?;
        
        // Export statistics if the export interval has elapsed
        if self.last_export_time.elapsed() >= self.export_interval {
            self.export_statistics()?;
        }
        
        Ok(())
    }
    
    /// Updates event statistics in the Per-CPU Maps
    fn update_event_stats(&self, event: &TracepointEvent) -> Result<(), Box<dyn Error>> {
        // Get the current timestamp
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Create a key for the event
        let key = TracepointKey {
            subsystem_id: self.hash_string(&event.subsystem),
            event_id: self.hash_string(&event.event_name),
        };
        
        // Update event statistics
        let mut event_stats = self.event_stats.lookup(key).unwrap_or_default();
        
        // Update process statistics
        let pid = event.process_id;
        let mut process_stats = self.process_stats.lookup(pid).unwrap_or_default();
        
        // Update user statistics
        let uid = event.user_id;
        let mut user_stats = self.user_stats.lookup(uid).unwrap_or_default();
        
        // Calculate event duration if available
        let duration = if let (Some(start), Some(end)) = (event.start_time, event.end_time) {
            end.saturating_sub(start)
        } else {
            0
        };
        
        // Update the statistics
        self.update_stats(&mut event_stats, duration, event.has_error, timestamp);
        self.update_stats(&mut process_stats, duration, event.has_error, timestamp);
        self.update_stats(&mut user_stats, duration, event.has_error, timestamp);
        
        // Store the updated statistics
        self.event_stats.update(key, event_stats)?;
        self.process_stats.update(pid, process_stats)?;
        self.user_stats.update(uid, user_stats)?;
        
        Ok(())
    }
    
    /// Helper method to update statistics
    fn update_stats(&self, stats: &mut TracepointEventStats, duration: u64, has_error: bool, timestamp: u64) {
        // Update event count
        stats.event_count += 1;
        
        // Update duration statistics
        if duration > 0 {
            stats.total_duration_ns += duration;
            stats.min_duration_ns = std::cmp::min(stats.min_duration_ns, duration);
            stats.max_duration_ns = std::cmp::max(stats.max_duration_ns, duration);
        }
        
        // Update error count
        if has_error {
            stats.error_count += 1;
        }
        
        // Update timestamp
        stats.last_event_timestamp = timestamp;
    }
    
    /// Exports statistics to external systems (e.g., Elasticsearch)
    fn export_statistics(&self) -> Result<(), Box<dyn Error>> {
        // This would be implemented to export statistics to external systems
        // For now, we'll just log some aggregated statistics
        
        // Get top 10 tracepoints by event count
        let mut tracepoints = Vec::new();
        
        // This is a simplified approach; in a real implementation, we would need a more efficient way
        // to iterate over the keys in the map
        for subsystem_id in 0..100 {
            for event_id in 0..100 {
                let key = TracepointKey {
                    subsystem_id,
                    event_id,
                };
                
                if let Some(stats) = self.event_stats.lookup_aggregated(key) {
                    if stats.event_count > 0 {
                        tracepoints.push((key, stats));
                    }
                }
            }
        }
        
        // Sort by event count
        tracepoints.sort_by(|a, b| b.1.event_count.cmp(&a.1.event_count));
        
        // Log the top 10 tracepoints
        info!("Top tracepoints by event count:");
        for (i, (key, stats)) in tracepoints.iter().take(10).enumerate() {
            let avg_duration = if stats.event_count > 0 {
                stats.total_duration_ns / stats.event_count
            } else {
                0
            };
            
            info!(
                "{}. Tracepoint {}/{}: {} events, {} errors, avg duration: {} ns, min: {} ns, max: {} ns",
                i + 1,
                key.subsystem_id,
                key.event_id,
                stats.event_count,
                stats.error_count,
                avg_duration,
                stats.min_duration_ns,
                stats.max_duration_ns
            );
        }
        
        // Get top 5 processes by event count
        let mut processes = Vec::new();
        for pid in 1..1024 {
            if let Some(stats) = self.process_stats.lookup_aggregated(pid) {
                if stats.event_count > 0 {
                    processes.push((pid, stats));
                }
            }
        }
        
        // Sort by event count
        processes.sort_by(|a, b| b.1.event_count.cmp(&a.1.event_count));
        
        // Log the top 5 processes
        info!("Top processes by event count:");
        for (i, (pid, stats)) in processes.iter().take(5).enumerate() {
            let avg_duration = if stats.event_count > 0 {
                stats.total_duration_ns / stats.event_count
            } else {
                0
            };
            
            info!(
                "{}. PID {}: {} events, {} errors, avg duration: {} ns",
                i + 1,
                pid,
                stats.event_count,
                stats.error_count,
                avg_duration
            );
        }
        
        Ok(())
    }
    
    /// Gets aggregated statistics for a tracepoint
    pub fn get_tracepoint_stats(&self, subsystem: &str, event_name: &str) -> Option<TracepointEventStats> {
        let key = TracepointKey {
            subsystem_id: self.hash_string(subsystem),
            event_id: self.hash_string(event_name),
        };
        
        self.event_stats.lookup_aggregated(key)
    }
    
    /// Gets aggregated statistics for a process
    pub fn get_process_stats(&self, pid: u32) -> Option<TracepointEventStats> {
        self.process_stats.lookup_aggregated(pid)
    }
    
    /// Gets aggregated statistics for a user
    pub fn get_user_stats(&self, uid: u32) -> Option<TracepointEventStats> {
        self.user_stats.lookup_aggregated(uid)
    }
    
    /// Gets the inner Tracepoint hooker
    pub fn inner(&self) -> &TracepointHooker {
        &self.inner
    }
    
    /// Gets map statistics
    pub fn get_map_stats(&self) -> (u64, u64, u64) {
        let event_stats = self.event_stats.get_stats();
        let process_stats = self.process_stats.get_stats();
        let user_stats = self.user_stats.get_stats();
        
        let lookups = event_stats.lookups.load(std::sync::atomic::Ordering::Relaxed)
            + process_stats.lookups.load(std::sync::atomic::Ordering::Relaxed)
            + user_stats.lookups.load(std::sync::atomic::Ordering::Relaxed);
            
        let updates = event_stats.updates.load(std::sync::atomic::Ordering::Relaxed)
            + process_stats.updates.load(std::sync::atomic::Ordering::Relaxed)
            + user_stats.updates.load(std::sync::atomic::Ordering::Relaxed);
            
        let misses = event_stats.misses.load(std::sync::atomic::Ordering::Relaxed)
            + process_stats.misses.load(std::sync::atomic::Ordering::Relaxed)
            + user_stats.misses.load(std::sync::atomic::Ordering::Relaxed);
            
        (lookups, updates, misses)
    }
    
    /// Helper method to hash a string to a u32
    fn hash_string(&self, s: &str) -> u32 {
        use std::hash::{Hash, Hasher};
        let mut hasher = std::collections::hash_map::DefaultHasher::new();
        s.hash(&mut hasher);
        hasher.finish() as u32
    }
} 