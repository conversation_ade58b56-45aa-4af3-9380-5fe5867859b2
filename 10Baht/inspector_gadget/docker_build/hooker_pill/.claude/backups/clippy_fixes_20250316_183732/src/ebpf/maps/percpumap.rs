/*!
 * Optimized Per-CPU Map Implementation for eBPF
 * 
 * This module provides an optimized per-CPU map implementation for eBPF maps,
 * which allows concurrent access without locks by maintaining separate values
 * for each CPU.
 */

use std::marker::PhantomData;
use std::sync::atomic::{AtomicU64, Ordering};

use log::{debug, warn, info, error};
use serde::{Serialize, Deserialize, de::DeserializeOwned};

use super::{Map, MapConfig, MapType, MapError};
use super::hash::HashFunction;
use super::iter::MapIter;

/// Per-CPU map statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerCpuMapStats {
    /// Number of lookups
    pub lookups: usize,
    /// Number of updates
    pub updates: usize,
    /// Number of deletes
    pub deletes: usize,
    /// Number of aggregations
    pub aggregations: usize,
    /// Number of CPUs
    pub num_cpus: usize,
    /// Number of online CPUs
    pub online_cpus: usize,
}

impl Default for PerCpuMapStats {
    fn default() -> Self {
        Self {
            lookups: 0,
            updates: 0,
            deletes: 0,
            aggregations: 0,
            num_cpus: num_cpus::get(),
            online_cpus: num_cpus::get(),
        }
    }
}

/// Optimized per-CPU map for eBPF
#[derive(Debug)]
pub struct OptimizedPerCpuMap {
    /// Inner map
    inner: Map,
    /// Statistics
    stats: Arc<PerCpuMapStats>,
    /// Number of CPUs detected at creation time
    num_cpus: usize,
    /// CPU online/offline tracking
    cpu_online: Vec<bool>,
}

impl OptimizedPerCpuMap {
    /// Create a new optimized per-CPU map
    pub fn create(
        key_size: usize,
        value_size: usize,
        max_entries: usize,
    ) -> Result<Self, MapError> {
        // Get the number of CPUs
        let num_cpus = num_cpus::get();
        
        // Create the map configuration
        // For per-CPU maps, the actual value size in the kernel is aligned and multiplied by num_cpus
        let config = MapConfig::new(MapType::PerCPUHash, key_size, value_size, max_entries, 0)?;
        
        // Create the map
        let inner = Map::create(config)?;
        
        // Initialize CPU tracking
        let cpu_online = vec![true; num_cpus];
        
        Ok(Self {
            inner,
            stats: Arc::new(PerCpuMapStats::default()),
            num_cpus,
            cpu_online,
        })
    }
    
    /// Open an existing per-CPU map
    pub fn open(name: &str) -> Result<Self, MapError> {
        // Open the map
        let inner = Map::open(name)?;
        
        // Verify that this is a per-CPU map
        let info = inner.info()?;
        if info.map_type != MapType::PerCPUHash && info.map_type != MapType::PerCPUArray {
            return Err(MapError::InvalidArgument(format!(
                "Map '{}' is not a per-CPU map (type: {:?})",
                name, info.map_type
            )));
        }
        
        // Get the number of CPUs
        let num_cpus = num_cpus::get();
        
        // Initialize CPU tracking
        let cpu_online = vec![true; num_cpus];
        
        Ok(Self {
            inner,
            stats: Arc::new(PerCpuMapStats::default()),
            num_cpus,
            cpu_online,
        })
    }
    
    /// Get the map name
    pub fn name(&self) -> &str {
        self.inner.name()
    }
    
    /// Get the number of CPUs this map was designed for
    pub fn num_cpus(&self) -> usize {
        self.num_cpus
    }
    
    /// Get a reference to the stats
    pub fn stats(&self) -> &PerCpuMapStats {
        &self.stats
    }
    
    /// Check if a CPU is online
    pub fn is_cpu_online(&self, cpu: usize) -> bool {
        if cpu >= self.cpu_online.len() {
            return false;
        }
        self.cpu_online[cpu]
    }
    
    /// Set CPU online status
    pub fn set_cpu_online(&mut self, cpu: usize, online: bool) {
        if cpu < self.cpu_online.len() {
            self.cpu_online[cpu] = online;
        }
    }
    
    /// Lookup a key's value for a specific CPU
    pub fn lookup_cpu<K, V>(&self, key: &K, cpu: usize) -> Result<Option<V>, MapError>
    where
        K: Serialize,
        V: DeserializeOwned,
    {
        // Increment lookup counter
        self.stats.lookups += 1;
        
        // Check if CPU is valid
        if cpu >= self.num_cpus {
            return Err(MapError::InvalidArgument(format!(
                "CPU {} is out of range (max: {})",
                cpu, self.num_cpus - 1
            )));
        }
        
        // Check if CPU is online
        if !self.is_cpu_online(cpu) {
            return Err(MapError::InvalidArgument(format!(
                "CPU {} is offline",
                cpu
            )));
        }
        
        // Serialize key
        let key_bytes = match bincode::serialize(key) {
            Ok(bytes) => bytes,
            Err(e) => {
                return Err(MapError::SerializationError(e.to_string()));
            }
        };
        
        // Perform lookup
        match self.inner.lookup(&key_bytes) {
            Ok(Some(value_bytes)) => {
                // Per-CPU maps store an array of values, one for each CPU
                // We need to extract the specific CPU's value
                let value_size = value_bytes.len() / self.num_cpus;
                let start = cpu * value_size;
                let end = start + value_size;
                
                if end > value_bytes.len() {
                    return Err(MapError::OperationError(format!(
                        "Invalid per-CPU value size: expected {} bytes, got {}",
                        value_size * self.num_cpus, value_bytes.len()
                    )));
                }
                
                // Extract CPU-specific value
                let cpu_value = &value_bytes[start..end];
                
                // Deserialize value
                match bincode::deserialize(cpu_value) {
                    Ok(value) => Ok(Some(value)),
                    Err(e) => {
                        return Err(MapError::DeserializationError(e.to_string()));
                    }
                }
            }
            Ok(None) => Ok(None),
            Err(e) => Err(e),
        }
    }
    
    /// Lookup a key's values for all CPUs
    pub fn lookup_all_cpus<K, V>(&self, key: &K) -> Result<Option<Vec<Option<V>>>, MapError>
    where
        K: Serialize,
        V: DeserializeOwned,
    {
        // Increment lookup counter
        self.stats.lookups += 1;
        
        // Serialize key
        let key_bytes = match bincode::serialize(key) {
            Ok(bytes) => bytes,
            Err(e) => {
                return Err(MapError::SerializationError(e.to_string()));
            }
        };
        
        // Perform lookup
        match self.inner.lookup(&key_bytes) {
            Ok(Some(value_bytes)) => {
                // Per-CPU maps store an array of values, one for each CPU
                let value_size = value_bytes.len() / self.num_cpus;
                let mut result = Vec::with_capacity(self.num_cpus);
                
                for cpu in 0..self.num_cpus {
                    // Skip offline CPUs
                    if !self.is_cpu_online(cpu) {
                        result.push(None);
                        continue;
                    }
                    
                    let start = cpu * value_size;
                    let end = start + value_size;
                    
                    if end > value_bytes.len() {
                        return Err(MapError::OperationError(format!(
                            "Invalid per-CPU value size: expected {} bytes, got {}",
                            value_size * self.num_cpus, value_bytes.len()
                        )));
                    }
                    
                    // Extract CPU-specific value
                    let cpu_value = &value_bytes[start..end];
                    
                    // Deserialize value
                    match bincode::deserialize(cpu_value) {
                        Ok(value) => {
                            result.push(Some(value));
                        }
                        Err(e) => {
                            result.push(None);
                            warn!("Failed to deserialize value for CPU {}: {}", cpu, e);
                        }
                    }
                }
                
                Ok(Some(result))
            }
            Ok(None) => Ok(None),
            Err(e) => Err(e),
        }
    }
    
    /// Update a key's value for a specific CPU
    pub fn update_cpu<K, V>(&self, key: &K, value: &V, cpu: usize) -> Result<(), MapError>
    where
        K: Serialize,
        V: Serialize,
    {
        // Increment update counter
        self.stats.updates += 1;
        
        // Check if CPU is valid
        if cpu >= self.num_cpus {
            return Err(MapError::InvalidArgument(format!(
                "CPU {} is out of range (max: {})",
                cpu, self.num_cpus - 1
            )));
        }
        
        // Check if CPU is online
        if !self.is_cpu_online(cpu) {
            return Err(MapError::InvalidArgument(format!(
                "CPU {} is offline",
                cpu
            )));
        }
        
        // First, we need to get the current values for all CPUs
        let key_bytes = match bincode::serialize(key) {
            Ok(bytes) => bytes,
            Err(e) => {
                return Err(MapError::SerializationError(e.to_string()));
            }
        };
        
        // Serialize the new value
        let new_value_bytes = match bincode::serialize(value) {
            Ok(bytes) => bytes,
            Err(e) => {
                return Err(MapError::SerializationError(e.to_string()));
            }
        };
        
        let value_size = new_value_bytes.len();
        
        // Get the current values or create a new array if the key doesn't exist
        let mut all_cpu_values = match self.inner.lookup(&key_bytes) {
            Ok(Some(bytes)) => {
                // Verify that the current value has the expected size
                if bytes.len() % self.num_cpus != 0 {
                    return Err(MapError::OperationError(format!(
                        "Invalid per-CPU value size: expected multiple of {}, got {}",
                        self.num_cpus, bytes.len()
                    )));
                }
                
                // Use the existing values
                bytes
            }
            Ok(None) => {
                // Create a new array with zeros for all CPUs
                vec![0u8; value_size * self.num_cpus]
            }
            Err(e) => {
                return Err(e);
            }
        };
        
        // Update the value for the specified CPU
        let start = cpu * value_size;
        let end = start + value_size;
        
        if end > all_cpu_values.len() {
            // Resize the array if needed
            all_cpu_values.resize(self.num_cpus * value_size, 0);
        }
        
        // Copy the new value to the array
        all_cpu_values[start..end].copy_from_slice(&new_value_bytes);
        
        // Update the map with the modified array
        match self.inner.update(&key_bytes, &all_cpu_values) {
            Ok(()) => Ok(()),
            Err(e) => Err(e),
        }
    }
    
    /// Delete a key and its values for all CPUs
    pub fn delete<K>(&self, key: &K) -> Result<bool, MapError>
    where
        K: Serialize,
    {
        // Increment delete counter
        self.stats.deletes += 1;
        
        // Serialize key
        let key_bytes = match bincode::serialize(key) {
            Ok(bytes) => bytes,
            Err(e) => {
                return Err(MapError::SerializationError(e.to_string()));
            }
        };
        
        // Perform delete
        match self.inner.delete(&key_bytes) {
            Ok(deleted) => Ok(deleted),
            Err(e) => Err(e),
        }
    }
    
    /// Aggregate values from all CPUs
    pub fn aggregate<K, V, R, F>(&self, key: &K, aggregator: F) -> Result<Option<R>, MapError>
    where
        K: Serialize,
        V: DeserializeOwned,
        F: Fn(Vec<Option<V>>) -> R,
    {
        // Get values from all CPUs
        match self.lookup_all_cpus(key) {
            Ok(Some(values)) => {
                // Apply the aggregator function
                let result = aggregator(values);
                Ok(Some(result))
            }
            Ok(None) => Ok(None),
            Err(e) => Err(e),
        }
    }
    
    /// Iterator over all keys and values in the map
    pub fn iter(&self) -> MapIter {
        self.inner.iter()
    }
}

/// Type-safe wrapper for the optimized per-CPU map
#[derive(Debug)]
pub struct TypedOptimizedPerCpuMap<K, V> {
    /// Inner map
    inner: OptimizedPerCpuMap,
    /// Phantom types
    _key_type: PhantomData<K>,
    _value_type: PhantomData<V>,
}

impl<K, V> TypedOptimizedPerCpuMap<K, V>
where
    K: Serialize + DeserializeOwned + Copy + Eq + std::hash::Hash,
    V: Serialize + DeserializeOwned + Copy,
{
    /// Create a new typed optimized per-CPU map
    pub fn create(max_entries: usize) -> Result<Self, MapError> {
        // Calculate key and value sizes
        let key_size = bincode::serialized_size(&K::default())
            .map_err(|e| MapError::SerializationError(e.to_string()))?;
        
        let value_size = bincode::serialized_size(&V::default())
            .map_err(|e| MapError::SerializationError(e.to_string()))?;
        
        let inner = OptimizedPerCpuMap::create(key_size as usize, value_size as usize, max_entries)?;
        
        Ok(Self {
            inner,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
    
    /// Open an existing typed optimized per-CPU map
    pub fn open(name: &str) -> Result<Self, MapError> {
        let inner = OptimizedPerCpuMap::open(name)?;
        
        Ok(Self {
            inner,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
    
    /// Get the map name
    pub fn name(&self) -> &str {
        self.inner.name()
    }
    
    /// Get the number of CPUs this map was designed for
    pub fn num_cpus(&self) -> usize {
        self.inner.num_cpus()
    }
    
    /// Get a reference to the stats
    pub fn stats(&self) -> &PerCpuMapStats {
        self.inner.stats()
    }
    
    /// Check if a CPU is online
    pub fn is_cpu_online(&self, cpu: usize) -> bool {
        self.inner.is_cpu_online(cpu)
    }
    
    /// Set CPU online status
    pub fn set_cpu_online(&mut self, cpu: usize, online: bool) {
        self.inner.set_cpu_online(cpu, online);
    }
    
    /// Lookup a key's value for a specific CPU
    pub fn lookup_cpu(&self, key: &K, cpu: usize) -> Result<Option<V>, MapError> {
        self.inner.lookup_cpu(key, cpu)
    }
    
    /// Lookup a key's values for all CPUs
    pub fn lookup_all_cpus(&self, key: &K) -> Result<Option<Vec<Option<V>>>, MapError> {
        self.inner.lookup_all_cpus(key)
    }
    
    /// Update a key's value for a specific CPU
    pub fn update_cpu(&self, key: &K, value: &V, cpu: usize) -> Result<(), MapError> {
        self.inner.update_cpu(key, value, cpu)
    }
    
    /// Delete a key and its values for all CPUs
    pub fn delete(&self, key: &K) -> Result<bool, MapError> {
        self.inner.delete(key)
    }
    
    /// Aggregate values from all CPUs
    pub fn aggregate<R, F>(&self, key: &K, aggregator: F) -> Result<Option<R>, MapError>
    where
        F: Fn(Vec<Option<V>>) -> R,
    {
        self.inner.aggregate(key, aggregator)
    }
    
    /// Sum values from all CPUs (only for numeric types)
    pub fn sum(&self, key: &K) -> Result<Option<V>, MapError>
    where
        V: std::ops::Add<Output = V> + Default + Copy,
    {
        self.aggregate(key, |values| {
            values.into_iter()
                .filter_map(|v| v)
                .fold(V::default(), |acc, v| acc + v)
        })
    }

    /// Looks up a value in the current CPU's data area.
    ///
    /// This is a convenience method that automatically determines the current CPU.
    ///
    /// # Returns
    ///
    /// An `Option` containing the value if found, or `None` if not found
    pub fn lookup(&self, key: K) -> Option<V> {
        let cpu_id = self.get_current_cpu();
        match self.lookup_cpu(&key, cpu_id) {
            Ok(value) => value,
            Err(e) => {
                warn!("Error looking up key in per-CPU map: {}", e);
                None
            }
        }
    }

    /// Looks up a value aggregated across all CPUs.
    ///
    /// This is a convenience method that uses the sum aggregator.
    ///
    /// # Returns
    ///
    /// An `Option` containing the aggregated value if found, or `None` if not found
    pub fn lookup_aggregated(&self, key: K) -> Option<V>
    where
        V: std::ops::Add<Output = V> + Default + Copy,
    {
        match self.sum(&key) {
            Ok(value) => value,
            Err(e) => {
                warn!("Error aggregating values in per-CPU map: {}", e);
                None
            }
        }
    }

    /// Updates a value in the current CPU's data area.
    ///
    /// This is a convenience method that automatically determines the current CPU.
    ///
    /// # Returns
    ///
    /// A `Result` indicating success or an error if the update fails
    pub fn update(&self, key: K, value: V) -> Result<(), Box<dyn std::error::Error>> {
        let cpu_id = self.get_current_cpu();
        match self.update_cpu(&key, &value, cpu_id) {
            Ok(_) => Ok(()),
            Err(e) => Err(Box::new(e)),
        }
    }

    /// Gets the current CPU ID.
    ///
    /// # Returns
    ///
    /// The ID of the current CPU
    fn get_current_cpu(&self) -> usize {
        // This is a simple implementation that might not be accurate in all cases
        // In a real implementation, we would use platform-specific methods
        // For Linux, this might involve reading /proc/self/stat or using sched_getcpu()
        
        // For now, we'll use a simple heuristic based on the thread ID
        let thread_id = std::thread::current().id().as_u64().get();
        (thread_id as usize) % self.num_cpus()
    }
}

// Add a method to get the thread ID as u64
trait ThreadIdExt {
    fn as_u64(&self) -> std::num::NonZeroU64;
}

impl ThreadIdExt for std::thread::ThreadId {
    fn as_u64(&self) -> std::num::NonZeroU64 {
        // This is a hack to get the thread ID as a number
        // The actual implementation might vary depending on the platform
        let id_str = format!("{:?}", self);
        let id_str = id_str.trim_start_matches("ThreadId(").trim_end_matches(")");
        let id = id_str.parse::<u64>().unwrap_or(1);
        std::num::NonZeroU64::new(id).unwrap_or(std::num::NonZeroU64::new(1).unwrap())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_percpumap_basic() {
        // Create a map
        let map = TypedOptimizedPerCpuMap::<u32, String>::create(1024).unwrap();
        
        // Test CPU count
        assert_eq!(map.num_cpus(), num_cpus::get());
        
        // Test CPU online status
        assert!(map.is_cpu_online(0));
        assert!(!map.is_cpu_online(map.num_cpus()));
        
        // Test update and lookup
        map.update_cpu(&1, &"value1".to_string(), 0).unwrap();
        let value = map.lookup_cpu(&1, 0).unwrap();
        assert_eq!(value, Some("value1".to_string()));
        
        // Test delete
        map.delete(&1).unwrap();
        let value = map.lookup_cpu(&1, 0).unwrap();
        assert_eq!(value, None);
        
        // Test statistics
        let stats = map.stats();
        assert_eq!(stats.lookups, 2);
        assert_eq!(stats.updates, 1);
        assert_eq!(stats.deletes, 1);
        
        // Test reset statistics
        map.stats().lookups = 0;
        map.stats().updates = 0;
        map.stats().deletes = 0;
        let stats = map.stats();
        assert_eq!(stats.lookups, 0);
        assert_eq!(stats.updates, 0);
        assert_eq!(stats.deletes, 0);
    }
    
    #[test]
    fn test_percpumap_aggregate() {
        // Create a map
        let map = TypedOptimizedPerCpuMap::<u32, u64>::create(1024).unwrap();
        
        // Update values on different CPUs
        for cpu in 0..map.num_cpus() {
            map.update_cpu(&1, &(cpu as u64 + 1), cpu).unwrap();
        }
        
        // Test sum aggregation
        let sum = map.aggregate(&1, |values| {
            let sum: u64 = values.iter().sum();
            Some(sum)
        }).unwrap();
        
        // Expected sum: 1 + 2 + ... + num_cpus
        let expected_sum = (1..=map.num_cpus() as u64).sum();
        assert_eq!(sum, Some(expected_sum));
        
        // Test max aggregation
        let max = map.aggregate(&1, |values| {
            values.iter().max().cloned()
        }).unwrap();
        
        assert_eq!(max, Some(map.num_cpus() as u64));
        
        // Test statistics
        let stats = map.stats();
        assert_eq!(stats.aggregations, 2);
    }
    
    #[test]
    fn test_percpumap_complex_types() {
        #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
        struct TestStruct {
            id: u32,
            name: String,
            values: Vec<f64>,
        }
        
        // Create a map
        let map = TypedOptimizedPerCpuMap::<String, TestStruct>::create(1024).unwrap();
        
        // Test update and lookup with complex types
        let key = "test_key".to_string();
        let value = TestStruct {
            id: 1,
            name: "Test".to_string(),
            values: vec![1.0, 2.0, 3.0],
        };
        
        map.update_cpu(&key, &value, 0).unwrap();
        let result = map.lookup_cpu(&key, 0).unwrap();
        assert_eq!(result, Some(value));
    }
} 