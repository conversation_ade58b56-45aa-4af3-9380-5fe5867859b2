
use std::error::Error;


pub mod lsm_hooker;
pub mod lsm_percpu_hooker;
pub mod xdp_hooker;
pub mod xdp_percpu_hooker;
pub mod tracepoint_hooker;
pub mod tracepoint_percpu_hooker;
pub mod uprobe_hooker;
pub mod uprobe_percpu_hooker;

// Re-exports
pub use lsm_hooker::{LsmHooker, LsmEvent, LsmHookerConfig, LsmHookerStats};
pub use lsm_percpu_hooker::{LsmPerCpuHooker, LsmEventStats};
pub use xdp_hooker::{XdpHooker, XdpEvent, XdpHookerConfig, XdpHookerStats};
pub use xdp_percpu_hooker::{XdpPerCpuHooker, XdpEventStats};
pub use tracepoint_hooker::{TracepointHooker, TracepointEvent, TracepointHookerConfig, TracepointHookerStats};
pub use tracepoint_percpu_hooker::{TracepointPerCpuHooker, TracepointEventStats, TracepointKey};
pub use uprobe_hooker::{UprobeHooker, UprobeEvent, UprobeHookerConfig, UprobeHookerStats};
pub use uprobe_percpu_hooker::{UprobePerCpuHooker, UprobeEventStats, UprobeKey};

/// Hooker error
#[derive(Debug)]
pub enum HookerError {
    /// Map creation error
    MapCreationError(String),
    /// Map operation error
    MapOperationError(String),
    /// Operation error
    OperationError(String),
    /// Configuration error
    ConfigurationError(String),
    /// Elasticsearch error
    ElasticsearchError(String),
}

impl fmt::Display for HookerError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            HookerError::MapCreationError(msg) => write!(f, "Map creation error: {}", msg),
            HookerError::MapOperationError(msg) => write!(f, "Map operation error: {}", msg),
            HookerError::OperationError(msg) => write!(f, "Operation error: {}", msg),
            HookerError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
            HookerError::ElasticsearchError(msg) => write!(f, "Elasticsearch error: {}", msg),
        }
    }
}

impl Error for HookerError {}

/// Hooker statistics trait
pub trait HookerStats {
    /// Get uptime
    fn uptime(&self) -> Duration;
    
    /// Get time since last update
    fn last_update(&self) -> Duration;
}

/// Hooker trait
pub trait Hooker {
    /// Get hooker name
    fn name(&self) -> &str;
    
    /// Get hooker version
    fn version(&self) -> &str;
    
    /// Get hooker description
    fn description(&self) -> &str;
    
    /// Get hooker statistics
    fn stats(&self) -> Box<dyn HookerStats>;
    
    /// Reset hooker statistics
    fn reset_stats(&self);
    
    /// Export data to Elasticsearch
    fn export_to_elasticsearch(&self) -> Result<usize, HookerError>;
} 