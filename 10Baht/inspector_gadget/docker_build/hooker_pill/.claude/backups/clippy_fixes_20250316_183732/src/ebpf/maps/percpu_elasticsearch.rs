use crate::core::exporter::elasticsearch::ElasticsearchExporter;
use crate::ebpf::maps::typed_optimized_percpu_map::TypedOptimizedPerCpuMap;
use crate::logging::elasticsearch::ElasticsearchLogger;
use serde::{Serialize, de::DeserializeOwned};
use std::error::Error;
use std::hash::Hash;
use std::time::{SystemTime, UNIX_EPOCH, Duration};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, Mutex};
use std::ops::Add;

/// Statistics for a Per-CPU Map that can be exported to Elasticsearch
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct PerCpuMapStats {
    /// Name of the map
    pub map_name: String,
    /// Timestamp when the stats were collected
    pub timestamp: u64,
    /// Number of entries in the map
    pub entry_count: usize,
    /// Number of lookups performed
    pub lookups: usize,
    /// Number of lookup hits
    pub lookup_hits: usize,
    /// Number of lookup misses
    pub lookup_misses: usize,
    /// Number of updates performed
    pub updates: usize,
    /// Number of deletes performed
    pub deletes: usize,
    /// Number of errors encountered
    pub errors: usize,
    /// CPU-specific statistics
    pub cpu_stats: HashMap<usize, CpuMapStats>,
    /// Performance metrics
    pub performance: PerformanceMetrics,
    /// Time-series data for the last minute (collected every second)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub time_series: Option<Vec<TimeSeriesPoint>>,
}

/// Statistics for a specific CPU in a Per-CPU Map
#[derive(Debug, Clone, Serialize)]
pub struct CpuMapStats {
    /// CPU ID
    pub cpu_id: usize,
    /// Number of lookups performed on this CPU
    pub lookups: usize,
    /// Number of lookup hits on this CPU
    pub lookup_hits: usize,
    /// Number of lookup misses on this CPU
    pub lookup_misses: usize,
    /// Number of updates performed on this CPU
    pub updates: usize,
    /// Number of deletes performed on this CPU
    pub deletes: usize,
    /// Number of errors encountered on this CPU
    pub errors: usize,
    /// CPU utilization percentage (0-100)
    pub cpu_utilization: f64,
    /// Memory usage in bytes
    pub memory_usage: usize,
}

/// Performance metrics for a Per-CPU Map
#[derive(Debug, Clone, Serialize, Default)]
pub struct PerformanceMetrics {
    /// Average lookup time in nanoseconds
    pub avg_lookup_time_ns: f64,
    /// Average update time in nanoseconds
    pub avg_update_time_ns: f64,
    /// Average aggregated lookup time in nanoseconds
    pub avg_aggregated_lookup_time_ns: f64,
    /// Maximum lookup time in nanoseconds
    pub max_lookup_time_ns: u64,
    /// Maximum update time in nanoseconds
    pub max_update_time_ns: u64,
    /// Maximum aggregated lookup time in nanoseconds
    pub max_aggregated_lookup_time_ns: u64,
    /// Lookup operations per second
    pub lookups_per_second: f64,
    /// Update operations per second
    pub updates_per_second: f64,
    /// Hit ratio (hits / lookups)
    pub hit_ratio: f64,
}

/// Time-series data point for a Per-CPU Map
#[derive(Debug, Clone, Serialize)]
pub struct TimeSeriesPoint {
    /// Timestamp of the data point
    pub timestamp: u64,
    /// Number of lookups in this time period
    pub lookups: usize,
    /// Number of updates in this time period
    pub updates: usize,
    /// Number of errors in this time period
    pub errors: usize,
    /// Average lookup time in nanoseconds during this period
    pub avg_lookup_time_ns: f64,
    /// Average update time in nanoseconds during this period
    pub avg_update_time_ns: f64,
    /// CPU utilization per CPU during this period
    pub cpu_utilization: HashMap<usize, f64>,
}

/// Visualization configuration for Kibana dashboards
#[derive(Debug, Clone, Serialize)]
pub struct KibanaDashboardConfig {
    /// Dashboard title
    pub title: String,
    /// Dashboard description
    pub description: String,
    /// List of visualizations to include
    pub visualizations: Vec<KibanaVisualization>,
    /// Time range for the dashboard
    pub time_range: KibanaTimeRange,
}

/// Kibana visualization configuration
#[derive(Debug, Clone, Serialize)]
pub struct KibanaVisualization {
    /// Visualization title
    pub title: String,
    /// Visualization type (e.g., "line", "bar", "pie", "metric")
    pub visualization_type: String,
    /// Index pattern to use
    pub index_pattern: String,
    /// Fields to include in the visualization
    pub fields: Vec<String>,
    /// Aggregation type (e.g., "sum", "avg", "min", "max")
    pub aggregation: String,
    /// Time field for time-based visualizations
    pub time_field: Option<String>,
    /// Interval for time-based visualizations (e.g., "1m", "5m", "1h")
    pub interval: Option<String>,
}

/// Kibana time range configuration
#[derive(Debug, Clone, Serialize)]
pub struct KibanaTimeRange {
    /// Start time (e.g., "now-15m", "now-1h", "now-1d")
    pub from: String,
    /// End time (usually "now")
    pub to: String,
    /// Refresh interval (e.g., "5s", "10s", "1m")
    pub refresh_interval: String,
}

/// Extension trait for TypedOptimizedPerCpuMap to add Elasticsearch integration
pub trait PerCpuMapElasticsearchExt<K, V>
where
    K: Serialize + DeserializeOwned + Copy + Eq + Hash,
    V: Serialize + DeserializeOwned + Copy,
{
    /// Get statistics for the map
    fn get_stats(&self) -> PerCpuMapStats;
    
    /// Get statistics for the map with time-series data
    fn get_stats_with_time_series(&self) -> PerCpuMapStats;
    
    /// Export map statistics to Elasticsearch
    fn export_stats_to_elasticsearch(&self, exporter: &ElasticsearchExporter) -> Result<(), Box<dyn Error>>;
    
    /// Export map data to Elasticsearch
    fn export_data_to_elasticsearch<F>(&self, exporter: &ElasticsearchExporter, transform: F) -> Result<(), Box<dyn Error>>
    where
        F: Fn(&K, &V) -> serde_json::Value;
        
    /// Start periodic export of map statistics to Elasticsearch
    fn start_periodic_stats_export(&self, exporter: Arc<ElasticsearchExporter>, interval_secs: u64) -> std::thread::JoinHandle<()>;
    
    /// Start collecting time-series data for the map
    fn start_time_series_collection(&self, max_points: usize) -> Arc<Mutex<VecDeque<TimeSeriesPoint>>>;
    
    /// Export time-series data to Elasticsearch
    fn export_time_series_to_elasticsearch(&self, exporter: &ElasticsearchExporter, time_series: &VecDeque<TimeSeriesPoint>) -> Result<(), Box<dyn Error>>;
    
    /// Create a Kibana dashboard for the map
    fn create_kibana_dashboard(&self, exporter: &ElasticsearchExporter) -> Result<String, Box<dyn Error>>;
}

/// Struct to track performance metrics for operations
#[derive(Debug, Clone)]
struct PerformanceTracker {
    lookup_times: VecDeque<u64>,
    update_times: VecDeque<u64>,
    aggregated_lookup_times: VecDeque<u64>,
    max_samples: usize,
}

impl PerformanceTracker {
    fn new(max_samples: usize) -> Self {
        Self {
            lookup_times: VecDeque::with_capacity(max_samples),
            update_times: VecDeque::with_capacity(max_samples),
            aggregated_lookup_times: VecDeque::with_capacity(max_samples),
            max_samples,
        }
    }
    
    fn add_lookup_time(&mut self, time_ns: u64) {
        if self.lookup_times.len() >= self.max_samples {
            self.lookup_times.pop_front();
        }
        self.lookup_times.push_back(time_ns);
    }
    
    fn add_update_time(&mut self, time_ns: u64) {
        if self.update_times.len() >= self.max_samples {
            self.update_times.pop_front();
        }
        self.update_times.push_back(time_ns);
    }
    
    fn add_aggregated_lookup_time(&mut self, time_ns: u64) {
        if self.aggregated_lookup_times.len() >= self.max_samples {
            self.aggregated_lookup_times.pop_front();
        }
        self.aggregated_lookup_times.push_back(time_ns);
    }
    
    fn get_avg_lookup_time(&self) -> f64 {
        if self.lookup_times.is_empty() {
            0.0
        } else {
            self.lookup_times.iter().sum::<u64>() as f64 / self.lookup_times.len() as f64
        }
    }
    
    fn get_avg_update_time(&self) -> f64 {
        if self.update_times.is_empty() {
            0.0
        } else {
            self.update_times.iter().sum::<u64>() as f64 / self.update_times.len() as f64
        }
    }
    
    fn get_avg_aggregated_lookup_time(&self) -> f64 {
        if self.aggregated_lookup_times.is_empty() {
            0.0
        } else {
            self.aggregated_lookup_times.iter().sum::<u64>() as f64 / self.aggregated_lookup_times.len() as f64
        }
    }
    
    fn get_max_lookup_time(&self) -> u64 {
        self.lookup_times.iter().max().copied().unwrap_or(0)
    }
    
    fn get_max_update_time(&self) -> u64 {
        self.update_times.iter().max().copied().unwrap_or(0)
    }
    
    fn get_max_aggregated_lookup_time(&self) -> u64 {
        self.aggregated_lookup_times.iter().max().copied().unwrap_or(0)
    }
}

impl<K, V> PerCpuMapElasticsearchExt<K, V> for TypedOptimizedPerCpuMap<K, V>
where
    K: Serialize + DeserializeOwned + Copy + Eq + Hash + Send + Sync + 'static,
    V: Serialize + DeserializeOwned + Copy + Send + Sync + 'static,
{
    fn get_stats(&self) -> PerCpuMapStats {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
            
        // In a real implementation, we would collect actual statistics from the map
        // For now, we'll return placeholder values with enhanced metrics
        let mut cpu_stats = HashMap::new();
        for cpu_id in 0..self.get_cpu_count() {
            cpu_stats.insert(cpu_id, CpuMapStats {
                cpu_id,
                lookups: 0,
                lookup_hits: 0,
                lookup_misses: 0,
                updates: 0,
                deletes: 0,
                errors: 0,
                cpu_utilization: 0.0, // Placeholder
                memory_usage: 0,      // Placeholder
            });
        }
        
        PerCpuMapStats {
            map_name: self.get_name().to_string(),
            timestamp: now,
            entry_count: 0, // Placeholder
            lookups: 0,     // Placeholder
            lookup_hits: 0, // Placeholder
            lookup_misses: 0, // Placeholder
            updates: 0,     // Placeholder
            deletes: 0,     // Placeholder
            errors: 0,      // Placeholder
            cpu_stats,
            performance: PerformanceMetrics::default(),
            time_series: None,
        }
    }
    
    fn get_stats_with_time_series(&self) -> PerCpuMapStats {
        let mut stats = self.get_stats();
        
        // In a real implementation, we would collect actual time-series data
        // For now, we'll generate some placeholder time-series data
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
            
        let mut time_series = Vec::new();
        for i in 0..60 {
            let timestamp = now - (60 - i);
            let mut cpu_utilization = HashMap::new();
            
            for cpu_id in 0..self.get_cpu_count() {
                // Generate random CPU utilization between 0 and 100
                let utilization = (timestamp % 100) as f64;
                cpu_utilization.insert(cpu_id, utilization);
            }
            
            time_series.push(TimeSeriesPoint {
                timestamp,
                lookups: (i * 10) as usize, // Placeholder
                updates: (i * 5) as usize,  // Placeholder
                errors: 0,                  // Placeholder
                avg_lookup_time_ns: 100.0,  // Placeholder
                avg_update_time_ns: 200.0,  // Placeholder
                cpu_utilization,
            });
        }
        
        stats.time_series = Some(time_series);
        stats
    }
    
    fn export_stats_to_elasticsearch(&self, exporter: &ElasticsearchExporter) -> Result<(), Box<dyn Error>> {
        let stats = self.get_stats();
        let index_name = format!("percpu_map_stats_{}", self.get_name());
        
        exporter.export_document(&index_name, &stats)?;
        Ok(())
    }
    
    fn export_data_to_elasticsearch<F>(&self, exporter: &ElasticsearchExporter, transform: F) -> Result<(), Box<dyn Error>>
    where
        F: Fn(&K, &V) -> serde_json::Value,
    {
        let index_name = format!("percpu_map_data_{}", self.get_name());
        
        // In a real implementation, we would iterate over all entries in the map
        // For now, we'll just log a message
        log::info!("Exporting data from map {} to Elasticsearch index {}", self.get_name(), index_name);
        
        Ok(())
    }
    
    fn start_periodic_stats_export(&self, exporter: Arc<ElasticsearchExporter>, interval_secs: u64) -> std::thread::JoinHandle<()> {
        let map_name = self.get_name().to_string();
        let map_clone = self.clone();
        
        std::thread::spawn(move || {
            log::info!("Starting periodic export of stats for map {} to Elasticsearch", map_name);
            
            // Create a time-series collection
            let time_series = map_clone.start_time_series_collection(3600); // Keep 1 hour of data
            
            loop {
                // Export regular stats
                if let Err(e) = map_clone.export_stats_to_elasticsearch(&exporter) {
                    log::error!("Failed to export stats for map {} to Elasticsearch: {}", map_name, e);
                }
                
                // Export time-series data
                let time_series_data = time_series.lock().unwrap();
                if let Err(e) = map_clone.export_time_series_to_elasticsearch(&exporter, &time_series_data) {
                    log::error!("Failed to export time-series data for map {} to Elasticsearch: {}", map_name, e);
                }
                
                std::thread::sleep(std::time::Duration::from_secs(interval_secs));
            }
        })
    }
    
    fn start_time_series_collection(&self, max_points: usize) -> Arc<Mutex<VecDeque<TimeSeriesPoint>>> {
        let time_series = Arc::new(Mutex::new(VecDeque::with_capacity(max_points)));
        let map_name = self.get_name().to_string();
        let time_series_clone = Arc::clone(&time_series);
        
        std::thread::spawn(move || {
            log::info!("Starting time-series collection for map {}", map_name);
            
            loop {
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs();
                    
                let mut cpu_utilization = HashMap::new();
                
                // In a real implementation, we would collect actual CPU utilization
                // For now, we'll generate random values
                for cpu_id in 0..num_cpus::get() {
                    let utilization = (now % 100) as f64;
                    cpu_utilization.insert(cpu_id, utilization);
                }
                
                let point = TimeSeriesPoint {
                    timestamp: now,
                    lookups: 0,      // Placeholder
                    updates: 0,      // Placeholder
                    errors: 0,       // Placeholder
                    avg_lookup_time_ns: 0.0, // Placeholder
                    avg_update_time_ns: 0.0, // Placeholder
                    cpu_utilization,
                };
                
                let mut time_series = time_series_clone.lock().unwrap();
                if time_series.len() >= max_points {
                    time_series.pop_front();
                }
                time_series.push_back(point);
                
                std::thread::sleep(Duration::from_secs(1));
            }
        });
        
        time_series
    }
    
    fn export_time_series_to_elasticsearch(&self, exporter: &ElasticsearchExporter, time_series: &VecDeque<TimeSeriesPoint>) -> Result<(), Box<dyn Error>> {
        let index_name = format!("percpu_map_timeseries_{}", self.get_name());
        
        for point in time_series {
            exporter.export_document(&index_name, point)?;
        }
        
        Ok(())
    }
    
    fn create_kibana_dashboard(&self, exporter: &ElasticsearchExporter) -> Result<String, Box<dyn Error>> {
        let map_name = self.get_name();
        let dashboard_title = format!("Per-CPU Map Dashboard - {}", map_name);
        
        // Create visualizations for the dashboard
        let visualizations = vec![
            // CPU Utilization Line Chart
            KibanaVisualization {
                title: format!("{} - CPU Utilization", map_name),
                visualization_type: "line".to_string(),
                index_pattern: format!("percpu_map_timeseries_{}", map_name),
                fields: vec!["cpu_utilization".to_string()],
                aggregation: "avg".to_string(),
                time_field: Some("timestamp".to_string()),
                interval: Some("1m".to_string()),
            },
            // Lookups and Updates Bar Chart
            KibanaVisualization {
                title: format!("{} - Operations", map_name),
                visualization_type: "bar".to_string(),
                index_pattern: format!("percpu_map_timeseries_{}", map_name),
                fields: vec!["lookups".to_string(), "updates".to_string()],
                aggregation: "sum".to_string(),
                time_field: Some("timestamp".to_string()),
                interval: Some("1m".to_string()),
            },
            // Performance Metrics
            KibanaVisualization {
                title: format!("{} - Performance", map_name),
                visualization_type: "metric".to_string(),
                index_pattern: format!("percpu_map_stats_{}", map_name),
                fields: vec![
                    "performance.avg_lookup_time_ns".to_string(),
                    "performance.avg_update_time_ns".to_string(),
                    "performance.avg_aggregated_lookup_time_ns".to_string(),
                ],
                aggregation: "avg".to_string(),
                time_field: Some("timestamp".to_string()),
                interval: None,
            },
            // Hit Ratio Gauge
            KibanaVisualization {
                title: format!("{} - Hit Ratio", map_name),
                visualization_type: "gauge".to_string(),
                index_pattern: format!("percpu_map_stats_{}", map_name),
                fields: vec!["performance.hit_ratio".to_string()],
                aggregation: "avg".to_string(),
                time_field: Some("timestamp".to_string()),
                interval: None,
            },
        ];
        
        // Create dashboard configuration
        let dashboard_config = KibanaDashboardConfig {
            title: dashboard_title.clone(),
            description: format!("Dashboard for Per-CPU Map {}", map_name),
            visualizations,
            time_range: KibanaTimeRange {
                from: "now-1h".to_string(),
                to: "now".to_string(),
                refresh_interval: "10s".to_string(),
            },
        };
        
        // In a real implementation, we would create the dashboard in Kibana
        // For now, we'll just log a message and return the dashboard title
        log::info!("Created Kibana dashboard: {}", dashboard_title);
        
        Ok(dashboard_title)
    }
}

/// Helper function to create an Elasticsearch exporter for Per-CPU Map data
pub fn create_percpu_map_elasticsearch_exporter(
    elasticsearch_url: &str,
    username: Option<&str>,
    password: Option<&str>,
) -> Result<ElasticsearchExporter, Box<dyn Error>> {
    let mut exporter = ElasticsearchExporter::new(elasticsearch_url)?;
    
    if let (Some(username), Some(password)) = (username, password) {
        exporter.set_auth(username, password);
    }
    
    // Configure index templates for Per-CPU Map data
    let stats_template = serde_json::json!({
        "index_patterns": ["percpu_map_stats_*"],
        "template": {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings": {
                "properties": {
                    "map_name": { "type": "keyword" },
                    "timestamp": { "type": "date" },
                    "entry_count": { "type": "integer" },
                    "lookups": { "type": "integer" },
                    "lookup_hits": { "type": "integer" },
                    "lookup_misses": { "type": "integer" },
                    "updates": { "type": "integer" },
                    "deletes": { "type": "integer" },
                    "errors": { "type": "integer" },
                    "cpu_stats": {
                        "type": "nested",
                        "properties": {
                            "cpu_id": { "type": "integer" },
                            "lookups": { "type": "integer" },
                            "lookup_hits": { "type": "integer" },
                            "lookup_misses": { "type": "integer" },
                            "updates": { "type": "integer" },
                            "deletes": { "type": "integer" },
                            "errors": { "type": "integer" },
                            "cpu_utilization": { "type": "float" },
                            "memory_usage": { "type": "integer" }
                        }
                    },
                    "performance": {
                        "properties": {
                            "avg_lookup_time_ns": { "type": "float" },
                            "avg_update_time_ns": { "type": "float" },
                            "avg_aggregated_lookup_time_ns": { "type": "float" },
                            "max_lookup_time_ns": { "type": "long" },
                            "max_update_time_ns": { "type": "long" },
                            "max_aggregated_lookup_time_ns": { "type": "long" },
                            "lookups_per_second": { "type": "float" },
                            "updates_per_second": { "type": "float" },
                            "hit_ratio": { "type": "float" }
                        }
                    }
                }
            }
        }
    });
    
    let timeseries_template = serde_json::json!({
        "index_patterns": ["percpu_map_timeseries_*"],
        "template": {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings": {
                "properties": {
                    "timestamp": { "type": "date" },
                    "lookups": { "type": "integer" },
                    "updates": { "type": "integer" },
                    "errors": { "type": "integer" },
                    "avg_lookup_time_ns": { "type": "float" },
                    "avg_update_time_ns": { "type": "float" },
                    "cpu_utilization": {
                        "type": "object",
                        "dynamic": true
                    }
                }
            }
        }
    });
    
    exporter.create_template("percpu_map_stats_template", &stats_template)?;
    exporter.create_template("percpu_map_timeseries_template", &timeseries_template)?;
    
    Ok(exporter)
}

/// Helper function to create Kibana dashboards for all Per-CPU Maps
pub fn create_kibana_dashboards_for_all_maps(
    exporter: &ElasticsearchExporter,
    maps: &[TypedOptimizedPerCpuMap<impl Serialize + DeserializeOwned + Copy + Eq + Hash + Send + Sync + 'static, impl Serialize + DeserializeOwned + Copy + Send + Sync + 'static>],
) -> Result<Vec<String>, Box<dyn Error>> {
    let mut dashboard_urls = Vec::new();
    
    for map in maps {
        let dashboard_url = map.create_kibana_dashboard(exporter)?;
        dashboard_urls.push(dashboard_url);
    }
    
    Ok(dashboard_urls)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::ebpf::maps::MapConfig;
    
    #[test]
    fn test_get_stats() {
        let config = MapConfig {
            name: "test_map".to_string(),
            max_entries: 1024,
            pinned: false,
            ..Default::default()
        };
        
        let map = TypedOptimizedPerCpuMap::<u32, u64>::new(config).expect("Failed to create map");
        let stats = map.get_stats();
        
        assert_eq!(stats.map_name, "test_map");
        assert!(stats.timestamp > 0);
        assert_eq!(stats.cpu_stats.len(), map.get_cpu_count());
    }
    
    #[test]
    fn test_get_stats_with_time_series() {
        let config = MapConfig {
            name: "test_map_ts".to_string(),
            max_entries: 1024,
            pinned: false,
            ..Default::default()
        };
        
        let map = TypedOptimizedPerCpuMap::<u32, u64>::new(config).expect("Failed to create map");
        let stats = map.get_stats_with_time_series();
        
        assert_eq!(stats.map_name, "test_map_ts");
        assert!(stats.timestamp > 0);
        assert_eq!(stats.cpu_stats.len(), map.get_cpu_count());
        assert!(stats.time_series.is_some());
        assert_eq!(stats.time_series.unwrap().len(), 60); // 60 seconds of data
    }
} 