use crate::ebpf::maps::TypedOptimizedPerCpuMap;
use crate::hookers::uprobe_hooker::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>robeHookerConfig};
use serde::{Serialize, Deserialize};
use std::error::Error;

use std::time::{Duration, Instant};


/// Statistics for Uprobe events by binary and function name
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UprobeEventStats {
    /// Number of function calls
    pub call_count: u64,
    /// Total duration of function calls (in nanoseconds)
    pub total_duration_ns: u64,
    /// Minimum duration of function calls (in nanoseconds)
    pub min_duration_ns: u64,
    /// Maximum duration of function calls (in nanoseconds)
    pub max_duration_ns: u64,
    /// Number of function calls with errors
    pub error_count: u64,
    /// Last event timestamp
    pub last_event_timestamp: u64,
}

impl Default for UprobeEventStats {
    fn default() -> Self {
        Self {
            call_count: 0,
            total_duration_ns: 0,
            min_duration_ns: u64::MAX,
            max_duration_ns: 0,
            error_count: 0,
            last_event_timestamp: 0,
        }
    }
}

impl std::ops::Add for UprobeEventStats {
    type Output = Self;
    
    fn add(self, other: Self) -> Self {
        Self {
            call_count: self.call_count + other.call_count,
            total_duration_ns: self.total_duration_ns + other.total_duration_ns,
            min_duration_ns: std::cmp::min(self.min_duration_ns, other.min_duration_ns),
            max_duration_ns: std::cmp::max(self.max_duration_ns, other.max_duration_ns),
            error_count: self.error_count + other.error_count,
            last_event_timestamp: std::cmp::max(self.last_event_timestamp, other.last_event_timestamp),
        }
    }
}

/// Key for uprobe event statistics
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct UprobeKey {
    /// Binary ID (hash of binary path)
    pub binary_id: u32,
    /// Function ID (hash of function name)
    pub function_id: u32,
}

/// Uprobe hooker with Per-CPU Maps for improved performance
pub struct UprobePerCpuHooker {
    /// Inner Uprobe hooker
    inner: UprobeHooker,
    /// Map to store event statistics by uprobe key
    function_stats: Arc<TypedOptimizedPerCpuMap<UprobeKey, UprobeEventStats>>,
    /// Map to store event statistics by process ID
    process_stats: Arc<TypedOptimizedPerCpuMap<u32, UprobeEventStats>>,
    /// Map to store event statistics by thread ID
    thread_stats: Arc<TypedOptimizedPerCpuMap<u32, UprobeEventStats>>,
    /// Last statistics export time
    last_export_time: Instant,
    /// Statistics export interval
    export_interval: Duration,
}

impl UprobePerCpuHooker {
    /// Creates a new Uprobe hooker with Per-CPU Maps
    pub fn new(config: UprobeHookerConfig) -> Result<Self, Box<dyn Error>> {
        // Create the inner Uprobe hooker
        let inner = UprobeHooker::new(config)?;
        
        // Create the Per-CPU Maps
        let function_stats = Arc::new(TypedOptimizedPerCpuMap::<UprobeKey, UprobeEventStats>::create(10240)?);
        let process_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, UprobeEventStats>::create(1024)?);
        let thread_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, UprobeEventStats>::create(4096)?);
        
        Ok(Self {
            inner,
            function_stats,
            process_stats,
            thread_stats,
            last_export_time: Instant::now(),
            export_interval: Duration::from_secs(60),
        })
    }
    
    /// Handles a Uprobe event
    pub fn handle_event(&self, event: &UprobeEvent) -> Result<(), Box<dyn Error>> {
        // Process the event with the inner hooker
        self.inner.handle_event(event)?;
        
        // Update statistics in the Per-CPU Maps
        self.update_event_stats(event)?;
        
        // Export statistics if the export interval has elapsed
        if self.last_export_time.elapsed() >= self.export_interval {
            self.export_statistics()?;
        }
        
        Ok(())
    }
    
    /// Updates event statistics in the Per-CPU Maps
    fn update_event_stats(&self, event: &UprobeEvent) -> Result<(), Box<dyn Error>> {
        // Get the current timestamp
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Create a key for the event
        let key = UprobeKey {
            binary_id: self.hash_string(&event.binary_path),
            function_id: self.hash_string(&event.function_name),
        };
        
        // Update function statistics
        let mut function_stats = self.function_stats.lookup(key).unwrap_or_default();
        
        // Update process statistics
        let pid = event.process_id;
        let mut process_stats = self.process_stats.lookup(pid).unwrap_or_default();
        
        // Update thread statistics if available
        if let Some(tid) = event.thread_id {
            let mut thread_stats = self.thread_stats.lookup(tid).unwrap_or_default();
            self.update_stats(&mut thread_stats, event, timestamp);
            self.thread_stats.update(tid, thread_stats)?;
        }
        
        // Update the statistics
        self.update_stats(&mut function_stats, event, timestamp);
        self.update_stats(&mut process_stats, event, timestamp);
        
        // Store the updated statistics
        self.function_stats.update(key, function_stats)?;
        self.process_stats.update(pid, process_stats)?;
        
        Ok(())
    }
    
    /// Helper method to update statistics
    fn update_stats(&self, stats: &mut UprobeEventStats, event: &UprobeEvent, timestamp: u64) {
        // Update call count
        stats.call_count += 1;
        
        // Update duration statistics if available
        if let Some(duration) = event.duration {
            stats.total_duration_ns += duration;
            stats.min_duration_ns = std::cmp::min(stats.min_duration_ns, duration);
            stats.max_duration_ns = std::cmp::max(stats.max_duration_ns, duration);
        }
        
        // Update error count
        if event.has_error {
            stats.error_count += 1;
        }
        
        // Update timestamp
        stats.last_event_timestamp = timestamp;
    }
    
    /// Exports statistics to external systems (e.g., Elasticsearch)
    fn export_statistics(&self) -> Result<(), Box<dyn Error>> {
        // This would be implemented to export statistics to external systems
        // For now, we'll just log some aggregated statistics
        
        // Get top 10 functions by call count
        let mut functions = Vec::new();
        
        // This is a simplified approach; in a real implementation, we would need a more efficient way
        // to iterate over the keys in the map
        for binary_id in 0..100 {
            for function_id in 0..100 {
                let key = UprobeKey {
                    binary_id,
                    function_id,
                };
                
                if let Some(stats) = self.function_stats.lookup_aggregated(key) {
                    if stats.call_count > 0 {
                        functions.push((key, stats));
                    }
                }
            }
        }
        
        // Sort by call count
        functions.sort_by(|a, b| b.1.call_count.cmp(&a.1.call_count));
        
        // Log the top 10 functions
        info!("Top functions by call count:");
        for (i, (key, stats)) in functions.iter().take(10).enumerate() {
            let avg_duration = if stats.call_count > 0 {
                stats.total_duration_ns / stats.call_count
            } else {
                0
            };
            
            info!(
                "{}. Function {}/{}: {} calls, {} errors, avg duration: {} ns, min: {} ns, max: {} ns",
                i + 1,
                key.binary_id,
                key.function_id,
                stats.call_count,
                stats.error_count,
                avg_duration,
                stats.min_duration_ns,
                stats.max_duration_ns
            );
        }
        
        // Get top 5 processes by call count
        let mut processes = Vec::new();
        for pid in 1..1024 {
            if let Some(stats) = self.process_stats.lookup_aggregated(pid) {
                if stats.call_count > 0 {
                    processes.push((pid, stats));
                }
            }
        }
        
        // Sort by call count
        processes.sort_by(|a, b| b.1.call_count.cmp(&a.1.call_count));
        
        // Log the top 5 processes
        info!("Top processes by call count:");
        for (i, (pid, stats)) in processes.iter().take(5).enumerate() {
            let avg_duration = if stats.call_count > 0 {
                stats.total_duration_ns / stats.call_count
            } else {
                0
            };
            
            info!(
                "{}. PID {}: {} calls, {} errors, avg duration: {} ns",
                i + 1,
                pid,
                stats.call_count,
                stats.error_count,
                avg_duration
            );
        }
        
        // Get top 5 threads by call count
        let mut threads = Vec::new();
        for tid in 1..4096 {
            if let Some(stats) = self.thread_stats.lookup_aggregated(tid) {
                if stats.call_count > 0 {
                    threads.push((tid, stats));
                }
            }
        }
        
        // Sort by call count
        threads.sort_by(|a, b| b.1.call_count.cmp(&a.1.call_count));
        
        // Log the top 5 threads
        info!("Top threads by call count:");
        for (i, (tid, stats)) in threads.iter().take(5).enumerate() {
            let avg_duration = if stats.call_count > 0 {
                stats.total_duration_ns / stats.call_count
            } else {
                0
            };
            
            info!(
                "{}. TID {}: {} calls, {} errors, avg duration: {} ns",
                i + 1,
                tid,
                stats.call_count,
                stats.error_count,
                avg_duration
            );
        }
        
        Ok(())
    }
    
    /// Gets aggregated statistics for a function
    pub fn get_function_stats(&self, binary_path: &str, function_name: &str) -> Option<UprobeEventStats> {
        let key = UprobeKey {
            binary_id: self.hash_string(binary_path),
            function_id: self.hash_string(function_name),
        };
        
        self.function_stats.lookup_aggregated(key)
    }
    
    /// Gets aggregated statistics for a process
    pub fn get_process_stats(&self, pid: u32) -> Option<UprobeEventStats> {
        self.process_stats.lookup_aggregated(pid)
    }
    
    /// Gets aggregated statistics for a thread
    pub fn get_thread_stats(&self, tid: u32) -> Option<UprobeEventStats> {
        self.thread_stats.lookup_aggregated(tid)
    }
    
    /// Gets the inner Uprobe hooker
    pub fn inner(&self) -> &UprobeHooker {
        &self.inner
    }
    
    /// Gets map statistics
    pub fn get_map_stats(&self) -> (u64, u64, u64) {
        let function_stats = self.function_stats.get_stats();
        let process_stats = self.process_stats.get_stats();
        let thread_stats = self.thread_stats.get_stats();
        
        let lookups = function_stats.lookups.load(std::sync::atomic::Ordering::Relaxed)
            + process_stats.lookups.load(std::sync::atomic::Ordering::Relaxed)
            + thread_stats.lookups.load(std::sync::atomic::Ordering::Relaxed);
            
        let updates = function_stats.updates.load(std::sync::atomic::Ordering::Relaxed)
            + process_stats.updates.load(std::sync::atomic::Ordering::Relaxed)
            + thread_stats.updates.load(std::sync::atomic::Ordering::Relaxed);
            
        let misses = function_stats.misses.load(std::sync::atomic::Ordering::Relaxed)
            + process_stats.misses.load(std::sync::atomic::Ordering::Relaxed)
            + thread_stats.misses.load(std::sync::atomic::Ordering::Relaxed);
            
        (lookups, updates, misses)
    }
    
    /// Helper method to hash a string to a u32
    fn hash_string(&self, s: &str) -> u32 {
        use std::hash::{Hash, Hasher};
        let mut hasher = std::collections::hash_map::DefaultHasher::new();
        s.hash(&mut hasher);
        hasher.finish() as u32
    }
} 