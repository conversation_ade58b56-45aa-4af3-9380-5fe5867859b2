/*!
 * eBPF Map Iterator
 * 
 * This module provides an iterator for traversing eBPF maps.
 */

use std::marker::PhantomData;
use super::{Map, MapError};

/// eBPF map iterator
#[derive(Debug)]
pub struct MapIter<'a> {
    /// Reference to the map
    map: &'a Map,
    /// Current key
    current_key: Option<Vec<u8>>,
    /// End of iteration flag
    done: bool,
}

impl<'a> MapIter<'a> {
    /// Create a new map iterator
    pub fn new(map: &'a Map) -> Self {
        Self {
            map,
            current_key: None,
            done: false,
        }
    }
}

impl<'a> Iterator for MapIter<'a> {
    type Item = Result<(Vec<u8>, Vec<u8>), MapError>;
    
    fn next(&mut self) -> Option<Self::Item> {
        if self.done {
            return None;
        }
        
        // Get the next key
        let next_key = match self.map.get_next_key(self.current_key.as_deref()) {
            Ok(Some(key)) => key,
            Ok(None) => {
                self.done = true;
                return None;
            }
            Err(e) => {
                self.done = true;
                return Some(Err(e));
            }
        };
        
        // Look up the value for this key
        let value = match self.map.lookup(&next_key) {
            Ok(value) => value,
            Err(e) => {
                self.done = true;
                return Some(Err(e));
            }
        };
        
        // Update the current key
        self.current_key = Some(next_key.clone());
        
        Some(Ok((next_key, value)))
    }
}

/// Typed map iterator
#[derive(Debug)]
pub struct TypedMapIter<'a, K, V> {
    /// Inner iterator
    inner: MapIter<'a>,
    /// Key type
    _key_type: PhantomData<K>,
    /// Value type
    _value_type: PhantomData<V>,
}

impl<'a, K, V> TypedMapIter<'a, K, V> {
    /// Create a new typed map iterator
    pub fn new(map: &'a Map) -> Self {
        Self {
            inner: MapIter::new(map),
            _key_type: PhantomData,
            _value_type: PhantomData,
        }
    }
}

impl<'a, K, V> Iterator for TypedMapIter<'a, K, V>
where
    K: serde::de::DeserializeOwned,
    V: serde::de::DeserializeOwned,
{
    type Item = Result<(K, V), MapError>;
    
    fn next(&mut self) -> Option<Self::Item> {
        self.inner.next().map(|result| {
            result.and_then(|(key_bytes, value_bytes)| {
                // Deserialize the key
                let key = bincode::deserialize(&key_bytes)
                    .map_err(|e| MapError::DeserializationError(format!("Failed to deserialize key: {}", e)))?;
                
                // Deserialize the value
                let value = bincode::deserialize(&value_bytes)
                    .map_err(|e| MapError::DeserializationError(format!("Failed to deserialize value: {}", e)))?;
                
                Ok((key, value))
            })
        })
    }
} 