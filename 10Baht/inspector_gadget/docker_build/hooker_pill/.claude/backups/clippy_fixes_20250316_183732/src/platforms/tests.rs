#[cfg(test)]
mod platform_tests {
    use super::*;

    #[test]
    fn test_detect_platform() {
        let platform = detect_platform();
        
        // This should work on either Windows or Linux
        assert!(platform == Platform::Windows || platform == Platform::Linux);
        
        // Check consistency with std::env
        let os = std::env::consts::OS;
        match os {
            "windows" => assert_eq!(platform, Platform::Windows),
            "linux" => assert_eq!(platform, Platform::Linux),
            _ => assert_eq!(platform, Platform::Unsupported),
        }
    }

    #[test]
    fn test_get_os_info() {
        let os_info = get_os_info();
        
        // Test should pass on supported platforms
        if detect_platform() != Platform::Unsupported {
            assert!(os_info.is_ok());
            
            if let Ok(info) = os_info {
                assert!(!info.version.is_empty());
                assert!(!info.architecture.is_empty());
                
                // Check platform matches what was detected
                assert_eq!(info.platform, detect_platform());
                
                // Check architecture matches what std reports
                assert_eq!(info.architecture, std::env::consts::ARCH);
            }
        } else {
            assert!(os_info.is_err());
        }
    }

    #[cfg(target_os = "windows")]
    #[test]
    fn test_windows_specific() {
        // Check if Windows-specific detection is working
        let platform = detect_platform();
        assert_eq!(platform, Platform::Windows);
        
        // Check Windows info
        let os_info = get_os_info();
        assert!(os_info.is_ok());
        
        if let Ok(info) = os_info {
            assert_eq!(info.platform, Platform::Windows);
            assert!(!info.version.is_empty());
        }
        
        // Check eBPF availability - result doesn't matter,
        // just test that the function runs
        let _ = crate::platforms::windows::is_ebpf_available();
    }

    #[cfg(target_os = "linux")]
    #[test]
    fn test_linux_specific() {
        // Check if Linux-specific detection is working
        let platform = detect_platform();
        assert_eq!(platform, Platform::Linux);
        
        // Check Linux info
        let os_info = get_os_info();
        assert!(os_info.is_ok());
        
        if let Ok(info) = os_info {
            assert_eq!(info.platform, Platform::Linux);
            assert!(!info.version.is_empty());
            
            // Check if version looks like a kernel version
            assert!(info.version.contains('.'));
        }
        
        // Check eBPF availability - result doesn't matter,
        // just test that the function runs
        let _ = crate::platforms::linux::is_ebpf_available();
    }
} 