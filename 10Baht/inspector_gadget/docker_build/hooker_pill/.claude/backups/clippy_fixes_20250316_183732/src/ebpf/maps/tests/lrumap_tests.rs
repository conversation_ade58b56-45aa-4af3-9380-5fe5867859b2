/*!
 * Tests for Optimized LRU Hash Map Implementation
 * 
 * This file contains integration tests for the optimized LRU hash map implementation.
 */

#[cfg(test)]
mod tests {
    use crate::ebpf::maps::{
        OptimizedLruMap, TypedOptimizedLruMap, HashFunction, MapError
    };
    use std::sync::atomic::Ordering;
    
    use std::time::{Duration, Instant};
    
    #[test]
    fn test_optimized_lru_map_performance() {
        // Create a standard hash map and an optimized LRU hash map
        let std_map = HashMap::new();
        let lru_map = OptimizedLruMap::create(8, 8, 10240, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test data
        let num_entries = 1000;
        let mut keys = Vec::with_capacity(num_entries);
        let mut values = Vec::with_capacity(num_entries);
        
        for i in 0..num_entries {
            let key = [
                (i & 0xFF) as u8,
                ((i >> 8) & 0xFF) as u8,
                ((i >> 16) & 0xFF) as u8,
                ((i >> 24) & 0xFF) as u8,
                0, 0, 0, 0
            ];
            let value = [
                ((i * 2) & 0xFF) as u8,
                (((i * 2) >> 8) & 0xFF) as u8,
                (((i * 2) >> 16) & 0xFF) as u8,
                (((i * 2) >> 24) & 0xFF) as u8,
                0, 0, 0, 0
            ];
            keys.push(key);
            values.push(value);
        }
        
        // Measure insert performance
        let start = Instant::now();
        for i in 0..num_entries {
            lru_map.update(&keys[i], &values[i], 0).unwrap();
        }
        let lru_insert_time = start.elapsed();
        
        // Measure lookup performance
        let start = Instant::now();
        for i in 0..num_entries {
            let _ = lru_map.lookup(&keys[i]).unwrap();
        }
        let lru_lookup_time = start.elapsed();
        
        // Measure batch lookup performance
        let key_refs: Vec<&[u8]> = keys.iter().map(|k| k.as_slice()).collect();
        let start = Instant::now();
        let results = lru_map.batch_lookup(&key_refs);
        let lru_batch_lookup_time = start.elapsed();
        
        // Verify batch lookup results
        for (i, result) in results.iter().enumerate() {
            assert!(result.is_ok());
            assert_eq!(result.as_ref().unwrap(), &values[i]);
        }
        
        // Print performance results
        println!("Optimized LRU Hash Map Performance:");
        println!("  Insert time for {} entries: {:?}", num_entries, lru_insert_time);
        println!("  Lookup time for {} entries: {:?}", num_entries, lru_lookup_time);
        println!("  Batch lookup time for {} entries: {:?}", num_entries, lru_batch_lookup_time);
        println!("  Average insert time: {:?} per entry", lru_insert_time / num_entries as u32);
        println!("  Average lookup time: {:?} per entry", lru_lookup_time / num_entries as u32);
        println!("  Average batch lookup time: {:?} per entry", lru_batch_lookup_time / num_entries as u32);
        
        // Check statistics
        println!("LRU Hash Map Statistics:");
        println!("  Lookups: {}", lru_map.stats().lookups.load(Ordering::Relaxed));
        println!("  Hits: {}", lru_map.stats().hits.load(Ordering::Relaxed));
        println!("  Misses: {}", lru_map.stats().misses.load(Ordering::Relaxed));
        println!("  Updates: {}", lru_map.stats().updates.load(Ordering::Relaxed));
        println!("  Evictions: {}", lru_map.stats().evictions.load(Ordering::Relaxed));
        println!("  Hit rate: {:.2}%", lru_map.stats().hit_rate() * 100.0);
        println!("  Eviction rate: {:.2}%", lru_map.stats().eviction_rate() * 100.0);
        
        // Verify statistics
        assert_eq!(lru_map.stats().lookups.load(Ordering::Relaxed), num_entries as u64 * 2); // Individual lookups + batch lookups
        assert_eq!(lru_map.stats().hits.load(Ordering::Relaxed), num_entries as u64 * 2);
        assert_eq!(lru_map.stats().updates.load(Ordering::Relaxed), num_entries as u64);
        assert!(lru_map.stats().hit_rate() > 0.99); // Should be very close to 1.0
    }
    
    #[test]
    fn test_typed_optimized_lru_map_performance() {
        // Create a typed optimized LRU hash map
        let map = TypedOptimizedLruMap::<String, u32>::create(10240, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test data
        let num_entries = 1000;
        let mut keys = Vec::with_capacity(num_entries);
        let mut values = Vec::with_capacity(num_entries);
        
        for i in 0..num_entries {
            keys.push(format!("key_{}", i));
            values.push(i as u32);
        }
        
        // Measure insert performance
        let start = Instant::now();
        for i in 0..num_entries {
            map.update(&keys[i], &values[i], 0).unwrap();
        }
        let insert_time = start.elapsed();
        
        // Measure lookup performance
        let start = Instant::now();
        for i in 0..num_entries {
            let result = map.lookup(&keys[i]).unwrap();
            assert_eq!(result, values[i]);
        }
        let lookup_time = start.elapsed();
        
        // Measure batch update performance
        let entries: Vec<(String, u32)> = keys.iter().zip(values.iter())
            .map(|(k, v)| (k.clone(), *v + 1000)) // Add 1000 to each value
            .collect();
            
        let start = Instant::now();
        map.batch_update(&entries, 0).unwrap();
        let batch_update_time = start.elapsed();
        
        // Measure batch lookup performance
        let start = Instant::now();
        let results = map.batch_lookup(&keys);
        let batch_lookup_time = start.elapsed();
        
        // Verify batch lookup results
        for (i, result) in results.iter().enumerate() {
            assert!(result.is_ok());
            assert_eq!(result.as_ref().unwrap(), &(values[i] + 1000));
        }
        
        // Print performance results
        println!("Typed Optimized LRU Hash Map Performance:");
        println!("  Insert time for {} entries: {:?}", num_entries, insert_time);
        println!("  Lookup time for {} entries: {:?}", num_entries, lookup_time);
        println!("  Batch update time for {} entries: {:?}", num_entries, batch_update_time);
        println!("  Batch lookup time for {} entries: {:?}", num_entries, batch_lookup_time);
        println!("  Average insert time: {:?} per entry", insert_time / num_entries as u32);
        println!("  Average lookup time: {:?} per entry", lookup_time / num_entries as u32);
        println!("  Average batch update time: {:?} per entry", batch_update_time / num_entries as u32);
        println!("  Average batch lookup time: {:?} per entry", batch_lookup_time / num_entries as u32);
        
        // Check statistics
        println!("LRU Hash Map Statistics:");
        println!("  Lookups: {}", map.stats().lookups.load(Ordering::Relaxed));
        println!("  Hits: {}", map.stats().hits.load(Ordering::Relaxed));
        println!("  Updates: {}", map.stats().updates.load(Ordering::Relaxed));
        println!("  Evictions: {}", map.stats().evictions.load(Ordering::Relaxed));
        println!("  Hit rate: {:.2}%", map.stats().hit_rate() * 100.0);
        println!("  Eviction rate: {:.2}%", map.stats().eviction_rate() * 100.0);
    }
    
    #[test]
    fn test_lru_eviction_behavior() {
        // Create a small LRU map to test eviction
        let map = OptimizedLruMap::create(8, 8, 3, Some(HashFunction::Fnv1a)).unwrap();
        
        // Insert 3 entries (fills the map)
        map.update(&[1, 0, 0, 0, 0, 0, 0, 0], &[1, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        map.update(&[2, 0, 0, 0, 0, 0, 0, 0], &[2, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        map.update(&[3, 0, 0, 0, 0, 0, 0, 0], &[3, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        
        // Verify all entries are present
        assert!(map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[2, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        
        // Access entries in a specific order to affect LRU order
        // The access order is: 2, 3, 1
        // So the LRU order should be: 2 (least recently used), 3, 1 (most recently used)
        map.lookup(&[2, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        
        // Insert a new entry, which should evict the least recently used (key 2)
        map.update(&[4, 0, 0, 0, 0, 0, 0, 0], &[4, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        
        // Check that key 2 was evicted
        assert!(map.lookup(&[2, 0, 0, 0, 0, 0, 0, 0]).is_err());
        
        // Check that the other keys are still there
        assert!(map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[4, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        
        // Now the LRU order should be: 3 (least recently used), 1, 4 (most recently used)
        // Insert another entry, which should evict key 3
        map.update(&[5, 0, 0, 0, 0, 0, 0, 0], &[5, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        
        // Check that key 3 was evicted
        assert!(map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).is_err());
        
        // Check that the other keys are still there
        assert!(map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[4, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[5, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        
        // Check eviction statistics
        assert_eq!(map.stats().evictions.load(Ordering::Relaxed), 2);
        
        // Calculate eviction rate
        let updates = map.stats().updates.load(Ordering::Relaxed);
        let evictions = map.stats().evictions.load(Ordering::Relaxed);
        let eviction_rate = evictions as f64 / updates as f64;
        
        println!("LRU Eviction Statistics:");
        println!("  Updates: {}", updates);
        println!("  Evictions: {}", evictions);
        println!("  Eviction rate: {:.2}%", eviction_rate * 100.0);
        
        // The eviction rate should be 2/5 = 0.4 (40%)
        assert!(eviction_rate > 0.39 && eviction_rate < 0.41);
    }
    
    #[test]
    fn test_lru_map_error_handling() {
        // Create a map with a small maximum number of entries
        let map = OptimizedLruMap::create(8, 8, 2, Some(HashFunction::Fnv1a)).unwrap();
        
        // Insert two entries
        map.update(&[1, 2, 3, 4, 5, 6, 7, 8], &[1, 1, 1, 1, 1, 1, 1, 1], 0).unwrap();
        map.update(&[2, 3, 4, 5, 6, 7, 8, 9], &[2, 2, 2, 2, 2, 2, 2, 2], 0).unwrap();
        
        // Try to insert a third entry, which should not fail but should evict the first entry
        let result = map.update(&[3, 4, 5, 6, 7, 8, 9, 10], &[3, 3, 3, 3, 3, 3, 3, 3], 0);
        
        // Check that the update succeeded
        assert!(result.is_ok());
        
        // Check that the first entry was evicted
        assert!(map.lookup(&[1, 2, 3, 4, 5, 6, 7, 8]).is_err());
        
        // Check that the other entries are still there
        assert!(map.lookup(&[2, 3, 4, 5, 6, 7, 8, 9]).is_ok());
        assert!(map.lookup(&[3, 4, 5, 6, 7, 8, 9, 10]).is_ok());
        
        // Check that the eviction count was incremented
        assert_eq!(map.stats().evictions.load(Ordering::Relaxed), 1);
    }
    
    #[test]
    fn test_lru_map_batch_operations() {
        // Create a small LRU map to test batch operations with eviction
        let map = OptimizedLruMap::create(8, 8, 3, Some(HashFunction::Fnv1a)).unwrap();
        
        // Insert 3 entries (fills the map)
        let entries = vec![
            ([1, 0, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0]),
            ([2, 0, 0, 0, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 0]),
            ([3, 0, 0, 0, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 0]),
        ];
        
        let entry_refs: Vec<(&[u8], &[u8])> = entries.iter()
            .map(|(k, v)| (k.as_slice(), v.as_slice()))
            .collect();
        
        map.batch_update(&entry_refs, 0).unwrap();
        
        // Verify all entries are present
        assert!(map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[2, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        
        // Access entries in a specific order to affect LRU order
        map.lookup(&[2, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        
        // Batch update with 2 new entries, which should evict the 2 least recently used entries
        let new_entries = vec![
            ([4, 0, 0, 0, 0, 0, 0, 0], [4, 0, 0, 0, 0, 0, 0, 0]),
            ([5, 0, 0, 0, 0, 0, 0, 0], [5, 0, 0, 0, 0, 0, 0, 0]),
        ];
        
        let new_entry_refs: Vec<(&[u8], &[u8])> = new_entries.iter()
            .map(|(k, v)| (k.as_slice(), v.as_slice()))
            .collect();
        
        map.batch_update(&new_entry_refs, 0).unwrap();
        
        // Check that keys 2 and 3 were evicted
        assert!(map.lookup(&[2, 0, 0, 0, 0, 0, 0, 0]).is_err());
        assert!(map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).is_err());
        
        // Check that the other keys are still there
        assert!(map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[4, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[5, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        
        // Check eviction statistics
        assert_eq!(map.stats().evictions.load(Ordering::Relaxed), 2);
        
        // Batch lookup
        let keys = vec![
            &[1, 0, 0, 0, 0, 0, 0, 0] as &[u8],
            &[4, 0, 0, 0, 0, 0, 0, 0] as &[u8],
            &[5, 0, 0, 0, 0, 0, 0, 0] as &[u8],
        ];
        
        let results = map.batch_lookup(&keys);
        
        // Verify batch lookup results
        for (i, result) in results.iter().enumerate() {
            assert!(result.is_ok());
            assert_eq!(result.as_ref().unwrap()[0], (i as u8) + 1);
        }
        
        // Batch delete
        map.batch_delete(&keys).unwrap();
        
        // Verify all entries were deleted
        assert!(map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).is_err());
        assert!(map.lookup(&[4, 0, 0, 0, 0, 0, 0, 0]).is_err());
        assert!(map.lookup(&[5, 0, 0, 0, 0, 0, 0, 0]).is_err());
    }
} 