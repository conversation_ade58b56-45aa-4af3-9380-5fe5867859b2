/*!
 * Windows Syscall Interception Module
 * 
 * This module provides functionality for intercepting and analyzing
 * Windows system calls using ETW (Event Tracing for Windows).
 */

use std::sync::{Arc, Mutex, atomic::{AtomicBool, Ordering}};
use std::collections::VecDeque;




use crate::platforms::Platform;
use crate::syscalls::common::{Syscall, SyscallEvent, SyscallFilter, SyscallCategory, SyscallStatistics};
use crate::syscalls::SyscallInterceptor;

pub mod etw;
pub mod syscall_table;

/// Error type for Windows syscall operations
#[derive(Error, Debug)]
pub enum WindowsSyscallError {
    #[error("ETW is not available on this system")]
    EtwNotAvailable,
    #[error("Failed to initialize ETW: {0}")]
    EtwInitializationError(String),
    #[error("Failed to start ETW tracing: {0}")]
    EtwStartError(String),
    #[error("Failed to stop ETW tracing: {0}")]
    EtwStopError(String),
    #[error("Syscall interception is already running")]
    AlreadyRunning,
    #[error("Syscall interception is not running")]
    NotRunning,
    #[error("Failed to collect syscall events: {0}")]
    CollectionError(String),
}

/// Windows syscall interceptor
pub struct WindowsSyscallInterceptor {
    /// ETW interceptor
    etw_interceptor: Arc<Mutex<etw::EtwInterceptor>>,
    /// Flag indicating if the interceptor is running
    running: Arc<AtomicBool>,
    /// Collected syscall events
    events: Arc<Mutex<VecDeque<SyscallEvent>>>,
    /// Filter for syscall events
    filter: Arc<Mutex<SyscallFilter>>,
    /// Statistics for syscall events
    statistics: Arc<Mutex<SyscallStatistics>>,
    /// Event callback
    event_callback: Option<Box<dyn Fn(&SyscallEvent) -> Result<()> + Send + Sync>>,
}

impl WindowsSyscallInterceptor {
    /// Create a new Windows syscall interceptor
    pub fn new() -> Result<Self, WindowsSyscallError> {
        // Check if ETW is available
        if !etw::is_etw_available() {
            return Err(WindowsSyscallError::EtwNotAvailable);
        }
        
        // Create ETW interceptor
        let etw_interceptor = match etw::EtwInterceptor::new() {
            Ok(interceptor) => Arc::new(Mutex::new(interceptor)),
            Err(err) => return Err(WindowsSyscallError::EtwInitializationError(err.to_string())),
        };
        
        Ok(Self {
            etw_interceptor,
            running: Arc::new(AtomicBool::new(false)),
            events: Arc::new(Mutex::new(VecDeque::new())),
            filter: Arc::new(Mutex::new(SyscallFilter::new())),
            statistics: Arc::new(Mutex::new(SyscallStatistics::new())),
            event_callback: None,
        })
    }
    
    /// Check if ETW is available on this system
    pub fn is_etw_available() -> bool {
        etw::is_etw_available()
    }
}

impl SyscallInterceptor for WindowsSyscallInterceptor {
    type Error = WindowsSyscallError;
    
    fn start(&self) -> Result<(), Self::Error> {
        // Check if already running
        if self.running.load(Ordering::SeqCst) {
            return Err(WindowsSyscallError::AlreadyRunning);
        }
        
        // Start ETW interceptor
        let mut etw = self.etw_interceptor.lock().unwrap();
        match etw.start() {
            Ok(_) => {
                self.running.store(true, Ordering::SeqCst);
                info!("Windows syscall interception started");
                Ok(())
            },
            Err(err) => Err(WindowsSyscallError::EtwStartError(err.to_string())),
        }
    }
    
    fn stop(&self) -> Result<(), Self::Error> {
        // Check if running
        if !self.running.load(Ordering::SeqCst) {
            return Err(WindowsSyscallError::NotRunning);
        }
        
        // Stop ETW interceptor
        let mut etw = self.etw_interceptor.lock().unwrap();
        match etw.stop() {
            Ok(_) => {
                self.running.store(false, Ordering::SeqCst);
                info!("Windows syscall interception stopped");
                Ok(())
            },
            Err(err) => Err(WindowsSyscallError::EtwStopError(err.to_string())),
        }
    }
    
    fn is_running(&self) -> bool {
        self.running.load(Ordering::SeqCst)
    }
    
    fn set_filter(&self, filter: SyscallFilter) -> Result<(), Self::Error> {
        let mut current_filter = self.filter.lock().unwrap();
        *current_filter = filter;
        Ok(())
    }
    
    fn get_filter(&self) -> Result<SyscallFilter, Self::Error> {
        let filter = self.filter.lock().unwrap();
        Ok(filter.clone())
    }
    
    fn collect_events(&self, max_events: usize) -> Result<Vec<SyscallEvent>, Self::Error> {
        // Check if running
        if !self.running.load(Ordering::SeqCst) {
            return Err(WindowsSyscallError::NotRunning);
        }
        
        // Collect events from ETW interceptor
        let mut etw = self.etw_interceptor.lock().unwrap();
        let new_events = match etw.collect_events() {
            Ok(events) => events,
            Err(err) => return Err(WindowsSyscallError::CollectionError(err.to_string())),
        };
        
        // Add events to the queue
        let mut events = self.events.lock().unwrap();
        for event in new_events {
            events.push_back(event);
        }
        
        // Apply filter and return events
        let filter = self.filter.lock().unwrap();
        let mut result = Vec::new();
        let mut stats = self.statistics.lock().unwrap();
        
        while let Some(event) = events.pop_front() {
            // Get syscall definition
            let syscall = crate::syscalls::common::mapping::get_syscall(Platform::Windows, event.syscall_id);
            
            // Apply filter
            if filter.matches(&event, syscall.as_ref()) {
                // Update statistics
                stats.update_with_event(&event, syscall.as_ref());
                
                // Add to result
                result.push(event);
                
                // Check if we have enough events
                if result.len() >= max_events {
                    break;
                }
            }
        }
        
        Ok(result)
    }
    
    fn get_statistics(&self) -> Result<SyscallStatistics, Self::Error> {
        let stats = self.statistics.lock().unwrap();
        Ok(stats.clone())
    }
    
    fn set_event_callback(&self, callback: Box<dyn Fn(&SyscallEvent) -> Result<()> + Send + Sync>) -> Result<(), Self::Error> {
        self.event_callback = Some(callback);
        Ok(())
    }
}

/// Initialize Windows syscall definitions
pub fn init_windows_syscalls() {
    info!("Initializing Windows syscall definitions");
    
    // Register common Windows syscalls
    register_common_syscalls();
    
    // Load additional syscalls from the syscall table
    syscall_table::load_syscalls_from_table();
}

/// Register common Windows syscalls
fn register_common_syscalls() {
    use crate::syscalls::common::register_syscall;
    
    // File System syscalls
    register_syscall(
        Syscall::new(
            0x0001,
            "NtCreateFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Create or open a file or device object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0002,
            "NtOpenFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Open a file or device object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0003,
            "NtReadFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Read from a file or device"),
    );
    
    register_syscall(
        Syscall::new(
            0x0004,
            "NtWriteFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Write to a file or device"),
    );
    
    register_syscall(
        Syscall::new(
            0x0005,
            "NtDeleteFile",
            Platform::Windows,
            SyscallCategory::FileSystem,
        )
        .with_description("Delete a file"),
    );
    
    // Process syscalls
    register_syscall(
        Syscall::new(
            0x0006,
            "NtCreateProcess",
            Platform::Windows,
            SyscallCategory::Process,
        )
        .with_description("Create a process object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0007,
            "NtCreateProcessEx",
            Platform::Windows,
            SyscallCategory::Process,
        )
        .with_description("Create a process object with extended parameters"),
    );
    
    register_syscall(
        Syscall::new(
            0x0008,
            "NtOpenProcess",
            Platform::Windows,
            SyscallCategory::Process,
        )
        .with_description("Open a process object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0009,
            "NtTerminateProcess",
            Platform::Windows,
            SyscallCategory::Process,
        )
        .with_description("Terminate a process"),
    );
    
    // Memory syscalls
    register_syscall(
        Syscall::new(
            0x000A,
            "NtAllocateVirtualMemory",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Allocate virtual memory"),
    );
    
    register_syscall(
        Syscall::new(
            0x000B,
            "NtFreeVirtualMemory",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Free virtual memory"),
    );
    
    register_syscall(
        Syscall::new(
            0x000C,
            "NtProtectVirtualMemory",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Change protection of virtual memory"),
    );
    
    register_syscall(
        Syscall::new(
            0x000D,
            "NtReadVirtualMemory",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Read from virtual memory"),
    );
    
    register_syscall(
        Syscall::new(
            0x000E,
            "NtWriteVirtualMemory",
            Platform::Windows,
            SyscallCategory::Memory,
        )
        .with_description("Write to virtual memory"),
    );
    
    // Thread syscalls
    register_syscall(
        Syscall::new(
            0x000F,
            "NtCreateThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Create a thread object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0010,
            "NtCreateThreadEx",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Create a thread object with extended parameters"),
    );
    
    register_syscall(
        Syscall::new(
            0x0011,
            "NtOpenThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Open a thread object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0012,
            "NtTerminateThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Terminate a thread"),
    );
    
    register_syscall(
        Syscall::new(
            0x0013,
            "NtSuspendThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Suspend a thread"),
    );
    
    register_syscall(
        Syscall::new(
            0x0014,
            "NtResumeThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Resume a thread"),
    );
    
    // Registry syscalls
    register_syscall(
        Syscall::new(
            0x0015,
            "NtCreateKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Create a registry key"),
    );
    
    register_syscall(
        Syscall::new(
            0x0016,
            "NtOpenKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Open a registry key"),
    );
    
    register_syscall(
        Syscall::new(
            0x0017,
            "NtDeleteKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Delete a registry key"),
    );
    
    register_syscall(
        Syscall::new(
            0x0018,
            "NtQueryKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Query a registry key"),
    );
    
    register_syscall(
        Syscall::new(
            0x0019,
            "NtSetValueKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Set a registry value"),
    );
    
    register_syscall(
        Syscall::new(
            0x001A,
            "NtQueryValueKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Query a registry value"),
    );
    
    register_syscall(
        Syscall::new(
            0x001B,
            "NtDeleteValueKey",
            Platform::Windows,
            SyscallCategory::Registry,
        )
        .with_description("Delete a registry value"),
    );
    
    // Network syscalls
    register_syscall(
        Syscall::new(
            0x001C,
            "NtCreateNamedPipeFile",
            Platform::Windows,
            SyscallCategory::Network,
        )
        .with_description("Create a named pipe"),
    );
    
    register_syscall(
        Syscall::new(
            0x001D,
            "NtCreateMailslotFile",
            Platform::Windows,
            SyscallCategory::Network,
        )
        .with_description("Create a mailslot"),
    );
    
    // Security syscalls
    register_syscall(
        Syscall::new(
            0x001E,
            "NtAdjustPrivilegesToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Adjust token privileges"),
    );
    
    register_syscall(
        Syscall::new(
            0x001F,
            "NtAccessCheck",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Check access rights"),
    );
    
    register_syscall(
        Syscall::new(
            0x0020,
            "NtCreateToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Create a token object"),
    );
    
    register_syscall(
        Syscall::new(
            0x0021,
            "NtOpenProcessToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Open a process token"),
    );
    
    register_syscall(
        Syscall::new(
            0x0022,
            "NtOpenThreadToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Open a thread token"),
    );
    
    // Module syscalls
    register_syscall(
        Syscall::new(
            0x0023,
            "NtLoadDriver",
            Platform::Windows,
            SyscallCategory::Module,
        )
        .with_description("Load a driver"),
    );
    
    register_syscall(
        Syscall::new(
            0x0024,
            "NtUnloadDriver",
            Platform::Windows,
            SyscallCategory::Module,
        )
        .with_description("Unload a driver"),
    );
    
    // Device syscalls
    register_syscall(
        Syscall::new(
            0x0025,
            "NtDeviceIoControlFile",
            Platform::Windows,
            SyscallCategory::Device,
        )
        .with_description("Send a control code to a device"),
    );
    
    // Time syscalls
    register_syscall(
        Syscall::new(
            0x0026,
            "NtQuerySystemTime",
            Platform::Windows,
            SyscallCategory::Time,
        )
        .with_description("Query the system time"),
    );
    
    register_syscall(
        Syscall::new(
            0x0027,
            "NtSetSystemTime",
            Platform::Windows,
            SyscallCategory::Time,
        )
        .with_description("Set the system time"),
    );
    
    // System info syscalls
    register_syscall(
        Syscall::new(
            0x0028,
            "NtQuerySystemInformation",
            Platform::Windows,
            SyscallCategory::SystemInfo,
        )
        .with_description("Query system information"),
    );
    
    register_syscall(
        Syscall::new(
            0x0029,
            "NtSetSystemInformation",
            Platform::Windows,
            SyscallCategory::SystemInfo,
        )
        .with_description("Set system information"),
    );
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::syscalls::common::mapping::{get_syscall, get_syscalls_by_category, clear_registry};
    
    #[test]
    fn test_windows_syscall_registry() {
        // Clear the registry first
        clear_registry();
        
        // Initialize Windows syscalls
        init_windows_syscalls();
        
        // Check that some syscalls are registered
        let syscall = get_syscall(Platform::Windows, 0x0001); // NtCreateFile
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtCreateFile");
        
        let syscall = get_syscall(Platform::Windows, 0x0006); // NtCreateProcess
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtCreateProcess");
        
        let syscall = get_syscall(Platform::Windows, 0x000A); // NtAllocateVirtualMemory
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtAllocateVirtualMemory");
        
        // Check syscalls by category
        let fs_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::FileSystem);
        assert!(!fs_syscalls.is_empty());
        
        let proc_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Process);
        assert!(!proc_syscalls.is_empty());
        
        let mem_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Memory);
        assert!(!mem_syscalls.is_empty());
    }
    
    #[test]
    fn test_etw_availability() {
        // This test is just a placeholder since we can't actually test ETW availability
        // in a cross-platform way
        #[cfg(target_os = "windows")]
        {
            // On Windows, we should be able to check ETW availability
            let available = WindowsSyscallInterceptor::is_etw_available();
            println!("ETW available: {}", available);
        }
        
        #[cfg(not(target_os = "windows"))]
        {
            // On non-Windows platforms, ETW should not be available
            assert!(!WindowsSyscallInterceptor::is_etw_available());
        }
    }
} 