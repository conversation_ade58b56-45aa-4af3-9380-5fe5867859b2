//! File logger implementation
//!
//! This module provides a file logger for Inspector Gadget.

use crate::core::events::TraceEvent;
use crate::error::Result;
use std::fs::{File, OpenOptions};
use std::io::Write;
use std::path::PathBuf;

/// File logger configuration
#[derive(Debug, <PERSON>lone)]
pub struct FileConfig {
    /// Path to the log file
    pub path: PathBuf,
    /// Whether to append to the log file
    pub append: bool,
    /// Whether to include timestamps in log messages
    pub include_timestamps: bool,
    /// Whether to include process IDs in log messages
    pub include_pids: bool,
    /// Whether to include syscall numbers in log messages
    pub include_syscall_numbers: bool,
}

impl FileConfig {
    /// Create a new file logger configuration
    pub fn new<P: Into<PathBuf>>(path: P) -> Self {
        Self {
            path: path.into(),
            append: true,
            include_timestamps: true,
            include_pids: true,
            include_syscall_numbers: true,
        }
    }
}

/// File logger
#[derive(Debug)]
pub struct FileLogger {
    /// Configuration
    pub config: FileConfig,
    /// File handle
    file: Option<File>,
}

impl FileLogger {
    /// Create a new file logger
    pub fn new(config: FileConfig) -> Result<Self> {
        let file = OpenOptions::new()
            .write(true)
            .create(true)
            .append(config.append)
            .truncate(!config.append)
            .open(&config.path)?;
        
        Ok(Self {
            config,
            file: Some(file),
        })
    }
    
    /// Log an event
    pub fn log(&mut self, event: &TraceEvent) -> Result<()> {
        if let Some(file) = &mut self.file {
            let formatted = self.format_event(event);
            writeln!(file, "{}", formatted)?;
            file.flush()?;
        }
        
        Ok(())
    }
    
    /// Format an event for file output
    fn format_event(&self, event: &TraceEvent) -> String {
        let mut parts = Vec::new();
        
        if self.config.include_timestamps {
            parts.push(format!("[{}]", event.timestamp));
        }
        
        if self.config.include_pids {
            parts.push(format!("PID: {}", event.pid));
        }
        
        let syscall_info = if let Some(syscall_name) = &event.syscall_name {
            if self.config.include_syscall_numbers {
                format!("{}({})", syscall_name, event.syscall)
            } else {
                syscall_name.clone()
            }
        } else {
            format!("syscall_{}", event.syscall)
        };
        
        parts.push(syscall_info);
        
        if event.args.len() > 0 {
            let args = event.args
                .iter()
                .map(|arg| format!("{:#x}", arg))
                .collect::<Vec<_>>()
                .join(", ");
            parts.push(format!("args: [{}]", args));
        }
        
        parts.push(format!("ret: {}", event.ret));
        
        parts.join(" ")
    }
}
