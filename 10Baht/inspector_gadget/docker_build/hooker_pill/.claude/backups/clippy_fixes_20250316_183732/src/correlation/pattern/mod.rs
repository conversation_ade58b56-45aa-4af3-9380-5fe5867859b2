//! Pattern matching module for the correlation engine
//!
//! This module provides functionality for defining and matching patterns of events.
//! It includes a pattern definition language, a state machine for tracking patterns,
//! and support for variables and context.

use std::collections::{HashMap, HashSet};
use std::sync::{<PERSON>, <PERSON>tex, RwLock};


use crate::correlation::{CorrelationError, CorrelationResult, Event, EventValue};

/// Pattern definition for matching sequences of events
#[derive(Debug, Clone)]
pub struct Pattern {
    /// Unique identifier for the pattern
    pub id: String,
    /// Name of the pattern
    pub name: String,
    /// Description of the pattern
    pub description: String,
    /// Pattern expression in the pattern definition language
    pub expression: String,
    /// Compiled pattern for efficient matching
    pub compiled: Option<CompiledPattern>,
    /// Variables used in the pattern
    pub variables: HashMap<String, VariableDefinition>,
    /// Maximum duration for the pattern to match (None for no limit)
    pub max_duration: Option<Duration>,
    /// Whether to continue matching after a successful match
    pub continue_after_match: bool,
}

/// Compiled pattern for efficient matching
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct CompiledPattern {
    /// States in the pattern state machine
    pub states: Vec<PatternState>,
    /// Transitions between states
    pub transitions: Vec<PatternTransition>,
    /// Initial state index
    pub initial_state: usize,
    /// Final state indices
    pub final_states: HashSet<usize>,
}

/// State in the pattern state machine
#[derive(Debug, Clone)]
pub struct PatternState {
    /// Unique identifier for the state
    pub id: usize,
    /// Name of the state
    pub name: String,
    /// Whether this is a final (accepting) state
    pub is_final: bool,
    /// Actions to perform when entering this state
    pub entry_actions: Vec<PatternAction>,
    /// Actions to perform when exiting this state
    pub exit_actions: Vec<PatternAction>,
}

/// Transition between states in the pattern state machine
#[derive(Debug, Clone)]
pub struct PatternTransition {
    /// Source state index
    pub from_state: usize,
    /// Destination state index
    pub to_state: usize,
    /// Condition for the transition
    pub condition: PatternCondition,
    /// Actions to perform when taking the transition
    pub actions: Vec<PatternAction>,
}

/// Condition for a pattern transition
#[derive(Debug, Clone)]
pub enum PatternCondition {
    /// Match an event type
    EventType(String),
    /// Match an event source
    EventSource(String),
    /// Match an event field value
    EventField {
        /// Field name
        field: String,
        /// Operator for comparison
        operator: PatternOperator,
        /// Value to compare against
        value: PatternValue,
    },
    /// Logical AND of conditions
    And(Vec<PatternCondition>),
    /// Logical OR of conditions
    Or(Vec<PatternCondition>),
    /// Logical NOT of a condition
    Not(Box<PatternCondition>),
    /// Always match
    Always,
    /// Never match
    Never,
    /// Match after a timeout
    Timeout(Duration),
}

/// Operator for pattern conditions
#[derive(Debug, Clone, PartialEq)]
pub enum PatternOperator {
    /// Equal
    Equal,
    /// Not equal
    NotEqual,
    /// Greater than
    GreaterThan,
    /// Greater than or equal
    GreaterThanOrEqual,
    /// Less than
    LessThan,
    /// Less than or equal
    LessThanOrEqual,
    /// Contains (for strings and arrays)
    Contains,
    /// Starts with (for strings)
    StartsWith,
    /// Ends with (for strings)
    EndsWith,
    /// Matches a regular expression (for strings)
    Matches,
}

/// Value for pattern conditions
#[derive(Debug, Clone)]
pub enum PatternValue {
    /// Literal value
    Literal(EventValue),
    /// Variable reference
    Variable(String),
    /// Function call
    Function {
        /// Function name
        name: String,
        /// Function arguments
        args: Vec<PatternValue>,
    },
}

/// Action to perform during pattern matching
#[derive(Debug, Clone)]
pub enum PatternAction {
    /// Set a variable value
    SetVariable {
        /// Variable name
        name: String,
        /// Value to set
        value: PatternValue,
    },
    /// Clear a variable
    ClearVariable(String),
    /// Log a message
    Log {
        /// Log level
        level: LogLevel,
        /// Message to log
        message: String,
    },
    /// Emit an event
    EmitEvent {
        /// Event type
        event_type: String,
        /// Event data
        data: HashMap<String, PatternValue>,
    },
}

/// Log level for pattern actions
#[derive(Debug, Clone, PartialEq)]
pub enum LogLevel {
    /// Debug level
    Debug,
    /// Info level
    Info,
    /// Warning level
    Warning,
    /// Error level
    Error,
}

/// Definition of a variable used in a pattern
#[derive(Debug, Clone)]
pub struct VariableDefinition {
    /// Name of the variable
    pub name: String,
    /// Type of the variable
    pub var_type: VariableType,
    /// Initial value of the variable
    pub initial_value: Option<EventValue>,
    /// Whether the variable is persistent across pattern matches
    pub persistent: bool,
}

/// Type of a variable
#[derive(Debug, Clone, PartialEq)]
pub enum VariableType {
    /// String type
    String,
    /// Integer type
    Integer,
    /// Float type
    Float,
    /// Boolean type
    Boolean,
    /// Array type
    Array(Box<VariableType>),
    /// Map type
    Map {
        /// Key type
        key_type: Box<VariableType>,
        /// Value type
        value_type: Box<VariableType>,
    },
    /// Any type
    Any,
}

/// Context for pattern matching
#[derive(Debug, Clone)]
pub struct PatternContext {
    /// Variables in the context
    pub variables: HashMap<String, EventValue>,
    /// Events that have been matched
    pub matched_events: Vec<Event>,
    /// Start time of the pattern matching
    pub start_time: u64,
    /// Current time
    pub current_time: u64,
}

/// Pattern matcher for tracking patterns across events
pub struct PatternMatcher {
    /// Patterns to match
    patterns: Arc<RwLock<HashMap<String, Pattern>>>,
    /// Active pattern instances
    instances: Arc<Mutex<Vec<PatternInstance>>>,
    /// Maximum number of pattern instances to track
    max_instances: usize,
}

/// Instance of a pattern being matched
#[derive(Debug)]
struct PatternInstance {
    /// Pattern being matched
    pattern: Pattern,
    /// Current state in the pattern state machine
    current_state: usize,
    /// Context for the pattern matching
    context: PatternContext,
    /// Whether the pattern has matched
    matched: bool,
}

impl PatternMatcher {
    /// Create a new pattern matcher
    pub fn new(max_instances: usize) -> Self {
        Self {
            patterns: Arc::new(RwLock::new(HashMap::new())),
            instances: Arc::new(Mutex::new(Vec::new())),
            max_instances,
        }
    }

    /// Add a pattern to the matcher
    pub fn add_pattern(&self, pattern: Pattern) -> CorrelationResult<()> {
        // Validate the pattern
        if pattern.expression.is_empty() {
            return Err(CorrelationError::PatternError("Pattern expression cannot be empty".to_string()));
        }

        // Compile the pattern
        let compiled_pattern = self.compile_pattern(&pattern)?;

        // Create a new pattern with the compiled version
        let mut pattern = pattern.clone();
        pattern.compiled = Some(compiled_pattern);

        // Add the pattern to the collection
        let mut patterns = self.patterns.write().map_err(|_| {
            CorrelationError::PatternError("Failed to acquire write lock on patterns".to_string())
        })?;

        patterns.insert(pattern.id.clone(), pattern);

        Ok(())
    }

    /// Remove a pattern from the matcher
    pub fn remove_pattern(&self, pattern_id: &str) -> CorrelationResult<()> {
        let mut patterns = self.patterns.write().map_err(|_| {
            CorrelationError::PatternError("Failed to acquire write lock on patterns".to_string())
        })?;

        if patterns.remove(pattern_id).is_none() {
            return Err(CorrelationError::PatternError(format!("Pattern with ID {} not found", pattern_id)));
        }

        Ok(())
    }

    /// Process an event through the pattern matcher
    pub fn process_event(&self, event: &Event) -> CorrelationResult<Vec<PatternMatch>> {
        let mut matches = Vec::new();

        // Get a read lock on the patterns
        let patterns = self.patterns.read().map_err(|_| {
            CorrelationError::PatternError("Failed to acquire read lock on patterns".to_string())
        })?;

        // Get a lock on the instances
        let mut instances = self.instances.lock().map_err(|_| {
            CorrelationError::PatternError("Failed to acquire lock on instances".to_string())
        })?;

        // Process existing instances
        let mut i = 0;
        while i < instances.len() {
            let instance = &mut instances[i];
            
            // Update the context with the current time
            instance.context.current_time = event.timestamp;

            // Check if the pattern has expired
            if let Some(max_duration) = instance.pattern.max_duration {
                let elapsed = Duration::from_nanos(instance.context.current_time - instance.context.start_time);
                if elapsed > max_duration {
                    // Remove the expired instance
                    instances.swap_remove(i);
                    continue;
                }
            }

            // Check if the event matches any transitions from the current state
            let mut matched_transition = false;
            if let Some(compiled) = &instance.pattern.compiled {
                for transition in &compiled.transitions {
                    if transition.from_state == instance.current_state && self.evaluate_condition(&transition.condition, event, &instance.context)? {
                        // Execute the transition actions
                        for action in &transition.actions {
                            self.execute_action(action, event, &mut instance.context)?;
                        }

                        // Execute the exit actions of the current state
                        let current_state = &compiled.states[instance.current_state];
                        for action in &current_state.exit_actions {
                            self.execute_action(action, event, &mut instance.context)?;
                        }

                        // Update the current state
                        instance.current_state = transition.to_state;

                        // Execute the entry actions of the new state
                        let new_state = &compiled.states[instance.current_state];
                        for action in &new_state.entry_actions {
                            self.execute_action(action, event, &mut instance.context)?;
                        }

                        // Add the event to the matched events
                        instance.context.matched_events.push(event.clone());

                        // Check if the new state is a final state
                        if compiled.final_states.contains(&instance.current_state) {
                            // Create a pattern match
                            let pattern_match = PatternMatch {
                                pattern_id: instance.pattern.id.clone(),
                                pattern_name: instance.pattern.name.clone(),
                                matched_events: instance.context.matched_events.clone(),
                                variables: instance.context.variables.clone(),
                                timestamp: event.timestamp,
                            };

                            matches.push(pattern_match);

                            // Mark the instance as matched
                            instance.matched = true;

                            // If we should continue after a match, keep the instance
                            if !instance.pattern.continue_after_match {
                                // Remove the matched instance
                                instances.swap_remove(i);
                                continue;
                            }
                        }

                        matched_transition = true;
                        break;
                    }
                }
            }

            // If no transition matched, move to the next instance
            if !matched_transition {
                i += 1;
            }
        }

        // Create new instances for patterns that don't have an active instance
        for (_, pattern) in patterns.iter() {
            if let Some(compiled) = &pattern.compiled {
                // Check if the pattern already has an active instance
                let has_instance = instances.iter().any(|instance| instance.pattern.id == pattern.id);

                // If not, check if the event matches the initial state
                if !has_instance {
                    for transition in &compiled.transitions {
                        if transition.from_state == compiled.initial_state && self.evaluate_condition(&transition.condition, event, &PatternContext {
                            variables: HashMap::new(),
                            matched_events: Vec::new(),
                            start_time: event.timestamp,
                            current_time: event.timestamp,
                        })? {
                            // Create a new instance
                            let mut context = PatternContext {
                                variables: HashMap::new(),
                                matched_events: vec![event.clone()],
                                start_time: event.timestamp,
                                current_time: event.timestamp,
                            };

                            // Initialize variables
                            for (name, var_def) in &pattern.variables {
                                if let Some(initial_value) = &var_def.initial_value {
                                    context.variables.insert(name.clone(), initial_value.clone());
                                }
                            }

                            // Execute the transition actions
                            for action in &transition.actions {
                                self.execute_action(action, event, &mut context)?;
                            }

                            // Execute the entry actions of the new state
                            let new_state = &compiled.states[transition.to_state];
                            for action in &new_state.entry_actions {
                                self.execute_action(action, event, &mut context)?;
                            }

                            // Create a new instance
                            let instance = PatternInstance {
                                pattern: pattern.clone(),
                                current_state: transition.to_state,
                                context,
                                matched: false,
                            };

                            // Add the instance to the collection
                            if instances.len() < self.max_instances {
                                instances.push(instance);
                            } else {
                                // If we've reached the maximum number of instances, replace the oldest one
                                if let Some(oldest) = instances.iter().enumerate().min_by_key(|(_, instance)| instance.context.start_time) {
                                    instances[oldest.0] = instance;
                                }
                            }

                            // Check if the new state is a final state
                            if compiled.final_states.contains(&transition.to_state) {
                                // Create a pattern match
                                let pattern_match = PatternMatch {
                                    pattern_id: pattern.id.clone(),
                                    pattern_name: pattern.name.clone(),
                                    matched_events: vec![event.clone()],
                                    variables: context.variables.clone(),
                                    timestamp: event.timestamp,
                                };

                                matches.push(pattern_match);

                                // If we should not continue after a match, remove the instance
                                if !pattern.continue_after_match {
                                    instances.pop();
                                }
                            }

                            break;
                        }
                    }
                }
            }
        }

        Ok(matches)
    }

    /// Compile a pattern into a state machine
    fn compile_pattern(&self, pattern: &Pattern) -> CorrelationResult<CompiledPattern> {
        // TODO: Implement pattern compilation
        // This is a placeholder implementation that creates a simple state machine
        // with an initial state and a final state, and a transition between them
        // that matches the pattern expression as an event type.

        let initial_state = PatternState {
            id: 0,
            name: "Initial".to_string(),
            is_final: false,
            entry_actions: Vec::new(),
            exit_actions: Vec::new(),
        };

        let final_state = PatternState {
            id: 1,
            name: "Final".to_string(),
            is_final: true,
            entry_actions: Vec::new(),
            exit_actions: Vec::new(),
        };

        let transition = PatternTransition {
            from_state: 0,
            to_state: 1,
            condition: PatternCondition::EventType(pattern.expression.clone()),
            actions: Vec::new(),
        };

        let compiled = CompiledPattern {
            states: vec![initial_state, final_state],
            transitions: vec![transition],
            initial_state: 0,
            final_states: [1].iter().cloned().collect(),
        };

        Ok(compiled)
    }

    /// Evaluate a pattern condition against an event
    fn evaluate_condition(&self, condition: &PatternCondition, event: &Event, context: &PatternContext) -> CorrelationResult<bool> {
        match condition {
            PatternCondition::EventType(event_type) => Ok(event.event_type == *event_type),
            PatternCondition::EventSource(source) => Ok(event.source == *source),
            PatternCondition::EventField { field, operator, value } => {
                // Get the field value from the event
                let field_value = event.data.fields.get(field).ok_or_else(|| {
                    CorrelationError::PatternError(format!("Field {} not found in event", field))
                })?;

                // Evaluate the value
                let value = self.evaluate_value(value, event, context)?;

                // Compare the values
                self.compare_values(field_value, &value, operator)
            }
            PatternCondition::And(conditions) => {
                for condition in conditions {
                    if !self.evaluate_condition(condition, event, context)? {
                        return Ok(false);
                    }
                }
                Ok(true)
            }
            PatternCondition::Or(conditions) => {
                for condition in conditions {
                    if self.evaluate_condition(condition, event, context)? {
                        return Ok(true);
                    }
                }
                Ok(false)
            }
            PatternCondition::Not(condition) => {
                let result = self.evaluate_condition(condition, event, context)?;
                Ok(!result)
            }
            PatternCondition::Always => Ok(true),
            PatternCondition::Never => Ok(false),
            PatternCondition::Timeout(duration) => {
                let elapsed = Duration::from_nanos(context.current_time - context.start_time);
                Ok(elapsed >= *duration)
            }
        }
    }

    /// Evaluate a pattern value
    fn evaluate_value(&self, value: &PatternValue, event: &Event, context: &PatternContext) -> CorrelationResult<EventValue> {
        match value {
            PatternValue::Literal(value) => Ok(value.clone()),
            PatternValue::Variable(name) => {
                context.variables.get(name).cloned().ok_or_else(|| {
                    CorrelationError::PatternError(format!("Variable {} not found in context", name))
                })
            }
            PatternValue::Function { name, args } => {
                // Evaluate the arguments
                let mut evaluated_args = Vec::new();
                for arg in args {
                    evaluated_args.push(self.evaluate_value(arg, event, context)?);
                }

                // Call the function
                match name.as_str() {
                    "concat" => {
                        // Concatenate strings
                        let mut result = String::new();
                        for arg in evaluated_args {
                            match arg {
                                EventValue::String(s) => result.push_str(&s),
                                _ => return Err(CorrelationError::PatternError(format!("concat function expects string arguments, got {:?}", arg))),
                            }
                        }
                        Ok(EventValue::String(result))
                    }
                    "length" => {
                        // Get the length of a string or array
                        if evaluated_args.len() != 1 {
                            return Err(CorrelationError::PatternError(format!("length function expects 1 argument, got {}", evaluated_args.len())));
                        }
                        match &evaluated_args[0] {
                            EventValue::String(s) => Ok(EventValue::Integer(s.len() as i64)),
                            EventValue::Array(a) => Ok(EventValue::Integer(a.len() as i64)),
                            _ => Err(CorrelationError::PatternError(format!("length function expects string or array argument, got {:?}", evaluated_args[0]))),
                        }
                    }
                    "now" => {
                        // Get the current time
                        Ok(EventValue::Integer(context.current_time as i64))
                    }
                    _ => Err(CorrelationError::PatternError(format!("Unknown function: {}", name))),
                }
            }
        }
    }

    /// Compare two values using an operator
    fn compare_values(&self, left: &EventValue, right: &EventValue, operator: &PatternOperator) -> CorrelationResult<bool> {
        match operator {
            PatternOperator::Equal => Ok(self.values_equal(left, right)),
            PatternOperator::NotEqual => Ok(!self.values_equal(left, right)),
            PatternOperator::GreaterThan => self.compare_ordered_values(left, right, |a, b| a > b),
            PatternOperator::GreaterThanOrEqual => self.compare_ordered_values(left, right, |a, b| a >= b),
            PatternOperator::LessThan => self.compare_ordered_values(left, right, |a, b| a < b),
            PatternOperator::LessThanOrEqual => self.compare_ordered_values(left, right, |a, b| a <= b),
            PatternOperator::Contains => {
                match (left, right) {
                    (EventValue::String(l), EventValue::String(r)) => Ok(l.contains(r)),
                    (EventValue::Array(l), r) => Ok(l.iter().any(|item| self.values_equal(item, r))),
                    _ => Err(CorrelationError::PatternError(format!("Contains operator not supported for {:?} and {:?}", left, right))),
                }
            }
            PatternOperator::StartsWith => {
                match (left, right) {
                    (EventValue::String(l), EventValue::String(r)) => Ok(l.starts_with(r)),
                    _ => Err(CorrelationError::PatternError(format!("StartsWith operator not supported for {:?} and {:?}", left, right))),
                }
            }
            PatternOperator::EndsWith => {
                match (left, right) {
                    (EventValue::String(l), EventValue::String(r)) => Ok(l.ends_with(r)),
                    _ => Err(CorrelationError::PatternError(format!("EndsWith operator not supported for {:?} and {:?}", left, right))),
                }
            }
            PatternOperator::Matches => {
                match (left, right) {
                    (EventValue::String(l), EventValue::String(r)) => {
                        // Use regex to match
                        let regex = regex::Regex::new(r).map_err(|e| {
                            CorrelationError::PatternError(format!("Invalid regex: {}", e))
                        })?;
                        Ok(regex.is_match(l))
                    }
                    _ => Err(CorrelationError::PatternError(format!("Matches operator not supported for {:?} and {:?}", left, right))),
                }
            }
        }
    }

    /// Check if two values are equal
    fn values_equal(&self, left: &EventValue, right: &EventValue) -> bool {
        match (left, right) {
            (EventValue::String(l), EventValue::String(r)) => l == r,
            (EventValue::Integer(l), EventValue::Integer(r)) => l == r,
            (EventValue::Float(l), EventValue::Float(r)) => l == r,
            (EventValue::Boolean(l), EventValue::Boolean(r)) => l == r,
            (EventValue::Array(l), EventValue::Array(r)) => {
                if l.len() != r.len() {
                    return false;
                }
                for (l_item, r_item) in l.iter().zip(r.iter()) {
                    if !self.values_equal(l_item, r_item) {
                        return false;
                    }
                }
                true
            }
            (EventValue::Object(l), EventValue::Object(r)) => {
                if l.len() != r.len() {
                    return false;
                }
                for (key, l_value) in l {
                    match r.get(key) {
                        Some(r_value) => {
                            if !self.values_equal(l_value, r_value) {
                                return false;
                            }
                        }
                        None => return false,
                    }
                }
                true
            }
            (EventValue::Null, EventValue::Null) => true,
            _ => false,
        }
    }

    /// Compare two values using a comparison function
    fn compare_ordered_values<F>(&self, left: &EventValue, right: &EventValue, compare: F) -> CorrelationResult<bool>
    where
        F: Fn(&dyn std::cmp::PartialOrd, &dyn std::cmp::PartialOrd) -> bool,
    {
        match (left, right) {
            (EventValue::Integer(l), EventValue::Integer(r)) => Ok(compare(l, r)),
            (EventValue::Float(l), EventValue::Float(r)) => Ok(compare(l, r)),
            (EventValue::String(l), EventValue::String(r)) => Ok(compare(l, r)),
            _ => Err(CorrelationError::PatternError(format!("Cannot compare {:?} and {:?}", left, right))),
        }
    }

    /// Execute a pattern action
    fn execute_action(&self, action: &PatternAction, event: &Event, context: &mut PatternContext) -> CorrelationResult<()> {
        match action {
            PatternAction::SetVariable { name, value } => {
                let value = self.evaluate_value(value, event, context)?;
                context.variables.insert(name.clone(), value);
                Ok(())
            }
            PatternAction::ClearVariable(name) => {
                context.variables.remove(name);
                Ok(())
            }
            PatternAction::Log { level, message } => {
                // TODO: Implement logging
                println!("[{}] {}", level, message);
                Ok(())
            }
            PatternAction::EmitEvent { event_type, data } => {
                // TODO: Implement event emission
                Ok(())
            }
        }
    }
}

/// Result of a pattern match
#[derive(Debug, Clone)]
pub struct PatternMatch {
    /// ID of the pattern that matched
    pub pattern_id: String,
    /// Name of the pattern that matched
    pub pattern_name: String,
    /// Events that contributed to the match
    pub matched_events: Vec<Event>,
    /// Variables at the time of the match
    pub variables: HashMap<String, EventValue>,
    /// Timestamp of the match
    pub timestamp: u64,
} 