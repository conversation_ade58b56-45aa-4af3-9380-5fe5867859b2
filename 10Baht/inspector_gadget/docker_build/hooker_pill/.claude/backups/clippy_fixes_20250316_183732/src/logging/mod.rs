//! Logging module for Inspector Gadget
//! 
//! This module provides logging functionality for the Inspector Gadget framework.

pub mod console;
pub mod file;
pub mod elasticsearch;

pub use self::console::{ConsoleLogger, ConsoleConfig};
pub use self::file::{FileLogger, FileConfig};
pub use self::elasticsearch::{ElasticsearchConfig, ElasticsearchExporter};
pub use elasticsearch::ElasticsearchLogger;

pub mod prelude {
    //! Prelude module for logging
    pub use super::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileLogger, ElasticsearchLogger};
}
