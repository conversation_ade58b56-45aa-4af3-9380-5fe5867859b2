/*!
 * Report generation for binary analysis
 * 
 * This module provides functionality for generating reports from binary analysis.
 */


use std::fmt::Write;
use crate::core::{TraceEvent, EventType};
use crate::syscalls::{Syscall, SyscallCategory};
use super::patterns::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};

/// Report format
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Copy, PartialEq, Eq)]
pub enum ReportFormat {
    /// Plain text format
    Text,
    /// JSON format
    Json,
    /// HTML format
    Html,
    /// Markdown format
    Markdown,
}

/// Report generator for binary analysis
pub struct ReportGenerator {
    /// Events to include in the report
    events: Vec<TraceEvent>,
    /// Detected patterns
    patterns: Vec<BehaviorPattern>,
    /// Report format
    format: ReportFormat,
}

impl ReportGenerator {
    /// Create a new report generator
    pub fn new(format: ReportFormat) -> Self {
        ReportGenerator {
            events: Vec::new(),
            patterns: Vec::new(),
            format,
        }
    }
    
    /// Add events to the report
    pub fn add_events(&mut self, events: Vec<TraceEvent>) {
        self.events.extend(events);
    }
    
    /// Add a detected pattern to the report
    pub fn add_pattern(&mut self, pattern: BehaviorPattern) {
        self.patterns.push(pattern);
    }
    
    /// Set the report format
    pub fn set_format(&mut self, format: ReportFormat) {
        self.format = format;
    }
    
    /// Generate the report
    pub fn generate(&self) -> String {
        match self.format {
            ReportFormat::Text => self.generate_text(),
            ReportFormat::Json => self.generate_json(),
            ReportFormat::Html => self.generate_html(),
            ReportFormat::Markdown => self.generate_markdown(),
        }
    }
    
    /// Generate a text report
    fn generate_text(&self) -> String {
        let mut report = String::new();
        
        // Header
        writeln!(&mut report, "Inspector Gadget Binary Analysis Report").unwrap();
        writeln!(&mut report, "=====================================").unwrap();
        writeln!(&mut report).unwrap();
        
        // Summary
        writeln!(&mut report, "Summary").unwrap();
        writeln!(&mut report, "-------").unwrap();
        writeln!(&mut report, "Total events: {}", self.events.len()).unwrap();
        writeln!(&mut report, "Detected patterns: {}", self.patterns.len()).unwrap();
        writeln!(&mut report).unwrap();
        
        // Event type distribution
        let mut event_types = HashMap::new();
        for event in &self.events {
            *event_types.entry(format!("{}", event.event_type)).or_insert(0) += 1;
        }
        
        writeln!(&mut report, "Event Type Distribution").unwrap();
        writeln!(&mut report, "----------------------").unwrap();
        for (event_type, count) in event_types {
            writeln!(&mut report, "{}: {}", event_type, count).unwrap();
        }
        writeln!(&mut report).unwrap();
        
        // Syscall distribution
        let mut syscalls = HashMap::new();
        for event in &self.events {
            if let EventType::Syscall = event.event_type {
                if let crate::core::EventData::Syscall(syscall_event) = &event.data {
                    *syscalls.entry(syscall_event.syscall.name()).or_insert(0) += 1;
                }
            }
        }
        
        if !syscalls.is_empty() {
            writeln!(&mut report, "Top 10 Syscalls").unwrap();
            writeln!(&mut report, "-------------").unwrap();
            
            let mut syscalls: Vec<(String, i32)> = syscalls.into_iter().collect();
            syscalls.sort_by(|a, b| b.1.cmp(&a.1));
            
            for (i, (syscall, count)) in syscalls.iter().take(10).enumerate() {
                writeln!(&mut report, "{}. {} - {} calls", i + 1, syscall, count).unwrap();
            }
            writeln!(&mut report).unwrap();
        }
        
        // Detected patterns
        if !self.patterns.is_empty() {
            writeln!(&mut report, "Detected Patterns").unwrap();
            writeln!(&mut report, "----------------").unwrap();
            
            // Group patterns by severity
            let mut patterns_by_severity = HashMap::new();
            for pattern in &self.patterns {
                patterns_by_severity.entry(pattern.severity).or_insert_with(Vec::new).push(pattern);
            }
            
            // Sort severities in descending order
            let mut severities: Vec<Severity> = patterns_by_severity.keys().cloned().collect();
            severities.sort_by(|a, b| b.cmp(a));
            
            for severity in severities {
                if let Some(patterns) = patterns_by_severity.get(&severity) {
                    writeln!(&mut report, "{:?} Severity:", severity).unwrap();
                    for pattern in patterns {
                        writeln!(&mut report, "- {}: {}", pattern.name, pattern.description).unwrap();
                        if let Some(id) = &pattern.mitre_technique_id {
                            writeln!(&mut report, "  MITRE ATT&CK: {}", id).unwrap();
                        }
                    }
                    writeln!(&mut report).unwrap();
                }
            }
        }
        
        // Event timeline
        writeln!(&mut report, "Event Timeline").unwrap();
        writeln!(&mut report, "--------------").unwrap();
        
        for (i, event) in self.events.iter().enumerate().take(20) {
            writeln!(
                &mut report,
                "[{}] {} - PID {} - {}",
                event.formatted_timestamp(),
                event.event_type,
                event.pid,
                match &event.data {
                    crate::core::EventData::Syscall(syscall_event) => {
                        format!("Syscall: {}", syscall_event.syscall.name())
                    },
                    crate::core::EventData::FileOperation { path, operation, .. } => {
                        format!("File {}: {}", operation, path)
                    },
                    crate::core::EventData::NetworkActivity { destination, protocol, .. } => {
                        format!("Network {}: {}", protocol, destination)
                    },
                    crate::core::EventData::ProcessExecution { command, .. } => {
                        format!("Process: {}", command)
                    },
                    crate::core::EventData::MemoryOperation { operation, address, size } => {
                        format!("Memory {}: {} bytes at 0x{:x}", operation, size, address)
                    },
                    crate::core::EventData::Custom(_) => {
                        "Custom event".to_string()
                    },
                }
            ).unwrap();
        }
        
        if self.events.len() > 20 {
            writeln!(&mut report, "... and {} more events", self.events.len() - 20).unwrap();
        }
        
        report
    }
    
    /// Generate a JSON report
    fn generate_json(&self) -> String {
        let mut report = serde_json::Map::new();
        
        // Summary
        let mut summary = serde_json::Map::new();
        summary.insert("total_events".to_string(), serde_json::Value::Number(serde_json::Number::from(self.events.len())));
        summary.insert("detected_patterns".to_string(), serde_json::Value::Number(serde_json::Number::from(self.patterns.len())));
        
        // Event type distribution
        let mut event_types = HashMap::new();
        for event in &self.events {
            *event_types.entry(format!("{}", event.event_type)).or_insert(0) += 1;
        }
        
        let mut event_type_distribution = serde_json::Map::new();
        for (event_type, count) in event_types {
            event_type_distribution.insert(event_type, serde_json::Value::Number(serde_json::Number::from(count)));
        }
        
        summary.insert("event_type_distribution".to_string(), serde_json::Value::Object(event_type_distribution));
        report.insert("summary".to_string(), serde_json::Value::Object(summary));
        
        // Patterns
        let mut patterns_json = Vec::new();
        for pattern in &self.patterns {
            let mut pattern_json = serde_json::Map::new();
            pattern_json.insert("name".to_string(), serde_json::Value::String(pattern.name.clone()));
            pattern_json.insert("description".to_string(), serde_json::Value::String(pattern.description.clone()));
            pattern_json.insert("severity".to_string(), serde_json::Value::String(format!("{:?}", pattern.severity)));
            
            if let Some(id) = &pattern.mitre_technique_id {
                pattern_json.insert("mitre_technique_id".to_string(), serde_json::Value::String(id.clone()));
            }
            
            patterns_json.push(serde_json::Value::Object(pattern_json));
        }
        
        report.insert("patterns".to_string(), serde_json::Value::Array(patterns_json));
        
        // Events (limited to 100 for brevity)
        let mut events_json = Vec::new();
        for event in self.events.iter().take(100) {
            let mut event_json = serde_json::Map::new();
            event_json.insert("timestamp".to_string(), serde_json::Value::Number(serde_json::Number::from(event.timestamp)));
            event_json.insert("pid".to_string(), serde_json::Value::Number(serde_json::Number::from(event.pid)));
            event_json.insert("tid".to_string(), serde_json::Value::Number(serde_json::Number::from(event.tid)));
            event_json.insert("event_type".to_string(), serde_json::Value::String(format!("{}", event.event_type)));
            
            // Event data would be complex to serialize here, so we'll just include a placeholder
            event_json.insert("data".to_string(), serde_json::Value::String("...".to_string()));
            
            events_json.push(serde_json::Value::Object(event_json));
        }
        
        report.insert("events".to_string(), serde_json::Value::Array(events_json));
        
        serde_json::to_string_pretty(&serde_json::Value::Object(report)).unwrap_or_else(|_| "{}".to_string())
    }
    
    /// Generate an HTML report
    fn generate_html(&self) -> String {
        let mut report = String::new();
        
        // HTML header
        report.push_str("<!DOCTYPE html>\n");
        report.push_str("<html>\n");
        report.push_str("<head>\n");
        report.push_str("  <title>Inspector Gadget Binary Analysis Report</title>\n");
        report.push_str("  <style>\n");
        report.push_str("    body { font-family: Arial, sans-serif; margin: 20px; }\n");
        report.push_str("    h1, h2 { color: #333; }\n");
        report.push_str("    table { border-collapse: collapse; width: 100%; }\n");
        report.push_str("    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
        report.push_str("    th { background-color: #f2f2f2; }\n");
        report.push_str("    .critical { color: #d9534f; }\n");
        report.push_str("    .high { color: #f0ad4e; }\n");
        report.push_str("    .medium { color: #5bc0de; }\n");
        report.push_str("    .low { color: #5cb85c; }\n");
        report.push_str("  </style>\n");
        report.push_str("</head>\n");
        report.push_str("<body>\n");
        
        // Report header
        report.push_str("  <h1>Inspector Gadget Binary Analysis Report</h1>\n");
        
        // Summary
        report.push_str("  <h2>Summary</h2>\n");
        report.push_str("  <p>\n");
        report.push_str(&format!("    Total events: {}<br>\n", self.events.len()));
        report.push_str(&format!("    Detected patterns: {}<br>\n", self.patterns.len()));
        report.push_str("  </p>\n");
        
        // Event type distribution
        let mut event_types = HashMap::new();
        for event in &self.events {
            *event_types.entry(format!("{}", event.event_type)).or_insert(0) += 1;
        }
        
        report.push_str("  <h2>Event Type Distribution</h2>\n");
        report.push_str("  <table>\n");
        report.push_str("    <tr><th>Event Type</th><th>Count</th></tr>\n");
        for (event_type, count) in event_types {
            report.push_str(&format!("    <tr><td>{}</td><td>{}</td></tr>\n", event_type, count));
        }
        report.push_str("  </table>\n");
        
        // Detected patterns
        if !self.patterns.is_empty() {
            report.push_str("  <h2>Detected Patterns</h2>\n");
            
            // Group patterns by severity
            let mut patterns_by_severity = HashMap::new();
            for pattern in &self.patterns {
                patterns_by_severity.entry(pattern.severity).or_insert_with(Vec::new).push(pattern);
            }
            
            // Sort severities in descending order
            let mut severities: Vec<Severity> = patterns_by_severity.keys().cloned().collect();
            severities.sort_by(|a, b| b.cmp(a));
            
            report.push_str("  <table>\n");
            report.push_str("    <tr><th>Severity</th><th>Name</th><th>Description</th><th>MITRE ATT&CK</th></tr>\n");
            
            for severity in severities {
                if let Some(patterns) = patterns_by_severity.get(&severity) {
                    let severity_class = match severity {
                        Severity::Critical => "critical",
                        Severity::High => "high",
                        Severity::Medium => "medium",
                        Severity::Low => "low",
                        Severity::Info => "",
                    };
                    
                    for pattern in patterns {
                        report.push_str(&format!(
                            "    <tr><td class=\"{}\">{:?}</td><td>{}</td><td>{}</td><td>{}</td></tr>\n",
                            severity_class,
                            severity,
                            pattern.name,
                            pattern.description,
                            pattern.mitre_technique_id.as_deref().unwrap_or("-")
                        ));
                    }
                }
            }
            
            report.push_str("  </table>\n");
        }
        
        // Event timeline
        report.push_str("  <h2>Event Timeline</h2>\n");
        report.push_str("  <table>\n");
        report.push_str("    <tr><th>Timestamp</th><th>PID</th><th>Event Type</th><th>Details</th></tr>\n");
        
        for event in self.events.iter().take(50) {
            report.push_str(&format!(
                "    <tr><td>{}</td><td>{}</td><td>{}</td><td>{}</td></tr>\n",
                event.formatted_timestamp(),
                event.pid,
                event.event_type,
                match &event.data {
                    crate::core::EventData::Syscall(syscall_event) => {
                        format!("Syscall: {}", syscall_event.syscall.name())
                    },
                    crate::core::EventData::FileOperation { path, operation, .. } => {
                        format!("File {}: {}", operation, path)
                    },
                    crate::core::EventData::NetworkActivity { destination, protocol, .. } => {
                        format!("Network {}: {}", protocol, destination)
                    },
                    crate::core::EventData::ProcessExecution { command, .. } => {
                        format!("Process: {}", command)
                    },
                    crate::core::EventData::MemoryOperation { operation, address, size } => {
                        format!("Memory {}: {} bytes at 0x{:x}", operation, size, address)
                    },
                    crate::core::EventData::Custom(_) => {
                        "Custom event".to_string()
                    },
                }
            ));
        }
        
        report.push_str("  </table>\n");
        
        if self.events.len() > 50 {
            report.push_str(&format!("  <p>... and {} more events</p>\n", self.events.len() - 50));
        }
        
        // HTML footer
        report.push_str("</body>\n");
        report.push_str("</html>\n");
        
        report
    }
    
    /// Generate a Markdown report
    fn generate_markdown(&self) -> String {
        let mut report = String::new();
        
        // Header
        writeln!(&mut report, "# Inspector Gadget Binary Analysis Report").unwrap();
        writeln!(&mut report).unwrap();
        
        // Summary
        writeln!(&mut report, "## Summary").unwrap();
        writeln!(&mut report).unwrap();
        writeln!(&mut report, "- Total events: {}", self.events.len()).unwrap();
        writeln!(&mut report, "- Detected patterns: {}", self.patterns.len()).unwrap();
        writeln!(&mut report).unwrap();
        
        // Event type distribution
        let mut event_types = HashMap::new();
        for event in &self.events {
            *event_types.entry(format!("{}", event.event_type)).or_insert(0) += 1;
        }
        
        writeln!(&mut report, "## Event Type Distribution").unwrap();
        writeln!(&mut report).unwrap();
        writeln!(&mut report, "| Event Type | Count |").unwrap();
        writeln!(&mut report, "|------------|-------|").unwrap();
        for (event_type, count) in event_types {
            writeln!(&mut report, "| {} | {} |", event_type, count).unwrap();
        }
        writeln!(&mut report).unwrap();
        
        // Detected patterns
        if !self.patterns.is_empty() {
            writeln!(&mut report, "## Detected Patterns").unwrap();
            writeln!(&mut report).unwrap();
            
            // Group patterns by severity
            let mut patterns_by_severity = HashMap::new();
            for pattern in &self.patterns {
                patterns_by_severity.entry(pattern.severity).or_insert_with(Vec::new).push(pattern);
            }
            
            // Sort severities in descending order
            let mut severities: Vec<Severity> = patterns_by_severity.keys().cloned().collect();
            severities.sort_by(|a, b| b.cmp(a));
            
            writeln!(&mut report, "| Severity | Name | Description | MITRE ATT&CK |").unwrap();
            writeln!(&mut report, "|----------|------|-------------|-------------|").unwrap();
            
            for severity in severities {
                if let Some(patterns) = patterns_by_severity.get(&severity) {
                    for pattern in patterns {
                        writeln!(
                            &mut report,
                            "| **{:?}** | {} | {} | {} |",
                            severity,
                            pattern.name,
                            pattern.description,
                            pattern.mitre_technique_id.as_deref().unwrap_or("-")
                        ).unwrap();
                    }
                }
            }
            
            writeln!(&mut report).unwrap();
        }
        
        // Event timeline
        writeln!(&mut report, "## Event Timeline").unwrap();
        writeln!(&mut report).unwrap();
        writeln!(&mut report, "| Timestamp | PID | Event Type | Details |").unwrap();
        writeln!(&mut report, "|-----------|-----|------------|---------|").unwrap();
        
        for event in self.events.iter().take(30) {
            writeln!(
                &mut report,
                "| {} | {} | {} | {} |",
                event.formatted_timestamp(),
                event.pid,
                event.event_type,
                match &event.data {
                    crate::core::EventData::Syscall(syscall_event) => {
                        format!("Syscall: {}", syscall_event.syscall.name())
                    },
                    crate::core::EventData::FileOperation { path, operation, .. } => {
                        format!("File {}: {}", operation, path)
                    },
                    crate::core::EventData::NetworkActivity { destination, protocol, .. } => {
                        format!("Network {}: {}", protocol, destination)
                    },
                    crate::core::EventData::ProcessExecution { command, .. } => {
                        format!("Process: {}", command)
                    },
                    crate::core::EventData::MemoryOperation { operation, address, size } => {
                        format!("Memory {}: {} bytes at 0x{:x}", operation, size, address)
                    },
                    crate::core::EventData::Custom(_) => {
                        "Custom event".to_string()
                    },
                }
            ).unwrap();
        }
        
        if self.events.len() > 30 {
            writeln!(&mut report).unwrap();
            writeln!(&mut report, "*... and {} more events*", self.events.len() - 30).unwrap();
        }
        
        report
    }
} 