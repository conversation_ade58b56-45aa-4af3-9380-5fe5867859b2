/*!
 * PE Binary Format Implementation
 * 
 * This module provides functionality for parsing and analyzing PE (Portable Executable)
 * binaries, commonly used on Windows systems.
 */


use goblin::pe::{PE, header, section_table::SectionTable, options::ParseOptions};
use goblin::Object;


use crate::binary::{Binary, BinaryFormat, BinaryInfo, BinarySection, BinaryError, Result};

/// PE binary implementation
pub struct PeBinary<'a> {
    /// Path to the binary file
    path: Option<PathBuf>,
    /// Parsed PE data
    pe: PE<'a>,
    /// Memory-mapped binary data
    data: &'static [u8],
    /// Cached sections
    sections: HashMap<String, BinarySection>,
}

impl<'a> PeBinary<'a> {
    /// Parse a PE binary from the given data
    pub fn parse<P: AsRef<Path>>(path: P, data: &[u8]) -> Result<Self> {
        // Create a static reference to the data
        // This is safe because the data is memory-mapped and will live for the lifetime of the program
        let data = unsafe { std::mem::transmute(data) };
        
        // Parse the PE binary
        let pe = match Object::parse(data) {
            Ok(Object::PE(pe)) => pe,
            _ => return Err(BinaryError::ParseError("Not a valid PE file".to_string())),
        };
        
        // Create the PE binary
        let mut binary = PeBinary {
            path: Some(path.as_ref().to_path_buf()),
            pe,
            data,
            sections: HashMap::new(),
        };
        
        // Parse sections
        binary.parse_sections()?;
        
        Ok(binary)
    }
    
    /// Parse sections from the PE binary
    fn parse_sections(&mut self) -> Result<()> {
        for (idx, section) in self.pe.sections.iter().enumerate() {
            let name = section.name().unwrap_or_else(|_| format!("Section{}", idx));
            let binary_section = self.create_binary_section(&name, section, idx)?;
            self.sections.insert(name, binary_section);
        }
        
        Ok(())
    }
    
    /// Create a BinarySection from a PE section
    fn create_binary_section(&self, name: &str, section: &SectionTable, idx: usize) -> Result<BinarySection> {
        // Determine section flags
        let is_executable = section.characteristics & 0x20000000 != 0; // IMAGE_SCN_MEM_EXECUTE
        let is_writable = section.characteristics & 0x80000000 != 0;   // IMAGE_SCN_MEM_WRITE
        let is_code = section.characteristics & 0x00000020 != 0;       // IMAGE_SCN_CNT_CODE
        let is_data = section.characteristics & 0x00000040 != 0;       // IMAGE_SCN_CNT_INITIALIZED_DATA
        
        // Extract section data if available
        let data = if section.pointer_to_raw_data > 0 && section.size_of_raw_data > 0 {
            let start = section.pointer_to_raw_data as usize;
            let end = start + section.size_of_raw_data as usize;
            
            if end <= self.data.len() {
                Some(self.data[start..end].to_vec())
            } else {
                None
            }
        } else {
            None
        };
        
        Ok(BinarySection {
            name: name.to_string(),
            address: section.virtual_address as u64,
            size: section.virtual_size as u64,
            is_executable,
            is_writable,
            is_code,
            is_data,
            data,
        })
    }
    
    /// Get the architecture string from the PE header
    fn get_architecture(&self) -> String {
        match self.pe.header.coff_header.machine {
            header::COFF_MACHINE_X86 => "x86".to_string(),
            header::COFF_MACHINE_X86_64 => "x86_64".to_string(),
            header::COFF_MACHINE_ARM => "ARM".to_string(),
            header::COFF_MACHINE_ARM64 => "AArch64".to_string(),
            header::COFF_MACHINE_ARMNT => "ARM Thumb-2".to_string(),
            header::COFF_MACHINE_EBC => "EFI Byte Code".to_string(),
            header::COFF_MACHINE_IA64 => "IA-64".to_string(),
            header::COFF_MACHINE_MIPS16 => "MIPS16".to_string(),
            header::COFF_MACHINE_MIPSFPU => "MIPS with FPU".to_string(),
            header::COFF_MACHINE_MIPSFPU16 => "MIPS16 with FPU".to_string(),
            header::COFF_MACHINE_POWERPC => "PowerPC".to_string(),
            header::COFF_MACHINE_POWERPCFP => "PowerPC with FPU".to_string(),
            header::COFF_MACHINE_RISCV32 => "RISC-V 32-bit".to_string(),
            header::COFF_MACHINE_RISCV64 => "RISC-V 64-bit".to_string(),
            header::COFF_MACHINE_RISCV128 => "RISC-V 128-bit".to_string(),
            machine => format!("Unknown ({})", machine),
        }
    }
}

impl<'a> Binary for PeBinary<'a> {
    fn format(&self) -> BinaryFormat {
        BinaryFormat::Pe
    }
    
    fn info(&self) -> BinaryInfo {
        let is_64bit = self.pe.header.coff_header.machine == header::COFF_MACHINE_X86_64 ||
                       self.pe.header.coff_header.machine == header::COFF_MACHINE_ARM64 ||
                       self.pe.header.coff_header.machine == header::COFF_MACHINE_IA64;
        
        let is_executable = self.pe.header.coff_header.characteristics & 0x0002 != 0; // IMAGE_FILE_EXECUTABLE_IMAGE
        let is_library = self.pe.header.coff_header.characteristics & 0x2000 != 0;    // IMAGE_FILE_DLL
        
        BinaryInfo {
            format: BinaryFormat::Pe,
            architecture: self.get_architecture(),
            is_64bit,
            is_executable,
            is_library,
            entry_point: self.pe.entry as u64,
            path: self.path.as_ref().map(|p| p.to_string_lossy().to_string()),
        }
    }
    
    fn sections(&self) -> Vec<BinarySection> {
        self.sections.values().cloned().collect()
    }
    
    fn get_section(&self, name: &str) -> Option<BinarySection> {
        self.sections.get(name).cloned()
    }
    
    fn is_executable(&self) -> bool {
        self.pe.header.coff_header.characteristics & 0x0002 != 0 // IMAGE_FILE_EXECUTABLE_IMAGE
    }
    
    fn is_library(&self) -> bool {
        self.pe.header.coff_header.characteristics & 0x2000 != 0 // IMAGE_FILE_DLL
    }
    
    fn entry_point(&self) -> u64 {
        self.pe.entry as u64
    }
    
    fn imports(&self) -> Vec<String> {
        let mut imports = Vec::new();
        
        // Get imports from import descriptors
        for import in &self.pe.imports {
            // Add the DLL name
            imports.push(import.dll.to_string());
            
            // Add the imported functions
            for import_info in &import.imports {
                if let Some(name) = &import_info.name {
                    imports.push(format!("{}!{}", import.dll, name));
                } else {
                    imports.push(format!("{}!Ordinal({})", import.dll, import_info.ordinal));
                }
            }
        }
        
        imports
    }
    
    fn exports(&self) -> Vec<String> {
        let mut exports = Vec::new();
        
        // Get exports from export descriptors
        if let Some(export_info) = &self.pe.exports {
            for export in &export_info.exports {
                if let Some(name) = &export.name {
                    exports.push(name.to_string());
                }
            }
        }
        
        exports
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    
    
    #[test]
    #[cfg(target_os = "windows")]
    fn test_parse_pe_binary() {
        // This test will only run on Windows
        // Use a common Windows binary for testing
        let path = Path::new("C:\\Windows\\System32\\notepad.exe");
        if path.exists() {
            let file = File::open(path).unwrap();
            let mmap = unsafe { MmapOptions::new().map(&file).unwrap() };
            
            let pe_binary = PeBinary::parse(path, &mmap).unwrap();
            
            // Basic checks
            assert_eq!(pe_binary.format(), BinaryFormat::Pe);
            assert!(pe_binary.is_executable());
            assert!(!pe_binary.is_library());
            assert!(pe_binary.entry_point() > 0);
            
            // Check sections
            let sections = pe_binary.sections();
            assert!(!sections.is_empty());
            
            // Check for common sections
            assert!(pe_binary.get_section(".text").is_some() || 
                   pe_binary.get_section("CODE").is_some());
            
            // Check imports and exports
            let imports = pe_binary.imports();
            assert!(!imports.is_empty());
        }
    }
} 