/*!
 * Optimized LRU Hash Map Implementation for eBPF
 * 
 * This module provides an optimized LRU (Least Recently Used) hash map implementation 
 * for eBPF maps, with features like batched operations, memory-mapped access, and
 * performance monitoring.
 */

use std::marker::PhantomData;
use std::sync::atomic::{AtomicU64, Ordering};

use std::sync::Mutex;
// Use the centralized logging system instead of direct log crate
use crate::logging::{ElasticsearchLogger, ElasticsearchConfig};
use log::{debug, warn, info, error};

use serde::{Serialize, de::DeserializeOwned};

use super::{Map, MapConfig, MapType, MapError};
use super::hash::HashFunction;

/// LRU hash map statistics
#[derive(Debug)]
pub struct LruMapStats {
    /// Number of lookups
    pub lookups: AtomicU64,
    /// Number of successful lookups (hits)
    pub hits: AtomicU64,
    /// Number of failed lookups (misses)
    pub misses: AtomicU64,
    /// Number of updates
    pub updates: AtomicU64,
    /// Number of deletes
    pub deletes: AtomicU64,
    /// Number of errors
    pub errors: AtomicU64,
    /// Number of evictions
    pub evictions: AtomicU64,
}

impl LruMapStats {
    /// Create new LRU hash map statistics
    pub fn new() -> Self {
        Self {
            lookups: AtomicU64::new(0),
            hits: AtomicU64::new(0),
            misses: AtomicU64::new(0),
            updates: AtomicU64::new(0),
            deletes: AtomicU64::new(0),
            errors: AtomicU64::new(0),
            evictions: AtomicU64::new(0),
        }
    }
    
    /// Reset all statistics to zero
    pub fn reset(&self) {
        self.lookups.store(0, Ordering::Relaxed);
        self.hits.store(0, Ordering::Relaxed);
        self.misses.store(0, Ordering::Relaxed);
        self.updates.store(0, Ordering::Relaxed);
        self.deletes.store(0, Ordering::Relaxed);
        self.errors.store(0, Ordering::Relaxed);
        self.evictions.store(0, Ordering::Relaxed);
    }
    
    /// Get the hit rate (hits / lookups)
    pub fn hit_rate(&self) -> f64 {
        let lookups = self.lookups.load(Ordering::Relaxed);
        if lookups == 0 {
            return 0.0;
        }
        
        let hits = self.hits.load(Ordering::Relaxed);
        hits as f64 / lookups as f64
    }
    
    /// Get the eviction rate (evictions / updates)
    pub fn eviction_rate(&self) -> f64 {
        let updates = self.updates.load(Ordering::Relaxed);
        if updates == 0 {
            return 0.0;
        }
        
        let evictions = self.evictions.load(Ordering::Relaxed);
        evictions as f64 / updates as f64
    }
}

impl Default for LruMapStats {
    fn default() -> Self {
        Self::new()
    }
}

/// Optimized LRU hash map for eBPF
#[derive(Debug)]
pub struct OptimizedLruMap {
    /// Inner map
    inner: Map,
    /// Hash function to use
    hash_function: HashFunction,
    /// Statistics
    stats: Arc<LruMapStats>,
    /// Memory-mapped access enabled
    mmap_enabled: bool,
    /// Maximum number of entries
    max_entries: usize,
}

impl OptimizedLruMap {
    /// Create a new optimized LRU hash map
    pub fn create(
        key_size: usize,
        value_size: usize,
        max_entries: usize,
        hash_function: Option<HashFunction>,
    ) -> Result<Self, MapError> {
        // Choose the best hash function for the key size if not specified
        let hash_function = hash_function.unwrap_or_else(|| HashFunction::recommended_for_size(key_size));
        
        // Create the map configuration
        let config = MapConfig::new(MapType::LruHash, key_size, value_size, max_entries, 0)?;
        
        // Create the map
        let inner = Map::create(config)?;
        
        // Log the creation of the LRU hash map using the centralized logging system
        info!("Created new LRU hash map: key_size={}, value_size={}, max_entries={}, hash_function={:?}", 
            key_size, value_size, max_entries, hash_function);
        
        Ok(Self {
            inner,
            hash_function,
            stats: Arc::new(LruMapStats::new()),
            mmap_enabled: false,
            max_entries,
        })
    }
    
    /// Open an existing optimized LRU hash map
    pub fn open(path: &str, hash_function: Option<HashFunction>) -> Result<Self, MapError> {
        // Open the map
        let inner = Map::open(path)?;
        
        // Get the key size and max entries
        let key_size = inner.config().key_size;
        let max_entries = inner.config().max_entries;
        
        // Choose the best hash function for the key size if not specified
        let hash_function = hash_function.unwrap_or_else(|| HashFunction::recommended_for_size(key_size));
        
        // Log the opening of the LRU hash map using the centralized logging system
        info!("Opened existing LRU hash map from {}: key_size={}, max_entries={}, hash_function={:?}", 
            path, key_size, max_entries, hash_function);
        
        Ok(Self {
            inner,
            hash_function,
            stats: Arc::new(LruMapStats::new()),
            mmap_enabled: false,
            max_entries,
        })
    }
    
    /// Enable memory-mapped access (if supported)
    pub fn enable_mmap(&mut self) -> Result<(), MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the kernel supports memory-mapped BPF maps
            // This is a simplified check - in a real implementation, we would check kernel version
            // and capabilities more thoroughly
            if std::path::Path::new("/sys/fs/bpf").exists() {
                self.mmap_enabled = true;
                info!("Memory-mapped access enabled for LRU hash map: key_size={}, value_size={}, max_entries={}", 
                    self.inner.config().key_size, self.inner.config().value_size, self.max_entries);
                Ok(())
            } else {
                warn!("Memory-mapped access not supported by this kernel: /sys/fs/bpf not found");
                Err(MapError::OperationError("Memory-mapped access not supported".to_string()))
            }
        }
        
        #[cfg(not(feature = "linux"))]
        {
            warn!("Memory-mapped access not supported on this platform: non-Linux platform detected");
            Err(MapError::OperationError("Memory-mapped access not supported on this platform".to_string()))
        }
    }
    
    /// Lookup a value by key
    pub fn lookup(&self, key: &[u8]) -> Result<Vec<u8>, MapError> {
        // Update statistics
        self.stats.lookups.fetch_add(1, Ordering::Relaxed);
        
        // Perform the lookup
        match self.inner.lookup(key) {
            Ok(value) => {
                // Update hit statistics
                self.stats.hits.fetch_add(1, Ordering::Relaxed);
                Ok(value)
            }
            Err(e) => {
                // Update miss statistics
                self.stats.misses.fetch_add(1, Ordering::Relaxed);
                
                if let MapError::NotFound(_) = e {
                    // Not found is a normal condition, don't count as error
                    Err(e)
                } else {
                    // Update error statistics
                    self.stats.errors.fetch_add(1, Ordering::Relaxed);
                    Err(e)
                }
            }
        }
    }
    
    /// Update a key-value pair
    pub fn update(&self, key: &[u8], value: &[u8], flags: u64) -> Result<(), MapError> {
        // Update statistics
        self.stats.updates.fetch_add(1, Ordering::Relaxed);
        
        // Check if the map is full before updating
        // This is a heuristic since we can't directly check if an eviction will occur
        let is_new_key = self.lookup(key).is_err();
        let current_entries = self.get_current_entries();
        
        if is_new_key && current_entries >= self.max_entries {
            // The map is full and we're adding a new key, so an eviction will occur
            self.stats.evictions.fetch_add(1, Ordering::Relaxed);
            debug!("LRU map is full, evicting least recently used entry");
        }
        
        // Perform the update
        match self.inner.update(key, value, flags) {
            Ok(()) => Ok(()),
            Err(e) => {
                // Update error statistics
                self.stats.errors.fetch_add(1, Ordering::Relaxed);
                
                // Log the error using the centralized logging system
                error!("Failed to update LRU map: key_len={}, value_len={}, error={}", 
                    key.len(), value.len(), e);
                
                Err(e)
            }
        }
    }
    
    /// Delete a key-value pair from the map
    pub fn delete(&self, key: &[u8]) -> Result<(), MapError> {
        // Update statistics
        self.stats.deletes.fetch_add(1, Ordering::Relaxed);
        
        // Perform the delete operation
        match self.inner.delete(key) {
            Ok(()) => {
                debug!("Successfully deleted key from LRU map: key_len={}", key.len());
                Ok(())
            },
            Err(e) => {
                // Update error statistics
                self.stats.errors.fetch_add(1, Ordering::Relaxed);
                
                // Log the error using the centralized logging system
                error!("Failed to delete key from LRU map: key_len={}, error={}", 
                    key.len(), e);
                
                Err(e)
            }
        }
    }
    
    /// Batch update multiple key-value pairs
    pub fn batch_update(&self, entries: &[(&[u8], &[u8])], flags: u64) -> Result<(), MapError> {
        // Update statistics
        self.stats.updates.fetch_add(entries.len() as u64, Ordering::Relaxed);
        
        // Perform batch update
        let mut errors = 0;
        
        for (key, value) in entries {
            // Check if the map is full before updating
            let is_new_key = self.lookup(key).is_err();
            let current_entries = self.get_current_entries();
            
            if is_new_key && current_entries >= self.max_entries {
                // The map is full and we're adding a new key, so an eviction will occur
                self.stats.evictions.fetch_add(1, Ordering::Relaxed);
                debug!("LRU map is full, evicting least recently used entry");
            }
            
            if let Err(e) = self.inner.update(key, value, flags) {
                // Update error statistics
                errors += 1;
                
                // Log the error using the centralized logging system
                error!("Failed to update entry in batch: key_len={}, value_len={}, error={}", 
                    key.len(), value.len(), e);
                
                warn!("Error in batch update: {}", e);
            }
        }
        
        if errors > 0 {
            self.stats.errors.fetch_add(errors, Ordering::Relaxed);
            Err(MapError::OperationError(format!("{} errors in batch update", errors)))
        } else {
            Ok(())
        }
    }
    
    /// Batch lookup multiple keys
    pub fn batch_lookup(&self, keys: &[&[u8]]) -> Vec<Result<Vec<u8>, MapError>> {
        // Update statistics
        self.stats.lookups.fetch_add(keys.len() as u64, Ordering::Relaxed);
        
        // Start timing for performance tracking
        let start_time = std::time::Instant::now();
        
        // Perform batch lookup
        let mut results = Vec::with_capacity(keys.len());
        let mut hits = 0;
        let mut misses = 0;
        let mut errors = 0;
        
        for key in keys {
            match self.lookup(key) {
                Ok(value) => {
                    hits += 1;
                    results.push(Ok(value));
                },
                Err(e) => {
                    if let MapError::NotFound(_) = e {
                        misses += 1;
                    } else {
                        errors += 1;
                    }
                    results.push(Err(e));
                }
            }
        }
        
        // Update statistics (lookup already updates hits and misses individually,
        // but we need to update errors here)
        self.stats.errors.fetch_add(errors, Ordering::Relaxed);
        
        // Calculate elapsed time
        let elapsed = start_time.elapsed();
        
        // Log performance metrics
        debug!("Batch lookup completed: keys={}, hits={}, misses={}, errors={}, time={:?}", 
            keys.len(), hits, misses, errors, elapsed);
        
        results
    }
    
    /// Batch delete multiple keys
    pub fn batch_delete(&self, keys: &[&[u8]]) -> Result<(), MapError> {
        // Update statistics
        self.stats.deletes.fetch_add(keys.len() as u64, Ordering::Relaxed);
        
        // Perform batch delete
        let mut errors = 0;
        
        for key in keys {
            if let Err(e) = self.inner.delete(key) {
                // Update error statistics
                errors += 1;
                warn!("Error in batch delete: {}", e);
            }
        }
        
        if errors > 0 {
            self.stats.errors.fetch_add(errors, Ordering::Relaxed);
            Err(MapError::OperationError(format!("{} errors in batch delete", errors)))
        } else {
            Ok(())
        }
    }
    
    /// Get the current number of entries in the map
    fn get_current_entries(&self) -> usize {
        // This is a heuristic since we can't directly get the current number of entries
        // In a real implementation, we would use the map info API
        let mut count = 0;
        let mut next_key = None;
        
        loop {
            match self.inner.get_next_key(next_key.as_deref()) {
                Ok(Some(key)) => {
                    count += 1;
                    next_key = Some(key);
                }
                Ok(None) => break,
                Err(_) => break,
            }
        }
        
        count
    }
    
    /// Get the inner map
    pub fn inner(&self) -> &Map {
        &self.inner
    }
    
    /// Get the hash function
    pub fn hash_function(&self) -> HashFunction {
        self.hash_function
    }
    
    /// Get the statistics
    pub fn stats(&self) -> &LruMapStats {
        &self.stats
    }
    
    /// Reset the statistics
    pub fn reset_stats(&self) {
        self.stats.reset();
    }
    
    /// Pin the map to the BPF filesystem
    pub fn pin(&self, path: &str) -> Result<(), MapError> {
        info!("Pinning LRU map to BPF filesystem: path={}", path);
        match self.inner.pin(path) {
            Ok(()) => {
                debug!("Successfully pinned LRU map to: {}", path);
                Ok(())
            },
            Err(e) => {
                error!("Failed to pin LRU map to {}: {}", path, e);
                Err(e)
            }
        }
    }
    
    /// Unpin the map from the BPF filesystem
    pub fn unpin(&self) -> Result<(), MapError> {
        info!("Unpinning LRU map from BPF filesystem");
        match self.inner.unpin() {
            Ok(()) => {
                debug!("Successfully unpinned LRU map");
                Ok(())
            },
            Err(e) => {
                error!("Failed to unpin LRU map: {}", e);
                Err(e)
            }
        }
    }
    
    /// Get the maximum number of entries
    pub fn max_entries(&self) -> usize {
        self.max_entries
    }
}

/// Type-safe optimized LRU hash map
#[derive(Debug)]
pub struct TypedOptimizedLruMap<K, V> {
    /// Inner optimized LRU hash map
    inner: OptimizedLruMap,
    /// Key type
    _key_type: PhantomData<K>,
    /// Value type
    _value_type: PhantomData<V>,
}

impl<K, V> TypedOptimizedLruMap<K, V>
where
    K: serde::Serialize + serde::de::DeserializeOwned + Default,
    V: serde::Serialize + serde::de::DeserializeOwned + Default,
{
    /// Create a new typed optimized LRU hash map
    pub fn create(
        max_entries: usize,
        hash_function: Option<HashFunction>,
    ) -> Result<Self, MapError>
    where
        K: Serialize + DeserializeOwned + Default,
        V: Serialize + DeserializeOwned + Default,
    {
        // Estimate key and value sizes using bincode serialization
        let key_size = bincode::serialized_size(&K::default())
            .map_err(|e| MapError::SerializationError(format!("Failed to estimate key size: {}", e)))?
            as usize;
            
        let value_size = bincode::serialized_size(&V::default())
            .map_err(|e| MapError::SerializationError(format!("Failed to estimate value size: {}", e)))?
            as usize;
        
        // Log the creation of the typed LRU hash map
        info!("Creating typed LRU hash map: key_type={}, value_type={}, key_size={}, value_size={}, max_entries={}, hash_function={:?}", 
            std::any::type_name::<K>(), std::any::type_name::<V>(), key_size, value_size, max_entries, hash_function);
        
        // Create the optimized LRU hash map
        let inner = OptimizedLruMap::create(key_size, value_size, max_entries, hash_function)?;
        
        // Create the typed optimized LRU hash map
        Ok(Self {
            inner,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
    
    /// Open an existing typed optimized LRU hash map
    pub fn open(path: &str, hash_function: Option<HashFunction>) -> Result<Self, MapError>
    where
        K: Serialize + DeserializeOwned,
        V: Serialize + DeserializeOwned,
    {
        // Log the opening of the typed LRU hash map
        info!("Opening typed LRU hash map from {}: key_type={}, value_type={}, hash_function={:?}", 
            path, std::any::type_name::<K>(), std::any::type_name::<V>(), hash_function);
        
        // Open the inner map
        let inner = OptimizedLruMap::open(path, hash_function)?;
        
        // Create the typed optimized LRU hash map
        Ok(Self {
            inner,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
    
    /// Enable memory-mapped access (if supported)
    pub fn enable_mmap(&mut self) -> Result<(), MapError> {
        self.inner.enable_mmap()
    }
    
    /// Lookup a value by key
    pub fn lookup(&self, key: &K) -> Result<V, MapError> {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
        
        // Lookup the value
        let value_bytes = self.inner.lookup(&key_bytes)?;
        
        // Deserialize the value
        bincode::deserialize(&value_bytes)
            .map_err(|e| MapError::DeserializationError(format!("Failed to deserialize value: {}", e)))
    }
    
    /// Update a key-value pair in the map
    pub fn update(&self, key: &K, value: &V, flags: u64) -> Result<(), MapError>
    where
        K: Serialize,
        V: Serialize,
    {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| {
                error!("Failed to serialize key for update: type={}, error={}", 
                    std::any::type_name::<K>(), e);
                MapError::SerializationError(format!("Failed to serialize key: {}", e))
            })?;
        
        // Serialize the value
        let value_bytes = bincode::serialize(value)
            .map_err(|e| {
                error!("Failed to serialize value for update: type={}, error={}", 
                    std::any::type_name::<V>(), e);
                MapError::SerializationError(format!("Failed to serialize value: {}", e))
            })?;
        
        // Update the map
        match self.inner.update(&key_bytes, &value_bytes, flags) {
            Ok(()) => {
                debug!("Successfully updated typed LRU map entry: key_type={}, value_type={}", 
                    std::any::type_name::<K>(), std::any::type_name::<V>());
                Ok(())
            },
            Err(e) => {
                error!("Failed to update typed LRU map entry: key_type={}, value_type={}, error={}", 
                    std::any::type_name::<K>(), std::any::type_name::<V>(), e);
                Err(e)
            }
        }
    }
    
    /// Delete a key-value pair
    pub fn delete(&self, key: &K) -> Result<(), MapError> {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
        
        // Delete the key-value pair
        self.inner.delete(&key_bytes)
    }
    
    /// Batch update multiple key-value pairs
    pub fn batch_update(&self, entries: &[(K, V)], flags: u64) -> Result<(), MapError> {
        // Serialize all entries
        let mut serialized_entries = Vec::with_capacity(entries.len());
        
        for (key, value) in entries {
            // Serialize the key
            let key_bytes = bincode::serialize(&key)
                .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
            
            // Serialize the value
            let value_bytes = bincode::serialize(&value)
                .map_err(|e| MapError::SerializationError(format!("Failed to serialize value: {}", e)))?;
            
            serialized_entries.push((key_bytes, value_bytes));
        }
        
        // Convert to slice references
        let entry_refs: Vec<(&[u8], &[u8])> = serialized_entries.iter()
            .map(|(k, v)| (k.as_slice(), v.as_slice()))
            .collect();
        
        // Update the map
        self.inner.batch_update(&entry_refs, flags)
    }
    
    /// Batch lookup multiple keys
    pub fn batch_lookup(&self, keys: &[K]) -> Vec<Result<V, MapError>> {
        // Serialize all keys
        let mut serialized_keys = Vec::with_capacity(keys.len());
        let mut serialization_errors = Vec::new();
        
        for (i, key) in keys.iter().enumerate() {
            match bincode::serialize(key) {
                Ok(key_bytes) => serialized_keys.push(key_bytes),
                Err(e) => {
                    serialization_errors.push((i, MapError::SerializationError(format!("Failed to serialize key: {}", e))));
                }
            }
        }
        
        // Convert to slice references
        let key_refs: Vec<&[u8]> = serialized_keys.iter()
            .map(|k| k.as_slice())
            .collect();
        
        // Lookup the values
        let raw_results = self.inner.batch_lookup(&key_refs);
        
        // Process the results
        let mut results = Vec::with_capacity(keys.len());
        let mut result_index = 0;
        
        for i in 0..keys.len() {
            // Check if this key had a serialization error
            if let Some((_, error)) = serialization_errors.iter().find(|(idx, _)| *idx == i) {
                results.push(Err(error.clone()));
            } else {
                // Process the lookup result
                match &raw_results[result_index] {
                    Ok(value_bytes) => {
                        match bincode::deserialize::<V>(value_bytes) {
                            Ok(value) => results.push(Ok(value)),
                            Err(e) => results.push(Err(MapError::DeserializationError(format!("Failed to deserialize value: {}", e)))),
                        }
                    }
                    Err(e) => results.push(Err(e.clone())),
                }
                result_index += 1;
            }
        }
        
        results
    }
    
    /// Batch delete multiple keys
    pub fn batch_delete(&self, keys: &[K]) -> Result<(), MapError> {
        // Serialize all keys
        let mut serialized_keys = Vec::with_capacity(keys.len());
        
        for key in keys {
            // Serialize the key
            let key_bytes = bincode::serialize(key)
                .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
            
            serialized_keys.push(key_bytes);
        }
        
        // Convert to slice references
        let key_refs: Vec<&[u8]> = serialized_keys.iter()
            .map(|k| k.as_slice())
            .collect();
        
        // Delete the keys
        self.inner.batch_delete(&key_refs)
    }
    
    /// Get the inner optimized LRU hash map
    pub fn inner(&self) -> &OptimizedLruMap {
        &self.inner
    }
    
    /// Get the hash function
    pub fn hash_function(&self) -> HashFunction {
        self.inner.hash_function()
    }
    
    /// Get the statistics
    pub fn stats(&self) -> &LruMapStats {
        self.inner.stats()
    }
    
    /// Reset the statistics
    pub fn reset_stats(&self) {
        self.inner.reset_stats();
    }
    
    /// Pin the map to the BPF filesystem
    pub fn pin(&self, path: &str) -> Result<(), MapError> {
        self.inner.pin(path)
    }
    
    /// Unpin the map from the BPF filesystem
    pub fn unpin(&self) -> Result<(), MapError> {
        self.inner.unpin()
    }
    
    /// Get the maximum number of entries
    pub fn max_entries(&self) -> usize {
        self.inner.max_entries()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    
    #[test]
    fn test_optimized_lru_map_basic() {
        // Create a new optimized LRU hash map
        let map = OptimizedLruMap::create(8, 8, 1024, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test update and lookup
        let key = [1, 2, 3, 4, 5, 6, 7, 8];
        let value = [10, 20, 30, 40, 50, 60, 70, 80];
        
        map.update(&key, &value, 0).unwrap();
        let result = map.lookup(&key).unwrap();
        
        assert_eq!(result, value);
        
        // Test delete
        map.delete(&key).unwrap();
        let result = map.lookup(&key);
        
        assert!(result.is_err());
        
        // Check statistics
        assert_eq!(map.stats().lookups.load(Ordering::Relaxed), 2);
        assert_eq!(map.stats().hits.load(Ordering::Relaxed), 1);
        assert_eq!(map.stats().misses.load(Ordering::Relaxed), 1);
        assert_eq!(map.stats().updates.load(Ordering::Relaxed), 1);
        assert_eq!(map.stats().deletes.load(Ordering::Relaxed), 1);
    }
    
    #[test]
    fn test_optimized_lru_map_eviction() {
        // Create a small LRU map to test eviction
        let map = OptimizedLruMap::create(8, 8, 3, Some(HashFunction::Fnv1a)).unwrap();
        
        // Insert 3 entries (fills the map)
        map.update(&[1, 0, 0, 0, 0, 0, 0, 0], &[1, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        map.update(&[2, 0, 0, 0, 0, 0, 0, 0], &[2, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        map.update(&[3, 0, 0, 0, 0, 0, 0, 0], &[3, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        
        // Access the entries in a specific order to affect LRU order
        map.lookup(&[2, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).unwrap();
        
        // Insert a new entry, which should evict the least recently used (key 2)
        map.update(&[4, 0, 0, 0, 0, 0, 0, 0], &[4, 0, 0, 0, 0, 0, 0, 0], 0).unwrap();
        
        // Check that key 2 was evicted
        assert!(map.lookup(&[2, 0, 0, 0, 0, 0, 0, 0]).is_err());
        
        // Check that the other keys are still there
        assert!(map.lookup(&[1, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[3, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        assert!(map.lookup(&[4, 0, 0, 0, 0, 0, 0, 0]).is_ok());
        
        // Check eviction statistics
        assert_eq!(map.stats().evictions.load(Ordering::Relaxed), 1);
    }
    
    #[test]
    fn test_typed_optimized_lru_map() {
        // Create a new typed optimized LRU hash map
        let map = TypedOptimizedLruMap::<String, u32>::create(1024, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test update and lookup
        map.update(&"test_key".to_string(), &42, 0).unwrap();
        let result = map.lookup(&"test_key".to_string()).unwrap();
        
        assert_eq!(result, 42);
        
        // Test delete
        map.delete(&"test_key".to_string()).unwrap();
        let result = map.lookup(&"test_key".to_string());
        
        assert!(result.is_err());
    }
    
    #[test]
    fn test_typed_optimized_lru_map_batch() {
        // Create a new typed optimized LRU hash map
        let map = TypedOptimizedLruMap::<String, u32>::create(1024, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test batch update
        let entries = vec![
            ("key1".to_string(), 1),
            ("key2".to_string(), 2),
            ("key3".to_string(), 3),
        ];
        
        map.batch_update(&entries, 0).unwrap();
        
        // Test batch lookup
        let keys = vec![
            "key1".to_string(),
            "key2".to_string(),
            "key3".to_string(),
        ];
        
        let results = map.batch_lookup(&keys);
        
        for (i, result) in results.iter().enumerate() {
            assert!(result.is_ok());
            assert_eq!(result.as_ref().unwrap(), &entries[i].1);
        }
        
        // Test batch delete
        map.batch_delete(&keys).unwrap();
        
        let results = map.batch_lookup(&keys);
        
        for result in results {
            assert!(result.is_err());
        }
    }
} 