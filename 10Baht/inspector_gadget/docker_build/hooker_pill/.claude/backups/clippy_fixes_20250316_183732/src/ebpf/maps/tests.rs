/*!
 * Tests for eBPF Maps Functionality
 * 
 * This module contains tests for eBPF maps functionality.
 */

// Import test modules
mod hashmap_tests;
mod lrumap_tests;

#[cfg(test)]
mod tests {
    use super::*;
    
    
    #[test]
    fn test_map_type_conversion() {
        // Test conversion to libbpf type
        assert_eq!(MapType::Hash.to_libbpf_type(), 1);
        assert_eq!(MapType::Array.to_libbpf_type(), 2);
        assert_eq!(MapType::PerCpuHash.to_libbpf_type(), 5);
        assert_eq!(MapType::PerCpuArray.to_libbpf_type(), 6);
        assert_eq!(MapType::LruHash.to_libbpf_type(), 9);
        assert_eq!(MapType::LruPerCpuHash.to_libbpf_type(), 10);
        assert_eq!(MapType::RingBuf.to_libbpf_type(), 27);
        assert_eq!(MapType::Stack.to_libbpf_type(), 7);
        assert_eq!(MapType::Queue.to_libbpf_type(), 22);
        
        // Test conversion from libbpf type
        assert_eq!(MapType::from_libbpf_type(1), Some(MapType::Hash));
        assert_eq!(MapType::from_libbpf_type(2), Some(MapType::Array));
        assert_eq!(MapType::from_libbpf_type(5), Some(MapType::PerCpuHash));
        assert_eq!(MapType::from_libbpf_type(6), Some(MapType::PerCpuArray));
        assert_eq!(MapType::from_libbpf_type(9), Some(MapType::LruHash));
        assert_eq!(MapType::from_libbpf_type(10), Some(MapType::LruPerCpuHash));
        assert_eq!(MapType::from_libbpf_type(27), Some(MapType::RingBuf));
        assert_eq!(MapType::from_libbpf_type(7), Some(MapType::Stack));
        assert_eq!(MapType::from_libbpf_type(22), Some(MapType::Queue));
        assert_eq!(MapType::from_libbpf_type(999), None);
    }
    
    #[test]
    fn test_map_type_properties() {
        // Test zero-length key support
        assert!(!MapType::Hash.supports_zero_length_key());
        assert!(!MapType::Array.supports_zero_length_key());
        assert!(MapType::RingBuf.supports_zero_length_key());
        assert!(MapType::Stack.supports_zero_length_key());
        assert!(MapType::Queue.supports_zero_length_key());
        
        // Test zero-length value support
        assert!(!MapType::Hash.supports_zero_length_value());
        assert!(!MapType::Array.supports_zero_length_value());
        assert!(MapType::RingBuf.supports_zero_length_value());
        
        // Test per-CPU property
        assert!(!MapType::Hash.is_per_cpu());
        assert!(!MapType::Array.is_per_cpu());
        assert!(MapType::PerCpuHash.is_per_cpu());
        assert!(MapType::PerCpuArray.is_per_cpu());
        assert!(MapType::LruPerCpuHash.is_per_cpu());
        
        // Test iteration support
        assert!(MapType::Hash.supports_iteration());
        assert!(MapType::Array.supports_iteration());
        assert!(!MapType::RingBuf.supports_iteration());
    }
    
    #[test]
    fn test_map_config_validation() {
        // Valid configurations
        assert!(MapConfig::new(MapType::Hash, 4, 8, 1024, 0).is_ok());
        assert!(MapConfig::new(MapType::Array, 4, 8, 1024, 0).is_ok());
        assert!(MapConfig::new(MapType::RingBuf, 0, 0, 4096, 0).is_ok());
        
        // Invalid key size
        assert!(MapConfig::new(MapType::Hash, 0, 8, 1024, 0).is_err());
        assert!(MapConfig::new(MapType::Array, 0, 8, 1024, 0).is_err());
        
        // Invalid value size
        assert!(MapConfig::new(MapType::Hash, 4, 0, 1024, 0).is_err());
        assert!(MapConfig::new(MapType::Array, 4, 0, 1024, 0).is_err());
        
        // Invalid max entries
        assert!(MapConfig::new(MapType::Hash, 4, 8, 0, 0).is_err());
        assert!(MapConfig::new(MapType::Array, 4, 8, 0, 0).is_err());
    }
    
    #[test]
    fn test_map_config_convenience_functions() {
        // Hash map
        let hash_config = MapConfig::new_hash(4, 8, 1024).unwrap();
        assert_eq!(hash_config.map_type, MapType::Hash);
        assert_eq!(hash_config.key_size, 4);
        assert_eq!(hash_config.value_size, 8);
        assert_eq!(hash_config.max_entries, 1024);
        
        // Array map
        let array_config = MapConfig::new_array(8, 1024).unwrap();
        assert_eq!(array_config.map_type, MapType::Array);
        assert_eq!(array_config.key_size, 4);
        assert_eq!(array_config.value_size, 8);
        assert_eq!(array_config.max_entries, 1024);
        
        // Ring buffer
        let ringbuf_config = MapConfig::new_ringbuf(4096).unwrap();
        assert_eq!(ringbuf_config.map_type, MapType::RingBuf);
        assert_eq!(ringbuf_config.key_size, 0);
        assert_eq!(ringbuf_config.value_size, 0);
        assert_eq!(ringbuf_config.max_entries, 4096);
    }
    
    // Note: The following tests would require actual eBPF map operations,
    // which we can't easily test in a unit test environment.
    // These would be better suited for integration tests.
    /*
    #[test]
    fn test_map_creation() {
        // Create a hash map
        let map = MapBuilder::hash()
            .name("test_map")
            .key_size(4)
            .value_size(8)
            .max_entries(1024)
            .build();
        
        assert!(map.is_ok());
    }
    
    #[test]
    fn test_map_operations() {
        // Create a hash map
        let map = MapBuilder::hash()
            .name("test_map")
            .key_size(4)
            .value_size(8)
            .max_entries(1024)
            .build()
            .unwrap();
        
        // Update a key-value pair
        let key = [1, 2, 3, 4];
        let value = [5, 6, 7, 8, 9, 10, 11, 12];
        assert!(map.update(&key, &value, 0).is_ok());
        
        // Lookup the value
        let result = map.lookup(&key);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), value);
        
        // Delete the key-value pair
        assert!(map.delete(&key).is_ok());
        
        // Lookup should now fail
        assert!(map.lookup(&key).is_err());
    }
    
    #[test]
    fn test_typed_map() {
        // Create a typed hash map
        let map: TypedMap<u32, String> = TypedMapBuilder::hash()
            .name("typed_test_map")
            .max_entries(1024)
            .build()
            .unwrap();
        
        // Update a key-value pair
        assert!(map.update(&42, &"hello".to_string(), 0).is_ok());
        
        // Lookup the value
        let result = map.lookup(&42);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "hello".to_string());
        
        // Delete the key-value pair
        assert!(map.delete(&42).is_ok());
        
        // Lookup should now fail
        assert!(map.lookup(&42).is_err());
    }
    */
} 