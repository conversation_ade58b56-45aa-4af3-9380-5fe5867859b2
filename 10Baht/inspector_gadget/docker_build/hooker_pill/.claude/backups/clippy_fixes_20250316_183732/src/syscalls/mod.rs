/*!
 * System Call Interception Framework
 * 
 * This module provides functionality for intercepting, analyzing, and reporting
 * system calls across different platforms. It enables monitoring of binary behavior
 * at runtime by capturing system calls and their parameters.
 */

// Submodules
pub mod common;
pub mod analysis;

// Platform-specific modules
#[cfg(target_os = "windows")]
pub mod windows;

#[cfg(target_os = "linux")]
pub mod linux;

// Re-exports
pub use self::common::{Syscall, SyscallEvent, SyscallFilter, SyscallCategory, SyscallParameter};


use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Serialize, Deserialize};
use crate::error::Result;
use crate::platforms::Platform;

/// Initialize the syscall interception framework for the current platform
pub fn init_syscall_interceptor(platform: Platform) -> Result<Box<dyn SyscallInterceptor>> {
    match platform {
        #[cfg(target_os = "windows")]
        Platform::Windows => {
            // Prefer ETW if available, fall back to eBPF
            if windows::etw::is_etw_available() {
                log::info!("Initializing Windows ETW syscall interceptor");
                Ok(Box::new(windows::etw::EtwSyscallInterceptor::new()?))
            } else if windows::ebpf::is_ebpf_available() {
                log::info!("Initializing Windows eBPF syscall interceptor");
                Ok(Box::new(windows::ebpf::WindowsEbpfSyscallInterceptor::new()?))
            } else {
                Err(crate::error::Error::Platform(
                    crate::error::PlatformError::Windows(
                        "No syscall interception mechanism available on Windows".to_string(),
                    ),
                ))
            }
        }
        #[cfg(target_os = "linux")]
        Platform::Linux => {
            // Use eBPF on Linux
            if linux::ebpf::is_ebpf_available() {
                log::info!("Initializing Linux eBPF syscall interceptor");
                Ok(Box::new(linux::ebpf::LinuxEbpfSyscallInterceptor::new()?))
            } else {
                Err(crate::error::Error::Platform(
                    crate::error::PlatformError::Linux(
                        "eBPF is not available on this Linux system".to_string(),
                    ),
                ))
            }
        }
        _ => Err(crate::error::Error::UnsupportedPlatform),
    }
}

/// Configure a syscall interceptor to log all syscalls
/// 
/// This is a convenience function that configures a syscall interceptor to log all syscalls
/// to a file, stdout, stderr, or any combination of these.
/// 
/// # Arguments
/// 
/// * `interceptor` - The syscall interceptor to configure
/// * `log_file` - Optional path to a log file
/// * `log_to_stdout` - Whether to log to stdout
/// * `log_to_stderr` - Whether to log to stderr
/// * `log_to_elasticsearch` - Whether to log to Elasticsearch
/// 
/// # Returns
/// 
/// A result indicating success or failure
/// 
/// # Example
/// 
/// ```
/// use inspector_gadget::syscalls::{init_syscall_interceptor, log_all_syscalls};
/// use inspector_gadget::platforms::Platform;
/// 
/// let mut interceptor = init_syscall_interceptor(Platform::current()).unwrap();
/// log_all_syscalls(&mut *interceptor, Some("syscalls.log"), true, false, true).unwrap();
/// interceptor.attach_to_process(1234).unwrap();
/// ```
pub fn log_all_syscalls(
    interceptor: &mut dyn SyscallInterceptor,
    log_file: Option<&str>,
    log_to_stdout: bool,
    log_to_stderr: bool,
    log_to_elasticsearch: bool,
) -> Result<()> {
    use std::sync::{Arc, Mutex};
    use std::fs::OpenOptions;
    use std::io::Write;
    
    // Create a filter that allows all syscalls
    let filter = common::filter::SyscallFilter::new();
    
    // Apply the filter to the interceptor
    interceptor.apply_filter(filter)?;
    
    // Set up logging based on the provided options
    let log_file_handle = if let Some(path) = log_file {
        let file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(path)?;
        Some(Arc::new(Mutex::new(file)))
    } else {
        None
    };
    
    // Set up Elasticsearch logging if requested
    #[cfg(feature = "elasticsearch")]
    let es_logger = if log_to_elasticsearch {
        use crate::logging::ElasticsearchLogger;
        
        match ElasticsearchLogger::new(None) {
            Ok(logger) => {
                log::info!("Elasticsearch logging enabled");
                Some(Arc::new(logger))
            },
            Err(e) => {
                log::error!("Failed to initialize Elasticsearch logger: {}", e);
                None
            }
        }
    } else {
        None
    };
    
    // Set up a callback to handle syscall events
    #[cfg(feature = "elasticsearch")]
    let es_logger_clone = es_logger.clone();
    
    interceptor.set_event_callback(Box::new(move |event| {
        // Format the syscall event
        let formatted = format!(
            "[{}:{}] {}({}) = {} <{} ns>",
            event.process_id,
            event.thread_id,
            event.syscall_name,
            event.parameters.iter()
                .map(|p| format!("{}={}", p.name, p.value))
                .collect::<Vec<String>>()
                .join(", "),
            event.return_value,
            event.duration_ns
        );
        
        // Log to stdout if requested
        if log_to_stdout {
            println!("{}", formatted);
        }
        
        // Log to stderr if requested
        if log_to_stderr {
            eprintln!("{}", formatted);
        }
        
        // Log to file if requested
        if let Some(file_handle) = &log_file_handle {
            if let Ok(mut file) = file_handle.lock() {
                let _ = writeln!(file, "{}", formatted);
            }
        }
        
        // Log to Elasticsearch if requested
        #[cfg(feature = "elasticsearch")]
        if let Some(logger) = &es_logger_clone {
            // We can't use .await here, so we'll spawn a task to handle it
            let event_clone = event.clone();
            let logger_clone = logger.clone();
            
            tokio::spawn(async move {
                if let Err(e) = logger_clone.log(&event_clone).await {
                    log::error!("Failed to log to Elasticsearch: {}", e);
                }
            });
        }
        
        Ok(())
    }))?;
    
    Ok(())
}

/// Convenience function for the common case of logging all syscalls
/// 
/// This is a shorthand for `log_all_syscalls(interceptor, log_file, log_to_stdout, log_to_stderr, false)`.
pub fn log_all_syscalls_simple(
    interceptor: &mut dyn SyscallInterceptor,
    log_file: Option<&str>,
    log_to_stdout: bool,
    log_to_stderr: bool,
) -> Result<()> {
    log_all_syscalls(interceptor, log_file, log_to_stdout, log_to_stderr, false)
}

/// Trait for syscall interception implementations
pub trait SyscallInterceptor: Send + Sync {
    /// Start intercepting syscalls for a specific process
    fn attach_to_process(&mut self, pid: u32) -> Result<()>;
    
    /// Start intercepting syscalls for a specific binary
    fn trace_binary(&mut self, path: &std::path::Path) -> Result<()>;
    
    /// Apply a filter to the syscalls being intercepted
    fn apply_filter(&mut self, filter: SyscallFilter) -> Result<()>;
    
    /// Collect intercepted syscall events
    fn collect_events(&self) -> Result<Vec<SyscallEvent>>;
    
    /// Stop intercepting syscalls
    fn stop(&mut self) -> Result<()>;
    
    /// Get statistics about intercepted syscalls
    fn get_statistics(&self) -> Result<SyscallStatistics>;
    
    /// Set a callback to be called for each syscall event
    fn set_event_callback(&mut self, callback: Box<dyn Fn(&SyscallEvent) -> Result<()> + Send + Sync>) -> Result<()>;
}

/// Statistics about intercepted syscalls
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyscallStatistics {
    /// Total number of syscalls intercepted
    pub total_count: usize,
    /// Number of syscalls by category
    pub category_counts: std::collections::HashMap<SyscallCategory, usize>,
    /// Number of syscalls by ID
    pub syscall_counts: std::collections::HashMap<u32, usize>,
    /// Number of syscalls by process
    pub process_counts: std::collections::HashMap<u32, usize>,
    /// Number of failed syscalls
    pub failed_count: usize,
    /// Start time of collection
    pub start_time: u64,
    /// End time of collection
    pub end_time: u64,
}

impl SyscallStatistics {
    /// Create a new empty statistics object
    pub fn new() -> Self {
        Self {
            total_count: 0,
            category_counts: std::collections::HashMap::new(),
            syscall_counts: std::collections::HashMap::new(),
            process_counts: std::collections::HashMap::new(),
            failed_count: 0,
            start_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            end_time: 0,
        }
    }
    
    /// Update statistics with a new syscall event
    pub fn update(&mut self, event: &SyscallEvent) {
        self.total_count += 1;
        
        // Update category count
        *self.category_counts.entry(event.category).or_insert(0) += 1;
        
        // Update syscall count
        *self.syscall_counts.entry(event.syscall_id).or_insert(0) += 1;
        
        // Update process count
        *self.process_counts.entry(event.process_id).or_insert(0) += 1;
        
        // Update failed count
        if !event.success {
            self.failed_count += 1;
        }
        
        // Update end time
        self.end_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
    }
    
    /// Merge another statistics object into this one
    pub fn merge(&mut self, other: &SyscallStatistics) {
        self.total_count += other.total_count;
        self.failed_count += other.failed_count;
        
        // Merge category counts
        for (category, count) in &other.category_counts {
            *self.category_counts.entry(*category).or_insert(0) += count;
        }
        
        // Merge syscall counts
        for (syscall_id, count) in &other.syscall_counts {
            *self.syscall_counts.entry(*syscall_id).or_insert(0) += count;
        }
        
        // Merge process counts
        for (process_id, count) in &other.process_counts {
            *self.process_counts.entry(*process_id).or_insert(0) += count;
        }
        
        // Update time range
        self.start_time = self.start_time.min(other.start_time);
        self.end_time = self.end_time.max(other.end_time);
    }
}

impl Default for SyscallStatistics {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::syscalls::common::SyscallCategory;
    
    #[test]
    fn test_syscall_statistics() {
        let mut stats = SyscallStatistics::new();
        
        // Create a test event
        let event = SyscallEvent {
            id: 1,
            timestamp: 12345,
            process_id: 1000,
            thread_id: 1001,
            syscall_id: 42,
            syscall_name: "test_syscall".to_string(),
            category: SyscallCategory::FileSystem,
            parameters: vec![],
            return_value: 0,
            success: true,
            duration_ns: 100,
        };
        
        // Update statistics
        stats.update(&event);
        
        // Check statistics
        assert_eq!(stats.total_count, 1);
        assert_eq!(stats.category_counts.get(&SyscallCategory::FileSystem), Some(&1));
        assert_eq!(stats.syscall_counts.get(&42), Some(&1));
        assert_eq!(stats.process_counts.get(&1000), Some(&1));
        assert_eq!(stats.failed_count, 0);
        
        // Create a failed event
        let failed_event = SyscallEvent {
            id: 2,
            timestamp: 12346,
            process_id: 1000,
            thread_id: 1001,
            syscall_id: 43,
            syscall_name: "test_syscall_failed".to_string(),
            category: SyscallCategory::Process,
            parameters: vec![],
            return_value: -1,
            success: false,
            duration_ns: 200,
        };
        
        // Update statistics
        stats.update(&failed_event);
        
        // Check statistics
        assert_eq!(stats.total_count, 2);
        assert_eq!(stats.category_counts.get(&SyscallCategory::FileSystem), Some(&1));
        assert_eq!(stats.category_counts.get(&SyscallCategory::Process), Some(&1));
        assert_eq!(stats.syscall_counts.get(&42), Some(&1));
        assert_eq!(stats.syscall_counts.get(&43), Some(&1));
        assert_eq!(stats.process_counts.get(&1000), Some(&2));
        assert_eq!(stats.failed_count, 1);
        
        // Test merging
        let mut other_stats = SyscallStatistics::new();
        other_stats.total_count = 5;
        other_stats.failed_count = 2;
        other_stats.category_counts.insert(SyscallCategory::Network, 3);
        other_stats.category_counts.insert(SyscallCategory::Process, 2);
        other_stats.syscall_counts.insert(44, 5);
        other_stats.process_counts.insert(1001, 5);
        
        // Merge statistics
        stats.merge(&other_stats);
        
        // Check merged statistics
        assert_eq!(stats.total_count, 7);
        assert_eq!(stats.category_counts.get(&SyscallCategory::FileSystem), Some(&1));
        assert_eq!(stats.category_counts.get(&SyscallCategory::Process), Some(&3));
        assert_eq!(stats.category_counts.get(&SyscallCategory::Network), Some(&3));
        assert_eq!(stats.syscall_counts.get(&42), Some(&1));
        assert_eq!(stats.syscall_counts.get(&43), Some(&1));
        assert_eq!(stats.syscall_counts.get(&44), Some(&5));
        assert_eq!(stats.process_counts.get(&1000), Some(&2));
        assert_eq!(stats.process_counts.get(&1001), Some(&5));
        assert_eq!(stats.failed_count, 3);
    }
} 