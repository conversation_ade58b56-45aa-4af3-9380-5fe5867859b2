/*!
 * Memory Buffer Implementation
 * 
 * This module provides a simple in-memory buffer for storing events.
 * Unlike the circular buffer, this implementation is simpler and doesn't
 * support blocking behavior.
 */

use std::sync::{Arc, Mutex, Condvar};
use std::time::{Duration, Instant};
use std::collections::VecDeque;
use async_trait::async_trait;

use crate::core::events::TraceEvent;
use crate::error::Result;
use super::{BufferOverflowBehavior, BufferStats, EventBuffer};

/// Configuration for the memory buffer.
#[derive(Debug, Clone)]
pub struct MemoryBufferConfig {
    /// Initial capacity of the buffer
    pub initial_capacity: usize,
}

impl Default for MemoryBufferConfig {
    fn default() -> Self {
        Self {
            initial_capacity: 1000,
        }
    }
}

/// Internal state of the memory buffer.
struct MemoryBufferState {
    /// The buffer of events
    buffer: VecDeque<TraceEvent>,
    /// Statistics for the buffer
    stats: BufferStats,
}

/// Memory buffer for storing events
pub struct MemoryBuffer {
    /// The internal state of the buffer
    state: Mutex<MemoryBufferState>,
    /// Condition variable for blocking operations
    condvar: Condvar,
    /// Buffer capacity
    capacity: usize,
    /// Overflow behavior
    overflow_behavior: BufferOverflowBehavior,
}

impl MemoryBuffer {
    /// Create a new memory buffer
    pub fn new(config: MemoryBufferConfig, overflow_behavior: BufferOverflowBehavior) -> Self {
        let state = MemoryBufferState {
            buffer: VecDeque::with_capacity(config.initial_capacity),
            stats: BufferStats::default(),
        };

        Self {
            state: Mutex::new(state),
            condvar: Condvar::new(),
            capacity: config.initial_capacity,
            overflow_behavior,
        }
    }

    /// Add an event to the buffer.
    ///
    /// # Arguments
    ///
    /// * `event` - The event to add
    ///
    /// # Returns
    ///
    /// `true` if the event was added, `false` if it was dropped
    pub fn add(&self, event: TraceEvent) -> bool {
        let mut state = self.state.lock().unwrap();

        // Check if buffer is full
        if state.buffer.len() >= self.capacity {
            match self.overflow_behavior {
                BufferOverflowBehavior::DropOldest => {
                    // Remove oldest event
                    state.buffer.pop_front();
                    state.stats.dropped += 1;
                    // Add new event
                    state.buffer.push_back(event);
                    state.stats.added += 1;
                }
                BufferOverflowBehavior::DropNewest => {
                    // Drop new event
                    state.stats.dropped += 1;
                }
                BufferOverflowBehavior::Block => {
                    // In memory buffer doesn't support blocking
                    // So we'll drop the newest event
                    state.stats.dropped += 1;
                }
            }
        } else {
            // Add event
            state.buffer.push_back(event);
            state.stats.added += 1;
        }
        
        // Update high water mark
        if state.buffer.len() > state.stats.high_water_mark {
            state.stats.high_water_mark = state.buffer.len();
        }
        
        // Notify waiters
        self.condvar.notify_one();

        true
    }
    
    /// Add multiple events to the buffer.
    ///
    /// # Arguments
    ///
    /// * `events` - The events to add
    ///
    /// # Returns
    ///
    /// The number of events that were added
    pub fn add_batch(&self, events: &[TraceEvent]) -> usize {
        if events.is_empty() {
            return 0;
        }
        
        let mut state = self.state.lock().unwrap();
        
        // Calculate available space
        let available_space = self.capacity - state.buffer.len();
        
        if events.len() <= available_space {
            // Add all events
            state.buffer.extend(events.iter().cloned());
            state.stats.added += events.len();
        } else {
            match self.overflow_behavior {
                BufferOverflowBehavior::DropOldest => {
                    // Calculate how many old events to drop
                    let to_drop = events.len() - available_space;
                    
                    // Remove oldest events
                    if to_drop >= state.buffer.len() {
                        // Drop all existing events
                        state.stats.dropped += state.buffer.len();
                        state.buffer.clear();
                    } else {
                        // Drop some existing events
                        state.buffer.drain(0..to_drop);
                        state.stats.dropped += to_drop;
                    }
                    
                    // Add new events
                    state.buffer.extend(events.iter().cloned());
                    state.stats.added += events.len();
                }
                BufferOverflowBehavior::DropNewest | BufferOverflowBehavior::Block => {
                    // Add as many events as possible
                    state.buffer.extend(events.iter().cloned());
                    state.stats.added += available_space;
                    state.stats.dropped += events.len() - available_space;
                }
            }
        }
        
        // Update high water mark
        if state.buffer.len() > state.stats.high_water_mark {
            state.stats.high_water_mark = state.buffer.len();
        }
        
        // Notify waiters
        self.condvar.notify_all();

        events.len()
    }
    
    /// Get the next event from the buffer.
    ///
    /// # Returns
    ///
    /// The next event, or `None` if the buffer is empty
    pub fn next(&self) -> Option<TraceEvent> {
        let mut state = self.state.lock().unwrap();
        
        if state.buffer.is_empty() {
            None
        } else {
            let event = state.buffer.pop_front().unwrap();
            
            // Update stats
            state.stats.removed += 1;
            
            Some(event)
        }
    }
    
    /// Get a batch of events from the buffer.
    ///
    /// # Arguments
    ///
    /// * `max_count` - The maximum number of events to get
    /// * `timeout` - The maximum time to wait for events
    ///
    /// # Returns
    ///
    /// A vector of events
    pub fn next_batch(&self, max_count: usize, timeout: Duration) -> Vec<TraceEvent> {
        let mut state = self.state.lock().unwrap();
        let start_time = Instant::now();
        let mut events = Vec::new();

        // Wait until there are events or the timeout expires
        while state.buffer.is_empty() && start_time.elapsed() < timeout {
            let remaining = timeout.checked_sub(start_time.elapsed()).unwrap_or(Duration::from_secs(0));
            let (new_state, _) = self.condvar.wait_timeout(state, remaining).unwrap();
            state = new_state;

            if start_time.elapsed() >= timeout {
                break;
            }
        }

        // Get up to max_count events
        let count = std::cmp::min(max_count, state.buffer.len());
        for _ in 0..count {
            if let Some(event) = state.buffer.pop_front() {
                events.push(event);
                state.stats.removed += 1;
            }
        }

        state.stats.current_count = state.buffer.len();

        events
    }
    
    /// Get the number of events in the buffer.
    ///
    /// # Returns
    ///
    /// The number of events in the buffer
    pub fn len(&self) -> usize {
        let state = self.state.lock().unwrap();
        state.buffer.len()
    }
    
    /// Check if the buffer is empty.
    ///
    /// # Returns
    ///
    /// `true` if the buffer is empty, `false` otherwise
    pub fn is_empty(&self) -> bool {
        let state = self.state.lock().unwrap();
        state.buffer.is_empty()
    }
    
    /// Get statistics for the buffer.
    ///
    /// # Returns
    ///
    /// Statistics for the buffer
    pub fn stats(&self) -> BufferStats {
        let state = self.state.lock().unwrap();
        state.stats.clone()
    }
    
    /// Clear the buffer.
    pub fn clear(&self) {
        let mut state = self.state.lock().unwrap();
        state.buffer.clear();
        state.stats.current_count = 0;
        
        // Notify waiters
        self.condvar.notify_all();
    }
}

#[async_trait]
impl EventBuffer for MemoryBuffer {
    async fn add(&self, event: TraceEvent) -> Result<()> {
        let mut state = self.state.lock().await;
        let mut stats = self.stats().await;

        // Check if buffer is full
        if state.buffer.len() >= self.capacity {
            match self.overflow_behavior {
                BufferOverflowBehavior::DropOldest => {
                    // Remove oldest event
                    state.buffer.pop_front();
                    stats.dropped += 1;
                    // Add new event
                    state.buffer.push_back(event);
                    stats.added += 1;
                }
                BufferOverflowBehavior::DropNewest => {
                    // Drop new event
                    stats.dropped += 1;
                }
                BufferOverflowBehavior::Block => {
                    // In memory buffer doesn't support blocking
                    // So we'll drop the newest event
                    stats.dropped += 1;
                }
            }
        } else {
            // Add event
            state.buffer.push_back(event);
            stats.added += 1;
        }
        
        // Update high water mark
        if state.buffer.len() > stats.high_water_mark {
            stats.high_water_mark = state.buffer.len();
        }
        
        // Notify waiters
        self.condvar.notify_one();
        
        Ok(())
    }
    
    async fn add_batch(&self, events: &[TraceEvent]) -> Result<()> {
        if events.is_empty() {
            return Ok(());
        }
        
        let mut state = self.state.lock().await;
        let mut stats = self.stats().await;
        
        // Calculate available space
        let available_space = self.capacity - state.buffer.len();
        
        if events.len() <= available_space {
            // Add all events
            state.buffer.extend(events.iter().cloned());
            stats.added += events.len();
        } else {
            match self.overflow_behavior {
                BufferOverflowBehavior::DropOldest => {
                    // Calculate how many old events to drop
                    let to_drop = events.len() - available_space;
                    
                    // Remove oldest events
                    if to_drop >= state.buffer.len() {
                        // Drop all existing events
                        stats.dropped += state.buffer.len();
                        state.buffer.clear();
                    } else {
                        // Drop some existing events
                        state.buffer.drain(0..to_drop);
                        stats.dropped += to_drop;
                    }
                    
                    // Add new events
                    state.buffer.extend(events.iter().cloned());
                    stats.added += events.len();
                }
                BufferOverflowBehavior::DropNewest | BufferOverflowBehavior::Block => {
                    // Add as many events as possible
                    state.buffer.extend(events.iter().cloned());
                    stats.added += available_space;
                    stats.dropped += events.len() - available_space;
                }
            }
        }
        
        // Update high water mark
        if state.buffer.len() > stats.high_water_mark {
            stats.high_water_mark = state.buffer.len();
        }
        
        // Notify waiters
        self.condvar.notify_all();
        
        Ok(())
    }
    
    async fn next(&self) -> Result<Option<TraceEvent>> {
        let mut state = self.state.lock().await;
        
        if state.buffer.is_empty() {
            Ok(None)
        } else {
            let event = state.buffer.pop_front().unwrap();
            
            // Update stats
            state.stats.removed += 1;
            
            Ok(Some(event))
        }
    }
    
    async fn next_batch(&self, max_count: usize, timeout: Duration) -> Result<Vec<TraceEvent>> {
        let mut state = self.state.lock().await;
        let start_time = Instant::now();
        let mut events = Vec::new();

        // Wait until there are events or the timeout expires
        while state.buffer.is_empty() && start_time.elapsed() < timeout {
            let remaining = timeout.checked_sub(start_time.elapsed()).unwrap_or(Duration::from_secs(0));
            let (new_state, _) = self.condvar.wait_timeout(state, remaining).unwrap();
            state = new_state;

            if start_time.elapsed() >= timeout {
                break;
            }
        }

        // Get up to max_count events
        let count = std::cmp::min(max_count, state.buffer.len());
        for _ in 0..count {
            if let Some(event) = state.buffer.pop_front() {
                events.push(event);
                state.stats.removed += 1;
            }
        }

        state.stats.current_count = state.buffer.len();

        Ok(events)
    }
    
    async fn len(&self) -> usize {
        let state = self.state.lock().await;
        state.buffer.len()
    }
    
    async fn is_empty(&self) -> bool {
        let state = self.state.lock().await;
        state.buffer.is_empty()
    }
    
    async fn stats(&self) -> BufferStats {
        let state = self.state.lock().await;
        state.stats.clone()
    }
    
    async fn clear(&self) -> Result<()> {
        let mut state = self.state.lock().await;
        let count = state.buffer.len();
        state.buffer.clear();
        
        // Update stats
        let mut stats = self.stats().await;
        stats.removed += count;
        
        // Notify waiters
        self.condvar.notify_all();
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{EventData, EventSeverity, EventType, TraceEvent};

    #[tokio::test]
    async fn test_memory_buffer_basic() {
        let config = MemoryBufferConfig {
            initial_capacity: 10,
        };

        let buffer = MemoryBuffer::new(config, BufferOverflowBehavior::DropOldest);
        
        // Create test event
        let event = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Add event
        buffer.add(event.clone()).await.unwrap();
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 1);
        assert_eq!(stats.removed, 0);
        assert_eq!(stats.dropped, 0);
        assert_eq!(stats.high_water_mark, 1);
        
        // Get event
        let next = buffer.next().await.unwrap().unwrap();
        
        // Check event
        assert_eq!(next.id, event.id);
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 1);
        assert_eq!(stats.removed, 1);
        assert_eq!(stats.dropped, 0);
        assert_eq!(stats.high_water_mark, 1);
        
        // Check empty
        assert!(buffer.is_empty().await);
        assert_eq!(buffer.len().await, 0);
        assert!(buffer.next().await.unwrap().is_none());
    }
    
    #[tokio::test]
    async fn test_memory_buffer_overflow() {
        let config = MemoryBufferConfig {
            initial_capacity: 2,
        };

        let buffer = MemoryBuffer::new(config, BufferOverflowBehavior::DropOldest);
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("test1".to_string()),
            EventData::Custom(serde_json::json!({"test": 1})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("test2".to_string()),
            EventData::Custom(serde_json::json!({"test": 2})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event3 = TraceEvent::now(
            EventType::Custom("test3".to_string()),
            EventData::Custom(serde_json::json!({"test": 3})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Add events
        buffer.add(event1.clone()).await.unwrap();
        buffer.add(event2.clone()).await.unwrap();
        buffer.add(event3.clone()).await.unwrap();
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 3);
        assert_eq!(stats.removed, 0);
        assert_eq!(stats.dropped, 1);
        assert_eq!(stats.high_water_mark, 2);
        
        // Get events
        let next1 = buffer.next().await.unwrap().unwrap();
        let next2 = buffer.next().await.unwrap().unwrap();
        
        // Check events (event1 should be dropped)
        assert_eq!(next1.id, event2.id);
        assert_eq!(next2.id, event3.id);
        
        // Check empty
        assert!(buffer.is_empty().await);
    }
    
    #[tokio::test]
    async fn test_memory_buffer_batch() {
        let config = MemoryBufferConfig {
            initial_capacity: 5,
        };

        let buffer = MemoryBuffer::new(config, BufferOverflowBehavior::DropOldest);
        
        // Create test events
        let events: Vec<TraceEvent> = (0..3).map(|i| {
            TraceEvent::now(
                EventType::Custom(format!("test{}", i)),
                EventData::Custom(serde_json::json!({"test": i})),
                1,
                1,
                EventSeverity::Info,
                "test",
                vec![],
            )
        }).collect();
        
        // Add batch
        buffer.add_batch(&events).await.unwrap();
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 3);
        assert_eq!(stats.removed, 0);
        assert_eq!(stats.dropped, 0);
        assert_eq!(stats.high_water_mark, 3);
        
        // Get batch
        let batch = buffer.next_batch(2, Duration::from_millis(0)).await.unwrap();
        
        // Check batch
        assert_eq!(batch.len(), 2);
        assert_eq!(batch[0].id, events[0].id);
        assert_eq!(batch[1].id, events[1].id);
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 3);
        assert_eq!(stats.removed, 2);
        assert_eq!(stats.dropped, 0);
        assert_eq!(stats.high_water_mark, 3);
        
        // Get remaining event
        let next = buffer.next().await.unwrap().unwrap();
        
        // Check event
        assert_eq!(next.id, events[2].id);
        
        // Check empty
        assert!(buffer.is_empty().await);
    }
} 