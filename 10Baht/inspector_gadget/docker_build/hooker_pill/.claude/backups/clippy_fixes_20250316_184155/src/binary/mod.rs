/*!
 * Binary Loading and Parsing Module
 * 
 * This module provides functionality for loading and parsing binary files
 * across different formats (ELF, PE) and extracting relevant information
 * for analysis.
 */

// Submodules
pub mod elf;
pub mod pe;
pub mod analysis;

// Test module
#[cfg(test)]
mod tests;

// Re-exports
pub use self::elf::ElfBinary;
pub use self::pe::PeBinary;

use std::path::Path;
use std::fs::File;
use std::io;
use memmap2::{Mmap, MmapOptions};
use thiserror::Error;

/// Errors that can occur during binary loading and parsing
#[derive(Error, Debug)]
pub enum BinaryError {
    #[error("IO error: {0}")]
    Io(#[from] io::Error),
    
    #[error("Failed to parse binary: {0}")]
    ParseError(String),
    
    #[error("Unsupported binary format")]
    UnsupportedFormat,
    
    #[error("Invalid binary file")]
    InvalidBinary,
    
    #[error("Section not found: {0}")]
    SectionNotFound(String),
}

/// Result type for binary operations
pub type Result<T> = std::result::Result<T, BinaryError>;

/// Supported binary formats
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BinaryFormat {
    /// Executable and Linkable Format (Linux, Unix)
    Elf,
    /// Portable Executable (Windows)
    Pe,
    /// Unknown or unsupported format
    Unknown,
}

/// Common interface for binary files
pub trait Binary {
    /// Get the format of the binary
    fn format(&self) -> BinaryFormat;
    
    /// Get basic information about the binary
    fn info(&self) -> BinaryInfo;
    
    /// Get a list of sections in the binary
    fn sections(&self) -> Vec<BinarySection>;
    
    /// Get a specific section by name
    fn get_section(&self, name: &str) -> Option<BinarySection>;
    
    /// Check if the binary is executable
    fn is_executable(&self) -> bool;
    
    /// Check if the binary is a shared library
    fn is_library(&self) -> bool;
    
    /// Get the entry point address
    fn entry_point(&self) -> u64;
    
    /// Get the list of imported symbols
    fn imports(&self) -> Vec<String>;
    
    /// Get the list of exported symbols
    fn exports(&self) -> Vec<String>;
}

/// Basic information about a binary
#[derive(Debug, Clone)]
pub struct BinaryInfo {
    /// Format of the binary
    pub format: BinaryFormat,
    /// Architecture the binary is compiled for
    pub architecture: String,
    /// Whether the binary is 64-bit
    pub is_64bit: bool,
    /// Whether the binary is executable
    pub is_executable: bool,
    /// Whether the binary is a shared library
    pub is_library: bool,
    /// Entry point address
    pub entry_point: u64,
    /// Path to the binary file
    pub path: Option<String>,
}

/// Information about a section in a binary
#[derive(Debug, Clone)]
pub struct BinarySection {
    /// Name of the section
    pub name: String,
    /// Virtual address of the section
    pub address: u64,
    /// Size of the section in bytes
    pub size: u64,
    /// Whether the section is executable
    pub is_executable: bool,
    /// Whether the section is writable
    pub is_writable: bool,
    /// Whether the section contains code
    pub is_code: bool,
    /// Whether the section contains data
    pub is_data: bool,
    /// Raw data of the section (if loaded)
    pub data: Option<Vec<u8>>,
}

/// Load a binary file from the given path
pub fn load_binary<P: AsRef<Path>>(path: P) -> Result<Box<dyn Binary>> {
    let file = File::open(path.as_ref())?;
    let mmap = unsafe { MmapOptions::new().map(&file)? };
    
    // Detect the binary format
    let format = detect_format(&mmap)?;
    
    match format {
        BinaryFormat::Elf => {
            let elf_binary = elf::ElfBinary::parse(path.as_ref(), &mmap)?;
            Ok(Box::new(elf_binary))
        },
        BinaryFormat::Pe => {
            let pe_binary = pe::PeBinary::parse(path.as_ref(), &mmap)?;
            Ok(Box::new(pe_binary))
        },
        BinaryFormat::Unknown => {
            Err(BinaryError::UnsupportedFormat)
        }
    }
}

/// Detect the format of a binary from its magic bytes
fn detect_format(data: &[u8]) -> Result<BinaryFormat> {
    if data.len() < 4 {
        return Err(BinaryError::InvalidBinary);
    }
    
    // Check for ELF magic bytes (0x7F, 'E', 'L', 'F')
    if data.starts_with(&[0x7F, 0x45, 0x4C, 0x46]) {
        return Ok(BinaryFormat::Elf);
    }
    
    // Check for PE magic bytes ('M', 'Z')
    if data.starts_with(&[0x4D, 0x5A]) {
        // Further validation for PE format
        if data.len() >= 0x40 {
            let pe_offset = u32::from_le_bytes([
                data[0x3C], data[0x3D], data[0x3E], data[0x3F]
            ]) as usize;
            
            if pe_offset < data.len() && pe_offset + 4 <= data.len() && &data[pe_offset..pe_offset + 4] == b"PE\0\0" {
                return Ok(BinaryFormat::Pe);
            }
    
    Ok(BinaryFormat::Unknown)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_detect_format_elf() {
        let data = [0x7F, 0x45, 0x4C, 0x46, 0x02, 0x01, 0x01, 0x00];
        assert_eq!(detect_format(&data).unwrap(), BinaryFormat::Elf);
    }
    
    #[test]
    fn test_detect_format_pe() {
        let mut data = vec![0; 0x100];
        // MZ header
        data[0] = 0x4D;
        data[1] = 0x5A;
        // PE offset at 0x3C
        data[0x3C] = 0x80;
        // PE signature at offset 0x80
        data[0x80] = b'P';
        data[0x81] = b'E';
        data[0x82] = 0;
        data[0x83] = 0;
        
        assert_eq!(detect_format(&data).unwrap(), BinaryFormat::Pe);
    }
    
    #[test]
    fn test_detect_format_unknown() {
        let data = [0x00, 0x01, 0x02, 0x03];
        assert_eq!(detect_format(&data).unwrap(), BinaryFormat::Unknown);
    }
} 