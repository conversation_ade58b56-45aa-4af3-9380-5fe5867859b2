/*!
 * Type-Safe eBPF Map Operations
 * 
 * This module provides type-safe wrappers for eBPF maps.
 */

use std::marker::PhantomData;
use serde::{Serialize, de::DeserializeOwned};
use super::{Map, MapConfig, MapType, MapError, MapBuilder, MapIter};

/// Type-safe eBPF map
#[derive(Debug)]
pub struct TypedMap<K, V> {
    /// Inner map
    map: Map,
    /// Key type
    _key_type: PhantomData<K>,
    /// Value type
    _value_type: PhantomData<V>,
}

impl<K, V> TypedMap<K, V>
where
    K: Serialize + DeserializeOwned,
    V: Serialize + DeserializeOwned,
{
    /// Create a new typed map
    pub fn create(config: MapConfig) -> Result<Self, MapError> {
        let map = Map::create(config)?;
        Ok(Self {
            map,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
    
    /// Open an existing typed map
    pub fn open(path: &str) -> Result<Self, MapError> {
        let map = Map::open(path)?;
        Ok(Self {
            map,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
    
    /// Update a key-value pair
    pub fn update(&self, key: &K, value: &V, flags: u64) -> Result<(), MapError> {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
        
        // Serialize the value
        let value_bytes = bincode::serialize(value)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize value: {}", e)))?;
        
        // Update the map
        self.map.update(&key_bytes, &value_bytes, flags)
    }
    
    /// Lookup a value by key
    pub fn lookup(&self, key: &K) -> Result<V, MapError> {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
        
        // Lookup the value
        let value_bytes = self.map.lookup(&key_bytes)?;
        
        // Deserialize the value
        bincode::deserialize(&value_bytes)
            .map_err(|e| MapError::DeserializationError(format!("Failed to deserialize value: {}", e)))
    }
    
    /// Delete a key-value pair
    pub fn delete(&self, key: &K) -> Result<(), MapError> {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
        
        // Delete the key-value pair
        self.map.delete(&key_bytes)
    }
    
    /// Create an iterator over the map's key-value pairs
    pub fn iter(&self) -> impl Iterator<Item = Result<(K, V), MapError>> + '_ {
        // Create a typed map iterator
        super::iter::TypedMapIter::<K, V>::new(&self.map)
    }
    
    /// Pin the map to the BPF filesystem
    pub fn pin(&self, path: &str) -> Result<(), MapError> {
        self.map.pin(path)
    }
    
    /// Unpin the map from the BPF filesystem
    pub fn unpin(&self) -> Result<(), MapError> {
        self.map.unpin()
    }
    
    /// Get the inner map
    pub fn inner(&self) -> &Map {
        &self.map
    }
}

/// Type-safe eBPF map builder
#[derive(Debug)]
pub struct TypedMapBuilder<K, V> {
    /// Inner builder
    inner: MapBuilder,
    /// Key type
    _key_type: PhantomData<K>,
    /// Value type
    _value_type: PhantomData<V>,
}

impl<K, V> TypedMapBuilder<K, V>
where
    K: Serialize + DeserializeOwned,
    V: Serialize + DeserializeOwned,
{
    /// Create a new typed map builder
    pub fn new(map_type: MapType) -> Self {
        Self {
            inner: MapBuilder::new(map_type),
            _key_type: PhantomData,
            _value_type: PhantomData,
        }
    }
    
    /// Set the map name
    pub fn name(mut self, name: impl Into<String>) -> Self {
        self.inner = self.inner.name(name);
        self
    }
    
    /// Set the key size
    pub fn key_size(mut self, key_size: usize) -> Self {
        self.inner = self.inner.key_size(key_size);
        self
    }
    
    /// Set the value size
    pub fn value_size(mut self, value_size: usize) -> Self {
        self.inner = self.inner.value_size(value_size);
        self
    }
    
    /// Set the maximum number of entries
    pub fn max_entries(mut self, max_entries: usize) -> Self {
        self.inner = self.inner.max_entries(max_entries);
        self
    }
    
    /// Set the map flags
    pub fn flags(mut self, flags: u32) -> Self {
        self.inner = self.inner.flags(flags);
        self
    }
    
    /// Build the typed map
    pub fn build(self) -> Result<TypedMap<K, V>, MapError> {
        let map = self.inner.build()?;
        Ok(TypedMap {
            map,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
}

// Convenience functions for creating common typed map types
impl<K, V> TypedMapBuilder<K, V>
where
    K: Serialize + DeserializeOwned,
    V: Serialize + DeserializeOwned,
{
    /// Create a new hash map builder
    pub fn hash() -> Self {
        Self::new(MapType::Hash)
    }
    
    /// Create a new array map builder
    pub fn array() -> Self {
        Self::new(MapType::Array)
    }
    
    /// Create a new per-CPU hash map builder
    pub fn percpu_hash() -> Self {
        Self::new(MapType::PerCpuHash)
    }
    
    /// Create a new per-CPU array map builder
    pub fn percpu_array() -> Self {
        Self::new(MapType::PerCpuArray)
    }
    
    /// Create a new LRU hash map builder
    pub fn lru_hash() -> Self {
        Self::new(MapType::LruHash)
    }
    
    /// Create a new per-CPU LRU hash map builder
    pub fn lru_percpu_hash() -> Self {
        Self::new(MapType::LruPerCpuHash)
    }
} 