/*!
 * Windows Syscall Registry
 * 
 * This module provides functionality for loading syscall definitions
 * from the Windows registry or other sources.
 */



use crate::platforms::Platform;
use crate::syscalls::common::{Syscall, SyscallCategory, register_syscall};

/// Load syscall definitions from the Windows registry
pub fn load_syscalls_from_registry() {
    // In a real implementation, this would load syscall definitions from the registry
    // For now, we'll just load some additional syscalls
    
    info!("Loading additional Windows syscall definitions");
    
    // Load additional syscalls
    load_additional_syscalls();
}

/// Load additional syscall definitions
fn load_additional_syscalls() {
    // Device syscalls
    register_syscall(
        Syscall::new(
            0x80,
            "NtCreateIoCompletion",
            Platform::Windows,
            SyscallCategory::IO,
        )
        .with_description("Creates an I/O completion object"),
    );
    
    register_syscall(
        Syscall::new(
            0x81,
            "NtOpenIoCompletion",
            Platform::Windows,
            SyscallCategory::IO,
        )
        .with_description("Opens an I/O completion object"),
    );
    
    register_syscall(
        Syscall::new(
            0x82,
            "NtQueryIoCompletion",
            Platform::Windows,
            SyscallCategory::IO,
        )
        .with_description("Queries an I/O completion object"),
    );
    
    register_syscall(
        Syscall::new(
            0x83,
            "NtRemoveIoCompletion",
            Platform::Windows,
            SyscallCategory::IO,
        )
        .with_description("Removes an I/O completion packet from an I/O completion object"),
    );
    
    register_syscall(
        Syscall::new(
            0x84,
            "NtSetIoCompletion",
            Platform::Windows,
            SyscallCategory::IO,
        )
        .with_description("Sets an I/O completion object"),
    );
    
    // Synchronization syscalls
    register_syscall(
        Syscall::new(
            0x90,
            "NtCreateMutant",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Creates a mutant object"),
    );
    
    register_syscall(
        Syscall::new(
            0x91,
            "NtOpenMutant",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Opens a mutant object"),
    );
    
    register_syscall(
        Syscall::new(
            0x92,
            "NtReleaseMutant",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Releases a mutant object"),
    );
    
    register_syscall(
        Syscall::new(
            0x93,
            "NtCreateSemaphore",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Creates a semaphore object"),
    );
    
    register_syscall(
        Syscall::new(
            0x94,
            "NtOpenSemaphore",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Opens a semaphore object"),
    );
    
    register_syscall(
        Syscall::new(
            0x95,
            "NtReleaseSemaphore",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Releases a semaphore object"),
    );
    
    register_syscall(
        Syscall::new(
            0x96,
            "NtCreateEvent",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Creates an event object"),
    );
    
    register_syscall(
        Syscall::new(
            0x97,
            "NtOpenEvent",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Opens an event object"),
    );
    
    register_syscall(
        Syscall::new(
            0x98,
            "NtSetEvent",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Sets an event object"),
    );
    
    register_syscall(
        Syscall::new(
            0x99,
            "NtResetEvent",
            Platform::Windows,
            SyscallCategory::Synchronization,
        )
        .with_description("Resets an event object"),
    );
    
    // Time syscalls
    register_syscall(
        Syscall::new(
            0xA0,
            "NtQuerySystemTime",
            Platform::Windows,
            SyscallCategory::Time,
        )
        .with_description("Queries the system time"),
    );
    
    register_syscall(
        Syscall::new(
            0xA1,
            "NtSetSystemTime",
            Platform::Windows,
            SyscallCategory::Time,
        )
        .with_description("Sets the system time"),
    );
    
    register_syscall(
        Syscall::new(
            0xA2,
            "NtQueryPerformanceCounter",
            Platform::Windows,
            SyscallCategory::Time,
        )
        .with_description("Queries the performance counter"),
    );
    
    register_syscall(
        Syscall::new(
            0xA3,
            "NtQueryTimerResolution",
            Platform::Windows,
            SyscallCategory::Time,
        )
        .with_description("Queries the timer resolution"),
    );
    
    register_syscall(
        Syscall::new(
            0xA4,
            "NtSetTimerResolution",
            Platform::Windows,
            SyscallCategory::Time,
        )
        .with_description("Sets the timer resolution"),
    );
    
    // System information syscalls
    register_syscall(
        Syscall::new(
            0xB0,
            "NtQuerySystemInformation",
            Platform::Windows,
            SyscallCategory::SystemInfo,
        )
        .with_description("Queries system information"),
    );
    
    register_syscall(
        Syscall::new(
            0xB1,
            "NtSetSystemInformation",
            Platform::Windows,
            SyscallCategory::SystemInfo,
        )
        .with_description("Sets system information"),
    );
    
    register_syscall(
        Syscall::new(
            0xB2,
            "NtQueryInformationProcess",
            Platform::Windows,
            SyscallCategory::Process,
        )
        .with_description("Queries process information"),
    );
    
    register_syscall(
        Syscall::new(
            0xB3,
            "NtSetInformationProcess",
            Platform::Windows,
            SyscallCategory::Process,
        )
        .with_description("Sets process information"),
    );
    
    register_syscall(
        Syscall::new(
            0xB4,
            "NtQueryInformationThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Queries thread information"),
    );
    
    register_syscall(
        Syscall::new(
            0xB5,
            "NtSetInformationThread",
            Platform::Windows,
            SyscallCategory::Thread,
        )
        .with_description("Sets thread information"),
    );
    
    // Security syscalls
    register_syscall(
        Syscall::new(
            0xC0,
            "NtAccessCheck",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Checks if a client has the specified access rights to an object"),
    );
    
    register_syscall(
        Syscall::new(
            0xC1,
            "NtPrivilegeCheck",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Checks if a token has the specified privileges"),
    );
    
    register_syscall(
        Syscall::new(
            0xC2,
            "NtCreateToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Creates a token object"),
    );
    
    register_syscall(
        Syscall::new(
            0xC3,
            "NtDuplicateToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Duplicates a token object"),
    );
    
    register_syscall(
        Syscall::new(
            0xC4,
            "NtFilterToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Creates a filtered token"),
    );
    
    register_syscall(
        Syscall::new(
            0xC5,
            "NtImpersonateAnonymousToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Impersonates an anonymous token"),
    );
    
    register_syscall(
        Syscall::new(
            0xC6,
            "NtImpersonateThread",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Impersonates a thread"),
    );
    
    register_syscall(
        Syscall::new(
            0xC7,
            "NtOpenThreadToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Opens a thread token"),
    );
    
    register_syscall(
        Syscall::new(
            0xC8,
            "NtOpenProcessToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Opens a process token"),
    );
    
    register_syscall(
        Syscall::new(
            0xC9,
            "NtQueryInformationToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Queries token information"),
    );
    
    register_syscall(
        Syscall::new(
            0xCA,
            "NtSetInformationToken",
            Platform::Windows,
            SyscallCategory::Security,
        )
        .with_description("Sets token information"),
    );
    
    // Add more syscalls as needed...
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::syscalls::common::mapping::{get_syscall, get_syscalls_by_category};
    
    #[test]
    fn test_load_additional_syscalls() {
        // Load additional syscalls
        load_additional_syscalls();
        
        // Check that some syscalls are registered
        let syscall = get_syscall(Platform::Windows, 0x80); // NtCreateIoCompletion
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtCreateIoCompletion");
        
        let syscall = get_syscall(Platform::Windows, 0x90); // NtCreateMutant
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtCreateMutant");
        
        let syscall = get_syscall(Platform::Windows, 0xA0); // NtQuerySystemTime
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtQuerySystemTime");
        
        let syscall = get_syscall(Platform::Windows, 0xB0); // NtQuerySystemInformation
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtQuerySystemInformation");
        
        let syscall = get_syscall(Platform::Windows, 0xC0); // NtAccessCheck
        assert!(syscall.is_some());
        assert_eq!(syscall.unwrap().name, "NtAccessCheck");
    }
    
    #[test]
    fn test_syscall_categories() {
        // Load additional syscalls
        load_additional_syscalls();
        
        // Check syscalls by category
        let io_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::IO);
        assert!(!io_syscalls.is_empty());
        
        let sync_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Synchronization);
        assert!(!sync_syscalls.is_empty());
        
        let time_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Time);
        assert!(!time_syscalls.is_empty());
        
        let sysinfo_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::SystemInfo);
        assert!(!sysinfo_syscalls.is_empty());
        
        let security_syscalls = get_syscalls_by_category(Platform::Windows, SyscallCategory::Security);
        assert!(!security_syscalls.is_empty());
    }
} 