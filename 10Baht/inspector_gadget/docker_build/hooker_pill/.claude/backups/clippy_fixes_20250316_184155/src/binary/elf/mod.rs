/*!
 * ELF Binary Format Implementation
 * 
 * This module provides functionality for parsing and analyzing ELF (Executable and Linkable Format)
 * binaries, commonly used on Linux and Unix-like systems.
 */


use goblin::elf::{<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>};
use goblin::Object;


use crate::binary::{Binary, BinaryFormat, BinaryInfo, BinarySection, BinaryError, Result};

/// ELF binary implementation
pub struct ElfBinary {
    /// Path to the binary file
    path: Option<PathBuf>,
    /// Parsed ELF data
    elf: Elf<'static>,
    /// Memory-mapped binary data
    data: &'static [u8],
    /// Cached sections
    sections: HashMap<String, BinarySection>,
}

impl ElfBinary {
    /// Parse an ELF binary from the given data
    pub fn parse<P: AsRef<Path>>(path: P, data: &[u8]) -> Result<Self> {
        // Create a static reference to the data
        // This is safe because the data is memory-mapped and will live for the lifetime of the program
        let data = unsafe { std::mem::transmute(data) };
        
        // Parse the ELF binary
        let elf = match Object::parse(data) {
            Ok(Object::Elf(elf)) => elf,
            _ => return Err(BinaryError::ParseError("Not a valid ELF file".to_string())),
        };
        
        // Create the ELF binary
        let mut binary = ElfBinary {
            path: Some(path.as_ref().to_path_buf()),
            elf,
            data,
            sections: HashMap::new(),
        };
        
        // Parse sections
        binary.parse_sections()?;
        
        Ok(binary)
    }
    
    /// Parse sections from the ELF binary
    fn parse_sections(&mut self) -> Result<()> {
        for (idx, section) in self.elf.section_headers.iter().enumerate() {
            if let Some(name) = self.elf.shdr_strtab.get_at(section.sh_name) {
                let binary_section = self.create_binary_section(name, &section, idx)?;
                self.sections.insert(name.to_string(), binary_section);
            }
        }
        
        Ok(())
    }
    
    /// Create a BinarySection from an ELF section
    fn create_binary_section(&self, name: &str, section: &SectionHeader, idx: usize) -> Result<BinarySection> {
        // Determine section flags
        let is_executable = section.sh_flags & 0x4 != 0; // SHF_EXECINSTR
        let is_writable = section.sh_flags & 0x1 != 0;   // SHF_WRITE
        let is_code = is_executable;
        let is_data = section.sh_type == 1 || section.sh_type == 8; // SHT_PROGBITS or SHT_NOBITS
        
        // Extract section data if available
        let data = if section.sh_offset > 0 && section.sh_size > 0 && section.sh_offset as usize + section.sh_size as usize <= self.data.len() {
            let start = section.sh_offset as usize;
            let end = start + section.sh_size as usize;
            Some(self.data[start..end].to_vec())
        } else {
            None
        };
        
        Ok(BinarySection {
            name: name.to_string(),
            address: section.sh_addr,
            size: section.sh_size,
            is_executable,
            is_writable,
            is_code,
            is_data,
            data,
        })
    }
    
    /// Get the architecture string from the ELF header
    fn get_architecture(&self) -> String {
        match self.elf.header.e_machine {
            0x03 => "x86".to_string(),
            0x3E => "x86_64".to_string(),
            0x28 => "ARM".to_string(),
            0xB7 => "AArch64".to_string(),
            0x08 => "MIPS".to_string(),
            0x14 => "PowerPC".to_string(),
            0x15 => "PowerPC64".to_string(),
            0x32 => "IA-64".to_string(),
            0x2B => "SPARC".to_string(),
            0x2C => "SPARC64".to_string(),
            0x16 => "S390".to_string(),
            0xF3 => "RISC-V".to_string(),
            _ => format!("Unknown ({})", self.elf.header.e_machine),
        }
    }
}

impl Binary for ElfBinary {
    fn format(&self) -> BinaryFormat {
        BinaryFormat::Elf
    }
    
    fn info(&self) -> BinaryInfo {
        BinaryInfo {
            format: BinaryFormat::Elf,
            architecture: self.get_architecture(),
            is_64bit: self.elf.header.e_ident[4] == 2, // ELFCLASS64
            is_executable: self.elf.header.e_type == 2, // ET_EXEC
            is_library: self.elf.header.e_type == 3, // ET_DYN
            entry_point: self.elf.header.e_entry,
            path: self.path.as_ref().map(|p| p.to_string_lossy().to_string()),
        }
    }
    
    fn sections(&self) -> Vec<BinarySection> {
        self.sections.values().cloned().collect()
    }
    
    fn get_section(&self, name: &str) -> Option<BinarySection> {
        self.sections.get(name).cloned()
    }
    
    fn is_executable(&self) -> bool {
        self.elf.header.e_type == 2 // ET_EXEC
    }
    
    fn is_library(&self) -> bool {
        self.elf.header.e_type == 3 // ET_DYN
    }
    
    fn entry_point(&self) -> u64 {
        self.elf.header.e_entry
    }
    
    fn imports(&self) -> Vec<String> {
        let mut imports = Vec::new();
        
        // Get imports from dynamic symbols
        for sym in &self.elf.dynsyms {
            if sym.st_bind() == 1 && sym.is_import() { // STB_GLOBAL
                if let Some(name) = self.elf.dynstrtab.get_at(sym.st_name) {
                    imports.push(name.to_string());
                }
            }
        }
        
        imports
    }
    
    fn exports(&self) -> Vec<String> {
        let mut exports = Vec::new();
        
        // Get exports from dynamic symbols
        for sym in &self.elf.dynsyms {
            if sym.st_bind() == 1 && !sym.is_import() { // STB_GLOBAL
                if let Some(name) = self.elf.dynstrtab.get_at(sym.st_name) {
                    exports.push(name.to_string());
                }
            }
        }
        
        exports
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    
    
    #[test]
    #[cfg(target_os = "linux")]
    fn test_parse_elf_binary() {
        // This test will only run on Linux
        // Use a common Linux binary for testing
        let path = Path::new("/bin/ls");
        if path.exists() {
            let file = File::open(path).unwrap();
            let mmap = unsafe { MmapOptions::new().map(&file).unwrap() };
            
            let elf_binary = ElfBinary::parse(path, &mmap).unwrap();
            
            // Basic checks
            assert_eq!(elf_binary.format(), BinaryFormat::Elf);
            assert!(elf_binary.is_executable());
            assert!(!elf_binary.is_library());
            assert!(elf_binary.entry_point() > 0);
            
            // Check sections
            let sections = elf_binary.sections();
            assert!(!sections.is_empty());
            
            // Check for common sections
            assert!(elf_binary.get_section(".text").is_some());
            
            // Check imports and exports
            let imports = elf_binary.imports();
            assert!(!imports.is_empty());
        }
    }
} 