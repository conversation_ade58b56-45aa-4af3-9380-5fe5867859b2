//! Tracer module for syscall interception
//!
//! This module provides the tracer interface for syscall interception.

use crate::error::Result;
use crate::core::events::TraceEvent;


/// Syscall filter for tracing
#[derive(Debug, Clone)]
pub struct SyscallFilter {
    /// Syscall number
    pub syscall: u32,
    /// Process ID
    pub pid: Option<u32>,
    /// Command name
    pub comm: Option<String>,
}

impl fmt::Display for SyscallFilter {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SyscallFilter(syscall: {}", self.syscall)?;
        if let Some(pid) = self.pid {
            write!(f, ", pid: {}", pid)?;
        }
        if let Some(comm) = &self.comm {
            write!(f, ", comm: {}", comm)?;
        }
        write!(f, ")")
    }
}

/// Tracer interface
pub trait Tracer: Send + Sync {
    /// Start tracing
    fn start(&mut self) -> Result<()>;
    
    /// Stop tracing
    fn stop(&mut self) -> Result<()>;
    
    /// Add a filter
    fn add_filter(&mut self, filter: SyscallFilter) -> Result<()>;
    
    /// Remove a filter
    fn remove_filter(&mut self, filter: SyscallFilter) -> Result<()>;
    
    /// Get the next event
    fn next_event(&mut self) -> Result<Option<TraceEvent>>;
    
    /// Get the next batch of events
    fn next_batch(&mut self, max_events: usize) -> Result<Vec<TraceEvent>>;

    /// Intercept syscalls
    async fn intercept_syscalls(&mut self, filter: SyscallFilter) -> Result<()>;

    /// Collect events
    async fn collect_events(&self) -> Result<Vec<TraceEvent>>;

    /// Intercept syscalls
    async fn intercept_syscalls(&mut self, filter: SyscallFilter) -> Result<()>;

    /// Collect events
    async fn collect_events(&self) -> Result<Vec<TraceEvent>>;

    /// Intercept syscalls
    async fn intercept_syscalls(&mut self, filter: SyscallFilter) -> Result<()>;

    /// Collect events
    async fn collect_events(&self) -> Result<Vec<TraceEvent>>;

    /// Intercept syscalls
    async fn intercept_syscalls(&mut self, filter: SyscallFilter) -> Result<()>;

    /// Collect events
    async fn collect_events(&self) -> Result<Vec<TraceEvent>>;

    /// Intercept syscalls
    async fn intercept_syscalls(&mut self, filter: SyscallFilter) -> Result<()>;

    /// Collect events
    async fn collect_events(&self) -> Result<Vec<TraceEvent>>;

    /// Intercept syscalls
    async fn intercept_syscalls(&mut self, filter: SyscallFilter) -> Result<()>;

    /// Collect events
    async fn collect_events(&self) -> Result<Vec<TraceEvent>>;

    /// Intercept syscalls
    async fn intercept_syscalls(&mut self, filter: SyscallFilter) -> Result<()>;

    /// Collect events
    async fn collect_events(&self) -> Result<Vec<TraceEvent>>;
}
