/*!
 * Statistics for binary analysis
 * 
 * This module provides functionality for calculating statistics from binary analysis.
 */

use std::collections::{HashMap, HashSet};
use crate::core::{TraceEvent, EventType};
use crate::syscalls::{Syscall, SyscallCategory};

/// Statistics for binary analysis
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct BinaryStatistics {
    /// Total number of events
    pub total_events: usize,
    /// Number of syscall events
    pub syscall_events: usize,
    /// Number of file operation events
    pub file_events: usize,
    /// Number of network events
    pub network_events: usize,
    /// Number of process events
    pub process_events: usize,
    /// Number of memory events
    pub memory_events: usize,
    /// Number of custom events
    pub custom_events: usize,
    /// Unique syscalls
    pub unique_syscalls: HashSet<String>,
    /// Unique file paths
    pub unique_file_paths: HashSet<String>,
    /// Unique network destinations
    pub unique_network_destinations: HashSet<String>,
    /// Unique processes
    pub unique_processes: HashSet<String>,
    /// Syscall frequency
    pub syscall_frequency: HashMap<String, usize>,
    /// File operation frequency
    pub file_operation_frequency: HashMap<String, usize>,
    /// Network protocol frequency
    pub network_protocol_frequency: HashMap<String, usize>,
    /// Process execution frequency
    pub process_execution_frequency: HashMap<String, usize>,
    /// Syscall category distribution
    pub syscall_category_distribution: HashMap<SyscallCategory, usize>,
    /// Duration of the analysis in milliseconds
    pub duration_ms: u64,
}

impl BinaryStatistics {
    /// Create a new binary statistics instance
    pub fn new() -> Self {
        BinaryStatistics {
            total_events: 0,
            syscall_events: 0,
            file_events: 0,
            network_events: 0,
            process_events: 0,
            memory_events: 0,
            custom_events: 0,
            unique_syscalls: HashSet::new(),
            unique_file_paths: HashSet::new(),
            unique_network_destinations: HashSet::new(),
            unique_processes: HashSet::new(),
            syscall_frequency: HashMap::new(),
            file_operation_frequency: HashMap::new(),
            network_protocol_frequency: HashMap::new(),
            process_execution_frequency: HashMap::new(),
            syscall_category_distribution: HashMap::new(),
            duration_ms: 0,
        }
    }
    
    /// Calculate statistics from events
    pub fn calculate(&mut self, events: &[TraceEvent]) {
        self.total_events = events.len();
        
        // Reset counters
        self.syscall_events = 0;
        self.file_events = 0;
        self.network_events = 0;
        self.process_events = 0;
        self.memory_events = 0;
        self.custom_events = 0;
        
        self.unique_syscalls.clear();
        self.unique_file_paths.clear();
        self.unique_network_destinations.clear();
        self.unique_processes.clear();
        
        self.syscall_frequency.clear();
        self.file_operation_frequency.clear();
        self.network_protocol_frequency.clear();
        self.process_execution_frequency.clear();
        self.syscall_category_distribution.clear();
        
        // Calculate duration
        if !events.is_empty() {
            let first_timestamp = events.first().unwrap().timestamp;
            let last_timestamp = events.last().unwrap().timestamp;
            self.duration_ms = last_timestamp - first_timestamp;
        }
        
        // Process events
        for event in events {
            match event.event_type {
                EventType::Syscall => {
                    self.syscall_events += 1;
                    
                    if let crate::core::EventData::Syscall(syscall_event) = &event.data {
                        let syscall_name = syscall_event.syscall.name();
                        self.unique_syscalls.insert(syscall_name.clone());
                        *self.syscall_frequency.entry(syscall_name).or_insert(0) += 1;
                        
                        let category = syscall_event.syscall.category();
                        *self.syscall_category_distribution.entry(category).or_insert(0) += 1;
                    }
                },
                EventType::FileOperation => {
                    self.file_events += 1;
                    
                    if let crate::core::EventData::FileOperation { path, operation, .. } = &event.data {
                        self.unique_file_paths.insert(path.clone());
                        *self.file_operation_frequency.entry(operation.clone()).or_insert(0) += 1;
                    }
                },
                EventType::NetworkActivity => {
                    self.network_events += 1;
                    
                    if let crate::core::EventData::NetworkActivity { destination, protocol, .. } = &event.data {
                        self.unique_network_destinations.insert(destination.clone());
                        *self.network_protocol_frequency.entry(protocol.clone()).or_insert(0) += 1;
                    }
                },
                EventType::ProcessExecution => {
                    self.process_events += 1;
                    
                    if let crate::core::EventData::ProcessExecution { command, .. } = &event.data {
                        self.unique_processes.insert(command.clone());
                        *self.process_execution_frequency.entry(command.clone()).or_insert(0) += 1;
                    }
                },
                EventType::MemoryOperation => {
                    self.memory_events += 1;
                },
                EventType::Custom(_) => {
                    self.custom_events += 1;
                },
            }
        }
    }
    
    /// Get the events per second
    pub fn events_per_second(&self) -> f64 {
        if self.duration_ms > 0 {
            (self.total_events as f64) / (self.duration_ms as f64 / 1000.0)
        } else {
            0.0
        }
    }
    
    /// Get the top N syscalls by frequency
    pub fn top_syscalls(&self, n: usize) -> Vec<(String, usize)> {
        let mut syscalls: Vec<(String, usize)> = self.syscall_frequency.clone().into_iter().collect();
        syscalls.sort_by(|a, b| b.1.cmp(&a.1));
        syscalls.truncate(n);
        syscalls
    }
    
    /// Get the top N file operations by frequency
    pub fn top_file_operations(&self, n: usize) -> Vec<(String, usize)> {
        let mut operations: Vec<(String, usize)> = self.file_operation_frequency.clone().into_iter().collect();
        operations.sort_by(|a, b| b.1.cmp(&a.1));
        operations.truncate(n);
        operations
    }
    
    /// Get the top N network protocols by frequency
    pub fn top_network_protocols(&self, n: usize) -> Vec<(String, usize)> {
        let mut protocols: Vec<(String, usize)> = self.network_protocol_frequency.clone().into_iter().collect();
        protocols.sort_by(|a, b| b.1.cmp(&a.1));
        protocols.truncate(n);
        protocols
    }
    
    /// Get the top N processes by frequency
    pub fn top_processes(&self, n: usize) -> Vec<(String, usize)> {
        let mut processes: Vec<(String, usize)> = self.process_execution_frequency.clone().into_iter().collect();
        processes.sort_by(|a, b| b.1.cmp(&a.1));
        processes.truncate(n);
        processes
    }
    
    /// Get the syscall category distribution as percentages
    pub fn syscall_category_percentages(&self) -> HashMap<SyscallCategory, f64> {
        let mut percentages = HashMap::new();
        
        if self.syscall_events > 0 {
            for (category, count) in &self.syscall_category_distribution {
                let percentage = (*count as f64) / (self.syscall_events as f64) * 100.0;
                percentages.insert(*category, percentage);
            }
        }
        
        percentages
    }
    
    /// Generate a summary of the statistics
    pub fn summary(&self) -> String {
        let mut summary = String::new();
        
        summary.push_str(&format!("Total events: {}\n", self.total_events));
        summary.push_str(&format!("Duration: {:.2} seconds\n", self.duration_ms as f64 / 1000.0));
        summary.push_str(&format!("Events per second: {:.2}\n", self.events_per_second()));
        summary.push_str("\n");
        
        summary.push_str(&format!("Syscall events: {} ({:.2}%)\n", self.syscall_events, self.percentage(self.syscall_events)));
        summary.push_str(&format!("File events: {} ({:.2}%)\n", self.file_events, self.percentage(self.file_events)));
        summary.push_str(&format!("Network events: {} ({:.2}%)\n", self.network_events, self.percentage(self.network_events)));
        summary.push_str(&format!("Process events: {} ({:.2}%)\n", self.process_events, self.percentage(self.process_events)));
        summary.push_str(&format!("Memory events: {} ({:.2}%)\n", self.memory_events, self.percentage(self.memory_events)));
        summary.push_str(&format!("Custom events: {} ({:.2}%)\n", self.custom_events, self.percentage(self.custom_events)));
        summary.push_str("\n");
        
        summary.push_str(&format!("Unique syscalls: {}\n", self.unique_syscalls.len()));
        summary.push_str(&format!("Unique file paths: {}\n", self.unique_file_paths.len()));
        summary.push_str(&format!("Unique network destinations: {}\n", self.unique_network_destinations.len()));
        summary.push_str(&format!("Unique processes: {}\n", self.unique_processes.len()));
        summary.push_str("\n");
        
        if !self.syscall_frequency.is_empty() {
            summary.push_str("Top 5 syscalls:\n");
            for (i, (syscall, count)) in self.top_syscalls(5).iter().enumerate() {
                summary.push_str(&format!("{}. {} - {} calls\n", i + 1, syscall, count));
            }
            summary.push_str("\n");
        }
        
        if !self.file_operation_frequency.is_empty() {
            summary.push_str("Top file operations:\n");
            for (i, (operation, count)) in self.top_file_operations(3).iter().enumerate() {
                summary.push_str(&format!("{}. {} - {} operations\n", i + 1, operation, count));
            }
            summary.push_str("\n");
        }
        
        if !self.network_protocol_frequency.is_empty() {
            summary.push_str("Network protocols:\n");
            for (i, (protocol, count)) in self.top_network_protocols(3).iter().enumerate() {
                summary.push_str(&format!("{}. {} - {} connections\n", i + 1, protocol, count));
            }
            summary.push_str("\n");
        }
        
        if !self.syscall_category_distribution.is_empty() {
            summary.push_str("Syscall category distribution:\n");
            let percentages = self.syscall_category_percentages();
            
            let mut categories: Vec<(SyscallCategory, f64)> = percentages.into_iter().collect();
            categories.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
            
            for (category, percentage) in categories {
                summary.push_str(&format!("{:?}: {:.2}%\n", category, percentage));
            }
        }
        
        summary
    }
    
    /// Calculate the percentage of a count relative to the total events
    fn percentage(&self, count: usize) -> f64 {
        if self.total_events > 0 {
            (count as f64) / (self.total_events as f64) * 100.0
        } else {
            0.0
        }
    }
} 