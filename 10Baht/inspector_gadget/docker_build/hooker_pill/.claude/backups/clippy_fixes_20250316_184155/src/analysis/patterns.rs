/*!
 * Behavior patterns for binary analysis
 * 
 * This module defines patterns of behavior that can be detected in binary execution.
 */


use crate::core::{TraceEvent, EventType};
use crate::syscalls::{Syscall, SyscallCategory};

/// Pattern type for behavior analysis
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq)]
pub enum PatternType {
    /// File access pattern
    FileAccess,
    /// Network activity pattern
    NetworkActivity,
    /// Process execution pattern
    ProcessExecution,
    /// Memory operation pattern
    MemoryOperation,
    /// Syscall sequence pattern
    SyscallSequence,
    /// Custom pattern
    Custom(String),
}

/// Pattern severity level
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, PartialOrd, Ord)]
pub enum Severity {
    /// Informational severity
    Info,
    /// Low severity
    Low,
    /// Medium severity
    Medium,
    /// High severity
    High,
    /// Critical severity
    Critical,
}

/// Behavior pattern for binary analysis
#[derive(Debug, <PERSON>lone)]
pub struct BehaviorPattern {
    /// Pattern name
    pub name: String,
    /// Pattern description
    pub description: String,
    /// Pattern type
    pub pattern_type: PatternType,
    /// Pattern severity
    pub severity: Severity,
    /// MITRE ATT&CK technique ID (if applicable)
    pub mitre_technique_id: Option<String>,
}

impl BehaviorPattern {
    /// Create a new behavior pattern
    pub fn new(
        name: impl Into<String>,
        description: impl Into<String>,
        pattern_type: PatternType,
        severity: Severity,
    ) -> Self {
        BehaviorPattern {
            name: name.into(),
            description: description.into(),
            pattern_type,
            severity,
            mitre_technique_id: None,
        }
    }
    
    /// Set the MITRE ATT&CK technique ID
    pub fn with_mitre_technique_id(mut self, id: impl Into<String>) -> Self {
        self.mitre_technique_id = Some(id.into());
        self
    }
}

/// Pattern matcher for binary analysis
pub trait PatternMatcher {
    /// Check if the pattern matches the events
    fn matches(&self, events: &[TraceEvent]) -> bool;
    
    /// Get the pattern
    fn pattern(&self) -> &BehaviorPattern;
}

/// Syscall sequence pattern matcher
pub struct SyscallSequenceMatcher {
    /// Behavior pattern
    pattern: BehaviorPattern,
    /// Syscall sequence to match
    sequence: Vec<Syscall>,
}

impl SyscallSequenceMatcher {
    /// Create a new syscall sequence matcher
    pub fn new(
        name: impl Into<String>,
        description: impl Into<String>,
        severity: Severity,
        sequence: Vec<Syscall>,
    ) -> Self {
        SyscallSequenceMatcher {
            pattern: BehaviorPattern::new(
                name,
                description,
                PatternType::SyscallSequence,
                severity,
            ),
            sequence,
        }
    }
}

impl PatternMatcher for SyscallSequenceMatcher {
    fn matches(&self, events: &[TraceEvent]) -> bool {
        if events.len() < self.sequence.len() {
            return false;
        }
        
        for i in 0..=(events.len() - self.sequence.len()) {
            let mut matches = true;
            
            for (j, syscall) in self.sequence.iter().enumerate() {
                if let EventType::Syscall = events[i + j].event_type {
                    if let crate::core::EventData::Syscall(syscall_event) = &events[i + j].data {
                        if &syscall_event.syscall != syscall {
                            matches = false;
                            break;
                        }
                    } else {
                        matches = false;
                        break;
                    }
                } else {
                    matches = false;
                    break;
                }
            }
            
            if matches {
                return true;
            }
        }
        
        false
    }
    
    fn pattern(&self) -> &BehaviorPattern {
        &self.pattern
    }
}

/// File access pattern matcher
pub struct FileAccessMatcher {
    /// Behavior pattern
    pattern: BehaviorPattern,
    /// File paths to match
    paths: Vec<String>,
}

impl FileAccessMatcher {
    /// Create a new file access matcher
    pub fn new(
        name: impl Into<String>,
        description: impl Into<String>,
        severity: Severity,
        paths: Vec<String>,
    ) -> Self {
        FileAccessMatcher {
            pattern: BehaviorPattern::new(
                name,
                description,
                PatternType::FileAccess,
                severity,
            ),
            paths,
        }
    }
}

impl PatternMatcher for FileAccessMatcher {
    fn matches(&self, events: &[TraceEvent]) -> bool {
        for event in events {
            if let EventType::FileOperation = event.event_type {
                if let crate::core::EventData::FileOperation { path, .. } = &event.data {
                    for pattern_path in &self.paths {
                        if path.contains(pattern_path) {
                            return true;
                        }
                    }
                }
            }
        }
        
        false
    }
    
    fn pattern(&self) -> &BehaviorPattern {
        &self.pattern
    }
}

/// Network activity pattern matcher
pub struct NetworkActivityMatcher {
    /// Behavior pattern
    pattern: BehaviorPattern,
    /// Destination addresses to match
    destinations: Vec<String>,
}

impl NetworkActivityMatcher {
    /// Create a new network activity matcher
    pub fn new(
        name: impl Into<String>,
        description: impl Into<String>,
        severity: Severity,
        destinations: Vec<String>,
    ) -> Self {
        NetworkActivityMatcher {
            pattern: BehaviorPattern::new(
                name,
                description,
                PatternType::NetworkActivity,
                severity,
            ),
            destinations,
        }
    }
}

impl PatternMatcher for NetworkActivityMatcher {
    fn matches(&self, events: &[TraceEvent]) -> bool {
        for event in events {
            if let EventType::NetworkActivity = event.event_type {
                if let crate::core::EventData::NetworkActivity { destination, .. } = &event.data {
                    for pattern_dest in &self.destinations {
                        if destination.contains(pattern_dest) {
                            return true;
                        }
                    }
                }
            }
        }
        
        false
    }
    
    fn pattern(&self) -> &BehaviorPattern {
        &self.pattern
    }
}

/// Process execution pattern matcher
pub struct ProcessExecutionMatcher {
    /// Behavior pattern
    pattern: BehaviorPattern,
    /// Command patterns to match
    commands: Vec<String>,
}

impl ProcessExecutionMatcher {
    /// Create a new process execution matcher
    pub fn new(
        name: impl Into<String>,
        description: impl Into<String>,
        severity: Severity,
        commands: Vec<String>,
    ) -> Self {
        ProcessExecutionMatcher {
            pattern: BehaviorPattern::new(
                name,
                description,
                PatternType::ProcessExecution,
                severity,
            ),
            commands,
        }
    }
}

impl PatternMatcher for ProcessExecutionMatcher {
    fn matches(&self, events: &[TraceEvent]) -> bool {
        for event in events {
            if let EventType::ProcessExecution = event.event_type {
                if let crate::core::EventData::ProcessExecution { command, .. } = &event.data {
                    for pattern_cmd in &self.commands {
                        if command.contains(pattern_cmd) {
                            return true;
                        }
                    }
                }
            }
        }
        
        false
    }
    
    fn pattern(&self) -> &BehaviorPattern {
        &self.pattern
    }
} 