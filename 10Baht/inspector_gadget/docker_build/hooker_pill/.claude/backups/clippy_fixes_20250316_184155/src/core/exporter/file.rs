//! File exporter implementation for the event collection system.
//! 
//! This module provides functionality to export events to files,
//! with support for rotation, compression, and various output formats.

use async_trait::async_trait;
use chrono::{DateTime, Utc};


use std::fs::{File, OpenOptions};
use std::io::{self, BufWriter, Write};

use std::sync::{Arc, Mutex};


use crate::core::events::Event;

/// Configuration for the file exporter.
#[derive(Debug, Clone)]
pub struct FileExporterConfig {
    /// The path to the output file
    pub path: PathBuf,
    /// The format to use for the output
    pub format: FileFormat,
    /// Whether to append to an existing file
    pub append: bool,
    /// Maximum file size in bytes before rotation (0 = no limit)
    pub max_size: u64,
    /// Maximum number of rotated files to keep (0 = keep all)
    pub max_files: usize,
    /// Whether to compress rotated files
    pub compress: bool,
    /// Flush interval in milliseconds (0 = flush on every write)
    pub flush_interval_ms: u64,
}

impl Default for FileExporterConfig {
    fn default() -> Self {
        Self {
            path: PathBuf::from("events.log"),
            format: FileFormat::Json,
            append: true,
            max_size: 100 * 1024 * 1024, // 100 MB
            max_files: 5,
            compress: true,
            flush_interval_ms: 1000, // 1 second
        }
    }
}

/// Output format for the file exporter.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FileFormat {
    /// JSON format (one event per line)
    Json,
    /// CSV format
    Csv,
    /// Plain text format
    Text,
}

/// File exporter for events.
pub struct FileExporter {
    /// Configuration for the exporter
    config: FileExporterConfig,
    /// File writer
    writer: Arc<Mutex<BufWriter<File>>>,
    /// Current file size
    current_size: Arc<Mutex<u64>>,
    /// Last flush time
    last_flush: Arc<Mutex<SystemTime>>,
}

#[async_trait]
impl super::EventExporter for FileExporter {
    async fn export_event(&self, event: Event) -> Result<(), String> {
        let serialized = self.serialize_event(&event)?;
        self.write_to_file(serialized).await
    }

    async fn export_events(&self, events: Vec<Event>) -> Result<(), String> {
        if events.is_empty() {
            return Ok(());
        }

        let mut serialized = String::new();
        for event in events {
            serialized.push_str(&self.serialize_event(&event)?);
        }

        self.write_to_file(serialized).await
    }

    async fn flush(&self) -> Result<(), String> {
        let mut writer = self.writer.lock().map_err(|e| format!("Failed to lock writer: {}", e))?;
        writer.flush().map_err(|e| format!("Failed to flush file: {}", e))?;
        
        // Update last flush time
        let mut last_flush = self.last_flush.lock().map_err(|e| format!("Failed to lock last_flush: {}", e))?;
        *last_flush = SystemTime::now();
        
        Ok(())
    }
}

impl FileExporter {
    /// Create a new file exporter with the given configuration.
    pub fn new(config: FileExporterConfig) -> Result<Self, String> {
        // Create parent directories if they don't exist
        if let Some(parent) = config.path.parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| format!("Failed to create directory {}: {}", parent.display(), e))?;
        }

        // Open the file
        let file = OpenOptions::new()
            .write(true)
            .create(true)
            .append(config.append)
            .truncate(!config.append)
            .open(&config.path)
            .map_err(|e| format!("Failed to open file {}: {}", config.path.display(), e))?;

        // Get the current file size
        let metadata = file.metadata()
            .map_err(|e| format!("Failed to get file metadata: {}", e))?;
        let current_size = metadata.len();

        let writer = BufWriter::new(file);

        let exporter = Self {
            config,
            writer: Arc::new(Mutex::new(writer)),
            current_size: Arc::new(Mutex::new(current_size)),
            last_flush: Arc::new(Mutex::new(SystemTime::now())),
        };

        // Start the flush timer if needed
        if exporter.config.flush_interval_ms > 0 {
            exporter.start_flush_timer();
        }

        Ok(exporter)
    }

    /// Serialize an event to a string based on the configured format.
    fn serialize_event(&self, event: &Event) -> Result<String, String> {
        match self.config.format {
            FileFormat::Json => {
                let json = serde_json::to_string(event)
                    .map_err(|e| format!("Failed to serialize event to JSON: {}", e))?;
                Ok(format!("{}\n", json))
            },
            FileFormat::Csv => {
                // Basic CSV format with common fields
                let timestamp = DateTime::<Utc>::from(
                    SystemTime::UNIX_EPOCH + Duration::from_secs(event.timestamp)
                ).format("%Y-%m-%d %H:%M:%S%.3f");
                
                let csv = format!(
                    "{},{},{},{},{},{},{},{},{}\n",
                    event.id,
                    timestamp,
                    event.event_type,
                    event.severity,
                    event.source,
                    event.process_id.unwrap_or(0),
                    event.thread_id.unwrap_or(0),
                    event.syscall_name.as_deref().unwrap_or(""),
                    event.return_value.unwrap_or(0)
                );
                Ok(csv)
            },
            FileFormat::Text => {
                // Human-readable text format
                let timestamp = DateTime::<Utc>::from(
                    SystemTime::UNIX_EPOCH + Duration::from_secs(event.timestamp)
                ).format("%Y-%m-%d %H:%M:%S%.3f");
                
                let text = format!(
                    "[{}] [{}] [{}] [{}] Process: {} Thread: {} Syscall: {} Return: {}\n",
                    timestamp,
                    event.event_type,
                    event.severity,
                    event.source,
                    event.process_id.unwrap_or(0),
                    event.thread_id.unwrap_or(0),
                    event.syscall_name.as_deref().unwrap_or(""),
                    event.return_value.unwrap_or(0)
                );
                Ok(text)
            },
        }
    }

    /// Write data to the file, handling rotation if needed.
    async fn write_to_file(&self, data: String) -> Result<(), String> {
        let data_len = data.len() as u64;
        
        // Check if we need to rotate the file
        if self.config.max_size > 0 {
            let current_size = {
                let size = self.current_size.lock().map_err(|e| format!("Failed to lock current_size: {}", e))?;
                *size
            };
            
            if current_size + data_len > self.config.max_size {
                self.rotate_file().map_err(|e| format!("Failed to rotate file: {}", e))?;
            }
        }
        
        // Write the data
        let mut writer = self.writer.lock().map_err(|e| format!("Failed to lock writer: {}", e))?;
        writer.write_all(data.as_bytes()).map_err(|e| format!("Failed to write to file: {}", e))?;
        
        // Update the current size
        let mut size = self.current_size.lock().map_err(|e| format!("Failed to lock current_size: {}", e))?;
        *size += data_len;
        
        // Flush if needed
        if self.config.flush_interval_ms == 0 {
            writer.flush().map_err(|e| format!("Failed to flush file: {}", e))?;
            
            // Update last flush time
            let mut last_flush = self.last_flush.lock().map_err(|e| format!("Failed to lock last_flush: {}", e))?;
            *last_flush = SystemTime::now();
        } else {
            // Check if we need to flush based on the interval
            let last_flush = {
                let lf = self.last_flush.lock().map_err(|e| format!("Failed to lock last_flush: {}", e))?;
                *lf
            };
            
            let elapsed = SystemTime::now().duration_since(last_flush)
                .map_err(|e| format!("Failed to calculate elapsed time: {}", e))?;
                
            if elapsed.as_millis() as u64 >= self.config.flush_interval_ms {
                writer.flush().map_err(|e| format!("Failed to flush file: {}", e))?;
                
                // Update last flush time
                let mut last_flush = self.last_flush.lock().map_err(|e| format!("Failed to lock last_flush: {}", e))?;
                *last_flush = SystemTime::now();
            }
        }
        
        Ok(())
    }

    /// Rotate the current file.
    fn rotate_file(&self) -> io::Result<()> {
        // Flush and close the current file
        {
            let mut writer = self.writer.lock().unwrap();
            writer.flush()?;
        }
        
        // Get the base path and extension
        let path = &self.config.path;
        let stem = path.file_stem().unwrap_or_default().to_string_lossy().to_string();
        let ext = path.extension().unwrap_or_default().to_string_lossy().to_string();
        
        // Rotate existing files
        if self.config.max_files > 0 {
            // Remove the oldest file if we've reached the limit
            let oldest_index = self.config.max_files;
            let oldest_path = if self.config.compress {
                format!("{}.{}.gz", stem, oldest_index)
            } else {
                format!("{}.{}.{}", stem, oldest_index, ext)
            };
            let _ = std::fs::remove_file(Path::new(&oldest_path)); // Ignore errors
            
            // Shift existing files
            for i in (1..self.config.max_files).rev() {
                let src_path = if self.config.compress {
                    format!("{}.{}.gz", stem, i)
                } else {
                    format!("{}.{}.{}", stem, i, ext)
                };
                
                let dst_path = if self.config.compress {
                    format!("{}.{}.gz", stem, i + 1)
                } else {
                    format!("{}.{}.{}", stem, i + 1, ext)
                };
                
                let _ = std::fs::rename(Path::new(&src_path), Path::new(&dst_path)); // Ignore errors
            }
        }
        
        // Rename the current file
        let new_path = if self.config.compress {
            format!("{}.1.gz", stem)
        } else {
            format!("{}.1.{}", stem, ext)
        };
        
        // Rename the current file
        std::fs::rename(path, Path::new(&new_path))?;
        
        // Compress the file if needed
        if self.config.compress {
            // This would be implemented with a compression library
            // For simplicity, we'll just log that we would compress the file
            info!("Would compress file {} to {}.gz", new_path, new_path);
        }
        
        // Open a new file
        let file = OpenOptions::new()
            .write(true)
            .create(true)
            .truncate(true)
            .open(path)?;
            
        // Replace the writer
        {
            let mut writer_lock = self.writer.lock().unwrap();
            *writer_lock = BufWriter::new(file);
        }
        
        // Reset the current size
        {
            let mut size = self.current_size.lock().unwrap();
            *size = 0;
        }
        
        info!("Rotated log file to {}", new_path);
        
        Ok(())
    }

    /// Start a background task to periodically flush the file.
    fn start_flush_timer(&self) {
        // Clone the necessary fields for the background task
        let writer = Arc::clone(&self.writer);
        let last_flush = Arc::clone(&self.last_flush);
        let interval = Duration::from_millis(self.config.flush_interval_ms);
        
        // Spawn a background task to flush the file periodically
        std::thread::spawn(move || {
            loop {
                std::thread::sleep(interval);
                
                // Try to flush the file
                let mut writer_guard = match writer.lock() {
                    Ok(guard) => guard,
                    Err(e) => {
                        error!("Failed to lock writer for periodic flush: {}", e);
                        continue;
                    }
                };
                
                if let Err(e) = writer_guard.flush() {
                    error!("Failed to flush file: {}", e);
                } else {
                    // Update last flush time
                    if let Ok(mut last_flush_guard) = last_flush.lock() {
                        *last_flush_guard = SystemTime::now();
                    }
                }
            }
        });
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{Event, EventId, EventType, Severity};
    use std::fs;
    use std::io::Read;
    use tempfile::tempdir;
    
    fn create_test_event() -> Event {
        Event {
            id: EventId::new(),
            timestamp: SystemTime::now()
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            event_type: EventType::Syscall,
            severity: Severity::Info,
            source: "test".to_string(),
            process_id: Some(1234),
            thread_id: Some(5678),
            syscall_number: Some(1),
            syscall_name: Some("write".to_string()),
            parameters: Some(json!({"fd": 1, "buf": "Hello", "count": 5})),
            return_value: Some(5),
            duration_ns: Some(1000),
            tags: vec!["test".to_string()],
            metadata: None,
        }
    }
    
    #[test]
    fn test_file_exporter_new() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.log");
        
        let config = FileExporterConfig {
            path: path.clone(),
            ..Default::default()
        };
        
        let exporter = FileExporter::new(config);
        assert!(exporter.is_ok());
        
        // Check that the file was created
        assert!(path.exists());
    }
    
    #[test]
    fn test_serialize_event_json() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.json");
        
        let config = FileExporterConfig {
            path,
            format: FileFormat::Json,
            ..Default::default()
        };
        
        let exporter = FileExporter::new(config).unwrap();
        let event = create_test_event();
        
        let serialized = exporter.serialize_event(&event).unwrap();
        assert!(serialized.starts_with("{"));
        assert!(serialized.ends_with("}\n"));
    }
    
    #[test]
    fn test_serialize_event_csv() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.csv");
        
        let config = FileExporterConfig {
            path,
            format: FileFormat::Csv,
            ..Default::default()
        };
        
        let exporter = FileExporter::new(config).unwrap();
        let event = create_test_event();
        
        let serialized = exporter.serialize_event(&event).unwrap();
        let parts: Vec<&str> = serialized.trim().split(',').collect();
        assert_eq!(parts.len(), 9);
    }
    
    #[test]
    fn test_serialize_event_text() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.txt");
        
        let config = FileExporterConfig {
            path,
            format: FileFormat::Text,
            ..Default::default()
        };
        
        let exporter = FileExporter::new(config).unwrap();
        let event = create_test_event();
        
        let serialized = exporter.serialize_event(&event).unwrap();
        assert!(serialized.contains("Syscall"));
        assert!(serialized.contains("write"));
    }
    
    #[tokio::test]
    async fn test_export_event() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.log");
        
        let config = FileExporterConfig {
            path: path.clone(),
            format: FileFormat::Json,
            flush_interval_ms: 0, // Flush immediately
            ..Default::default()
        };
        
        let exporter = FileExporter::new(config).unwrap();
        let event = create_test_event();
        
        let result = exporter.export_event(event).await;
        assert!(result.is_ok());
        
        // Check that the file contains the event
        let mut file = File::open(path).unwrap();
        let mut contents = String::new();
        file.read_to_string(&mut contents).unwrap();
        
        assert!(!contents.is_empty());
        assert!(contents.contains("write"));
    }
    
    #[tokio::test]
    async fn test_export_events() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.log");
        
        let config = FileExporterConfig {
            path: path.clone(),
            format: FileFormat::Json,
            flush_interval_ms: 0, // Flush immediately
            ..Default::default()
        };
        
        let exporter = FileExporter::new(config).unwrap();
        let events = vec![create_test_event(), create_test_event()];
        
        let result = exporter.export_events(events).await;
        assert!(result.is_ok());
        
        // Check that the file contains both events
        let mut file = File::open(path).unwrap();
        let mut contents = String::new();
        file.read_to_string(&mut contents).unwrap();
        
        let lines: Vec<&str> = contents.lines().collect();
        assert_eq!(lines.len(), 2);
    }
    
    #[tokio::test]
    async fn test_file_rotation() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.log");
        
        let config = FileExporterConfig {
            path: path.clone(),
            format: FileFormat::Json,
            flush_interval_ms: 0, // Flush immediately
            max_size: 10, // Very small to trigger rotation
            max_files: 3,
            compress: false,
            ..Default::default()
        };
        
        let exporter = FileExporter::new(config).unwrap();
        
        // Export enough events to trigger rotation multiple times
        for _ in 0..5 {
            let event = create_test_event();
            let _ = exporter.export_event(event).await;
        }
        
        // Check that the rotated files exist
        let stem = path.file_stem().unwrap().to_string_lossy();
        let ext = path.extension().unwrap().to_string_lossy();
        
        let rotated_path1 = dir.path().join(format!("{}.1.{}", stem, ext));
        let rotated_path2 = dir.path().join(format!("{}.2.{}", stem, ext));
        let rotated_path3 = dir.path().join(format!("{}.3.{}", stem, ext));
        
        assert!(path.exists());
        assert!(rotated_path1.exists() || rotated_path2.exists() || rotated_path3.exists());
    }
    
    #[tokio::test]
    async fn test_flush() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.log");
        
        let config = FileExporterConfig {
            path: path.clone(),
            format: FileFormat::Json,
            flush_interval_ms: 1000, // Don't flush immediately
            ..Default::default()
        };
        
        let exporter = FileExporter::new(config).unwrap();
        let event = create_test_event();
        
        // Export an event (won't flush immediately)
        let result = exporter.export_event(event).await;
        assert!(result.is_ok());
        
        // Manually flush
        let result = exporter.flush().await;
        assert!(result.is_ok());
        
        // Check that the file contains the event
        let mut file = File::open(path).unwrap();
        let mut contents = String::new();
        file.read_to_string(&mut contents).unwrap();
        
        assert!(!contents.is_empty());
    }
} 