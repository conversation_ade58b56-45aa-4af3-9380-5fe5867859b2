//! Advanced Correlation Engine for Inspector Gadget
//!
//! This module provides the core functionality for correlating events across different sources
//! and time periods to detect complex security threats. It includes pattern matching, time-based
//! correlation, a rule engine, and visualization support.

pub mod pattern;
pub mod time_window;
pub mod rule;
pub mod visualization;

use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use std::error::Error;


/// Error type for correlation engine operations
#[derive(Debug)]
pub enum CorrelationError {
    /// Error in pattern matching
    PatternError(String),
    /// Error in time window management
    TimeWindowError(String),
    /// Error in rule evaluation
    RuleError(String),
    /// Error in visualization
    VisualizationError(String),
    /// General error
    GeneralError(String),
}

impl fmt::Display for CorrelationError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CorrelationError::PatternError(msg) => write!(f, "Pattern error: {}", msg),
            CorrelationError::TimeWindowError(msg) => write!(f, "Time window error: {}", msg),
            CorrelationError::RuleError(msg) => write!(f, "Rule error: {}", msg),
            CorrelationError::VisualizationError(msg) => write!(f, "Visualization error: {}", msg),
            CorrelationError::GeneralError(msg) => write!(f, "General error: {}", msg),
        }
    }
}

impl Error for CorrelationError {}

/// Result type for correlation engine operations
pub type CorrelationResult<T> = Result<T, CorrelationError>;

/// Main correlation engine that coordinates pattern matching, time windows, and rule evaluation
pub struct CorrelationEngine {
    /// Flag indicating whether the engine is running
    running: Arc<AtomicBool>,
    /// Configuration for the correlation engine
    config: CorrelationConfig,
}

/// Configuration for the correlation engine
#[derive(Debug, Clone)]
pub struct CorrelationConfig {
    /// Maximum number of patterns to support
    pub max_patterns: usize,
    /// Maximum number of time windows to support
    pub max_time_windows: usize,
    /// Maximum number of rules to support
    pub max_rules: usize,
    /// Maximum number of events to store in memory
    pub max_events: usize,
    /// Whether to enable debug logging
    pub debug: bool,
}

impl Default for CorrelationConfig {
    fn default() -> Self {
        Self {
            max_patterns: 1000,
            max_time_windows: 500,
            max_rules: 1000,
            max_events: 100000,
            debug: false,
        }
    }
}

impl CorrelationEngine {
    /// Create a new correlation engine with the given configuration
    pub fn new(config: CorrelationConfig) -> Self {
        Self {
            running: Arc::new(AtomicBool::new(false)),
            config,
        }
    }

    /// Start the correlation engine
    pub fn start(&self) -> CorrelationResult<()> {
        if self.running.swap(true, Ordering::SeqCst) {
            return Err(CorrelationError::GeneralError("Correlation engine already running".to_string()));
        }

        // TODO: Start pattern matcher, time window manager, and rule engine

        Ok(())
    }

    /// Stop the correlation engine
    pub fn stop(&self) -> CorrelationResult<()> {
        if !self.running.swap(false, Ordering::SeqCst) {
            return Err(CorrelationError::GeneralError("Correlation engine not running".to_string()));
        }

        // TODO: Stop pattern matcher, time window manager, and rule engine

        Ok(())
    }

    /// Process an event through the correlation engine
    pub fn process_event(&self, event: &Event) -> CorrelationResult<Vec<CorrelationResult>> {
        if !self.running.load(Ordering::SeqCst) {
            return Err(CorrelationError::GeneralError("Correlation engine not running".to_string()));
        }

        // TODO: Process event through pattern matcher, time window manager, and rule engine

        Ok(Vec::new())
    }
}

/// Event to be processed by the correlation engine
#[derive(Debug, Clone)]
pub struct Event {
    /// Unique identifier for the event
    pub id: String,
    /// Timestamp of the event in nanoseconds since the Unix epoch
    pub timestamp: u64,
    /// Source of the event (e.g., "ebpf", "syscall", "log")
    pub source: String,
    /// Type of the event (e.g., "file_access", "network_connection", "process_creation")
    pub event_type: String,
    /// Severity of the event (0-100, with 100 being the most severe)
    pub severity: u8,
    /// Additional data associated with the event
    pub data: EventData,
}

/// Data associated with an event
#[derive(Debug, Clone)]
pub struct EventData {
    /// Key-value pairs of event data
    pub fields: std::collections::HashMap<String, EventValue>,
}

/// Value of an event field
#[derive(Debug, Clone)]
pub enum EventValue {
    /// String value
    String(String),
    /// Integer value
    Integer(i64),
    /// Floating-point value
    Float(f64),
    /// Boolean value
    Boolean(bool),
    /// Array of values
    Array(Vec<EventValue>),
    /// Object (map) of values
    Object(std::collections::HashMap<String, EventValue>),
    /// Null value
    Null,
}

/// Result of a correlation rule match
#[derive(Debug, Clone)]
pub struct CorrelationResult {
    /// Unique identifier for the correlation result
    pub id: String,
    /// Timestamp of the correlation result in nanoseconds since the Unix epoch
    pub timestamp: u64,
    /// Name of the rule that generated the result
    pub rule_name: String,
    /// Description of the correlation result
    pub description: String,
    /// Severity of the correlation result (0-100, with 100 being the most severe)
    pub severity: u8,
    /// Confidence in the correlation result (0-100, with 100 being the highest confidence)
    pub confidence: u8,
    /// Events that contributed to the correlation result
    pub events: Vec<Event>,
    /// Additional data associated with the correlation result
    pub data: EventData,
} 