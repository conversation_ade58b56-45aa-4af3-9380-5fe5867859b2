/*!
 * Optimized Hash Map Implementation for eBPF
 * 
 * This module provides an optimized hash map implementation for eBPF maps,
 * with features like batched operations, memory-mapped access, and
 * performance monitoring.
 */

use std::marker::PhantomData;
use std::sync::atomic::{AtomicU64, Ordering};



use super::{Map, MapConfig, MapType, MapError};
use super::hash::HashFunction;

/// Hash map statistics
#[derive(Debug)]
pub struct HashMapStats {
    /// Number of lookups
    pub lookups: AtomicU64,
    /// Number of successful lookups (hits)
    pub hits: AtomicU64,
    /// Number of failed lookups (misses)
    pub misses: AtomicU64,
    /// Number of updates
    pub updates: AtomicU64,
    /// Number of deletes
    pub deletes: AtomicU64,
    /// Number of errors
    pub errors: AtomicU64,
}

impl HashMapStats {
    /// Create new hash map statistics
    pub fn new() -> Self {
        Self {
            lookups: AtomicU64::new(0),
            hits: AtomicU64::new(0),
            misses: AtomicU64::new(0),
            updates: AtomicU64::new(0),
            deletes: AtomicU64::new(0),
            errors: AtomicU64::new(0),
        }
    }
    
    /// Reset all statistics to zero
    pub fn reset(&self) {
        self.lookups.store(0, Ordering::Relaxed);
        self.hits.store(0, Ordering::Relaxed);
        self.misses.store(0, Ordering::Relaxed);
        self.updates.store(0, Ordering::Relaxed);
        self.deletes.store(0, Ordering::Relaxed);
        self.errors.store(0, Ordering::Relaxed);
    }
    
    /// Get the hit rate (hits / lookups)
    pub fn hit_rate(&self) -> f64 {
        let lookups = self.lookups.load(Ordering::Relaxed);
        if lookups == 0 {
            return 0.0;
        }
        
        let hits = self.hits.load(Ordering::Relaxed);
        hits as f64 / lookups as f64
    }
}

impl Default for HashMapStats {
    fn default() -> Self {
        Self::new()
    }
}

/// Optimized hash map for eBPF
#[derive(Debug)]
pub struct OptimizedHashMap {
    /// Inner map
    inner: Map,
    /// Hash function to use
    hash_function: HashFunction,
    /// Statistics
    stats: Arc<HashMapStats>,
    /// Memory-mapped access enabled
    mmap_enabled: bool,
}

impl OptimizedHashMap {
    /// Create a new optimized hash map
    pub fn create(
        key_size: usize,
        value_size: usize,
        max_entries: usize,
        hash_function: Option<HashFunction>,
    ) -> Result<Self, MapError> {
        // Choose the best hash function for the key size if not specified
        let hash_function = hash_function.unwrap_or_else(|| HashFunction::recommended_for_size(key_size));
        
        // Create the map configuration
        let config = MapConfig::new(MapType::Hash, key_size, value_size, max_entries, 0)?;
        
        // Create the map
        let inner = Map::create(config)?;
        
        Ok(Self {
            inner,
            hash_function,
            stats: Arc::new(HashMapStats::new()),
            mmap_enabled: false,
        })
    }
    
    /// Open an existing optimized hash map
    pub fn open(path: &str, hash_function: Option<HashFunction>) -> Result<Self, MapError> {
        // Open the map
        let inner = Map::open(path)?;
        
        // Get the key size
        let key_size = inner.config().key_size;
        
        // Choose the best hash function for the key size if not specified
        let hash_function = hash_function.unwrap_or_else(|| HashFunction::recommended_for_size(key_size));
        
        Ok(Self {
            inner,
            hash_function,
            stats: Arc::new(HashMapStats::new()),
            mmap_enabled: false,
        })
    }
    
    /// Enable memory-mapped access (if supported)
    pub fn enable_mmap(&mut self) -> Result<(), MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the kernel supports memory-mapped BPF maps
            // This is a simplified check - in a real implementation, we would check kernel version
            // and capabilities more thoroughly
            if std::path::Path::new("/sys/fs/bpf").exists() {
                self.mmap_enabled = true;
                debug!("Memory-mapped access enabled for hash map");
                Ok(())
            } else {
                warn!("Memory-mapped access not supported by this kernel");
                Err(MapError::OperationError("Memory-mapped access not supported".to_string()))
            }
        }
        
        #[cfg(not(feature = "linux"))]
        {
            warn!("Memory-mapped access not supported on this platform");
            Err(MapError::OperationError("Memory-mapped access not supported on this platform".to_string()))
        }
    }
    
    /// Lookup a value by key
    pub fn lookup(&self, key: &[u8]) -> Result<Vec<u8>, MapError> {
        // Update statistics
        self.stats.lookups.fetch_add(1, Ordering::Relaxed);
        
        // Perform the lookup
        match self.inner.lookup(key) {
            Ok(value) => {
                // Update hit statistics
                self.stats.hits.fetch_add(1, Ordering::Relaxed);
                Ok(value)
            }
            Err(e) => {
                // Update miss statistics
                self.stats.misses.fetch_add(1, Ordering::Relaxed);
                
                if let MapError::NotFound(_) = e {
                    // Not found is a normal condition, don't count as error
                    Err(e)
                } else {
                    // Update error statistics
                    self.stats.errors.fetch_add(1, Ordering::Relaxed);
                    Err(e)
                }
            }
        }
    }
    
    /// Update a key-value pair
    pub fn update(&self, key: &[u8], value: &[u8], flags: u64) -> Result<(), MapError> {
        // Update statistics
        self.stats.updates.fetch_add(1, Ordering::Relaxed);
        
        // Perform the update
        match self.inner.update(key, value, flags) {
            Ok(()) => Ok(()),
            Err(e) => {
                // Update error statistics
                self.stats.errors.fetch_add(1, Ordering::Relaxed);
                Err(e)
            }
        }
    }
    
    /// Delete a key-value pair
    pub fn delete(&self, key: &[u8]) -> Result<(), MapError> {
        // Update statistics
        self.stats.deletes.fetch_add(1, Ordering::Relaxed);
        
        // Perform the delete
        match self.inner.delete(key) {
            Ok(()) => Ok(()),
            Err(e) => {
                // Update error statistics
                self.stats.errors.fetch_add(1, Ordering::Relaxed);
                Err(e)
            }
        }
    }
    
    /// Batch update multiple key-value pairs
    pub fn batch_update(&self, entries: &[(&[u8], &[u8])], flags: u64) -> Result<(), MapError> {
        // Update statistics
        self.stats.updates.fetch_add(entries.len() as u64, Ordering::Relaxed);
        
        // Perform batch update
        let mut errors = 0;
        
        for (key, value) in entries {
            if let Err(e) = self.inner.update(key, value, flags) {
                // Update error statistics
                errors += 1;
                warn!("Error in batch update: {}", e);
            }
        }
        
        if errors > 0 {
            self.stats.errors.fetch_add(errors, Ordering::Relaxed);
            Err(MapError::OperationError(format!("{} errors in batch update", errors)))
        } else {
            Ok(())
        }
    }
    
    /// Batch lookup multiple keys
    pub fn batch_lookup(&self, keys: &[&[u8]]) -> Vec<Result<Vec<u8>, MapError>> {
        // Update statistics
        self.stats.lookups.fetch_add(keys.len() as u64, Ordering::Relaxed);
        
        // Perform batch lookup
        let mut results = Vec::with_capacity(keys.len());
        let mut hits = 0;
        let mut misses = 0;
        let mut errors = 0;
        
        for key in keys {
            match self.inner.lookup(key) {
                Ok(value) => {
                    hits += 1;
                    results.push(Ok(value));
                }
                Err(e) => {
                    if let MapError::NotFound(_) = e {
                        misses += 1;
                    } else {
                        errors += 1;
                    }
                    results.push(Err(e));
                }
            }
        }
        
        // Update statistics
        self.stats.hits.fetch_add(hits, Ordering::Relaxed);
        self.stats.misses.fetch_add(misses, Ordering::Relaxed);
        self.stats.errors.fetch_add(errors, Ordering::Relaxed);
        
        results
    }
    
    /// Batch delete multiple keys
    pub fn batch_delete(&self, keys: &[&[u8]]) -> Result<(), MapError> {
        // Update statistics
        self.stats.deletes.fetch_add(keys.len() as u64, Ordering::Relaxed);
        
        // Perform batch delete
        let mut errors = 0;
        
        for key in keys {
            if let Err(e) = self.inner.delete(key) {
                // Update error statistics
                errors += 1;
                warn!("Error in batch delete: {}", e);
            }
        }
        
        if errors > 0 {
            self.stats.errors.fetch_add(errors, Ordering::Relaxed);
            Err(MapError::OperationError(format!("{} errors in batch delete", errors)))
        } else {
            Ok(())
        }
    }
    
    /// Get the inner map
    pub fn inner(&self) -> &Map {
        &self.inner
    }
    
    /// Get the hash function
    pub fn hash_function(&self) -> HashFunction {
        self.hash_function
    }
    
    /// Get the statistics
    pub fn stats(&self) -> &HashMapStats {
        &self.stats
    }
    
    /// Reset the statistics
    pub fn reset_stats(&self) {
        self.stats.reset();
    }
    
    /// Pin the map to the BPF filesystem
    pub fn pin(&self, path: &str) -> Result<(), MapError> {
        self.inner.pin(path)
    }
    
    /// Unpin the map from the BPF filesystem
    pub fn unpin(&self) -> Result<(), MapError> {
        self.inner.unpin()
    }
}

/// Type-safe optimized hash map
#[derive(Debug)]
pub struct TypedOptimizedHashMap<K, V> {
    /// Inner optimized hash map
    inner: OptimizedHashMap,
    /// Key type
    _key_type: PhantomData<K>,
    /// Value type
    _value_type: PhantomData<V>,
}

impl<K, V> TypedOptimizedHashMap<K, V>
where
    K: serde::Serialize + serde::de::DeserializeOwned,
    V: serde::Serialize + serde::de::DeserializeOwned,
{
    /// Create a new typed optimized hash map
    pub fn create(
        max_entries: usize,
        hash_function: Option<HashFunction>,
    ) -> Result<Self, MapError> {
        // Estimate key and value sizes using bincode serialization
        let key_size = bincode::serialized_size(&K::default())
            .map_err(|e| MapError::SerializationError(format!("Failed to estimate key size: {}", e)))?
            as usize;
            
        let value_size = bincode::serialized_size(&V::default())
            .map_err(|e| MapError::SerializationError(format!("Failed to estimate value size: {}", e)))?
            as usize;
        
        // Create the optimized hash map
        let inner = OptimizedHashMap::create(key_size, value_size, max_entries, hash_function)?;
        
        Ok(Self {
            inner,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
    
    /// Open an existing typed optimized hash map
    pub fn open(path: &str, hash_function: Option<HashFunction>) -> Result<Self, MapError> {
        let inner = OptimizedHashMap::open(path, hash_function)?;
        
        Ok(Self {
            inner,
            _key_type: PhantomData,
            _value_type: PhantomData,
        })
    }
    
    /// Enable memory-mapped access (if supported)
    pub fn enable_mmap(&mut self) -> Result<(), MapError> {
        self.inner.enable_mmap()
    }
    
    /// Lookup a value by key
    pub fn lookup(&self, key: &K) -> Result<V, MapError> {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
        
        // Lookup the value
        let value_bytes = self.inner.lookup(&key_bytes)?;
        
        // Deserialize the value
        bincode::deserialize(&value_bytes)
            .map_err(|e| MapError::DeserializationError(format!("Failed to deserialize value: {}", e)))
    }
    
    /// Update a key-value pair
    pub fn update(&self, key: &K, value: &V, flags: u64) -> Result<(), MapError> {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
        
        // Serialize the value
        let value_bytes = bincode::serialize(value)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize value: {}", e)))?;
        
        // Update the map
        self.inner.update(&key_bytes, &value_bytes, flags)
    }
    
    /// Delete a key-value pair
    pub fn delete(&self, key: &K) -> Result<(), MapError> {
        // Serialize the key
        let key_bytes = bincode::serialize(key)
            .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
        
        // Delete the key-value pair
        self.inner.delete(&key_bytes)
    }
    
    /// Batch update multiple key-value pairs
    pub fn batch_update(&self, entries: &[(K, V)], flags: u64) -> Result<(), MapError> {
        // Serialize all entries
        let mut serialized_entries = Vec::with_capacity(entries.len());
        
        for (key, value) in entries {
            // Serialize the key
            let key_bytes = bincode::serialize(key)
                .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
            
            // Serialize the value
            let value_bytes = bincode::serialize(value)
                .map_err(|e| MapError::SerializationError(format!("Failed to serialize value: {}", e)))?;
            
            serialized_entries.push((key_bytes, value_bytes));
        }
        
        // Convert to slice references
        let entry_refs: Vec<(&[u8], &[u8])> = serialized_entries.iter()
            .map(|(k, v)| (k.as_slice(), v.as_slice()))
            .collect();
        
        // Update the map
        self.inner.batch_update(&entry_refs, flags)
    }
    
    /// Batch lookup multiple keys
    pub fn batch_lookup(&self, keys: &[K]) -> Vec<Result<V, MapError>> {
        // Serialize all keys
        let mut serialized_keys = Vec::with_capacity(keys.len());
        let mut serialization_errors = Vec::new();
        
        for (i, key) in keys.iter().enumerate() {
            match bincode::serialize(key) {
                Ok(key_bytes) => serialized_keys.push(key_bytes),
                Err(e) => {
                    serialization_errors.push((i, MapError::SerializationError(format!("Failed to serialize key: {}", e))));
                }
            }
        }
        
        // Convert to slice references
        let key_refs: Vec<&[u8]> = serialized_keys.iter()
            .map(|k| k.as_slice())
            .collect();
        
        // Lookup the values
        let raw_results = self.inner.batch_lookup(&key_refs);
        
        // Process the results
        let mut results = Vec::with_capacity(keys.len());
        let mut result_index = 0;
        
        for i in 0..keys.len() {
            // Check if this key had a serialization error
            if let Some((_, error)) = serialization_errors.iter().find(|(idx, _)| *idx == i) {
                results.push(Err(error.clone()));
            } else {
                // Process the lookup result
                match &raw_results[result_index] {
                    Ok(value_bytes) => {
                        match bincode::deserialize::<V>(value_bytes) {
                            Ok(value) => results.push(Ok(value)),
                            Err(e) => results.push(Err(MapError::DeserializationError(format!("Failed to deserialize value: {}", e)))),
                        }
                    }
                    Err(e) => results.push(Err(e.clone())),
                }
                result_index += 1;
            }
        }
        
        results
    }
    
    /// Batch delete multiple keys
    pub fn batch_delete(&self, keys: &[K]) -> Result<(), MapError> {
        // Serialize all keys
        let mut serialized_keys = Vec::with_capacity(keys.len());
        
        for key in keys {
            // Serialize the key
            let key_bytes = bincode::serialize(key)
                .map_err(|e| MapError::SerializationError(format!("Failed to serialize key: {}", e)))?;
            
            serialized_keys.push(key_bytes);
        }
        
        // Convert to slice references
        let key_refs: Vec<&[u8]> = serialized_keys.iter()
            .map(|k| k.as_slice())
            .collect();
        
        // Delete the keys
        self.inner.batch_delete(&key_refs)
    }
    
    /// Get the inner optimized hash map
    pub fn inner(&self) -> &OptimizedHashMap {
        &self.inner
    }
    
    /// Get the hash function
    pub fn hash_function(&self) -> HashFunction {
        self.inner.hash_function()
    }
    
    /// Get the statistics
    pub fn stats(&self) -> &HashMapStats {
        self.inner.stats()
    }
    
    /// Reset the statistics
    pub fn reset_stats(&self) {
        self.inner.reset_stats();
    }
    
    /// Pin the map to the BPF filesystem
    pub fn pin(&self, path: &str) -> Result<(), MapError> {
        self.inner.pin(path)
    }
    
    /// Unpin the map from the BPF filesystem
    pub fn unpin(&self) -> Result<(), MapError> {
        self.inner.unpin()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    
    #[test]
    fn test_optimized_hash_map_basic() {
        // Create a new optimized hash map
        let map = OptimizedHashMap::create(8, 8, 1024, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test update and lookup
        let key = [1, 2, 3, 4, 5, 6, 7, 8];
        let value = [10, 20, 30, 40, 50, 60, 70, 80];
        
        map.update(&key, &value, 0).unwrap();
        let result = map.lookup(&key).unwrap();
        
        assert_eq!(result, value);
        
        // Test delete
        map.delete(&key).unwrap();
        let result = map.lookup(&key);
        
        assert!(result.is_err());
        
        // Check statistics
        assert_eq!(map.stats().lookups.load(Ordering::Relaxed), 2);
        assert_eq!(map.stats().hits.load(Ordering::Relaxed), 1);
        assert_eq!(map.stats().misses.load(Ordering::Relaxed), 1);
        assert_eq!(map.stats().updates.load(Ordering::Relaxed), 1);
        assert_eq!(map.stats().deletes.load(Ordering::Relaxed), 1);
    }
    
    #[test]
    fn test_optimized_hash_map_batch() {
        // Create a new optimized hash map
        let map = OptimizedHashMap::create(8, 8, 1024, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test batch update
        let entries = vec![
            ([1, 2, 3, 4, 5, 6, 7, 8], [10, 20, 30, 40, 50, 60, 70, 80]),
            ([2, 3, 4, 5, 6, 7, 8, 9], [20, 30, 40, 50, 60, 70, 80, 90]),
            ([3, 4, 5, 6, 7, 8, 9, 10], [30, 40, 50, 60, 70, 80, 90, 100]),
        ];
        
        let entry_refs: Vec<(&[u8], &[u8])> = entries.iter()
            .map(|(k, v)| (k.as_slice(), v.as_slice()))
            .collect();
        
        map.batch_update(&entry_refs, 0).unwrap();
        
        // Test batch lookup
        let keys: Vec<&[u8]> = entries.iter()
            .map(|(k, _)| k.as_slice())
            .collect();
        
        let results = map.batch_lookup(&keys);
        
        for (i, result) in results.iter().enumerate() {
            assert!(result.is_ok());
            assert_eq!(result.as_ref().unwrap(), &entries[i].1);
        }
        
        // Test batch delete
        map.batch_delete(&keys).unwrap();
        
        let results = map.batch_lookup(&keys);
        
        for result in results {
            assert!(result.is_err());
        }
    }
    
    #[test]
    fn test_typed_optimized_hash_map() {
        // Create a new typed optimized hash map
        let map = TypedOptimizedHashMap::<String, u32>::create(1024, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test update and lookup
        map.update(&"test_key".to_string(), &42, 0).unwrap();
        let result = map.lookup(&"test_key".to_string()).unwrap();
        
        assert_eq!(result, 42);
        
        // Test delete
        map.delete(&"test_key".to_string()).unwrap();
        let result = map.lookup(&"test_key".to_string());
        
        assert!(result.is_err());
    }
    
    #[test]
    fn test_typed_optimized_hash_map_batch() {
        // Create a new typed optimized hash map
        let map = TypedOptimizedHashMap::<String, u32>::create(1024, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test batch update
        let entries = vec![
            ("key1".to_string(), 1),
            ("key2".to_string(), 2),
            ("key3".to_string(), 3),
        ];
        
        map.batch_update(&entries, 0).unwrap();
        
        // Test batch lookup
        let keys = vec![
            "key1".to_string(),
            "key2".to_string(),
            "key3".to_string(),
        ];
        
        let results = map.batch_lookup(&keys);
        
        for (i, result) in results.iter().enumerate() {
            assert!(result.is_ok());
            assert_eq!(result.as_ref().unwrap(), &entries[i].1);
        }
        
        // Test batch delete
        map.batch_delete(&keys).unwrap();
        
        let results = map.batch_lookup(&keys);
        
        for result in results {
            assert!(result.is_err());
        }
    }
} 