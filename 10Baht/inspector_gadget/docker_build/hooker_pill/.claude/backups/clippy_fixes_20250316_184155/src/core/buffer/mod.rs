/*!
 * Event Buffer Module
 * 
 * This module provides a thread-safe circular buffer for storing events
 * with configurable overflow behavior and batch operations.
 */

pub mod circular;
pub mod memory;

pub use circular::{CircularBuffer, CircularBufferConfig, OverflowBehavior};
pub use memory::{<PERSON>B<PERSON>er, MemoryBufferConfig};


use async_trait::async_trait;


use crate::core::events::TraceEvent;
use crate::error::Result;

/// Buffer overflow behavior
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum BufferOverflowBehavior {
    /// Drop oldest events when buffer is full
    DropOldest,
    /// Drop newest events when buffer is full
    DropNewest,
    /// Block until space is available
    Block,
}

impl Default for BufferOverflowBehavior {
    fn default() -> Self {
        Self::DropOldest
    }
}

/// Buffer statistics
#[derive(Debug, Clone, Default)]
pub struct BufferStats {
    /// Number of events added to the buffer
    pub added_count: usize,
    /// Number of events removed from the buffer
    pub removed_count: usize,
    /// Number of events dropped due to overflow
    pub dropped_count: usize,
    /// Maximum number of events in the buffer at any time
    pub high_water_mark: usize,
    /// Current number of events in the buffer
    pub current_count: usize,
}

/// Event buffer trait
#[async_trait]
pub trait EventBuffer: Send + Sync {
    /// Add an event to the buffer
    async fn add(&self, event: TraceEvent) -> Result<()>;
    
    /// Add multiple events to the buffer
    async fn add_batch(&self, events: &[TraceEvent]) -> Result<()>;
    
    /// Get the next event from the buffer
    async fn next(&self) -> Result<Option<TraceEvent>>;
    
    /// Get a batch of events from the buffer
    async fn next_batch(&self, max_events: usize) -> Result<Vec<TraceEvent>>;
    
    /// Get the number of events in the buffer
    async fn len(&self) -> usize;
    
    /// Check if the buffer is empty
    async fn is_empty(&self) -> bool;
    
    /// Get buffer statistics
    async fn stats(&self) -> BufferStats;
    
    /// Clear the buffer
    async fn clear(&self) -> Result<()>;
}

/// Create a new event buffer
pub fn create_buffer(capacity: usize, overflow_behavior: BufferOverflowBehavior) -> Arc<dyn EventBuffer> {
    Arc::new(CircularBuffer::new(capacity, overflow_behavior))
}

/// Create a new circular buffer with the given configuration.
///
/// # Arguments
///
/// * `config` - The configuration for the circular buffer
///
/// # Returns
///
/// A new circular buffer
pub fn new_circular_buffer(config: circular::CircularBufferConfig) -> Arc<dyn EventBuffer> {
    Arc::new(circular::CircularBuffer::new(config))
}

/// Create a new memory buffer with the given configuration.
///
/// # Arguments
///
/// * `config` - The configuration for the memory buffer
///
/// # Returns
///
/// A new memory buffer
pub fn new_memory_buffer(config: memory::MemoryBufferConfig) -> Arc<dyn EventBuffer> {
    Arc::new(memory::MemoryBuffer::new(config))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{Event, EventId, EventType, Severity};
    
    use std::time::{SystemTime, UNIX_EPOCH};

    fn create_test_event() -> Event {
        Event {
            id: EventId::new(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            event_type: EventType::Syscall,
            severity: Severity::Info,
            source: "test".to_string(),
            process_id: Some(1234),
            thread_id: Some(5678),
            syscall_number: Some(1),
            syscall_name: Some("write".to_string()),
            parameters: Some(json!({"fd": 1, "buf": "Hello", "count": 5})),
            return_value: Some(5),
            duration_ns: Some(1000),
            tags: vec!["test".to_string()],
            metadata: None,
        }
    }

    #[test]
    #[tokio::test]
async #[tokio::test]
async #[tokio::test]
async #[tokio::test]
async fn test_circular_buffer() {
        let config = circular::CircularBufferConfig {
            capacity: 10,
            overflow_behavior: circular::OverflowBehavior::DropOldest,
        };

        let buffer = new_circular_buffer(config);

        // Add events
        for _ in 0..5 {
            let event = create_test_event();
            assert!(buffer.add(event).is_ok());
        }

        // Check length
        assert_eq!(buffer.len(), 5);

        // Get events
        for _ in 0..5 {
            assert!(buffer.next().is_ok());
        }

        // Buffer should be empty
        assert!(buffer.is_empty().await);
        assert!(buffer.next().is_ok().is_none());

        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added_count, 5);
        assert_eq!(stats.removed_count, 5);
        assert_eq!(stats.dropped_count, 0);
        assert_eq!(stats.high_water_mark, 5);
        assert_eq!(stats.current_count, 0);
    }

    #[test]
    #[tokio::test]
async #[tokio::test]
async #[tokio::test]
async #[tokio::test]
async fn test_memory_buffer() {
        let config = memory::MemoryBufferConfig {
            initial_capacity: 10,
        };

        let buffer = new_memory_buffer(config);

        // Add events
        for _ in 0..5 {
            let event = create_test_event();
            assert!(buffer.add(event).is_ok());
        }

        // Check length
        assert_eq!(buffer.len(), 5);

        // Get events
        for _ in 0..5 {
            assert!(buffer.next().is_ok());
        }

        // Buffer should be empty
        assert!(buffer.is_empty().await);
        assert!(buffer.next().is_ok().is_none());

        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added_count, 5);
        assert_eq!(stats.removed_count, 5);
        assert_eq!(stats.dropped_count, 0);
        assert_eq!(stats.high_water_mark, 5);
        assert_eq!(stats.current_count, 0);
    }

    #[test]
    #[tokio::test]
async #[tokio::test]
async #[tokio::test]
async #[tokio::test]
async fn test_add_batch() {
        let config = circular::CircularBufferConfig {
            capacity: 10,
            overflow_behavior: circular::OverflowBehavior::DropOldest,
        };

        let buffer = new_circular_buffer(config);

        // Create batch of events
        let mut events = Vec::new();
        for _ in 0..5 {
            events.push(create_test_event());
        }

        // Add batch
        let added = buffer.add_batch(&events).await;
        assert!(added.is_ok());
        assert_eq!(buffer.len(), 5);

        // Get batch
        let batch = buffer.next_batch(10).await;
        assert!(batch.is_ok());
        assert_eq!(batch.unwrap().len(), 5);
        assert!(buffer.is_empty().await);
    }

    #[test]
    #[tokio::test]
async #[tokio::test]
async #[tokio::test]
async #[tokio::test]
async fn test_clear() {
        let config = circular::CircularBufferConfig {
            capacity: 10,
            overflow_behavior: circular::OverflowBehavior::DropOldest,
        };

        let buffer = new_circular_buffer(config);

        // Add events
        for _ in 0..5 {
            let event = create_test_event();
            buffer.add(event).await.unwrap();
        }

        assert_eq!(buffer.len(), 5);

        // Clear buffer
        buffer.clear().await.unwrap();
        assert!(buffer.is_empty().await);

        // Stats should be reset
        let stats = buffer.stats().await;
        assert_eq!(stats.current_count, 0);
    }
} 