/*!
 * Event Exporter Module
 * 
 * This module provides exporters for sending events to external systems,
 * with a focus on Elasticsearch integration.
 */

pub mod elasticsearch;
pub mod file;

pub use elasticsearch::{ElasticsearchConfig, ElasticsearchExporter};
pub use file::{FileExporter, FileExporterConfig, FileFormat};


use async_trait::async_trait;

use crate::core::events::Event;
use crate::error::Result;

/// Trait for event exporters.
///
/// Implementations of this trait are responsible for exporting events
/// to external systems such as Elasticsearch, files, or other destinations.
#[async_trait]
pub trait EventExporter: Send + Sync {
    /// Export a single event.
    ///
    /// # Arguments
    ///
    /// * `event` - The event to export
    ///
    /// # Returns
    ///
    /// A Result indicating success or an error message
    async fn export_event(&self, event: Event) -> Result<(), String>;

    /// Export multiple events in a batch.
    ///
    /// # Arguments
    ///
    /// * `events` - The events to export
    ///
    /// # Returns
    ///
    /// A Result indicating success or an error message
    async fn export_events(&self, events: Vec<Event>) -> Result<(), String>;

    /// Flush any buffered events to ensure they are written to the destination.
    ///
    /// # Returns
    ///
    /// A Result indicating success or an error message
    async fn flush(&self) -> Result<(), String>;
}

/// Create a new Elasticsearch exporter with the given configuration.
///
/// # Arguments
///
/// * `config` - The configuration for the Elasticsearch exporter
///
/// # Returns
///
/// A Result containing the exporter or an error message
pub async fn new_elasticsearch_exporter(
    config: elasticsearch::ElasticsearchConfig,
) -> Result<Arc<dyn EventExporter>, String> {
    let exporter = elasticsearch::ElasticsearchExporter::new(config).await?;
    Ok(Arc::new(exporter))
}

/// Create a new file exporter with the given configuration.
///
/// # Arguments
///
/// * `config` - The configuration for the file exporter
///
/// # Returns
///
/// A Result containing the exporter or an error message
pub fn new_file_exporter(
    config: file::FileExporterConfig,
) -> Result<Arc<dyn EventExporter>, String> {
    let exporter = file::FileExporter::new(config)?;
    Ok(Arc::new(exporter))
}

/// A composite exporter that sends events to multiple exporters.
pub struct CompositeExporter {
    exporters: Vec<Arc<dyn EventExporter>>,
}

impl CompositeExporter {
    /// Create a new composite exporter with the given exporters.
    ///
    /// # Arguments
    ///
    /// * `exporters` - The exporters to use
    ///
    /// # Returns
    ///
    /// A new CompositeExporter
    pub fn new(exporters: Vec<Arc<dyn EventExporter>>) -> Self {
        Self { exporters }
    }
}

#[async_trait]
impl EventExporter for CompositeExporter {
    async fn export_event(&self, event: Event) -> Result<(), String> {
        let mut errors = Vec::new();

        for exporter in &self.exporters {
            if let Err(e) = exporter.export_event(event.clone()).await {
                errors.push(format!("{}: {}", std::any::type_name::<dyn EventExporter>(), e));
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(format!("Errors exporting event: {}", errors.join(", ")))
        }
    }

    async fn export_events(&self, events: Vec<Event>) -> Result<(), String> {
        let mut errors = Vec::new();

        for exporter in &self.exporters {
            if let Err(e) = exporter.export_events(events.clone()).await {
                errors.push(format!("{}: {}", std::any::type_name::<dyn EventExporter>(), e));
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(format!("Errors exporting events: {}", errors.join(", ")))
        }
    }

    async fn flush(&self) -> Result<(), String> {
        let mut errors = Vec::new();

        for exporter in &self.exporters {
            if let Err(e) = exporter.flush().await {
                errors.push(format!("{}: {}", std::any::type_name::<dyn EventExporter>(), e));
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(format!("Errors flushing exporters: {}", errors.join(", ")))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{Event, EventId, EventType, Severity};
    
    use std::time::{SystemTime, UNIX_EPOCH};
    use tempfile::tempdir;

    fn create_test_event() -> Event {
        Event {
            id: EventId::new(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            event_type: EventType::Syscall,
            severity: Severity::Info,
            source: "test".to_string(),
            process_id: Some(1234),
            thread_id: Some(5678),
            syscall_number: Some(1),
            syscall_name: Some("write".to_string()),
            parameters: Some(json!({"fd": 1, "buf": "Hello", "count": 5})),
            return_value: Some(5),
            duration_ns: Some(1000),
            tags: vec!["test".to_string()],
            metadata: None,
        }
    }

    #[tokio::test]
    async fn test_new_file_exporter() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("events.log");

        let config = file::FileExporterConfig {
            path,
            ..Default::default()
        };

        let exporter = new_file_exporter(config);
        assert!(exporter.is_ok());
    }

    #[tokio::test]
    async fn test_composite_exporter() {
        let dir = tempdir().unwrap();
        let path1 = dir.path().join("events1.log");
        let path2 = dir.path().join("events2.log");

        let config1 = file::FileExporterConfig {
            path: path1.clone(),
            format: file::FileFormat::Json,
            flush_interval_ms: 0, // Flush immediately
            ..Default::default()
        };

        let config2 = file::FileExporterConfig {
            path: path2.clone(),
            format: file::FileFormat::Text,
            flush_interval_ms: 0, // Flush immediately
            ..Default::default()
        };

        let exporter1 = new_file_exporter(config1).unwrap();
        let exporter2 = new_file_exporter(config2).unwrap();

        let composite = CompositeExporter::new(vec![exporter1, exporter2]);
        let event = create_test_event();

        let result = composite.export_event(event).await;
        assert!(result.is_ok());

        // Check that both files contain the event
        let contents1 = std::fs::read_to_string(path1).unwrap();
        let contents2 = std::fs::read_to_string(path2).unwrap();

        assert!(!contents1.is_empty());
        assert!(!contents2.is_empty());
        assert!(contents1.contains("write"));
        assert!(contents2.contains("write"));
    }

    // Mock exporter for testing
    struct MockExporter {
        should_fail: bool,
    }

    #[async_trait]
    impl EventExporter for MockExporter {
        async fn export_event(&self, _event: Event) -> Result<(), String> {
            if self.should_fail {
                Err("Mock export failure".to_string())
            } else {
                Ok(())
            }
        }

        async fn export_events(&self, _events: Vec<Event>) -> Result<(), String> {
            if self.should_fail {
                Err("Mock export failure".to_string())
            } else {
                Ok(())
            }
        }

        async fn flush(&self) -> Result<(), String> {
            if self.should_fail {
                Err("Mock flush failure".to_string())
            } else {
                Ok(())
            }
        }
    }

    #[tokio::test]
    async fn test_composite_exporter_partial_failure() {
        let success_exporter = Arc::new(MockExporter { should_fail: false });
        let failure_exporter = Arc::new(MockExporter { should_fail: true });

        let composite = CompositeExporter::new(vec![success_exporter, failure_exporter]);
        let event = create_test_event();

        let result = composite.export_event(event).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Mock export failure"));

        let events = vec![create_test_event(), create_test_event()];
        let result = composite.export_events(events).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Mock export failure"));

        let result = composite.flush().await;
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Mock flush failure"));
    }
} 