/*!
 * Optimized Hash Functions for eBPF Maps
 * 
 * This module provides optimized hash functions for use with eBPF Hash Maps.
 */

use std::hash::{Hash, Hasher};
use std::mem;

/// FNV-1a hash function constants
const FNV_PRIME: u64 = 1099511628211;
const FNV_OFFSET_BASIS: u64 = 14695981039346656037;

/// MurmurHash3 constants
const C1: u32 = 0xcc9e2d51;
const C2: u32 = 0x1b873593;
const R1: u32 = 15;
const R2: u32 = 13;
const M: u32 = 5;
const N: u32 = 0xe6546b64;

/// Hash function type
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum HashFunction {
    /// FNV-1a hash function (good for small keys)
    Fnv1a,
    /// MurmurHash3 (good for larger keys)
    MurmurHash3,
    /// Default hash function (uses <PERSON><PERSON>'s default hasher)
    Default,
}

impl HashFunction {
    /// Hash a key using the selected hash function
    pub fn hash<T: Hash + ?Sized>(&self, key: &T) -> u64 {
        match self {
            HashFunction::Fnv1a => fnv1a_hash(key),
            HashFunction::MurmurHash3 => murmur3_hash(key),
            HashFunction::Default => default_hash(key),
        }
    }
    
    /// Get the recommended hash function for a key of the given size
    pub fn recommended_for_size(key_size: usize) -> Self {
        if key_size <= 16 {
            // FNV-1a is faster for small keys
            HashFunction::Fnv1a
        } else {
            // MurmurHash3 has better distribution for larger keys
            HashFunction::MurmurHash3
        }
    }
}

/// FNV-1a hash implementation
pub fn fnv1a_hash<T: Hash + ?Sized>(key: &T) -> u64 {
    let mut hasher = Fnv1aHasher::new();
    key.hash(&mut hasher);
    hasher.finish()
}

/// MurmurHash3 implementation
pub fn murmur3_hash<T: Hash + ?Sized>(key: &T) -> u64 {
    let mut hasher = MurmurHash3Hasher::new();
    key.hash(&mut hasher);
    hasher.finish()
}

/// Default hash implementation using Rust's default hasher
pub fn default_hash<T: Hash + ?Sized>(key: &T) -> u64 {
    use std::collections::hash_map::DefaultHasher;
    let mut hasher = DefaultHasher::new();
    key.hash(&mut hasher);
    hasher.finish()
}

/// FNV-1a hasher implementation
#[derive(Default)]
pub struct Fnv1aHasher {
    state: u64,
}

impl Fnv1aHasher {
    /// Create a new FNV-1a hasher
    pub fn new() -> Self {
        Self { state: FNV_OFFSET_BASIS }
    }
}

impl Hasher for Fnv1aHasher {
    fn finish(&self) -> u64 {
        self.state
    }

    fn write(&mut self, bytes: &[u8]) {
        for &byte in bytes {
            self.state ^= u64::from(byte);
            self.state = self.state.wrapping_mul(FNV_PRIME);
        }
    }
}

/// MurmurHash3 hasher implementation
#[derive(Default)]
pub struct MurmurHash3Hasher {
    state: u32,
    length: u32,
    tail: [u8; 4],
    tail_len: usize,
}

impl MurmurHash3Hasher {
    /// Create a new MurmurHash3 hasher
    pub fn new() -> Self {
        Self {
            state: 0,
            length: 0,
            tail: [0; 4],
            tail_len: 0,
        }
    }
    
    fn process_block(&mut self, block: u32) {
        let mut k = block;
        
        k = k.wrapping_mul(C1);
        k = k.rotate_left(R1);
        k = k.wrapping_mul(C2);
        
        self.state ^= k;
        self.state = self.state.rotate_left(R2);
        self.state = self.state.wrapping_mul(M).wrapping_add(N);
    }
}

impl Hasher for MurmurHash3Hasher {
    fn finish(&self) -> u64 {
        let mut hash = self.state;
        
        // Process any remaining bytes in the tail
        if self.tail_len > 0 {
            let mut k = 0u32;
            for i in 0..self.tail_len {
                k |= (self.tail[i] as u32) << (i * 8);
            }
            
            k = k.wrapping_mul(C1);
            k = k.rotate_left(R1);
            k = k.wrapping_mul(C2);
            
            hash ^= k;
        }
        
        // Finalization
        hash ^= self.length;
        
        hash ^= hash >> 16;
        hash = hash.wrapping_mul(0x85ebca6b);
        hash ^= hash >> 13;
        hash = hash.wrapping_mul(0xc2b2ae35);
        hash ^= hash >> 16;
        
        hash as u64
    }

    fn write(&mut self, bytes: &[u8]) {
        let mut i = 0;
        
        // Update length
        self.length += bytes.len() as u32;
        
        // Process any bytes in the tail from a previous write
        if self.tail_len > 0 {
            while i < bytes.len() && self.tail_len < 4 {
                self.tail[self.tail_len] = bytes[i];
                self.tail_len += 1;
                i += 1;
            }
            
            if self.tail_len == 4 {
                let block = u32::from_le_bytes(self.tail);
                self.process_block(block);
                self.tail_len = 0;
            }
        }
        
        // Process 4-byte blocks
        let end = bytes.len() - (bytes.len() % 4);
        while i + 4 <= end {
            let block = u32::from_le_bytes([bytes[i], bytes[i+1], bytes[i+2], bytes[i+3]]);
            self.process_block(block);
            i += 4;
        }
        
        // Store any remaining bytes in the tail
        while i < bytes.len() {
            self.tail[self.tail_len] = bytes[i];
            self.tail_len += 1;
            i += 1;
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_fnv1a_hash() {
        // Test vectors for FNV-1a
        assert_eq!(fnv1a_hash(&""), 14695981039346656037);
        assert_eq!(fnv1a_hash(&"a"), 9650029242287828579);
        assert_eq!(fnv1a_hash(&"hello"), 11831194018420276491);
    }
    
    #[test]
    fn test_murmur3_hash() {
        // Test vectors for MurmurHash3 (32-bit)
        // Note: These are the 32-bit values, we're returning them as 64-bit
        assert_eq!(murmur3_hash(&"") as u32, 0);
        assert_eq!(murmur3_hash(&"a") as u32, 0xB269253C);
        assert_eq!(murmur3_hash(&"hello") as u32, 0x248BFA47);
    }
    
    #[test]
    fn test_hash_function_recommended() {
        assert_eq!(HashFunction::recommended_for_size(8), HashFunction::Fnv1a);
        assert_eq!(HashFunction::recommended_for_size(16), HashFunction::Fnv1a);
        assert_eq!(HashFunction::recommended_for_size(32), HashFunction::MurmurHash3);
    }
    
    #[test]
    fn test_hash_consistency() {
        // Ensure hash functions produce consistent results
        let key = "test_key";
        assert_eq!(HashFunction::Fnv1a.hash(&key), HashFunction::Fnv1a.hash(&key));
        assert_eq!(HashFunction::MurmurHash3.hash(&key), HashFunction::MurmurHash3.hash(&key));
        assert_eq!(HashFunction::Default.hash(&key), HashFunction::Default.hash(&key));
    }
} 