/*!
 * Binary Analysis Module
 * 
 * This module provides functionality for analyzing binary files,
 * extracting metadata, and identifying potential security issues.
 */


use crate::binary::{Binary, BinarySection, BinaryFormat};

/// Metadata extracted from a binary
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct BinaryMetadata {
    /// Basic information about the binary
    pub info: crate::binary::BinaryInfo,
    /// Number of sections
    pub section_count: usize,
    /// Total size of the binary
    pub total_size: u64,
    /// Size of code sections
    pub code_size: u64,
    /// Size of data sections
    pub data_size: u64,
    /// Number of imports
    pub import_count: usize,
    /// Number of exports
    pub export_count: usize,
    /// Additional format-specific metadata
    pub additional_info: HashMap<String, String>,
}

/// Extract metadata from a binary
pub fn extract_metadata(binary: &dyn Binary) -> BinaryMetadata {
    let info = binary.info();
    let sections = binary.sections();
    let imports = binary.imports();
    let exports = binary.exports();
    
    // Calculate sizes
    let total_size: u64 = sections.iter().map(|s| s.size).sum();
    let code_size: u64 = sections.iter().filter(|s| s.is_code).map(|s| s.size).sum();
    let data_size: u64 = sections.iter().filter(|s| s.is_data).map(|s| s.size).sum();
    
    // Create additional info based on format
    let mut additional_info = HashMap::new();
    match info.format {
        BinaryFormat::Elf => {
            // Add ELF-specific metadata
            if let Some(interp) = binary.get_section(".interp") {
                if let Some(data) = &interp.data {
                    if let Ok(interpreter) = std::str::from_utf8(&data[..data.len().saturating_sub(1)]) {
                        additional_info.insert("interpreter".to_string(), interpreter.to_string());
                    }
                }
            }
        },
        BinaryFormat::Pe => {
            // Add PE-specific metadata
            if let Some(rsrc) = binary.get_section(".rsrc") {
                additional_info.insert("has_resources".to_string(), "true".to_string());
            }
        },
        _ => {}
    }
    
    BinaryMetadata {
        info,
        section_count: sections.len(),
        total_size,
        code_size,
        data_size,
        import_count: imports.len(),
        export_count: exports.len(),
        additional_info,
    }
}

/// Security check result
#[derive(Debug, Clone)]
pub struct SecurityCheck {
    /// Name of the security check
    pub name: String,
    /// Description of the security check
    pub description: String,
    /// Whether the check passed (true) or failed (false)
    pub passed: bool,
    /// Additional details about the check result
    pub details: Option<String>,
}

/// Perform security checks on a binary
pub fn perform_security_checks(binary: &dyn Binary) -> Vec<SecurityCheck> {
    let mut checks = Vec::new();
    
    // Check for executable stack
    checks.push(check_executable_stack(binary));
    
    // Check for writable and executable sections
    checks.push(check_wx_sections(binary));
    
    // Format-specific checks
    match binary.format() {
        BinaryFormat::Elf => {
            // ELF-specific security checks
            checks.push(check_elf_relro(binary));
        },
        BinaryFormat::Pe => {
            // PE-specific security checks
            checks.push(check_pe_aslr(binary));
            checks.push(check_pe_dep(binary));
        },
        _ => {}
    }
    
    checks
}

/// Check if the stack is executable
fn check_executable_stack(binary: &dyn Binary) -> SecurityCheck {
    let mut passed = true;
    let mut details = None;
    
    if binary.format() == BinaryFormat::Elf {
        if let Some(stack) = binary.get_section(".stack") {
            if stack.is_executable {
                passed = false;
                details = Some("Stack section is marked as executable".to_string());
            }
        }
        
        // For ELF, also check for GNU_STACK program header
        // This would require more detailed ELF parsing
    }
    
    SecurityCheck {
        name: "Executable Stack".to_string(),
        description: "Checks if the stack is marked as executable, which is a security risk".to_string(),
        passed,
        details,
    }
}

/// Check for sections that are both writable and executable
fn check_wx_sections(binary: &dyn Binary) -> SecurityCheck {
    let sections = binary.sections();
    let wx_sections: Vec<&BinarySection> = sections.iter()
        .filter(|s| s.is_writable && s.is_executable)
        .collect();
    
    let passed = wx_sections.is_empty();
    let details = if !passed {
        let section_names: Vec<String> = wx_sections.iter()
            .map(|s| s.name.clone())
            .collect();
        Some(format!("Writable and executable sections found: {}", section_names.join(", ")))
    } else {
        None
    };
    
    SecurityCheck {
        name: "W^X Protection".to_string(),
        description: "Checks for sections that are both writable and executable".to_string(),
        passed,
        details,
    }
}

/// Check for RELRO (RELocation Read-Only) in ELF binaries
fn check_elf_relro(binary: &dyn Binary) -> SecurityCheck {
    // This is a simplified check and would require more detailed ELF parsing
    let has_relro = binary.get_section(".got.plt").is_some();
    
    SecurityCheck {
        name: "RELRO".to_string(),
        description: "Checks if RELRO (RELocation Read-Only) is enabled".to_string(),
        passed: has_relro,
        details: if has_relro {
            Some("RELRO appears to be enabled".to_string())
        } else {
            Some("RELRO does not appear to be enabled".to_string())
        },
    }
}

/// Check for ASLR (Address Space Layout Randomization) in PE binaries
fn check_pe_aslr(binary: &dyn Binary) -> SecurityCheck {
    // This is a simplified check and would require more detailed PE parsing
    // In a real implementation, we would check the DLL characteristics in the PE header
    
    SecurityCheck {
        name: "ASLR".to_string(),
        description: "Checks if ASLR (Address Space Layout Randomization) is enabled".to_string(),
        passed: true, // Placeholder
        details: Some("ASLR check requires detailed PE header analysis".to_string()),
    }
}

/// Check for DEP (Data Execution Prevention) in PE binaries
fn check_pe_dep(binary: &dyn Binary) -> SecurityCheck {
    // This is a simplified check and would require more detailed PE parsing
    // In a real implementation, we would check the DLL characteristics in the PE header
    
    SecurityCheck {
        name: "DEP/NX".to_string(),
        description: "Checks if DEP (Data Execution Prevention) is enabled".to_string(),
        passed: true, // Placeholder
        details: Some("DEP check requires detailed PE header analysis".to_string()),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::Path;
    
    
    use crate::binary::{load_binary, BinaryFormat};
    
    #[test]
    #[cfg(target_os = "linux")]
    fn test_extract_metadata_elf() {
        // This test will only run on Linux
        let path = Path::new("/bin/ls");
        if path.exists() {
            let binary = load_binary(path).unwrap();
            let metadata = extract_metadata(binary.as_ref());
            
            assert_eq!(metadata.info.format, BinaryFormat::Elf);
            assert!(metadata.section_count > 0);
            assert!(metadata.total_size > 0);
            assert!(metadata.code_size > 0);
        }
    }
    
    #[test]
    #[cfg(target_os = "linux")]
    fn test_security_checks_elf() {
        // This test will only run on Linux
        let path = Path::new("/bin/ls");
        if path.exists() {
            let binary = load_binary(path).unwrap();
            let checks = perform_security_checks(binary.as_ref());
            
            assert!(!checks.is_empty());
            
            // Find the W^X check
            let wx_check = checks.iter().find(|c| c.name == "W^X Protection");
            assert!(wx_check.is_some());
        }
    }
} 