/*!
 * Tests for Optimized Hash Map Implementation
 * 
 * This file contains integration tests for the optimized hash map implementation.
 */

#[cfg(test)]
mod tests {
    use crate::ebpf::maps::{
        OptimizedHashMap, TypedOptimizedHashMap, HashFunction, MapError
    };
    use std::sync::atomic::Ordering;
    
    use std::time::{Duration, Instant};
    
    #[test]
    fn test_optimized_hash_map_performance() {
        // Create a standard hash map and an optimized hash map
        let std_map = HashMap::new();
        let opt_map = OptimizedHashMap::create(8, 8, 10240, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test data
        let num_entries = 1000;
        let mut keys = Vec::with_capacity(num_entries);
        let mut values = Vec::with_capacity(num_entries);
        
        for i in 0..num_entries {
            let key = [
                (i & 0xFF) as u8,
                ((i >> 8) & 0xFF) as u8,
                ((i >> 16) & 0xFF) as u8,
                ((i >> 24) & 0xFF) as u8,
                0, 0, 0, 0
            ];
            let value = [
                ((i * 2) & 0xFF) as u8,
                (((i * 2) >> 8) & 0xFF) as u8,
                (((i * 2) >> 16) & 0xFF) as u8,
                (((i * 2) >> 24) & 0xFF) as u8,
                0, 0, 0, 0
            ];
            keys.push(key);
            values.push(value);
        }
        
        // Measure insert performance
        let start = Instant::now();
        for i in 0..num_entries {
            opt_map.update(&keys[i], &values[i], 0).unwrap();
        }
        let opt_insert_time = start.elapsed();
        
        // Measure lookup performance
        let start = Instant::now();
        for i in 0..num_entries {
            let _ = opt_map.lookup(&keys[i]).unwrap();
        }
        let opt_lookup_time = start.elapsed();
        
        // Measure batch lookup performance
        let key_refs: Vec<&[u8]> = keys.iter().map(|k| k.as_slice()).collect();
        let start = Instant::now();
        let results = opt_map.batch_lookup(&key_refs);
        let opt_batch_lookup_time = start.elapsed();
        
        // Verify batch lookup results
        for (i, result) in results.iter().enumerate() {
            assert!(result.is_ok());
            assert_eq!(result.as_ref().unwrap(), &values[i]);
        }
        
        // Print performance results
        println!("Optimized Hash Map Performance:");
        println!("  Insert time for {} entries: {:?}", num_entries, opt_insert_time);
        println!("  Lookup time for {} entries: {:?}", num_entries, opt_lookup_time);
        println!("  Batch lookup time for {} entries: {:?}", num_entries, opt_batch_lookup_time);
        println!("  Average insert time: {:?} per entry", opt_insert_time / num_entries as u32);
        println!("  Average lookup time: {:?} per entry", opt_lookup_time / num_entries as u32);
        println!("  Average batch lookup time: {:?} per entry", opt_batch_lookup_time / num_entries as u32);
        
        // Check statistics
        println!("Hash Map Statistics:");
        println!("  Lookups: {}", opt_map.stats().lookups.load(Ordering::Relaxed));
        println!("  Hits: {}", opt_map.stats().hits.load(Ordering::Relaxed));
        println!("  Misses: {}", opt_map.stats().misses.load(Ordering::Relaxed));
        println!("  Updates: {}", opt_map.stats().updates.load(Ordering::Relaxed));
        println!("  Hit rate: {:.2}%", opt_map.stats().hit_rate() * 100.0);
        
        // Verify statistics
        assert_eq!(opt_map.stats().lookups.load(Ordering::Relaxed), num_entries as u64 * 2); // Individual lookups + batch lookups
        assert_eq!(opt_map.stats().hits.load(Ordering::Relaxed), num_entries as u64 * 2);
        assert_eq!(opt_map.stats().updates.load(Ordering::Relaxed), num_entries as u64);
        assert!(opt_map.stats().hit_rate() > 0.99); // Should be very close to 1.0
    }
    
    #[test]
    fn test_typed_optimized_hash_map_performance() {
        // Create a typed optimized hash map
        let map = TypedOptimizedHashMap::<String, u32>::create(10240, Some(HashFunction::Fnv1a)).unwrap();
        
        // Test data
        let num_entries = 1000;
        let mut keys = Vec::with_capacity(num_entries);
        let mut values = Vec::with_capacity(num_entries);
        
        for i in 0..num_entries {
            keys.push(format!("key_{}", i));
            values.push(i as u32);
        }
        
        // Measure insert performance
        let start = Instant::now();
        for i in 0..num_entries {
            map.update(&keys[i], &values[i], 0).unwrap();
        }
        let insert_time = start.elapsed();
        
        // Measure lookup performance
        let start = Instant::now();
        for i in 0..num_entries {
            let result = map.lookup(&keys[i]).unwrap();
            assert_eq!(result, values[i]);
        }
        let lookup_time = start.elapsed();
        
        // Measure batch update performance
        let entries: Vec<(String, u32)> = keys.iter().zip(values.iter())
            .map(|(k, v)| (k.clone(), *v + 1000)) // Add 1000 to each value
            .collect();
            
        let start = Instant::now();
        map.batch_update(&entries, 0).unwrap();
        let batch_update_time = start.elapsed();
        
        // Measure batch lookup performance
        let start = Instant::now();
        let results = map.batch_lookup(&keys);
        let batch_lookup_time = start.elapsed();
        
        // Verify batch lookup results
        for (i, result) in results.iter().enumerate() {
            assert!(result.is_ok());
            assert_eq!(result.as_ref().unwrap(), &(values[i] + 1000));
        }
        
        // Print performance results
        println!("Typed Optimized Hash Map Performance:");
        println!("  Insert time for {} entries: {:?}", num_entries, insert_time);
        println!("  Lookup time for {} entries: {:?}", num_entries, lookup_time);
        println!("  Batch update time for {} entries: {:?}", num_entries, batch_update_time);
        println!("  Batch lookup time for {} entries: {:?}", num_entries, batch_lookup_time);
        println!("  Average insert time: {:?} per entry", insert_time / num_entries as u32);
        println!("  Average lookup time: {:?} per entry", lookup_time / num_entries as u32);
        println!("  Average batch update time: {:?} per entry", batch_update_time / num_entries as u32);
        println!("  Average batch lookup time: {:?} per entry", batch_lookup_time / num_entries as u32);
        
        // Check statistics
        println!("Hash Map Statistics:");
        println!("  Lookups: {}", map.stats().lookups.load(Ordering::Relaxed));
        println!("  Hits: {}", map.stats().hits.load(Ordering::Relaxed));
        println!("  Updates: {}", map.stats().updates.load(Ordering::Relaxed));
        println!("  Hit rate: {:.2}%", map.stats().hit_rate() * 100.0);
    }
    
    #[test]
    fn test_hash_function_comparison() {
        // Create test data
        let num_entries = 1000;
        let mut keys = Vec::with_capacity(num_entries);
        
        for i in 0..num_entries {
            keys.push(format!("key_{}", i));
        }
        
        // Test FNV-1a hash function
        let start = Instant::now();
        for key in &keys {
            let _ = HashFunction::Fnv1a.hash(key);
        }
        let fnv1a_time = start.elapsed();
        
        // Test MurmurHash3 hash function
        let start = Instant::now();
        for key in &keys {
            let _ = HashFunction::MurmurHash3.hash(key);
        }
        let murmur3_time = start.elapsed();
        
        // Test default hash function
        let start = Instant::now();
        for key in &keys {
            let _ = HashFunction::Default.hash(key);
        }
        let default_time = start.elapsed();
        
        // Print performance results
        println!("Hash Function Performance Comparison:");
        println!("  FNV-1a time for {} hashes: {:?}", num_entries, fnv1a_time);
        println!("  MurmurHash3 time for {} hashes: {:?}", num_entries, murmur3_time);
        println!("  Default time for {} hashes: {:?}", num_entries, default_time);
        println!("  Average FNV-1a time: {:?} per hash", fnv1a_time / num_entries as u32);
        println!("  Average MurmurHash3 time: {:?} per hash", murmur3_time / num_entries as u32);
        println!("  Average Default time: {:?} per hash", default_time / num_entries as u32);
    }
    
    #[test]
    fn test_error_handling() {
        // Create a map with a small maximum number of entries
        let map = OptimizedHashMap::create(8, 8, 2, Some(HashFunction::Fnv1a)).unwrap();
        
        // Insert two entries
        map.update(&[1, 2, 3, 4, 5, 6, 7, 8], &[1, 1, 1, 1, 1, 1, 1, 1], 0).unwrap();
        map.update(&[2, 3, 4, 5, 6, 7, 8, 9], &[2, 2, 2, 2, 2, 2, 2, 2], 0).unwrap();
        
        // Try to insert a third entry, which should fail because the map is full
        let result = map.update(&[3, 4, 5, 6, 7, 8, 9, 10], &[3, 3, 3, 3, 3, 3, 3, 3], 0);
        
        // Check that the error is of the expected type
        match result {
            Err(MapError::OperationError(_)) => {
                // This is the expected error type
                println!("Got expected error: {:?}", result);
            }
            _ => {
                panic!("Expected OperationError, got: {:?}", result);
            }
        }
        
        // Check that the error count was incremented
        assert_eq!(map.stats().errors.load(Ordering::Relaxed), 1);
    }
} 