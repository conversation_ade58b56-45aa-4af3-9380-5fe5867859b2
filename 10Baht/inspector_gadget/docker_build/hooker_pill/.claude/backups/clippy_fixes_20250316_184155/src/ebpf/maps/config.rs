/*!
 * eBPF Map Configuration
 * 
 * This module defines the configuration parameters for eBPF maps.
 */

use super::types::MapType;
use super::MapError;

/// eBPF map configuration
#[derive(Debug, Clone)]
pub struct MapConfig {
    /// Map type
    pub map_type: MapType,
    /// Key size in bytes
    pub key_size: usize,
    /// Value size in bytes
    pub value_size: usize,
    /// Maximum number of entries
    pub max_entries: usize,
    /// Map flags
    pub flags: u32,
}

impl MapConfig {
    /// Create a new map configuration
    pub fn new(
        map_type: MapType,
        key_size: usize,
        value_size: usize,
        max_entries: usize,
        flags: u32,
    ) -> Result<Self, MapError> {
        // Validate key size
        if key_size == 0 && !map_type.supports_zero_length_key() {
            return Err(MapError::InvalidArgument(format!(
                "Map type {:?} does not support zero-length keys",
                map_type
            )));
        }
        
        // Validate value size
        if value_size == 0 && !map_type.supports_zero_length_value() {
            return Err(MapError::InvalidArgument(format!(
                "Map type {:?} does not support zero-length values",
                map_type
            )));
        }
        
        // Validate max entries
        if max_entries == 0 {
            return Err(MapError::InvalidArgument(
                "Maximum number of entries must be greater than zero".to_string(),
            ));
        }
        
        Ok(Self {
            map_type,
            key_size,
            value_size,
            max_entries,
            flags,
        })
    }
    
    /// Create a new hash map configuration
    pub fn new_hash(key_size: usize, value_size: usize, max_entries: usize) -> Result<Self, MapError> {
        Self::new(MapType::Hash, key_size, value_size, max_entries, 0)
    }
    
    /// Create a new array map configuration
    pub fn new_array(value_size: usize, max_entries: usize) -> Result<Self, MapError> {
        Self::new(MapType::Array, 4, value_size, max_entries, 0)
    }
    
    /// Create a new per-CPU hash map configuration
    pub fn new_percpu_hash(key_size: usize, value_size: usize, max_entries: usize) -> Result<Self, MapError> {
        Self::new(MapType::PerCpuHash, key_size, value_size, max_entries, 0)
    }
    
    /// Create a new per-CPU array map configuration
    pub fn new_percpu_array(value_size: usize, max_entries: usize) -> Result<Self, MapError> {
        Self::new(MapType::PerCpuArray, 4, value_size, max_entries, 0)
    }
    
    /// Create a new LRU hash map configuration
    pub fn new_lru_hash(key_size: usize, value_size: usize, max_entries: usize) -> Result<Self, MapError> {
        Self::new(MapType::LruHash, key_size, value_size, max_entries, 0)
    }
    
    /// Create a new per-CPU LRU hash map configuration
    pub fn new_lru_percpu_hash(key_size: usize, value_size: usize, max_entries: usize) -> Result<Self, MapError> {
        Self::new(MapType::LruPerCpuHash, key_size, value_size, max_entries, 0)
    }
    
    /// Create a new ring buffer map configuration
    pub fn new_ringbuf(buffer_size: usize) -> Result<Self, MapError> {
        Self::new(MapType::RingBuf, 0, 0, buffer_size, 0)
    }
    
    /// Create a new stack map configuration
    pub fn new_stack(value_size: usize, max_entries: usize) -> Result<Self, MapError> {
        Self::new(MapType::Stack, 0, value_size, max_entries, 0)
    }
    
    /// Create a new queue map configuration
    pub fn new_queue(value_size: usize, max_entries: usize) -> Result<Self, MapError> {
        Self::new(MapType::Queue, 0, value_size, max_entries, 0)
    }
} 