/*!
 * Binary Module Tests
 * 
 * This module contains integration tests for the binary loading and parsing functionality.
 */

use super::*;
use std::path::Path;



#[test]
fn test_detect_format() {
    // Test ELF detection
    let elf_data = [0x7F, 0x45, 0x4C, 0x46, 0x02, 0x01, 0x01, 0x00];
    assert_eq!(detect_format(&elf_data).unwrap(), BinaryFormat::Elf);
    
    // Test PE detection
    let mut pe_data = vec![0; 0x100];
    pe_data[0] = 0x4D; // 'M'
    pe_data[1] = 0x5A; // 'Z'
    pe_data[0x3C] = 0x80; // PE header offset
    pe_data[0x80] = b'P';
    pe_data[0x81] = b'E';
    pe_data[0x82] = 0;
    pe_data[0x83] = 0;
    assert_eq!(detect_format(&pe_data).unwrap(), BinaryFormat::Pe);
    
    // Test unknown format
    let unknown_data = [0x00, 0x01, 0x02, 0x03];
    assert_eq!(detect_format(&unknown_data).unwrap(), BinaryFormat::Unknown);
}

#[test]
fn test_binary_info() {
    // Create a mock binary info
    let info = BinaryInfo {
        format: BinaryFormat::Elf,
        architecture: "x86_64".to_string(),
        is_64bit: true,
        is_executable: true,
        is_library: false,
        entry_point: 0x1000,
        path: Some("/bin/test".to_string()),
    };
    
    assert_eq!(info.format, BinaryFormat::Elf);
    assert_eq!(info.architecture, "x86_64");
    assert!(info.is_64bit);
    assert!(info.is_executable);
    assert!(!info.is_library);
    assert_eq!(info.entry_point, 0x1000);
    assert_eq!(info.path, Some("/bin/test".to_string()));
}

#[test]
fn test_binary_section() {
    // Create a mock binary section
    let section = BinarySection {
        name: ".text".to_string(),
        address: 0x1000,
        size: 0x500,
        is_executable: true,
        is_writable: false,
        is_code: true,
        is_data: false,
        data: Some(vec![0xDE, 0xAD, 0xBE, 0xEF]),
    };
    
    assert_eq!(section.name, ".text");
    assert_eq!(section.address, 0x1000);
    assert_eq!(section.size, 0x500);
    assert!(section.is_executable);
    assert!(!section.is_writable);
    assert!(section.is_code);
    assert!(!section.is_data);
    assert_eq!(section.data, Some(vec![0xDE, 0xAD, 0xBE, 0xEF]));
}

#[test]
#[cfg(target_os = "linux")]
fn test_load_binary_linux() {
    // This test will only run on Linux
    let path = Path::new("/bin/ls");
    if path.exists() {
        let binary = load_binary(path).unwrap();
        
        // Basic checks
        assert_eq!(binary.format(), BinaryFormat::Elf);
        assert!(binary.is_executable());
        assert!(!binary.is_library());
        assert!(binary.entry_point() > 0);
        
        // Check sections
        let sections = binary.sections();
        assert!(!sections.is_empty());
        
        // Check for common sections
        assert!(binary.get_section(".text").is_some());
        
        // Check imports and exports
        let imports = binary.imports();
        assert!(!imports.is_empty());
    }
}

#[test]
#[cfg(target_os = "windows")]
fn test_load_binary_windows() {
    // This test will only run on Windows
    let path = Path::new("C:\\Windows\\System32\\notepad.exe");
    if path.exists() {
        let binary = load_binary(path).unwrap();
        
        // Basic checks
        assert_eq!(binary.format(), BinaryFormat::Pe);
        assert!(binary.is_executable());
        assert!(!binary.is_library());
        assert!(binary.entry_point() > 0);
        
        // Check sections
        let sections = binary.sections();
        assert!(!sections.is_empty());
        
        // Check for common sections
        assert!(binary.get_section(".text").is_some() || 
               binary.get_section("CODE").is_some());
        
        // Check imports and exports
        let imports = binary.imports();
        assert!(!imports.is_empty());
    }
}

#[test]
fn test_binary_error() {
    // Test error conversion
    let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "File not found");
    let binary_error: BinaryError = io_error.into();
    
    match binary_error {
        BinaryError::Io(_) => {}, // Expected
        _ => panic!("Expected Io error variant"),
    }
    
    // Test error messages
    let parse_error = BinaryError::ParseError("Test error".to_string());
    assert!(parse_error.to_string().contains("Test error"));
    
    let unsupported_error = BinaryError::UnsupportedFormat;
    assert!(unsupported_error.to_string().contains("Unsupported"));
    
    let invalid_error = BinaryError::InvalidBinary;
    assert!(invalid_error.to_string().contains("Invalid"));
    
    let section_error = BinaryError::SectionNotFound(".text".to_string());
    assert!(section_error.to_string().contains(".text"));
} 