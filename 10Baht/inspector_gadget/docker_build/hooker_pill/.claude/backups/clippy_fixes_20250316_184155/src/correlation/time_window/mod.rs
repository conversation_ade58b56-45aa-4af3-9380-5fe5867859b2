//! Time window module for the correlation engine
//!
//! This module provides functionality for defining and managing time windows for event correlation.
//! It includes support for fixed time windows, sliding time windows, and session-based windows.

use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, Mutex, RwLock};


use crate::correlation::{CorrelationError, CorrelationResult, Event, EventValue};

/// Time window definition for correlating events over time
#[derive(Debug, Clone)]
pub struct TimeWindow {
    /// Unique identifier for the time window
    pub id: String,
    /// Name of the time window
    pub name: String,
    /// Description of the time window
    pub description: String,
    /// Type of the time window
    pub window_type: TimeWindowType,
    /// Duration of the time window (for fixed and sliding windows)
    pub duration: Option<Duration>,
    /// Slide interval for sliding windows
    pub slide_interval: Option<Duration>,
    /// Start trigger for session windows
    pub start_trigger: Option<TimeWindowTrigger>,
    /// End trigger for session windows
    pub end_trigger: Option<TimeWindowTrigger>,
    /// Maximum number of events to store in the window
    pub max_events: usize,
    /// Whether to discard events when the window is full
    pub discard_when_full: bool,
}

/// Type of time window
#[derive(Debug, Clone, PartialEq)]
pub enum TimeWindowType {
    /// Fixed time window (e.g., 5 minutes)
    Fixed,
    /// Sliding time window (e.g., last 5 minutes, sliding every 1 minute)
    Sliding,
    /// Session-based window (e.g., from login to logout)
    Session,
    /// Custom window with user-defined start and end triggers
    Custom,
}

/// Trigger for starting or ending a time window
#[derive(Debug, Clone)]
pub enum TimeWindowTrigger {
    /// Trigger based on an event type
    EventType(String),
    /// Trigger based on an event source
    EventSource(String),
    /// Trigger based on an event field value
    EventField {
        /// Field name
        field: String,
        /// Operator for comparison
        operator: TriggerOperator,
        /// Value to compare against
        value: EventValue,
    },
    /// Trigger based on a timeout
    Timeout(Duration),
    /// Trigger based on a schedule (e.g., every day at midnight)
    Schedule(String),
    /// Trigger based on a custom condition
    Custom(String),
}

/// Operator for trigger conditions
#[derive(Debug, Clone, PartialEq)]
pub enum TriggerOperator {
    /// Equal
    Equal,
    /// Not equal
    NotEqual,
    /// Greater than
    GreaterThan,
    /// Greater than or equal
    GreaterThanOrEqual,
    /// Less than
    LessThan,
    /// Less than or equal
    LessThanOrEqual,
    /// Contains (for strings and arrays)
    Contains,
    /// Starts with (for strings)
    StartsWith,
    /// Ends with (for strings)
    EndsWith,
    /// Matches a regular expression (for strings)
    Matches,
}

/// Instance of a time window
#[derive(Debug)]
struct TimeWindowInstance {
    /// Time window definition
    window: TimeWindow,
    /// Events in the window
    events: VecDeque<Event>,
    /// Start time of the window
    start_time: u64,
    /// End time of the window (None for open windows)
    end_time: Option<u64>,
    /// Whether the window is active
    active: bool,
    /// Metadata for the window
    metadata: HashMap<String, EventValue>,
}

/// Time window manager for managing time windows
pub struct TimeWindowManager {
    /// Time windows
    windows: Arc<RwLock<HashMap<String, TimeWindow>>>,
    /// Active window instances
    instances: Arc<Mutex<Vec<TimeWindowInstance>>>,
    /// Maximum number of window instances to track
    max_instances: usize,
}

impl TimeWindowManager {
    /// Create a new time window manager
    pub fn new(max_instances: usize) -> Self {
        Self {
            windows: Arc::new(RwLock::new(HashMap::new())),
            instances: Arc::new(Mutex::new(Vec::new())),
            max_instances,
        }
    }

    /// Add a time window to the manager
    pub fn add_window(&self, window: TimeWindow) -> CorrelationResult<()> {
        // Validate the window
        match window.window_type {
            TimeWindowType::Fixed | TimeWindowType::Sliding => {
                if window.duration.is_none() {
                    return Err(CorrelationError::TimeWindowError("Fixed and sliding windows require a duration".to_string()));
                }
                if window.window_type == TimeWindowType::Sliding && window.slide_interval.is_none() {
                    return Err(CorrelationError::TimeWindowError("Sliding windows require a slide interval".to_string()));
                }
            }
            TimeWindowType::Session | TimeWindowType::Custom => {
                if window.start_trigger.is_none() {
                    return Err(CorrelationError::TimeWindowError("Session and custom windows require a start trigger".to_string()));
                }
                if window.end_trigger.is_none() {
                    return Err(CorrelationError::TimeWindowError("Session and custom windows require an end trigger".to_string()));
                }
            }
        }

        // Add the window to the collection
        let mut windows = self.windows.write().map_err(|_| {
            CorrelationError::TimeWindowError("Failed to acquire write lock on windows".to_string())
        })?;

        windows.insert(window.id.clone(), window);

        Ok(())
    }

    /// Remove a time window from the manager
    pub fn remove_window(&self, window_id: &str) -> CorrelationResult<()> {
        let mut windows = self.windows.write().map_err(|_| {
            CorrelationError::TimeWindowError("Failed to acquire write lock on windows".to_string())
        })?;

        if windows.remove(window_id).is_none() {
            return Err(CorrelationError::TimeWindowError(format!("Time window with ID {} not found", window_id)));
        }

        // Remove any instances of this window
        let mut instances = self.instances.lock().map_err(|_| {
            CorrelationError::TimeWindowError("Failed to acquire lock on instances".to_string())
        })?;

        instances.retain(|instance| instance.window.id != window_id);

        Ok(())
    }

    /// Process an event through the time window manager
    pub fn process_event(&self, event: &Event) -> CorrelationResult<Vec<TimeWindowEvent>> {
        let mut window_events = Vec::new();

        // Get a read lock on the windows
        let windows = self.windows.read().map_err(|_| {
            CorrelationError::TimeWindowError("Failed to acquire read lock on windows".to_string())
        })?;

        // Get a lock on the instances
        let mut instances = self.instances.lock().map_err(|_| {
            CorrelationError::TimeWindowError("Failed to acquire lock on instances".to_string())
        })?;

        // Process fixed and sliding windows
        for (_, window) in windows.iter() {
            match window.window_type {
                TimeWindowType::Fixed => {
                    // Find or create an instance for this window
                    let instance_index = instances.iter().position(|instance| instance.window.id == window.id);
                    
                    if let Some(index) = instance_index {
                        let instance = &mut instances[index];
                        
                        // Check if the window has expired
                        if let Some(duration) = window.duration {
                            let end_time = instance.start_time + duration.as_nanos() as u64;
                            if event.timestamp >= end_time {
                                // Window has expired, create a new one
                                let window_event = TimeWindowEvent {
                                    window_id: window.id.clone(),
                                    window_name: window.name.clone(),
                                    events: instance.events.iter().cloned().collect(),
                                    start_time: instance.start_time,
                                    end_time,
                                    metadata: instance.metadata.clone(),
                                };
                                
                                window_events.push(window_event);
                                
                                // Create a new instance
                                instance.events.clear();
                                instance.start_time = event.timestamp;
                                instance.end_time = Some(event.timestamp + duration.as_nanos() as u64);
                                instance.active = true;
                            }
                        }
                        
                        // Add the event to the window
                        if instance.active {
                            if instance.events.len() < window.max_events {
                                instance.events.push_back(event.clone());
                            } else if !window.discard_when_full {
                                // Remove the oldest event
                                instance.events.pop_front();
                                instance.events.push_back(event.clone());
                            }
                        }
                    } else {
                        // Create a new instance
                        let mut events = VecDeque::new();
                        events.push_back(event.clone());
                        
                        let duration = window.duration.unwrap_or_else(|| Duration::from_secs(0));
                        let end_time = event.timestamp + duration.as_nanos() as u64;
                        
                        let instance = TimeWindowInstance {
                            window: window.clone(),
                            events,
                            start_time: event.timestamp,
                            end_time: Some(end_time),
                            active: true,
                            metadata: HashMap::new(),
                        };
                        
                        if instances.len() < self.max_instances {
                            instances.push(instance);
                        } else {
                            // If we've reached the maximum number of instances, replace the oldest one
                            if let Some(oldest) = instances.iter().enumerate().min_by_key(|(_, instance)| instance.start_time) {
                                instances[oldest.0] = instance;
                            }
                        }
                    }
                }
                TimeWindowType::Sliding => {
                    // Find or create instances for this window
                    let instance_indices: Vec<usize> = instances.iter()
                        .enumerate()
                        .filter(|(_, instance)| instance.window.id == window.id)
                        .map(|(i, _)| i)
                        .collect();
                    
                    if !instance_indices.is_empty() {
                        // Process existing instances
                        for &index in &instance_indices {
                            let instance = &mut instances[index];
                            
                            // Check if the window has expired
                            if let Some(end_time) = instance.end_time {
                                if event.timestamp >= end_time {
                                    // Window has expired
                                    instance.active = false;
                                    
                                    let window_event = TimeWindowEvent {
                                        window_id: window.id.clone(),
                                        window_name: window.name.clone(),
                                        events: instance.events.iter().cloned().collect(),
                                        start_time: instance.start_time,
                                        end_time,
                                        metadata: instance.metadata.clone(),
                                    };
                                    
                                    window_events.push(window_event);
                                }
                            }
                            
                            // Add the event to active windows
                            if instance.active {
                                if instance.events.len() < window.max_events {
                                    instance.events.push_back(event.clone());
                                } else if !window.discard_when_full {
                                    // Remove the oldest event
                                    instance.events.pop_front();
                                    instance.events.push_back(event.clone());
                                }
                            }
                        }
                        
                        // Create a new instance if it's time to slide
                        if let Some(slide_interval) = window.slide_interval {
                            let duration = window.duration.unwrap_or_else(|| Duration::from_secs(0));
                            let latest_instance = instances.iter()
                                .filter(|instance| instance.window.id == window.id)
                                .max_by_key(|instance| instance.start_time);
                            
                            if let Some(latest) = latest_instance {
                                let next_start_time = latest.start_time + slide_interval.as_nanos() as u64;
                                if event.timestamp >= next_start_time {
                                    // Time to create a new sliding window
                                    let mut events = VecDeque::new();
                                    events.push_back(event.clone());
                                    
                                    let end_time = event.timestamp + duration.as_nanos() as u64;
                                    
                                    let instance = TimeWindowInstance {
                                        window: window.clone(),
                                        events,
                                        start_time: event.timestamp,
                                        end_time: Some(end_time),
                                        active: true,
                                        metadata: HashMap::new(),
                                    };
                                    
                                    if instances.len() < self.max_instances {
                                        instances.push(instance);
                                    } else {
                                        // If we've reached the maximum number of instances, replace the oldest one
                                        if let Some(oldest) = instances.iter().enumerate()
                                            .filter(|(_, instance)| !instance.active)
                                            .min_by_key(|(_, instance)| instance.start_time) {
                                            instances[oldest.0] = instance;
                                        } else if let Some(oldest) = instances.iter().enumerate()
                                            .min_by_key(|(_, instance)| instance.start_time) {
                                            instances[oldest.0] = instance;
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        // Create the first instance
                        let mut events = VecDeque::new();
                        events.push_back(event.clone());
                        
                        let duration = window.duration.unwrap_or_else(|| Duration::from_secs(0));
                        let end_time = event.timestamp + duration.as_nanos() as u64;
                        
                        let instance = TimeWindowInstance {
                            window: window.clone(),
                            events,
                            start_time: event.timestamp,
                            end_time: Some(end_time),
                            active: true,
                            metadata: HashMap::new(),
                        };
                        
                        if instances.len() < self.max_instances {
                            instances.push(instance);
                        } else {
                            // If we've reached the maximum number of instances, replace the oldest one
                            if let Some(oldest) = instances.iter().enumerate().min_by_key(|(_, instance)| instance.start_time) {
                                instances[oldest.0] = instance;
                            }
                        }
                    }
                }
                TimeWindowType::Session | TimeWindowType::Custom => {
                    // Find instances for this window
                    let instance_indices: Vec<usize> = instances.iter()
                        .enumerate()
                        .filter(|(_, instance)| instance.window.id == window.id && instance.active)
                        .map(|(i, _)| i)
                        .collect();
                    
                    // Check if any existing instances should end
                    for &index in &instance_indices {
                        let instance = &mut instances[index];
                        
                        if let Some(end_trigger) = &window.end_trigger {
                            if self.evaluate_trigger(end_trigger, event)? {
                                // Session has ended
                                instance.active = false;
                                instance.end_time = Some(event.timestamp);
                                
                                let window_event = TimeWindowEvent {
                                    window_id: window.id.clone(),
                                    window_name: window.name.clone(),
                                    events: instance.events.iter().cloned().collect(),
                                    start_time: instance.start_time,
                                    end_time: instance.end_time.unwrap(),
                                    metadata: instance.metadata.clone(),
                                };
                                
                                window_events.push(window_event);
                            } else {
                                // Add the event to the active session
                                if instance.events.len() < window.max_events {
                                    instance.events.push_back(event.clone());
                                } else if !window.discard_when_full {
                                    // Remove the oldest event
                                    instance.events.pop_front();
                                    instance.events.push_back(event.clone());
                                }
                            }
                        }
                    }
                    
                    // Check if a new session should start
                    if let Some(start_trigger) = &window.start_trigger {
                        if self.evaluate_trigger(start_trigger, event)? {
                            // Start a new session
                            let mut events = VecDeque::new();
                            events.push_back(event.clone());
                            
                            let instance = TimeWindowInstance {
                                window: window.clone(),
                                events,
                                start_time: event.timestamp,
                                end_time: None,
                                active: true,
                                metadata: HashMap::new(),
                            };
                            
                            if instances.len() < self.max_instances {
                                instances.push(instance);
                            } else {
                                // If we've reached the maximum number of instances, replace the oldest inactive one
                                if let Some(oldest) = instances.iter().enumerate()
                                    .filter(|(_, instance)| !instance.active)
                                    .min_by_key(|(_, instance)| instance.start_time) {
                                    instances[oldest.0] = instance;
                                } else if let Some(oldest) = instances.iter().enumerate()
                                    .min_by_key(|(_, instance)| instance.start_time) {
                                    instances[oldest.0] = instance;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Clean up expired windows
        instances.retain(|instance| instance.active || instance.end_time.is_none() || instance.end_time.unwrap() > event.timestamp - Duration::from_secs(3600).as_nanos() as u64);
        
        Ok(window_events)
    }
    
    /// Evaluate a trigger condition against an event
    fn evaluate_trigger(&self, trigger: &TimeWindowTrigger, event: &Event) -> CorrelationResult<bool> {
        match trigger {
            TimeWindowTrigger::EventType(event_type) => Ok(event.event_type == *event_type),
            TimeWindowTrigger::EventSource(source) => Ok(event.source == *source),
            TimeWindowTrigger::EventField { field, operator, value } => {
                // Get the field value from the event
                let field_value = event.data.fields.get(field).ok_or_else(|| {
                    CorrelationError::TimeWindowError(format!("Field {} not found in event", field))
                })?;
                
                // Compare the values
                self.compare_values(field_value, value, operator)
            }
            TimeWindowTrigger::Timeout(_) => {
                // Timeout triggers are handled by the time window manager
                Ok(false)
            }
            TimeWindowTrigger::Schedule(schedule) => {
                // TODO: Implement schedule evaluation
                Ok(false)
            }
            TimeWindowTrigger::Custom(custom) => {
                // TODO: Implement custom trigger evaluation
                Ok(false)
            }
        }
    }
    
    /// Compare two values using an operator
    fn compare_values(&self, left: &EventValue, right: &EventValue, operator: &TriggerOperator) -> CorrelationResult<bool> {
        match operator {
            TriggerOperator::Equal => Ok(self.values_equal(left, right)),
            TriggerOperator::NotEqual => Ok(!self.values_equal(left, right)),
            TriggerOperator::GreaterThan => self.compare_ordered_values(left, right, |a, b| a > b),
            TriggerOperator::GreaterThanOrEqual => self.compare_ordered_values(left, right, |a, b| a >= b),
            TriggerOperator::LessThan => self.compare_ordered_values(left, right, |a, b| a < b),
            TriggerOperator::LessThanOrEqual => self.compare_ordered_values(left, right, |a, b| a <= b),
            TriggerOperator::Contains => {
                match (left, right) {
                    (EventValue::String(l), EventValue::String(r)) => Ok(l.contains(r)),
                    (EventValue::Array(l), r) => Ok(l.iter().any(|item| self.values_equal(item, r))),
                    _ => Err(CorrelationError::TimeWindowError(format!("Contains operator not supported for {:?} and {:?}", left, right))),
                }
            }
            TriggerOperator::StartsWith => {
                match (left, right) {
                    (EventValue::String(l), EventValue::String(r)) => Ok(l.starts_with(r)),
                    _ => Err(CorrelationError::TimeWindowError(format!("StartsWith operator not supported for {:?} and {:?}", left, right))),
                }
            }
            TriggerOperator::EndsWith => {
                match (left, right) {
                    (EventValue::String(l), EventValue::String(r)) => Ok(l.ends_with(r)),
                    _ => Err(CorrelationError::TimeWindowError(format!("EndsWith operator not supported for {:?} and {:?}", left, right))),
                }
            }
            TriggerOperator::Matches => {
                match (left, right) {
                    (EventValue::String(l), EventValue::String(r)) => {
                        // Use regex to match
                        let regex = regex::Regex::new(r).map_err(|e| {
                            CorrelationError::TimeWindowError(format!("Invalid regex: {}", e))
                        })?;
                        Ok(regex.is_match(l))
                    }
                    _ => Err(CorrelationError::TimeWindowError(format!("Matches operator not supported for {:?} and {:?}", left, right))),
                }
            }
        }
    }
    
    /// Check if two values are equal
    fn values_equal(&self, left: &EventValue, right: &EventValue) -> bool {
        match (left, right) {
            (EventValue::String(l), EventValue::String(r)) => l == r,
            (EventValue::Integer(l), EventValue::Integer(r)) => l == r,
            (EventValue::Float(l), EventValue::Float(r)) => l == r,
            (EventValue::Boolean(l), EventValue::Boolean(r)) => l == r,
            (EventValue::Array(l), EventValue::Array(r)) => {
                if l.len() != r.len() {
                    return false;
                }
                for (l_item, r_item) in l.iter().zip(r.iter()) {
                    if !self.values_equal(l_item, r_item) {
                        return false;
                    }
                }
                true
            }
            (EventValue::Object(l), EventValue::Object(r)) => {
                if l.len() != r.len() {
                    return false;
                }
                for (key, l_value) in l {
                    match r.get(key) {
                        Some(r_value) => {
                            if !self.values_equal(l_value, r_value) {
                                return false;
                            }
                        }
                        None => return false,
                    }
                }
                true
            }
            (EventValue::Null, EventValue::Null) => true,
            _ => false,
        }
    }
    
    /// Compare two values using a comparison function
    fn compare_ordered_values<F>(&self, left: &EventValue, right: &EventValue, compare: F) -> CorrelationResult<bool>
    where
        F: Fn(&dyn std::cmp::PartialOrd, &dyn std::cmp::PartialOrd) -> bool,
    {
        match (left, right) {
            (EventValue::Integer(l), EventValue::Integer(r)) => Ok(compare(l, r)),
            (EventValue::Float(l), EventValue::Float(r)) => Ok(compare(l, r)),
            (EventValue::String(l), EventValue::String(r)) => Ok(compare(l, r)),
            _ => Err(CorrelationError::TimeWindowError(format!("Cannot compare {:?} and {:?}", left, right))),
        }
    }
}

/// Result of a time window event
#[derive(Debug, Clone)]
pub struct TimeWindowEvent {
    /// ID of the time window
    pub window_id: String,
    /// Name of the time window
    pub window_name: String,
    /// Events in the window
    pub events: Vec<Event>,
    /// Start time of the window
    pub start_time: u64,
    /// End time of the window
    pub end_time: u64,
    /// Metadata for the window
    pub metadata: HashMap<String, EventValue>,
} 