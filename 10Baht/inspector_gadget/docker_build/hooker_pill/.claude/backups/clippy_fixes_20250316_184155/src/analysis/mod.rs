/*!
 * Analysis functionality for Inspector Gadget
 * 
 * This module contains tools for analyzing binary behavior.
 */

use std::collections::{HashMap, HashSet};
use crate::core::{TraceEvent, EventType};
use crate::error::Result;
use crate::syscalls::{Syscall, SyscallCategory};

pub mod patterns;
pub mod reports;
pub mod statistics;

/// Analyzer for binary behavior
pub struct BehaviorAnalyzer {
    /// Events collected during analysis
    events: Vec<TraceEvent>,
    /// Unique syscalls observed
    syscalls: HashSet<Syscall>,
    /// File paths accessed
    file_paths: HashSet<String>,
    /// Network connections
    network_connections: HashSet<String>,
    /// Process executions
    process_executions: HashSet<String>,
    /// Syscall frequency
    syscall_frequency: HashMap<Syscall, usize>,
}

impl BehaviorAnalyzer {
    /// Create a new behavior analyzer
    pub fn new() -> Self {
        BehaviorAnalyzer {
            events: Vec::new(),
            syscalls: HashSet::new(),
            file_paths: HashSet::new(),
            network_connections: HashSet::new(),
            process_executions: HashSet::new(),
            syscall_frequency: HashMap::new(),
        }
    }
    
    /// Add an event to the analyzer
    pub fn add_event(&mut self, event: TraceEvent) {
        match &event.event_type {
            EventType::Syscall => {
                if let EventData::Syscall(syscall_event) = &event.data {
                    self.syscalls.insert(syscall_event.syscall.clone());
                    *self.syscall_frequency.entry(syscall_event.syscall.clone()).or_insert(0) += 1;
                }
            },
            EventType::FileOperation => {
                if let EventData::FileOperation { path, .. } = &event.data {
                    self.file_paths.insert(path.clone());
                }
            },
            EventType::NetworkActivity => {
                if let EventData::NetworkActivity { destination, .. } = &event.data {
                    self.network_connections.insert(destination.clone());
                }
            },
            EventType::ProcessExecution => {
                if let EventData::ProcessExecution { command, .. } = &event.data {
                    self.process_executions.insert(command.clone());
                }
            },
            _ => {}
        }
        
        self.events.push(event);
    }
    
    /// Get all events
    pub fn events(&self) -> &[TraceEvent] {
        &self.events
    }
    
    /// Get unique syscalls
    pub fn syscalls(&self) -> &HashSet<Syscall> {
        &self.syscalls
    }
    
    /// Get file paths accessed
    pub fn file_paths(&self) -> &HashSet<String> {
        &self.file_paths
    }
    
    /// Get network connections
    pub fn network_connections(&self) -> &HashSet<String> {
        &self.network_connections
    }
    
    /// Get process executions
    pub fn process_executions(&self) -> &HashSet<String> {
        &self.process_executions
    }
    
    /// Get syscall frequency
    pub fn syscall_frequency(&self) -> &HashMap<Syscall, usize> {
        &self.syscall_frequency
    }
    
    /// Get syscalls by category
    pub fn syscalls_by_category(&self) -> HashMap<SyscallCategory, Vec<Syscall>> {
        let mut result = HashMap::new();
        
        for syscall in &self.syscalls {
            result.entry(syscall.category()).or_insert_with(Vec::new).push(syscall.clone());
        }
        
        result
    }
    
    /// Generate a summary report
    pub fn generate_summary(&self) -> String {
        let mut summary = String::new();
        
        summary.push_str(&format!("Total events: {}\n", self.events.len()));
        summary.push_str(&format!("Unique syscalls: {}\n", self.syscalls.len()));
        summary.push_str(&format!("File paths accessed: {}\n", self.file_paths.len()));
        summary.push_str(&format!("Network connections: {}\n", self.network_connections.len()));
        summary.push_str(&format!("Process executions: {}\n", self.process_executions.len()));
        
        if !self.syscalls.is_empty() {
            summary.push_str("\nTop 5 syscalls by frequency:\n");
            
            let mut syscalls: Vec<(&Syscall, &usize)> = self.syscall_frequency.iter().collect();
            syscalls.sort_by(|a, b| b.1.cmp(a.1));
            
            for (i, (syscall, count)) in syscalls.iter().take(5).enumerate() {
                summary.push_str(&format!("{}. {} - {} calls\n", i + 1, syscall.name(), count));
            }
        }
        
        summary
    }
    
    /// Detect suspicious behavior
    pub fn detect_suspicious_behavior(&self) -> Vec<String> {
        let mut suspicious = Vec::new();
        
        // Check for sensitive file access
        let sensitive_paths = ["/etc/passwd", "/etc/shadow", "C:\\Windows\\System32\\config"];
        for path in &self.file_paths {
            for sensitive in &sensitive_paths {
                if path.contains(sensitive) {
                    suspicious.push(format!("Accessed sensitive file: {}", path));
                }
            }
        }
        
        // Check for network connections to suspicious ports
        for conn in &self.network_connections {
            if conn.contains(":4444") || conn.contains(":8080") {
                suspicious.push(format!("Suspicious network connection: {}", conn));
            }
        }
        
        // Check for suspicious process executions
        let suspicious_commands = ["nc ", "netcat", "powershell -e", "bash -i"];
        for cmd in &self.process_executions {
            for susp in &suspicious_commands {
                if cmd.contains(susp) {
                    suspicious.push(format!("Suspicious command execution: {}", cmd));
                }
            }
        }
        
        suspicious
    }
}

/// Event data for analysis
#[derive(Debug, Clone)]
pub enum EventData {
    /// System call event data
    Syscall(crate::syscalls::SyscallEvent),
    /// File operation event data
    FileOperation {
        /// Path of the file
        path: String,
        /// Operation type (read, write, etc.)
        operation: String,
        /// Result of the operation
        result: i32,
    },
    /// Network activity event data
    NetworkActivity {
        /// Source address
        source: String,
        /// Destination address
        destination: String,
        /// Protocol used
        protocol: String,
        /// Amount of data transferred
        bytes: usize,
    },
    /// Process execution event data
    ProcessExecution {
        /// Process ID
        pid: u32,
        /// Parent process ID
        ppid: u32,
        /// Command line
        command: String,
        /// Environment variables
        environment: Vec<String>,
    },
    /// Memory operation event data
    MemoryOperation {
        /// Address of the memory operation
        address: u64,
        /// Size of the memory operation
        size: usize,
        /// Type of memory operation (allocate, free, etc.)
        operation: String,
    },
    /// Custom event data
    Custom(serde_json::Value),
} 