use crate::ebpf::maps::TypedOptimizedPerCpuMap;
use crate::hookers::xdp_hooker::{<PERSON>d<PERSON><PERSON><PERSON><PERSON>, Xdp<PERSON><PERSON>, XdpHookerConfig};
use serde::{Serialize, Deserialize};
use std::error::Error;

use std::time::{Duration, Instant};


/// Statistics for XDP events by network interface
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct XdpEventStats {
    /// Number of packets passed
    pub pass_count: u64,
    /// Number of packets dropped
    pub drop_count: u64,
    /// Number of packets redirected
    pub redirect_count: u64,
    /// Number of packets with TX action
    pub tx_count: u64,
    /// Number of packets with ABORTED action
    pub aborted_count: u64,
    /// Total bytes processed
    pub bytes_processed: u64,
    /// Last event timestamp
    pub last_event_timestamp: u64,
}

impl Default for XdpEventStats {
    fn default() -> Self {
        Self {
            pass_count: 0,
            drop_count: 0,
            redirect_count: 0,
            tx_count: 0,
            aborted_count: 0,
            bytes_processed: 0,
            last_event_timestamp: 0,
        }
    }
}

impl std::ops::Add for XdpEventStats {
    type Output = Self;
    
    fn add(self, other: Self) -> Self {
        Self {
            pass_count: self.pass_count + other.pass_count,
            drop_count: self.drop_count + other.drop_count,
            redirect_count: self.redirect_count + other.redirect_count,
            tx_count: self.tx_count + other.tx_count,
            aborted_count: self.aborted_count + other.aborted_count,
            bytes_processed: self.bytes_processed + other.bytes_processed,
            last_event_timestamp: std::cmp::max(self.last_event_timestamp, other.last_event_timestamp),
        }
    }
}

/// XDP hooker with Per-CPU Maps for improved performance
pub struct XdpPerCpuHooker {
    /// Inner XDP hooker
    inner: XdpHooker,
    /// Map to store event statistics by interface index
    interface_stats: Arc<TypedOptimizedPerCpuMap<u32, XdpEventStats>>,
    /// Map to store event statistics by source IP (as u32)
    source_ip_stats: Arc<TypedOptimizedPerCpuMap<u32, XdpEventStats>>,
    /// Map to store event statistics by destination port
    dest_port_stats: Arc<TypedOptimizedPerCpuMap<u16, XdpEventStats>>,
    /// Last statistics export time
    last_export_time: Instant,
    /// Statistics export interval
    export_interval: Duration,
}

impl XdpPerCpuHooker {
    /// Creates a new XDP hooker with Per-CPU Maps
    pub fn new(config: XdpHookerConfig) -> Result<Self, Box<dyn Error>> {
        // Create the inner XDP hooker
        let inner = XdpHooker::new(config)?;
        
        // Create the Per-CPU Maps
        let interface_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, XdpEventStats>::create(256)?);
        let source_ip_stats = Arc::new(TypedOptimizedPerCpuMap::<u32, XdpEventStats>::create(10240)?);
        let dest_port_stats = Arc::new(TypedOptimizedPerCpuMap::<u16, XdpEventStats>::create(65536)?);
        
        Ok(Self {
            inner,
            interface_stats,
            source_ip_stats,
            dest_port_stats,
            last_export_time: Instant::now(),
            export_interval: Duration::from_secs(60),
        })
    }
    
    /// Handles an XDP event
    pub fn handle_event(&self, event: &XdpEvent) -> Result<(), Box<dyn Error>> {
        // Process the event with the inner hooker
        self.inner.handle_event(event)?;
        
        // Update statistics in the Per-CPU Maps
        self.update_event_stats(event)?;
        
        // Export statistics if the export interval has elapsed
        if self.last_export_time.elapsed() >= self.export_interval {
            self.export_statistics()?;
        }
        
        Ok(())
    }
    
    /// Updates event statistics in the Per-CPU Maps
    fn update_event_stats(&self, event: &XdpEvent) -> Result<(), Box<dyn Error>> {
        // Get the current timestamp
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Update interface statistics
        let if_index = event.interface_index;
        let mut if_stats = self.interface_stats.lookup(if_index).unwrap_or_default();
        
        // Update source IP statistics if available
        if let Some(src_ip) = event.source_ip {
            let mut src_ip_stats = self.source_ip_stats.lookup(src_ip).unwrap_or_default();
            self.update_stats_for_event(&mut src_ip_stats, event, timestamp);
            self.source_ip_stats.update(src_ip, src_ip_stats)?;
        }
        
        // Update destination port statistics if available
        if let Some(dest_port) = event.destination_port {
            let mut dest_port_stats = self.dest_port_stats.lookup(dest_port).unwrap_or_default();
            self.update_stats_for_event(&mut dest_port_stats, event, timestamp);
            self.dest_port_stats.update(dest_port, dest_port_stats)?;
        }
        
        // Update the interface statistics
        self.update_stats_for_event(&mut if_stats, event, timestamp);
        self.interface_stats.update(if_index, if_stats)?;
        
        Ok(())
    }
    
    /// Helper method to update statistics based on the event
    fn update_stats_for_event(&self, stats: &mut XdpEventStats, event: &XdpEvent, timestamp: u64) {
        // Update the appropriate counters based on XDP action
        match event.action.as_str() {
            "PASS" => stats.pass_count += 1,
            "DROP" => stats.drop_count += 1,
            "REDIRECT" => stats.redirect_count += 1,
            "TX" => stats.tx_count += 1,
            "ABORTED" => stats.aborted_count += 1,
            _ => {}
        }
        
        // Update bytes processed
        stats.bytes_processed += event.packet_size as u64;
        
        // Update timestamp
        stats.last_event_timestamp = timestamp;
    }
    
    /// Exports statistics to external systems (e.g., Elasticsearch)
    fn export_statistics(&self) -> Result<(), Box<dyn Error>> {
        // This would be implemented to export statistics to external systems
        // For now, we'll just log some aggregated statistics
        
        // Get top 5 interfaces by total packet count
        let mut interfaces = Vec::new();
        for if_index in 1..256 {
            if let Some(stats) = self.interface_stats.lookup_aggregated(if_index) {
                let total_packets = stats.pass_count
                    + stats.drop_count
                    + stats.redirect_count
                    + stats.tx_count
                    + stats.aborted_count;
                
                if total_packets > 0 {
                    interfaces.push((if_index, total_packets, stats));
                }
            }
        }
        
        // Sort by total packet count
        interfaces.sort_by(|a, b| b.1.cmp(&a.1));
        
        // Log the top 5 interfaces
        info!("Top interfaces by packet count:");
        for (i, (if_index, total, stats)) in interfaces.iter().take(5).enumerate() {
            info!(
                "{}. Interface {}: {} packets ({} pass, {} drop, {} redirect, {} tx, {} aborted), {} bytes",
                i + 1,
                if_index,
                total,
                stats.pass_count,
                stats.drop_count,
                stats.redirect_count,
                stats.tx_count,
                stats.aborted_count,
                stats.bytes_processed
            );
        }
        
        // Get top 5 source IPs by drop count
        let mut source_ips = Vec::new();
        for src_ip in 0..10240 {
            if let Some(stats) = self.source_ip_stats.lookup_aggregated(src_ip) {
                if stats.drop_count > 0 {
                    source_ips.push((src_ip, stats.drop_count, stats));
                }
            }
        }
        
        // Sort by drop count
        source_ips.sort_by(|a, b| b.1.cmp(&a.1));
        
        // Log the top 5 source IPs
        info!("Top source IPs by drop count:");
        for (i, (src_ip, drops, stats)) in source_ips.iter().take(5).enumerate() {
            // Convert u32 IP to string representation
            let ip_str = format!(
                "{}.{}.{}.{}",
                (src_ip >> 24) & 0xFF,
                (src_ip >> 16) & 0xFF,
                (src_ip >> 8) & 0xFF,
                src_ip & 0xFF
            );
            
            info!(
                "{}. IP {}: {} drops (out of {} total packets)",
                i + 1,
                ip_str,
                drops,
                stats.pass_count
                    + stats.drop_count
                    + stats.redirect_count
                    + stats.tx_count
                    + stats.aborted_count
            );
        }
        
        Ok(())
    }
    
    /// Gets aggregated statistics for an interface
    pub fn get_interface_stats(&self, if_index: u32) -> Option<XdpEventStats> {
        self.interface_stats.lookup_aggregated(if_index)
    }
    
    /// Gets aggregated statistics for a source IP
    pub fn get_source_ip_stats(&self, src_ip: u32) -> Option<XdpEventStats> {
        self.source_ip_stats.lookup_aggregated(src_ip)
    }
    
    /// Gets aggregated statistics for a destination port
    pub fn get_dest_port_stats(&self, dest_port: u16) -> Option<XdpEventStats> {
        self.dest_port_stats.lookup_aggregated(dest_port)
    }
    
    /// Gets the inner XDP hooker
    pub fn inner(&self) -> &XdpHooker {
        &self.inner
    }
    
    /// Gets map statistics
    pub fn get_map_stats(&self) -> (u64, u64, u64) {
        let interface_stats = self.interface_stats.get_stats();
        let source_ip_stats = self.source_ip_stats.get_stats();
        let dest_port_stats = self.dest_port_stats.get_stats();
        
        let lookups = interface_stats.lookups.load(std::sync::atomic::Ordering::Relaxed)
            + source_ip_stats.lookups.load(std::sync::atomic::Ordering::Relaxed)
            + dest_port_stats.lookups.load(std::sync::atomic::Ordering::Relaxed);
            
        let updates = interface_stats.updates.load(std::sync::atomic::Ordering::Relaxed)
            + source_ip_stats.updates.load(std::sync::atomic::Ordering::Relaxed)
            + dest_port_stats.updates.load(std::sync::atomic::Ordering::Relaxed);
            
        let misses = interface_stats.misses.load(std::sync::atomic::Ordering::Relaxed)
            + source_ip_stats.misses.load(std::sync::atomic::Ordering::Relaxed)
            + dest_port_stats.misses.load(std::sync::atomic::Ordering::Relaxed);
            
        (lookups, updates, misses)
    }
} 