/*!
 * eBPF Maps Support
 * 
 * This module provides functionality for creating and managing eBPF maps,
 * which enable data sharing between eBPF programs in the kernel and
 * applications in user space.
 */

use std::marker::PhantomData;
use std::path::Path;
use std::fs;
use std::io;
use std::sync::{Arc, Mutex};


use serde::{Serialize, de::DeserializeOwned};


use std::error::Error;

#[cfg(feature = "linux")]
use libbpf_rs::{MapFlags, Map as LibbpfMap, MapType as LibbpfMapType, OpenMap};

// Submodules
mod types;
mod config;
mod builder;
mod iter;
mod typed;
mod hash;
mod hashmap;
mod lrumap;
pub mod percpumap;
mod typed_optimized_percpu_map;
mod percpu_elasticsearch;
#[cfg(test)]
mod tests;

// Re-exports
pub use types::MapType;
pub use config::MapConfig;
pub use builder::MapBuilder;
pub use iter::MapIter;
pub use typed::{TypedMap, TypedMapBuilder};
pub use hash::{HashFunction, fnv1a_hash, murmur3_hash};
pub use hashmap::{OptimizedHashMap, TypedOptimizedHashMap, HashMapStats};
pub use lrumap::{OptimizedLruMap, TypedOptimizedLruMap, LruMapStats};
pub use percpumap::{OptimizedPerCpuMap, TypedOptimizedPerCpuMap, PerCpuMapStats};
pub use typed_optimized_percpu_map::{MapStats, PerCpuMapError};
pub use percpu_elasticsearch::{PerCpuMapElasticsearchExt, CpuMapStats, create_percpu_map_elasticsearch_exporter};

/// Map error
#[derive(Debug)]
pub enum MapError {
    /// Invalid argument
    InvalidArgument(String),
    /// Invalid CPU
    InvalidCpu(usize),
    /// Serialization error
    SerializationError(String),
    /// Deserialization error
    DeserializationError(String),
    /// Operation error
    OperationError(String),
    /// System error
    SystemError(String),
}

impl fmt::Display for MapError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            MapError::InvalidArgument(msg) => write!(f, "Invalid argument: {}", msg),
            MapError::InvalidCpu(cpu) => write!(f, "Invalid CPU: {}", cpu),
            MapError::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
            MapError::DeserializationError(msg) => write!(f, "Deserialization error: {}", msg),
            MapError::OperationError(msg) => write!(f, "Operation error: {}", msg),
            MapError::SystemError(msg) => write!(f, "System error: {}", msg),
        }
    }
}

impl Error for MapError {}

/// Map information
#[derive(Debug, Clone)]
pub struct MapInfo {
    /// Map type
    pub map_type: MapType,
    /// Map name
    pub name: String,
    /// Key size in bytes
    pub key_size: usize,
    /// Value size in bytes
    pub value_size: usize,
    /// Maximum number of entries
    pub max_entries: usize,
    /// Current number of entries
    pub current_entries: usize,
    /// Map flags
    pub flags: u32,
    /// Map ID
    pub id: u32,
    /// Memory usage in bytes
    pub memory_usage: usize,
}

/// eBPF map
#[derive(Debug)]
pub struct Map {
    /// Map file descriptor
    fd: i32,
    /// Map configuration
    config: MapConfig,
    /// Map name
    name: String,
    /// Inner libbpf map (Linux only)
    #[cfg(feature = "linux")]
    inner: Option<OpenMap>,
}

impl Map {
    /// Create a new map
    pub fn create(config: MapConfig) -> Result<Self, MapError> {
        #[cfg(feature = "linux")]
        {
            // Convert map type to libbpf map type
            let libbpf_map_type = match LibbpfMapType::try_from(config.map_type.to_libbpf_type() as u32) {
                Ok(map_type) => map_type,
                Err(_) => return Err(MapError::CreateError(format!(
                    "Invalid map type: {:?}", config.map_type
                ))),
            };
            
            // Create the map
            let map = LibbpfMap::create(
                libbpf_map_type,
                Some("inspector_gadget_map"),
                config.key_size as u32,
                config.value_size as u32,
                config.max_entries as u32,
                config.flags,
            )?;
            
            // Get the file descriptor
            let fd = map.fd();
            
            // Open the map
            let open_map = map.open()?;
            
            Ok(Self {
                fd,
                config,
                name: "inspector_gadget_map".to_string(),
                inner: Some(open_map),
            })
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::CreateError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Open an existing map by path
    pub fn open(path: &str) -> Result<Self, MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the path exists
            if !Path::new(path).exists() {
                return Err(MapError::NotFound(format!("Map not found at path: {}", path)));
            }
            
            // Open the map
            let map = LibbpfMap::open(path)?;
            
            // Get the file descriptor
            let fd = map.fd();
            
            // Get map info
            let info = map.info()?;
            
            // Convert map type
            let map_type = match MapType::from_libbpf_type(info.type_ as u32) {
                Some(map_type) => map_type,
                None => return Err(MapError::CreateError(format!(
                    "Unsupported map type: {}", info.type_
                ))),
            };
            
            // Create map configuration
            let config = MapConfig {
                map_type,
                key_size: info.key_size as usize,
                value_size: info.value_size as usize,
                max_entries: info.max_entries as usize,
                flags: info.map_flags,
            };
            
            // Open the map
            let open_map = map.open()?;
            
            // Extract the map name from the path
            let name = Path::new(path)
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("unknown")
                .to_string();
            
            Ok(Self {
                fd,
                config,
                name,
                inner: Some(open_map),
            })
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::CreateError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Update a key-value pair
    pub fn update(&self, key: &[u8], value: &[u8], flags: u64) -> Result<(), MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the inner map is available
            let inner = match &self.inner {
                Some(inner) => inner,
                None => return Err(MapError::OperationError("Map is not open".to_string())),
            };
            
            // Check key size
            if key.len() != self.config.key_size {
                return Err(MapError::InvalidArgument(format!(
                    "Key size mismatch: expected {}, got {}",
                    self.config.key_size, key.len()
                )));
            }
            
            // Check value size
            if value.len() != self.config.value_size {
                return Err(MapError::InvalidArgument(format!(
                    "Value size mismatch: expected {}, got {}",
                    self.config.value_size, value.len()
                )));
            }
            
            // Convert flags
            let map_flags = match flags {
                0 => MapFlags::empty(),
                1 => MapFlags::ANY,
                2 => MapFlags::NO_EXIST,
                3 => MapFlags::EXIST,
                _ => return Err(MapError::InvalidArgument(format!(
                    "Invalid flags: {}", flags
                ))),
            };
            
            // Update the map
            inner.update(key, value, map_flags)?;
            
            Ok(())
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::OperationError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Lookup a value by key
    pub fn lookup(&self, key: &[u8]) -> Result<Vec<u8>, MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the inner map is available
            let inner = match &self.inner {
                Some(inner) => inner,
                None => return Err(MapError::OperationError("Map is not open".to_string())),
            };
            
            // Check key size
            if key.len() != self.config.key_size {
                return Err(MapError::InvalidArgument(format!(
                    "Key size mismatch: expected {}, got {}",
                    self.config.key_size, key.len()
                )));
            }
            
            // Lookup the value
            let value = inner.lookup(key, MapFlags::empty())?;
            
            // Check if the value exists
            match value {
                Some(value) => Ok(value),
                None => Err(MapError::NotFound(format!("Key not found: {:?}", key))),
            }
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::OperationError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Delete a key-value pair
    pub fn delete(&self, key: &[u8]) -> Result<(), MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the inner map is available
            let inner = match &self.inner {
                Some(inner) => inner,
                None => return Err(MapError::OperationError("Map is not open".to_string())),
            };
            
            // Check key size
            if key.len() != self.config.key_size {
                return Err(MapError::InvalidArgument(format!(
                    "Key size mismatch: expected {}, got {}",
                    self.config.key_size, key.len()
                )));
            }
            
            // Delete the key-value pair
            inner.delete(key)?;
            
            Ok(())
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::OperationError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Get the next key
    pub fn get_next_key(&self, key: Option<&[u8]>) -> Result<Option<Vec<u8>>, MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the inner map is available
            let inner = match &self.inner {
                Some(inner) => inner,
                None => return Err(MapError::OperationError("Map is not open".to_string())),
            };
            
            // Check key size if provided
            if let Some(key) = key {
                if key.len() != self.config.key_size {
                    return Err(MapError::InvalidArgument(format!(
                        "Key size mismatch: expected {}, got {}",
                        self.config.key_size, key.len()
                    )));
                }
            }
            
            // Get the next key
            let next_key = inner.get_next_key(key)?;
            
            Ok(next_key)
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::OperationError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Pin the map to the BPF filesystem
    pub fn pin(&self, path: &str) -> Result<(), MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the inner map is available
            let inner = match &self.inner {
                Some(inner) => inner,
                None => return Err(MapError::OperationError("Map is not open".to_string())),
            };
            
            // Create parent directories if they don't exist
            if let Some(parent) = Path::new(path).parent() {
                fs::create_dir_all(parent)?;
            }
            
            // Pin the map
            inner.pin(path)?;
            
            Ok(())
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::OperationError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Unpin the map from the BPF filesystem
    pub fn unpin(&self) -> Result<(), MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the inner map is available
            let inner = match &self.inner {
                Some(inner) => inner,
                None => return Err(MapError::OperationError("Map is not open".to_string())),
            };
            
            // Unpin the map
            // Note: libbpf-rs doesn't provide a direct unpin method,
            // so we need to use the underlying syscall
            
            // For now, just return success
            // TODO: Implement actual unpinning
            
            Ok(())
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::OperationError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Get map information
    pub fn info(&self) -> Result<MapInfo, MapError> {
        #[cfg(feature = "linux")]
        {
            // Check if the inner map is available
            let inner = match &self.inner {
                Some(inner) => inner,
                None => return Err(MapError::OperationError("Map is not open".to_string())),
            };
            
            // Get map info
            let info = LibbpfMap::from_fd(self.fd)?.info()?;
            
            // Convert map type
            let map_type = match MapType::from_libbpf_type(info.type_ as u32) {
                Some(map_type) => map_type,
                None => return Err(MapError::CreateError(format!(
                    "Unsupported map type: {}", info.type_
                ))),
            };
            
            Ok(MapInfo {
                map_type,
                name: self.name.clone(),
                key_size: info.key_size as usize,
                value_size: info.value_size as usize,
                max_entries: info.max_entries as usize,
                current_entries: info.btf_value_type_id as usize, // This is not correct, but libbpf-rs doesn't expose current_entries
                flags: info.map_flags,
                id: info.id,
                memory_usage: 0, // Not available in libbpf-rs
            })
        }
        
        #[cfg(not(feature = "linux"))]
        {
            Err(MapError::OperationError("eBPF maps are only supported on Linux".to_string()))
        }
    }
    
    /// Get the map file descriptor
    pub fn fd(&self) -> i32 {
        self.fd
    }
    
    /// Get the map configuration
    pub fn config(&self) -> &MapConfig {
        &self.config
    }
    
    /// Get the map name
    pub fn name(&self) -> &str {
        &self.name
    }
    
    /// Create an iterator over the map's key-value pairs
    pub fn iter(&self) -> MapIter {
        MapIter::new(self)
    }
}

impl Drop for Map {
    fn drop(&mut self) {
        // Close the map file descriptor
        if self.fd >= 0 {
            unsafe {
                libc::close(self.fd);
            }
        }
    }
}

/// Check if eBPF maps are supported on the system
pub fn are_maps_supported() -> bool {
    // Check if the BPF filesystem is mounted
    if Path::new("/sys/fs/bpf").exists() {
        return true;
    }
    
    // Check if the kernel has BPF syscall
    if let Ok(content) = fs::read_to_string("/proc/kallsyms") {
        if content.contains("bpf_map_") {
            return true;
        }
    }
    
    false
}

/// Get the maximum number of maps supported by the system
pub fn get_max_maps() -> Result<usize, MapError> {
    // Try to read the maximum number of maps from the kernel
    match fs::read_to_string("/proc/sys/kernel/bpf_map_limit") {
        Ok(content) => {
            content.trim().parse::<usize>().map_err(|e| {
                MapError::SystemError(io::Error::new(
                    io::ErrorKind::InvalidData,
                    format!("Failed to parse bpf_map_limit: {}", e),
                ))
            })
        }
        Err(e) => {
            // If the file doesn't exist, use a default value
            if e.kind() == io::ErrorKind::NotFound {
                Ok(1024) // Default value
            } else {
                Err(MapError::SystemError(e))
            }
        }
    }
} 