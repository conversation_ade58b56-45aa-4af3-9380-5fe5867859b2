//! Elasticsearch exporter implementation for the event collection system.
//! 
//! This module provides functionality to export events to Elasticsearch,
//! with support for bulk operations, retry mechanisms, and secure transport.

use async_trait::async_trait;
use elasticsearch::{
    Elasticsearch, 
    Error as ElasticsearchError,
    http::transport::{Transport, TransportBuilder},
    auth::Credentials,
    cert::CertificateValidation,
    BulkParts, 
    IndexParts,
};
use elasticsearch::http::StatusCode;
use serde_json::{json, Value};


use tokio::sync::Mutex;
use log::{error, info, debug, warn};
use url::Url;

use crate::core::events::{Event, EventId};

/// Configuration for the Elasticsearch exporter.
#[derive(Debug, Clone)]
pub struct ElasticsearchConfig {
    /// The Elasticsearch URL (e.g., "https://localhost:9200")
    pub url: String,
    /// The index name to use for events
    pub index: String,
    /// Optional username for authentication
    pub username: Option<String>,
    /// Optional password for authentication
    pub password: Option<String>,
    /// Whether to verify SSL certificates
    pub verify_certs: bool,
    /// Maximum number of events to include in a bulk request
    pub bulk_size: usize,
    /// Number of retry attempts for failed requests
    pub max_retries: usize,
    /// Delay between retry attempts in milliseconds
    pub retry_delay_ms: u64,
    /// Whether to create the index if it doesn't exist
    pub create_index_if_missing: bool,
    /// Optional index mapping template
    pub index_mapping: Option<Value>,
}

impl Default for ElasticsearchConfig {
    fn default() -> Self {
        Self {
            url: "http://localhost:9200".to_string(),
            index: "inspector-gadget-events".to_string(),
            username: None,
            password: None,
            verify_certs: true,
            bulk_size: 1000,
            max_retries: 3,
            retry_delay_ms: 1000,
            create_index_if_missing: true,
            index_mapping: None,
        }
    }
}

/// Elasticsearch exporter for events.
pub struct ElasticsearchExporter {
    /// Elasticsearch client
    client: Elasticsearch,
    /// Configuration for the exporter
    config: ElasticsearchConfig,
    /// Buffer for events to be sent in bulk
    buffer: Arc<Mutex<Vec<Event>>>,
}

#[async_trait]
impl super::EventExporter for ElasticsearchExporter {
    async fn export_event(&self, event: Event) -> Result<(), String> {
        let mut buffer = self.buffer.lock().await;
        buffer.push(event);
        
        if buffer.len() >= self.config.bulk_size {
            let events = std::mem::take(&mut *buffer);
            drop(buffer); // Release the lock before the potentially long operation
            self.send_bulk_events(events).await
        } else {
            Ok(())
        }
    }

    async fn export_events(&self, events: Vec<Event>) -> Result<(), String> {
        if events.is_empty() {
            return Ok(());
        }

        let mut buffer = self.buffer.lock().await;
        buffer.extend(events);
        
        if buffer.len() >= self.config.bulk_size {
            let events_to_send = std::mem::take(&mut *buffer);
            drop(buffer); // Release the lock before the potentially long operation
            self.send_bulk_events(events_to_send).await
        } else {
            Ok(())
        }
    }

    async fn flush(&self) -> Result<(), String> {
        let mut buffer = self.buffer.lock().await;
        if buffer.is_empty() {
            return Ok(());
        }
        
        let events = std::mem::take(&mut *buffer);
        drop(buffer); // Release the lock before the potentially long operation
        self.send_bulk_events(events).await
    }
}

impl ElasticsearchExporter {
    /// Create a new Elasticsearch exporter with the given configuration.
    pub async fn new(config: ElasticsearchConfig) -> Result<Self, String> {
        let client = Self::create_client(&config).map_err(|e| format!("Failed to create Elasticsearch client: {}", e))?;
        
        let exporter = Self {
            client,
            config: config.clone(),
            buffer: Arc::new(Mutex::new(Vec::with_capacity(config.bulk_size))),
        };
        
        if config.create_index_if_missing {
            exporter.ensure_index_exists().await?;
        }
        
        Ok(exporter)
    }
    
    /// Create an Elasticsearch client from the configuration.
    fn create_client(config: &ElasticsearchConfig) -> Result<Elasticsearch, ElasticsearchError> {
        let url = Url::parse(&config.url).map_err(|e| ElasticsearchError::Builder(format!("Invalid URL: {}", e)))?;
        
        let mut transport_builder = TransportBuilder::new(url);
        
        // Configure authentication if provided
        if let (Some(username), Some(password)) = (&config.username, &config.password) {
            transport_builder = transport_builder.auth(Credentials::Basic(username.clone(), password.clone()));
        }
        
        // Configure SSL verification
        if !config.verify_certs {
            transport_builder = transport_builder.cert_validation(CertificateValidation::None);
        }
        
        let transport = transport_builder.build()?;
        Ok(Elasticsearch::new(transport))
    }
    
    /// Ensure the configured index exists, creating it if necessary.
    async fn ensure_index_exists(&self) -> Result<(), String> {
        let index_exists = self.client
            .indices()
            .exists(elasticsearch::indices::IndicesExistsParts::Index(&[&self.config.index]))
            .send()
            .await
            .map_err(|e| format!("Failed to check if index exists: {}", e))?
            .status_code() == StatusCode::OK;
            
        if !index_exists {
            info!("Creating Elasticsearch index '{}'", self.config.index);
            
            let mut create_index = self.client
                .indices()
                .create(elasticsearch::indices::IndicesCreateParts::Index(&self.config.index));
                
            // Apply custom mapping if provided
            if let Some(mapping) = &self.config.index_mapping {
                create_index = create_index.body(mapping.clone());
            } else {
                // Default mapping with keyword fields for better search performance
                create_index = create_index.body(json!({
                    "mappings": {
                        "properties": {
                            "id": { "type": "keyword" },
                            "timestamp": { "type": "date" },
                            "event_type": { "type": "keyword" },
                            "severity": { "type": "keyword" },
                            "source": { "type": "keyword" },
                            "process_id": { "type": "integer" },
                            "thread_id": { "type": "integer" },
                            "syscall_number": { "type": "integer" },
                            "syscall_name": { "type": "keyword" },
                            "parameters": { "type": "object", "dynamic": true },
                            "return_value": { "type": "long" },
                            "duration_ns": { "type": "long" },
                            "tags": { "type": "keyword" },
                            "metadata": { "type": "object", "dynamic": true }
                        }
                    },
                    "settings": {
                        "number_of_shards": 1,
                        "number_of_replicas": 0
                    }
                }));
            }
            
            create_index
                .send()
                .await
                .map_err(|e| format!("Failed to create index: {}", e))?;
                
            info!("Successfully created Elasticsearch index '{}'", self.config.index);
        }
        
        Ok(())
    }
    
    /// Send events in bulk to Elasticsearch with retry logic.
    async fn send_bulk_events(&self, events: Vec<Event>) -> Result<(), String> {
        if events.is_empty() {
            return Ok(());
        }
        
        debug!("Sending {} events to Elasticsearch", events.len());
        
        // Prepare the bulk request body
        let mut body = String::new();
        for event in &events {
            // Add action line
            let action = json!({
                "index": {
                    "_index": self.config.index,
                    "_id": event.id.to_string()
                }
            });
            body.push_str(&serde_json::to_string(&action).unwrap());
            body.push('\n');
            
            // Add document line
            body.push_str(&serde_json::to_string(&event).unwrap());
            body.push('\n');
        }
        
        // Send with retry logic
        let mut attempt = 0;
        loop {
            attempt += 1;
            
            match self.client
                .bulk(BulkParts::None)
                .body(body.clone())
                .send()
                .await
            {
                Ok(response) => {
                    let status = response.status_code();
                    
                    if status.is_success() {
                        let response_body = response.json::<Value>().await
                            .map_err(|e| format!("Failed to parse Elasticsearch response: {}", e))?;
                        
                        // Check for errors in the response
                        let has_errors = response_body["errors"]
                            .as_bool()
                            .unwrap_or(false);
                            
                        if has_errors {
                            let error_count = response_body["items"]
                                .as_array()
                                .map(|items| items.iter().filter(|item| item["index"]["error"].is_object()).count())
                                .unwrap_or(0);
                                
                            warn!("{} errors occurred while indexing {} events", error_count, events.len());
                            
                            // Log the first few errors for debugging
                            if let Some(items) = response_body["items"].as_array() {
                                for (i, item) in items.iter().enumerate() {
                                    if i >= 5 {
                                        break; // Only log the first 5 errors
                                    }
                                    
                                    if let Some(error) = item["index"]["error"].as_object() {
                                        let event_id = item["index"]["_id"].as_str().unwrap_or("unknown");
                                        let error_type = error["type"].as_str().unwrap_or("unknown");
                                        let reason = error["reason"].as_str().unwrap_or("unknown");
                                        
                                        error!("Error indexing event {}: {} - {}", event_id, error_type, reason);
                                    }
                                }
                            }
                            
                            // Consider partial success as success
                            info!("Successfully indexed {} of {} events", events.len() - error_count, events.len());
                            return Ok(());
                        } else {
                            info!("Successfully indexed {} events", events.len());
                            return Ok(());
                        }
                    } else if status.is_server_error() && attempt <= self.config.max_retries {
                        // Server error, retry after delay
                        warn!("Elasticsearch server error ({}), retrying ({}/{})", 
                              status, attempt, self.config.max_retries);
                        tokio::time::sleep(Duration::from_millis(self.config.retry_delay_ms)).await;
                        continue;
                    } else {
                        // Client error or max retries reached
                        let error_body = response.text().await
                            .unwrap_or_else(|_| "Unable to read error response".to_string());
                        return Err(format!("Elasticsearch error: {} - {}", status, error_body));
                    }
                },
                Err(e) => {
                    if attempt <= self.config.max_retries {
                        warn!("Elasticsearch request failed, retrying ({}/{}): {}", 
                              attempt, self.config.max_retries, e);
                        tokio::time::sleep(Duration::from_millis(self.config.retry_delay_ms)).await;
                        continue;
                    } else {
                        return Err(format!("Failed to send events after {} attempts: {}", attempt, e));
                    }
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{Event, EventType, Severity};
    use std::time::{SystemTime, UNIX_EPOCH};
    use mockito::{mock, server_url};
    
    #[tokio::test]
    async fn test_elasticsearch_exporter_new() {
        let config = ElasticsearchConfig {
            url: server_url(),
            index: "test-events".to_string(),
            create_index_if_missing: false, // Don't try to create the index for this test
            ..Default::default()
        };
        
        let exporter = ElasticsearchExporter::new(config).await;
        assert!(exporter.is_ok());
    }
    
    #[tokio::test]
    async fn test_ensure_index_exists_creates_index() {
        let index_name = "test-events";
        
        // Mock the index exists check (returning 404 Not Found)
        let _m1 = mock("HEAD", format!("/{}", index_name).as_str())
            .with_status(404)
            .create();
            
        // Mock the index creation
        let _m2 = mock("PUT", format!("/{}", index_name).as_str())
            .with_status(200)
            .with_body(r#"{"acknowledged":true,"shards_acknowledged":true,"index":"test-events"}"#)
            .create();
            
        let config = ElasticsearchConfig {
            url: server_url(),
            index: index_name.to_string(),
            create_index_if_missing: true,
            ..Default::default()
        };
        
        let exporter = ElasticsearchExporter::new(config).await;
        assert!(exporter.is_ok());
    }
    
    #[tokio::test]
    async fn test_export_event() {
        let index_name = "test-events";
        
        // Mock the index exists check
        let _m1 = mock("HEAD", format!("/{}", index_name).as_str())
            .with_status(200)
            .create();
            
        // Mock the bulk API
        let _m2 = mock("POST", "/_bulk")
            .with_status(200)
            .with_body(r#"{"took":7,"errors":false,"items":[{"index":{"_index":"test-events","_id":"test-id","_version":1,"result":"created","_shards":{"total":1,"successful":1,"failed":0},"_seq_no":0,"_primary_term":1,"status":201}}]}"#)
            .create();
            
        let config = ElasticsearchConfig {
            url: server_url(),
            index: index_name.to_string(),
            bulk_size: 1, // Set to 1 to trigger immediate send
            ..Default::default()
        };
        
        let exporter = ElasticsearchExporter::new(config).await.unwrap();
        
        let event = Event {
            id: EventId::new(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            event_type: EventType::Syscall,
            severity: Severity::Info,
            source: "test".to_string(),
            process_id: Some(1234),
            thread_id: Some(5678),
            syscall_number: Some(1),
            syscall_name: Some("write".to_string()),
            parameters: Some(json!({"fd": 1, "buf": "Hello", "count": 5})),
            return_value: Some(5),
            duration_ns: Some(1000),
            tags: vec!["test".to_string()],
            metadata: None,
        };
        
        let result = exporter.export_event(event).await;
        assert!(result.is_ok());
    }
    
    #[tokio::test]
    async fn test_flush() {
        let index_name = "test-events";
        
        // Mock the index exists check
        let _m1 = mock("HEAD", format!("/{}", index_name).as_str())
            .with_status(200)
            .create();
            
        // Mock the bulk API
        let _m2 = mock("POST", "/_bulk")
            .with_status(200)
            .with_body(r#"{"took":7,"errors":false,"items":[{"index":{"_index":"test-events","_id":"test-id","_version":1,"result":"created","_shards":{"total":1,"successful":1,"failed":0},"_seq_no":0,"_primary_term":1,"status":201}}]}"#)
            .create();
            
        let config = ElasticsearchConfig {
            url: server_url(),
            index: index_name.to_string(),
            bulk_size: 100, // Set high enough to not trigger automatic send
            ..Default::default()
        };
        
        let exporter = ElasticsearchExporter::new(config).await.unwrap();
        
        let event = Event {
            id: EventId::new(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            event_type: EventType::Syscall,
            severity: Severity::Info,
            source: "test".to_string(),
            process_id: Some(1234),
            thread_id: Some(5678),
            syscall_number: Some(1),
            syscall_name: Some("write".to_string()),
            parameters: Some(json!({"fd": 1, "buf": "Hello", "count": 5})),
            return_value: Some(5),
            duration_ns: Some(1000),
            tags: vec!["test".to_string()],
            metadata: None,
        };
        
        // Add event to buffer but don't trigger automatic send
        {
            let mut buffer = exporter.buffer.lock().await;
            buffer.push(event);
        }
        
        // Flush should send the event
        let result = exporter.flush().await;
        assert!(result.is_ok());
        
        // Buffer should be empty after flush
        let buffer = exporter.buffer.lock().await;
        assert!(buffer.is_empty());
    }
} 