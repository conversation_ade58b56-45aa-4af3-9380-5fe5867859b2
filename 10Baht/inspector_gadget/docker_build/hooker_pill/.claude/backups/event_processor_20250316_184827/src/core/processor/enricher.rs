/*!
 * Context Enricher Implementation
 * 
 * This module provides enrichment capabilities for events, adding
 * additional context information.
 */



use async_trait::async_trait;

use serde_json::Value;
use tokio::sync::RwLock;

use crate::core::events::{EventData, TraceEvent};
use crate::error::Result;
use super::EventProcessor;

/// Context enricher configuration
#[derive(Debug, Clone)]
pub struct ContextEnricherConfig {
    /// Process information to add
    pub add_process_info: bool,
    /// File information to add
    pub add_file_info: bool,
    /// Network information to add
    pub add_network_info: bool,
    /// User information to add
    pub add_user_info: bool,
    /// System information to add
    pub add_system_info: bool,
}

impl Default for ContextEnricherConfig {
    fn default() -> Self {
        Self {
            add_process_info: true,
            add_file_info: true,
            add_network_info: true,
            add_user_info: true,
            add_system_info: true,
        }
    }
}

/// Process information
#[derive(Debug, <PERSON><PERSON>)]
struct ProcessInfo {
    /// Process name
    name: String,
    /// Process path
    path: String,
    /// Process command line
    cmdline: String,
    /// Process environment variables
    env: HashMap<String, String>,
    /// Process user ID
    uid: u32,
    /// Process group ID
    gid: u32,
    /// Process start time
    start_time: u64,
}

/// File information
#[derive(Debug, Clone)]
struct FileInfo {
    /// File path
    path: String,
    /// File type
    file_type: String,
    /// File size
    size: u64,
    /// File permissions
    permissions: u32,
    /// File owner
    owner: u32,
    /// File group
    group: u32,
    /// File modification time
    mtime: u64,
}

/// Network information
#[derive(Debug, Clone)]
struct NetworkInfo {
    /// Local address
    local_addr: String,
    /// Local port
    local_port: u16,
    /// Remote address
    remote_addr: String,
    /// Remote port
    remote_port: u16,
    /// Protocol
    protocol: String,
    /// Connection state
    state: String,
}

/// User information
#[derive(Debug, Clone)]
struct UserInfo {
    /// User ID
    uid: u32,
    /// User name
    name: String,
    /// User home directory
    home: String,
    /// User shell
    shell: String,
    /// User groups
    groups: Vec<u32>,
}

/// System information
#[derive(Debug, Clone)]
struct SystemInfo {
    /// Hostname
    hostname: String,
    /// Operating system
    os: String,
    /// Kernel version
    kernel: String,
    /// Architecture
    arch: String,
    /// Boot time
    boot_time: u64,
}

/// Context enricher
pub struct ContextEnricher {
    /// Configuration
    config: ContextEnricherConfig,
    /// Process information cache
    process_cache: Arc<RwLock<HashMap<u32, ProcessInfo>>>,
    /// File information cache
    file_cache: Arc<RwLock<HashMap<String, FileInfo>>>,
    /// Network information cache
    network_cache: Arc<RwLock<HashMap<String, NetworkInfo>>>,
    /// User information cache
    user_cache: Arc<RwLock<HashMap<u32, UserInfo>>>,
    /// System information
    system_info: Arc<RwLock<Option<SystemInfo>>>,
}

impl ContextEnricher {
    /// Create a new context enricher
    pub fn new(config: ContextEnricherConfig) -> Self {
        Self {
            config,
            process_cache: Arc::new(RwLock::new(HashMap::new())),
            file_cache: Arc::new(RwLock::new(HashMap::new())),
            network_cache: Arc::new(RwLock::new(HashMap::new())),
            user_cache: Arc::new(RwLock::new(HashMap::new())),
            system_info: Arc::new(RwLock::new(None)),
        }
    }
    
    /// Create a new context enricher with default configuration
    pub fn with_defaults() -> Self {
        Self::new(ContextEnricherConfig::default())
    }
    
    /// Enrich an event with additional context
    async fn enrich_event(&self, event: &mut TraceEvent) -> Result<()> {
        // Add process information
        if self.config.add_process_info {
            self.add_process_info(event).await?;
        }
        
        // Add file information
        if self.config.add_file_info {
            self.add_file_info(event).await?;
        }
        
        // Add network information
        if self.config.add_network_info {
            self.add_network_info(event).await?;
        }
        
        // Add user information
        if self.config.add_user_info {
            self.add_user_info(event).await?;
        }
        
        // Add system information
        if self.config.add_system_info {
            self.add_system_info(event).await?;
        }
        
        Ok(())
    }
    
    /// Add process information to an event
    async fn add_process_info(&self, event: &mut TraceEvent) -> Result<()> {
        // Check if we already have process information for this PID
        let pid = event.pid;
        let process_info = {
            let cache = self.process_cache.read().await;
            cache.get(&pid).cloned()
        };
        
        // If we don't have process information, try to get it
        let process_info = if let Some(info) = process_info {
            info
        } else {
            // Try to get process information
            if let Some(info) = self.get_process_info(pid).await? {
                // Add to cache
                let mut cache = self.process_cache.write().await;
                cache.insert(pid, info.clone());
                info
            } else {
                // No process information available
                return Ok(());
            }
        };
        
        // Add process information to event
        let mut process_data = serde_json::Map::new();
        process_data.insert("name".to_string(), Value::String(process_info.name.clone()));
        process_data.insert("path".to_string(), Value::String(process_info.path.clone()));
        process_data.insert("cmdline".to_string(), Value::String(process_info.cmdline.clone()));
        
        let mut env_data = serde_json::Map::new();
        for (key, value) in &process_info.env {
            env_data.insert(key.clone(), Value::String(value.clone()));
        }
        process_data.insert("env".to_string(), Value::Object(env_data));
        
        process_data.insert("uid".to_string(), Value::Number(process_info.uid.into()));
        process_data.insert("gid".to_string(), Value::Number(process_info.gid.into()));
        process_data.insert("start_time".to_string(), Value::Number(process_info.start_time.into()));
        
        // Add to event
        event.context.insert("process".to_string(), Value::Object(process_data));
        
        Ok(())
    }
    
    /// Get process information for a PID
    async fn get_process_info(&self, pid: u32) -> Result<Option<ProcessInfo>> {
        // This is a placeholder implementation
        // In a real implementation, we would get this information from the system
        
        // For now, just return some dummy data
        if pid > 0 {
            let mut env = HashMap::new();
            env.insert("PATH".to_string(), "/usr/bin:/bin".to_string());
            
            let info = ProcessInfo {
                name: format!("process-{}", pid),
                path: format!("/proc/{}/exe", pid),
                cmdline: format!("process-{} --arg1 --arg2", pid),
                env,
                uid: 1000,
                gid: 1000,
                start_time: 1234567890,
            };
            
            Ok(Some(info))
        } else {
            Ok(None)
        }
    }
    
    /// Add file information to an event
    async fn add_file_info(&self, event: &mut TraceEvent) -> Result<()> {
        // Check if this event has file information
        let file_path = match &event.data {
            EventData::FileOpen(path, _) => Some(path.clone()),
            EventData::FileRead(path, _) => Some(path.clone()),
            EventData::FileWrite(path, _) => Some(path.clone()),
            EventData::FileClose(path) => Some(path.clone()),
            EventData::FileDelete(path) => Some(path.clone()),
            EventData::FileRename(old_path, _) => Some(old_path.clone()),
            EventData::Syscall(params) => {
                if let Some(path) = params.get("path").and_then(|v| v.as_str()) {
                    Some(path.to_string())
                } else {
                    None
                }
            }
            _ => None,
        };
        
        // If we have a file path, add file information
        if let Some(path) = file_path {
            // Check if we already have file information for this path
            let file_info = {
                let cache = self.file_cache.read().await;
                cache.get(&path).cloned()
            };
            
            // If we don't have file information, try to get it
            let file_info = if let Some(info) = file_info {
                info
            } else {
                // Try to get file information
                if let Some(info) = self.get_file_info(&path).await? {
                    // Add to cache
                    let mut cache = self.file_cache.write().await;
                    cache.insert(path.clone(), info.clone());
                    info
                } else {
                    // No file information available
                    return Ok(());
                }
            };
            
            // Add file information to event
            let mut file_data = serde_json::Map::new();
            file_data.insert("path".to_string(), Value::String(file_info.path.clone()));
            file_data.insert("type".to_string(), Value::String(file_info.file_type.clone()));
            file_data.insert("size".to_string(), Value::Number(file_info.size.into()));
            file_data.insert("permissions".to_string(), Value::Number(file_info.permissions.into()));
            file_data.insert("owner".to_string(), Value::Number(file_info.owner.into()));
            file_data.insert("group".to_string(), Value::Number(file_info.group.into()));
            file_data.insert("mtime".to_string(), Value::Number(file_info.mtime.into()));
            
            // Add to event
            event.context.insert("file".to_string(), Value::Object(file_data));
        }
        
        Ok(())
    }
    
    /// Get file information for a path
    async fn get_file_info(&self, path: &str) -> Result<Option<FileInfo>> {
        // This is a placeholder implementation
        // In a real implementation, we would get this information from the system
        
        // For now, just return some dummy data
        if !path.is_empty() {
            let info = FileInfo {
                path: path.to_string(),
                file_type: "regular".to_string(),
                size: 1024,
                permissions: 0o644,
                owner: 1000,
                group: 1000,
                mtime: 1234567890,
            };
            
            Ok(Some(info))
        } else {
            Ok(None)
        }
    }
    
    /// Add network information to an event
    async fn add_network_info(&self, event: &mut TraceEvent) -> Result<()> {
        // Check if this event has network information
        let network_key = match &event.data {
            EventData::NetworkConnect(addr, port) => Some(format!("{}:{}", addr, port)),
            EventData::NetworkListen(addr, port) => Some(format!("{}:{}", addr, port)),
            EventData::NetworkAccept(addr, port) => Some(format!("{}:{}", addr, port)),
            EventData::NetworkSend(addr, port, _) => Some(format!("{}:{}", addr, port)),
            EventData::NetworkReceive(addr, port, _) => Some(format!("{}:{}", addr, port)),
            EventData::NetworkClose(addr, port) => Some(format!("{}:{}", addr, port)),
            EventData::Syscall(params) => {
                if let (Some(addr), Some(port)) = (
                    params.get("addr").and_then(|v| v.as_str()),
                    params.get("port").and_then(|v| v.as_u64()),
                ) {
                    Some(format!("{}:{}", addr, port))
                } else {
                    None
                }
            }
            _ => None,
        };
        
        // If we have a network key, add network information
        if let Some(key) = network_key {
            // Check if we already have network information for this key
            let network_info = {
                let cache = self.network_cache.read().await;
                cache.get(&key).cloned()
            };
            
            // If we don't have network information, try to get it
            let network_info = if let Some(info) = network_info {
                info
            } else {
                // Try to get network information
                if let Some(info) = self.get_network_info(&key).await? {
                    // Add to cache
                    let mut cache = self.network_cache.write().await;
                    cache.insert(key.clone(), info.clone());
                    info
                } else {
                    // No network information available
                    return Ok(());
                }
            };
            
            // Add network information to event
            let mut network_data = serde_json::Map::new();
            network_data.insert("local_addr".to_string(), Value::String(network_info.local_addr.clone()));
            network_data.insert("local_port".to_string(), Value::Number(network_info.local_port.into()));
            network_data.insert("remote_addr".to_string(), Value::String(network_info.remote_addr.clone()));
            network_data.insert("remote_port".to_string(), Value::Number(network_info.remote_port.into()));
            network_data.insert("protocol".to_string(), Value::String(network_info.protocol.clone()));
            network_data.insert("state".to_string(), Value::String(network_info.state.clone()));
            
            // Add to event
            event.context.insert("network".to_string(), Value::Object(network_data));
        }
        
        Ok(())
    }
    
    /// Get network information for a key
    async fn get_network_info(&self, key: &str) -> Result<Option<NetworkInfo>> {
        // This is a placeholder implementation
        // In a real implementation, we would get this information from the system
        
        // For now, just return some dummy data
        if !key.is_empty() {
            let parts: Vec<&str> = key.split(':').collect();
            if parts.len() == 2 {
                let addr = parts[0];
                let port = parts[1].parse::<u16>().unwrap_or(0);
                
                let info = NetworkInfo {
                    local_addr: "127.0.0.1".to_string(),
                    local_port: 12345,
                    remote_addr: addr.to_string(),
                    remote_port: port,
                    protocol: "TCP".to_string(),
                    state: "ESTABLISHED".to_string(),
                };
                
                Ok(Some(info))
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }
    
    /// Add user information to an event
    async fn add_user_info(&self, event: &mut TraceEvent) -> Result<()> {
        // Get user ID from event
        let uid = match &event.data {
            EventData::Syscall(params) => {
                params.get("uid").and_then(|v| v.as_u64()).map(|v| v as u32)
            }
            _ => None,
        }.unwrap_or(event.uid);
        
        // Check if we already have user information for this UID
        let user_info = {
            let cache = self.user_cache.read().await;
            cache.get(&uid).cloned()
        };
        
        // If we don't have user information, try to get it
        let user_info = if let Some(info) = user_info {
            info
        } else {
            // Try to get user information
            if let Some(info) = self.get_user_info(uid).await? {
                // Add to cache
                let mut cache = self.user_cache.write().await;
                cache.insert(uid, info.clone());
                info
            } else {
                // No user information available
                return Ok(());
            }
        };
        
        // Add user information to event
        let mut user_data = serde_json::Map::new();
        user_data.insert("uid".to_string(), Value::Number(user_info.uid.into()));
        user_data.insert("name".to_string(), Value::String(user_info.name.clone()));
        user_data.insert("home".to_string(), Value::String(user_info.home.clone()));
        user_data.insert("shell".to_string(), Value::String(user_info.shell.clone()));
        
        let groups = user_info.groups.iter().map(|g| Value::Number((*g).into())).collect();
        user_data.insert("groups".to_string(), Value::Array(groups));
        
        // Add to event
        event.context.insert("user".to_string(), Value::Object(user_data));
        
        Ok(())
    }
    
    /// Get user information for a UID
    async fn get_user_info(&self, uid: u32) -> Result<Option<UserInfo>> {
        // This is a placeholder implementation
        // In a real implementation, we would get this information from the system
        
        // For now, just return some dummy data
        if uid > 0 {
            let info = UserInfo {
                uid,
                name: format!("user-{}", uid),
                home: format!("/home/<USER>", uid),
                shell: "/bin/bash".to_string(),
                groups: vec![uid, 1000],
            };
            
            Ok(Some(info))
        } else {
            Ok(None)
        }
    }
    
    /// Add system information to an event
    async fn add_system_info(&self, event: &mut TraceEvent) -> Result<()> {
        // Check if we already have system information
        let system_info = {
            let info = self.system_info.read().await;
            info.clone()
        };
        
        // If we don't have system information, try to get it
        let system_info = if let Some(info) = system_info {
            info
        } else {
            // Try to get system information
            if let Some(info) = self.get_system_info().await? {
                // Add to cache
                let mut cache = self.system_info.write().await;
                *cache = Some(info.clone());
                info
            } else {
                // No system information available
                return Ok(());
            }
        };
        
        // Add system information to event
        let mut system_data = serde_json::Map::new();
        system_data.insert("hostname".to_string(), Value::String(system_info.hostname.clone()));
        system_data.insert("os".to_string(), Value::String(system_info.os.clone()));
        system_data.insert("kernel".to_string(), Value::String(system_info.kernel.clone()));
        system_data.insert("arch".to_string(), Value::String(system_info.arch.clone()));
        system_data.insert("boot_time".to_string(), Value::Number(system_info.boot_time.into()));
        
        // Add to event
        event.context.insert("system".to_string(), Value::Object(system_data));
        
        Ok(())
    }
    
    /// Get system information
    async fn get_system_info(&self) -> Result<Option<SystemInfo>> {
        // This is a placeholder implementation
        // In a real implementation, we would get this information from the system
        
        // For now, just return some dummy data
        let info = SystemInfo {
            hostname: "localhost".to_string(),
            os: "Linux".to_string(),
            kernel: "5.10.0".to_string(),
            arch: "x86_64".to_string(),
            boot_time: 1234567890,
        };
        
        Ok(Some(info))
    }
}

#[async_trait]
impl EventProcessor for ContextEnricher {
    async fn process(&self, mut event: TraceEvent) -> Result<Option<TraceEvent>> {
        self.enrich_event(&mut event).await?;
        debug!("Enriched event: {}", event.id);
        Ok(Some(event))
    }
    
    async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
        let mut result = Vec::with_capacity(events.len());
        
        for mut event in events {
            self.enrich_event(&mut event).await?;
            result.push(event);
        }
        
        debug!("Enriched {} events", result.len());
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{EventSeverity, EventType, TraceEvent};
    
    #[tokio::test]
    async fn test_process_enrichment() {
        // Create an enricher with default configuration
        let enricher = ContextEnricher::with_defaults();
        
        // Create a test event
        let event = TraceEvent::now(
            EventType::Syscall("open".to_string()),
            EventData::Syscall(serde_json::json!({
                "path": "/tmp/test.txt",
                "flags": "O_RDWR",
            })),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Process event
        let result = enricher.process(event).await.unwrap().unwrap();
        
        // Check result
        assert!(result.context.contains_key("process"));
        let process = result.context.get("process").unwrap();
        assert!(process.is_object());
        assert!(process.as_object().unwrap().contains_key("name"));
        assert!(process.as_object().unwrap().contains_key("path"));
    }
    
    #[tokio::test]
    async fn test_file_enrichment() {
        // Create an enricher with default configuration
        let enricher = ContextEnricher::with_defaults();
        
        // Create a test event
        let event = TraceEvent::now(
            EventType::Syscall("open".to_string()),
            EventData::FileOpen("/tmp/test.txt".to_string(), 0),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Process event
        let result = enricher.process(event).await.unwrap().unwrap();
        
        // Check result
        assert!(result.context.contains_key("file"));
        let file = result.context.get("file").unwrap();
        assert!(file.is_object());
        assert!(file.as_object().unwrap().contains_key("path"));
        assert!(file.as_object().unwrap().contains_key("type"));
    }
    
    #[tokio::test]
    async fn test_network_enrichment() {
        // Create an enricher with default configuration
        let enricher = ContextEnricher::with_defaults();
        
        // Create a test event
        let event = TraceEvent::now(
            EventType::Syscall("connect".to_string()),
            EventData::NetworkConnect("192.168.1.1".to_string(), 80),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Process event
        let result = enricher.process(event).await.unwrap().unwrap();
        
        // Check result
        assert!(result.context.contains_key("network"));
        let network = result.context.get("network").unwrap();
        assert!(network.is_object());
        assert!(network.as_object().unwrap().contains_key("local_addr"));
        assert!(network.as_object().unwrap().contains_key("remote_addr"));
    }
} 