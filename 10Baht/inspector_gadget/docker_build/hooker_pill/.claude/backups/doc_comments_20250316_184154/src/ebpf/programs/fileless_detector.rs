/*!
 * Fileless Malware Detector using eBPF
 * 
 * This module provides functionality for detecting fileless malware techniques
 * using eBPF programs.
 */

use std::sync::mpsc::Receiver;
use std::sync::mpsc::Receiver;
use std::sync::mpsc::Receiver;
use std::sync::mpsc::Receiver;
use std::sync::mpsc::Receiver;
use std::sync::mpsc::Receiver;
use std::sync::mpsc::Receiver;
use std::path::Path;

use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::mpsc::Sender;
use std::collections::{HashMap, HashSet};



use crate::ebpf::{EbpfError, EbpfEvent, ProgramType};
use crate::ebpf::loader::EbpfLoader;
use crate::ebpf::programs::{ThreatDetector, ThreatType, ThreatDetectionResult};

// Include the eBPF program bytecode
#[cfg(feature = "linux")]
const FILELESS_DETECTOR_BPF: &[u8] = include_bytes!(concat!(
    env!("OUT_DIR"),
    "/fileless_detector.bpf.o"
));

/// Event types from the eBPF program
#[repr(u32)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum EventType {
    Mmap = 1,
    Mprotect = 2,
    Memfd = 3,
    ProcessVmWritev = 4,
    Ptrace = 5,
    Exec = 6,
}

impl From<u32> for EventType {
    fn from(value: u32) -> Self {
        match value {
            1 => EventType::Mmap,
            2 => EventType::Mprotect,
            3 => EventType::Memfd,
            4 => EventType::ProcessVmWritev,
            5 => EventType::Ptrace,
            6 => EventType::Exec,
            _ => panic!("Invalid event type: {}", value),
        }
    }
}

/// Suspicious memory operation types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum SuspiciousMemoryOperation {
    /// Memory region with execute permission
    ExecutableMemory,
    /// Memory region with write+execute permissions
    WriteExecuteMemory,
    /// Memory mapping with anonymous+executable flags
    AnonymousExecutableMapping,
    /// Memory protection change to executable
    ProtectionChangeToExecutable,
    /// Memory injection into another process
    CrossProcessMemoryInjection,
    /// Process hollowing
    ProcessHollowing,
    /// Thread injection
    ThreadInjection,
    /// Reflective DLL/library loading
    ReflectiveLoading,
}

/// Process information
#[derive(Debug, Clone)]
struct ProcessInfo {
    /// Process ID
    pid: u32,
    /// Process name
    name: String,
    /// Parent process ID
    ppid: u32,
    /// User ID
    uid: u32,
    /// Group ID
    gid: u32,
    /// Creation time
    creation_time: u64,
    /// Suspicious operations
    suspicious_operations: Vec<SuspiciousMemoryOperation>,
    /// Executable memory regions (address -> size)
    executable_regions: HashMap<u64, u64>,
    /// Memory file descriptors
    memory_fds: HashSet<u32>,
    /// Processes this process has attached to
    attached_processes: HashSet<u32>,
}

impl ProcessInfo {
    /// Create a new process info
    fn new(pid: u32, name: String, ppid: u32, uid: u32, gid: u32) -> Self {
        Self {
            pid,
            name,
            ppid,
            uid,
            gid,
            creation_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            suspicious_operations: Vec::new(),
            executable_regions: HashMap::new(),
            memory_fds: HashSet::new(),
            attached_processes: HashSet::new(),
        }
    }
    
    /// Add a suspicious operation
    fn add_suspicious_operation(&mut self, operation: SuspiciousMemoryOperation) {
        if !self.suspicious_operations.contains(&operation) {
            self.suspicious_operations.push(operation);
            info!(
                "Process {} ({}) performed suspicious operation: {:?}",
                self.pid, self.name, operation
            );
        }
    }
    
    /// Add an executable memory region
    fn add_executable_region(&mut self, address: u64, size: u64) {
        self.executable_regions.insert(address, size);
        debug!(
            "Process {} ({}) mapped executable memory at 0x{:x} (size: {} bytes)",
            self.pid, self.name, address, size
        );
    }
    
    /// Add a memory file descriptor
    fn add_memory_fd(&mut self, fd: u32) {
        self.memory_fds.insert(fd);
        debug!(
            "Process {} ({}) created memory file descriptor: {}",
            self.pid, self.name, fd
        );
    }
    
    /// Add an attached process
    fn add_attached_process(&mut self, pid: u32) {
        self.attached_processes.insert(pid);
        debug!(
            "Process {} ({}) attached to process {}",
            self.pid, self.name, pid
        );
    }
}

/// Fileless malware detector
#[derive(Debug)]
pub struct FilelessMalwareDetector<'a> {
    /// Running flag
    running: Arc<AtomicBool>,
    /// Event sender
    event_sender: Option<Sender<EbpfEvent>>,
    /// Event receiver
    event_receiver: Option<Receiver<EbpfEvent>>,
    /// eBPF loader
    #[cfg(feature = "linux")]
    loader: Option<EbpfLoader<'a>>,
    /// Process information
    processes: HashMap<u32, ProcessInfo>,
    /// Sensitivity threshold (0-100)
    sensitivity: u8,
    /// Whitelist of processes
    process_whitelist: HashSet<String>,
}

impl FilelessMalwareDetector<'_> {
    /// Create a new fileless malware detector
    pub fn new(running: Arc<AtomicBool>) -> Self {
        Self {
            running,
            event_sender: None,
            event_receiver: None,
            #[cfg(feature = "linux")]
            loader: None,
            processes: HashMap::new(),
            sensitivity: 50,
            process_whitelist: HashSet::new(),
        }
    }
    
    /// Set sensitivity threshold
    pub fn set_sensitivity(&mut self, sensitivity: u8) {
        self.sensitivity = sensitivity.min(100);
        info!("Fileless malware detector sensitivity set to {}", self.sensitivity);
    }
    
    /// Add a process to the whitelist
    pub fn add_to_whitelist(&mut self, process_name: &str) {
        self.process_whitelist.insert(process_name.to_string());
        info!("Added {} to fileless malware detector whitelist", process_name);
    }
    
    /// Remove a process from the whitelist
    pub fn remove_from_whitelist(&mut self, process_name: &str) {
        self.process_whitelist.remove(process_name);
        info!("Removed {} from fileless malware detector whitelist", process_name);
    }
    
    /// Check if a process is whitelisted
    fn is_whitelisted(&self, process_name: &str) -> bool {
        self.process_whitelist.contains(process_name)
    }
    
    /// Calculate threat confidence based on suspicious operations
    fn calculate_confidence(&self, operations: &[SuspiciousMemoryOperation]) -> u8 {
        // Base confidence on the number and types of suspicious operations
        let mut confidence = 0;
        
        for op in operations {
            match op {
                SuspiciousMemoryOperation::ExecutableMemory => confidence += 10,
                SuspiciousMemoryOperation::WriteExecuteMemory => confidence += 30,
                SuspiciousMemoryOperation::AnonymousExecutableMapping => confidence += 40,
                SuspiciousMemoryOperation::ProtectionChangeToExecutable => confidence += 50,
                SuspiciousMemoryOperation::CrossProcessMemoryInjection => confidence += 70,
                SuspiciousMemoryOperation::ProcessHollowing => confidence += 80,
                SuspiciousMemoryOperation::ThreadInjection => confidence += 60,
                SuspiciousMemoryOperation::ReflectiveLoading => confidence += 75,
            }
        }
        
        // Adjust based on the number of different techniques
        let unique_operations = operations.iter().collect::<HashSet<_>>().len();
        confidence = confidence.saturating_add((unique_operations as u8) * 5);
        
        // Cap at 100
        confidence.min(100)
    }
    
    /// Process an eBPF event
    fn process_ebpf_event(&mut self, event: &EbpfEvent) -> Option<ThreatDetectionResult> {
        if event.data.len() < 8 {
            error!("Received invalid event data: too short");
            return None;
        }
        
        // Parse the event data
        // This is a simplified example - actual implementation would depend on the data format
        let event_type = EventType::from(u32::from_le_bytes([
            event.data[0], event.data[1], event.data[2], event.data[3]
        ]));
        
        let pid = event.pid;
        let tid = event.tid;
        
        // Get or create process info
        let process_info = self.processes.entry(pid).or_insert_with(|| {
            // Extract process name from event data
            let name = String::from_utf8_lossy(&event.data[16..32])
                .trim_end_matches('\0')
                .to_string();
            
            // Extract other process info from event data
            let ppid = u32::from_le_bytes([
                event.data[8], event.data[9], event.data[10], event.data[11]
            ]);
            let uid = u32::from_le_bytes([
                event.data[12], event.data[13], event.data[14], event.data[15]
            ]);
            let gid = u32::from_le_bytes([
                event.data[16], event.data[17], event.data[18], event.data[19]
            ]);
            
            ProcessInfo::new(pid, name, ppid, uid, gid)
        });
        
        // Skip whitelisted processes
        if self.is_whitelisted(&process_info.name) {
            return None;
        }
        
        // Process the event based on its type
        match event_type {
            EventType::Mmap => {
                // Extract mmap event data
                let addr = u64::from_le_bytes([
                    event.data[32], event.data[33], event.data[34], event.data[35],
                    event.data[36], event.data[37], event.data[38], event.data[39]
                ]);
                let len = u64::from_le_bytes([
                    event.data[40], event.data[41], event.data[42], event.data[43],
                    event.data[44], event.data[45], event.data[46], event.data[47]
                ]);
                let prot = u32::from_le_bytes([
                    event.data[48], event.data[49], event.data[50], event.data[51]
                ]);
                let flags = u32::from_le_bytes([
                    event.data[52], event.data[53], event.data[54], event.data[55]
                ]);
                let fd = u32::from_le_bytes([
                    event.data[56], event.data[57], event.data[58], event.data[59]
                ]);
                
                // Check for suspicious mmap operations
                if prot & 0x4 != 0 {  // PROT_EXEC
                    process_info.add_executable_region(addr, len);
                    
                    if prot & 0x2 != 0 {  // PROT_WRITE
                        process_info.add_suspicious_operation(SuspiciousMemoryOperation::WriteExecuteMemory);
                    } else {
                        process_info.add_suspicious_operation(SuspiciousMemoryOperation::ExecutableMemory);
                    }
                    
                    if flags & 0x20 != 0 {  // MAP_ANONYMOUS
                        process_info.add_suspicious_operation(SuspiciousMemoryOperation::AnonymousExecutableMapping);
                    }
                    
                    // Check if this is a memory file descriptor
                    if process_info.memory_fds.contains(&fd) {
                        process_info.add_suspicious_operation(SuspiciousMemoryOperation::ReflectiveLoading);
                    }
                }
            }
            EventType::Mprotect => {
                // Extract mprotect event data
                let addr = u64::from_le_bytes([
                    event.data[32], event.data[33], event.data[34], event.data[35],
                    event.data[36], event.data[37], event.data[38], event.data[39]
                ]);
                let len = u64::from_le_bytes([
                    event.data[40], event.data[41], event.data[42], event.data[43],
                    event.data[44], event.data[45], event.data[46], event.data[47]
                ]);
                let prot = u32::from_le_bytes([
                    event.data[48], event.data[49], event.data[50], event.data[51]
                ]);
                
                // Check for suspicious mprotect operations
                if prot & 0x4 != 0 {  // PROT_EXEC
                    process_info.add_executable_region(addr, len);
                    process_info.add_suspicious_operation(SuspiciousMemoryOperation::ProtectionChangeToExecutable);
                    
                    if prot & 0x2 != 0 {  // PROT_WRITE
                        process_info.add_suspicious_operation(SuspiciousMemoryOperation::WriteExecuteMemory);
                    }
                }
            }
            EventType::Memfd => {
                // Extract memfd_create event data
                let flags = u32::from_le_bytes([
                    event.data[32], event.data[33], event.data[34], event.data[35]
                ]);
                
                // We don't know the FD yet, but we'll mark this process as suspicious
                process_info.add_suspicious_operation(SuspiciousMemoryOperation::AnonymousExecutableMapping);
            }
            EventType::ProcessVmWritev => {
                // Extract process_vm_writev event data
                let target_pid = u32::from_le_bytes([
                    event.data[32], event.data[33], event.data[34], event.data[35]
                ]);
                
                // Check for suspicious process_vm_writev operations
                process_info.add_suspicious_operation(SuspiciousMemoryOperation::CrossProcessMemoryInjection);
                
                // If we're tracking the target process, mark it as potentially hollowed
                if let Some(target_process) = self.processes.get_mut(&target_pid) {
                    target_process.add_suspicious_operation(SuspiciousMemoryOperation::ProcessHollowing);
                }
            }
            EventType::Ptrace => {
                // Extract ptrace event data
                let options = u32::from_le_bytes([
                    event.data[32], event.data[33], event.data[34], event.data[35]
                ]);
                let target_pid = u32::from_le_bytes([
                    event.data[36], event.data[37], event.data[38], event.data[39]
                ]);
                
                // Check for suspicious ptrace operations
                process_info.add_attached_process(target_pid);
                
                // If we're tracking the target process, mark it as potentially hollowed
                if let Some(target_process) = self.processes.get_mut(&target_pid) {
                    target_process.add_suspicious_operation(SuspiciousMemoryOperation::ProcessHollowing);
                }
            }
            EventType::Exec => {
                // Extract execve event data
                let flags = u32::from_le_bytes([
                    event.data[32], event.data[33], event.data[34], event.data[35]
                ]);
                
                // Check if this is an execution from a memory file descriptor
                if process_info.memory_fds.len() > 0 {
                    process_info.add_suspicious_operation(SuspiciousMemoryOperation::ReflectiveLoading);
                }
            }
        }
        
        // Check if we have enough evidence to report a threat
        let confidence = self.calculate_confidence(&process_info.suspicious_operations);
        
        // Only report if confidence exceeds sensitivity threshold
        if confidence >= self.sensitivity {
            // Create evidence map
            let mut evidence = HashMap::new();
            for (i, op) in process_info.suspicious_operations.iter().enumerate() {
                evidence.insert(format!("operation_{}", i), format!("{:?}", op));
            }
            
            // Add process info to evidence
            evidence.insert("process_name".to_string(), process_info.name.clone());
            evidence.insert("ppid".to_string(), process_info.ppid.to_string());
            evidence.insert("uid".to_string(), process_info.uid.to_string());
            evidence.insert("gid".to_string(), process_info.gid.to_string());
            
            // Create detection result
            let result = ThreatDetectionResult {
                threat_type: ThreatType::FilelessMalware,
                confidence,
                description: format!(
                    "Detected fileless malware techniques in process {} ({})",
                    process_info.pid, process_info.name
                ),
                pid: process_info.pid,
                tid,
                evidence,
                timestamp: event.timestamp,
            };
            
            return Some(result);
        }
        
        None
    }
}

impl ThreatDetector for FilelessMalwareDetector<'_> {
    fn threat_type(&self) -> ThreatType {
        ThreatType::FilelessMalware
    }
    
    fn initialize(&mut self) -> Result<(), EbpfError> {
        // Add common legitimate processes to whitelist
        self.add_to_whitelist("chrome");
        self.add_to_whitelist("firefox");
        self.add_to_whitelist("systemd");
        self.add_to_whitelist("sshd");
        self.add_to_whitelist("bash");
        self.add_to_whitelist("zsh");
        self.add_to_whitelist("java");
        self.add_to_whitelist("python");
        self.add_to_whitelist("node");
        
        #[cfg(feature = "linux")]
        {
            // Create eBPF loader
            let loader = EbpfLoader::new(Arc::clone(&self.running))?;
            self.loader = Some(loader);
        }
        
        info!("Fileless malware detector initialized");
        
        Ok(())
    }
    
    fn start(&mut self, event_sender: Sender<EbpfEvent>) -> Result<(), EbpfError> {
        if self.running.load(Ordering::SeqCst) {
            return Ok(());
        }
        
        // Store the event sender
        self.event_sender = Some(event_sender.clone());
        
        #[cfg(feature = "linux")]
        {
            if let Some(loader) = &mut self.loader {
                // Load the eBPF program
                loader.load_object_memory(FILELESS_DETECTOR_BPF, "fileless_detector")?;
                
                // Attach the eBPF programs
                loader.attach_program("fileless_detector", "tracepoint__syscalls__sys_enter_mmap")?;
                loader.attach_program("fileless_detector", "tracepoint__syscalls__sys_enter_mprotect")?;
                loader.attach_program("fileless_detector", "tracepoint__syscalls__sys_enter_memfd_create")?;
                loader.attach_program("fileless_detector", "tracepoint__syscalls__sys_enter_process_vm_writev")?;
                loader.attach_program("fileless_detector", "tracepoint__syscalls__sys_enter_ptrace")?;
                loader.attach_program("fileless_detector", "tracepoint__syscalls__sys_enter_execve")?;
                
                // Set up ring buffer
                loader.setup_ring_buffer(
                    "fileless_detector",
                    "events",
                    event_sender,
                    ProgramType::Tracepoint,
                )?;
                
                // Start event collection
                loader.start_collection(event_sender)?;
            }
        }
        
        info!("Fileless malware detector started");
        
        Ok(())
    }
    
    fn stop(&mut self) -> Result<(), EbpfError> {
        if !self.running.load(Ordering::SeqCst) {
            return Ok(());
        }
        
        #[cfg(feature = "linux")]
        {
            if let Some(loader) = &mut self.loader {
                // Stop event collection
                loader.stop_collection()?;
                
                // Clean up resources
                loader.cleanup()?;
            }
        }
        
        // Clean up resources
        self.event_sender = None;
        
        info!("Fileless malware detector stopped");
        
        Ok(())
    }
    
    fn process_event(&mut self, event: &EbpfEvent) -> Option<ThreatDetectionResult> {
        // Process the event
        self.process_ebpf_event(event)
    }
} 