use std::time::{Duration, Instant};
use std::time::{Duration, Instant};
/**
 * Circular Buffer Implementation
 * 
 * This module provides a thread-safe circular buffer for storing events
 * with configurable overflow behavior.
 */


use tokio::sync::Mutex;
use std::sync::Condvar;
use async_trait::async_trait;

use crate::core::events::TraceEvent;
use crate::error::{Error, Result};
use super::{BufferOverflowBehavior, BufferStats, EventBuffer};

/// Default buffer capacity
pub const DEFAULT_BUFFER_CAPACITY: usize = 10000;

/// Default buffer timeout in milliseconds
pub const DEFAULT_BUFFER_TIMEOUT_MS: u64 = 5000;

/// Configuration for the circular buffer.
#[derive(Debug, Clone)]
pub struct CircularBufferConfig {
    /// Maximum number of events the buffer can hold
    pub capacity: usize,
    /// Behavior when the buffer is full
    pub overflow_behavior: BufferOverflowBehavior,
}

impl Default for CircularBufferConfig {
    fn default() -> Self {
        Self {
            capacity: DEFAULT_BUFFER_CAPACITY,
            overflow_behavior: BufferOverflowBehavior::DropOldest,
        }
    }
}

/// Behavior when the buffer is full.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OverflowBehavior {
    /// Drop the oldest event in the buffer
    DropOldest,
    /// Drop the newest event (the one being added)
    DropNewest,
    /// Block until space is available
    Block,
}

/// Internal state of the circular buffer.
struct CircularBufferState {
    /// The buffer of events
    buffer: Vec<TraceEvent>,
    /// Maximum capacity of the buffer
    capacity: usize,
    /// Behavior when the buffer is full
    overflow_behavior: BufferOverflowBehavior,
    /// Statistics for the buffer
    stats: BufferStats,
}

/// A thread-safe circular buffer for events.
pub struct CircularBuffer {
    /// The internal state of the buffer
    state: Mutex<CircularBufferState>,
    /// Condition variable for blocking operations
    condvar: Condvar,
}

impl CircularBuffer {
    /// Create a new circular buffer with the given configuration.
    ///
    /// # Arguments
    ///
    /// * `config` - The configuration for the buffer
    ///
    /// # Returns
    ///
    /// A new circular buffer
    pub fn new(config: CircularBufferConfig) -> Self {
        let state = CircularBufferState {
            buffer: Vec::with_capacity(config.capacity),
            capacity: config.capacity,
            overflow_behavior: config.overflow_behavior,
            stats: BufferStats::default(),
        };

        Self {
            state: Mutex::new(state),
            condvar: Condvar::new(),
        }
    }

    /// Create a new circular buffer with default capacity
    pub fn with_default_capacity(overflow_behavior: BufferOverflowBehavior) -> Self {
        Self::new(CircularBufferConfig {
            capacity: DEFAULT_BUFFER_CAPACITY,
            overflow_behavior,
        })
    }

    /// Add an event to the buffer.
    ///
    /// # Arguments
    ///
    /// * `event` - The event to add
    ///
    /// # Returns
    ///
    /// `true` if the event was added, `false` if it was dropped
    pub fn add(&self, event: TraceEvent) -> bool {
        let mut state = self.state.lock().unwrap();

        // Check if the buffer is full
        if state.buffer.len() >= state.capacity {
            match state.overflow_behavior {
                BufferOverflowBehavior::DropOldest => {
                    // Remove the oldest event
                    state.buffer.remove(0);
                    state.stats.dropped += 1;
                    state.stats.removed += 1;
                },
                BufferOverflowBehavior::DropNewest => {
                    // Drop the new event
                    state.stats.dropped += 1;
                    return false;
                },
                BufferOverflowBehavior::Block => {
                    // Wait until space is available
                    while state.buffer.len() >= state.capacity {
                        state = self.condvar.wait(state).unwrap();
                    }
                },
            }
        }

        // Add the event
        state.buffer.push(event);
        state.stats.added += 1;
        state.stats.current_count = state.buffer.len();

        // Update high water mark
        if state.buffer.len() > state.stats.high_water_mark {
            state.stats.high_water_mark = state.buffer.len();
        }

        // Notify waiters
        self.condvar.notify_one();

        true
    }

    /// Add multiple events to the buffer.
    ///
    /// # Arguments
    ///
    /// * `events` - The events to add
    ///
    /// # Returns
    ///
    /// The number of events that were added
    pub fn add_batch(&self, events: &[TraceEvent]) -> usize {
        let mut added = 0;

        for event in events {
            if self.add(event.clone()) {
                added += 1;
            }
        }

        added
    }

    /// Get the next event from the buffer.
    ///
    /// # Returns
    ///
    /// The next event, or `None` if the buffer is empty
    pub fn next(&self) -> Option<TraceEvent> {
        let mut state = self.state.lock().unwrap();

        if state.buffer.is_empty() {
            return None;
        }

        let event = state.buffer.remove(0);
        state.stats.removed += 1;
        state.stats.current_count = state.buffer.len();

        // Notify waiters
        self.condvar.notify_one();

        Some(event)
    }

    /// Get a batch of events from the buffer.
    ///
    /// # Arguments
    ///
    /// * `max_count` - The maximum number of events to get
    /// * `timeout` - The maximum time to wait for events
    ///
    /// # Returns
    ///
    /// A vector of events
    pub fn next_batch(&self, max_count: usize, timeout: Duration) -> Vec<TraceEvent> {
        let mut state = self.state.lock().unwrap();
        let start_time = Instant::now();
        let mut events = Vec::new();

        // Wait until there are events or the timeout expires
        while state.buffer.is_empty() && start_time.elapsed() < timeout {
            let remaining = timeout.checked_sub(start_time.elapsed()).unwrap_or(Duration::from_secs(0));
            let (new_state, _) = self.condvar.wait_timeout(state, remaining).unwrap();
            state = new_state;

            if start_time.elapsed() >= timeout {
                break;
            }
        }

        // Get up to max_count events
        let count = std::cmp::min(max_count, state.buffer.len());
        for _ in 0..count {
            if let Some(event) = state.buffer.remove(0) {
                events.push(event);
                state.stats.removed += 1;
            }
        }

        state.stats.current_count = state.buffer.len();

        // Notify waiters
        self.condvar.notify_one();

        events
    }

    /// Get the number of events in the buffer.
    ///
    /// # Returns
    ///
    /// The number of events in the buffer
    pub fn len(&self) -> usize {
        let state = self.state.lock().unwrap();
        state.buffer.len()
    }

    /// Check if the buffer is empty.
    ///
    /// # Returns
    ///
    /// `true` if the buffer is empty, `false` otherwise
    pub fn is_empty(&self) -> bool {
        let state = self.state.lock().unwrap();
        state.buffer.is_empty()
    }

    /// Get statistics for the buffer.
    ///
    /// # Returns
    ///
    /// Statistics for the buffer
    pub fn stats(&self) -> BufferStats {
        let state = self.state.lock().unwrap();
        state.stats.clone()
    }

    /// Clear the buffer.
    pub fn clear(&self) {
        let mut state = self.state.lock().unwrap();
        state.buffer.clear();
        state.stats.current_count = 0;

        // Notify waiters
        self.condvar.notify_all();
    }
}

#[async_trait]
impl EventBuffer for CircularBuffer {
    async fn add(&self, event: TraceEvent) -> Result<()> {
        let mut state = self.state.lock().await;

        // Check if the buffer is full
        if state.buffer.len() >= state.capacity {
            match state.overflow_behavior {
                BufferOverflowBehavior::DropOldest => {
                    // Remove the oldest event
                    state.buffer.remove(0);
                    state.stats.dropped += 1;
                    state.stats.removed += 1;
                },
                BufferOverflowBehavior::DropNewest => {
                    // Drop the new event
                    state.stats.dropped += 1;
                    return Ok(());
                },
                BufferOverflowBehavior::Block => {
                    // Wait until space is available
                    while state.buffer.len() >= state.capacity {
                        state = self.condvar.wait(state).await.unwrap();
                    }
                },
            }
        }

        // Add the event
        state.buffer.push(event);
        state.stats.added += 1;
        state.stats.current_count = state.buffer.len();

        // Update high water mark
        if state.buffer.len() > state.stats.high_water_mark {
            state.stats.high_water_mark = state.buffer.len();
        }

        // Notify waiters
        self.condvar.notify_one();

        Ok(())
    }
    
    async fn add_batch(&self, events: &[TraceEvent]) -> Result<()> {
        if events.is_empty() {
            return Ok(());
        }
        
        let mut state = self.state.lock().await;
        
        // Calculate available space
        let available_space = self.capacity - state.buffer.len();
        
        if events.len() <= available_space {
            // Add all events
            state.buffer.extend_from_slice(events);
            state.stats.added += events.len();
            
            // Notify waiting consumers
            self.condvar.notify_all();
        } else {
            match self.overflow_behavior {
                BufferOverflowBehavior::DropOldest => {
                    // Calculate how many old events to drop
                    let to_drop = events.len() - available_space;
                    
                    // Remove oldest events
                    if to_drop >= state.buffer.len() {
                        // Drop all existing events
                        state.stats.dropped += state.buffer.len();
                        state.buffer.clear();
                    } else {
                        // Drop some existing events
                        state.buffer.drain(0..to_drop);
                        state.stats.dropped += to_drop;
                    }
                    
                    // Add new events
                    state.buffer.extend_from_slice(events);
                    state.stats.added += events.len();
                    
                    // Notify waiting consumers
                    self.condvar.notify_all();
                }
                BufferOverflowBehavior::DropNewest => {
                    // Add as many events as possible
                    state.buffer.extend_from_slice(&events[0..available_space]);
                    state.stats.added += available_space;
                    state.stats.dropped += events.len() - available_space;
                    
                    // Notify waiting consumers
                    self.condvar.notify_all();
                }
                BufferOverflowBehavior::Block => {
                    // This is a batch operation, so we can't block
                    // Instead, we'll add as many as possible and drop the rest
                    state.buffer.extend_from_slice(&events[0..available_space]);
                    state.stats.added += available_space;
                    state.stats.dropped += events.len() - available_space;
                    
                    // Notify waiting consumers
                    self.condvar.notify_all();
                }
            }
        }
        
        // Update high water mark
        if state.buffer.len() > state.stats.high_water_mark {
            state.stats.high_water_mark = state.buffer.len();
        }
        
        Ok(())
    }
    
    async fn next(&self) -> Result<Option<TraceEvent>> {
        let mut state = self.state.lock().await;
        
        if state.buffer.is_empty() {
            Ok(None)
        } else {
            let event = state.buffer.remove(0);
            
            // Update stats
            state.stats.removed += 1;
            
            // Notify waiters
            self.condvar.notify_one();
            
            Ok(Some(event))
        }
    }
    
    async fn next_batch(&self, max_events: usize, timeout: Duration) -> Result<Vec<TraceEvent>> {
        let mut state = self.state.lock().await;
        
        if state.buffer.is_empty() {
            Ok(Vec::new())
        } else {
            // Calculate how many events to take
            let count = std::cmp::min(max_events, state.buffer.len());
            
            // Take events
            let events: Vec<TraceEvent> = state.buffer.drain(0..count).collect();
            
            // Update stats
            state.stats.removed += events.len();
            
            // Notify waiters
            self.condvar.notify_one();
            
            Ok(events)
        }
    }
    
    async fn len(&self) -> usize {
        let state = self.state.lock().await;
        state.buffer.len()
    }
    
    async fn is_empty(&self) -> bool {
        let state = self.state.lock().await;
        state.buffer.is_empty()
    }
    
    async fn stats(&self) -> BufferStats {
        let state = self.state.lock().await;
        state.stats.clone()
    }
    
    async fn clear(&self) -> Result<()> {
        let mut state = self.state.lock().await;
        let count = state.buffer.len();
        state.buffer.clear();
        
        // Update stats
        state.stats.removed += count;
        
        // Notify waiters
        self.condvar.notify_all();
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{EventData, EventSeverity, EventType, TraceEvent};

    #[tokio::test]
    async fn test_add_and_next() {
        let buffer = CircularBuffer::new(CircularBufferConfig {
            capacity: 10,
            overflow_behavior: BufferOverflowBehavior::DropOldest,
        });
        
        // Create test event
        let event = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Add event
        buffer.add(event.clone()).await.unwrap();
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 1);
        assert_eq!(stats.removed, 0);
        assert_eq!(stats.dropped, 0);
        assert_eq!(stats.high_water_mark, 1);
        
        // Get event
        let next = buffer.next().await.unwrap().unwrap();
        
        // Check event
        assert_eq!(next.id, event.id);
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 1);
        assert_eq!(stats.removed, 1);
        assert_eq!(stats.dropped, 0);
        assert_eq!(stats.high_water_mark, 1);
        
        // Check empty
        assert!(buffer.is_empty().await);
        assert_eq!(buffer.len().await, 0);
        assert!(buffer.next().await.unwrap().is_none());
    }
    
    #[tokio::test]
    async fn test_overflow_drop_oldest() {
        let buffer = CircularBuffer::new(CircularBufferConfig {
            capacity: 2,
            overflow_behavior: BufferOverflowBehavior::DropOldest,
        });
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("test1".to_string()),
            EventData::Custom(serde_json::json!({"test": 1})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("test2".to_string()),
            EventData::Custom(serde_json::json!({"test": 2})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event3 = TraceEvent::now(
            EventType::Custom("test3".to_string()),
            EventData::Custom(serde_json::json!({"test": 3})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Add events
        buffer.add(event1.clone()).await.unwrap();
        buffer.add(event2.clone()).await.unwrap();
        buffer.add(event3.clone()).await.unwrap();
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 3);
        assert_eq!(stats.removed, 0);
        assert_eq!(stats.dropped, 1);
        assert_eq!(stats.high_water_mark, 2);
        
        // Get events
        let next1 = buffer.next().await.unwrap().unwrap();
        let next2 = buffer.next().await.unwrap().unwrap();
        
        // Check events (event1 should be dropped)
        assert_eq!(next1.id, event2.id);
        assert_eq!(next2.id, event3.id);
        
        // Check empty
        assert!(buffer.is_empty().await);
    }
    
    #[tokio::test]
    async fn test_overflow_drop_newest() {
        let buffer = CircularBuffer::new(CircularBufferConfig {
            capacity: 2,
            overflow_behavior: BufferOverflowBehavior::DropNewest,
        });
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("test1".to_string()),
            EventData::Custom(serde_json::json!({"test": 1})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("test2".to_string()),
            EventData::Custom(serde_json::json!({"test": 2})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event3 = TraceEvent::now(
            EventType::Custom("test3".to_string()),
            EventData::Custom(serde_json::json!({"test": 3})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        // Add events
        buffer.add(event1.clone()).await.unwrap();
        buffer.add(event2.clone()).await.unwrap();
        buffer.add(event3.clone()).await.unwrap();
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 2);
        assert_eq!(stats.removed, 0);
        assert_eq!(stats.dropped, 1);
        assert_eq!(stats.high_water_mark, 2);
        
        // Get events
        let next1 = buffer.next().await.unwrap().unwrap();
        let next2 = buffer.next().await.unwrap().unwrap();
        
        // Check events (event3 should be dropped)
        assert_eq!(next1.id, event1.id);
        assert_eq!(next2.id, event2.id);
        
        // Check empty
        assert!(buffer.is_empty().await);
    }
    
    #[tokio::test]
    async fn test_batch_operations() {
        let buffer = CircularBuffer::new(CircularBufferConfig {
            capacity: 5,
            overflow_behavior: BufferOverflowBehavior::DropOldest,
        });
        
        // Create test events
        let events: Vec<TraceEvent> = (0..3).map(|i| {
            TraceEvent::now(
                EventType::Custom(format!("test{}", i)),
                EventData::Custom(serde_json::json!({"test": i})),
                1,
                1,
                EventSeverity::Info,
                "test",
                vec![],
            )
        }).collect();
        
        // Add batch
        buffer.add_batch(&events).await.unwrap();
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 3);
        assert_eq!(stats.removed, 0);
        assert_eq!(stats.dropped, 0);
        assert_eq!(stats.high_water_mark, 3);
        
        // Get batch
        let batch = buffer.next_batch(2, Duration::from_millis(0)).await.unwrap();
        
        // Check batch
        assert_eq!(batch.len(), 2);
        assert_eq!(batch[0].id, events[0].id);
        assert_eq!(batch[1].id, events[1].id);
        
        // Check stats
        let stats = buffer.stats().await;
        assert_eq!(stats.added, 3);
        assert_eq!(stats.removed, 2);
        assert_eq!(stats.dropped, 0);
        assert_eq!(stats.high_water_mark, 3);
        
        // Get remaining event
        let next = buffer.next().await.unwrap().unwrap();
        
        // Check event
        assert_eq!(next.id, events[2].id);
        
        // Check empty
        assert!(buffer.is_empty().await);
    }
} 