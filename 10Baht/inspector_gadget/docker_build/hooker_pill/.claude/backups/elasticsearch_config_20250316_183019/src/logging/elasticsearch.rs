/*!
 * Elasticsearch integration for Inspector Gadget
 * 
 * This module provides functionality for exporting events to Elasticsearch.
 */

use crate::core::exporter::EventExporter;
use std::sync::Arc;
use std::time::Duration;

use reqwest::{Client, ClientBuilder};
use serde::{Deserialize, Serialize};
use tokio::sync::Mutex;

use crate::core::events::TraceEvent;
use crate::error::{Error, Result};

/// Default Elasticsearch URL
const DEFAULT_ES_URL: &str = "http://localhost:9200";

/// Default Elasticsearch index
const DEFAULT_ES_INDEX: &str = "inspector-gadget";

/// Default batch size for bulk operations
const DEFAULT_BATCH_SIZE: usize = 100;

/// Default flush interval (seconds)
const DEFAULT_FLUSH_INTERVAL: u64 = 5;

/// Elasticsearch configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ElasticsearchConfig {
    /// Elasticsearch URL
    pub url: String,
    /// Elasticsearch index
    pub index: String,
    /// Username for authentication
    pub username: Option<String>,
    /// Password for authentication
    pub password: Option<String>,
    /// Batch size for bulk operations
    pub batch_size: usize,
    /// Flush interval (seconds)
    pub flush_interval: u64,
    /// Connect timeout (seconds)
    pub connect_timeout: u64,
    /// Request timeout (seconds)
    pub request_timeout: u64,
}

impl Default for ElasticsearchConfig {
    fn default() -> Self {
        ElasticsearchConfig {
            url: DEFAULT_ES_URL.to_string(),
            index: DEFAULT_ES_INDEX.to_string(),
            username: None,
            password: None,
            batch_size: DEFAULT_BATCH_SIZE,
            flush_interval: DEFAULT_FLUSH_INTERVAL,
            connect_timeout: 10,
            request_timeout: 30,
        }
    }
}

/// Elasticsearch exporter
pub struct ElasticsearchExporter {
    /// Configuration
    config: ElasticsearchConfig,
    /// HTTP client
    client: Client,
    /// Event buffer for batching
    buffer: Arc<Mutex<Vec<TraceEvent>>>,
}

impl ElasticsearchExporter {
    /// Create a new Elasticsearch exporter
    pub async fn new(config: ElasticsearchConfig) -> Result<Self> {
        // Build HTTP client
        let mut client_builder = ClientBuilder::new()
            .connect_timeout(Duration::from_secs(config.connect_timeout))
            .timeout(Duration::from_secs(config.request_timeout));

        // Add authentication if provided
        if let (Some(username), Some(password)) = (&config.username, &config.password) {
            client_builder = client_builder.basic_auth(username, Some(password));
        }

        let client = client_builder.build().map_err(|e| Error::HttpClient(e.to_string()))?;

        // Create exporter
        let exporter = ElasticsearchExporter {
            config,
            client,
            buffer: Arc::new(Mutex::new(Vec::new())),
        };

        // Check connection
        exporter.check_connection().await?;

        // Create index if it doesn't exist
        exporter.create_index_if_not_exists().await?;

        // Start background flush task
        exporter.start_flush_task();

        Ok(exporter)
    }

    /// Check connection to Elasticsearch
    async fn check_connection(&self) -> Result<()> {
        let response = self.client.get(&self.config.url)
            .send()
            .await
            .map_err(|e| Error::HttpRequest(e.to_string()))?;

        if !response.status().is_success() {
            return Err(Error::ElasticsearchConnection(format!(
                "Failed to connect to Elasticsearch: {}",
                response.status()
            )));
        }

        Ok(())
    }

    /// Create index if it doesn't exist
    async fn create_index_if_not_exists(&self) -> Result<()> {
        let index_url = format!("{}/{}", self.config.url, self.config.index);
        
        // Check if index exists
        let response = self.client.head(&index_url)
            .send()
            .await
            .map_err(|e| Error::HttpRequest(e.to_string()))?;

        if response.status().is_success() {
            // Index already exists
            return Ok(());
        }

        // Create index with mapping
        let mapping = serde_json::json!({
            "mappings": {
                "properties": {
                    "id": { "type": "keyword" },
                    "event_type": { "type": "keyword" },
                    "pid": { "type": "integer" },
                    "tid": { "type": "integer" },
                    "timestamp": { "type": "date", "format": "epoch_micros" },
                    "severity": { "type": "keyword" },
                    "source": { "type": "keyword" },
                    "tags": { "type": "keyword" },
                    "data": { "type": "object", "dynamic": true }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        });

        let response = self.client.put(&index_url)
            .json(&mapping)
            .send()
            .await
            .map_err(|e| Error::HttpRequest(e.to_string()))?;

        if !response.status().is_success() {
            return Err(Error::ElasticsearchIndex(format!(
                "Failed to create index: {}",
                response.status()
            )));
        }

        Ok(())
    }

    /// Start background flush task
    fn start_flush_task(&self) {
        let buffer = self.buffer.clone();
        let config = self.config.clone();
        let client = self.client.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(config.flush_interval));
            
            loop {
                interval.tick().await;
                
                // Get events from buffer
                let events = {
                    let mut buffer = buffer.lock().await;
                    if buffer.is_empty() {
                        continue;
                    }
                    
                    std::mem::take(&mut *buffer)
                };
                
                // Export events
                if !events.is_empty() {
                    if let Err(err) = Self::do_export_batch(&client, &config, &events).await {
                        log::error!("Error exporting events to Elasticsearch: {}", err);
                        
                        // Put events back in buffer
                        let mut buffer = buffer.lock().await;
                        buffer.extend(events);
                    }
                }
            }
        });
    }

    /// Export a batch of events to Elasticsearch
    async fn do_export_batch(client: &Client, config: &ElasticsearchConfig, events: &[TraceEvent]) -> Result<()> {
        if events.is_empty() {
            return Ok(());
        }

        // Build bulk request body
        let mut body = String::new();
        
        for event in events {
            // Add action line
            let action = serde_json::json!({
                "index": {
                    "_index": config.index,
                    "_id": event.id.to_string()
                }
            });
            body.push_str(&serde_json::to_string(&action).unwrap());
            body.push('\n');
            
            // Add document line
            body.push_str(&serde_json::to_string(&event).unwrap());
            body.push('\n');
        }

        // Send bulk request
        let bulk_url = format!("{}/_bulk", config.url);
        let response = client.post(&bulk_url)
            .header("Content-Type", "application/x-ndjson")
            .body(body)
            .send()
            .await
            .map_err(|e| Error::HttpRequest(e.to_string()))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(Error::ElasticsearchBulk(format!(
                "Bulk request failed: {} - {}",
                response.status(),
                error_text
            )));
        }

        Ok(())
    }
}

#[async_trait::async_trait]
impl EventExporter for ElasticsearchExporter {
    /// Export an event to Elasticsearch
    async fn export(&self, event: &TraceEvent) -> Result<()> {
        // Add event to buffer
        let mut buffer = self.buffer.lock().await;
        buffer.push(event.clone());
        
        // Flush if buffer is full
        if buffer.len() >= self.config.batch_size {
            let events = std::mem::take(&mut *buffer);
            drop(buffer); // Release lock before async operation
            
            Self::do_export_batch(&self.client, &self.config, &events).await?;
        }
        
        Ok(())
    }

    /// Export a batch of events to Elasticsearch
    async fn export_batch(&self, events: &[TraceEvent]) -> Result<()> {
        if events.is_empty() {
            return Ok(());
        }

        // If batch is large enough, export directly
        if events.len() >= self.config.batch_size {
            return Self::do_export_batch(&self.client, &self.config, events).await;
        }

        // Otherwise, add to buffer
        let mut buffer = self.buffer.lock().await;
        buffer.extend(events.iter().cloned());
        
        // Flush if buffer is full
        if buffer.len() >= self.config.batch_size {
            let events = std::mem::take(&mut *buffer);
            drop(buffer); // Release lock before async operation
            
            Self::do_export_batch(&self.client, &self.config, &events).await?;
        }
        
        Ok(())
    }

    /// Flush any buffered events
    async fn flush(&self) -> Result<()> {
        let events = {
            let mut buffer = self.buffer.lock().await;
            std::mem::take(&mut *buffer)
        };
        
        if !events.is_empty() {
            Self::do_export_batch(&self.client, &self.config, &events).await?;
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{EventType, EventData, EventSeverity};

    #[tokio::test]
    async fn test_elasticsearch_exporter() {
        // This test requires a running Elasticsearch instance
        // It's disabled by default to avoid test failures in CI
        if std::env::var("RUN_ELASTICSEARCH_TESTS").is_err() {
            return;
        }

        // Create exporter
        let config = ElasticsearchConfig {
            url: "http://localhost:9200".to_string(),
            index: "inspector-gadget-test".to_string(),
            ..Default::default()
        };
        
        let exporter = ElasticsearchExporter::new(config).await.unwrap();

        // Create test event
        let event = TraceEvent::new(
            1,
            EventType::Custom("Test".to_string()),
            EventData::Custom(serde_json::json!({ "test": true })),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec!["test".to_string()],
        );

        // Export event
        exporter.export(&event).await.unwrap();

        // Flush
        exporter.flush().await.unwrap();

        // Wait for indexing
        tokio::time::sleep(Duration::from_secs(1)).await;

        // Verify event was indexed
        let search_url = format!("{}/{}/_search", exporter.config.url, exporter.config.index);
        let response = exporter.client.post(&search_url)
            .json(&serde_json::json!({
                "query": {
                    "term": {
                        "id": 1
                    }
                }
            }))
            .send()
            .await
            .unwrap();

        assert!(response.status().is_success());
        
        let search_result: serde_json::Value = response.json().await.unwrap();
        let hits = search_result["hits"]["total"]["value"].as_u64().unwrap();
        
        assert_eq!(hits, 1);
    }
} 
/// Elasticsearch logger configuration
#[derive(Debug, Clone)]

/// Elasticsearch logger
#[derive(Debug)]
pub struct ElasticsearchLogger {
    /// Configuration
    pub config: ElasticsearchConfig,
    /// Client
    client: Option<()>, // Placeholder for actual client
}

impl ElasticsearchLogger {
    /// Create a new Elasticsearch logger
    pub fn new(config: ElasticsearchConfig) -> Self {
        Self {
            config,
            client: None,
        }
    }
    
    /// Log an event
    pub fn log(&self, event: &crate::core::events::Event) -> Result<(), crate::error::Error> {
        // Placeholder for actual logging
        Ok(())
    }
}
