# Troubleshooting Guide for Inspector Gadget

This document provides solutions for common issues encountered when using the Inspector Gadget System Call Interception Framework.

## Table of Contents

1. [Installation Issues](#installation-issues)
2. [Linux-Specific Issues](#linux-specific-issues)
3. [Windows-Specific Issues](#windows-specific-issues)
4. [Configuration Issues](#configuration-issues)
5. [Runtime Issues](#runtime-issues)
6. [Test Environment Issues](#test-environment-issues)
7. [Performance Issues](#performance-issues)
8. [Output Issues](#output-issues)

## Installation Issues

### Dependency Installation Failures

**Problem**: Missing dependencies during installation.

**Solution**:

On Linux:
```bash
sudo apt-get update
sudo apt-get install -y build-essential libelf-dev libbpf-dev pkg-config
```

On Windows:
```powershell
# Install Chocolatey if not already installed
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))

# Install dependencies
choco install -y visualstudio2019buildtools visualstudio2019-workload-vctools rust-ms
```

### Rust Installation Issues

**Problem**: Rust installation fails or Cargo commands don't work.

**Solution**:

On Linux:
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source $HOME/.cargo/env
```

On Windows:
```powershell
Invoke-WebRequest -Uri https://win.rustup.rs/x86_64 -OutFile rustup-init.exe
.\rustup-init.exe
```

### Build Failures

**Problem**: Build fails with compilation errors.

**Solution**:

1. Ensure you have the latest dependencies installed
2. Update Rust to the latest version: `rustup update`
3. Clean the build directory: `cargo clean`
4. Try building again: `cargo build --release`

## Linux-Specific Issues

### eBPF Not Available

**Problem**: eBPF functionality is not available.

**Solution**:

1. Check your kernel version:
   ```bash
   uname -r
   ```
   You need kernel 4.18 or newer.

2. Check if BPF syscall is available:
   ```bash
   grep bpf /proc/kallsyms
   ```

3. If using a VM, ensure that eBPF is enabled in the VM settings.

### Permission Denied

**Problem**: Permission denied when accessing eBPF features.

**Solution**:

1. Run with sudo:
   ```bash
   sudo ./inspector_gadget
   ```

2. Or set capabilities:
   ```bash
   sudo setcap cap_sys_admin+ep ./inspector_gadget
   ```

### Missing Headers

**Problem**: Missing Linux headers during build.

**Solution**:

```bash
sudo apt-get install -y linux-headers-$(uname -r)
```

## Windows-Specific Issues

### ETW Access Denied

**Problem**: Access denied when using ETW.

**Solution**:

1. Run as Administrator:
   - Right-click on Command Prompt or PowerShell and select "Run as Administrator"
   - Then run Inspector Gadget

2. Check Windows Event Log for more details:
   ```powershell
   Get-EventLog -LogName Application -Newest 20
   ```

### Missing Visual C++ Runtime

**Problem**: Missing Visual C++ Runtime.

**Solution**:

```powershell
choco install -y vcredist140
```

### Windows Defender Blocking

**Problem**: Windows Defender blocking Inspector Gadget.

**Solution**:

1. Add an exclusion for Inspector Gadget in Windows Defender:
   - Open Windows Security
   - Go to Virus & threat protection
   - Under Virus & threat protection settings, click Manage settings
   - Under Exclusions, click Add or remove exclusions
   - Add the path to Inspector Gadget

## Configuration Issues

### Invalid Configuration File

**Problem**: Configuration file is invalid or not found.

**Solution**:

1. Check the configuration file path:
   ```bash
   ./inspector_gadget --config /path/to/config.toml
   ```

2. Validate the TOML syntax:
   ```bash
   cargo install taplo-cli
   taplo check config.toml
   ```

### Filter Configuration Issues

**Problem**: Filters not working as expected.

**Solution**:

1. Check filter syntax in the configuration file
2. Use verbose logging to see which filters are applied:
   ```bash
   ./inspector_gadget --verbose --config config.toml
   ```

## Runtime Issues

### Process Termination

**Problem**: Target process terminates unexpectedly.

**Solution**:

1. Check if the process has permission to run
2. Check system logs for errors:
   ```bash
   dmesg | tail
   ```

### High CPU Usage

**Problem**: Inspector Gadget uses too much CPU.

**Solution**:

1. Limit the scope of syscall interception with filters
2. Use sampling mode instead of capturing all syscalls
3. Increase the buffer size to reduce processing overhead

### Memory Leaks

**Problem**: Memory usage grows over time.

**Solution**:

1. Update to the latest version
2. Set a timeout for the interception:
   ```bash
   ./inspector_gadget --timeout 300
   ```

## Test Environment Issues

### Docker Issues

**Problem**: Docker container fails to start or run tests.

**Solution**:

1. Ensure Docker has the necessary permissions:
   ```bash
   docker-compose down
   docker-compose up -d --build
   ```

2. Check that volumes are mounted correctly:
   ```bash
   docker-compose config
   ```

3. For Linux containers, ensure the host has eBPF support.

### Vagrant Issues

**Problem**: Vagrant VM fails to start or provision.

**Solution**:

#### Linux VM Issues

1. Check VirtualBox logs for errors
2. Try increasing memory allocation in the Vagrantfile:
   ```ruby
   vb.memory = "4096"
   ```
3. Try reprovisioning:
   ```bash
   vagrant provision linux
   ```

#### Windows VM Issues

1. Ensure your host has enough resources (at least 4GB RAM available)
2. Check if Hyper-V is enabled (may conflict with VirtualBox)
3. Try using a different provider:
   ```bash
   vagrant up windows --provider=hyperv
   ```
4. Check the provisioning log inside the VM:
   ```
   C:\vagrant\provision.log
   ```
5. If RDP doesn't work, ensure your firewall allows RDP connections

### Test Script Issues

**Problem**: Test scripts fail to run.

**Solution**:

1. Check permissions:
   ```bash
   chmod +x test/run_linux_tests.sh
   ```

2. Ensure dependencies are installed:
   ```bash
   # Linux
   sudo apt-get install -y time valgrind
   
   # Windows
   choco install -y sysinternals
   ```

## Performance Issues

### Slow Interception

**Problem**: Syscall interception is too slow.

**Solution**:

1. Use more specific filters to reduce the number of intercepted syscalls
2. Increase buffer size:
   ```bash
   ./inspector_gadget --buffer-size 8192
   ```
3. Use sampling mode:
   ```bash
   ./inspector_gadget --sampling-rate 0.1
   ```

### High Memory Usage

**Problem**: Inspector Gadget uses too much memory.

**Solution**:

1. Limit the number of collected events:
   ```bash
   ./inspector_gadget --max-events 10000
   ```
2. Use more specific filters
3. Process events in batches

## Output Issues

### Missing Output

**Problem**: No output is generated.

**Solution**:

1. Check if any syscalls were intercepted
2. Verify that the filters aren't too restrictive
3. Check if the output file is writable:
   ```bash
   ./inspector_gadget --output-file output.json --output-format json
   ```

### Invalid JSON Output

**Problem**: JSON output is invalid.

**Solution**:

1. Check if the output file was properly closed (process might have been terminated)
2. Validate the JSON:
   ```bash
   cat output.json | jq
   ```

### CSV Formatting Issues

**Problem**: CSV output is not formatted correctly.

**Solution**:

1. Check if the CSV file was properly closed
2. Validate the CSV:
   ```bash
   cat output.csv | csvlint
   ```

## Common Error Messages

### "Failed to initialize eBPF"

**Cause**: eBPF is not available or accessible.

**Solution**: See [eBPF Not Available](#ebpf-not-available) section.

### "Failed to initialize ETW"

**Cause**: ETW is not available or accessible.

**Solution**: See [ETW Access Denied](#etw-access-denied) section.

### "Failed to parse configuration file"

**Cause**: Configuration file is invalid.

**Solution**: See [Invalid Configuration File](#invalid-configuration-file) section.

### "Failed to start target process"

**Cause**: Target process could not be started.

**Solution**:
1. Check if the binary exists and is executable
2. Check permissions
3. Try running the binary directly to see any error messages

---

Last Updated: [Date] 