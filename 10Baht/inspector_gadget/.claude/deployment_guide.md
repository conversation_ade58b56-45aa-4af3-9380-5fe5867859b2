# Inspector Gadget Deployment Guide for <PERSON>

This document provides guidance for <PERSON> on how to assist users with deploying and using the Inspector Gadget System Call Interception Framework.

## Project Overview

Inspector Gadget is a system call interception and analysis framework that works on both Linux and Windows platforms. It uses eBPF on Linux and ETW on Windows to capture and analyze system calls made by applications.

The framework consists of several components:
1. **Syscall Interception**: Platform-specific modules for intercepting system calls
2. **Syscall Definitions**: Common definitions and mappings for system calls
3. **Syscall Analysis**: Pattern detection and anomaly detection for system call sequences
4. **Configuration**: TOML-based configuration for specifying targets and filters

## Deployment Process

When assisting users with deployment, follow these steps:

1. **Determine the user's platform**: Linux or Windows
2. **Check system requirements**:
   - Linux: Kernel 4.18+, libelf-dev, libbpf-dev, root privileges
   - Windows: Windows 10/Server 2016+, Administrator privileges, ETW enabled
3. **Guide through installation**:
   - From binary releases
   - From source code (requires Rust 1.56+)
4. **Help with configuration**:
   - Creating/modifying the TOML configuration file
   - Setting up target binary and filtering options
5. **Assist with execution**:
   - Command line options
   - Running with appropriate privileges

## Test Environment

The project includes a test environment setup for validating the functionality:

1. **Test directory**: `/test-temp/0001/` (Linux) or `C:\test-temp\0001\` (Windows)
2. **Test programs**:
   - Linux: A C program that performs various syscalls
   - Windows: A C++ program that performs various Windows API calls
3. **Test configuration**: A TOML file configured for the test programs
4. **Setup scripts**:
   - `test/setup_test_env.sh` for Linux
   - `test/setup_test_env.bat` for Windows

To set up the test environment:
- Linux: `bash test/setup_test_env.sh`
- Windows: Run `test/setup_test_env.bat` from a Developer Command Prompt

## Common Issues and Solutions

When helping users troubleshoot deployment issues, consider these common problems:

### Linux
1. **eBPF not available**: Check kernel version (`uname -r`), should be 4.18+
2. **Permission denied**: Run with sudo or set capabilities (`sudo setcap cap_sys_admin+ep ./inspector_gadget`)
3. **Missing dependencies**: Install `libelf-dev` and `libbpf-dev` packages

### Windows
1. **ETW access denied**: Ensure running as Administrator
2. **ETW not enabled**: Check Event Tracing for Windows service
3. **Missing Visual C++ Runtime**: Install the appropriate Visual C++ Redistributable

## Configuration Examples

When helping users create configurations, refer to these examples:

### Basic Configuration
```toml
[general]
log_level = "info"
output_format = "text"
output_file = ""

[target]
binary_path = "/path/to/target"
arguments = []

[filter]
exclude_names = ["brk", "mmap", "clock_gettime"]

[analysis]
detect_patterns = true
detect_anomalies = true
```

### Security Monitoring Configuration
```toml
[general]
log_level = "info"
output_format = "json"
output_file = "security_events.json"

[target]
binary_path = "/path/to/target"
arguments = []

[filter]
include_categories = ["Security", "Process"]
only_security_sensitive = true

[analysis]
detect_patterns = true
detect_anomalies = true
min_anomaly_severity = 0.7
```

## Version Compatibility

- Linux: Tested on Ubuntu 20.04, 22.04, and Fedora 36, 37
- Windows: Tested on Windows 10 (21H2), Windows 11, and Windows Server 2019, 2022

## Future Development

When discussing future plans with users, you can mention:
- macOS support using DTrace
- Real-time visualization of syscall activity
- Machine learning-based anomaly detection
- Performance improvements for high-throughput applications 