# Testing Strategy for Inspector Gadget

This document outlines the testing strategy for the Inspector Gadget System Call Interception Framework.

## Table of Contents

1. [Testing Objectives](#testing-objectives)
2. [Testing Environments](#testing-environments)
3. [Types of Tests](#types-of-tests)
4. [Test Automation](#test-automation)
5. [Continuous Integration](#continuous-integration)
6. [Test Coverage](#test-coverage)
7. [Performance Testing](#performance-testing)
8. [Security Testing](#security-testing)
9. [Cross-Platform Testing](#cross-platform-testing)
10. [Test Reporting](#test-reporting)
11. [Hooker Pill Testing](#hooker-pill-testing)

## Testing Objectives

The primary objectives of testing Inspector Gadget are:

1. **Functionality Verification**: Ensure that all features work as expected.
2. **Cross-Platform Compatibility**: Verify functionality across different operating systems.
3. **Performance Optimization**: Identify and address performance bottlenecks.
4. **Security Assurance**: Validate that security-sensitive operations are properly detected and handled.
5. **Usability Enhancement**: Ensure that the tool is user-friendly and provides clear output.

## Testing Environments

Inspector Gadget is tested in multiple environments to ensure cross-platform compatibility and reliability:

### Local Development Environment

Developers test Inspector Gadget on their local machines during development. This includes:

- Unit tests
- Integration tests
- Manual testing of new features

### Vagrant Virtual Machines

Vagrant is used to create consistent, reproducible test environments:

- **Linux VM (Ubuntu 20.04)**: For testing Linux-specific functionality
  - eBPF availability
  - Linux syscall interception
  - Linux-specific syscall analysis

- **Windows VM (Windows 10)**: For testing Windows-specific functionality
  - ETW availability
  - Windows syscall interception
  - Windows-specific syscall analysis

The Vagrant setup is located in `test-environments/vagrant/` and includes:
- `Vagrantfile`: Main configuration for both Linux and Windows VMs
- `Vagrantfile.windows`: Dedicated configuration for Windows testing
- `provision/linux.sh`: Provisioning script for Linux
- `provision/windows.ps1`: Provisioning script for Windows

### Docker Containers

Docker is used for lightweight, isolated testing environments:

- **Linux Container**: Based on Ubuntu 20.04
  - Quick testing of Linux functionality
  - CI/CD pipeline integration
  - Automated testing

- **Windows Container**: Based on Windows Nano Server (ltsc2022)
  - Testing Windows functionality in containers
  - Requires Windows host with Docker Desktop

### CI/CD Environments

GitHub Actions is used for continuous integration and deployment:

- Linux runners for Linux testing
- Windows runners for Windows testing
- Matrix testing across different OS versions

## Types of Tests

Inspector Gadget employs several types of tests to ensure quality:

### Unit Tests

Unit tests verify the functionality of individual components:

- Syscall mapping
- Syscall filtering
- Event collection
- Configuration parsing

### Integration Tests

Integration tests verify the interaction between components:

- Syscall interception and filtering
- Event collection and analysis
- Output formatting

### System Tests

System tests verify the functionality of the entire system:

- End-to-end testing of syscall interception
- Testing with real-world applications
- Cross-platform testing

### Performance Tests

Performance tests measure the performance of Inspector Gadget:

- CPU usage
- Memory usage
- Syscall overhead

### Security Tests

Security tests verify the security aspects of Inspector Gadget:

- Privilege escalation detection
- Sensitive file access detection
- Network activity detection

## Test Automation

Test automation is implemented using:

- Rust's built-in testing framework
- Shell scripts for Linux testing
- Batch scripts for Windows testing
- GitHub Actions for CI/CD

## Continuous Integration

Continuous integration is implemented using GitHub Actions:

- Automated testing on each commit
- Matrix testing across different OS versions
- Scheduled nightly builds and tests

## Test Coverage

Test coverage is measured using:

- Rust's built-in coverage tools
- Custom coverage reporting for syscall interception

## Performance Testing

Performance testing includes:

- CPU usage measurement
- Memory usage measurement
- Syscall overhead measurement
- Load testing

## Security Testing

Security testing includes:

- Privilege escalation testing
- File access testing
- Network activity testing
- Compliance testing

## Cross-Platform Testing

Cross-platform testing ensures that Inspector Gadget works on:

- Linux (various distributions and kernel versions)
- Windows (10, 11, Server 2016, Server 2019, Server 2022)

## Test Reporting

Test results are reported in:

- GitHub Actions logs
- JSON output files
- CSV output files
- HTML reports

## Hooker Pill Testing

The Hooker Pill component is a critical part of the Inspector Gadget system, requiring specialized testing approaches due to its deep integration with the operating system. We use virtual machines to isolate the testing environment and ensure reproducible results.

### Hooker Pill VM Injection Framework

Our VM injection framework for hooker pill testing consists of several components:

1. **VM Management**:
   - Automated VM provisioning through Vagrant
   - Dynamic VM naming and registration
   - State verification and recovery mechanisms

2. **Pill Injection Pipeline**:
   - Automated detection of available VMs
   - Pill configuration based on test type (LSM, XDP, Tracepoint, Uprobe)
   - Injection verification with retry mechanisms
   - Health monitoring throughout pill lifecycle

3. **Performance Metrics**:
   - Latency tracking for pill operations
   - Progress monitoring for test completion
   - Health status verification and reporting
   - Statistical analysis of performance metrics

### Testing Scripts

We've developed several testing scripts to support different testing scenarios:

1. **run_pill_test.py**: Quick and reliable script for testing with running VMs
   - Auto-detects available VMs and their test types
   - Injects and monitors hooker pills
   - Collects and reports performance metrics
   - Provides cleanup options

2. **hooker_pill_test.py**: Comprehensive end-to-end testing framework
   - Creates and manages VMs for all test types
   - Runs complete test suites across all hooker types
   - Generates detailed test reports
   - Integrates with CI/CD pipelines

3. **inject_pill_test.py**: Debug script for pill injection verification
   - Simplified interface for quick verification
   - Low-level access to VM and pill operations
   - Useful for debugging specific issues

### Test Coverage

The hooker pill tests cover all four types of eBPF hookers:

1. **LSM Hookers**: Testing Linux Security Module hooks
2. **XDP Hookers**: Testing eXpress Data Path packet processing
3. **Tracepoint Hookers**: Testing kernel tracepoint instrumentation
4. **Uprobe Hookers**: Testing user-space probes for application instrumentation

Each test type verifies functionality, performance, and reliability of the hooking mechanism.

## Test Environments Setup

### Vagrant Setup

To set up the Vagrant test environments:

```bash
# Clone the repository
git clone https://github.com/yourusername/inspector_gadget.git
cd inspector_gadget

# Start the Linux VM
cd test-environments/vagrant
vagrant up linux

# Start the Windows VM
vagrant up windows

# SSH into the Linux VM
vagrant ssh linux

# RDP into the Windows VM
vagrant rdp windows
```

### Docker Setup

To set up the Docker test environments:

```bash
# Clone the repository
git clone https://github.com/yourusername/inspector_gadget.git
cd inspector_gadget

# Build and start the Linux container
cd test-environments/docker
docker-compose up -d

# Access the Linux container
docker-compose exec inspector-gadget-linux bash

# For Windows containers (requires Windows host):
# Pull the Windows Nano Server image
docker pull mcr.microsoft.com/windows/nanoserver:ltsc2022

# Build and start the Windows container
docker-compose -f docker-compose.windows.yml up -d

# Access the Windows container
docker-compose -f docker-compose.windows.yml exec inspector-gadget-windows cmd.exe
```

## Running Tests

### Running Tests in Vagrant VMs

#### Linux VM

```bash
cd /inspector_gadget
sudo ./test/run_linux_tests.sh
```

#### Windows VM

```cmd
cd C:\inspector_gadget
test\run_windows_tests.bat
```

### Running Tests in Docker Containers

#### Linux Container

```bash
cd /test-temp/0001
./inspector_gadget --config config.toml
```

#### Windows Container

```cmd
cd C:\test-temp\0001
inspector_gadget.exe --config config.toml
```

## Test Maintenance

Tests should be maintained and updated when:

- New features are added
- Bugs are fixed
- Dependencies are updated
- Operating system versions change

---

*Note: This strategy should evolve as the project grows and new testing considerations arise. Testing is an ongoing process that should adapt to the changing needs of the project.* 