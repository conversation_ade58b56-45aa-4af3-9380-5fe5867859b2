# Vagrant/VirtualBox Compatibility Options

This document tracks the options to fix compatibility issues between nix-shell and host system libraries when using VirtualBox and Vagrant for hooker_pill testing.

## Problem Statement

When trying to set up the Alpine testing environment using Vagrant/VirtualBox, we encounter compatibility issues between the host system libraries and those provided by nix-shell (specifically, glibc version mismatches).

<PERSON><PERSON><PERSON> observed:
```
/home/<USER>/.nix-profile/bin/VBoxManage: /usr/lib/libstdc++.so.6: version `CXXABI_1.3.15' not found (required by /home/<USER>/.nix-profile/bin/VBoxManage)
/home/<USER>/.nix-profile/bin/VBoxManage: /usr/lib/libc.so.6: version `GLIBC_2.38' not found (required by /nix/store/wfppgm2jhfr9zf64ry6znch9gdvwdwkp-virtualbox-7.1.6a/libexec/virtualbox/VBoxXPCOM.so)
```

## Detailed Problem Analysis

We've identified that the issue is more complex than initially thought:

1. The system has VirtualBox installed via nix (`/home/<USER>/.nix-profile/bin/VBoxManage`)
2. This nix-provided VirtualBox has library dependencies on newer glibc and libstdc++ versions than are available on the host system
3. When we clear PATH to remove nix entries, vagrant can't find VirtualBox at all
4. The system doesn't have VirtualBox installed outside of nix (no `/usr/bin/VBoxManage`)

This suggests we need a more comprehensive solution than just manipulating environment variables.

## Options to Fix

### Option 1: Use Host System's VirtualBox Instead of Nix Version

- **Implementation**: Create a wrapper script that temporarily removes nix paths from PATH
- **File**: `/home/<USER>/cdev/inspector_gadget/.claude/tools/vbox-wrapper.sh`
- **Status**: Partial Success - Script works but can't find VirtualBox outside of nix
- **Testing Instructions**:
  ```bash
  cd /home/<USER>/cdev/inspector_gadget/test-environments/vagrant
  /home/<USER>/cdev/inspector_gadget/.claude/tools/vbox-wrapper.sh status
  ```

### Option 2: Modify shell.nix to Handle Library Paths

- **Implementation**: Edit `hooker_pill/shell.nix` to conditionally handle VirtualBox
- **File**: `/home/<USER>/cdev/inspector_gadget/hooker_pill/shell.nix.option2`
- **Status**: Not tested yet
- **Testing Instructions**:
  ```bash
  cd /home/<USER>/cdev/inspector_gadget/hooker_pill
  cp shell.nix shell.nix.backup
  cp /home/<USER>/cdev/inspector_gadget/hooker_pill/shell.nix.option2 shell.nix
  nix-shell
  # Inside nix-shell, try:
  vagrant status
  # Restore original shell.nix when done
  exit
  mv shell.nix.backup shell.nix
  ```

### Option 3: Create a Simple Shell Script for Alpine Testing

- **Implementation**: Create a script that escapes the nix-shell environment for VirtualBox operations
- **File**: `/home/<USER>/cdev/inspector_gadget/.claude/tools/alpine-test.sh`
- **Status**: Tested - FAILURE - Can't find VirtualBox outside nix
- **Testing Instructions**:
  ```bash
  /home/<USER>/cdev/inspector_gadget/.claude/tools/alpine-test.sh
  ```

### Option 4: Fix Host System's LD_LIBRARY_PATH

- **Implementation**: Create a script that temporarily fixes the library path for VirtualBox
- **File**: `/home/<USER>/cdev/inspector_gadget/.claude/tools/fix-vbox.sh`
- **Status**: Partial Success - Script works but can't find VirtualBox outside of nix
- **Testing Instructions**:
  ```bash
  cd /home/<USER>/cdev/inspector_gadget/test-environments/vagrant
  /home/<USER>/cdev/inspector_gadget/.claude/tools/fix-vbox.sh vagrant status
  ```

### Option 5: Install VirtualBox System-Wide (NEW)

- **Implementation**: Install VirtualBox directly on the host system (outside of nix)
- **Status**: Not implemented
- **Instructions**:
  ```bash
  # Depending on the distribution, one of these:
  sudo apt-get install virtualbox vagrant # Debian/Ubuntu
  sudo dnf install VirtualBox vagrant     # Fedora
  # Then restart the terminal or log out/in
  ```

### Option 6: Use Docker for Alpine Testing (NEW)

- **Implementation**: Use Docker to create an Alpine environment for testing instead of a VM
- **Status**: Implemented, ready for testing
- **Files**: 
  - `/home/<USER>/cdev/inspector_gadget/test-environments/docker/Dockerfile.alpine`
  - `/home/<USER>/cdev/inspector_gadget/test-environments/docker/docker-compose.alpine.yml`
  - `/home/<USER>/cdev/inspector_gadget/test-environments/docker/run-alpine-tests.sh`
- **Testing Instructions**:
  ```bash
  # Option A: Use docker-compose
  cd /home/<USER>/cdev/inspector_gadget/test-environments/docker
  docker-compose -f docker-compose.alpine.yml up

  # Option B: Build and run directly
  cd /home/<USER>/cdev/inspector_gadget
  docker build -t hooker-alpine-test -f test-environments/docker/Dockerfile.alpine .
  
  # Run in test mode (runs tests and exits)
  docker run --privileged -v $(pwd):/inspector_gadget hooker-alpine-test
  
  # Run in interactive mode (drops to shell)
  docker run --privileged -v $(pwd):/inspector_gadget -it hooker-alpine-test shell
  
  # Run with persistence (keeps container running after tests)
  docker run --privileged -v $(pwd):/inspector_gadget hooker-alpine-test persist
  ```

## Testing Results

| Option | Test Date       | Success/Failure | Notes                                                      |
|--------|-----------------|----------------|-----------------------------------------------------------|
| 1      | Mar 22, 2024    | PARTIAL        | Script works, but no VirtualBox installed outside of nix   |
| 2      |                 |                |                                                           |
| 3      | Mar 22, 2024    | FAILURE        | Cannot find VirtualBox executable outside of nix          |
| 4      | Mar 22, 2024    | PARTIAL        | Script works, but no VirtualBox installed outside of nix   |
| 5      |                 |                |                                                           |
| 6      | Mar 22, 2024    | PENDING        | Implementation complete, ready for testing                |

## Recommended Next Steps

Based on our testing so far, we have two main paths forward:

1. **System-wide VirtualBox + Script Approach**:
   - Install VirtualBox and Vagrant system-wide (Option 5)
   - Use either the vbox-wrapper.sh or fix-vbox.sh script to run VMs

2. **Docker Approach** (Recommended):
   - Use the Docker-based Alpine testing environment (Option 6)
   - This avoids VirtualBox compatibility issues entirely
   - Provides a clean, isolated environment for testing with Alpine Linux

To test the Docker approach:

1. Install Docker if not already installed:
   ```bash
   # Debian/Ubuntu
   sudo apt-get install docker.io docker-compose
   
   # Fedora
   sudo dnf install docker docker-compose
   ```

2. Start the Docker service:
   ```bash
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

3. Add your user to the docker group (to run without sudo):
   ```bash
   sudo usermod -aG docker $USER
   # Log out and log back in for this to take effect
   ```

4. Run the testing environment:
   ```bash
   cd /home/<USER>/cdev/inspector_gadget
   docker-compose -f test-environments/docker/docker-compose.alpine.yml up
   ```

## Conclusion

After testing multiple approaches to fix the compatibility issues, we found that the fundamental issue is that VirtualBox is only installed through nix, and this version has library incompatibilities with the host system. 

The most promising solutions are:
1. Install VirtualBox system-wide and use our scripts to bypass nix-shell
2. Use Docker for Alpine testing instead of VirtualBox VMs

We have implemented the Docker-based approach, which provides several advantages:
- Avoids the VirtualBox compatibility issues entirely
- Provides a clean, isolated Alpine Linux environment
- Has proper integration with Elasticsearch for logging
- Supports testing all hook types (LSM, XDP, Tracepoint, Uprobe)
- Is more portable and less dependent on host system configuration

The Docker implementation includes:
- A Dockerfile with all necessary dependencies for hooker_pill testing
- A docker-compose configuration with Elasticsearch and Kibana integration
- A comprehensive test script that logs before and after each test 