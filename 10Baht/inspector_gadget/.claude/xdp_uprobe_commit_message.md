feat(testing): Add XDP and uprobe hooking test scripts

This commit adds scripts for testing the XDP and uprobe hooking functionality
using nix-shell. It also documents the current issues and recommendations for
improving the testing process.

Key additions:
- <PERSON><PERSON><PERSON> for running XDP and uprobe tests with pimp.py
- <PERSON><PERSON><PERSON> for running local XDP and uprobe tests
- <PERSON><PERSON><PERSON> for running tests while skipping eBPF compilation
- Documentation of current issues and recommendations
- Elasticsearch query templates for syscall detection

The testing scripts currently encounter compilation issues due to module
structure problems and eBPF compilation dependencies. The commit includes
a detailed analysis of these issues and recommendations for resolving them.

Next steps:
- Fix module structure issues in the codebase
- Update nix-shell configuration to include all necessary dependencies
- Create simplified test versions that don't require eBPF compilation

Related: #123 