# XDP and Uprobe eBPF Hooker Debugging Guide

**Date Created: 2024-03-17**
**Last Updated: 2024-03-17 16:00:00**

## 1. Problem Statement

We've encountered issues with the XDP and uprobe eBPF hookers not functioning correctly when tested through the pimp.py VM orchestration tool. The hooker pills are injected into VMs but either fail to load/attach the eBPF programs or fail to properly send observations back to Elasticsearch.

## 2. Methodology

We've implemented a systematic approach to debugging with the following components:

### 2.1 Enhanced Logging Strategy

We've added timestamped logging throughout the codebase to track the execution flow and identify failure points:

1. **Component Initialization Logging**:
   - System environment details (kernel version, CPU count, capabilities)
   - Compiler toolchain information (clang, LLVM tools)
   - Network interface details for XDP

2. **eBPF Program Lifecycle Logging**:
   - Program compilation and loading
   - Program attachment to hooks
   - Program execution and events

3. **Per-CPU Maps Logging**:
   - Map creation and initialization
   - Data insertion and retrieval operations
   - Aggregation across CPUs

4. **Elasticsearch Integration Logging**:
   - Connection establishment and authentication
   - Data serialization and bulk operations
   - Error handling and transmission status

### 2.2 Key Files Modified

1. `/src/hookers/uprobe_hooker.rs` - Added environment checks and detailed operation logging
2. `/src/hookers/xdp_hooker.rs` - Added network interface checking and per-CPU map diagnostics
3. `/src/ebpf/loader.rs` - Added detailed program loading and attachment logging
4. `/src/elasticsearch/mod.rs` - Added connection and data transmission logging

### 2.3 Implementation Approach

Our implementation follows these principles:

1. **Non-intrusive Logging**: 
   - Added logging without changing core functionality
   - Used a consistent timestamp format across all components
   - Ensured log levels are appropriate (debug for detailed operations, info for key events, error for failures)

2. **Environment Validation**:
   - Added helper functions to check kernel version, BPF capabilities, and compiler tools
   - Implemented network interface detection for XDP hooker
   - Added validation of kernel headers and eBPF prerequisites

3. **Error Propagation**:
   - Enhanced error messages with detailed context
   - Added error logging before returning errors
   - Improved error handling in Elasticsearch communication

4. **Test Script Enhancements**:
   - Created a more robust test script with proper logging
   - Added system requirements checking
   - Implemented separate test runs for each hooker type
   - Added automatic log analysis for common error patterns

5. **Nix-Shell Integration**:
   - Ensured all operations run within the nix-shell environment
   - Added validation of nix-shell dependencies
   - Implemented proper environment isolation for consistent testing
   - Added specific error detection for nix-shell related issues

## 3. Potential Issues

Based on our analysis, we've identified several potential sources of the problems:

1. **eBPF Compilation Environment**:
   - Missing or incompatible clang/LLVM tools
   - Kernel header mismatches
   - Insufficient permissions for BPF operations

2. **Per-CPU Maps Implementation**:
   - Synchronization issues during data aggregation
   - Memory allocation or management problems
   - Type safety or size calculations errors

3. **VM Resource Constraints**:
   - Insufficient CPU/memory resources
   - Network interface configuration issues
   - Capability/permission restrictions

4. **Elasticsearch Connectivity**:
   - Network connectivity issues
   - Authentication failures
   - Index configuration problems
   - Data serialization issues

5. **gRPC Communication**:
   - Transport failures between host and VM
   - Protocol version mismatches
   - Connection timeouts or drops

6. **Nix-Shell Environment Issues**:
   - Missing dependencies in shell.nix
   - Inconsistent environment variables
   - Version conflicts between dependencies
   - Improper isolation from host system

## 4. Investigation Steps

1. **Verify Environment Requirements**:
   - The enhanced logging will now report the kernel version, available BPF capabilities, and compiler toolchain
   - This will help identify if the environment is properly configured for eBPF operations

2. **Trace eBPF Program Lifecycle**:
   - The enhanced loader logging will show if programs are being loaded and attached correctly
   - Look for specific error messages related to compilation, loading or attachment

3. **Validate Per-CPU Maps**:
   - The detailed logging will show if maps are being created successfully
   - Check for errors during map operations or data aggregation

4. **Test Elasticsearch Connectivity**:
   - The enhanced Elasticsearch logging will identify connection issues
   - Look for serialization errors or failed bulk operations

5. **Validate Nix-Shell Environment**:
   - Check for proper dependency resolution in shell.nix
   - Verify that all required tools are available within the nix-shell
   - Ensure consistent environment variables across all operations

## 5. Data Collection Plan

For each test run:

1. Collect and analyze logs from:
   - Host system running pimp.py
   - VM guest running the hooker pills
   - Elasticsearch server (if available)
   - Nix-shell environment information

2. Focus on timestamps to correlate events across components:
   - All logs now include precise timestamps (Unix time with milliseconds)
   - This helps track the sequence of operations and identify delays or timeouts

3. Check for error patterns:
   - Look for recurring errors or failures
   - Identify specific components or operations that consistently fail
   - Analyze nix-shell related errors separately

## 6. Next Steps

1. **Run Focused Tests**:
   - Test each hooker type (XDP, uprobe) separately
   - Start with minimal configuration to isolate issues
   - Ensure all tests run within the nix-shell environment

2. **Validate Core Components**:
   - Test eBPF program loading without hooker integration
   - Test Per-CPU Maps independently
   - Test Elasticsearch connectivity directly
   - Verify nix-shell environment completeness

3. **Iterative Refinement**:
   - Add more detailed logging as needed based on initial findings
   - Isolate problematic components for targeted fixes
   - Update shell.nix if missing dependencies are identified

## 7. Using Nix-Shell for Testing

To ensure consistent and reproducible testing, we've integrated nix-shell into our debugging approach:

1. **Environment Isolation**:
   - All tests run within the nix-shell environment defined by shell.nix
   - This ensures consistent dependencies and tools across all test runs
   - Prevents interference from host system packages

2. **Dependency Management**:
   - shell.nix defines all required dependencies for building and testing
   - Includes Rust, clang, LLVM tools, and Python dependencies
   - Ensures consistent versions across all components

3. **Compilation Process**:
   - All Rust code is compiled within the nix-shell environment
   - Debug symbols are enabled for better error reporting
   - Verbose output captures all compilation warnings and errors

4. **Test Execution**:
   - pimp.py is run within the nix-shell environment
   - All VM operations are performed through nix-shell
   - Environment variables are properly propagated

5. **Debugging Commands**:
   ```bash
   # Run the debug tests using nix-shell
   ./run_debug_tests.sh
   
   # Manually run commands in nix-shell
   nix-shell --run "cd tools/pimp && python3 pimp.py status"
   
   # Compile the project in nix-shell
   nix-shell --run "cargo build --verbose"
   ```

## 8. Expected Outcomes

Based on our enhanced logging, we expect to identify one of the following scenarios:

1. **eBPF Compilation Environment Issues**:
   - Logs will show errors during program loading with specific clang/LLVM errors
   - Environment checks will reveal missing tools or incompatible versions
   - Kernel header version mismatches will be visible in the logs

2. **Per-CPU Maps Issues**:
   - Logs will show successful program loading but errors during map creation
   - Map operations might fail with specific error codes
   - Memory allocation errors might be visible

3. **Elasticsearch Connectivity Issues**:
   - Logs will show successful program and map operations but errors during data transmission
   - Connection timeouts or authentication failures will be visible
   - Bulk operation errors might indicate index or serialization problems

4. **Nix-Shell Environment Issues**:
   - Logs will show errors related to missing dependencies
   - Environment variable inconsistencies might be visible
   - Tool version mismatches might cause compilation or runtime errors

## 9. References

- [eBPF Documentation](https://ebpf.io/what-is-ebpf/)
- [libbpf-rs Documentation](https://github.com/libbpf/libbpf-rs)
- [Elasticsearch Bulk API](https://www.elastic.co/guide/en/elasticsearch/reference/current/docs-bulk.html)
- [Per-CPU Maps Documentation](docs/percpumap_architecture.md)
- [Nix Package Manager](https://nixos.org/manual/nix/stable/)
- [Nix-Shell Documentation](https://nixos.org/manual/nix/stable/command-ref/nix-shell.html) 