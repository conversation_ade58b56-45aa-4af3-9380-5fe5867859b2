use std::sync::Arc;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use serde::{Serialize, Deserialize};

use inspector_gadget::ebpf::maps::{TypedOptimizedPerCpuMap, PerCpuMapStats, MapError};
use inspector_gadget::logging::ElasticsearchLogger;

/// Connection tuple (5-tuple)
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct ConnectionTuple {
    /// Source IP address
    pub src_ip: IpAddr,
    /// Destination IP address
    pub dst_ip: IpAddr,
    /// Source port
    pub src_port: u16,
    /// Destination port
    pub dst_port: u16,
    /// Protocol
    pub protocol: u8,
}

/// Connection state
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, Serialize, Deserialize, PartialEq, Eq)]
pub enum ConnectionState {
    /// New connection
    New,
    /// Established connection
    Established,
    /// Connection closing
    Closing,
    /// Connection closed
    Closed,
}

/// Connection statistics
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct ConnectionStats {
    /// Connection state
    pub state: ConnectionState,
    /// Packets sent
    pub packets_sent: u64,
    /// Packets received
    pub packets_received: u64,
    /// Bytes sent
    pub bytes_sent: u64,
    /// Bytes received
    pub bytes_received: u64,
    /// Creation timestamp
    pub created_at: u64,
    /// Last update timestamp
    pub last_updated: u64,
}

/// Bandwidth usage
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct BandwidthUsage {
    /// Bytes per second
    pub bytes_per_second: u64,
    /// Packets per second
    pub packets_per_second: u64,
    /// Timestamp
    pub timestamp: u64,
}

/// XDP Hooker with Per-CPU Maps
pub struct XdpHooker {
    /// Connection map
    connection_map: TypedOptimizedPerCpuMap<u64, ConnectionStats>,
    /// Bandwidth map
    bandwidth_map: TypedOptimizedPerCpuMap<u64, BandwidthUsage>,
    /// Elasticsearch logger
    es_logger: Arc<ElasticsearchLogger>,
}

impl XdpHooker {
    /// Create a new XDP Hooker
    pub fn new(es_logger: Arc<ElasticsearchLogger>) -> Result<Self, MapError> {
        // Create connection map
        let connection_map = TypedOptimizedPerCpuMap::<u64, ConnectionStats>::create(4096)?;
        
        // Create bandwidth map
        let bandwidth_map = TypedOptimizedPerCpuMap::<u64, BandwidthUsage>::create(1024)?;
        
        Ok(Self {
            connection_map,
            bandwidth_map,
            es_logger,
        })
    }
    
    /// Hash connection tuple to u64
    fn hash_connection_tuple(tuple: &ConnectionTuple) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        tuple.hash(&mut hasher);
        hasher.finish()
    }
    
    /// Get connection statistics
    pub fn get_connection_stats(&self, tuple: &ConnectionTuple, cpu: usize) -> Result<Option<ConnectionStats>, MapError> {
        let key = Self::hash_connection_tuple(tuple);
        self.connection_map.lookup_cpu(&key, cpu)
    }
    
    /// Update connection statistics
    pub fn update_connection_stats(
        &self,
        tuple: &ConnectionTuple,
        packet_size: u32,
        is_ingress: bool,
        cpu: usize,
    ) -> Result<(), MapError> {
        let key = Self::hash_connection_tuple(tuple);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Get current stats or create new ones
        let stats = match self.connection_map.lookup_cpu(&key, cpu)? {
            Some(mut s) => {
                // Update existing stats
                if is_ingress {
                    s.packets_received += 1;
                    s.bytes_received += packet_size as u64;
                } else {
                    s.packets_sent += 1;
                    s.bytes_sent += packet_size as u64;
                }
                
                // Update state if needed
                if s.state == ConnectionState::New && (s.packets_sent > 0 && s.packets_received > 0) {
                    s.state = ConnectionState::Established;
                }
                
                s.last_updated = now;
                s
            }
            None => {
                // Create new stats
                ConnectionStats {
                    state: ConnectionState::New,
                    packets_sent: if is_ingress { 0 } else { 1 },
                    packets_received: if is_ingress { 1 } else { 0 },
                    bytes_sent: if is_ingress { 0 } else { packet_size as u64 },
                    bytes_received: if is_ingress { packet_size as u64 } else { 0 },
                    created_at: now,
                    last_updated: now,
                }
            }
        };
        
        // Update connection map
        self.connection_map.update_cpu(&key, &stats, cpu)?;
        
        // Update bandwidth map
        self.update_bandwidth(tuple, packet_size, is_ingress, cpu)?;
        
        Ok(())
    }
    
    /// Update bandwidth usage
    fn update_bandwidth(
        &self,
        tuple: &ConnectionTuple,
        packet_size: u32,
        is_ingress: bool,
        cpu: usize,
    ) -> Result<(), MapError> {
        let key = Self::hash_connection_tuple(tuple);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Get current bandwidth or create new one
        let bandwidth = match self.bandwidth_map.lookup_cpu(&key, cpu)? {
            Some(mut b) => {
                // Update existing bandwidth
                if now == b.timestamp {
                    // Same second, update counters
                    b.bytes_per_second += packet_size as u64;
                    b.packets_per_second += 1;
                } else {
                    // New second, reset counters
                    b.bytes_per_second = packet_size as u64;
                    b.packets_per_second = 1;
                    b.timestamp = now;
                }
                b
            }
            None => {
                // Create new bandwidth
                BandwidthUsage {
                    bytes_per_second: packet_size as u64,
                    packets_per_second: 1,
                    timestamp: now,
                }
            }
        };
        
        // Update bandwidth map
        self.bandwidth_map.update_cpu(&key, &bandwidth, cpu)
    }
    
    /// Get total bandwidth for a connection
    pub fn get_total_bandwidth(&self, tuple: &ConnectionTuple) -> Result<BandwidthUsage, MapError> {
        let key = Self::hash_connection_tuple(tuple);
        
        // Aggregate bandwidth across all CPUs
        match self.bandwidth_map.aggregate(
            &key,
            |bandwidths| {
                if bandwidths.is_empty() {
                    None
                } else {
                    // Get the latest timestamp
                    let latest_timestamp = bandwidths
                        .iter()
                        .map(|b| b.timestamp)
                        .max()
                        .unwrap_or(0);
                    
                    // Sum bandwidth for the latest timestamp
                    let latest_bandwidths: Vec<_> = bandwidths
                        .iter()
                        .filter(|b| b.timestamp == latest_timestamp)
                        .collect();
                    
                    if latest_bandwidths.is_empty() {
                        None
                    } else {
                        Some(BandwidthUsage {
                            bytes_per_second: latest_bandwidths
                                .iter()
                                .map(|b| b.bytes_per_second)
                                .sum(),
                            packets_per_second: latest_bandwidths
                                .iter()
                                .map(|b| b.packets_per_second)
                                .sum(),
                            timestamp: latest_timestamp,
                        })
                    }
                }
            },
        )? {
            Some(bandwidth) => Ok(bandwidth),
            None => Ok(BandwidthUsage {
                bytes_per_second: 0,
                packets_per_second: 0,
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs(),
            }),
        }
    }
    
    /// Clean up inactive connections
    pub fn cleanup_inactive_connections(&self, timeout_seconds: u64) -> Result<usize, MapError> {
        let mut cleaned_up = 0;
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Iterate over all CPUs
        for cpu in 0..self.connection_map.num_cpus() {
            // Skip offline CPUs
            if !self.connection_map.is_cpu_online(cpu) {
                continue;
            }
            
            // TODO: Implement efficient iteration over connections
            // For now, we'll just check a few known connection IDs
            for conn_id in 0..100 {
                if let Some(stats) = self.connection_map.lookup_cpu(&conn_id, cpu)? {
                    if now - stats.last_updated > timeout_seconds {
                        // Connection is inactive, delete it
                        self.connection_map.delete_cpu(&conn_id, cpu)?;
                        self.bandwidth_map.delete_cpu(&conn_id, cpu)?;
                        cleaned_up += 1;
                    }
                }
            }
        }
        
        Ok(cleaned_up)
    }
    
    /// Export connection statistics to Elasticsearch
    pub fn export_connections(&self) -> Result<usize, MapError> {
        let mut exported = 0;
        
        // Iterate over all CPUs
        for cpu in 0..self.connection_map.num_cpus() {
            // Skip offline CPUs
            if !self.connection_map.is_cpu_online(cpu) {
                continue;
            }
            
            // TODO: Implement efficient iteration over connections
            // For now, we'll just export a few known connection IDs
            for conn_id in 0..100 {
                if let Some(stats) = self.connection_map.lookup_cpu(&conn_id, cpu)? {
                    // Convert stats to JSON
                    if let Ok(json) = serde_json::to_value(&stats) {
                        // Log to Elasticsearch
                        if let Ok(()) = self.es_logger.log_event("xdp_connection_stats", json) {
                            exported += 1;
                        }
                    }
                }
            }
        }
        
        Ok(exported)
    }
    
    /// Get statistics for all maps
    pub fn get_stats(&self) -> (PerCpuMapStats, PerCpuMapStats) {
        (
            self.connection_map.stats().clone(),
            self.bandwidth_map.stats().clone(),
        )
    }
}

/// XDP hook handler for packet processing
pub fn handle_packet(
    hooker: &XdpHooker,
    src_ip: IpAddr,
    dst_ip: IpAddr,
    src_port: u16,
    dst_port: u16,
    protocol: u8,
    packet_size: u32,
    is_ingress: bool,
) -> Result<(), MapError> {
    // Get current CPU
    let cpu = 0; // In a real implementation, get the current CPU
    
    // Create connection tuple
    let tuple = ConnectionTuple {
        src_ip,
        dst_ip,
        src_port,
        dst_port,
        protocol,
    };
    
    // Update connection statistics
    hooker.update_connection_stats(&tuple, packet_size, is_ingress, cpu)?;
    
    Ok(())
}

/// Example test function
#[cfg(test)]
mod tests {
    use super::*;
    use inspector_gadget::logging::ElasticsearchConfig;
    
    #[test]
    fn test_xdp_hooker() -> Result<(), MapError> {
        // Create Elasticsearch logger
        let es_config = ElasticsearchConfig {
            url: "http://localhost:9200".to_string(),
            index: "xdp_test_results".to_string(),
            username: None,
            password: None,
            batch_size: 10,
            flush_interval: 5,
            connect_timeout: 10,
            request_timeout: 30,
        };
        
        let es_logger = Arc::new(
            ElasticsearchLogger::new(es_config).expect("Failed to create Elasticsearch logger"),
        );
        
        // Create XDP Hooker
        let hooker = XdpHooker::new(es_logger)?;
        
        // Test packet handling
        let src_ip = IpAddr::V4(Ipv4Addr::new(192, 168, 1, 100));
        let dst_ip = IpAddr::V4(Ipv4Addr::new(93, 184, 216, 34));
        let src_port = 12345;
        let dst_port = 443;
        let protocol = 6; // TCP
        
        // Handle outgoing packet
        handle_packet(
            &hooker,
            src_ip,
            dst_ip,
            src_port,
            dst_port,
            protocol,
            64,
            false,
        )?;
        
        // Handle incoming packet
        handle_packet(
            &hooker,
            dst_ip,
            src_ip,
            dst_port,
            src_port,
            protocol,
            1500,
            true,
        )?;
        
        // Verify connection statistics
        let tuple = ConnectionTuple {
            src_ip,
            dst_ip,
            src_port,
            dst_port,
            protocol,
        };
        
        let stats = hooker.get_connection_stats(&tuple, 0)?;
        assert!(stats.is_some());
        let stats = stats.unwrap();
        assert_eq!(stats.state, ConnectionState::Established);
        assert_eq!(stats.packets_sent, 1);
        assert_eq!(stats.packets_received, 1);
        assert_eq!(stats.bytes_sent, 64);
        assert_eq!(stats.bytes_received, 1500);
        
        // Get total bandwidth
        let bandwidth = hooker.get_total_bandwidth(&tuple)?;
        println!("Total bandwidth: {:?}", bandwidth);
        
        // Clean up inactive connections
        let cleaned_up = hooker.cleanup_inactive_connections(3600)?;
        println!("Cleaned up {} inactive connections", cleaned_up);
        
        // Export connections
        let exported = hooker.export_connections()?;
        println!("Exported {} connections", exported);
        
        // Get statistics
        let (connection_stats, bandwidth_stats) = hooker.get_stats();
        println!("Connection map stats: {:?}", connection_stats);
        println!("Bandwidth map stats: {:?}", bandwidth_stats);
        
        Ok(())
    }
} 