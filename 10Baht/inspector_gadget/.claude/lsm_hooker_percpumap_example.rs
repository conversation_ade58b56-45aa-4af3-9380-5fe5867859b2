use std::sync::Arc;
use serde::{Serialize, Deserialize};

use inspector_gadget::ebpf::maps::{TypedOptimizedPerCpuMap, PerCpuMapStats, MapError};
use inspector_gadget::logging::ElasticsearchLogger;

/// Security context for a process
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SecurityContext {
    /// Process ID
    pub pid: u32,
    /// User ID
    pub uid: u32,
    /// Group ID
    pub gid: u32,
    /// Capabilities
    pub capabilities: u64,
    /// Creation timestamp
    pub created_at: u64,
}

/// Security policy violation
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PolicyViolation {
    /// Violation counter
    pub count: u64,
    /// Last violation timestamp
    pub last_violation: u64,
    /// Severity level (0-100)
    pub severity: u8,
}

/// Security event
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct SecurityEvent {
    /// Event type
    pub event_type: SecurityEventType,
    /// Process ID
    pub pid: u32,
    /// Timestamp
    pub timestamp: u64,
    /// Event details
    pub details: String,
}

/// Security event type
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum SecurityEventType {
    /// Process creation
    ProcessCreation,
    /// Process termination
    ProcessTermination,
    /// File access
    FileAccess,
    /// Network access
    NetworkAccess,
    /// Capability check
    CapabilityCheck,
    /// Policy violation
    PolicyViolation,
}

/// LSM Hooker with Per-CPU Maps
pub struct LsmHooker {
    /// Process context map
    process_map: TypedOptimizedPerCpuMap<u32, SecurityContext>,
    /// Policy violation map
    violation_map: TypedOptimizedPerCpuMap<u32, PolicyViolation>,
    /// Security event map
    event_map: TypedOptimizedPerCpuMap<u64, SecurityEvent>,
    /// Elasticsearch logger
    es_logger: Arc<ElasticsearchLogger>,
}

impl LsmHooker {
    /// Create a new LSM Hooker
    pub fn new(es_logger: Arc<ElasticsearchLogger>) -> Result<Self, MapError> {
        // Create process context map
        let process_map = TypedOptimizedPerCpuMap::<u32, SecurityContext>::create(1024)?;
        
        // Create policy violation map
        let violation_map = TypedOptimizedPerCpuMap::<u32, PolicyViolation>::create(1024)?;
        
        // Create security event map
        let event_map = TypedOptimizedPerCpuMap::<u64, SecurityEvent>::create(4096)?;
        
        Ok(Self {
            process_map,
            violation_map,
            event_map,
            es_logger,
        })
    }
    
    /// Get process context
    pub fn get_process_context(&self, pid: u32, cpu: usize) -> Result<Option<SecurityContext>, MapError> {
        self.process_map.lookup_cpu(&pid, cpu)
    }
    
    /// Set process context
    pub fn set_process_context(&self, pid: u32, context: &SecurityContext, cpu: usize) -> Result<(), MapError> {
        self.process_map.update_cpu(&pid, context, cpu)
    }
    
    /// Get policy violation
    pub fn get_policy_violation(&self, policy_id: u32, cpu: usize) -> Result<Option<PolicyViolation>, MapError> {
        self.violation_map.lookup_cpu(&policy_id, cpu)
    }
    
    /// Increment policy violation counter
    pub fn increment_violation(&self, policy_id: u32, severity: u8, cpu: usize) -> Result<(), MapError> {
        // Get current violation or create new one
        let violation = match self.violation_map.lookup_cpu(&policy_id, cpu)? {
            Some(mut v) => {
                v.count += 1;
                v.last_violation = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs();
                v.severity = severity;
                v
            }
            None => PolicyViolation {
                count: 1,
                last_violation: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs(),
                severity,
            },
        };
        
        // Update violation map
        self.violation_map.update_cpu(&policy_id, &violation, cpu)
    }
    
    /// Add security event
    pub fn add_security_event(&self, event_id: u64, event: &SecurityEvent, cpu: usize) -> Result<(), MapError> {
        self.event_map.update_cpu(&event_id, event, cpu)
    }
    
    /// Get total policy violations
    pub fn get_total_violations(&self, policy_id: u32) -> Result<u64, MapError> {
        // Aggregate violations across all CPUs
        match self.violation_map.aggregate(
            &policy_id,
            |violations| {
                // Sum violation counts
                let total = violations.iter().map(|v| v.count).sum();
                if total > 0 {
                    Some(PolicyViolation {
                        count: total,
                        last_violation: violations
                            .iter()
                            .map(|v| v.last_violation)
                            .max()
                            .unwrap_or(0),
                        severity: violations
                            .iter()
                            .map(|v| v.severity)
                            .max()
                            .unwrap_or(0),
                    })
                } else {
                    None
                }
            },
        )? {
            Some(violation) => Ok(violation.count),
            None => Ok(0),
        }
    }
    
    /// Export security events to Elasticsearch
    pub fn export_events(&self) -> Result<usize, MapError> {
        let mut exported = 0;
        
        // Iterate over all CPUs
        for cpu in 0..self.event_map.num_cpus() {
            // Skip offline CPUs
            if !self.event_map.is_cpu_online(cpu) {
                continue;
            }
            
            // TODO: Implement efficient iteration over events
            // For now, we'll just export a few known event IDs
            for event_id in 0..100 {
                if let Some(event) = self.event_map.lookup_cpu(&event_id, cpu)? {
                    // Convert event to JSON
                    if let Ok(json) = serde_json::to_value(&event) {
                        // Log to Elasticsearch
                        if let Ok(()) = self.es_logger.log_event("lsm_security_event", json) {
                            exported += 1;
                        }
                    }
                }
            }
        }
        
        Ok(exported)
    }
    
    /// Get statistics for all maps
    pub fn get_stats(&self) -> (PerCpuMapStats, PerCpuMapStats, PerCpuMapStats) {
        (
            self.process_map.stats().clone(),
            self.violation_map.stats().clone(),
            self.event_map.stats().clone(),
        )
    }
}

/// LSM hook handler for process creation
pub fn handle_process_creation(
    hooker: &LsmHooker,
    pid: u32,
    uid: u32,
    gid: u32,
    capabilities: u64,
) -> Result<(), MapError> {
    // Get current CPU
    let cpu = 0; // In a real implementation, get the current CPU
    
    // Create security context
    let context = SecurityContext {
        pid,
        uid,
        gid,
        capabilities,
        created_at: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs(),
    };
    
    // Update process map
    hooker.set_process_context(pid, &context, cpu)?;
    
    // Create security event
    let event = SecurityEvent {
        event_type: SecurityEventType::ProcessCreation,
        pid,
        timestamp: context.created_at,
        details: format!("Process created: PID={}, UID={}, GID={}", pid, uid, gid),
    };
    
    // Add event to map
    hooker.add_security_event(
        std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos() as u64,
        &event,
        cpu,
    )?;
    
    Ok(())
}

/// LSM hook handler for file access
pub fn handle_file_access(
    hooker: &LsmHooker,
    pid: u32,
    path: &str,
    access_mask: u32,
) -> Result<bool, MapError> {
    // Get current CPU
    let cpu = 0; // In a real implementation, get the current CPU
    
    // Get process context
    let context = match hooker.get_process_context(pid, cpu)? {
        Some(ctx) => ctx,
        None => {
            // Process not found, deny access
            return Ok(false);
        }
    };
    
    // Check if access is allowed (simplified example)
    let is_allowed = if path.contains("/etc/shadow") && context.uid != 0 {
        // Only root can access shadow file
        
        // Record policy violation
        hooker.increment_violation(1, 80, cpu)?;
        
        // Create security event
        let event = SecurityEvent {
            event_type: SecurityEventType::PolicyViolation,
            pid,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            details: format!(
                "Unauthorized access to {}: PID={}, UID={}",
                path, pid, context.uid
            ),
        };
        
        // Add event to map
        hooker.add_security_event(
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            &event,
            cpu,
        )?;
        
        false
    } else {
        // Access allowed
        
        // Create security event
        let event = SecurityEvent {
            event_type: SecurityEventType::FileAccess,
            pid,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            details: format!("File access: PID={}, path={}, mask={:x}", pid, path, access_mask),
        };
        
        // Add event to map
        hooker.add_security_event(
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            &event,
            cpu,
        )?;
        
        true
    };
    
    Ok(is_allowed)
}

/// Example test function
#[cfg(test)]
mod tests {
    use super::*;
    use inspector_gadget::logging::ElasticsearchConfig;
    
    #[test]
    fn test_lsm_hooker() -> Result<(), MapError> {
        // Create Elasticsearch logger
        let es_config = ElasticsearchConfig {
            url: "http://localhost:9200".to_string(),
            index: "lsm_test_results".to_string(),
            username: None,
            password: None,
            batch_size: 10,
            flush_interval: 5,
            connect_timeout: 10,
            request_timeout: 30,
        };
        
        let es_logger = Arc::new(
            ElasticsearchLogger::new(es_config).expect("Failed to create Elasticsearch logger"),
        );
        
        // Create LSM Hooker
        let hooker = LsmHooker::new(es_logger)?;
        
        // Test process creation
        handle_process_creation(&hooker, 1000, 1000, 1000, 0)?;
        
        // Verify process context
        let context = hooker.get_process_context(1000, 0)?;
        assert!(context.is_some());
        let context = context.unwrap();
        assert_eq!(context.pid, 1000);
        assert_eq!(context.uid, 1000);
        assert_eq!(context.gid, 1000);
        
        // Test file access (allowed)
        let allowed = handle_file_access(&hooker, 1000, "/tmp/test.txt", 0x4)?;
        assert!(allowed);
        
        // Test file access (denied)
        let allowed = handle_file_access(&hooker, 1000, "/etc/shadow", 0x4)?;
        assert!(!allowed);
        
        // Verify policy violation
        let total_violations = hooker.get_total_violations(1)?;
        assert_eq!(total_violations, 1);
        
        // Export events
        let exported = hooker.export_events()?;
        println!("Exported {} events", exported);
        
        // Get statistics
        let (process_stats, violation_stats, event_stats) = hooker.get_stats();
        println!("Process map stats: {:?}", process_stats);
        println!("Violation map stats: {:?}", violation_stats);
        println!("Event map stats: {:?}", event_stats);
        
        Ok(())
    }
} 