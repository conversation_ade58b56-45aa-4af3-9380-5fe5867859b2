# Per-CPU Maps Implementation Summary

## Overview

The Per-CPU Maps implementation is a critical component of the Inspector Gadget framework, providing efficient concurrent access to data structures without locks. This document summarizes the implementation, its features, and its integration with other components.

## Key Features

- **Concurrent Access**: Maintains separate values for each CPU, eliminating the need for locks
- **Type Safety**: Provides type-safe wrappers for compile-time checking
- **Aggregation**: Supports aggregation operations across CPUs (sum, max, min, avg, custom)
- **Statistics**: Comprehensive tracking of operations (lookups, hits, misses, updates, deletes, errors)
- **CPU Tracking**: Monitors which CPUs are online/offline and handles transitions gracefully
- **Error Handling**: Detailed error messages and recovery mechanisms
- **Elasticsearch Integration**: Monitoring and analysis of map operations

## Architecture

The implementation consists of three main components:

1. **PerCpuMapStats**: Tracks statistics for map operations using atomic counters
2. **OptimizedPerCpuMap**: Low-level implementation of per-CPU maps
3. **TypedOptimizedPerCpuMap<K, V>**: Type-safe wrapper for per-CPU maps

```mermaid
classDiagram
    class PerCpuMapStats {
        +AtomicU64 lookups
        +AtomicU64 hits
        +AtomicU64 misses
        +AtomicU64 updates
        +AtomicU64 deletes
        +AtomicU64 errors
        +AtomicU64 atomic_ops
        +new() PerCpuMapStats
        +reset() void
        +hit_rate() f64
    }
    
    class OptimizedPerCpuMap {
        -Map inner
        -Arc~PerCpuMapStats~ stats
        -usize num_cpus
        -Vec~bool~ cpu_online
        +create(key_size, value_size, max_entries) Result~Self, MapError~
        +open(name) Result~Self, MapError~
        +name() &str
        +num_cpus() usize
        +stats() &PerCpuMapStats
        +is_cpu_online(cpu) bool
        +set_cpu_online(cpu, online) void
        +lookup_cpu(key, cpu) Result~Option~V~, MapError~
        +lookup_all_cpus(key) Result~Vec~V~, MapError~
        +update_cpu(key, value, cpu) Result~(), MapError~
        +delete(key) Result~bool, MapError~
        +atomic_add(key, value) Result~(), MapError~
        +aggregate(key, func) Result~Option~V~, MapError~
        +sum(key) Result~Option~V~, MapError~
        +max(key) Result~Option~V~, MapError~
        +min(key) Result~Option~V~, MapError~
        +avg(key) Result~Option~V~, MapError~
    }
    
    class TypedOptimizedPerCpuMap~K, V~ {
        -OptimizedPerCpuMap inner
        -PhantomData~K~ _key_type
        -PhantomData~V~ _value_type
        +create(max_entries) Result~Self, MapError~
        +open(name) Result~Self, MapError~
        +name() &str
        +num_cpus() usize
        +stats() &PerCpuMapStats
        +is_cpu_online(cpu) bool
        +set_cpu_online(cpu, online) void
        +lookup_cpu(key, cpu) Result~Option~V~, MapError~
        +lookup_all_cpus(key) Result~Vec~V~, MapError~
        +update_cpu(key, value, cpu) Result~(), MapError~
        +delete(key) Result~bool, MapError~
        +atomic_add(key, value) Result~(), MapError~
        +aggregate(key, func) Result~Option~V~, MapError~
        +sum(key) Result~Option~V~, MapError~
        +max(key) Result~Option~V~, MapError~
        +min(key) Result~Option~V~, MapError~
        +avg(key) Result~Option~V~, MapError~
    }
    
    OptimizedPerCpuMap *-- PerCpuMapStats
    TypedOptimizedPerCpuMap *-- OptimizedPerCpuMap
```

## Operations

The implementation supports the following operations:

- **lookup_cpu**: Retrieve a value for a specific CPU
- **lookup_all_cpus**: Retrieve values for all CPUs
- **update_cpu**: Update a value for a specific CPU
- **delete**: Remove a key and its values from all CPUs
- **atomic_add**: Atomically add a value to the current value
- **aggregate**: Apply a custom aggregation function to values from all CPUs
- **sum**: Sum values across all CPUs
- **max**: Find the maximum value across all CPUs
- **min**: Find the minimum value across all CPUs
- **avg**: Calculate the average value across all CPUs

## Performance Characteristics

The Per-CPU Maps implementation is designed for high-performance concurrent access:

- **Lock-Free**: No locks are required for concurrent access from different CPUs
- **Cache Efficiency**: Values are properly aligned to avoid false sharing
- **Atomic Operations**: Statistics are tracked using atomic operations
- **Minimal Overhead**: Operations have minimal overhead compared to raw map operations

## Integration with Other Components

The Per-CPU Maps implementation integrates with the following components:

- **eBPF Hookers**: Used by all hooker types for concurrent data collection
- **Elasticsearch**: Test results and performance metrics are stored in Elasticsearch
- **Event Collection System**: Provides efficient data structures for event collection

## Testing

The implementation includes comprehensive tests:

- **Unit Tests**: Test individual components and operations
- **Integration Tests**: Test integration with other components
- **Performance Tests**: Measure performance under various conditions
- **Concurrency Tests**: Test concurrent access from multiple threads

## Conclusion

The Per-CPU Maps implementation provides a robust and efficient way to handle per-CPU data structures in eBPF programs. It offers comprehensive error handling, statistics tracking, and type safety, making it a valuable component of the Inspector Gadget framework. 