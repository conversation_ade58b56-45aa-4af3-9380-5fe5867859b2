# Hooker Tests Plan

This document outlines the test plan for validating the integration of Per-CPU Maps with the different hooker types (LSM, XDP, Tracepoint, and Uprobe) in the Inspector Gadget framework.

## Test Objectives

1. Verify correct functionality of Per-CPU Maps with each hooker type
2. Validate performance improvements compared to non-Per-CPU implementations
3. Ensure proper error handling and recovery mechanisms
4. Confirm Elasticsearch integration for test results
5. Validate cross-hooker correlation capabilities

## Test Environment

### Hardware Requirements
- Multi-core CPU (minimum 4 cores)
- Minimum 8GB RAM
- Fast SSD for storage
- Network interface for XDP tests

### Software Requirements
- Linux kernel 5.10+ with eBPF support
- Elasticsearch 7.10+ for test result storage
- Docker for containerized testing
- Vagrant for VM-based testing

## Test Categories

### 1. Unit Tests

#### Per-CPU Maps Core Tests
- Test creation and initialization of Per-CPU Maps
- Verify basic operations (lookup, update, delete)
- Test aggregation functions (sum, max, min, avg, custom)
- Validate statistics tracking
- Test error handling and recovery

#### Hooker-Specific Unit Tests
- Test hooker initialization with Per-CPU Maps
- Verify hooker-specific map operations
- Test hooker cleanup and resource management
- Validate hooker-specific error handling

### 2. Integration Tests

#### LSM Hooker Integration Tests
- Test security context tracking with Per-CPU Maps
- Verify policy violation detection and counting
- Test security event logging and analysis
- Validate security policy lookup performance

#### XDP Hooker Integration Tests
- Test connection tracking with Per-CPU Maps
- Verify bandwidth usage monitoring
- Test packet statistics collection
- Validate connection state transitions

#### Tracepoint Hooker Integration Tests
- Test syscall tracking with Per-CPU Maps
- Verify syscall argument capture and analysis
- Test syscall frequency and latency monitoring
- Validate syscall correlation capabilities

#### Uprobe Hooker Integration Tests
- Test function call tracking with Per-CPU Maps
- Verify parameter monitoring and analysis
- Test call graph construction and traversal
- Validate return value statistics

#### Cross-Hooker Integration Tests
- Test shared maps across hooker types
- Verify cross-hooker event correlation
- Test synchronized access mechanisms
- Validate end-to-end workflows

### 3. Performance Tests

#### Throughput Tests
- Measure operations per second for each hooker type
- Compare Per-CPU vs. non-Per-CPU implementations
- Test under varying load conditions
- Measure scaling with number of CPUs

#### Latency Tests
- Measure operation latency for each hooker type
- Calculate percentile distributions (p50, p95, p99)
- Test under varying load conditions
- Measure impact of CPU cache effects

#### Resource Usage Tests
- Measure memory usage for different map sizes
- Monitor CPU utilization under load
- Track kernel memory allocation
- Measure impact on system performance

#### Concurrency Tests
- Test concurrent access from multiple CPUs
- Measure contention and false sharing
- Verify correctness under high concurrency
- Test with varying thread counts

### 4. Elasticsearch Integration Tests

#### Export Tests
- Test bulk export of test results to Elasticsearch
- Verify correct indexing and mapping
- Test retry and backoff mechanisms
- Validate custom serializers for complex types

#### Query Tests
- Test retrieval of test results from Elasticsearch
- Verify aggregation and analysis capabilities
- Test visualization of test results
- Validate cross-hooker correlation queries

## Test Scenarios

### LSM Hooker Test Scenarios

#### Scenario 1: Security Context Tracking
1. Create processes with different security contexts
2. Verify correct tracking in Per-CPU Maps
3. Measure lookup performance for security contexts
4. Test concurrent access from multiple processes

#### Scenario 2: Policy Violation Detection
1. Configure security policies with different restrictions
2. Trigger policy violations from test processes
3. Verify correct counting in Per-CPU Maps
4. Test aggregation of violation statistics

#### Scenario 3: Security Event Logging
1. Generate security events from different processes
2. Verify correct logging in Per-CPU Maps
3. Test export to Elasticsearch
4. Validate event correlation and analysis

### XDP Hooker Test Scenarios

#### Scenario 1: Connection Tracking
1. Generate network traffic with different connection patterns
2. Verify correct tracking in Per-CPU Maps
3. Measure lookup performance for connections
4. Test concurrent tracking of multiple connections

#### Scenario 2: Bandwidth Monitoring
1. Generate network traffic with varying bandwidth usage
2. Verify correct monitoring in Per-CPU Maps
3. Test aggregation of bandwidth statistics
4. Validate accuracy of bandwidth measurements

#### Scenario 3: Packet Statistics
1. Generate different types of network packets
2. Verify correct statistics in Per-CPU Maps
3. Test export to Elasticsearch
4. Validate packet analysis and correlation

### Tracepoint Hooker Test Scenarios

#### Scenario 1: Syscall Tracking
1. Generate syscalls from different processes
2. Verify correct tracking in Per-CPU Maps
3. Measure lookup performance for syscalls
4. Test concurrent tracking of multiple syscalls

#### Scenario 2: Syscall Latency Monitoring
1. Generate syscalls with varying latencies
2. Verify correct monitoring in Per-CPU Maps
3. Test aggregation of latency statistics
4. Validate accuracy of latency measurements

#### Scenario 3: Syscall Correlation
1. Generate sequences of related syscalls
2. Verify correct correlation in Per-CPU Maps
3. Test export to Elasticsearch
4. Validate syscall sequence analysis

### Uprobe Hooker Test Scenarios

#### Scenario 1: Function Call Tracking
1. Generate function calls from test applications
2. Verify correct tracking in Per-CPU Maps
3. Measure lookup performance for function calls
4. Test concurrent tracking of multiple functions

#### Scenario 2: Call Graph Analysis
1. Generate function calls with known call graphs
2. Verify correct graph construction in Per-CPU Maps
3. Test traversal and analysis of call graphs
4. Validate accuracy of call relationships

#### Scenario 3: Return Value Analysis
1. Generate function calls with different return values
2. Verify correct statistics in Per-CPU Maps
3. Test export to Elasticsearch
4. Validate return value analysis

## Test Implementation

### Test Framework
- Use Rust's built-in testing framework for unit tests
- Implement custom test harness for integration tests
- Use Pimp tool for automated test execution
- Implement performance benchmarks using criterion.rs

### Test Data Generation
- Create test programs for generating specific events
- Implement workload generators for performance testing
- Use recorded traces for reproducible testing
- Generate synthetic data for edge cases

### Test Execution
- Automate test execution with CI/CD pipeline
- Run tests in isolated environments
- Capture detailed logs and metrics
- Generate comprehensive test reports

### Test Result Analysis
- Store test results in Elasticsearch
- Create dashboards for visualizing results
- Implement trend analysis for performance metrics
- Generate alerts for test failures

## Test Schedule

### Week 1: Unit Tests
- Implement Per-CPU Maps core tests
- Create hooker-specific unit tests
- Set up test automation framework

### Week 2: Integration Tests
- Implement LSM and XDP hooker integration tests
- Create Tracepoint and Uprobe hooker integration tests
- Set up Elasticsearch for test results

### Week 3: Performance Tests
- Implement throughput and latency tests
- Create resource usage and concurrency tests
- Set up performance benchmarking framework

### Week 4: End-to-End Testing
- Implement cross-hooker integration tests
- Create comprehensive test scenarios
- Finalize test result analysis and reporting

## Success Criteria

1. All unit tests pass with 100% coverage
2. Integration tests demonstrate correct functionality
3. Performance tests show improvement over non-Per-CPU implementations:
   - Minimum 30% throughput improvement
   - Maximum 50% latency reduction
   - No increase in resource usage
4. Elasticsearch integration successfully stores and retrieves test results
5. Cross-hooker correlation capabilities are validated

## Reporting

### Test Reports
- Generate detailed test reports for each test run
- Include pass/fail status for each test
- Provide performance metrics and comparisons
- Include logs and diagnostics for failures

### Performance Dashboards
- Create dashboards for visualizing performance metrics
- Include trend analysis for tracking improvements
- Provide drill-down capabilities for detailed analysis
- Set up alerts for performance regressions

### Documentation
- Update documentation with test results
- Include performance tuning recommendations
- Document known issues and limitations
- Provide troubleshooting guidelines

## Conclusion

This test plan provides a comprehensive approach to validating the integration of Per-CPU Maps with the different hooker types in the Inspector Gadget framework. By following this plan, we can ensure that the implementation meets all functional and performance requirements while providing a solid foundation for future enhancements. 