# Per-CPU Maps Integration with <PERSON><PERSON>

This document outlines the implementation plan for integrating the Per-CPU Maps with the different hooker types (LSM, XDP, Tracepoint, and Uprobe) in the Inspector Gadget framework.

## Overview

The Per-CPU Maps implementation provides efficient concurrent access to data structures without locks, making it ideal for high-throughput scenarios in eBPF hookers. This integration will enhance the performance and scalability of all hooker types.

## Integration Strategy

### Phase 1: Core Integration

1. **Create Hooker-Specific Map Wrappers**
   - Implement specialized map wrappers for each hooker type
   - Add hooker-specific operations and aggregations
   - Ensure type safety for hooker-specific data structures

2. **Update Hooker Initialization**
   - Modify hooker initialization to create Per-CPU Maps
   - Add configuration options for map size and other parameters
   - Implement proper cleanup on hooker termination

3. **Implement Data Collection**
   - Update data collection logic to use Per-CPU Maps
   - Ensure proper CPU identification in hooker context
   - Add statistics tracking for hooker operations

### Phase 2: Hooker-Specific Implementations

#### LSM Hooker Integration

```mermaid
flowchart TB
    subgraph "LSM Hooker"
        lsm_hook["LSM Hooks"]
        lsm_events["Security Events"]
        
        subgraph "Per-CPU Maps"
            process_context["Process Context Map"]
            policy_violations["Policy Violations Map"]
            security_events["Security Events Map"]
        end
        
        lsm_hook --> lsm_events
        lsm_events --> process_context
        lsm_events --> policy_violations
        lsm_events --> security_events
    end
    
    subgraph "User Space"
        security_api["Security Monitoring API"]
        elasticsearch["Elasticsearch Export"]
    end
    
    process_context -.-> security_api
    policy_violations -.-> security_api
    security_events -.-> security_api
    security_api --> elasticsearch
```

**Implementation Steps:**
1. Create `ProcessContextMap` using Per-CPU Maps
   - Key: Process ID
   - Value: Security context (UID, GID, capabilities)
   - Operations: Lookup, update on process creation/termination

2. Implement `PolicyViolationsMap` using Per-CPU Maps
   - Key: Policy ID
   - Value: Violation counter and details
   - Operations: Atomic increment, aggregation for reporting

3. Create `SecurityEventsMap` using Per-CPU Maps
   - Key: Event ID
   - Value: Event details (timestamp, process, operation)
   - Operations: Update on security events, batch export

4. Update LSM hook handlers to use these maps
   - Modify process creation/termination hooks
   - Update security check hooks
   - Enhance event generation logic

#### XDP Hooker Integration

```mermaid
flowchart TB
    subgraph "XDP Hooker"
        xdp_hook["XDP Hooks"]
        xdp_events["Network Events"]
        
        subgraph "Per-CPU Maps"
            connection_map["Connection Tracking Map"]
            bandwidth_map["Bandwidth Usage Map"]
            packet_stats["Packet Statistics Map"]
        end
        
        xdp_hook --> xdp_events
        xdp_events --> connection_map
        xdp_events --> bandwidth_map
        xdp_events --> packet_stats
    end
    
    subgraph "User Space"
        network_api["Network Monitoring API"]
        elasticsearch["Elasticsearch Export"]
    end
    
    connection_map -.-> network_api
    bandwidth_map -.-> network_api
    packet_stats -.-> network_api
    network_api --> elasticsearch
```

**Implementation Steps:**
1. Create `ConnectionTrackingMap` using Per-CPU Maps
   - Key: 5-tuple (src/dst IP, src/dst port, protocol)
   - Value: Connection state and statistics
   - Operations: Lookup, update on packet processing

2. Implement `BandwidthUsageMap` using Per-CPU Maps
   - Key: Connection ID or interface
   - Value: Bandwidth usage counters
   - Operations: Atomic increment, aggregation for reporting

3. Create `PacketStatisticsMap` using Per-CPU Maps
   - Key: Protocol or packet type
   - Value: Packet counters and statistics
   - Operations: Atomic increment, aggregation for reporting

4. Update XDP hook handlers to use these maps
   - Modify packet processing logic
   - Update connection tracking
   - Enhance bandwidth monitoring

#### Tracepoint Hooker Integration

```mermaid
flowchart TB
    subgraph "Tracepoint Hooker"
        tp_hook["Tracepoint Hooks"]
        tp_events["Syscall Events"]
        
        subgraph "Per-CPU Maps"
            syscall_stats["Syscall Statistics Map"]
            process_syscalls["Process Syscall Map"]
            syscall_latency["Syscall Latency Map"]
        end
        
        tp_hook --> tp_events
        tp_events --> syscall_stats
        tp_events --> process_syscalls
        tp_events --> syscall_latency
    end
    
    subgraph "User Space"
        syscall_api["Syscall Monitoring API"]
        elasticsearch["Elasticsearch Export"]
    end
    
    syscall_stats -.-> syscall_api
    process_syscalls -.-> syscall_api
    syscall_latency -.-> syscall_api
    syscall_api --> elasticsearch
```

**Implementation Steps:**
1. Create `SyscallStatisticsMap` using Per-CPU Maps
   - Key: Syscall ID
   - Value: Call frequency and error statistics
   - Operations: Atomic increment, aggregation for reporting

2. Implement `ProcessSyscallMap` using Per-CPU Maps
   - Key: Process ID + Syscall ID
   - Value: Call frequency and details
   - Operations: Atomic increment, lookup for analysis

3. Create `SyscallLatencyMap` using Per-CPU Maps
   - Key: Syscall ID
   - Value: Latency statistics (min, max, avg)
   - Operations: Update on syscall completion, aggregation

4. Update Tracepoint hook handlers to use these maps
   - Modify syscall entry/exit hooks
   - Update process tracking
   - Enhance latency measurement

#### Uprobe Hooker Integration

```mermaid
flowchart TB
    subgraph "Uprobe Hooker"
        up_hook["Uprobe Hooks"]
        up_events["Function Events"]
        
        subgraph "Per-CPU Maps"
            function_stats["Function Statistics Map"]
            call_graph["Call Graph Map"]
            return_values["Return Values Map"]
        end
        
        up_hook --> up_events
        up_events --> function_stats
        up_events --> call_graph
        up_events --> return_values
    end
    
    subgraph "User Space"
        function_api["Function Monitoring API"]
        elasticsearch["Elasticsearch Export"]
    end
    
    function_stats -.-> function_api
    call_graph -.-> function_api
    return_values -.-> function_api
    function_api --> elasticsearch
```

**Implementation Steps:**
1. Create `FunctionStatisticsMap` using Per-CPU Maps
   - Key: Function ID
   - Value: Call frequency and stack depth
   - Operations: Atomic increment, aggregation for reporting

2. Implement `CallGraphMap` using Per-CPU Maps
   - Key: Caller + Callee function IDs
   - Value: Call frequency and context
   - Operations: Atomic increment, lookup for analysis

3. Create `ReturnValuesMap` using Per-CPU Maps
   - Key: Function ID
   - Value: Return value statistics
   - Operations: Update on function return, aggregation

4. Update Uprobe hook handlers to use these maps
   - Modify function entry/exit hooks
   - Update call graph tracking
   - Enhance return value analysis

### Phase 3: Cross-Hooker Integration

```mermaid
flowchart TB
    subgraph "Hookers"
        lsm["LSM Hooker"]
        xdp["XDP Hooker"]
        tp["Tracepoint Hooker"]
        up["Uprobe Hooker"]
    end
    
    subgraph "Shared Per-CPU Maps"
        process_map["Process Map"]
        file_map["File Map"]
        network_map["Network Map"]
        correlation_map["Correlation Map"]
    end
    
    lsm --> process_map
    lsm --> file_map
    
    xdp --> network_map
    xdp --> correlation_map
    
    tp --> process_map
    tp --> file_map
    tp --> network_map
    tp --> correlation_map
    
    up --> process_map
    up --> correlation_map
    
    subgraph "User Space"
        correlation_api["Correlation API"]
        elasticsearch["Elasticsearch Export"]
    end
    
    process_map -.-> correlation_api
    file_map -.-> correlation_api
    network_map -.-> correlation_api
    correlation_map -.-> correlation_api
    correlation_api --> elasticsearch
```

**Implementation Steps:**
1. Create shared maps accessible by all hooker types
   - Process map for tracking process information
   - File map for tracking file operations
   - Network map for tracking network connections
   - Correlation map for cross-hooker event correlation

2. Implement synchronized access mechanisms
   - Read-write locks for concurrent access
   - Atomic operations for counter updates
   - Versioned entries for concurrent modifications

3. Create correlation API for analyzing cross-hooker events
   - Process-to-network correlation
   - File-to-process correlation
   - Syscall-to-function correlation

## Testing Strategy

### Unit Tests
- Test each map wrapper independently
- Verify correct behavior of hooker-specific operations
- Ensure proper error handling and recovery

### Integration Tests
- Test hooker initialization with Per-CPU Maps
- Verify data collection and aggregation
- Test cross-hooker correlation

### Performance Tests
- Measure throughput with and without Per-CPU Maps
- Compare lock-based vs. lock-free implementations
- Benchmark different map sizes and configurations

### Concurrency Tests
- Test concurrent access from multiple CPUs
- Verify correct behavior under high load
- Measure contention and false sharing

## Elasticsearch Integration

### Index Templates
- Create index templates for each hooker type
- Define mappings for hooker-specific data structures
- Set up appropriate sharding and replication

### Export Pipeline
- Implement bulk operations for efficient export
- Add retry and backoff mechanisms for resilience
- Create custom serializers for complex data types

### Visualization
- Create dashboards for each hooker type
- Implement cross-hooker correlation views
- Add performance monitoring visualizations

## Timeline

### Week 1-2: Core Integration
- Create hooker-specific map wrappers
- Update hooker initialization
- Implement basic data collection

### Week 3-4: Hooker-Specific Implementations
- Implement LSM hooker integration
- Implement XDP hooker integration
- Implement Tracepoint hooker integration
- Implement Uprobe hooker integration

### Week 5-6: Cross-Hooker Integration
- Create shared maps
- Implement synchronized access
- Develop correlation API

### Week 7-8: Testing and Optimization
- Implement comprehensive tests
- Optimize performance
- Finalize Elasticsearch integration

## Conclusion

The integration of Per-CPU Maps with the different hooker types will significantly enhance the performance and scalability of the Inspector Gadget framework. By eliminating locks and providing efficient concurrent access, Per-CPU Maps enable high-throughput data collection and analysis across all hooker types. 