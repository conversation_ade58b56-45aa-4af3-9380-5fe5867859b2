use std::sync::Arc;
use serde::{Serialize, Deserialize};

use inspector_gadget::ebpf::maps::{TypedOptimizedPerCpuMap, PerCpuMapStats, MapError};
use inspector_gadget::logging::ElasticsearchLogger;

/// Syscall information
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SyscallInfo {
    /// Syscall number
    pub syscall_number: u32,
    /// Syscall name
    pub syscall_name: String,
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// User ID
    pub uid: u32,
    /// Command name
    pub comm: String,
}

/// Syscall statistics
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SyscallStats {
    /// Call count
    pub count: u64,
    /// Total execution time (nanoseconds)
    pub total_time_ns: u64,
    /// Minimum execution time (nanoseconds)
    pub min_time_ns: u64,
    /// Maximum execution time (nanoseconds)
    pub max_time_ns: u64,
    /// Error count
    pub error_count: u64,
    /// Last call timestamp
    pub last_call: u64,
}

/// Syscall sequence
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SyscallSequence {
    /// Sequence of syscall numbers
    pub syscalls: Vec<u32>,
    /// Process ID
    pub pid: u32,
    /// Timestamp
    pub timestamp: u64,
}

/// Tracepoint Hooker with Per-CPU Maps
pub struct TracepointHooker {
    /// Syscall info map
    syscall_info_map: TypedOptimizedPerCpuMap<u32, SyscallInfo>,
    /// Syscall statistics map
    syscall_stats_map: TypedOptimizedPerCpuMap<u64, SyscallStats>,
    /// Syscall sequence map
    syscall_sequence_map: TypedOptimizedPerCpuMap<u32, SyscallSequence>,
    /// Elasticsearch logger
    es_logger: Arc<ElasticsearchLogger>,
}

impl TracepointHooker {
    /// Create a new Tracepoint Hooker
    pub fn new(es_logger: Arc<ElasticsearchLogger>) -> Result<Self, MapError> {
        // Create syscall info map
        let syscall_info_map = TypedOptimizedPerCpuMap::<u32, SyscallInfo>::create(512)?;
        
        // Create syscall statistics map
        let syscall_stats_map = TypedOptimizedPerCpuMap::<u64, SyscallStats>::create(1024)?;
        
        // Create syscall sequence map
        let syscall_sequence_map = TypedOptimizedPerCpuMap::<u32, SyscallSequence>::create(256)?;
        
        Ok(Self {
            syscall_info_map,
            syscall_stats_map,
            syscall_sequence_map,
            es_logger,
        })
    }
    
    /// Generate key for syscall statistics
    fn generate_stats_key(syscall_number: u32, pid: u32) -> u64 {
        ((pid as u64) << 32) | (syscall_number as u64)
    }
    
    /// Record syscall entry
    pub fn record_syscall_entry(
        &self,
        syscall_number: u32,
        syscall_name: &str,
        pid: u32,
        tid: u32,
        uid: u32,
        comm: &str,
        cpu: usize,
    ) -> Result<(), MapError> {
        // Create syscall info
        let info = SyscallInfo {
            syscall_number,
            syscall_name: syscall_name.to_string(),
            pid,
            tid,
            uid,
            comm: comm.to_string(),
        };
        
        // Update syscall info map
        self.syscall_info_map.update_cpu(&syscall_number, &info, cpu)?;
        
        // Update syscall sequence map
        self.update_syscall_sequence(pid, syscall_number, cpu)?;
        
        Ok(())
    }
    
    /// Update syscall sequence
    fn update_syscall_sequence(&self, pid: u32, syscall_number: u32, cpu: usize) -> Result<(), MapError> {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Get current sequence or create new one
        let sequence = match self.syscall_sequence_map.lookup_cpu(&pid, cpu)? {
            Some(mut seq) => {
                // Update existing sequence
                if seq.syscalls.len() >= 10 {
                    // Keep only the last 9 syscalls
                    seq.syscalls.drain(0..1);
                }
                
                // Add new syscall
                seq.syscalls.push(syscall_number);
                seq.timestamp = now;
                seq
            }
            None => {
                // Create new sequence
                SyscallSequence {
                    syscalls: vec![syscall_number],
                    pid,
                    timestamp: now,
                }
            }
        };
        
        // Update sequence map
        self.syscall_sequence_map.update_cpu(&pid, &sequence, cpu)
    }
    
    /// Record syscall exit
    pub fn record_syscall_exit(
        &self,
        syscall_number: u32,
        pid: u32,
        duration_ns: u64,
        error: bool,
        cpu: usize,
    ) -> Result<(), MapError> {
        let key = Self::generate_stats_key(syscall_number, pid);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Get current stats or create new ones
        let stats = match self.syscall_stats_map.lookup_cpu(&key, cpu)? {
            Some(mut s) => {
                // Update existing stats
                s.count += 1;
                s.total_time_ns += duration_ns;
                s.min_time_ns = s.min_time_ns.min(duration_ns);
                s.max_time_ns = s.max_time_ns.max(duration_ns);
                if error {
                    s.error_count += 1;
                }
                s.last_call = now;
                s
            }
            None => {
                // Create new stats
                SyscallStats {
                    count: 1,
                    total_time_ns: duration_ns,
                    min_time_ns: duration_ns,
                    max_time_ns: duration_ns,
                    error_count: if error { 1 } else { 0 },
                    last_call: now,
                }
            }
        };
        
        // Update stats map
        self.syscall_stats_map.update_cpu(&key, &stats, cpu)
    }
    
    /// Get syscall info
    pub fn get_syscall_info(&self, syscall_number: u32, cpu: usize) -> Result<Option<SyscallInfo>, MapError> {
        self.syscall_info_map.lookup_cpu(&syscall_number, cpu)
    }
    
    /// Get syscall statistics
    pub fn get_syscall_stats(&self, syscall_number: u32, pid: u32, cpu: usize) -> Result<Option<SyscallStats>, MapError> {
        let key = Self::generate_stats_key(syscall_number, pid);
        self.syscall_stats_map.lookup_cpu(&key, cpu)
    }
    
    /// Get syscall sequence
    pub fn get_syscall_sequence(&self, pid: u32, cpu: usize) -> Result<Option<SyscallSequence>, MapError> {
        self.syscall_sequence_map.lookup_cpu(&pid, cpu)
    }
    
    /// Get total syscall statistics
    pub fn get_total_syscall_stats(&self, syscall_number: u32, pid: u32) -> Result<Option<SyscallStats>, MapError> {
        let key = Self::generate_stats_key(syscall_number, pid);
        
        // Aggregate statistics across all CPUs
        self.syscall_stats_map.aggregate(
            &key,
            |stats_list| {
                if stats_list.is_empty() {
                    None
                } else {
                    let mut total_stats = SyscallStats {
                        count: 0,
                        total_time_ns: 0,
                        min_time_ns: u64::MAX,
                        max_time_ns: 0,
                        error_count: 0,
                        last_call: 0,
                    };
                    
                    for stats in stats_list {
                        total_stats.count += stats.count;
                        total_stats.total_time_ns += stats.total_time_ns;
                        total_stats.min_time_ns = total_stats.min_time_ns.min(stats.min_time_ns);
                        total_stats.max_time_ns = total_stats.max_time_ns.max(stats.max_time_ns);
                        total_stats.error_count += stats.error_count;
                        total_stats.last_call = total_stats.last_call.max(stats.last_call);
                    }
                    
                    if total_stats.min_time_ns == u64::MAX {
                        total_stats.min_time_ns = 0;
                    }
                    
                    Some(total_stats)
                }
            },
        )
    }
    
    /// Export syscall statistics to Elasticsearch
    pub fn export_syscall_stats(&self) -> Result<usize, MapError> {
        let mut exported = 0;
        
        // Iterate over all CPUs
        for cpu in 0..self.syscall_stats_map.num_cpus() {
            // Skip offline CPUs
            if !self.syscall_stats_map.is_cpu_online(cpu) {
                continue;
            }
            
            // TODO: Implement efficient iteration over syscall stats
            // For now, we'll just export a few known syscall stats
            for syscall_number in 0..100 {
                for pid in 0..10 {
                    let key = Self::generate_stats_key(syscall_number, pid);
                    if let Some(stats) = self.syscall_stats_map.lookup_cpu(&key, cpu)? {
                        // Get syscall info
                        let info = self.syscall_info_map.lookup_cpu(&syscall_number, cpu)?;
                        
                        // Create export data
                        let export_data = if let Some(info) = info {
                            serde_json::json!({
                                "syscall_number": syscall_number,
                                "syscall_name": info.syscall_name,
                                "pid": pid,
                                "comm": info.comm,
                                "count": stats.count,
                                "total_time_ns": stats.total_time_ns,
                                "min_time_ns": stats.min_time_ns,
                                "max_time_ns": stats.max_time_ns,
                                "avg_time_ns": if stats.count > 0 { stats.total_time_ns / stats.count } else { 0 },
                                "error_count": stats.error_count,
                                "error_rate": if stats.count > 0 { (stats.error_count as f64) / (stats.count as f64) } else { 0.0 },
                                "last_call": stats.last_call,
                                "cpu": cpu,
                            })
                        } else {
                            serde_json::json!({
                                "syscall_number": syscall_number,
                                "pid": pid,
                                "count": stats.count,
                                "total_time_ns": stats.total_time_ns,
                                "min_time_ns": stats.min_time_ns,
                                "max_time_ns": stats.max_time_ns,
                                "avg_time_ns": if stats.count > 0 { stats.total_time_ns / stats.count } else { 0 },
                                "error_count": stats.error_count,
                                "error_rate": if stats.count > 0 { (stats.error_count as f64) / (stats.count as f64) } else { 0.0 },
                                "last_call": stats.last_call,
                                "cpu": cpu,
                            })
                        };
                        
                        // Log to Elasticsearch
                        if let Ok(()) = self.es_logger.log_event("tracepoint_syscall_stats", export_data) {
                            exported += 1;
                        }
                    }
                }
            }
        }
        
        Ok(exported)
    }
    
    /// Get statistics for all maps
    pub fn get_stats(&self) -> (PerCpuMapStats, PerCpuMapStats, PerCpuMapStats) {
        (
            self.syscall_info_map.stats().clone(),
            self.syscall_stats_map.stats().clone(),
            self.syscall_sequence_map.stats().clone(),
        )
    }
}

/// Tracepoint hook handler for syscall entry
pub fn handle_syscall_entry(
    hooker: &TracepointHooker,
    syscall_number: u32,
    syscall_name: &str,
    pid: u32,
    tid: u32,
    uid: u32,
    comm: &str,
) -> Result<(), MapError> {
    // Get current CPU
    let cpu = 0; // In a real implementation, get the current CPU
    
    // Record syscall entry
    hooker.record_syscall_entry(syscall_number, syscall_name, pid, tid, uid, comm, cpu)?;
    
    Ok(())
}

/// Tracepoint hook handler for syscall exit
pub fn handle_syscall_exit(
    hooker: &TracepointHooker,
    syscall_number: u32,
    pid: u32,
    duration_ns: u64,
    error: bool,
) -> Result<(), MapError> {
    // Get current CPU
    let cpu = 0; // In a real implementation, get the current CPU
    
    // Record syscall exit
    hooker.record_syscall_exit(syscall_number, pid, duration_ns, error, cpu)?;
    
    Ok(())
}

/// Example test function
#[cfg(test)]
mod tests {
    use super::*;
    use inspector_gadget::logging::ElasticsearchConfig;
    
    #[test]
    fn test_tracepoint_hooker() -> Result<(), MapError> {
        // Create Elasticsearch logger
        let es_config = ElasticsearchConfig {
            url: "http://localhost:9200".to_string(),
            index: "tracepoint_test_results".to_string(),
            username: None,
            password: None,
            batch_size: 10,
            flush_interval: 5,
            connect_timeout: 10,
            request_timeout: 30,
        };
        
        let es_logger = Arc::new(
            ElasticsearchLogger::new(es_config).expect("Failed to create Elasticsearch logger"),
        );
        
        // Create Tracepoint Hooker
        let hooker = TracepointHooker::new(es_logger)?;
        
        // Test syscall entry
        handle_syscall_entry(
            &hooker,
            1, // write
            "write",
            1000,
            1000,
            1000,
            "test_process",
        )?;
        
        // Test syscall exit
        handle_syscall_exit(&hooker, 1, 1000, 1000000, false)?;
        
        // Test another syscall entry
        handle_syscall_entry(
            &hooker,
            2, // open
            "open",
            1000,
            1000,
            1000,
            "test_process",
        )?;
        
        // Test syscall exit with error
        handle_syscall_exit(&hooker, 2, 1000, 500000, true)?;
        
        // Verify syscall info
        let info = hooker.get_syscall_info(1, 0)?;
        assert!(info.is_some());
        let info = info.unwrap();
        assert_eq!(info.syscall_number, 1);
        assert_eq!(info.syscall_name, "write");
        assert_eq!(info.pid, 1000);
        
        // Verify syscall stats
        let stats = hooker.get_syscall_stats(1, 1000, 0)?;
        assert!(stats.is_some());
        let stats = stats.unwrap();
        assert_eq!(stats.count, 1);
        assert_eq!(stats.total_time_ns, 1000000);
        assert_eq!(stats.min_time_ns, 1000000);
        assert_eq!(stats.max_time_ns, 1000000);
        assert_eq!(stats.error_count, 0);
        
        // Verify syscall sequence
        let sequence = hooker.get_syscall_sequence(1000, 0)?;
        assert!(sequence.is_some());
        let sequence = sequence.unwrap();
        assert_eq!(sequence.syscalls, vec![1, 2]);
        assert_eq!(sequence.pid, 1000);
        
        // Get total syscall stats
        let total_stats = hooker.get_total_syscall_stats(1, 1000)?;
        assert!(total_stats.is_some());
        let total_stats = total_stats.unwrap();
        println!("Total stats for syscall 1: {:?}", total_stats);
        
        // Export syscall stats
        let exported = hooker.export_syscall_stats()?;
        println!("Exported {} syscall stats", exported);
        
        // Get statistics
        let (info_stats, stats_stats, sequence_stats) = hooker.get_stats();
        println!("Syscall info map stats: {:?}", info_stats);
        println!("Syscall stats map stats: {:?}", stats_stats);
        println!("Syscall sequence map stats: {:?}", sequence_stats);
        
        Ok(())
    }
} 