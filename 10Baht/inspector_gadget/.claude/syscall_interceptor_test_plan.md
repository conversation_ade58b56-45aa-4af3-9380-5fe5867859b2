# SyscallInterceptor Test Plan

## Overview

This document outlines the test plan for the Linux SyscallInterceptor implementation with Elasticsearch and Per-CPU Maps integration. The tests are designed to verify that the implementation correctly intercepts syscalls, processes events, and logs them to Elasticsearch without limiting the number of events.

## Test Environment Setup

1. **Prerequisites**:
   - Linux kernel 5.10+ with eBPF support
   - Elasticsearch instance running (local or remote)
   - Rust toolchain with cargo
   - libbpf development libraries

2. **Environment Variables**:
   ```bash
   export RUST_LOG=debug
   export ELASTICSEARCH_URL=http://localhost:9200
   export ELASTICSEARCH_INDEX=syscall_events
   ```

3. **Build with Features**:
   ```bash
   cargo build --features elasticsearch,percpu_maps,tokio,xdp,tracepoint
   ```

## Test Cases

### 1. Basic Functionality Tests

#### 1.1 Initialization Test
- **Objective**: Verify that the SyscallInterceptor can be initialized correctly
- **Steps**:
  1. Create a new LinuxSyscallInterceptor instance
  2. Verify that it initializes without errors
  3. Check that the syscall registry is populated
- **Expected Result**: Interceptor initializes successfully with syscall registry populated

#### 1.2 Elasticsearch Integration Test
- **Objective**: Verify that the SyscallInterceptor can be initialized with Elasticsearch
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Elasticsearch integration
  2. Verify that it connects to Elasticsearch
  3. Check that the flush thread is started
- **Expected Result**: Interceptor initializes with Elasticsearch and starts the flush thread

#### 1.3 Per-CPU Maps Integration Test
- **Objective**: Verify that the SyscallInterceptor can be initialized with Per-CPU Maps
- **Steps**:
  1. Create a new LinuxSyscallInterceptor
  2. Enable Per-CPU Maps
  3. Verify that the Per-CPU Maps are created
- **Expected Result**: Interceptor initializes with Per-CPU Maps

### 2. Event Processing Tests

#### 2.1 Event Collection Test
- **Objective**: Verify that the SyscallInterceptor collects events correctly
- **Steps**:
  1. Create a new LinuxSyscallInterceptor
  2. Start intercepting syscalls
  3. Generate some syscalls (e.g., open, read, write)
  4. Collect events
  5. Verify that the events are collected
- **Expected Result**: Events are collected correctly

#### 2.2 Event Filtering Test
- **Objective**: Verify that the SyscallInterceptor filters events correctly
- **Steps**:
  1. Create a new LinuxSyscallInterceptor
  2. Apply a filter (e.g., only collect open syscalls)
  3. Start intercepting syscalls
  4. Generate various syscalls (open, read, write)
  5. Collect events
  6. Verify that only the filtered events are collected
- **Expected Result**: Only filtered events are collected

#### 2.3 Event Statistics Test
- **Objective**: Verify that the SyscallInterceptor updates statistics correctly
- **Steps**:
  1. Create a new LinuxSyscallInterceptor
  2. Start intercepting syscalls
  3. Generate some syscalls
  4. Get statistics
  5. Verify that the statistics are updated correctly
- **Expected Result**: Statistics are updated correctly

### 3. Elasticsearch Integration Tests

#### 3.1 Event Logging Test
- **Objective**: Verify that events are logged to Elasticsearch
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Elasticsearch integration
  2. Start intercepting syscalls
  3. Generate some syscalls
  4. Wait for the flush interval
  5. Query Elasticsearch for the events
  6. Verify that the events are logged
- **Expected Result**: Events are logged to Elasticsearch

#### 3.2 Batch Processing Test
- **Objective**: Verify that events are processed in batches
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Elasticsearch integration
  2. Configure a small batch size (e.g., 10)
  3. Start intercepting syscalls
  4. Generate many syscalls (e.g., 100)
  5. Monitor the logs for batch processing
  6. Verify that events are processed in batches
- **Expected Result**: Events are processed in batches of the configured size

#### 3.3 Flush Interval Test
- **Objective**: Verify that events are flushed at the configured interval
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Elasticsearch integration
  2. Configure a short flush interval (e.g., 500ms)
  3. Start intercepting syscalls
  4. Generate some syscalls
  5. Wait for the flush interval
  6. Verify that events are flushed
- **Expected Result**: Events are flushed at the configured interval

### 4. Per-CPU Maps Integration Tests

#### 4.1 Per-CPU Event Collection Test
- **Objective**: Verify that events are collected per CPU
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Per-CPU Maps integration
  2. Start intercepting syscalls
  3. Generate syscalls on multiple CPUs
  4. Verify that events are collected per CPU
- **Expected Result**: Events are collected per CPU

#### 4.2 Per-CPU Statistics Test
- **Objective**: Verify that statistics are collected per CPU
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Per-CPU Maps integration
  2. Start intercepting syscalls
  3. Generate syscalls on multiple CPUs
  4. Get per-CPU statistics
  5. Verify that statistics are collected per CPU
- **Expected Result**: Statistics are collected per CPU

#### 4.3 Per-CPU Elasticsearch Integration Test
- **Objective**: Verify that per-CPU events are logged to Elasticsearch
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Per-CPU Maps and Elasticsearch integration
  2. Start intercepting syscalls
  3. Generate syscalls on multiple CPUs
  4. Wait for the flush interval
  5. Query Elasticsearch for the events
  6. Verify that events from all CPUs are logged
- **Expected Result**: Events from all CPUs are logged to Elasticsearch

### 5. XDP and Tracepoint Integration Tests

#### 5.1 XDP Integration Test
- **Objective**: Verify that XDP events are intercepted and logged
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Elasticsearch integration
  2. Configure XDP integration for a network interface
  3. Generate network traffic
  4. Wait for the flush interval
  5. Query Elasticsearch for XDP events
  6. Verify that XDP events are logged
- **Expected Result**: XDP events are intercepted and logged

#### 5.2 Tracepoint Integration Test
- **Objective**: Verify that tracepoint events are intercepted and logged
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Elasticsearch integration
  2. Configure tracepoint integration for a specific tracepoint
  3. Generate events that trigger the tracepoint
  4. Wait for the flush interval
  5. Query Elasticsearch for tracepoint events
  6. Verify that tracepoint events are logged
- **Expected Result**: Tracepoint events are intercepted and logged

### 6. Stress Tests

#### 6.1 High-Throughput Test
- **Objective**: Verify that the SyscallInterceptor can handle high throughput
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Elasticsearch integration
  2. Start intercepting syscalls
  3. Generate a high volume of syscalls (e.g., 10,000 per second)
  4. Monitor memory usage and CPU utilization
  5. Verify that all events are logged to Elasticsearch
- **Expected Result**: All events are processed and logged without memory issues

#### 6.2 Long-Running Test
- **Objective**: Verify that the SyscallInterceptor can run for extended periods
- **Steps**:
  1. Create a new LinuxSyscallInterceptor with Elasticsearch integration
  2. Start intercepting syscalls
  3. Generate syscalls periodically over a long period (e.g., 24 hours)
  4. Monitor memory usage and CPU utilization
  5. Verify that the interceptor continues to function correctly
- **Expected Result**: Interceptor runs stably for extended periods

## Test Execution

### Manual Testing

For manual testing, use the `pimp.py` tool:

```bash
# Start the test environment
cd /home/<USER>/dev/inspector_gadget
nix-shell

# Run the pimp.py tool
python3 tools/pimp/pimp.py status
python3 tools/pimp/pimp.py setup
python3 tools/pimp/pimp.py run --test syscall_interceptor_test
```

### Automated Testing

For automated testing, use the test scripts:

```bash
# Run all tests
cargo test --features elasticsearch,percpu_maps,tokio,xdp,tracepoint

# Run specific tests
cargo test --test syscall_interceptor_test -- --nocapture
```

## Test Results

Test results should be documented in the `.claude/test_results/` directory with the following format:

```
test_name_YYYY-MM-DD.md
```

Each test result document should include:
- Test name and date
- Test environment details
- Steps executed
- Results observed
- Any issues encountered
- Screenshots or logs if applicable 