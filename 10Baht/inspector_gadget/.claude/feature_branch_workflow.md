# Git Feature Branch Workflow

This document outlines the Git workflow for feature development in an AI-assisted environment.

## Branch Naming Convention

- Feature branches: `feature/short-feature-description`
- Bugfix branches: `bugfix/issue-description`
- Hotfix branches: `hotfix/critical-issue-description`
- Release branches: `release/version-number`

## Feature Development Process

### 1. Starting a New Feature

Always start from an up-to-date main branch:

```bash
git checkout main
git pull origin main
git checkout -b feature/feature-name
```

Set up the remote tracking immediately:

```bash
git push -u origin feature/feature-name
```

### 2. Making Commits

Follow these guidelines for commits:

- Use semantic commit messages:
  - `feat: add user authentication`
  - `fix: resolve login form validation issue`
  - `docs: update API documentation`
  - `style: format code according to style guide`
  - `refactor: simplify authentication logic`
  - `test: add unit tests for user service`
  - `chore: update dependencies`

- Keep commits focused on single logical changes
- Reference issue numbers when applicable: `feat: implement login form (#123)`
- Commit frequently with clear messages

### 3. Pushing Changes

Push changes to the remote branch frequently:

```bash
git push
```

Always push before ending a development session to ensure work is backed up and visible to the team.

### 4. Staying Up-to-Date with Main

Regularly incorporate changes from main to avoid significant divergence:

```bash
git checkout main
git pull origin main
git checkout feature/feature-name
git merge main
```

Alternatively, use rebase for a cleaner history:

```bash
git checkout feature/feature-name
git fetch origin
git rebase origin/main
```

If using rebase and the branch is already pushed:

```bash
git push --force-with-lease
```

### 5. Completing a Feature

Before finalizing a feature:

1. Ensure all tests pass
2. Update documentation
3. Rebase on latest main if needed
4. Make a final push

```bash
git push
```

### 6. Creating a Pull Request

Create a pull request with:

- Clear title describing the feature
- Detailed description of changes
- Reference to related issue(s)
- Steps to test the feature
- Screenshots or videos if applicable

### 7. After Merge

Once the pull request is merged:

```bash
git checkout main
git pull origin main
git branch -d feature/feature-name  # Delete local branch
git push origin --delete feature/feature-name  # Delete remote branch
```

## Handling Merge Conflicts

If conflicts occur during merge or rebase:

1. Identify conflicting files:
   ```bash
   git status
   ```

2. Edit files to resolve conflicts (look for `<<<<<<< HEAD`, `=======`, and `>>>>>>> branch-name` markers)

3. Add resolved files:
   ```bash
   git add <resolved-files>
   ```

4. Continue the merge or rebase:
   ```bash
   # For merge
   git merge --continue
   
   # For rebase
   git rebase --continue
   ```

## Best Practices for AI-Assisted Development

1. Always verify the branch you're on before making changes:
   ```bash
   git branch
   ```

2. Pull before starting work each session:
   ```bash
   git pull
   ```

3. Keep feature branches short-lived (1-2 weeks maximum)

4. Avoid committing sensitive information (credentials, API keys)

5. Use meaningful commit messages that clearly describe what changed and why

6. Keep commits atomic and focused on single logical changes

7. Push changes at least daily, and before ending each development session

8. Document significant decisions or approaches in commit messages or PR descriptions

9. Always set upstream tracking for new branches using `git push -u origin branch-name`

10. Delete branches after they're merged to keep the repository clean 