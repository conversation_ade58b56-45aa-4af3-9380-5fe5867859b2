# eBPF Mapper Functionality Guide

## Overview

The eBPF Mapper is a critical component of Inspector Gadget that provides optimized hash map functionality for efficient data sharing between kernel and user space. This document explains the purpose, implementation, and usage of the eBPF Mapper, as well as how to test it using the PIMP (Performance & Integration Management Platform) tool.

## Purpose

The eBPF Mapper serves several key purposes:

1. **Efficient Data Sharing**: Enables high-performance data sharing between kernel and user space components
2. **Memory Optimization**: Provides memory-efficient storage and retrieval of key-value pairs
3. **Concurrent Access**: Supports concurrent access patterns for high-throughput scenarios
4. **Performance Monitoring**: Facilitates performance monitoring and analysis of system behavior

## Implementation

The eBPF Mapper is implemented in the `hooker_pill/src/hashmap_test.rs` file and includes the following key components:

### Core Components

1. **OptimizedHashMap**: A high-performance hash map implementation optimized for eBPF use cases
2. **TypedOptimizedHashMap**: A type-safe wrapper around the OptimizedHashMap for structured data
3. **HashFunction**: Configurable hash functions for different key types and sizes
4. **MapError**: Comprehensive error handling for map operations

### Key Features

- **Optimized Hash Functions**: Uses FNV-1a for small keys and MurmurHash3 for larger keys
- **Cache-Efficient Layout**: Aligns keys and values to cache lines for improved performance
- **Lock-Free Operations**: Implements lock-free operations where possible to reduce contention
- **Batch Operations**: Supports batch operations for improved throughput
- **Memory-Mapped Access**: Uses memory-mapped access for high-performance operations
- **Automatic Eviction**: Implements LRU-based eviction for memory management

## Testing with PIMP

The PIMP (Performance & Integration Management Platform) tool provides a comprehensive testing framework for the eBPF Mapper. Here's how to use it:

### Prerequisites

- Nix package manager installed
- Docker for Elasticsearch logging (optional)

### Running eBPF Mapper Tests

1. **Enter the nix-shell environment**:
   ```bash
   cd ~/dev/inspector_gadget
   nix-shell
   ```

2. **Run the hashmap tests directly**:
   ```bash
   cd hooker_pill/src
   bash run_hashmap_tests.sh
   ```

3. **Using PIMP for comprehensive testing**:
   ```bash
   cd tools/pimp
   python3 pimp.py pill inject --component ebpf_mapper --vm test_vm
   ```

4. **Checking test results in Elasticsearch**:
   - Start Elasticsearch: `cd .dockerwrapper && docker-compose up -d`
   - Access Kibana at http://localhost:5601
   - Search for "ebpf_mapper" in the Discover view

### Test Cases

The eBPF Mapper tests include:

1. **Basic Hash Map Operations**: Tests basic key-value operations (get, put, delete)
2. **Typed Hash Map Operations**: Tests operations with structured data types
3. **Batch Operations**: Tests performance of batch operations
4. **Concurrency Tests**: Tests behavior under concurrent access patterns
5. **Performance Benchmarks**: Measures throughput and latency metrics

## Troubleshooting

### Common Issues

1. **Missing Dependencies**:
   - Ensure you're using the nix-shell environment
   - Check that all required Python packages are installed

2. **Build Failures**:
   - Verify that the hooker_pill library is properly built
   - Check for any missing system dependencies

3. **Test Failures**:
   - Check the test logs for specific error messages
   - Verify that the eBPF maps are properly initialized

4. **Performance Issues**:
   - Check for lock contention in high-concurrency scenarios
   - Verify that the optimized hash functions are being used

## Integration with Elasticsearch

The eBPF Mapper can log performance metrics and test results to Elasticsearch for analysis:

1. **Start Elasticsearch and Kibana**:
   ```bash
   cd .dockerwrapper
   docker-compose up -d
   ```

2. **Configure Logging**:
   - Edit `tools/pimp/config/default_config.yaml` to enable Elasticsearch logging
   - Set the appropriate Elasticsearch endpoint and index name

3. **Visualize Results**:
   - Access Kibana at http://localhost:5601
   - Create visualizations for map operation throughput, latency, and error rates

## Future Enhancements

Planned enhancements for the eBPF Mapper include:

1. **Additional Map Types**: Support for more specialized map types (LPM, Stack Trace)
2. **Improved Concurrency**: Enhanced lock-free algorithms for higher throughput
3. **Memory Optimization**: Reduced memory footprint for resource-constrained environments
4. **Integration with BPF CO-RE**: Support for BPF Compile Once - Run Everywhere
5. **Enhanced Monitoring**: More detailed performance metrics and analysis tools

## References

- [eBPF Maps Documentation](https://docs.kernel.org/bpf/maps.html)
- [Inspector Gadget Architecture](/.claude/architecture.md)
- [eBPF Maps PRD](/.claude/ebpf_maps_prd.md)
- [Enhanced eBPF Functionality](/.claude/enhanced_ebpf_functionality.md) 