use std::io;
use thiserror::Error;

/// Error types for the Inspector Gadget library
#[derive(Erro<PERSON>, Debug)]
pub enum Error {
    /// I/O error
    #[error("I/O error: {0}")]
    Io(io::Error),
    
    /// eBPF error
    #[error("eBPF error: {0}")]
    Ebpf(String),
    
    /// Configuration error
    #[error("Configuration error: {0}")]
    Config(String),
    
    /// Elasticsearch error
    #[error("Elasticsearch error: {0}")]
    Elasticsearch(String),
    
    /// Other error
    #[error("{0}")]
    Other(String),
}

/// Result type for the Inspector Gadget library
pub type Result<T> = std::result::Result<T, Error>;
