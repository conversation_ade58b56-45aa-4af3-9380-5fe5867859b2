/*!
 * Advanced eBPF Hooking for Inspector Gadget
 * 
 * This module provides enhanced eBPF-based syscall interception and monitoring
 * capabilities for Inspector Gadget.
 */


use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::mpsc::{channel, Receiver, Sender};
use std::thread;




// Submodules
pub mod lsm;
pub mod xdp;
pub mod tracepoints;
pub mod uprobes;
pub mod programs;
pub mod loader;
pub mod bpf;
pub mod maps;

// Re-exports
pub use lsm::LsmHooker;
pub use xdp::XdpHooker;
pub use tracepoints::TracepointHooker;
pub use uprobes::UprobeHooker;
pub use loader::EbpfLoader;
pub use maps::{Map, MapType, MapConfig, MapBuilder, TypedMap, TypedMapBuilder};

/// eBPF program types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ProgramType {
    /// Linux Security Module
    LSM,
    /// eXpress Data Path
    XDP,
    /// Kernel tracepoint
    Tracepoint,
    /// User-space probe
    Uprobe,
}

/// eBPF hooking error
#[derive(Debug, Error)]
pub enum EbpfError {
    /// eBPF initialization error
    #[error("eBPF initialization error: {0}")]
    InitError(String),
    
    /// eBPF program loading error
    #[error("eBPF program loading error: {0}")]
    ProgramError(String),
    
    /// eBPF collection error
    #[error("eBPF collection error: {0}")]
    CollectionError(String),
    
    /// eBPF program not found
    #[error("eBPF program not found: {0}")]
    ProgramNotFound(String),
    
    /// eBPF program already loaded
    #[error("eBPF program already loaded: {0}")]
    ProgramAlreadyLoaded(String),
    
    /// eBPF map error
    #[error("eBPF map error: {0}")]
    MapError(#[from] maps::MapError),
}

/// eBPF event
#[derive(Debug, Clone)]
pub struct EbpfEvent {
    /// Event ID
    pub id: u64,
    /// Timestamp
    pub timestamp: u64,
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// Program type
    pub program_type: ProgramType,
    /// Program name
    pub program_name: String,
    /// Event data
    pub data: Vec<u8>,
}

/// eBPF hooker configuration
#[derive(Debug, Clone)]
pub struct EbpfHookerConfig {
    /// Maximum number of events to buffer
    pub max_events: usize,
    /// Event polling interval in milliseconds
    pub polling_interval_ms: u64,
    /// Whether to verify programs before loading
    pub verify_programs: bool,
    /// Verification log level (0-4)
    pub log_level: u32,
}

impl Default for EbpfHookerConfig {
    fn default() -> Self {
        Self {
            max_events: 1000,
            polling_interval_ms: 100,
            verify_programs: true,
            log_level: 1,
        }
    }
}

/// eBPF hooker
#[derive(Debug)]
pub struct EbpfHooker<'a> {
    /// Running flag
    running: Arc<AtomicBool>,
    /// Event receiver
    event_receiver: Option<Receiver<EbpfEvent>>,
    /// Event sender
    event_sender: Option<Sender<EbpfEvent>>,
    /// Collection thread handle
    collection_thread: Option<thread::JoinHandle<()>>,
    /// Next event ID
    next_event_id: u64,
    /// Configuration
    config: EbpfHookerConfig,
    /// Loaded programs
    loaded_programs: Vec<(ProgramType, String)>,
    /// eBPF loader
    #[cfg(feature = "linux")]
    loader: Option<loader::EbpfLoader<'a>>,
}

impl EbpfHooker<'_> {
    /// Create a new eBPF hooker with default configuration
    pub fn new() -> Result<Self, EbpfError> {
        Self::with_config(EbpfHookerConfig::default())
    }
    
    /// Create a new eBPF hooker with custom configuration
    pub fn with_config(config: EbpfHookerConfig) -> Result<Self, EbpfError> {
        // Check if eBPF is available
        if !Self::is_ebpf_available() {
            return Err(EbpfError::InitError(
                "eBPF is not available on this system".to_string(),
            ));
        }
        
        let running = Arc::new(AtomicBool::new(false));
        
        #[cfg(feature = "linux")]
        let loader = {
            // Create loader configuration
            let loader_config = loader::LoaderConfig {
                verify_programs: config.verify_programs,
                log_level: config.log_level,
                polling_interval_ms: config.polling_interval_ms,
            };
            
            // Create loader
            match loader::EbpfLoader::with_config(Arc::clone(&running), loader_config) {
                Ok(loader) => Some(loader),
                Err(e) => {
                    return Err(EbpfError::InitError(
                        format!("Failed to create eBPF loader: {}", e)
                    ));
                }
            }
        };
        
        Ok(Self {
            running,
            event_receiver: None,
            event_sender: None,
            collection_thread: None,
            next_event_id: 1,
            config,
            loaded_programs: Vec::new(),
            #[cfg(feature = "linux")]
            loader,
        })
    }
    
    /// Check if eBPF is available on the system
    fn is_ebpf_available() -> bool {
        #[cfg(feature = "linux")]
        {
            // Check if we're on Linux
            if !cfg!(target_os = "linux") {
                return false;
            }
            
            // TODO: Implement more thorough eBPF availability check
            // For example, check kernel version, BPF syscall availability, etc.
            
            true
        }
        
        #[cfg(not(feature = "linux"))]
        {
            false
        }
    }
    
    /// Load an eBPF program
    pub fn load_program(&mut self, program_type: ProgramType, program_name: &str) -> Result<(), EbpfError> {
        // Check if the program is already loaded
        if self.loaded_programs.contains(&(program_type, program_name.to_string())) {
            return Err(EbpfError::ProgramAlreadyLoaded(program_name.to_string()));
        }
        
        #[cfg(feature = "linux")]
        {
            if let Some(loader) = &mut self.loader {
                // TODO: Implement actual program loading based on program_type
                // For now, just log the loading
                info!("Loading eBPF program: {:?} - {}", program_type, program_name);
            }
        }
        
        // Add the program to the loaded programs list
        self.loaded_programs.push((program_type, program_name.to_string()));
        
        Ok(())
    }
    
    /// Unload an eBPF program
    pub fn unload_program(&mut self, program_type: ProgramType, program_name: &str) -> Result<(), EbpfError> {
        // Check if the program is loaded
        let index = self.loaded_programs.iter().position(|(pt, pn)| *pt == program_type && pn == program_name);
        
        if let Some(index) = index {
            #[cfg(feature = "linux")]
            {
                if let Some(loader) = &mut self.loader {
                    // TODO: Implement actual program unloading
                    info!("Unloading eBPF program: {:?} - {}", program_type, program_name);
                }
            }
            
            // Remove the program from the loaded programs list
            self.loaded_programs.remove(index);
            
            Ok(())
        } else {
            Err(EbpfError::ProgramNotFound(program_name.to_string()))
        }
    }
    
    /// Start monitoring
    pub fn start(&mut self) -> Result<(), EbpfError> {
        if self.running.load(Ordering::SeqCst) {
            return Ok(());
        }
        
        // Check if we have any programs loaded
        if self.loaded_programs.is_empty() {
            return Err(EbpfError::InitError("No eBPF programs loaded".to_string()));
        }
        
        // Create a channel for events
        let (sender, receiver) = channel();
        self.event_sender = Some(sender.clone());
        self.event_receiver = Some(receiver);
        
        #[cfg(feature = "linux")]
        {
            if let Some(loader) = &mut self.loader {
                // Start event collection
                loader.start_collection(sender.clone())?;
            }
        }
        
        // Create a thread for processing events
        let running = Arc::clone(&self.running);
        let receiver = self.event_receiver.as_ref().unwrap().clone();
        let mut next_event_id = self.next_event_id;
        
        self.collection_thread = Some(thread::spawn(move || {
            Self::collection_thread_func(running, receiver, &mut next_event_id);
        }));
        
        self.running.store(true, Ordering::SeqCst);
        info!("eBPF hooking started with {} programs", self.loaded_programs.len());
        
        Ok(())
    }
    
    /// Stop monitoring
    pub fn stop(&mut self) -> Result<(), EbpfError> {
        if !self.running.load(Ordering::SeqCst) {
            return Ok(());
        }
        
        // Signal the collection thread to stop
        self.running.store(false, Ordering::SeqCst);
        
        #[cfg(feature = "linux")]
        {
            if let Some(loader) = &mut self.loader {
                // Stop event collection
                loader.stop_collection()?;
            }
        }
        
        // Wait for the collection thread to finish
        if let Some(thread) = self.collection_thread.take() {
            if let Err(e) = thread.join() {
                error!("Failed to join collection thread: {:?}", e);
            }
        }
        
        // Update the next event ID
        if let Some(receiver) = &self.event_receiver {
            // Drain the receiver to get the latest event ID
            while let Ok(event) = receiver.try_recv() {
                self.next_event_id = event.id + 1;
            }
        }
        
        // Clean up resources
        self.event_receiver = None;
        self.event_sender = None;
        
        info!("eBPF hooking stopped");
        
        Ok(())
    }
    
    /// Get the next event
    pub fn next_event(&self) -> Option<EbpfEvent> {
        if let Some(receiver) = &self.event_receiver {
            match receiver.try_recv() {
                Ok(event) => Some(event),
                Err(_) => None,
            }
        } else {
            None
        }
    }
    
    /// Collection thread function
    fn collection_thread_func(
        running: Arc<AtomicBool>,
        receiver: Receiver<EbpfEvent>,
        next_event_id: &mut u64,
    ) {
        info!("eBPF collection thread started");
        
        while running.load(Ordering::SeqCst) {
            // Process events from the receiver
            match receiver.recv_timeout(Duration::from_millis(100)) {
                Ok(mut event) => {
                    // Set the event ID
                    event.id = *next_event_id;
                    *next_event_id += 1;
                    
                    // Process the event
                    // TODO: Implement actual event processing
                    debug!("Received eBPF event: {:?}", event);
                }
                Err(_) => {
                    // Timeout or channel closed, just continue
                }
            }
        }
        
        info!("eBPF collection thread stopped");
    }
} 