/*!
 * Fileless Malware Detector
 * 
 * This module provides eBPF-based detection of fileless malware techniques.
 */


use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::mpsc::Sender;
use std::collections::{HashMap, HashSet};


use crate::ebpf::{Eb<PERSON><PERSON><PERSON><PERSON>, Ebpf<PERSON>vent, ProgramType};
use crate::ebpf::programs::{ThreatDetector, ThreatType, ThreatDetectionResult};

/// Suspicious memory operation types
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash)]
pub enum SuspiciousMemoryOperation {
    /// Memory region with execute permission
    ExecutableMemory,
    /// Memory region with write+execute permissions
    WriteExecuteMemory,
    /// Memory mapping with anonymous+executable flags
    AnonymousExecutableMapping,
    /// Memory protection change to executable
    ProtectionChangeToExecutable,
    /// Memory injection into another process
    CrossProcessMemoryInjection,
    /// Process hollowing
    ProcessHollowing,
    /// Thread injection
    ThreadInjection,
    /// Reflective DLL/library loading
    ReflectiveLoading,
}

/// Fileless malware detector
pub struct FilelessMalwareDetector {
    /// Running flag
    running: Arc<AtomicBool>,
    /// Event sender
    event_sender: Option<Sender<EbpfEvent>>,
    /// Suspicious processes
    suspicious_processes: HashMap<u32, Vec<SuspiciousMemoryOperation>>,
    /// Executable memory regions
    executable_regions: HashMap<u32, HashSet<u64>>,
    /// Process creation times
    process_creation_times: HashMap<u32, u64>,
    /// Sensitivity threshold (0-100)
    sensitivity: u8,
    /// Whitelist of processes
    process_whitelist: HashSet<String>,
}

impl FilelessMalwareDetector {
    /// Create a new fileless malware detector
    pub fn new(running: Arc<AtomicBool>) -> Self {
        Self {
            running,
            event_sender: None,
            suspicious_processes: HashMap::new(),
            executable_regions: HashMap::new(),
            process_creation_times: HashMap::new(),
            sensitivity: 50,
            process_whitelist: HashSet::new(),
        }
    }
    
    /// Set sensitivity threshold
    pub fn set_sensitivity(&mut self, sensitivity: u8) {
        self.sensitivity = sensitivity.min(100);
        info!("Fileless malware detector sensitivity set to {}", self.sensitivity);
    }
    
    /// Add a process to the whitelist
    pub fn add_to_whitelist(&mut self, process_name: &str) {
        self.process_whitelist.insert(process_name.to_string());
        info!("Added {} to fileless malware detector whitelist", process_name);
    }
    
    /// Remove a process from the whitelist
    pub fn remove_from_whitelist(&mut self, process_name: &str) {
        self.process_whitelist.remove(process_name);
        info!("Removed {} from fileless malware detector whitelist", process_name);
    }
    
    /// Check if a process is whitelisted
    fn is_whitelisted(&self, process_name: &str) -> bool {
        self.process_whitelist.contains(process_name)
    }
    
    /// Calculate threat confidence based on suspicious operations
    fn calculate_confidence(&self, operations: &[SuspiciousMemoryOperation]) -> u8 {
        // Base confidence on the number and types of suspicious operations
        let mut confidence = 0;
        
        for op in operations {
            match op {
                SuspiciousMemoryOperation::ExecutableMemory => confidence += 10,
                SuspiciousMemoryOperation::WriteExecuteMemory => confidence += 30,
                SuspiciousMemoryOperation::AnonymousExecutableMapping => confidence += 40,
                SuspiciousMemoryOperation::ProtectionChangeToExecutable => confidence += 50,
                SuspiciousMemoryOperation::CrossProcessMemoryInjection => confidence += 70,
                SuspiciousMemoryOperation::ProcessHollowing => confidence += 80,
                SuspiciousMemoryOperation::ThreadInjection => confidence += 60,
                SuspiciousMemoryOperation::ReflectiveLoading => confidence += 75,
            }
        }
        
        // Adjust based on the number of different techniques
        let unique_operations = operations.iter().collect::<HashSet<_>>().len();
        confidence = confidence.saturating_add((unique_operations as u8) * 5);
        
        // Cap at 100
        confidence.min(100)
    }
    
    /// Process a memory-related syscall event
    fn process_memory_syscall(&mut self, event: &EbpfEvent) -> Option<SuspiciousMemoryOperation> {
        // TODO: Implement actual memory syscall analysis
        // For now, just return None
        None
    }
    
    /// Process a process-related syscall event
    fn process_process_syscall(&mut self, event: &EbpfEvent) -> Option<SuspiciousMemoryOperation> {
        // TODO: Implement actual process syscall analysis
        // For now, just return None
        None
    }
}

impl ThreatDetector for FilelessMalwareDetector {
    fn threat_type(&self) -> ThreatType {
        ThreatType::FilelessMalware
    }
    
    fn initialize(&mut self) -> Result<(), EbpfError> {
        // Add common legitimate processes to whitelist
        self.add_to_whitelist("chrome");
        self.add_to_whitelist("firefox");
        self.add_to_whitelist("systemd");
        self.add_to_whitelist("sshd");
        self.add_to_whitelist("bash");
        self.add_to_whitelist("zsh");
        self.add_to_whitelist("java");
        self.add_to_whitelist("python");
        self.add_to_whitelist("node");
        
        info!("Fileless malware detector initialized");
        
        Ok(())
    }
    
    fn start(&mut self, event_sender: Sender<EbpfEvent>) -> Result<(), EbpfError> {
        if self.running.load(Ordering::SeqCst) {
            return Ok(());
        }
        
        // Store the event sender
        self.event_sender = Some(event_sender);
        
        info!("Fileless malware detector started");
        
        Ok(())
    }
    
    fn stop(&mut self) -> Result<(), EbpfError> {
        if !self.running.load(Ordering::SeqCst) {
            return Ok(());
        }
        
        // Clean up resources
        self.event_sender = None;
        
        info!("Fileless malware detector stopped");
        
        Ok(())
    }
    
    fn process_event(&mut self, event: &EbpfEvent) -> Option<ThreatDetectionResult> {
        // Skip if not a memory or process-related event
        // TODO: Implement proper event type checking
        
        // Process the event based on its type
        let suspicious_op = match event.program_type {
            ProgramType::Tracepoint => {
                // Check if it's a memory-related tracepoint
                if event.program_name.contains("mm_") || event.program_name.contains("mmap") {
                    self.process_memory_syscall(event)
                } else if event.program_name.contains("process") || event.program_name.contains("exec") {
                    self.process_process_syscall(event)
                } else {
                    None
                }
            }
            ProgramType::Uprobe => {
                // Check if it's a memory-related function
                if event.program_name.contains("mmap") || event.program_name.contains("malloc") {
                    self.process_memory_syscall(event)
                } else {
                    None
                }
            }
            _ => None,
        };
        
        // If we found a suspicious operation, add it to the process's list
        if let Some(op) = suspicious_op {
            let pid = event.pid;
            
            self.suspicious_processes
                .entry(pid)
                .or_insert_with(Vec::new)
                .push(op);
            
            // Check if we have enough evidence to report a threat
            if let Some(operations) = self.suspicious_processes.get(&pid) {
                let confidence = self.calculate_confidence(operations);
                
                // Only report if confidence exceeds sensitivity threshold
                if confidence >= self.sensitivity {
                    // Create evidence map
                    let mut evidence = HashMap::new();
                    for (i, op) in operations.iter().enumerate() {
                        evidence.insert(format!("operation_{}", i), format!("{:?}", op));
                    }
                    
                    // Create detection result
                    let result = ThreatDetectionResult {
                        threat_type: ThreatType::FilelessMalware,
                        confidence,
                        description: format!("Detected fileless malware techniques in process {}", pid),
                        pid,
                        tid: event.tid,
                        evidence,
                        timestamp: event.timestamp,
                    };
                    
                    return Some(result);
                }
            }
        }
        
        None
    }
} 