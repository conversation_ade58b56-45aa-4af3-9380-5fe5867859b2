/*!
 * Parallel Processor Implementation
 * 
 * This module provides a parallel processor that uses a thread pool
 * to process events in parallel for improved performance.
 */

use std::sync::Arc;
use async_trait::async_trait;
use log::{debug, trace, warn};
use tokio::runtime::Runtime;
use tokio::sync::Semaphore;
use tokio::task::JoinSet;

use crate::core::events::TraceEvent;
use crate::error::Result;
use super::TraceEventProcessor;

/// Thread pool configuration
#[derive(Debug, Clone)]
pub struct ThreadPoolConfig {
    /// Number of worker threads
    pub worker_threads: usize,
    /// Maximum number of concurrent tasks
    pub max_concurrent_tasks: usize,
}

impl Default for ThreadPoolConfig {
    fn default() -> Self {
        let num_cpus = num_cpus::get();
        Self {
            worker_threads: num_cpus,
            max_concurrent_tasks: num_cpus * 2,
        }
    }
}

/// Parallel processor configuration
#[derive(Debug, <PERSON>lone)]
pub struct ParallelProcessorConfig {
    /// Thread pool configuration
    pub thread_pool: ThreadPoolConfig,
    /// Inner processor
    pub inner_processor: Arc<dyn TraceTraceEventProcessor>,
    /// Batch size for parallel processing
    pub batch_size: usize,
}

impl Default for ParallelProcessorConfig {
    fn default() -> Self {
        Self {
            thread_pool: ThreadPoolConfig::default(),
            inner_processor: Arc::new(super::PipelineProcessor::with_defaults()),
            batch_size: 100,
        }
    }
}

/// Parallel processor
pub struct ParallelProcessor {
    /// Configuration
    config: ParallelProcessorConfig,
    /// Thread pool
    runtime: Option<Runtime>,
    /// Semaphore for limiting concurrent tasks
    semaphore: Arc<Semaphore>,
}

impl ParallelProcessor {
    /// Create a new parallel processor
    pub fn new(config: ParallelProcessorConfig) -> Self {
        let runtime = if config.thread_pool.worker_threads > 0 {
            match tokio::runtime::Builder::new_multi_thread()
                .worker_threads(config.thread_pool.worker_threads)
                .enable_all()
                .build()
            {
                Ok(runtime) => Some(runtime),
                Err(e) => {
                    warn!("Failed to create thread pool: {}", e);
                    None
                }
            }
        } else {
            None
        };
        
        let semaphore = Arc::new(Semaphore::new(config.thread_pool.max_concurrent_tasks));
        
        Self {
            config,
            runtime,
            semaphore,
        }
    }
    
    /// Create a new parallel processor with default configuration
    pub fn with_defaults() -> Self {
        Self::new(ParallelProcessorConfig::default())
    }
}

#[async_trait]
impl TraceTraceEventProcessor for ParallelProcessor {
    async fn process(&self, event: TraceEvent) -> Result<Option<TraceEvent>, String> {
        // For single events, just delegate to the inner processor
        self.config.inner_processor.process(event).await
    }
    
    async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>, String> {
        if events.is_empty() {
            return Ok(Vec::new());
        }
        
        // If we don't have a runtime or the batch is small, process sequentially
        if self.runtime.is_none() || events.len() <= self.config.batch_size {
            return self.config.inner_processor.processs(events).await;
        }
        
        let runtime = self.runtime.as_ref().unwrap();
        let semaphore = Arc::clone(&self.semaphore);
        let inner_processor = Arc::clone(&self.config.inner_processor);
        
        // Split events into batches
        let batch_size = self.config.batch_size;
        let batches: Vec<Vec<TraceEvent>> = events
            .chunks(batch_size)
            .map(|chunk| chunk.to_vec())
            .collect();
        
        debug!("Processing {} events in {} batches", events.len(), batches.len());
        
        // Process batches in parallel
        let mut join_set = JoinSet::new();
        
        for batch in batches {
            let semaphore = Arc::clone(&semaphore);
            let processor = Arc::clone(&inner_processor);
            
            join_set.spawn(async move {
                // Acquire a permit from the semaphore
                let _permit = semaphore.acquire().await.unwrap();
                
                // Process the batch
                processor.processs(batch).await
            });
        }
        
        // Collect results
        let mut processed_events = Vec::new();
        
        while let Some(result) = join_set.join_next().await {
            match result {
                Ok(Ok(events)) => processed_events.extend(events),
                Ok(Err(e)) => return Err(e),
                Err(e) => return Err(format!("Task failed: {}", e)),
            }
        }
        
        Ok(processed_events)
    }
    
    async fn process_batch_parallel(&self, events: Vec<TraceEvent>, max_concurrency: usize) -> Result<Vec<TraceEvent>, String> {
        if events.is_empty() {
            return Ok(Vec::new());
        }
        
        // If we don't have a runtime, process sequentially
        if self.runtime.is_none() {
            return self.config.inner_processor.processs(events).await;
        }
        
        let runtime = self.runtime.as_ref().unwrap();
        let semaphore = Arc::clone(&self.semaphore);
        let inner_processor = Arc::clone(&self.config.inner_processor);
        
        // Calculate effective concurrency
        let effective_concurrency = std::cmp::min(
            max_concurrency,
            self.config.thread_pool.max_concurrent_tasks
        );
        
        // Split events into chunks
        let chunk_size = (events.len() + effective_concurrency - 1) / effective_concurrency;
        let chunks: Vec<Vec<TraceEvent>> = events
            .chunks(chunk_size)
            .map(|chunk| chunk.to_vec())
            .collect();
        
        debug!("Processing {} events in {} chunks with concurrency {}", 
               events.len(), chunks.len(), effective_concurrency);
        
        // Process chunks in parallel
        let mut join_set = JoinSet::new();
        
        for chunk in chunks {
            let semaphore = Arc::clone(&semaphore);
            let processor = Arc::clone(&inner_processor);
            
            join_set.spawn(async move {
                // Acquire a permit from the semaphore
                let _permit = semaphore.acquire().await.unwrap();
                
                // Process the chunk
                processor.processs(chunk).await
            });
        }
        
        // Collect results
        let mut processed_events = Vec::new();
        
        while let Some(result) = join_set.join_next().await {
            match result {
                Ok(Ok(events)) => processed_events.extend(events),
                Ok(Err(e)) => return Err(e),
                Err(e) => return Err(format!("Task failed: {}", e)),
            }
        }
        
        Ok(processed_events)
    }
} 