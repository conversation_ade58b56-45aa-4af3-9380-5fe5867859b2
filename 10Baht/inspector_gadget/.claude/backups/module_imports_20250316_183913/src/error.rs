use std::io;


/// Error types for the Inspector Gadget library
#[derive(E<PERSON><PERSON>, Debug)]
pub enum Error {
    /// I/O error
    #[error("I/O error: {0}")]
    Io(io::Error),
    
    /// eBPF error
    #[error("eBPF error: {0}")]
    Ebpf(String),
    
    /// Configuration error
    #[error("Configuration error: {0}")]
    Config(String),
    
    /// Elasticsearch error
    #[error("Elasticsearch error: {0}")]
    Elasticsearch(String),
    
    /// Other error
    #[error("{0}")]
    Other(String),
}

/// Result type for the Inspector Gadget library
pub type Result<T> = std::result::Result<T, Error>;

/// Platform-specific errors
#[derive(Debug, Error)]
pub enum PlatformError {
    /// Linux-specific errors
    #[error("Linux error: {0}")]
    Linux(String),

    /// Windows-specific errors
    #[error("Windows error: {0}")]
    Windows(String),
}
