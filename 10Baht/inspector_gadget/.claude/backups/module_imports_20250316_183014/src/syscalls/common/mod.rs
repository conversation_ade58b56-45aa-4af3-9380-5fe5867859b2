/*!
 * Common Syscall Components
 * 
 * This module provides common data structures and utilities for handling
 * system calls across different platforms.
 */

// Re-export submodules
pub mod categories;
pub mod filter;
pub mod mapping;

// Re-export common types
pub use categories::SyscallCategory;
pub use filter::SyscallFilter;
pub use mapping::{
    get_syscall, get_syscall_category, get_syscall_name, get_syscalls_by_category,
    get_syscalls_for_platform, init_syscall_registry, register_syscall,
};

use std::fmt;
use serde::{Serialize, Deserialize};
use crate::platforms::Platform;

/// Represents a system call definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Syscall {
    /// Syscall ID (platform-specific)
    pub id: u32,
    /// Syscall name
    pub name: String,
    /// Platform this syscall belongs to
    pub platform: dyn Platform,
    /// Functional category of the syscall
    pub category: SyscallCategory,
    /// Optional description of the syscall
    pub description: Option<String>,
}

impl Syscall {
    /// Create a new syscall definition
    pub fn new(
        id: u32,
        name: &str,
        platform: dyn Platform,
        category: SyscallCategory,
    ) -> Self {
        Self {
            id,
            name: name.to_string(),
            platform,
            category,
            description: None,
        }
    }
    
    /// Add a description to the syscall
    pub fn with_description(mut self, description: &str) -> Self {
        self.description = Some(description.to_string());
        self
    }
}

/// Direction of a syscall parameter
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ParameterDirection {
    /// Parameter is input only
    In,
    /// Parameter is output only
    Out,
    /// Parameter is both input and output
    InOut,
}

/// Value of a syscall parameter
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SyscallParameterValue {
    /// Integer value
    Integer(i64),
    /// Unsigned integer value
    UnsignedInteger(u64),
    /// String value
    String(String),
    /// Buffer value (represented as hex)
    Buffer(Vec<u8>),
    /// Pointer value
    Pointer(u64),
    /// File descriptor or handle
    Handle(u64),
    /// Boolean value
    Boolean(bool),
    /// Flags (bitfield)
    Flags(u64),
    /// Enumeration value
    Enum(String),
    /// Structure value (serialized as JSON)
    Struct(String),
    /// Array of values
    Array(Vec<SyscallParameterValue>),
    /// Unknown value
    Unknown,
}

impl fmt::Display for SyscallParameterValue {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SyscallParameterValue::Integer(val) => write!(f, "{}", val),
            SyscallParameterValue::UnsignedInteger(val) => write!(f, "{}", val),
            SyscallParameterValue::String(val) => write!(f, "\"{}\"", val),
            SyscallParameterValue::Buffer(val) => {
                if val.len() > 16 {
                    write!(f, "[{} bytes]", val.len())
                } else {
                    write!(f, "{:02X?}", val)
                }
            }
            SyscallParameterValue::Pointer(val) => write!(f, "0x{:X}", val),
            SyscallParameterValue::Handle(val) => write!(f, "handle:{}", val),
            SyscallParameterValue::Boolean(val) => write!(f, "{}", val),
            SyscallParameterValue::Flags(val) => write!(f, "0x{:X}", val),
            SyscallParameterValue::Enum(val) => write!(f, "{}", val),
            SyscallParameterValue::Struct(val) => write!(f, "{}", val),
            SyscallParameterValue::Array(vals) => {
                write!(f, "[")?;
                for (i, val) in vals.iter().enumerate() {
                    if i > 0 {
                        write!(f, ", ")?;
                    }
                    write!(f, "{}", val)?;
                }
                write!(f, "]")
            }
            SyscallParameterValue::Unknown => write!(f, "unknown"),
        }
    }
}

/// Parameter for a system call
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyscallParameter {
    /// Parameter name
    pub name: String,
    /// Parameter value
    pub value: SyscallParameterValue,
    /// Parameter direction
    pub direction: ParameterDirection,
}

impl SyscallParameter {
    /// Create a new syscall parameter
    pub fn new(
        name: &str,
        value: SyscallParameterValue,
        direction: ParameterDirection,
    ) -> Self {
        Self {
            name: name.to_string(),
            value,
            direction,
        }
    }
}

/// Represents a system call event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyscallEvent {
    /// Unique event ID
    pub id: u64,
    /// Timestamp of the event (nanoseconds since epoch)
    pub timestamp: u64,
    /// Process ID that made the syscall
    pub process_id: u32,
    /// Thread ID that made the syscall
    pub thread_id: u32,
    /// Syscall ID (platform-specific)
    pub syscall_id: u32,
    /// Syscall name
    pub syscall_name: String,
    /// Syscall category
    pub category: SyscallCategory,
    /// Parameters passed to the syscall
    pub parameters: Vec<SyscallParameter>,
    /// Return value of the syscall
    pub return_value: i64,
    /// Whether the syscall was successful
    pub success: bool,
    /// Duration of the syscall in nanoseconds
    pub duration_ns: u64,
}

impl SyscallEvent {
    /// Create a new syscall event
    pub fn new(
        id: u64,
        timestamp: u64,
        process_id: u32,
        thread_id: u32,
        syscall_id: u32,
        syscall_name: &str,
        category: SyscallCategory,
    ) -> Self {
        Self {
            id,
            timestamp,
            process_id,
            thread_id,
            syscall_id,
            syscall_name: syscall_name.to_string(),
            category,
            parameters: Vec::new(),
            return_value: 0,
            success: false,
            duration_ns: 0,
        }
    }
    
    /// Add a parameter to the syscall event
    pub fn add_parameter(
        &mut self,
        name: &str,
        value: SyscallParameterValue,
        direction: ParameterDirection,
    ) {
        self.parameters.push(SyscallParameter::new(name, value, direction));
    }
    
    /// Set the return value and success status of the syscall event
    pub fn set_return_value(&mut self, return_value: i64, success: bool) {
        self.return_value = return_value;
        self.success = success;
    }
    
    /// Set the duration of the syscall
    pub fn set_duration(&mut self, duration_ns: u64) {
        self.duration_ns = duration_ns;
    }
    
    /// Get a parameter value by name
    pub fn get_parameter_value(&self, name: &str) -> Option<&SyscallParameterValue> {
        self.parameters
            .iter()
            .find(|p| p.name == name)
            .map(|p| &p.value)
    }
    
    /// Get a string parameter value by name
    pub fn get_string_parameter(&self, name: &str) -> Option<&str> {
        match self.get_parameter_value(name) {
            Some(SyscallParameterValue::String(s)) => Some(s),
            _ => None,
        }
    }
    
    /// Get an integer parameter value by name
    pub fn get_integer_parameter(&self, name: &str) -> Option<i64> {
        match self.get_parameter_value(name) {
            Some(SyscallParameterValue::Integer(i)) => Some(*i),
            Some(SyscallParameterValue::UnsignedInteger(u)) if *u <= i64::MAX as u64 => {
                Some(*u as i64)
            }
            _ => None,
        }
    }
    
    /// Get an unsigned integer parameter value by name
    pub fn get_unsigned_parameter(&self, name: &str) -> Option<u64> {
        match self.get_parameter_value(name) {
            Some(SyscallParameterValue::UnsignedInteger(u)) => Some(*u),
            Some(SyscallParameterValue::Integer(i)) if *i >= 0 => Some(*i as u64),
            Some(SyscallParameterValue::Pointer(p)) => Some(*p),
            Some(SyscallParameterValue::Handle(h)) => Some(*h),
            Some(SyscallParameterValue::Flags(f)) => Some(*f),
            _ => None,
        }
    }
    
    /// Get a boolean parameter value by name
    pub fn get_boolean_parameter(&self, name: &str) -> Option<bool> {
        match self.get_parameter_value(name) {
            Some(SyscallParameterValue::Boolean(b)) => Some(*b),
            Some(SyscallParameterValue::Integer(i)) => Some(*i != 0),
            Some(SyscallParameterValue::UnsignedInteger(u)) => Some(*u != 0),
            _ => None,
        }
    }
    
    /// Get a buffer parameter value by name
    pub fn get_buffer_parameter(&self, name: &str) -> Option<&[u8]> {
        match self.get_parameter_value(name) {
            Some(SyscallParameterValue::Buffer(b)) => Some(b),
            _ => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_syscall_creation() {
        let syscall = Syscall::new(
            1,
            "read",
            Platform::Linux,
            SyscallCategory::FileSystem,
        )
        .with_description("Read from a file descriptor");
        
        assert_eq!(syscall.id, 1);
        assert_eq!(syscall.name, "read");
        assert_eq!(syscall.platform, Platform::Linux);
        assert_eq!(syscall.category, SyscallCategory::FileSystem);
        assert_eq!(
            syscall.description,
            Some("Read from a file descriptor".to_string())
        );
    }
    
    #[test]
    fn test_syscall_event_creation() {
        let mut event = SyscallEvent::new(
            1,
            12345,
            1000,
            1001,
            0,
            "read",
            SyscallCategory::FileSystem,
        );
        
        assert_eq!(event.id, 1);
        assert_eq!(event.timestamp, 12345);
        assert_eq!(event.process_id, 1000);
        assert_eq!(event.thread_id, 1001);
        assert_eq!(event.syscall_id, 0);
        assert_eq!(event.syscall_name, "read");
        assert_eq!(event.category, SyscallCategory::FileSystem);
        assert!(event.parameters.is_empty());
        assert_eq!(event.return_value, 0);
        assert!(!event.success);
        assert_eq!(event.duration_ns, 0);
        
        // Add parameters
        event.add_parameter(
            "fd",
            SyscallParameterValue::Integer(3),
            ParameterDirection::In,
        );
        event.add_parameter(
            "buf",
            SyscallParameterValue::Pointer(0x12345678),
            ParameterDirection::Out,
        );
        event.add_parameter(
            "count",
            SyscallParameterValue::UnsignedInteger(1024),
            ParameterDirection::In,
        );
        
        assert_eq!(event.parameters.len(), 3);
        assert_eq!(event.parameters[0].name, "fd");
        assert_eq!(event.parameters[1].name, "buf");
        assert_eq!(event.parameters[2].name, "count");
        
        // Set return value and duration
        event.set_return_value(1024, true);
        event.set_duration(500);
        
        assert_eq!(event.return_value, 1024);
        assert!(event.success);
        assert_eq!(event.duration_ns, 500);
        
        // Test parameter getters
        assert_eq!(
            event.get_integer_parameter("fd"),
            Some(3)
        );
        assert_eq!(
            event.get_unsigned_parameter("buf"),
            Some(0x12345678)
        );
        assert_eq!(
            event.get_unsigned_parameter("count"),
            Some(1024)
        );
        assert_eq!(
            event.get_integer_parameter("nonexistent"),
            None
        );
    }
    
    #[test]
    fn test_parameter_value_display() {
        assert_eq!(
            format!("{}", SyscallParameterValue::Integer(42)),
            "42"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::UnsignedInteger(42)),
            "42"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::String("hello".to_string())),
            "\"hello\""
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::Buffer(vec![1, 2, 3, 4])),
            "[01, 02, 03, 04]"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::Pointer(0x12345678)),
            "0x12345678"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::Handle(42)),
            "handle:42"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::Boolean(true)),
            "true"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::Flags(0x1234)),
            "0x1234"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::Enum("READ".to_string())),
            "READ"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::Struct("{\"key\":\"value\"}".to_string())),
            "{\"key\":\"value\"}"
        );
        assert_eq!(
            format!(
                "{}",
                SyscallParameterValue::Array(vec![
                    SyscallParameterValue::Integer(1),
                    SyscallParameterValue::Integer(2),
                    SyscallParameterValue::Integer(3)
                ])
            ),
            "[1, 2, 3]"
        );
        assert_eq!(
            format!("{}", SyscallParameterValue::Unknown),
            "unknown"
        );
    }
} 