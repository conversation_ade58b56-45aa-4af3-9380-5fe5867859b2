/*!
 * Event Filter Implementation
 * 
 * This module provides filtering capabilities for events based on
 * configurable rules.
 */


use async_trait::async_trait;

use regex::Regex;

use crate::core::events::{EventSeverity, EventType, TraceEvent};
use crate::error::{<PERSON><PERSON><PERSON>, Result};
use super::EventProcessor;

/// Filter rule
#[derive(Debug, Clone)]
pub enum FilterRule {
    /// Include events matching a pattern
    IncludePattern(String),
    /// Exclude events matching a pattern
    ExcludePattern(String),
    /// Include events with a specific type
    IncludeType(EventType),
    /// Exclude events with a specific type
    ExcludeType(EventType),
    /// Include events with a severity at or above the specified level
    IncludeSeverity(EventSeverity),
    /// Exclude events with a severity at or above the specified level
    ExcludeSeverity(EventSeverity),
    /// Include events from a specific process ID
    IncludePid(u32),
    /// Exclude events from a specific process ID
    ExcludePid(u32),
    /// Include events with a specific tag
    IncludeTag(String),
    /// Exclude events with a specific tag
    ExcludeTag(String),
}

/// Event filter configuration
#[derive(Debug, Clone)]
pub struct EventFilterConfig {
    /// Filter rules
    pub rules: Vec<FilterRule>,
}

impl Default for EventFilterConfig {
    fn default() -> Self {
        Self {
            rules: Vec::new(),
        }
    }
}

/// Event filter
pub struct EventFilter {
    /// Configuration
    config: EventFilterConfig,
    /// Compiled regex patterns
    patterns: Vec<(bool, Regex)>,
    /// Type filters
    types: Vec<(bool, EventType)>,
    /// Severity filters
    severities: Vec<(bool, EventSeverity)>,
    /// Process ID filters
    pids: Vec<(bool, u32)>,
    /// Tag filters
    tags: Vec<(bool, String)>,
}

impl EventFilter {
    /// Create a new event filter
    pub fn new(config: EventFilterConfig) -> Self {
        let mut patterns = Vec::new();
        let mut types = Vec::new();
        let mut severities = Vec::new();
        let mut pids = Vec::new();
        let mut tags = Vec::new();
        
        for rule in &config.rules {
            match rule {
                FilterRule::IncludePattern(pattern) => {
                    if let Ok(regex) = Regex::new(pattern) {
                        patterns.push((true, regex));
                    }
                }
                FilterRule::ExcludePattern(pattern) => {
                    if let Ok(regex) = Regex::new(pattern) {
                        patterns.push((false, regex));
                    }
                }
                FilterRule::IncludeType(event_type) => {
                    types.push((true, event_type.clone()));
                }
                FilterRule::ExcludeType(event_type) => {
                    types.push((false, event_type.clone()));
                }
                FilterRule::IncludeSeverity(severity) => {
                    severities.push((true, *severity));
                }
                FilterRule::ExcludeSeverity(severity) => {
                    severities.push((false, *severity));
                }
                FilterRule::IncludePid(pid) => {
                    pids.push((true, *pid));
                }
                FilterRule::ExcludePid(pid) => {
                    pids.push((false, *pid));
                }
                FilterRule::IncludeTag(tag) => {
                    tags.push((true, tag.clone()));
                }
                FilterRule::ExcludeTag(tag) => {
                    tags.push((false, tag.clone()));
                }
            }
        }
        
        Self {
            config,
            patterns,
            types,
            severities,
            pids,
            tags,
        }
    }
    
    /// Check if an event matches the filter
    fn matches(&self, event: &TraceEvent) -> bool {
        // If there are no rules, accept all events
        if self.config.rules.is_empty() {
            return true;
        }
        
        // Check patterns
        for (include, pattern) in &self.patterns {
            let event_str = format!("{:?}", event);
            let matches = pattern.is_match(&event_str);
            
            if matches && !*include {
                trace!("Event {} excluded by pattern", event.id);
                return false;
            }
        }
        
        // Check types
        for (include, event_type) in &self.types {
            let matches = &event.event_type == event_type;
            
            if matches && !*include {
                trace!("Event {} excluded by type", event.id);
                return false;
            }
        }
        
        // Check severities
        for (include, severity) in &self.severities {
            let matches = event.severity >= *severity;
            
            if matches && !*include {
                trace!("Event {} excluded by severity", event.id);
                return false;
            }
        }
        
        // Check PIDs
        for (include, pid) in &self.pids {
            let matches = event.pid == *pid;
            
            if matches && !*include {
                trace!("Event {} excluded by PID", event.id);
                return false;
            }
        }
        
        // Check tags
        for (include, tag) in &self.tags {
            let matches = event.tags.contains(&tag.clone());
            
            if matches && !*include {
                trace!("Event {} excluded by tag", event.id);
                return false;
            }
        }
        
        // Check if any include rules match
        let mut has_include_rules = false;
        let mut include_matched = false;
        
        // Check patterns
        for (include, pattern) in &self.patterns {
            if *include {
                has_include_rules = true;
                let event_str = format!("{:?}", event);
                if pattern.is_match(&event_str) {
                    include_matched = true;
                    break;
                }
            }
        }
        
        // Check types
        if !include_matched {
            for (include, event_type) in &self.types {
                if *include {
                    has_include_rules = true;
                    if &event.event_type == event_type {
                        include_matched = true;
                        break;
                    }
                }
            }
        }
        
        // Check severities
        if !include_matched {
            for (include, severity) in &self.severities {
                if *include {
                    has_include_rules = true;
                    if event.severity >= *severity {
                        include_matched = true;
                        break;
                    }
                }
            }
        }
        
        // Check PIDs
        if !include_matched {
            for (include, pid) in &self.pids {
                if *include {
                    has_include_rules = true;
                    if event.pid == *pid {
                        include_matched = true;
                        break;
                    }
                }
            }
        }
        
        // Check tags
        if !include_matched {
            for (include, tag) in &self.tags {
                if *include {
                    has_include_rules = true;
                    if event.tags.contains(&tag.clone()) {
                        include_matched = true;
                        break;
                    }
                }
            }
        }
        
        // If there are include rules but none matched, exclude the event
        if has_include_rules && !include_matched {
            trace!("Event {} excluded by lack of include match", event.id);
            return false;
        }
        
        true
    }
}

#[async_trait]
impl EventProcessor for EventFilter {
    async fn process(&self, event: TraceEvent) -> Result<Option<TraceEvent>> {
        if self.matches(&event) {
            debug!("Event {} passed filter", event.id);
            Ok(Some(event))
        } else {
            debug!("Event {} filtered out", event.id);
            Ok(None)
        }
    }
    
    async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
        let mut result = Vec::with_capacity(events.len());
        
        for event in events {
            if self.matches(&event) {
                result.push(event);
            }
        }
        
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::events::{EventData, EventType, TraceEvent};
    
    #[tokio::test]
    async fn test_filter_include_pattern() {
        // Create a filter with an include pattern
        let config = EventFilterConfig {
            rules: vec![
                FilterRule::IncludePattern("test".to_string()),
            ],
        };
        let filter = EventFilter::new(config);
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("other".to_string()),
            EventData::Custom(serde_json::json!({"other": "value"})),
            1,
            1,
            EventSeverity::Info,
            "other",
            vec![],
        );
        
        // Process events
        let result1 = filter.process(event1.clone()).await.unwrap();
        let result2 = filter.process(event2.clone()).await.unwrap();
        
        // Check results
        assert!(result1.is_some());
        assert!(result2.is_none());
    }
    
    #[tokio::test]
    async fn test_filter_exclude_pattern() {
        // Create a filter with an exclude pattern
        let config = EventFilterConfig {
            rules: vec![
                FilterRule::ExcludePattern("test".to_string()),
            ],
        };
        let filter = EventFilter::new(config);
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("other".to_string()),
            EventData::Custom(serde_json::json!({"other": "value"})),
            1,
            1,
            EventSeverity::Info,
            "other",
            vec![],
        );
        
        // Process events
        let result1 = filter.process(event1.clone()).await.unwrap();
        let result2 = filter.process(event2.clone()).await.unwrap();
        
        // Check results
        assert!(result1.is_none());
        assert!(result2.is_some());
    }
    
    #[tokio::test]
    async fn test_filter_severity() {
        // Create a filter with a severity rule
        let config = EventFilterConfig {
            rules: vec![
                FilterRule::IncludeSeverity(EventSeverity::Warning),
            ],
        };
        let filter = EventFilter::new(config);
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Warning,
            "test",
            vec![],
        );
        
        let event3 = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Error,
            "test",
            vec![],
        );
        
        // Process events
        let result1 = filter.process(event1.clone()).await.unwrap();
        let result2 = filter.process(event2.clone()).await.unwrap();
        let result3 = filter.process(event3.clone()).await.unwrap();
        
        // Check results
        assert!(result1.is_none());
        assert!(result2.is_some());
        assert!(result3.is_some());
    }
    
    #[tokio::test]
    async fn test_filter_multiple_rules() {
        // Create a filter with multiple rules
        let config = EventFilterConfig {
            rules: vec![
                FilterRule::IncludePattern("test".to_string()),
                FilterRule::ExcludeSeverity(EventSeverity::Error),
            ],
        };
        let filter = EventFilter::new(config);
        
        // Create test events
        let event1 = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        
        let event2 = TraceEvent::now(
            EventType::Custom("test".to_string()),
            EventData::Custom(serde_json::json!({"test": "value"})),
            1,
            1,
            EventSeverity::Error,
            "test",
            vec![],
        );
        
        let event3 = TraceEvent::now(
            EventType::Custom("other".to_string()),
            EventData::Custom(serde_json::json!({"other": "value"})),
            1,
            1,
            EventSeverity::Info,
            "other",
            vec![],
        );
        
        // Process events
        let result1 = filter.process(event1.clone()).await.unwrap();
        let result2 = filter.process(event2.clone()).await.unwrap();
        let result3 = filter.process(event3.clone()).await.unwrap();
        
        // Check results
        assert!(result1.is_some());
        assert!(result2.is_none());
        assert!(result3.is_none());
    }
} 