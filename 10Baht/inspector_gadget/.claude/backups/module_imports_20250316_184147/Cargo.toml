[package]
name = "inspector_gadget"
version = "0.2.0"
edition = "2021"
description = "A framework for Linux kernel and userspace tracing with eBPF"
authors = ["Inspector Gadget Team"]
license = "MIT"
repository = "https://github.com/inspector-gadget/inspector-gadget"
readme = "README.md"
keywords = ["ebpf", "tracing", "linux", "security", "monitoring"]
categories = ["development-tools", "monitoring-tools", "os::linux-apis"]

[dependencies]
url = "2.5.0"
elasticsearch = "8.5.0-alpha.1"
futures = "0.3.30"
regex = "1.10.2"
# Core dependencies
log = "0.4"
env_logger = "0.10"
thiserror = "1.0"
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"

# Binary parsing
goblin = "0.7"
memmap2 = "0.7"

# Concurrency and lazy initialization
once_cell = "1.18"

# Async runtime
tokio = { version = "1.28", features = ["full"] }
async-trait = "0.1"

# Concurrency
parking_lot = "0.12"
num_cpus = "1.16"

# HTTP client for Elasticsearch
reqwest = { version = "0.11", features = ["json"] }

# Date and time handling
chrono = "0.4"

# Unique identifiers
uuid = { version = "1.3", features = ["v4"] }

# System information
sys-info = "0.9"

# eBPF dependencies
libbpf-rs = { version = "0.20", optional = true }
libc = "0.2"

[features]
default = ["linux"]
linux = ["libbpf-rs"]

[dev-dependencies]
criterion = "0.5"
tempfile = "3.8"
mockall = "0.11"
test-log = "0.2"

[[bench]]
name = "percpumap_bench"
harness = false

[[bench]]
name = "hooker_percpu_bench"
harness = false

[[bin]]
name = "inspector_gadget"
path = "src/main.rs"

[[bin]]
name = "lrumap_pill_test"
path = "tests/lrumap_pill_test.rs"

[[bin]]
name = "percpumap_test"
path = "hooker_pill/src/percpumap_test.rs"

[[bin]]
name = "lsm_percpu_hooker_test"
path = "tests/lsm_percpu_hooker_test.rs"

[[bin]]
name = "xdp_percpu_hooker_test"
path = "tests/xdp_percpu_hooker_test.rs"

[[bin]]
name = "tracepoint_percpu_hooker_test"
path = "tests/tracepoint_percpu_hooker_test.rs"

[[bin]]
name = "uprobe_percpu_hooker_test"
path = "tests/uprobe_percpu_hooker_test.rs"

[[example]]
name = "percpumap_example"
path = "examples/percpumap_example.rs"
required-features = ["linux"]

[[example]]
name = "xdp_percpu_example"
path = "examples/xdp_percpu_example.rs"
required-features = ["linux"]

[[example]]
name = "tracepoint_percpu_example"
path = "examples/tracepoint_percpu_example.rs"
required-features = ["linux"]

[[example]]
name = "uprobe_percpu_example"
path = "examples/uprobe_percpu_example.rs"
required-features = ["linux"]

[[example]]
name = "lrumap_logging_example"
required-features = ["linux"]

[[example]]
name = "percpu_elasticsearch_example"
path = "examples/percpu_elasticsearch_example.rs"
required-features = ["linux"]

[lib]
name = "inspector_gadget"
path = "src/lib.rs"

[workspace]
members = [
    ".", "test_app_rust", "hooker_pill",
] 
