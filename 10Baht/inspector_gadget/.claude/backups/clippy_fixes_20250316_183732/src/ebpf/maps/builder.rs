/*!
 * eBPF Map Builder
 * 
 * This module provides a builder pattern for creating eBPF maps.
 */

use super::{Map, MapConfig, MapType, MapError};

/// eBPF map builder
#[derive(Debug, Clone)]
pub struct MapBuilder {
    /// Map type
    map_type: MapType,
    /// Map name
    name: Option<String>,
    /// Key size in bytes
    key_size: Option<usize>,
    /// Value size in bytes
    value_size: Option<usize>,
    /// Maximum number of entries
    max_entries: Option<usize>,
    /// Map flags
    flags: u32,
}

impl MapBuilder {
    /// Create a new map builder
    pub fn new(map_type: MapType) -> Self {
        Self {
            map_type,
            name: None,
            key_size: None,
            value_size: None,
            max_entries: None,
            flags: 0,
        }
    }
    
    /// Set the map name
    pub fn name(mut self, name: impl Into<String>) -> Self {
        self.name = Some(name.into());
        self
    }
    
    /// Set the key size
    pub fn key_size(mut self, key_size: usize) -> Self {
        self.key_size = Some(key_size);
        self
    }
    
    /// Set the value size
    pub fn value_size(mut self, value_size: usize) -> Self {
        self.value_size = Some(value_size);
        self
    }
    
    /// Set the maximum number of entries
    pub fn max_entries(mut self, max_entries: usize) -> Self {
        self.max_entries = Some(max_entries);
        self
    }
    
    /// Set the map flags
    pub fn flags(mut self, flags: u32) -> Self {
        self.flags = flags;
        self
    }
    
    /// Build the map
    pub fn build(self) -> Result<Map, MapError> {
        // Determine key size
        let key_size = match self.key_size {
            Some(size) => size,
            None => {
                if self.map_type.supports_zero_length_key() {
                    0
                } else if self.map_type == MapType::Array || self.map_type == MapType::PerCpuArray {
                    4 // Array maps use 4-byte keys (u32 indices)
                } else {
                    return Err(MapError::InvalidArgument(
                        "Key size must be specified for this map type".to_string(),
                    ));
                }
            }
        };
        
        // Determine value size
        let value_size = match self.value_size {
            Some(size) => size,
            None => {
                if self.map_type.supports_zero_length_value() {
                    0
                } else {
                    return Err(MapError::InvalidArgument(
                        "Value size must be specified for this map type".to_string(),
                    ));
                }
            }
        };
        
        // Determine max entries
        let max_entries = match self.max_entries {
            Some(entries) => entries,
            None => self.map_type.default_max_entries(),
        };
        
        // Create map configuration
        let config = MapConfig::new(
            self.map_type,
            key_size,
            value_size,
            max_entries,
            self.flags,
        )?;
        
        // Create the map
        let mut map = Map::create(config)?;
        
        // Set the map name if provided
        if let Some(name) = self.name {
            // In a real implementation, we would set the map name here
            // For now, we'll just store it in the Map struct
            map.name = name;
        }
        
        Ok(map)
    }
}

// Convenience functions for creating common map types
impl MapBuilder {
    /// Create a new hash map builder
    pub fn hash() -> Self {
        Self::new(MapType::Hash)
    }
    
    /// Create a new array map builder
    pub fn array() -> Self {
        Self::new(MapType::Array)
    }
    
    /// Create a new per-CPU hash map builder
    pub fn percpu_hash() -> Self {
        Self::new(MapType::PerCpuHash)
    }
    
    /// Create a new per-CPU array map builder
    pub fn percpu_array() -> Self {
        Self::new(MapType::PerCpuArray)
    }
    
    /// Create a new LRU hash map builder
    pub fn lru_hash() -> Self {
        Self::new(MapType::LruHash)
    }
    
    /// Create a new per-CPU LRU hash map builder
    pub fn lru_percpu_hash() -> Self {
        Self::new(MapType::LruPerCpuHash)
    }
    
    /// Create a new ring buffer map builder
    pub fn ringbuf() -> Self {
        Self::new(MapType::RingBuf)
    }
    
    /// Create a new stack map builder
    pub fn stack() -> Self {
        Self::new(MapType::Stack)
    }
    
    /// Create a new queue map builder
    pub fn queue() -> Self {
        Self::new(MapType::Queue)
    }
} 