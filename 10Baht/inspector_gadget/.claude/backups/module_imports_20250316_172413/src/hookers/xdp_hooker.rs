use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use std::sync::Arc;
use std::time::{Duration, Instant};

use serde::{Serialize, Deserialize};
use parking_lot::RwLock;
use log::{debug, error, info, warn};

use crate::ebpf::maps::percpumap::{TypedOptimizedPerCpuMap, PerCpuMapStats};
use crate::ebpf::maps::MapError;
use crate::elasticsearch::ElasticsearchLogger;
use crate::hookers::{HookerError, HookerStats};

/// Connection tuple
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct ConnectionTuple {
    /// Source IP
    pub src_ip: IpAddr,
    /// Destination IP
    pub dst_ip: IpAddr,
    /// Source port
    pub src_port: u16,
    /// Destination port
    pub dst_port: u16,
    /// Protocol
    pub protocol: u8,
}

impl ConnectionTuple {
    /// Create a new connection tuple
    pub fn new(src_ip: IpAddr, dst_ip: IpAddr, src_port: u16, dst_port: u16, protocol: u8) -> Self {
        Self {
            src_ip,
            dst_ip,
            src_port,
            dst_port,
            protocol,
        }
    }
    
    /// Create a reverse connection tuple
    pub fn reverse(&self) -> Self {
        Self {
            src_ip: self.dst_ip,
            dst_ip: self.src_ip,
            src_port: self.dst_port,
            dst_port: self.src_port,
            protocol: self.protocol,
        }
    }
}

/// Connection state
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ConnectionState {
    /// New connection
    New,
    /// Established connection
    Established,
    /// Closing connection
    Closing,
    /// Closed connection
    Closed,
}

/// Connection statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionStats {
    /// Connection tuple
    pub tuple: ConnectionTuple,
    /// Connection state
    pub state: ConnectionState,
    /// Packets sent
    pub packets_sent: u64,
    /// Packets received
    pub packets_received: u64,
    /// Bytes sent
    pub bytes_sent: u64,
    /// Bytes received
    pub bytes_received: u64,
    /// Creation time
    pub creation_time: u64,
    /// Last update time
    pub last_update_time: u64,
}

/// Bandwidth usage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BandwidthUsage {
    /// Total bytes sent
    pub total_bytes_sent: u64,
    /// Total bytes received
    pub total_bytes_received: u64,
    /// Last update time
    pub last_update_time: u64,
}

/// XDP Hooker statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct XdpHookerStats {
    /// Connection statistics map statistics
    pub connection_stats_stats: PerCpuMapStats,
    /// Bandwidth usage map statistics
    pub bandwidth_usage_stats: PerCpuMapStats,
    /// Number of packets processed
    pub packets_processed: usize,
    /// Number of packets dropped
    pub packets_dropped: usize,
    /// Number of connections tracked
    pub connections_tracked: usize,
    /// Number of connections expired
    pub connections_expired: usize,
    /// Start time
    pub start_time: Instant,
    /// Last update time
    pub last_update_time: Instant,
}

impl Default for XdpHookerStats {
    fn default() -> Self {
        Self {
            connection_stats_stats: PerCpuMapStats::default(),
            bandwidth_usage_stats: PerCpuMapStats::default(),
            packets_processed: 0,
            packets_dropped: 0,
            connections_tracked: 0,
            connections_expired: 0,
            start_time: Instant::now(),
            last_update_time: Instant::now(),
        }
    }
}

impl HookerStats for XdpHookerStats {
    fn uptime(&self) -> Duration {
        self.start_time.elapsed()
    }
    
    fn last_update(&self) -> Duration {
        self.last_update_time.elapsed()
    }
}

/// XDP Hooker
pub struct XdpHooker {
    /// Connection statistics map
    connection_stats_map: TypedOptimizedPerCpuMap<u32, ConnectionStats>,
    /// Bandwidth usage map
    bandwidth_usage_map: TypedOptimizedPerCpuMap<u32, BandwidthUsage>,
    /// Statistics
    stats: Arc<RwLock<XdpHookerStats>>,
    /// Elasticsearch logger
    elasticsearch_logger: Option<Arc<ElasticsearchLogger>>,
}

impl XdpHooker {
    /// Create a new XDP Hooker
    pub fn new(elasticsearch_logger: Option<Arc<ElasticsearchLogger>>) -> Result<Self, HookerError> {
        // Create maps
        let connection_stats_map = TypedOptimizedPerCpuMap::<u32, ConnectionStats>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        let bandwidth_usage_map = TypedOptimizedPerCpuMap::<u32, BandwidthUsage>::create(1024)
            .map_err(|e| HookerError::MapCreationError(e.to_string()))?;
        
        // Create statistics
        let stats = Arc::new(RwLock::new(XdpHookerStats::default()));
        
        Ok(Self {
            connection_stats_map,
            bandwidth_usage_map,
            stats,
            elasticsearch_logger,
        })
    }
    
    /// Hash a connection tuple to a 32-bit key
    fn hash_connection_tuple(&self, tuple: &ConnectionTuple) -> u32 {
        // Simple hash function
        let mut hash = 0u32;
        
        // Hash source IP
        match tuple.src_ip {
            IpAddr::V4(ip) => {
                let octets = ip.octets();
                hash ^= u32::from_ne_bytes([octets[0], octets[1], octets[2], octets[3]]);
            }
            IpAddr::V6(ip) => {
                let segments = ip.segments();
                for segment in segments.iter() {
                    hash ^= *segment as u32;
                }
            }
        }
        
        // Hash destination IP
        match tuple.dst_ip {
            IpAddr::V4(ip) => {
                let octets = ip.octets();
                hash ^= u32::from_ne_bytes([octets[0], octets[1], octets[2], octets[3]]);
            }
            IpAddr::V6(ip) => {
                let segments = ip.segments();
                for segment in segments.iter() {
                    hash ^= *segment as u32;
                }
            }
        }
        
        // Hash ports and protocol
        hash ^= (tuple.src_port as u32) << 16;
        hash ^= tuple.dst_port as u32;
        hash ^= (tuple.protocol as u32) << 24;
        
        hash
    }
    
    /// Get CPU ID for a connection tuple
    fn get_cpu_for_connection(&self, tuple: &ConnectionTuple) -> usize {
        // Hash the connection tuple and use it to select a CPU
        (self.hash_connection_tuple(tuple) as usize) % self.connection_stats_map.num_cpus()
    }
    
    /// Get connection statistics
    pub fn get_connection_stats(&self, tuple: &ConnectionTuple) -> Result<Option<ConnectionStats>, HookerError> {
        // Hash the connection tuple
        let key = self.hash_connection_tuple(tuple);
        
        // Get CPU ID
        let cpu = self.get_cpu_for_connection(tuple);
        
        // Lookup connection statistics
        self.connection_stats_map.lookup_cpu(&key, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))
    }
    
    /// Update connection statistics
    pub fn update_connection_stats(&self, stats: ConnectionStats) -> Result<(), HookerError> {
        // Hash the connection tuple
        let key = self.hash_connection_tuple(&stats.tuple);
        
        // Get CPU ID
        let cpu = self.get_cpu_for_connection(&stats.tuple);
        
        // Update connection statistics
        self.connection_stats_map.update_cpu(&key, &stats, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update statistics
        let mut stats_lock = self.stats.write();
        stats_lock.connections_tracked += 1;
        stats_lock.last_update_time = Instant::now();
        
        Ok(())
    }
    
    /// Update bandwidth usage
    pub fn update_bandwidth_usage(&self, interface_index: u32, bytes_sent: u64, bytes_received: u64) -> Result<(), HookerError> {
        // Get CPU ID
        let cpu = (interface_index as usize) % self.bandwidth_usage_map.num_cpus();
        
        // Get current bandwidth usage
        let mut usage = match self.bandwidth_usage_map.lookup_cpu(&interface_index, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
            Some(u) => u,
            None => BandwidthUsage {
                total_bytes_sent: 0,
                total_bytes_received: 0,
                last_update_time: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs(),
            },
        };
        
        // Update bandwidth usage
        usage.total_bytes_sent += bytes_sent;
        usage.total_bytes_received += bytes_received;
        usage.last_update_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Update bandwidth usage map
        self.bandwidth_usage_map.update_cpu(&interface_index, &usage, cpu)
            .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
        
        // Update statistics
        let mut stats_lock = self.stats.write();
        stats_lock.last_update_time = Instant::now();
        
        Ok(())
    }
    
    /// Clean up inactive connections
    pub fn cleanup_inactive_connections(&self, timeout_seconds: u64) -> Result<usize, HookerError> {
        let mut cleaned_up = 0;
        
        // Get current time
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // This is a simplified implementation
        // In a real implementation, we would need to scan all maps
        // and remove inactive connections
        
        // For now, we'll just simulate cleaning up some connections
        for cpu in 0..self.connection_stats_map.num_cpus() {
            // Simulate cleaning up one connection per CPU
            let key = cpu as u32;
            
            // Check if the connection exists
            if let Some(stats) = self.connection_stats_map.lookup_cpu(&key, cpu)
                .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
                
                // Check if the connection is inactive
                if current_time - stats.last_update_time > timeout_seconds {
                    // Delete the connection
                    self.connection_stats_map.delete_cpu(&key, cpu)
                        .map_err(|e| HookerError::MapOperationError(e.to_string()))?;
                    
                    cleaned_up += 1;
                }
            }
        }
        
        // Update statistics
        let mut stats_lock = self.stats.write();
        stats_lock.connections_expired += cleaned_up;
        stats_lock.last_update_time = Instant::now();
        
        Ok(cleaned_up)
    }
    
    /// Export connection statistics to Elasticsearch
    pub fn export_connections_to_elasticsearch(&self) -> Result<usize, HookerError> {
        // Check if Elasticsearch logger is available
        let logger = match &self.elasticsearch_logger {
            Some(l) => l,
            None => return Err(HookerError::OperationError("Elasticsearch logger not configured".to_string())),
        };
        
        let mut exported_count = 0;
        
        // Export connections for all CPUs
        for cpu in 0..self.connection_stats_map.num_cpus() {
            // This is a simplified implementation
            // In a real implementation, we would need to scan the map for this CPU
            // and export all connections
            
            // For now, we'll just simulate exporting some connections
            let key = cpu as u32;
            
            // Check if the connection exists
            if let Some(stats) = self.connection_stats_map.lookup_cpu(&key, cpu)
                .map_err(|e| HookerError::MapOperationError(e.to_string()))? {
                
                // Export the connection
                if let Err(e) = logger.log_event("connection_stats", &stats) {
                    error!("Failed to export connection statistics to Elasticsearch: {}", e);
                } else {
                    exported_count += 1;
                }
            }
        }
        
        Ok(exported_count)
    }
    
    /// Get statistics for all maps
    pub fn get_stats(&self) -> XdpHookerStats {
        let mut stats = self.stats.write();
        
        // Update map statistics
        stats.connection_stats_stats = self.connection_stats_map.stats().clone();
        stats.bandwidth_usage_stats = self.bandwidth_usage_map.stats().clone();
        
        stats.clone()
    }
    
    /// Handle packet
    pub fn handle_packet(&self, is_ingress: bool, interface_index: u32, tuple: ConnectionTuple, packet_size: u64) -> Result<bool, HookerError> {
        // Get current time
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Get connection statistics
        let mut stats = match self.get_connection_stats(&tuple)? {
            Some(s) => s,
            None => {
                // Create new connection statistics
                ConnectionStats {
                    tuple: tuple.clone(),
                    state: ConnectionState::New,
                    packets_sent: 0,
                    packets_received: 0,
                    bytes_sent: 0,
                    bytes_received: 0,
                    creation_time: current_time,
                    last_update_time: current_time,
                }
            }
        };
        
        // Update connection statistics
        if is_ingress {
            stats.packets_received += 1;
            stats.bytes_received += packet_size;
        } else {
            stats.packets_sent += 1;
            stats.bytes_sent += packet_size;
        }
        
        // Update connection state
        if stats.state == ConnectionState::New && (stats.packets_sent > 0 && stats.packets_received > 0) {
            stats.state = ConnectionState::Established;
        }
        
        stats.last_update_time = current_time;
        
        // Update connection statistics
        self.update_connection_stats(stats)?;
        
        // Update bandwidth usage
        if is_ingress {
            self.update_bandwidth_usage(interface_index, 0, packet_size)?;
        } else {
            self.update_bandwidth_usage(interface_index, packet_size, 0)?;
        }
        
        // Update statistics
        let mut stats_lock = self.stats.write();
        stats_lock.packets_processed += 1;
        stats_lock.last_update_time = Instant::now();
        
        // Check if the packet should be dropped
        // This is a simplified implementation
        // In a real implementation, we would check against firewall rules
        
        // For now, we'll just allow all packets
        Ok(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_xdp_hooker_basic() {
        // Create Elasticsearch logger
        let elasticsearch_logger = ElasticsearchLogger::new("http://localhost:9200", "test_index")
            .ok()
            .map(Arc::new);
        
        // Create XDP Hooker
        let hooker = XdpHooker::new(elasticsearch_logger).unwrap();
        
        // Create connection tuple
        let tuple = ConnectionTuple::new(
            IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)),
            IpAddr::V4(Ipv4Addr::new(8, 8, 8, 8)),
            12345,
            443,
            6, // TCP
        );
        
        // Test outgoing packet
        let allowed = hooker.handle_packet(false, 1, tuple.clone(), 1024).unwrap();
        assert!(allowed);
        
        // Test incoming packet
        let allowed = hooker.handle_packet(true, 1, tuple.reverse(), 512).unwrap();
        assert!(allowed);
        
        // Test connection statistics
        let stats = hooker.get_connection_stats(&tuple).unwrap().unwrap();
        assert_eq!(stats.tuple, tuple);
        assert_eq!(stats.state, ConnectionState::Established);
        assert_eq!(stats.packets_sent, 1);
        assert_eq!(stats.packets_received, 1);
        assert_eq!(stats.bytes_sent, 1024);
        assert_eq!(stats.bytes_received, 512);
        
        // Test cleanup
        let cleaned_up = hooker.cleanup_inactive_connections(3600).unwrap();
        assert_eq!(cleaned_up, 0); // No inactive connections yet
        
        // Test statistics
        let stats = hooker.get_stats();
        assert_eq!(stats.packets_processed, 2);
        assert_eq!(stats.connections_tracked, 2);
        assert_eq!(stats.connections_expired, 0);
    }
} 
/// Xdp event data
#[derive(Debug, Clone)]
pub struct XdpEvent {
    /// Event ID
    pub id: u64,
    /// Timestamp
    pub timestamp: u64,
    /// Event data
    pub data: Vec<u8>,
}

/// Configuration for XdpHooker
#[derive(Debug, Clone)]
pub struct XdpHookerConfig {
    /// Name of the hooker
    pub name: String,
    /// Buffer size
    pub buffer_size: usize,
    /// Maximum events
    pub max_events: usize,
}

impl Default for XdpHookerConfig {
    fn default() -> Self {
        Self {
            name: "xdp_hooker".to_string(),
            buffer_size: 4096,
            max_events: 1000,
        }
    }
}
