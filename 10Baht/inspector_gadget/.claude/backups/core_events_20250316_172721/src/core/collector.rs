//! Event Collector for the event collection system.
//!
//! This module provides the main collector component that integrates
//! the buffer, processor, and exporter components.

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tokio::time;
use log::{debug, error, info, warn};
use num_cpus;

use crate::core::buffer::{EventBuffer, BufferStats};
use crate::core::processor::{EventProcessor, ParallelProcessor, ParallelProcessorConfig, ThreadPoolConfig};
use crate::core::exporter::EventExporter;
use crate::core::events::Event;

/// Configuration for the event collector.
#[derive(Debug, Clone)]
pub struct EventCollectorConfig {
    /// Maximum number of events to process in a batch
    pub batch_size: usize,
    /// Interval between processing batches (in milliseconds)
    pub batch_interval_ms: u64,
    /// Maximum time to wait for events from the buffer (in milliseconds)
    pub buffer_timeout_ms: u64,
    /// Whether to start the collector automatically
    pub auto_start: bool,
    /// Whether to use parallel processing
    pub use_parallel_processing: bool,
    /// Maximum number of concurrent processing tasks
    pub max_concurrent_tasks: usize,
}

impl Default for EventCollectorConfig {
    fn default() -> Self {
        Self {
            batch_size: 100,
            batch_interval_ms: 1000,
            buffer_timeout_ms: 100,
            auto_start: true,
            use_parallel_processing: true,
            max_concurrent_tasks: num_cpus::get() * 2,
        }
    }
}

/// Status of the event collector.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CollectorStatus {
    /// The collector is stopped
    Stopped,
    /// The collector is running
    Running,
    /// The collector is paused
    Paused,
}

/// Statistics for the event collector.
#[derive(Debug, Clone, Default)]
pub struct CollectorStats {
    /// Number of events collected
    pub collected_count: usize,
    /// Number of events processed
    pub processed_count: usize,
    /// Number of events exported
    pub exported_count: usize,
    /// Number of events dropped during processing
    pub dropped_count: usize,
    /// Number of processing errors
    pub processing_errors: usize,
    /// Number of export errors
    pub export_errors: usize,
    /// Time spent processing events (in milliseconds)
    pub processing_time_ms: u64,
    /// Time spent exporting events (in milliseconds)
    pub export_time_ms: u64,
    /// Buffer statistics
    pub buffer_stats: Option<BufferStats>,
    /// Time when the collector was started
    pub start_time: Option<std::time::SystemTime>,
    /// Time when the collector was last active
    pub last_active_time: Option<std::time::SystemTime>,
}

/// Event collector for the event collection system.
pub struct EventCollector {
    /// Configuration for the collector
    config: EventCollectorConfig,
    /// Buffer for storing events
    buffer: Arc<dyn EventBuffer>,
    /// Processor for processing events
    processor: Arc<dyn EventProcessor>,
    /// Parallel processor for parallel processing
    parallel_processor: Option<Arc<ParallelProcessor>>,
    /// Exporter for exporting events
    exporter: Arc<dyn EventExporter>,
    /// Status of the collector
    status: Arc<Mutex<CollectorStatus>>,
    /// Statistics for the collector
    stats: Arc<Mutex<CollectorStats>>,
    /// Background task handle
    task_handle: Arc<Mutex<Option<JoinHandle<()>>>>,
}

impl EventCollector {
    /// Create a new event collector.
    ///
    /// # Arguments
    ///
    /// * `config` - The configuration for the collector
    /// * `buffer` - The buffer for storing events
    /// * `processor` - The processor for processing events
    /// * `exporter` - The exporter for exporting events
    ///
    /// # Returns
    ///
    /// A new event collector
    pub fn new(
        config: EventCollectorConfig,
        buffer: Arc<dyn EventBuffer>,
        processor: Arc<dyn EventProcessor>,
        exporter: Arc<dyn EventExporter>,
    ) -> Self {
        // Create parallel processor if enabled
        let parallel_processor = if config.use_parallel_processing {
            let parallel_config = ParallelProcessorConfig {
                thread_pool: ThreadPoolConfig {
                    worker_threads: num_cpus::get(),
                    max_concurrent_tasks: config.max_concurrent_tasks,
                },
                inner_processor: Arc::clone(&processor),
                batch_size: config.batch_size / 4, // Smaller batches for parallel processing
            };
            Some(Arc::new(ParallelProcessor::new(parallel_config)))
        } else {
            None
        };
        
        let collector = Self {
            config,
            buffer,
            processor,
            parallel_processor,
            exporter,
            status: Arc::new(Mutex::new(CollectorStatus::Stopped)),
            stats: Arc::new(Mutex::new(CollectorStats::default())),
            task_handle: Arc::new(Mutex::new(None)),
        };

        if collector.config.auto_start {
            tokio::spawn(collector.start());
        }

        collector
    }

    /// Start the collector.
    ///
    /// This starts a background task that periodically processes
    /// events from the buffer and exports them.
    pub async fn start(&self) -> Result<(), String> {
        let mut status = self.status.lock().await;
        if *status == CollectorStatus::Running {
            return Ok(());
        }

        *status = CollectorStatus::Running;
        drop(status);

        // Update stats
        let mut stats = self.stats.lock().await;
        stats.start_time = Some(std::time::SystemTime::now());
        stats.last_active_time = Some(std::time::SystemTime::now());
        drop(stats);

        // Start the background task
        let config = self.config.clone();
        let buffer = Arc::clone(&self.buffer);
        let processor = Arc::clone(&self.processor);
        let exporter = Arc::clone(&self.exporter);
        let status = Arc::clone(&self.status);
        let stats = Arc::clone(&self.stats);

        let handle = tokio::spawn(async move {
            info!("Event collector started");
            
            let mut interval = time::interval(Duration::from_millis(config.batch_interval_ms));
            
            loop {
                interval.tick().await;
                
                // Check if we should stop
                let current_status = *status.lock().await;
                if current_status == CollectorStatus::Stopped {
                    break;
                }
                
                // Skip processing if paused
                if current_status == CollectorStatus::Paused {
                    continue;
                }
                
                // Update last active time
                {
                    let mut stats_guard = stats.lock().await;
                    stats_guard.last_active_time = Some(std::time::SystemTime::now());
                    
                    // Update buffer stats
                    stats_guard.buffer_stats = Some(buffer.stats());
                }
                
                // Get events from buffer
                let buffer_timeout = Duration::from_millis(config.buffer_timeout_ms);
                let events = buffer.next_batch(config.batch_size, buffer_timeout);
                
                if events.is_empty() {
                    continue;
                }
                
                debug!("Processing batch of {} events", events.len());
                
                // Process events
                let processing_start = Instant::now();
                let processed_events = if let Some(parallel_processor) = &self.parallel_processor {
                    match parallel_processor.process_events(events).await {
                        Ok(processed) => {
                            let mut stats_guard = stats.lock().await;
                            stats_guard.collected_count += events.len();
                            stats_guard.processed_count += processed.len();
                            stats_guard.dropped_count += events.len() - processed.len();
                            processed
                        },
                        Err(e) => {
                            error!("Error processing events: {}", e);
                            let mut stats_guard = stats.lock().await;
                            stats_guard.processing_errors += 1;
                            continue;
                        }
                    }
                } else {
                    match processor.process_events(events).await {
                        Ok(processed) => {
                            let mut stats_guard = stats.lock().await;
                            stats_guard.collected_count += events.len();
                            stats_guard.processed_count += processed.len();
                            stats_guard.dropped_count += events.len() - processed.len();
                            processed
                        },
                        Err(e) => {
                            error!("Error processing events: {}", e);
                            let mut stats_guard = stats.lock().await;
                            stats_guard.processing_errors += 1;
                            continue;
                        }
                    }
                };
                
                let processing_time = processing_start.elapsed();
                
                // Export events
                if !processed_events.is_empty() {
                    let export_start = Instant::now();
                    match exporter.export_events(processed_events).await {
                        Ok(_) => {
                            let mut stats_guard = stats.lock().await;
                            stats_guard.exported_count += processed_events.len();
                            stats_guard.processing_time_ms += processing_time.as_millis() as u64;
                            stats_guard.export_time_ms += export_start.elapsed().as_millis() as u64;
                        },
                        Err(e) => {
                            error!("Error exporting events: {}", e);
                            let mut stats_guard = stats.lock().await;
                            stats_guard.export_errors += 1;
                            stats_guard.processing_time_ms += processing_time.as_millis() as u64;
                        }
                    }
                }
            }
            
            info!("Event collector stopped");
        });

        // Store the task handle
        let mut task_handle = self.task_handle.lock().await;
        *task_handle = Some(handle);

        Ok(())
    }

    /// Stop the collector.
    ///
    /// This stops the background task and flushes any remaining events.
    pub async fn stop(&self) -> Result<(), String> {
        let mut status = self.status.lock().await;
        if *status == CollectorStatus::Stopped {
            return Ok(());
        }

        *status = CollectorStatus::Stopped;
        drop(status);

        // Wait for the background task to finish
        let mut task_handle = self.task_handle.lock().await;
        if let Some(handle) = task_handle.take() {
            // Give the task a chance to finish gracefully
            tokio::time::sleep(Duration::from_millis(100)).await;
            
            // Abort the task if it's still running
            handle.abort();
        }

        // Flush any remaining events
        self.flush().await?;

        Ok(())
    }

    /// Pause the collector.
    ///
    /// This pauses the background task but keeps it running.
    pub async fn pause(&self) -> Result<(), String> {
        let mut status = self.status.lock().await;
        if *status != CollectorStatus::Running {
            return Ok(());
        }

        *status = CollectorStatus::Paused;
        info!("Event collector paused");
        Ok(())
    }

    /// Resume the collector.
    ///
    /// This resumes the background task if it was paused.
    pub async fn resume(&self) -> Result<(), String> {
        let mut status = self.status.lock().await;
        if *status != CollectorStatus::Paused {
            return Ok(());
        }

        *status = CollectorStatus::Running;
        info!("Event collector resumed");
        Ok(())
    }

    /// Collect an event.
    ///
    /// This adds an event to the buffer for processing.
    ///
    /// # Arguments
    ///
    /// * `event` - The event to collect
    ///
    /// # Returns
    ///
    /// `true` if the event was added to the buffer, `false` otherwise
    pub fn collect(&self, event: Event) -> bool {
        self.buffer.add(event)
    }

    /// Collect multiple events.
    ///
    /// This adds multiple events to the buffer for processing.
    ///
    /// # Arguments
    ///
    /// * `events` - The events to collect
    ///
    /// # Returns
    ///
    /// The number of events that were added to the buffer
    pub fn collect_batch(&self, events: Vec<Event>) -> usize {
        self.buffer.add_batch(events)
    }

    /// Flush any buffered events.
    ///
    /// This processes and exports any events in the buffer.
    pub async fn flush(&self) -> Result<(), String> {
        // Get all events from the buffer
        let buffer_timeout = Duration::from_millis(self.config.buffer_timeout_ms);
        let events = self.buffer.next_batch(usize::MAX, buffer_timeout);
        
        if events.is_empty() {
            return Ok(());
        }
        
        debug!("Flushing {} events", events.len());
        
        // Process events
        let processed_events = if let Some(parallel_processor) = &self.parallel_processor {
            parallel_processor.process_events(events).await?
        } else {
            self.processor.process_events(events).await?
        };
        
        // Export events
        if !processed_events.is_empty() {
            self.exporter.export_events(processed_events).await?;
        }
        
        // Flush the exporter
        self.exporter.flush().await?;
        
        Ok(())
    }

    /// Get the current status of the collector.
    pub async fn status(&self) -> CollectorStatus {
        *self.status.lock().await
    }

    /// Get statistics for the collector.
    pub async fn stats(&self) -> CollectorStats {
        let mut stats = self.stats.lock().await.clone();
        stats.buffer_stats = Some(self.buffer.stats());
        stats
    }

    /// Clear the buffer and reset statistics.
    pub async fn clear(&self) -> Result<(), String> {
        // Clear the buffer
        self.buffer.clear();
        
        // Reset statistics
        let mut stats = self.stats.lock().await;
        *stats = CollectorStats::default();
        stats.start_time = Some(std::time::SystemTime::now());
        stats.last_active_time = Some(std::time::SystemTime::now());
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::buffer::{CircularBuffer, CircularBufferConfig, OverflowBehavior};
    use crate::core::processor::{PipelineProcessor, PipelineProcessorConfig};
    use crate::core::exporter::{FileExporter, FileExporterConfig, FileFormat};
    use crate::core::events::{Event, EventId, EventType, Severity};
    use serde_json::json;
    use std::time::{SystemTime, UNIX_EPOCH};
    use tempfile::tempdir;

    fn create_test_event() -> Event {
        Event {
            id: EventId::new(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            event_type: EventType::Syscall,
            severity: Severity::Info,
            source: "test".to_string(),
            process_id: Some(1234),
            thread_id: Some(5678),
            syscall_number: Some(1),
            syscall_name: Some("write".to_string()),
            parameters: Some(json!({"fd": 1, "buf": "Hello", "count": 5})),
            return_value: Some(5),
            duration_ns: Some(1000),
            tags: vec!["test".to_string()],
            metadata: None,
        }
    }

    #[tokio::test]
    async fn test_collector_lifecycle() {
        // Create components
        let buffer_config = CircularBufferConfig {
            capacity: 100,
            overflow_behavior: OverflowBehavior::DropOldest,
        };
        let buffer = Arc::new(CircularBuffer::new(buffer_config));

        let processor_config = PipelineProcessorConfig::default();
        let processor = Arc::new(PipelineProcessor::new(processor_config));

        let dir = tempdir().unwrap();
        let path = dir.path().join("events.log");
        let exporter_config = FileExporterConfig {
            path,
            format: FileFormat::Json,
            flush_interval_ms: 0, // Flush immediately
            ..Default::default()
        };
        let exporter = Arc::new(FileExporter::new(exporter_config).unwrap());

        // Create collector
        let collector_config = EventCollectorConfig {
            auto_start: false, // Don't start automatically
            ..Default::default()
        };
        let collector = EventCollector::new(
            collector_config,
            buffer,
            processor,
            exporter,
        );

        // Check initial status
        assert_eq!(collector.status().await, CollectorStatus::Stopped);

        // Start collector
        collector.start().await.unwrap();
        assert_eq!(collector.status().await, CollectorStatus::Running);

        // Collect events
        let event = create_test_event();
        assert!(collector.collect(event));

        // Pause collector
        collector.pause().await.unwrap();
        assert_eq!(collector.status().await, CollectorStatus::Paused);

        // Resume collector
        collector.resume().await.unwrap();
        assert_eq!(collector.status().await, CollectorStatus::Running);

        // Flush events
        collector.flush().await.unwrap();

        // Stop collector
        collector.stop().await.unwrap();
        assert_eq!(collector.status().await, CollectorStatus::Stopped);

        // Check stats
        let stats = collector.stats().await;
        assert!(stats.start_time.is_some());
        assert!(stats.last_active_time.is_some());
    }

    #[tokio::test]
    async fn test_collector_batch_processing() {
        // Create components
        let buffer_config = CircularBufferConfig {
            capacity: 100,
            overflow_behavior: OverflowBehavior::DropOldest,
        };
        let buffer = Arc::new(CircularBuffer::new(buffer_config));

        let processor_config = PipelineProcessorConfig::default();
        let processor = Arc::new(PipelineProcessor::new(processor_config));

        let dir = tempdir().unwrap();
        let path = dir.path().join("events.log");
        let exporter_config = FileExporterConfig {
            path: path.clone(),
            format: FileFormat::Json,
            flush_interval_ms: 0, // Flush immediately
            ..Default::default()
        };
        let exporter = Arc::new(FileExporter::new(exporter_config).unwrap());

        // Create collector
        let collector_config = EventCollectorConfig {
            batch_size: 10,
            batch_interval_ms: 100,
            auto_start: false,
            ..Default::default()
        };
        let collector = EventCollector::new(
            collector_config,
            buffer,
            processor,
            exporter,
        );

        // Start collector
        collector.start().await.unwrap();

        // Collect batch of events
        let mut events = Vec::new();
        for _ in 0..5 {
            events.push(create_test_event());
        }
        assert_eq!(collector.collect_batch(events), 5);

        // Wait for processing
        tokio::time::sleep(Duration::from_millis(200)).await;

        // Stop collector
        collector.stop().await.unwrap();

        // Check stats
        let stats = collector.stats().await;
        assert!(stats.collected_count > 0);
        assert!(stats.processed_count > 0);
        assert!(stats.exported_count > 0);

        // Check that events were written to the file
        let contents = std::fs::read_to_string(path).unwrap();
        let lines: Vec<&str> = contents.lines().collect();
        assert_eq!(lines.len(), 5);
    }
} 