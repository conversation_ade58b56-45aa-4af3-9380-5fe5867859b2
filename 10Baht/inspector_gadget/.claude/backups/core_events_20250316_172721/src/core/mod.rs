/*!
 * Core functionality for Inspector Gadget
 * 
 * This module contains the core types and traits used throughout the framework.
 */

// Re-export event types from the events module
pub use self::events::{
    EventType as ImportedEventType, EventData as ImportedEventData, TraceEvent as ImportedTraceEvent, EventSeverity,
    EventBuffer, EventBufferConfig, BufferOverflowBehavior, EventBufferStats,
    EventProcessor, EventExporter, EventCollectionSystem,
    EventSender, EventReceiver, create_event_channel,
};

// Re-export buffer types
pub use self::buffer::{
    EventBuffer as ImportedEventBuffer, BufferStats,
    CircularBuffer, CircularBufferConfig, OverflowBehavior,
    MemoryBuffer, MemoryBufferConfig,
    new_circular_buffer, new_memory_buffer,
};

// Re-export processor types
pub use self::processor::{
    EventProcessor as ImportedEventProcessor, PipelineProcessor, PipelineProcessorConfig,
    SyscallNormalizer, SyscallNormalizerConfig,
    ContextEnricher, ContextEnricherConfig,
    EventFilter, EventFilterConfig, FilterRule,
    EventCorrelator, EventCorrelatorConfig, CorrelationRule,
    new_pipeline_processor,
};

// Re-export exporter types
pub use self::exporter::{
    EventExporter as ImportedEventExporter, ElasticsearchExporter, ElasticsearchConfig,
    FileExporter, FileExporterConfig, FileFormat,
    CompositeExporter,
    new_elasticsearch_exporter, new_file_exporter,
};

// Re-export collector types
pub use self::collector::{
    EventCollector, EventCollectorConfig,
    CollectorStatus, CollectorStats,
};

// Modules
pub mod events;
pub mod buffer;
pub mod processor;
pub mod exporter;
pub mod collector;

use std::fmt;
use std::time::{SystemTime, UNIX_EPOCH};
use crate::error::Result;
use crate::syscalls::SyscallEvent;

/// Event type for binary analysis
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum EventType {
    /// System call event
    Syscall,
    /// File operation event
    FileOperation,
    /// Network activity event
    NetworkActivity,
    /// Process execution event
    ProcessExecution,
    /// Memory operation event
    MemoryOperation,
    /// Custom event type
    Custom(String),
}

impl fmt::Display for EventType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EventType::Syscall => write!(f, "Syscall"),
            EventType::FileOperation => write!(f, "FileOperation"),
            EventType::NetworkActivity => write!(f, "NetworkActivity"),
            EventType::ProcessExecution => write!(f, "ProcessExecution"),
            EventType::MemoryOperation => write!(f, "MemoryOperation"),
            EventType::Custom(name) => write!(f, "Custom({})", name),
        }
    }
}

/// Event data for binary analysis
#[derive(Debug, Clone)]
pub enum EventData {
    /// System call event data
    Syscall(SyscallEvent),
    /// File operation event data
    FileOperation {
        /// Path of the file
        path: String,
        /// Operation type (read, write, etc.)
        operation: String,
        /// Result of the operation
        result: i32,
    },
    /// Network activity event data
    NetworkActivity {
        /// Source address
        source: String,
        /// Destination address
        destination: String,
        /// Protocol used
        protocol: String,
        /// Amount of data transferred
        bytes: usize,
    },
    /// Process execution event data
    ProcessExecution {
        /// Process ID
        pid: u32,
        /// Parent process ID
        ppid: u32,
        /// Command line
        command: String,
        /// Environment variables
        environment: Vec<String>,
    },
    /// Memory operation event data
    MemoryOperation {
        /// Address of the memory operation
        address: u64,
        /// Size of the memory operation
        size: usize,
        /// Type of memory operation (allocate, free, etc.)
        operation: String,
    },
    /// Custom event data
    Custom(serde_json::Value),
}

/// Trace event for binary analysis
#[derive(Debug, Clone)]
pub struct TraceEvent {
    /// Timestamp of the event (microseconds since UNIX epoch)
    pub timestamp: u64,
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// Event type
    pub event_type: EventType,
    /// Event data
    pub data: EventData,
}

impl TraceEvent {
    /// Create a new trace event
    pub fn new(pid: u32, tid: u32, event_type: EventType, data: EventData) -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_micros() as u64;
        
        TraceEvent {
            timestamp,
            pid,
            tid,
            event_type,
            data,
        }
    }
    
    /// Get the event timestamp as a human-readable string
    pub fn formatted_timestamp(&self) -> String {
        let secs = (self.timestamp / 1_000_000) as i64;
        let nsecs = ((self.timestamp % 1_000_000) * 1000) as u32;
        
        let datetime = chrono::NaiveDateTime::from_timestamp_opt(secs, nsecs)
            .unwrap_or_else(|| chrono::NaiveDateTime::from_timestamp_opt(0, 0).unwrap());
        
        datetime.format("%Y-%m-%d %H:%M:%S.%f").to_string()
    }
}

/// Tracer trait for binary analysis
pub trait Tracer: Send + Sync {
    /// Start tracing a process
    fn start(&mut self, pid: u32) -> Result<()>;
    
    /// Stop tracing
    fn stop(&mut self) -> Result<()>;
    
    /// Get the next trace event
    fn next_event(&mut self) -> Result<Option<TraceEvent>>;
    
    /// Check if the tracer is active
    fn is_active(&self) -> bool;
    
    /// Get statistics about the tracing session
    fn stats(&self) -> TracerStats;
}

/// Statistics for a tracing session
#[derive(Debug, Clone, Default)]
pub struct TracerStats {
    /// Number of events processed
    pub events_processed: usize,
    /// Number of events filtered out
    pub events_filtered: usize,
    /// Start time of the tracing session
    pub start_time: Option<SystemTime>,
    /// End time of the tracing session
    pub end_time: Option<SystemTime>,
}

impl TracerStats {
    /// Create a new tracer stats instance
    pub fn new() -> Self {
        TracerStats {
            events_processed: 0,
            events_filtered: 0,
            start_time: None,
            end_time: None,
        }
    }
    
    /// Start the tracing session
    pub fn start(&mut self) {
        self.start_time = Some(SystemTime::now());
    }
    
    /// End the tracing session
    pub fn end(&mut self) {
        self.end_time = Some(SystemTime::now());
    }
    
    /// Get the duration of the tracing session
    pub fn duration(&self) -> Option<std::time::Duration> {
        match (self.start_time, self.end_time) {
            (Some(start), Some(end)) => end.duration_since(start).ok(),
            (Some(start), None) => SystemTime::now().duration_since(start).ok(),
            _ => None,
        }
    }
    
    /// Get the events per second
    pub fn events_per_second(&self) -> f64 {
        if let Some(duration) = self.duration() {
            let seconds = duration.as_secs_f64();
            if seconds > 0.0 {
                return self.events_processed as f64 / seconds;
            }
        }
        0.0
    }
} 