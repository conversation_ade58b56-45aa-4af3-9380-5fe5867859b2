/*!
 * Syscall Filter Module
 * 
 * This module provides functionality for filtering syscall events
 * based on various criteria such as syscall ID, name, category,
 * process ID, and more.
 */

use std::collections::HashSet;


use crate::platforms::Platform;
use crate::syscalls::common::{Syscall, SyscallCategory, SyscallEvent};

/// Represents a filter for syscall events
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct SyscallFilter {
    /// Include syscalls with these IDs
    include_ids: HashSet<u32>,
    /// Exclude syscalls with these IDs
    exclude_ids: HashSet<u32>,
    /// Include syscalls with these names
    include_names: HashSet<String>,
    /// Exclude syscalls with these names
    exclude_names: HashSet<String>,
    /// Include syscalls in these categories
    include_categories: HashSet<SyscallCategory>,
    /// Exclude syscalls in these categories
    exclude_categories: HashSet<SyscallCategory>,
    /// Include syscalls from these processes
    include_pids: HashSet<u32>,
    /// Exclude syscalls from these processes
    exclude_pids: HashSet<u32>,
    /// Include only successful syscalls
    only_successful: bool,
    /// Include only failed syscalls
    only_failed: bool,
    /// Include only security-sensitive syscalls
    only_security_sensitive: bool,
    /// Platform to filter on (if None, all platforms are included)
    platform: Option<dyn Platform>,
}

impl Default for SyscallFilter {
    fn default() -> Self {
        Self::new()
    }
}

impl SyscallFilter {
    /// Create a new syscall filter with default settings
    pub fn new() -> Self {
        Self {
            include_ids: HashSet::new(),
            exclude_ids: HashSet::new(),
            include_names: HashSet::new(),
            exclude_names: HashSet::new(),
            include_categories: HashSet::new(),
            exclude_categories: HashSet::new(),
            include_pids: HashSet::new(),
            exclude_pids: HashSet::new(),
            only_successful: false,
            only_failed: false,
            only_security_sensitive: false,
            platform: None,
        }
    }
    
    /// Add a syscall ID to include
    pub fn include_id(mut self, id: u32) -> Self {
        self.include_ids.insert(id);
        self
    }
    
    /// Add a syscall ID to exclude
    pub fn exclude_id(mut self, id: u32) -> Self {
        self.exclude_ids.insert(id);
        self
    }
    
    /// Add a syscall name to include
    pub fn include_name(mut self, name: &str) -> Self {
        self.include_names.insert(name.to_string());
        self
    }
    
    /// Add a syscall name to exclude
    pub fn exclude_name(mut self, name: &str) -> Self {
        self.exclude_names.insert(name.to_string());
        self
    }
    
    /// Add a syscall category to include
    pub fn include_category(mut self, category: SyscallCategory) -> Self {
        self.include_categories.insert(category);
        self
    }
    
    /// Add a syscall category to exclude
    pub fn exclude_category(mut self, category: SyscallCategory) -> Self {
        self.exclude_categories.insert(category);
        self
    }
    
    /// Add a process ID to include
    pub fn include_pid(mut self, pid: u32) -> Self {
        self.include_pids.insert(pid);
        self
    }
    
    /// Add a process ID to exclude
    pub fn exclude_pid(mut self, pid: u32) -> Self {
        self.exclude_pids.insert(pid);
        self
    }
    
    /// Include only successful syscalls
    pub fn only_successful(mut self, value: bool) -> Self {
        self.only_successful = value;
        self
    }
    
    /// Include only failed syscalls
    pub fn only_failed(mut self, value: bool) -> Self {
        self.only_failed = value;
        self
    }
    
    /// Include only security-sensitive syscalls
    pub fn only_security_sensitive(mut self, value: bool) -> Self {
        self.only_security_sensitive = value;
        self
    }
    
    /// Set the platform to filter on
    pub fn platform(mut self, platform: dyn Platform) -> Self {
        self.platform = Some(platform);
        self
    }
    
    /// Check if a syscall event matches the filter
    pub fn matches(&self, event: &SyscallEvent, syscall: Option<&Syscall>) -> bool {
        // Check if the syscall ID is excluded
        if !self.exclude_ids.is_empty() && self.exclude_ids.contains(&event.syscall_id) {
            return false;
        }
        
        // Check if the syscall ID is included (if include_ids is not empty)
        if !self.include_ids.is_empty() && !self.include_ids.contains(&event.syscall_id) {
            return false;
        }
        
        // Check if the process ID is excluded
        if !self.exclude_pids.is_empty() && self.exclude_pids.contains(&event.process_id) {
            return false;
        }
        
        // Check if the process ID is included (if include_pids is not empty)
        if !self.include_pids.is_empty() && !self.include_pids.contains(&event.process_id) {
            return false;
        }
        
        // Check if the syscall is successful/failed
        if self.only_successful && !event.success {
            return false;
        }
        
        if self.only_failed && event.success {
            return false;
        }
        
        // If we have a syscall reference, check name, category, and platform
        if let Some(syscall) = syscall {
            // Check if the syscall name is excluded
            if !self.exclude_names.is_empty() && self.exclude_names.contains(&syscall.name) {
                return false;
            }
            
            // Check if the syscall name is included (if include_names is not empty)
            if !self.include_names.is_empty() && !self.include_names.contains(&syscall.name) {
                return false;
            }
            
            // Check if the syscall category is excluded
            if !self.exclude_categories.is_empty() && self.exclude_categories.contains(&syscall.category) {
                return false;
            }
            
            // Check if the syscall category is included (if include_categories is not empty)
            if !self.include_categories.is_empty() && !self.include_categories.contains(&syscall.category) {
                return false;
            }
            
            // Check if the platform matches
            if let Some(platform) = self.platform {
                if syscall.platform != platform {
                    return false;
                }
            }
            
            // Check if only security-sensitive syscalls are included
            if self.only_security_sensitive {
                let security_sensitive_categories = [
                    SyscallCategory::Security,
                    SyscallCategory::Process,
                    SyscallCategory::Module,
                ];
                
                if !security_sensitive_categories.contains(&syscall.category) {
                    return false;
                }
            }
        } else {
            // If we don't have a syscall reference, we can't check name, category, or platform
            // If any of these filters are set, we should return false
            if !self.include_names.is_empty() || !self.exclude_names.is_empty() ||
               !self.include_categories.is_empty() || !self.exclude_categories.is_empty() ||
               self.platform.is_some() || self.only_security_sensitive {
                return false;
            }
        }
        
        // If we've made it this far, the event matches the filter
        true
    }
    
    /// Create a filter that includes only security-sensitive syscalls
    pub fn security_sensitive() -> Self {
        Self::new().only_security_sensitive(true)
    }
    
    /// Create a filter that includes only file system syscalls
    pub fn file_system() -> Self {
        Self::new().include_category(SyscallCategory::FileSystem)
    }
    
    /// Create a filter that includes only network syscalls
    pub fn network() -> Self {
        Self::new().include_category(SyscallCategory::Network)
    }
    
    /// Create a filter that includes only process syscalls
    pub fn process() -> Self {
        Self::new().include_category(SyscallCategory::Process)
    }
    
    /// Create a filter that includes only memory syscalls
    pub fn memory() -> Self {
        Self::new().include_category(SyscallCategory::Memory)
    }
    
    /// Create a filter that includes only IPC syscalls
    pub fn ipc() -> Self {
        Self::new().include_category(SyscallCategory::IPC)
    }
    
    /// Create a filter that includes only time syscalls
    pub fn time() -> Self {
        Self::new().include_category(SyscallCategory::Time)
    }
    
    /// Create a filter that includes only system info syscalls
    pub fn system_info() -> Self {
        Self::new().include_category(SyscallCategory::SystemInfo)
    }
    
    /// Create a filter that includes only security syscalls
    pub fn security() -> Self {
        Self::new().include_category(SyscallCategory::Security)
    }
    
    /// Create a filter that includes only IO syscalls
    pub fn io() -> Self {
        Self::new().include_category(SyscallCategory::IO)
    }
    
    /// Create a filter that includes only thread syscalls
    pub fn thread() -> Self {
        Self::new().include_category(SyscallCategory::Thread)
    }
    
    /// Create a filter that includes only synchronization syscalls
    pub fn synchronization() -> Self {
        Self::new().include_category(SyscallCategory::Synchronization)
    }
    
    /// Create a filter that includes only module syscalls
    pub fn module() -> Self {
        Self::new().include_category(SyscallCategory::Module)
    }
    
    /// Create a filter that includes only device syscalls
    pub fn device() -> Self {
        Self::new().include_category(SyscallCategory::Device)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    
    #[test]
    fn test_filter_by_id() {
        let filter = SyscallFilter::new().include_id(1).include_id(2);
        
        let event1 = SyscallEvent::new(1, 1, 1);
        let event2 = SyscallEvent::new(2, 2, 2);
        let event3 = SyscallEvent::new(3, 3, 3);
        
        assert!(filter.matches(&event1, None));
        assert!(filter.matches(&event2, None));
        assert!(!filter.matches(&event3, None));
    }
    
    #[test]
    fn test_filter_by_pid() {
        let filter = SyscallFilter::new().include_pid(100).include_pid(200);
        
        let event1 = SyscallEvent::new(1, 100, 1);
        let event2 = SyscallEvent::new(2, 200, 2);
        let event3 = SyscallEvent::new(3, 300, 3);
        
        assert!(filter.matches(&event1, None));
        assert!(filter.matches(&event2, None));
        assert!(!filter.matches(&event3, None));
    }
    
    #[test]
    fn test_filter_by_success() {
        let filter = SyscallFilter::new().only_successful(true);
        
        let mut event1 = SyscallEvent::new(1, 1, 1);
        event1.success = true;
        
        let mut event2 = SyscallEvent::new(2, 2, 2);
        event2.success = false;
        
        assert!(filter.matches(&event1, None));
        assert!(!filter.matches(&event2, None));
    }
    
    #[test]
    fn test_filter_by_failure() {
        let filter = SyscallFilter::new().only_failed(true);
        
        let mut event1 = SyscallEvent::new(1, 1, 1);
        event1.success = true;
        
        let mut event2 = SyscallEvent::new(2, 2, 2);
        event2.success = false;
        
        assert!(!filter.matches(&event1, None));
        assert!(filter.matches(&event2, None));
    }
    
    #[test]
    fn test_filter_by_name() {
        let filter = SyscallFilter::new().include_name("read").include_name("write");
        
        let event1 = SyscallEvent::new(1, 1, 1);
        let event2 = SyscallEvent::new(2, 2, 2);
        let event3 = SyscallEvent::new(3, 3, 3);
        
        let syscall1 = Syscall::new(1, "read", Platform::Linux, SyscallCategory::FileSystem);
        let syscall2 = Syscall::new(2, "write", Platform::Linux, SyscallCategory::FileSystem);
        let syscall3 = Syscall::new(3, "open", Platform::Linux, SyscallCategory::FileSystem);
        
        assert!(filter.matches(&event1, Some(&syscall1)));
        assert!(filter.matches(&event2, Some(&syscall2)));
        assert!(!filter.matches(&event3, Some(&syscall3)));
    }
    
    #[test]
    fn test_filter_by_category() {
        let filter = SyscallFilter::new().include_category(SyscallCategory::FileSystem);
        
        let event1 = SyscallEvent::new(1, 1, 1);
        let event2 = SyscallEvent::new(2, 2, 2);
        
        let syscall1 = Syscall::new(1, "read", Platform::Linux, SyscallCategory::FileSystem);
        let syscall2 = Syscall::new(2, "fork", Platform::Linux, SyscallCategory::Process);
        
        assert!(filter.matches(&event1, Some(&syscall1)));
        assert!(!filter.matches(&event2, Some(&syscall2)));
    }
    
    #[test]
    fn test_filter_by_platform() {
        let filter = SyscallFilter::new().platform(Platform::Linux);
        
        let event1 = SyscallEvent::new(1, 1, 1);
        let event2 = SyscallEvent::new(2, 2, 2);
        
        let syscall1 = Syscall::new(1, "read", Platform::Linux, SyscallCategory::FileSystem);
        let syscall2 = Syscall::new(2, "NtCreateFile", Platform::Windows, SyscallCategory::FileSystem);
        
        assert!(filter.matches(&event1, Some(&syscall1)));
        assert!(!filter.matches(&event2, Some(&syscall2)));
    }
    
    #[test]
    fn test_filter_by_security_sensitive() {
        let filter = SyscallFilter::new().only_security_sensitive(true);
        
        let event1 = SyscallEvent::new(1, 1, 1);
        let event2 = SyscallEvent::new(2, 2, 2);
        let event3 = SyscallEvent::new(3, 3, 3);
        
        let syscall1 = Syscall::new(1, "fork", Platform::Linux, SyscallCategory::Process);
        let syscall2 = Syscall::new(2, "chmod", Platform::Linux, SyscallCategory::Security);
        let syscall3 = Syscall::new(3, "read", Platform::Linux, SyscallCategory::FileSystem);
        
        assert!(filter.matches(&event1, Some(&syscall1)));
        assert!(filter.matches(&event2, Some(&syscall2)));
        assert!(!filter.matches(&event3, Some(&syscall3)));
    }
    
    #[test]
    fn test_combined_filters() {
        let filter = SyscallFilter::new()
            .include_category(SyscallCategory::FileSystem)
            .platform(Platform::Linux)
            .only_successful(true);
        
        let mut event1 = SyscallEvent::new(1, 1, 1);
        event1.success = true;
        
        let mut event2 = SyscallEvent::new(2, 2, 2);
        event2.success = false;
        
        let mut event3 = SyscallEvent::new(3, 3, 3);
        event3.success = true;
        
        let syscall1 = Syscall::new(1, "read", Platform::Linux, SyscallCategory::FileSystem);
        let syscall2 = Syscall::new(2, "read", Platform::Linux, SyscallCategory::FileSystem);
        let syscall3 = Syscall::new(3, "read", Platform::Windows, SyscallCategory::FileSystem);
        
        assert!(filter.matches(&event1, Some(&syscall1)));
        assert!(!filter.matches(&event2, Some(&syscall2))); // Not successful
        assert!(!filter.matches(&event3, Some(&syscall3))); // Not Linux
    }
    
    #[test]
    fn test_factory_methods() {
        let event = SyscallEvent::new(1, 1, 1);
        
        let syscall_fs = Syscall::new(1, "read", Platform::Linux, SyscallCategory::FileSystem);
        let syscall_net = Syscall::new(2, "socket", Platform::Linux, SyscallCategory::Network);
        let syscall_proc = Syscall::new(3, "fork", Platform::Linux, SyscallCategory::Process);
        let syscall_mem = Syscall::new(4, "mmap", Platform::Linux, SyscallCategory::Memory);
        let syscall_ipc = Syscall::new(5, "pipe", Platform::Linux, SyscallCategory::IPC);
        let syscall_time = Syscall::new(6, "clock_gettime", Platform::Linux, SyscallCategory::Time);
        let syscall_sysinfo = Syscall::new(7, "uname", Platform::Linux, SyscallCategory::SystemInfo);
        let syscall_security = Syscall::new(8, "chmod", Platform::Linux, SyscallCategory::Security);
        let syscall_io = Syscall::new(9, "ioctl", Platform::Linux, SyscallCategory::IO);
        let syscall_thread = Syscall::new(10, "clone", Platform::Linux, SyscallCategory::Thread);
        let syscall_sync = Syscall::new(11, "futex", Platform::Linux, SyscallCategory::Synchronization);
        let syscall_module = Syscall::new(12, "init_module", Platform::Linux, SyscallCategory::Module);
        let syscall_device = Syscall::new(13, "iopl", Platform::Linux, SyscallCategory::Device);
        
        assert!(SyscallFilter::file_system().matches(&event, Some(&syscall_fs)));
        assert!(!SyscallFilter::file_system().matches(&event, Some(&syscall_net)));
        
        assert!(SyscallFilter::network().matches(&event, Some(&syscall_net)));
        assert!(!SyscallFilter::network().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::process().matches(&event, Some(&syscall_proc)));
        assert!(!SyscallFilter::process().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::memory().matches(&event, Some(&syscall_mem)));
        assert!(!SyscallFilter::memory().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::ipc().matches(&event, Some(&syscall_ipc)));
        assert!(!SyscallFilter::ipc().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::time().matches(&event, Some(&syscall_time)));
        assert!(!SyscallFilter::time().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::system_info().matches(&event, Some(&syscall_sysinfo)));
        assert!(!SyscallFilter::system_info().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::security().matches(&event, Some(&syscall_security)));
        assert!(!SyscallFilter::security().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::io().matches(&event, Some(&syscall_io)));
        assert!(!SyscallFilter::io().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::thread().matches(&event, Some(&syscall_thread)));
        assert!(!SyscallFilter::thread().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::synchronization().matches(&event, Some(&syscall_sync)));
        assert!(!SyscallFilter::synchronization().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::module().matches(&event, Some(&syscall_module)));
        assert!(!SyscallFilter::module().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::device().matches(&event, Some(&syscall_device)));
        assert!(!SyscallFilter::device().matches(&event, Some(&syscall_fs)));
        
        assert!(SyscallFilter::security_sensitive().matches(&event, Some(&syscall_proc)));
        assert!(SyscallFilter::security_sensitive().matches(&event, Some(&syscall_security)));
        assert!(SyscallFilter::security_sensitive().matches(&event, Some(&syscall_module)));
        assert!(!SyscallFilter::security_sensitive().matches(&event, Some(&syscall_fs)));
    }
} 