/*!
 * Event Collection System for Inspector Gadget
 * 
 * This module provides the core event types, buffer implementation, and utilities
 * for collecting, processing, and exporting events from binary analysis.
 */

use std::collections::VecDeque;
use std::fmt;
use std::sync::{<PERSON>, <PERSON>tex, RwLock};
use std::time::{Duration, SystemTime, UNIX_EPOCH};

use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;
use uuid::Uuid;

use crate::error::{Error, Result};
use crate::syscalls::SyscallEvent as ImportedSyscallEvent;

/// Maximum number of events to store in the buffer by default
const DEFAULT_BUFFER_CAPACITY: usize = 10_000;

/// Maximum time to wait for buffer space (milliseconds)
const DEFAULT_BUFFER_TIMEOUT_MS: u64 = 100;

//------------------------------------------------------------------------------
// Event Types and Data Structures
//------------------------------------------------------------------------------

/// Event severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum EventSeverity {
    /// Informational event
    Info,
    /// Warning event
    Warning,
    /// Error event
    Error,
    /// Critical event
    Critical,
}

/// Event types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum EventType {
    /// System call event
    Syscall,
    /// File operation event
    FileOperation,
    /// Network operation event
    NetworkOperation,
    /// Process operation event
    ProcessOperation,
    /// Memory operation event
    MemoryOperation,
    /// Security event
    SecurityEvent,
    /// Custom event
    Custom(String),
}

/// Event data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum EventData {
    /// System call event data
    Syscall(SyscallEvent),
    /// File operation event data
    FileOperation(FileEvent),
    /// Network operation event data
    NetworkOperation(NetworkEvent),
    /// Process operation event data
    ProcessOperation(ProcessEvent),
    /// Memory operation event data
    MemoryOperation(MemoryEvent),
    /// Security event data
    SecurityEvent(SecurityEvent),
    /// Custom event data
    Custom(serde_json::Value),
}

/// System call event data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SyscallEvent {
    /// System call ID
    pub syscall_id: u32,
    /// System call name
    pub syscall_name: String,
    /// System call category
    pub category: String,
    /// Windows equivalent syscall ID (if applicable)
    pub windows_equivalent: Option<u32>,
    /// System call arguments
    pub args: Vec<u64>,
    /// Return value
    pub return_value: i64,
    /// Duration in nanoseconds
    pub duration_ns: u64,
}

/// File event data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FileEvent {
    /// File path
    pub path: String,
    /// Operation type (open, read, write, close, etc.)
    pub operation: String,
    /// File descriptor
    pub fd: i32,
    /// Result code
    pub result: i32,
    /// Bytes transferred (if applicable)
    pub bytes: Option<usize>,
    /// File permissions (if applicable)
    pub permissions: Option<u32>,
}

/// Network event data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct NetworkEvent {
    /// Socket file descriptor
    pub socket_fd: i32,
    /// Operation type (socket, connect, bind, listen, accept, send, recv, etc.)
    pub operation: String,
    /// Protocol (TCP, UDP, etc.)
    pub protocol: String,
    /// Local address
    pub local_addr: Option<String>,
    /// Remote address
    pub remote_addr: Option<String>,
    /// Bytes transferred (if applicable)
    pub bytes: Option<usize>,
    /// Result code
    pub result: i32,
}

/// Process event data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ProcessEvent {
    /// Operation type (fork, exec, exit, etc.)
    pub operation: String,
    /// Process ID
    pub pid: u32,
    /// Parent process ID
    pub ppid: u32,
    /// Process name
    pub name: Option<String>,
    /// Command line
    pub cmdline: Option<String>,
    /// Result code
    pub result: i32,
}

/// Memory event data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct MemoryEvent {
    /// Operation type (mmap, munmap, mprotect, etc.)
    pub operation: String,
    /// Memory address
    pub address: u64,
    /// Memory size
    pub size: usize,
    /// Memory protection flags
    pub protection: Option<u32>,
    /// Result code
    pub result: i32,
}

/// Security event data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SecurityEvent {
    /// Operation type (setuid, setgid, capset, etc.)
    pub operation: String,
    /// User ID (if applicable)
    pub uid: Option<u32>,
    /// Group ID (if applicable)
    pub gid: Option<u32>,
    /// Capabilities (if applicable)
    pub capabilities: Option<u64>,
    /// Result code
    pub result: i32,
}

/// Trace event
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TraceEvent {
    /// Event ID
    pub id: String,
    /// Timestamp in nanoseconds since UNIX epoch
    pub timestamp: u64,
    /// Event type
    pub event_type: EventType,
    /// Event data
    pub data: EventData,
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// Event severity
    pub severity: EventSeverity,
    /// Event source
    pub source: String,
    /// Event tags
    pub tags: Vec<String>,
}

impl TraceEvent {
    /// Create a new trace event
    pub fn new(
        timestamp: u64,
        event_type: EventType,
        data: EventData,
        pid: u32,
        tid: u32,
        severity: EventSeverity,
        source: impl Into<String>,
        tags: Vec<String>,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            timestamp,
            event_type,
            data,
            pid,
            tid,
            severity,
            source: source.into(),
            tags,
        }
    }

    /// Create a new trace event with the current timestamp
    pub fn now(
        event_type: EventType,
        data: EventData,
        pid: u32,
        tid: u32,
        severity: EventSeverity,
        source: impl Into<String>,
        tags: Vec<String>,
    ) -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos() as u64;
        
        Self::new(timestamp, event_type, data, pid, tid, severity, source, tags)
    }
}

//------------------------------------------------------------------------------
// Event Buffer Implementation
//------------------------------------------------------------------------------

/// Overflow behavior for the event buffer
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BufferOverflowBehavior {
    /// Drop oldest events when buffer is full
    DropOldest,
    /// Drop newest events when buffer is full
    DropNewest,
    /// Block until space is available
    Block,
}

/// Configuration for the event buffer
#[derive(Debug, Clone)]
pub struct EventBufferConfig {
    /// Maximum capacity of the buffer
    pub capacity: usize,
    /// Overflow behavior
    pub overflow_behavior: BufferOverflowBehavior,
    /// Timeout for blocking operations (milliseconds)
    pub timeout_ms: u64,
}

impl Default for EventBufferConfig {
    fn default() -> Self {
        EventBufferConfig {
            capacity: DEFAULT_BUFFER_CAPACITY,
            overflow_behavior: BufferOverflowBehavior::DropOldest,
            timeout_ms: DEFAULT_BUFFER_TIMEOUT_MS,
        }
    }
}

/// Thread-safe event buffer
#[derive(Debug)]
pub struct EventBuffer {
    /// Internal buffer
    buffer: Arc<Mutex<VecDeque<TraceEvent>>>,
    /// Configuration
    config: EventBufferConfig,
    /// Statistics
    stats: Arc<RwLock<EventBufferStats>>,
}

/// Statistics for the event buffer
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct EventBufferStats {
    /// Total events added
    pub total_added: u64,
    /// Total events removed
    pub total_removed: u64,
    /// Total events dropped
    pub total_dropped: u64,
    /// High water mark
    pub high_water_mark: usize,
}

impl EventBuffer {
    /// Create a new event buffer with the given configuration
    pub fn new(config: EventBufferConfig) -> Self {
        let buffer = Arc::new(Mutex::new(VecDeque::with_capacity(config.capacity)));
        let stats = Arc::new(RwLock::new(EventBufferStats::default()));

        EventBuffer {
            buffer,
            config,
            stats,
        }
    }

    /// Create a new event buffer with default configuration
    pub fn default() -> Self {
        Self::new(EventBufferConfig::default())
    }

    /// Add an event to the buffer
    pub fn add(&self, event: TraceEvent) -> Result<()> {
        let mut buffer = self.buffer.lock().map_err(|e| Error::Concurrency(e.to_string()))?;
        let mut stats = self.stats.write().map_err(|e| Error::Concurrency(e.to_string()))?;

        // Check if buffer is full
        if buffer.len() >= self.config.capacity {
            match self.config.overflow_behavior {
                BufferOverflowBehavior::DropOldest => {
                    // Drop oldest event
                    buffer.pop_front();
                    stats.total_dropped += 1;
                }
                BufferOverflowBehavior::DropNewest => {
                    // Drop newest event (the one we're trying to add)
                    stats.total_dropped += 1;
                    return Ok(());
                }
                BufferOverflowBehavior::Block => {
                    // In a real implementation, we would block here
                    // For simplicity, we'll just return an error
                    return Err(Error::BufferFull);
                }
            }
        }

        // Add the event
        buffer.push_back(event);
        stats.total_added += 1;

        // Update high water mark
        if buffer.len() > stats.high_water_mark {
            stats.high_water_mark = buffer.len();
        }

        Ok(())
    }

    /// Get the next event from the buffer
    pub fn next(&self) -> Result<Option<TraceEvent>> {
        let mut buffer = self.buffer.lock().map_err(|e| Error::Concurrency(e.to_string()))?;
        let mut stats = self.stats.write().map_err(|e| Error::Concurrency(e.to_string()))?;

        if let Some(event) = buffer.pop_front() {
            stats.total_removed += 1;
            Ok(Some(event))
        } else {
            Ok(None)
        }
    }

    /// Get multiple events from the buffer
    pub fn next_batch(&self, max_count: usize) -> Result<Vec<TraceEvent>> {
        let mut buffer = self.buffer.lock().map_err(|e| Error::Concurrency(e.to_string()))?;
        let mut stats = self.stats.write().map_err(|e| Error::Concurrency(e.to_string()))?;

        let count = std::cmp::min(max_count, buffer.len());
        let mut events = Vec::with_capacity(count);

        for _ in 0..count {
            if let Some(event) = buffer.pop_front() {
                events.push(event);
                stats.total_removed += 1;
            } else {
                break;
            }
        }

        Ok(events)
    }

    /// Get the number of events in the buffer
    pub fn len(&self) -> Result<usize> {
        let buffer = self.buffer.lock().map_err(|e| Error::Concurrency(e.to_string()))?;
        Ok(buffer.len())
    }

    /// Check if the buffer is empty
    pub fn is_empty(&self) -> Result<bool> {
        let buffer = self.buffer.lock().map_err(|e| Error::Concurrency(e.to_string()))?;
        Ok(buffer.is_empty())
    }

    /// Get the buffer statistics
    pub fn stats(&self) -> Result<EventBufferStats> {
        let stats = self.stats.read().map_err(|e| Error::Concurrency(e.to_string()))?;
        Ok(stats.clone())
    }

    /// Clear the buffer
    pub fn clear(&self) -> Result<()> {
        let mut buffer = self.buffer.lock().map_err(|e| Error::Concurrency(e.to_string()))?;
        buffer.clear();
        Ok(())
    }
}

//------------------------------------------------------------------------------
// Async Event Channel
//------------------------------------------------------------------------------

/// Async event sender
pub type EventSender = mpsc::Sender<TraceEvent>;

/// Async event receiver
pub type EventReceiver = mpsc::Receiver<TraceEvent>;

/// Create a new async event channel
pub fn create_event_channel(buffer_size: usize) -> (EventSender, EventReceiver) {
    mpsc::channel(buffer_size)
}

//------------------------------------------------------------------------------
// Event Processor
//------------------------------------------------------------------------------

/// Event processor trait
#[async_trait::async_trait]
pub trait EventProcessor: Send + Sync {
    /// Process an event
    async fn process(&self, event: TraceEvent) -> Result<TraceEvent>;

    /// Process a batch of events
    async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
        let mut processed = Vec::with_capacity(events.len());
        for event in events {
            processed.push(self.process(event).await?);
        }
        Ok(processed)
    }
}

//------------------------------------------------------------------------------
// Event Exporter
//------------------------------------------------------------------------------

/// Event exporter trait
#[async_trait::async_trait]
pub trait EventExporter: Send + Sync {
    /// Export an event
    async fn export(&self, event: &TraceEvent) -> Result<()>;

    /// Export a batch of events
    async fn export_batch(&self, events: &[TraceEvent]) -> Result<()>;

    /// Flush any buffered events
    async fn flush(&self) -> Result<()>;
}

/// Elasticsearch event exporter
pub struct ElasticsearchExporter {
    // Configuration and client will be added in the implementation
}

//------------------------------------------------------------------------------
// Event Collection System
//------------------------------------------------------------------------------

/// Event collection system
pub struct EventCollectionSystem {
    /// Event buffer
    buffer: Arc<EventBuffer>,
    /// Event processor
    processor: Option<Arc<dyn EventProcessor>>,
    /// Event exporter
    exporter: Option<Arc<dyn EventExporter>>,
    /// Event sender
    sender: EventSender,
    /// Event receiver
    receiver: Option<EventReceiver>,
}

impl EventCollectionSystem {
    /// Create a new event collection system
    pub fn new(
        buffer_config: EventBufferConfig,
        processor: Option<Arc<dyn EventProcessor>>,
        exporter: Option<Arc<dyn EventExporter>>,
    ) -> Self {
        let buffer = Arc::new(EventBuffer::new(buffer_config));
        let (sender, receiver) = create_event_channel(buffer_config.capacity);

        EventCollectionSystem {
            buffer,
            processor,
            exporter,
            sender,
            receiver: Some(receiver),
        }
    }

    /// Get the event sender
    pub fn sender(&self) -> EventSender {
        self.sender.clone()
    }

    /// Start the event collection system
    pub async fn start(&mut self) -> Result<()> {
        let buffer = self.buffer.clone();
        let processor = self.processor.clone();
        let exporter = self.exporter.clone();
        let mut receiver = self.receiver.take().ok_or_else(|| Error::AlreadyStarted)?;

        // Spawn a task to process events
        tokio::spawn(async move {
            while let Some(event) = receiver.recv().await {
                // Process the event
                let processed_event = match &processor {
                    Some(p) => match p.process(event).await {
                        Ok(e) => e,
                        Err(err) => {
                            log::error!("Error processing event: {}", err);
                            continue;
                        }
                    },
                    None => event,
                };

                // Add to buffer
                if let Err(err) = buffer.add(processed_event.clone()) {
                    log::error!("Error adding event to buffer: {}", err);
                }

                // Export the event
                if let Some(e) = &exporter {
                    if let Err(err) = e.export(&processed_event).await {
                        log::error!("Error exporting event: {}", err);
                    }
                }
            }
        });

        Ok(())
    }

    /// Stop the event collection system
    pub async fn stop(&self) -> Result<()> {
        // In a real implementation, we would signal the task to stop
        // and wait for it to complete
        Ok(())
    }

    /// Get the event buffer
    pub fn buffer(&self) -> Arc<EventBuffer> {
        self.buffer.clone()
    }
}

//------------------------------------------------------------------------------
// Tests
//------------------------------------------------------------------------------

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_event_buffer() {
        let config = EventBufferConfig {
            capacity: 5,
            overflow_behavior: BufferOverflowBehavior::DropOldest,
            timeout_ms: 100,
        };
        let buffer = EventBuffer::new(config);

        // Add events
        for i in 0..5 {
            let event = TraceEvent::new(
                i,
                EventType::Custom(format!("Event {}", i)),
                EventData::Custom(serde_json::json!({ "index": i })),
                1,
                1,
                EventSeverity::Info,
                "test",
                vec![],
            );
            buffer.add(event).unwrap();
        }

        // Check length
        assert_eq!(buffer.len().unwrap(), 5);

        // Add one more event (should drop oldest)
        let event = TraceEvent::new(
            5,
            EventType::Custom("Event 5".to_string()),
            EventData::Custom(serde_json::json!({ "index": 5 })),
            1,
            1,
            EventSeverity::Info,
            "test",
            vec![],
        );
        buffer.add(event).unwrap();

        // Check length (still 5)
        assert_eq!(buffer.len().unwrap(), 5);

        // Get next event (should be event 1, since event 0 was dropped)
        let next = buffer.next().unwrap().unwrap();
        assert_eq!(next.id, "1");

        // Check length (now 4)
        assert_eq!(buffer.len().unwrap(), 4);

        // Get batch of events (should be events 2, 3, 4, 5)
        let batch = buffer.next_batch(10).unwrap();
        assert_eq!(batch.len(), 4);
        assert_eq!(batch[0].id, "2");
        assert_eq!(batch[3].id, "5");

        // Check length (now 0)
        assert_eq!(buffer.len().unwrap(), 0);
        assert!(buffer.is_empty().unwrap());
    }
} 