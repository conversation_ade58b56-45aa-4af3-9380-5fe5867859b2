# Evaluation Framework Reference: Detecting Malicious Binaries

## Implementation Status

The Binary Evaluation Framework has been implemented and integrated with the existing hooker_pill central logging system. This implementation provides a comprehensive solution for collecting, analyzing, and storing data about binary execution behavior.

### Key Components Implemented

1. **Elasticsearch Index Setup**: A script (`setup_binary_evaluation_index.sh`) to set up the Elasticsearch index for binary evaluation data, including creating an ILM policy, index template with detailed mappings, and initial index.

2. **Binary Evaluation Extension**: A Rust module (`binary_evaluation_extension.rs`) that extends the hooker_pill central logging system to handle binary evaluation data, including data structures and a logger implementation.

3. **Example Program**: A simple example program (`binary_evaluation_example.rs`) demonstrating how to use the binary evaluation extension to evaluate a binary and send the data to Elasticsearch.

4. **Documentation**: Comprehensive documentation (`binary_evaluation_integration.md` and `README_BINARY_EVALUATION.md`) explaining how to integrate binary evaluation data with the existing hooker_pill central logging system.

### Data Model

The binary evaluation data model includes the following fields:

- **Binary Metadata**: Name, path, size, format, architecture, hashes, etc.
- **Execution Environment**: OS, kernel version, hostname, container/VM info, etc.
- **Syscall Activity**: Syscall counts, types, frequencies, and sequences
- **File Operations**: Files read, written, created, deleted, executed, etc.
- **Network Activity**: Connections, DNS queries, HTTP requests, etc.
- **Process Activity**: Processes created and terminated
- **Memory Activity**: Memory regions, allocations, frees, peak usage, etc.
- **Registry Activity**: Registry keys read, written, created, deleted, etc.
- **Performance Metrics**: CPU usage, memory usage, I/O, execution time, etc.
- **Security Indicators**: Suspicious behaviors, matched signatures, risk score, etc.
- **Analysis Metadata**: Analysis ID, timestamp, tool, parameters, tags, notes, etc.

### Integration with Hooker Pill

The Binary Evaluation Framework is integrated with the existing hooker_pill central logging system, using the same environment variables and following the same patterns for sending data to Elasticsearch.

## Key Behavioral Indicators of Malicious Binaries

Malicious binaries often exhibit specific behaviors that distinguish them from benign ones. Our evaluations will target the following:

1. **Network Activity**: Suspicious connections or data exfiltration attempts.
2. **File System Interactions**: Unauthorized access or modifications to sensitive files.
3. **Process Manipulation**: Code injection or abnormal process creation.
4. **Registry Modifications (Windows)**: Changes to persistence or security-related keys.
5. **System Call Patterns**: Unusual or exploit-related system call sequences.
6. **Resource Usage**: Excessive consumption indicating mining or DDoS behavior.
7. **Privilege Escalation**: Attempts to gain unauthorized privileges.
8. **Hiding Mechanisms**: Techniques to conceal presence, like rootkits.
9. **Container-Specific Behaviors**: Attempts to bypass isolation or affect the host.

## Comprehensive Set of Evaluations

Here's how we can structure the evaluations, leveraging BPF for Windows, Fibratus, eBPF, and BCC to hook into the containerized binary's activities:

### 1. Network Activity Evaluation

**Objective**: Detect suspicious or malicious network behavior.

**Actions**:
- Monitor all outbound network connections from the container using eBPF (Linux) or BPF (Windows) to trace connect() system calls and capture packet data.
- Compare destination IPs and domains against a threat intelligence feed (e.g., known C2 servers or malicious domains).
- Analyze data volume and patterns—flag sudden large uploads as potential data exfiltration.
- Check for connections to unusual ports (e.g., non-standard or high-numbered ports) or protocols.

**Container Context**: Ensure monitoring respects network namespaces and flags attempts to bypass container network policies.

### 2. File System Interaction Evaluation

**Objective**: Identify unauthorized or malicious file operations.

**Actions**:
- Track file operations (open(), read(), write(), unlink()) using eBPF or Fibratus.
- Flag access to sensitive directories (e.g., /etc, /var, C:\Windows) or files (e.g., system binaries, configs).
- Detect ransomware-like patterns, such as bulk file encryption or extension changes.
- Monitor writes to unexpected locations, like system files or outside the container's scope.

**Container Context**: Pay special attention to host-mounted volumes, as they bridge the container and host.

### 3. Process Manipulation Evaluation

**Objective**: Catch attempts to manipulate or spawn processes maliciously.

**Actions**:
- Use Fibratus (Windows) or eBPF (Linux) to monitor process creation (CreateProcess, fork(), execve()) and termination.
- Detect code injection into other processes (e.g., via WriteProcessMemory or ptrace).
- Flag processes with suspicious names, paths, or parent-child relationships (e.g., a user app spawning a system process).

**Container Context**: Check for processes attempting to interact with host processes, indicating a breakout attempt.

### 4. Registry Modifications Evaluation (Windows-Specific)

**Objective**: Identify persistence or system-altering registry changes.

**Actions**:
- Hook registry operations with BPF for Windows or Fibratus (e.g., RegSetValue, RegCreateKey).
- Monitor changes to startup keys (e.g., HKLM\Software\Microsoft\Windows\CurrentVersion\Run), services, or security settings.
- Flag unauthorized modifications that could enable persistence or disable defenses.

**Container Context**: Registry changes are less common in containers but should be monitored if the container has host access.

### 5. System Call Patterns Evaluation

**Objective**: Spot anomalous or exploit-related system call behavior.

**Actions**:
- Use eBPF or BCC (Linux) to trace system calls like open(), execve(), connect(), and chmod().
- Analyze frequency and sequences—e.g., excessive open() calls might suggest file scanning.
- Compare against known malicious patterns (e.g., exploit chains or shellcode execution).

**Container Context**: Ensure tracing operates within the container's PID and mount namespaces.

### 6. Resource Usage Evaluation

**Objective**: Detect excessive resource consumption indicative of malice.

**Actions**:
- Monitor CPU, memory, and network usage via eBPF or BPF metrics.
- Set baseline thresholds for benign behavior and flag exceedances (e.g., steady high CPU for crypto mining).
- Look for bursts of network traffic suggestive of DDoS activity.

**Container Context**: Check if the binary exceeds container resource limits, which could indicate an attempt to disrupt the host.

### 7. Privilege Escalation Evaluation

**Objective**: Identify attempts to gain unauthorized privileges.

**Actions**:
- Monitor system calls like setuid(), setgid(), or Windows privilege APIs (AdjustTokenPrivileges).
- Detect exploitation attempts, such as accessing /proc or kernel memory.
- Flag unexpected elevation of privileges within the container.

**Container Context**: Watch for attempts to escalate beyond the container's user namespace.

### 8. Hiding Mechanisms Evaluation

**Objective**: Uncover attempts to conceal malicious activity.

**Actions**:
- Check for manipulation of process listings, logs, or network connections (e.g., via unlink() on logs).
- Detect rootkit-like behaviors, such as hooking system calls or hiding files/processes.
- Use eBPF or Fibratus to verify process and file visibility against expected states.

**Container Context**: Ensure monitoring pierces container isolation to detect host-level hiding attempts.

### 9. Container-Specific Evaluation

**Objective**: Catch behaviors exploiting or escaping containerization.

**Actions**:
- Monitor namespace interactions to ensure the binary respects isolation (e.g., no attempts to join host namespaces).
- Check volume mounts for suspicious activity affecting the host.
- Detect breakout attempts, such as exploiting kernel vulnerabilities or misconfigured capabilities.
- Use eBPF to trace interactions with container runtime (e.g., Docker socket access).

## Implementation with Frameworks

### Linux (eBPF/BCC):
- Use eBPF to hook system calls (tracepoints, kprobes) and network events (socket filters).
- Leverage BCC for high-level scripting and analysis of traced data.

### Windows (BPF/Fibratus):
- Use BPF for Windows to monitor system calls and network packets.
- Employ Fibratus for detailed process, file, and registry event tracking.

**Data Collection**: Log events in real-time, aggregating them for analysis.

## Enhancing Detection

- **Baseline Comparison**: Establish a profile of normal behavior for benign binaries in similar containers and flag deviations.
- **Threat Intelligence**: Integrate feeds to cross-reference IPs, domains, or file hashes with known threats.
- **Analysis**: Apply rule-based thresholds or machine learning to identify anomalies and generate alerts.

## Using BCC for Inspector Gadget Implementation

This section outlines how to leverage BCC (BPF Compiler Collection) to implement Inspector Gadget's binary monitoring capabilities.

### 1. Basic Functionality

**Purpose**: Establish a foundation for hooking into binary processes.

**How BCC Helps**: 
- Provides Python or C interfaces to write eBPF programs
- Attaches to kernel tracepoints, kprobes, or uprobes
- Intercepts system calls or library functions executed by containerized binaries

**Implementation**:
- Install BCC on a Linux system with eBPF support
- Write a BCC script to trace execve() calls
- Log details like PID, command name, and arguments for container processes

**Example Code**:
```python
from bcc import BPF

# eBPF program to trace execve
prog = """
#include <uapi/linux/ptrace.h>
int trace_execve(struct pt_regs *ctx) {
    char comm[16];
    bpf_get_current_comm(&comm, sizeof(comm));
    bpf_trace_printk("Binary executed: %s\\n", comm);
    return 0;
}
"""
b = BPF(text=prog)
b.attach_kprobe(event="sys_execve", fn_name="trace_execve")
b.trace_print()
```

**Outcome**: Real-time monitoring of binary executions with minimal setup complexity.

### 2. Filtering

**Purpose**: Focus monitoring on specific events or processes.

**How BCC Helps**:
- Supports eBPF maps (e.g., BPF_HASH) for state tracking
- Enables filtering logic to selectively process events
- Allows filtering by PID, container namespace, or system call type

**Implementation**:
- Extend scripts with conditional logic
- Use BPF_PERF_OUTPUT for efficient data transfer to user space
- Add dynamic rules via command-line arguments or config files

**Example Code**:
```python
prog = """
BPF_HASH(pid_filter, u32, u8);
int trace_execve(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    u8 *val = pid_filter.lookup(&pid);
    if (val) {
        char comm[16];
        bpf_get_current_comm(&comm, sizeof(comm));
        bpf_trace_printk("Filtered PID %d: %s\\n", pid, comm);
    }
    return 0;
}
"""
b = BPF(text=prog)
b["pid_filter"][1234] = 1  # Only trace PID 1234
b.attach_kprobe(event="sys_execve", fn_name="trace_execve")
```

**Outcome**: Reduced noise by targeting only relevant processes or events.

### 3. Security

**Purpose**: Ensure Inspector Gadget operates safely in containerized environments.

**How BCC Helps**:
- eBPF programs are sandboxed by the kernel's verifier
- Prevents unsafe operations automatically
- User-space component can be secured with standard practices

**Implementation**:
- Run BCC scripts with minimal privileges (e.g., only CAP_SYS_ADMIN when needed)
- Validate all inputs to prevent injection attacks
- Avoid overly permissive eBPF programs that could be exploited

**Outcome**: A secure monitoring system that leverages eBPF's safety while protecting the user-space interface.

### 4. Analysis

**Purpose**: Interpret traced data for insights or threat detection.

**How BCC Helps**:
- Outputs data via bpf_trace_printk() (simple) or perf events (complex)
- User-space code can analyze and correlate events
- Supports complex data structures for pattern recognition

**Implementation**:
- Add logic to detect suspicious patterns
- Integrate with external tools (e.g., SIEM or threat databases)
- Implement anomaly detection algorithms

**Example Code**:
```python
prog = """
BPF_HASH(exec_count, u32, u64);
int trace_execve(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    u64 *count = exec_count.lookup_or_init(&pid, &0);
    (*count)++;
    if (*count > 10) {
        bpf_trace_printk("PID %d executed too often: %llu\\n", pid, *count);
    }
    return 0;
}
"""
```

**Outcome**: Real-time detection of anomalies or malicious behavior.

### 5. Performance

**Purpose**: Minimize Inspector Gadget's impact on system resources.

**How BCC Helps**:
- eBPF is inherently low-overhead
- BCC provides tools to profile and optimize programs
- Supports efficient data structures and algorithms

**Implementation**:
- Optimize eBPF code with techniques like tail calls or efficient map usage
- Use BCC's profiling tools or Linux perf to measure impact
- Implement configurable tracing granularity

**Outcome**: Efficient monitoring suitable for high-throughput container environments.

### Implementation Roadmap

**Phase 1: Setup and Research**
- Install BCC and verify eBPF kernel support
- Study BCC examples for process tracing
- Deliverable: A basic script tracing execve() in a container

**Phase 2: Core Development**
- Write eBPF programs to trace key system calls (e.g., open(), fork())
- Add basic filtering by container or process
- Deliverable: A working script monitoring containerized binaries

**Phase 3: Enhanced Features**
- Implement advanced filtering (e.g., by syscall arguments)
- Add security controls (e.g., input validation)
- Introduce basic analysis (e.g., anomaly detection)
- Deliverable: A feature-rich Inspector Gadget with security and analysis capabilities

**Phase 4: Optimization and Testing**
- Optimize eBPF code for performance
- Test under load and audit for security
- Deliverable: An efficient, validated Inspector Gadget with performance metrics

**Phase 5: Deployment and Documentation**
- Write usage docs and setup instructions
- Package the script for deployment
- Deploy in a test environment and refine based on feedback
- Deliverable: A deployable, well-documented Inspector Gadget 