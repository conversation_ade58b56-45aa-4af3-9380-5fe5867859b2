# Hooker Pill Test Script Documentation

## Overview

The `run_hooker_pill_test.sh` script is designed to run tests for the hooker_pill library in a Vagrant VM environment. It follows the VM testing architecture described in the project documentation at `docs/testing/vm_testing.md`.

## Prerequisites

- Vagrant VM set up and running
- Nix environment for building the hooker_pill library
- Access to the VM via SSH or gRPC

## Script Location

The script is located at `test/vm/run_hooker_pill_test.sh`.

## Usage

```bash
./test/vm/run_hooker_pill_test.sh [options]
```

### Options

- `--port PORT`: Port for Vagrant gRPC (default: 1234 from .env or 1234)
- `--host HOST`: Host for Vagrant gRPC (default: localhost from .env or localhost)
- `--rebuild`: Force rebuild of the hooker_pill library
- `--local`: Run local tests without Vagrant
- `--direct-ssh`: Use direct SSH instead of gRPC (fallback method)
- `--nix-store DIR`: Directory to use for Nix store (default: /mnt/volume_fra1_01/nix-store)
- `--help`: Show help message

## Workflow

1. **Environment Setup**:
   - Loads environment variables from `.env` file if it exists
   - Parses command-line arguments
   - Sets up default values for various parameters

2. **Building the Hooker Pill Library**:
   - Checks if the library needs to be rebuilt
   - Uses nix-shell to build the library if available
   - Handles the case where `/nix` is a symlink by using an alternative Nix store location
   - Falls back to direct cargo build if nix-shell is not available

3. **VM Communication**:
   - Checks if the Vagrant gRPC server is accessible
   - Falls back to direct SSH method if gRPC is not available
   - Creates necessary directories in the VM
   - Copies the hooker_pill library and test script to the VM

4. **Test Execution**:
   - Runs the test script in the VM
   - Collects results from the VM
   - Copies results back to the project directory

5. **Results Analysis**:
   - Prints a summary of the test results
   - Provides the location of the results for further analysis

## Current Implementation Status

The script has been implemented with all the necessary features, but there are some issues that need to be resolved:

1. **Nix Environment Issues**:
   - The `/nix` directory is a symlink, which is not allowed for the Nix store
   - An alternative Nix store location at `/mnt/volume_fra1_01/nix-store` is being used
   - Need to verify if this approach works

2. **VM Communication**:
   - Vagrant VM is configured with port forwarding: `guest: 40000, host: 1234`
   - Need to verify if the gRPC service is running and accessible

3. **Test Script Execution**:
   - The `test/vm/artifacts/hooker_pill_test.sh` script needs to be created or verified
   - Need to ensure the test script can properly execute in the VM environment

## Troubleshooting

### Nix Store Symlink Issue

If you encounter the error "the path '/nix' is a symlink; this is not allowed for the Nix store and its parent directories", you can use the `--nix-store` option to specify an alternative location for the Nix store:

```bash
./test/vm/run_hooker_pill_test.sh --nix-store /path/to/alternative/nix-store
```

### gRPC Connection Issues

If you encounter issues connecting to the gRPC service, you can use the `--direct-ssh` option to use the direct SSH method instead:

```bash
./test/vm/run_hooker_pill_test.sh --direct-ssh
```

### Port Configuration

If the Vagrant VM is using a different port for the gRPC service, you can specify it using the `--port` option:

```bash
./test/vm/run_hooker_pill_test.sh --port 1234
```

## Future Improvements

1. Add support for running multiple test cases
2. Improve error handling and recovery
3. Add more detailed logging and debugging options
4. Integrate with CI/CD pipelines 