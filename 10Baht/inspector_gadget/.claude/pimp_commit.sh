#!/bin/bash

# <PERSON><PERSON><PERSON> to commit and push the Pimp.py Vagrant VM integration improvements

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Set branch name
BRANCH_NAME="feature/pimp-vagrant-improvements"

# Display separator
echo -e "${YELLOW}=======================================================${NC}"
echo -e "${GREEN}Committing Pimp.py Vagrant VM Integration Improvements${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Create a new branch
echo -e "${YELLOW}Creating new branch: ${BRANCH_NAME}${NC}"
git checkout -b $BRANCH_NAME

# Add files - assuming files have already been modified
echo -e "${GREEN}Adding files to git...${NC}"
git add .claude/pimp_vagrant_integration_prd.md
git add .claude/changelog.md
git add .claude/roadmap.md
git add .claude/ROADMAP.md
git add tools/pimp/vagrant_manager.py
git add tools/pimp/config/default_config.yaml
git add tools/pimp/requirements.txt
git add tools/pimp/shell.nix
git add tools/pimp/run.sh

# Commit with the message from the commit_message.md file
echo -e "${GREEN}Committing changes...${NC}"
COMMIT_MSG=$(cat .claude/pimp_commit_message.md)
git commit -m "$COMMIT_MSG"

# Offer to push to remote
read -p "Do you want to push to remote? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}Pushing to remote...${NC}"
    git push -u origin $BRANCH_NAME
    echo -e "${GREEN}Successfully pushed to ${BRANCH_NAME}${NC}"
else
    echo -e "${YELLOW}Changes committed locally but not pushed.${NC}"
    echo -e "To push later, run: git push -u origin $BRANCH_NAME"
fi

echo -e "${GREEN}Done!${NC}" 