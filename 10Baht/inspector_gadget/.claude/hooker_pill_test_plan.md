# Hooker Pill Test Plan

## Overview

This document outlines the plan for testing the hooker_pill data event logger component of Inspector Gadget, which is responsible for intercepting system calls and sending the data to Elasticsearch. We will leverage the existing gRPC-based testing infrastructure that uses Vagrant VMs.

## Existing Infrastructure

Inspector Gadget already has a robust testing infrastructure that includes:

1. **Vagrant VMs**: Virtual machines for testing in isolated environments
2. **gRPC Service**: A service running on port 40000 that allows communication between the host and the VM
3. **Test Scripts**: Various scripts for running different types of tests
4. **Result Collection**: Mechanisms for collecting and analyzing test results

## Test Approach

We will extend the existing infrastructure to test the hooker_pill data event logger with the following approach:

1. **Create a new test type**: Add a new test type for the hooker_pill data event logger
2. **Implement test scripts**: Create scripts to test the hooker_pill's ability to intercept system calls and send data to Elasticsearch
3. **Verify data flow**: Confirm that data is correctly sent to Elasticsearch and can be visualized in Kibana

## Implementation Steps

### 1. Add a New Test Type

Add a new test type to the gRPC service by modifying `test/vm/grpc_server.rs`:

```rust
// In the TestType enum
pub enum TestType {
    Basic = 0,
    Filtering = 1,
    Security = 2,
    Analysis = 3,
    Performance = 4,
    HookerPill = 5,  // New test type
}
```

### 2. Create Test Scripts

Create a new test script at `test/vm/artifacts/hooker_pill_test.sh`:

```bash
#!/bin/bash
# Test script for hooker_pill data event logger

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Configuration
TEST_DIR="/test-temp/vm-test"
RESULTS_DIR="${TEST_DIR}/results"
ES_URL="http://localhost:9200"
ES_INDEX="inspector_gadget"

echo -e "${YELLOW}Running hooker_pill test...${NC}"

# Create a simple test application
mkdir -p /tmp/test_app
cat > /tmp/test_app/test_app.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <string.h>

int main() {
    printf("Starting test application...\n");
    
    // Create a file
    int fd = open("test.txt", O_CREAT | O_WRONLY, 0644);
    if (fd < 0) {
        perror("open");
        return 1;
    }
    
    // Write to the file
    const char *data = "Hello, Inspector Gadget!";
    write(fd, data, strlen(data));
    
    // Close the file
    close(fd);
    
    // Read the file
    fd = open("test.txt", O_RDONLY);
    if (fd < 0) {
        perror("open");
        return 1;
    }
    
    char buffer[100];
    ssize_t bytes_read = read(fd, buffer, sizeof(buffer) - 1);
    if (bytes_read > 0) {
        buffer[bytes_read] = '\0';
        printf("Read from file: %s\n", buffer);
    }
    
    close(fd);
    
    // Remove the file
    unlink("test.txt");
    
    printf("Test application completed.\n");
    return 0;
}
EOF

# Compile the test application
gcc -o /tmp/test_app/test_app /tmp/test_app/test_app.c

# Run with LD_PRELOAD
export LD_PRELOAD="${TEST_DIR}/bin/libhooker_pill.so"
export INSPECTOR_GADGET_LOG_FILE="${RESULTS_DIR}/hooker_pill_test.log"
export INSPECTOR_GADGET_ES_URL="${ES_URL}"
export INSPECTOR_GADGET_ES_INDEX="${ES_INDEX}"

# Run the test application
/tmp/test_app/test_app

# Check if the log file was created
if [ -f "${RESULTS_DIR}/hooker_pill_test.log" ]; then
    echo -e "${GREEN}Test completed successfully. Log file created.${NC}"
else
    echo -e "${RED}No log file was created.${NC}"
    exit 1
fi

# Check Elasticsearch for data
echo -e "${YELLOW}Checking Elasticsearch for data...${NC}"
curl -s "${ES_URL}/${ES_INDEX}/_search?pretty" > "${RESULTS_DIR}/hooker_pill_es_data.json"

# Count the number of events
EVENT_COUNT=$(grep -c "\"_id\"" "${RESULTS_DIR}/hooker_pill_es_data.json")
if [ "${EVENT_COUNT}" -gt 0 ]; then
    echo -e "${GREEN}Found ${EVENT_COUNT} events in Elasticsearch.${NC}"
else
    echo -e "${RED}No events found in Elasticsearch.${NC}"
    exit 1
fi

# Create a JSON result file
cat > "${RESULTS_DIR}/hooker_pill_test.json" << EOF
{
  "test_name": "hooker_pill_test",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "success": true,
  "log_file_created": true,
  "elasticsearch_events": ${EVENT_COUNT},
  "syscalls": [
    {"name": "open", "count": $(grep -c "open" "${RESULTS_DIR}/hooker_pill_test.log")},
    {"name": "write", "count": $(grep -c "write" "${RESULTS_DIR}/hooker_pill_test.log")},
    {"name": "read", "count": $(grep -c "read" "${RESULTS_DIR}/hooker_pill_test.log")},
    {"name": "close", "count": $(grep -c "close" "${RESULTS_DIR}/hooker_pill_test.log")},
    {"name": "unlink", "count": $(grep -c "unlink" "${RESULTS_DIR}/hooker_pill_test.log")}
  ]
}
EOF

echo -e "${GREEN}Test completed successfully.${NC}"
exit 0
```

### 3. Update the gRPC Server

Modify the `run_test` method in `test/vm/grpc_server.rs` to handle the new test type:

```rust
// In the run_test method
let script_path = match test_type {
    TestType::Basic => "/test-temp/vm-test/basic_test.sh",
    TestType::Filtering => "/test-temp/vm-test/filtering_test.sh",
    TestType::Security => "/test-temp/vm-test/security_test.sh",
    TestType::Analysis => "/test-temp/vm-test/analysis_test.sh",
    TestType::Performance => "/test-temp/vm-test/performance_test.sh",
    TestType::HookerPill => "/test-temp/vm-test/hooker_pill_test.sh",
};

// And in the results_path determination
let results_path = match test_type {
    TestType::Basic => "/test-temp/vm-test/results/basic_test.json",
    TestType::Filtering => "/test-temp/vm-test/results/filtering_test.json",
    TestType::Security => "/test-temp/vm-test/results/security_test.json",
    TestType::Analysis => "/test-temp/vm-test/results/analysis_test.json",
    TestType::Performance => "/test-temp/vm-test/results/performance_test.log",
    TestType::HookerPill => "/test-temp/vm-test/results/hooker_pill_test.json",
};
```

### 4. Update the VM Setup Script

Modify `test/vm/setup_linux_vm_test.sh` to copy the hooker_pill library to the VM:

```bash
# Copy hooker_pill library to the VM
echo -e "${YELLOW}Copying hooker_pill library to the VM...${NC}"
vagrant ssh "${VAGRANT_VM_NAME}" -c "mkdir -p ${VM_TEST_TEMP_DIR}/bin"
vagrant ssh "${VAGRANT_VM_NAME}" -c "cp /vagrant/target/release/libhooker_pill.so ${VM_TEST_TEMP_DIR}/bin/"
```

### 5. Update the Client

Add a new test method to `test/vm/grpc_client.rs`:

```rust
#[test]
fn test_run_hooker_pill_test() {
    let rt = Runtime::new().unwrap();
    let result = rt.block_on(async {
        let mut client = GrpcClient::new("localhost:40000").await?;
        client.run_test(TestType::HookerPill, 30).await
    });
    assert!(result.is_ok(), "Failed to run hooker_pill test: {:?}", result.err());
    if let Ok(status) = result {
        assert_eq!(status, TestStatus::Success, "Hooker pill test failed with status: {:?}", status);
    }
}
```

## Running the Tests

To run the tests:

1. Start the Vagrant VM:
   ```bash
   cd test-environments/vagrant
   vagrant up linux
   ```

2. Set up the test environment:
   ```bash
   cd test/vm
   ./setup_linux_vm_test.sh
   ```

3. Run the hooker_pill test:
   ```bash
   ./run_test.sh --test-type hooker_pill
   ```

4. Check the results:
   ```bash
   ./analyze_results.sh --results-path /test-temp/vm-test/results/hooker_pill_test.json
   ```

## Expected Results

The test should:

1. Successfully intercept system calls made by the test application
2. Log the intercepted calls to a log file
3. Send the intercepted calls to Elasticsearch
4. Verify that the data is present in Elasticsearch

## Conclusion

By leveraging the existing gRPC-based testing infrastructure, we can efficiently test the hooker_pill data event logger component of Inspector Gadget. This approach ensures that the component works correctly in isolation and integrates properly with Elasticsearch. 