#!/bin/bash
# fix-vbox.sh - Fix library paths for VirtualBox
#
# This script temporarily modifies LD_LIBRARY_PATH to use the host
# system's libraries when running VirtualBox commands, which helps
# avoid conflicts with nix-shell libraries.

set -e

# Print out the command we're about to run
echo "Running command: $@"
echo "------------------------------"

# Host system library paths
HOST_LIB_PATH="/usr/lib:/lib:/usr/lib/x86_64-linux-gnu:/lib/x86_64-linux-gnu:/usr/local/lib"

# Save original library path
ORIGINAL_LD_LIBRARY_PATH="$LD_LIBRARY_PATH"
ORIGINAL_LIBRARY_PATH="$LIBRARY_PATH"
ORIGINAL_PATH="$PATH"

echo "Original LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
echo "Original LIBRARY_PATH: $LIBRARY_PATH"
echo "Original PATH: $PATH"

# Modify PATH to remove nix entries
export PATH=$(echo "$PATH" | tr ':' '\n' | grep -v "nix" | tr '\n' ':')

# Add system binary paths if needed
if [[ ! "$PATH" =~ /usr/bin ]]; then
    export PATH="$PATH:/usr/bin"
fi

if [[ ! "$PATH" =~ /bin ]]; then
    export PATH="$PATH:/bin"
fi

# Modify library paths
export LD_LIBRARY_PATH="$HOST_LIB_PATH"
export LIBRARY_PATH="$HOST_LIB_PATH"

echo "Modified LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
echo "Modified LIBRARY_PATH: $LIBRARY_PATH"
echo "Modified PATH: $PATH"
echo "------------------------------"

# Unset potentially problematic nix environment variables
unset NIX_PATH
unset NIX_PROFILES
unset NIX_SSL_CERT_FILE
unset NIX_STORE

# Run the command with the fixed paths
"$@"
RESULT=$?

# Restore original paths
export LD_LIBRARY_PATH="$ORIGINAL_LD_LIBRARY_PATH"
export LIBRARY_PATH="$ORIGINAL_LIBRARY_PATH"
export PATH="$ORIGINAL_PATH"

# Return the original command's exit code
exit $RESULT 