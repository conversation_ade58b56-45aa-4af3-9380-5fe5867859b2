#!/bin/bash
# run-alpine-docker-test.sh - <PERSON><PERSON><PERSON> to run hooker_pill tests in Alpine Docker

set -e

# Define directories
INSPECTOR_DIR="/home/<USER>/cdev/inspector_gadget"
DOCKER_DIR="${INSPECTOR_DIR}/test-environments/docker"
RESULTS_DIR="${INSPECTOR_DIR}/.claude/test_results/alpine_docker"

# Ensure directories exist
mkdir -p "${RESULTS_DIR}"
mkdir -p "${DOCKER_DIR}/results"

# Function to print colored output
print_color() {
    COLOR=$1
    shift
    echo -e "\033[${COLOR}m$@\033[0m"
}

print_color "1;36" "=========================================================="
print_color "1;36" "Running hooker_pill tests in Alpine Docker environment"
print_color "1;36" "=========================================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_color "1;31" "Error: Docker is not installed or not in PATH"
    print_color "1;31" "Please install Docker before proceeding"
    exit 1
fi

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_color "1;33" "Warning: docker-compose not found, will use Docker directly"
    USE_COMPOSE=false
else
    USE_COMPOSE=true
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    print_color "1;31" "Error: Docker daemon is not running"
    print_color "1;31" "Please start Docker service: sudo systemctl start docker"
    exit 1
fi

# Process arguments
RUN_MODE="test"
if [ "$1" == "shell" ]; then
    RUN_MODE="shell"
    print_color "1;33" "Running in shell mode - will open interactive shell"
elif [ "$1" == "persist" ]; then
    RUN_MODE="persist"
    print_color "1;33" "Running in persist mode - will keep container running after tests"
elif [ "$1" == "build" ]; then
    RUN_MODE="build"
    print_color "1;33" "Build mode - will build images but not run tests"
fi

# Build the Docker image
cd "${INSPECTOR_DIR}"
print_color "1;36" "Building Alpine Docker image..."
docker build -t hooker-alpine-test -f "${DOCKER_DIR}/Dockerfile.alpine" .

# If only building, exit here
if [ "$RUN_MODE" == "build" ]; then
    print_color "1;32" "Build completed successfully!"
    exit 0
fi

# Create the results directory and set permissions
mkdir -p "${DOCKER_DIR}/results"
chmod 777 "${DOCKER_DIR}/results"

# Run the tests based on chosen method
if [ "$USE_COMPOSE" == "true" ] && [ "$RUN_MODE" == "test" ]; then
    print_color "1;36" "Running tests using docker-compose..."
    cd "${DOCKER_DIR}"
    docker-compose -f docker-compose.alpine.yml up -d
    
    # Wait for container to finish
    print_color "1;33" "Waiting for tests to complete..."
    docker wait hooker-alpine-test
    
    # View logs
    print_color "1;36" "Test output:"
    docker logs hooker-alpine-test
    
    # Shutdown the stack
    print_color "1;36" "Shutting down containers..."
    docker-compose -f docker-compose.alpine.yml down
else
    # Run using direct Docker commands
    print_color "1;36" "Running tests using Docker directly..."
    cd "${INSPECTOR_DIR}"
    
    if [ "$RUN_MODE" == "shell" ]; then
        print_color "1;36" "Starting interactive shell in Alpine container..."
        docker run --privileged -v "$(pwd):/inspector_gadget" -v "${DOCKER_DIR}/results:/opt/hooker_test/results" -it hooker-alpine-test shell
    elif [ "$RUN_MODE" == "persist" ]; then
        print_color "1;36" "Running tests with persistence..."
        docker run --privileged -v "$(pwd):/inspector_gadget" -v "${DOCKER_DIR}/results:/opt/hooker_test/results" -d --name hooker-alpine-test-instance hooker-alpine-test persist
        print_color "1;32" "Container is running in the background"
        print_color "1;32" "To view logs: docker logs hooker-alpine-test-instance"
        print_color "1;32" "To stop: docker stop hooker-alpine-test-instance"
    else
        print_color "1;36" "Running tests in normal mode..."
        docker run --privileged -v "$(pwd):/inspector_gadget" -v "${DOCKER_DIR}/results:/opt/hooker_test/results" --name hooker-alpine-test-instance hooker-alpine-test
        
        # Copy results
        print_color "1;36" "Copying test results..."
        docker cp hooker-alpine-test-instance:/opt/hooker_test/results/. "${RESULTS_DIR}/"
        
        # Cleanup
        print_color "1;36" "Cleaning up container..."
        docker rm hooker-alpine-test-instance
    fi
fi

# Show results if available
if [ -f "${RESULTS_DIR}/summary.txt" ] && [ "$RUN_MODE" == "test" ]; then
    print_color "1;36" "=========================================================="
    print_color "1;36" "Test Summary:"
    print_color "1;36" "=========================================================="
    cat "${RESULTS_DIR}/summary.txt"
    
    # Show structured test results
    if [ -f "${RESULTS_DIR}/test_results.log" ]; then
        print_color "1;36" "Detailed results available in: ${RESULTS_DIR}/test_results.log"
    fi
fi

print_color "1;32" "Alpine Docker testing completed!"
print_color "1;32" "Results directory: ${RESULTS_DIR}"
print_color "1;36" "==========================================================" 