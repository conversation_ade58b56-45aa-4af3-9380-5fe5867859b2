#!/bin/bash
# vbox-wrapper.sh - Run VirtualBox commands using host system's libraries
#
# This wrapper script temporarily cleans the PATH to remove nix-specific entries
# which may cause conflicts with system libraries when running Vagrant/VirtualBox.

# Print out the command we're about to run
echo "Running command: vagrant $@"
echo "------------------------------"

# Save the original PATH
ORIGINAL_PATH="$PATH"
echo "Original PATH: $PATH"

# Remove nix-related paths from PATH
export PATH=$(echo "$PATH" | tr ':' '\n' | grep -v "nix" | tr '\n' ':')
echo "Cleaned PATH: $PATH"

# Add system binary paths if they're not in PATH
if [[ ! "$PATH" =~ /usr/bin ]]; then
    export PATH="$PATH:/usr/bin"
fi

if [[ ! "$PATH" =~ /bin ]]; then
    export PATH="$PATH:/bin"
fi

if [[ ! "$PATH" =~ /usr/local/bin ]]; then
    export PATH="$PATH:/usr/local/bin"
fi

echo "Final PATH: $PATH"
echo "------------------------------"

# Unset potentially problematic nix environment variables
unset NIX_PATH
unset NIX_PROFILES
unset NIX_SSL_CERT_FILE
unset NIX_STORE

# Run vagrant command with host system's libraries
vagrant "$@"
RESULT=$?

# Restore original PATH
export PATH="$ORIGINAL_PATH"

# Return the original command's exit code
exit $RESULT 