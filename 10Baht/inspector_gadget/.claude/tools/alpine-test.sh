#!/bin/bash
# alpine-test.sh - Test hooker_pill in Alpine Linux VM
#
# This script creates an Alpine Linux Vagrant environment and
# tests the hooker_pill components in it, while avoiding library
# conflicts between nix-shell and the host system.

set -e
echo "Starting Alpine Linux hooker_pill test environment setup..."

# Define directories
INSPECTOR_DIR="/home/<USER>/cdev/inspector_gadget"
VAGRANT_DIR="$INSPECTOR_DIR/test-environments/vagrant"
TOOLS_DIR="$INSPECTOR_DIR/.claude/tools"
RESULTS_DIR="$INSPECTOR_DIR/.claude/test_results/alpine"
HOOKER_PILL_DIR="$INSPECTOR_DIR/hooker_pill"

# Create directories if needed
mkdir -p "$VAGRANT_DIR"
mkdir -p "$RESULTS_DIR"

# Create Alpine Vagrantfile if it doesn't exist
if [ ! -f "$VAGRANT_DIR/Vagrantfile.alpine" ]; then
  echo "Creating Alpine Vagrantfile..."
  cat > "$VAGRANT_DIR/Vagrantfile.alpine" << 'EOF'
# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  config.vm.box = "alpine/alpine64"
  config.vm.hostname = "inspector-gadget-alpine"
  
  # Explicitly set the provider to VirtualBox
  config.vm.provider "virtualbox"
  
  # Forward port 40000 for gRPC
  config.vm.network "forwarded_port", guest: 40000, host: 40010
  
  # Forward port 9200 for Elasticsearch
  config.vm.network "forwarded_port", guest: 9200, host: 9210
  
  # Forward port 5601 for Kibana
  config.vm.network "forwarded_port", guest: 5601, host: 5610
  
  # Sync the project directory
  config.vm.synced_folder ".", "/vagrant"
  
  # VM configuration
  config.vm.provider "virtualbox" do |vb|
    vb.memory = "1024"
    vb.cpus = 1
    vb.name = "inspector_gadget_alpine_vm"
    # Enable nested virtualization features for better performance
    vb.customize ["modifyvm", :id, "--nestedpaging", "on"]
    vb.customize ["modifyvm", :id, "--largepages", "on"]
    vb.customize ["modifyvm", :id, "--vtxvpid", "on"]
  end
  
  # Provision the VM
  config.vm.provision "shell", inline: <<-SHELL
    # Update package lists
    apk update
    
    # Install dependencies for eBPF
    apk add build-base linux-headers elfutils-dev clang llvm bcc bcc-dev
    apk add git python3 py3-pip rust cargo openssl-dev bash curl wget
    
    # Create test directories
    mkdir -p /opt/hooker_test
    mkdir -p /opt/hooker_test/results
    
    # Verify eBPF functionality
    echo "Checking eBPF availability..."
    if [ -d "/sys/fs/bpf" ]; then
      echo "BPF filesystem mounted - eBPF is available"
    else
      echo "Mounting BPF filesystem"
      mount -t bpf bpffs /sys/fs/bpf
    fi
    
    # Check kernel version
    echo "Kernel version: $(uname -r)"
    
    echo "VM provisioning completed."
  SHELL
end
EOF
fi

# Link the Alpine Vagrantfile
cd "$VAGRANT_DIR"
ln -sf Vagrantfile.alpine Vagrantfile

# Start the VM using the host system (without nix-shell)
echo "Starting Alpine VM using host system's VirtualBox..."

# Save the original PATH
ORIGINAL_PATH="$PATH"

# Remove nix-related paths from PATH
export PATH=$(echo "$PATH" | tr ':' '\n' | grep -v "nix" | tr '\n' ':')

# Add system binary paths if needed
if [[ ! "$PATH" =~ /usr/bin ]]; then
    export PATH="$PATH:/usr/bin"
fi

if [[ ! "$PATH" =~ /bin ]]; then
    export PATH="$PATH:/bin"
fi

# Unset potentially problematic nix environment variables
unset NIX_PATH
unset NIX_PROFILES
unset NIX_SSL_CERT_FILE
unset NIX_STORE

# Start the VM (explicitly specify the provider)
vagrant up --provider=virtualbox || {
  echo "Failed to start VM. See error above."
  # Restore PATH
  export PATH="$ORIGINAL_PATH"
  exit 1
}

# Create Alpine test script
echo "Creating Alpine test script..."
cat > /tmp/alpine_test.sh << 'EOF'
#!/bin/sh
# Alpine Linux test script for hooker_pill functionality

set -e
echo "Starting Alpine Linux Hooker Pill Test..."

# Configure environment variables
TEST_DIR="/opt/hooker_test"
RESULTS_DIR="${TEST_DIR}/results"

# Create directories
mkdir -p ${RESULTS_DIR}

# Record system information
echo "=== System Information ===" > ${RESULTS_DIR}/system_info.txt
uname -a >> ${RESULTS_DIR}/system_info.txt
apk info | grep 'linux-' >> ${RESULTS_DIR}/system_info.txt
echo "eBPF filesystem:" >> ${RESULTS_DIR}/system_info.txt
ls -la /sys/fs/bpf >> ${RESULTS_DIR}/system_info.txt 2>/dev/null || echo "Not mounted" >> ${RESULTS_DIR}/system_info.txt

# Function to log test results
log_result() {
  echo "===== $1 =====" >> ${RESULTS_DIR}/test_results.log
  echo "Test: $1" >> ${RESULTS_DIR}/test_results.log
  echo "Status: $2" >> ${RESULTS_DIR}/test_results.log
  echo "Output:" >> ${RESULTS_DIR}/test_results.log
  cat $3 >> ${RESULTS_DIR}/test_results.log
  echo "" >> ${RESULTS_DIR}/test_results.log
  echo "" >> ${RESULTS_DIR}/test_results.log
}

# Build the hooker_pill (if source is available)
if [ -d "/vagrant/hooker_pill" ]; then
  echo "Building hooker_pill..."
  cd /vagrant/hooker_pill
  cargo build --release

  # Copy the hooker_pill library to test directory
  cp /vagrant/hooker_pill/target/release/libhooker_pill.so ${TEST_DIR}/
  echo "Hooker pill library built and copied to test directory"
else
  echo "Hooker pill source directory not found, skipping build"
fi

# Test 1: Basic LSM hook test
echo "=== Running LSM Hook Test ==="
  
# Test LSM hook by accessing a file
touch ${TEST_DIR}/test_file.txt
echo "This is a test file" > ${TEST_DIR}/test_file.txt
  
# Run a program with the hooker_pill library preloaded
echo "Running test program with LSM hook..."
TEST_PROGRAM="cat ${TEST_DIR}/test_file.txt"
LD_PRELOAD=${TEST_DIR}/libhooker_pill.so ${TEST_PROGRAM} > ${RESULTS_DIR}/lsm_output.txt 2>&1 || true
  
# Check if hook was triggered
if grep -q "hook triggered" ${RESULTS_DIR}/lsm_output.txt; then
  echo "LSM hook test succeeded"
  log_result "LSM Hook Test" "SUCCESS" "${RESULTS_DIR}/lsm_output.txt"
else
  echo "LSM hook test output:" 
  cat ${RESULTS_DIR}/lsm_output.txt
  echo "LSM hook might not have triggered correctly, but continuing..."
  log_result "LSM Hook Test" "WARNING" "${RESULTS_DIR}/lsm_output.txt"
fi

# Test 2: XDP hook test
echo "=== Running XDP Hook Test ==="
  
# Get network interface
IFACE=$(ip -o -4 route show to default | awk '{print $5}' | head -1)
if [ -z "${IFACE}" ]; then
  echo "No network interface found, using loopback"
  IFACE="lo"
fi
  
echo "Using network interface: ${IFACE}"
  
# Run a ping command to generate some traffic
ping -c 5 127.0.0.1 > /dev/null 2>&1
  
# Run a program with the hooker_pill library preloaded
echo "Running test program with XDP hook..."
TEST_PROGRAM="ping -c 3 127.0.0.1"
HOOKER_TYPE=XDP HOOKER_INTERFACE=${IFACE} LD_PRELOAD=${TEST_DIR}/libhooker_pill.so ${TEST_PROGRAM} > ${RESULTS_DIR}/xdp_output.txt 2>&1 || true
  
# Check results
echo "XDP hook test complete"
log_result "XDP Hook Test" "COMPLETE" "${RESULTS_DIR}/xdp_output.txt"

# Test 3: Tracepoint hook test
echo "=== Running Tracepoint Hook Test ==="
  
# Run a program with the hooker_pill library preloaded
echo "Running test program with Tracepoint hook..."
TEST_PROGRAM="ls -la ${TEST_DIR}"
HOOKER_TYPE=TRACEPOINT LD_PRELOAD=${TEST_DIR}/libhooker_pill.so ${TEST_PROGRAM} > ${RESULTS_DIR}/tracepoint_output.txt 2>&1 || true
  
# Check results
echo "Tracepoint hook test complete"
log_result "Tracepoint Hook Test" "COMPLETE" "${RESULTS_DIR}/tracepoint_output.txt"

# Test 4: Uprobe hook test
echo "=== Running Uprobe Hook Test ==="
  
# Create a simple test program
cat > ${TEST_DIR}/uprobe_test.c << EOF
#include <stdio.h>
#include <stdlib.h>

void target_function(int value) {
  printf("Target function called with value: %d\n", value);
}

int main() {
  printf("Starting uprobe test program\n");
  target_function(42);
  return 0;
}
EOF
  
# Compile the test program
gcc -o ${TEST_DIR}/uprobe_test ${TEST_DIR}/uprobe_test.c
  
# Run the test program with the hooker_pill library preloaded
echo "Running test program with Uprobe hook..."
HOOKER_TYPE=UPROBE HOOKER_TARGET="${TEST_DIR}/uprobe_test" HOOKER_FUNCTION="target_function" LD_PRELOAD=${TEST_DIR}/libhooker_pill.so ${TEST_DIR}/uprobe_test > ${RESULTS_DIR}/uprobe_output.txt 2>&1 || true
  
# Check results
echo "Uprobe hook test complete"
log_result "Uprobe Hook Test" "COMPLETE" "${RESULTS_DIR}/uprobe_output.txt"

# Create a summary file
echo "=== Test Results Summary ===" > ${RESULTS_DIR}/summary.txt
echo "LSM Test: Complete" >> ${RESULTS_DIR}/summary.txt
echo "XDP Test: Complete" >> ${RESULTS_DIR}/summary.txt
echo "Tracepoint Test: Complete" >> ${RESULTS_DIR}/summary.txt
echo "Uprobe Test: Complete" >> ${RESULTS_DIR}/summary.txt

# Create a tarball of the results
tar -czf /tmp/test_results.tar.gz -C ${RESULTS_DIR} .
chmod 644 /tmp/test_results.tar.gz

# Echo success
echo "All tests completed. Results available in /tmp/test_results.tar.gz"
echo "ALPINE TEST PASSED"
EOF

# Copy the test script to VM
echo "Copying test script to VM..."
vagrant ssh -c "mkdir -p /opt/hooker_test"
cat /tmp/alpine_test.sh | vagrant ssh -c "cat > /opt/hooker_test/alpine_test.sh && chmod +x /opt/hooker_test/alpine_test.sh"

# Build hooker_pill in nix-shell
echo "Building hooker_pill in nix-shell..."
# Restore PATH for nix-shell
export PATH="$ORIGINAL_PATH"
cd "$HOOKER_PILL_DIR"
nix-shell --run "cargo build --release"

# Switch back to Vagrant dir with clean PATH for vagrant commands
cd "$VAGRANT_DIR"
export PATH=$(echo "$PATH" | tr ':' '\n' | grep -v "nix" | tr '\n' ':')
if [[ ! "$PATH" =~ /usr/bin ]]; then
    export PATH="$PATH:/usr/bin"
fi
if [[ ! "$PATH" =~ /bin ]]; then
    export PATH="$PATH:/bin"
fi

# Run the test script
echo "Running hooker_pill tests in Alpine VM..."
vagrant ssh -c "sudo /opt/hooker_test/alpine_test.sh"

# Copy test results from VM
echo "Copying test results from VM..."
vagrant ssh -c "if [ -f /tmp/test_results.tar.gz ]; then cat /tmp/test_results.tar.gz; fi" > "${RESULTS_DIR}/test_results.tar.gz"
cd "${RESULTS_DIR}" && tar -xzf test_results.tar.gz

# Display summary
echo "Test run completed."
echo "Results available in ${RESULTS_DIR}"

if [ -f "${RESULTS_DIR}/summary.txt" ]; then
  echo "=== Test Summary ==="
  cat "${RESULTS_DIR}/summary.txt"
fi

# Ask to keep the VM running or shut it down
read -p "Do you want to keep the Alpine VM running? (y/n) " KEEP_RUNNING
if [ "$KEEP_RUNNING" != "y" ]; then
  echo "Shutting down Alpine VM..."
  cd "$VAGRANT_DIR"
  vagrant halt
fi

# Update the tracking document
echo "Alpine test with Option 3 completed on $(date)" >> "$INSPECTOR_DIR/.claude/vagrant_compatibility_options.md"

# Restore original PATH
export PATH="$ORIGINAL_PATH"

echo "Alpine test completed!" 