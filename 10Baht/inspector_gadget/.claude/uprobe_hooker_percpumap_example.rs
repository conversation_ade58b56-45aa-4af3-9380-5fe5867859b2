use std::sync::Arc;
use serde::{Serialize, Deserialize};

use inspector_gadget::ebpf::maps::{TypedOptimizedPerCpuMap, PerCpuMapStats, MapError};
use inspector_gadget::logging::ElasticsearchLogger;

/// Function call information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct FunctionCallInfo {
    /// Function name
    pub function_name: String,
    /// Binary path
    pub binary_path: String,
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// User ID
    pub uid: u32,
    /// Command name
    pub comm: String,
}

/// Function call statistics
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct FunctionCallStats {
    /// Call count
    pub count: u64,
    /// Total execution time (nanoseconds)
    pub total_time_ns: u64,
    /// Minimum execution time (nanoseconds)
    pub min_time_ns: u64,
    /// Maximum execution time (nanoseconds)
    pub max_time_ns: u64,
    /// Error count
    pub error_count: u64,
    /// Last call timestamp
    pub last_call: u64,
}

/// Function parameter
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FunctionParameter {
    /// Integer parameter
    Integer(i64),
    /// Unsigned integer parameter
    UnsignedInteger(u64),
    /// String parameter
    String(String),
    /// Pointer parameter
    Pointer(u64),
    /// Boolean parameter
    Boolean(bool),
}

/// Function call with parameters
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FunctionCallWithParams {
    /// Function name
    pub function_name: String,
    /// Process ID
    pub pid: u32,
    /// Thread ID
    pub tid: u32,
    /// Parameters
    pub parameters: Vec<FunctionParameter>,
    /// Return value
    pub return_value: Option<FunctionParameter>,
    /// Timestamp
    pub timestamp: u64,
}

/// Call graph node
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CallGraphNode {
    /// Function name
    pub function_name: String,
    /// Call count
    pub call_count: u64,
    /// Child functions
    pub children: Vec<String>,
    /// Parent functions
    pub parents: Vec<String>,
}

/// Uprobe Hooker with Per-CPU Maps
pub struct UprobeHooker {
    /// Function call info map
    function_info_map: TypedOptimizedPerCpuMap<u64, FunctionCallInfo>,
    /// Function call statistics map
    function_stats_map: TypedOptimizedPerCpuMap<u64, FunctionCallStats>,
    /// Function call with parameters map
    function_params_map: TypedOptimizedPerCpuMap<u64, FunctionCallWithParams>,
    /// Call graph map
    call_graph_map: TypedOptimizedPerCpuMap<u64, CallGraphNode>,
    /// Elasticsearch logger
    es_logger: Arc<ElasticsearchLogger>,
}

impl UprobeHooker {
    /// Create a new Uprobe Hooker
    pub fn new(es_logger: Arc<ElasticsearchLogger>) -> Result<Self, MapError> {
        // Create function call info map
        let function_info_map = TypedOptimizedPerCpuMap::<u64, FunctionCallInfo>::create(512)?;
        
        // Create function call statistics map
        let function_stats_map = TypedOptimizedPerCpuMap::<u64, FunctionCallStats>::create(1024)?;
        
        // Create function call with parameters map
        let function_params_map = TypedOptimizedPerCpuMap::<u64, FunctionCallWithParams>::create(256)?;
        
        // Create call graph map
        let call_graph_map = TypedOptimizedPerCpuMap::<u64, CallGraphNode>::create(128)?;
        
        Ok(Self {
            function_info_map,
            function_stats_map,
            function_params_map,
            call_graph_map,
            es_logger,
        })
    }
    
    /// Generate key for function statistics
    fn generate_stats_key(function_name: &str, pid: u32) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        function_name.hash(&mut hasher);
        pid.hash(&mut hasher);
        hasher.finish()
    }
    
    /// Generate key for function name
    fn generate_function_key(function_name: &str) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        function_name.hash(&mut hasher);
        hasher.finish()
    }
    
    /// Record function call entry
    pub fn record_function_entry(
        &self,
        function_name: &str,
        binary_path: &str,
        pid: u32,
        tid: u32,
        uid: u32,
        comm: &str,
        parameters: Vec<FunctionParameter>,
        cpu: usize,
    ) -> Result<u64, MapError> {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos() as u64;
        
        // Create function call info
        let info = FunctionCallInfo {
            function_name: function_name.to_string(),
            binary_path: binary_path.to_string(),
            pid,
            tid,
            uid,
            comm: comm.to_string(),
        };
        
        // Update function info map
        let function_key = Self::generate_function_key(function_name);
        self.function_info_map.update_cpu(&function_key, &info, cpu)?;
        
        // Create function call with parameters
        let call_with_params = FunctionCallWithParams {
            function_name: function_name.to_string(),
            pid,
            tid,
            parameters,
            return_value: None,
            timestamp: now,
        };
        
        // Update function params map
        self.function_params_map.update_cpu(&now, &call_with_params, cpu)?;
        
        // Update call graph
        self.update_call_graph(function_name, pid, cpu)?;
        
        Ok(now)
    }
    
    /// Update call graph
    fn update_call_graph(&self, function_name: &str, pid: u32, cpu: usize) -> Result<(), MapError> {
        let function_key = Self::generate_function_key(function_name);
        
        // Get current call graph node or create new one
        let node = match self.call_graph_map.lookup_cpu(&function_key, cpu)? {
            Some(mut node) => {
                // Update existing node
                node.call_count += 1;
                node
            }
            None => {
                // Create new node
                CallGraphNode {
                    function_name: function_name.to_string(),
                    call_count: 1,
                    children: Vec::new(),
                    parents: Vec::new(),
                }
            }
        };
        
        // Update call graph map
        self.call_graph_map.update_cpu(&function_key, &node, cpu)
    }
    
    /// Record function call exit
    pub fn record_function_exit(
        &self,
        function_name: &str,
        pid: u32,
        call_id: u64,
        duration_ns: u64,
        return_value: Option<FunctionParameter>,
        error: bool,
        cpu: usize,
    ) -> Result<(), MapError> {
        let stats_key = Self::generate_stats_key(function_name, pid);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        // Get current stats or create new ones
        let stats = match self.function_stats_map.lookup_cpu(&stats_key, cpu)? {
            Some(mut s) => {
                // Update existing stats
                s.count += 1;
                s.total_time_ns += duration_ns;
                s.min_time_ns = s.min_time_ns.min(duration_ns);
                s.max_time_ns = s.max_time_ns.max(duration_ns);
                if error {
                    s.error_count += 1;
                }
                s.last_call = now;
                s
            }
            None => {
                // Create new stats
                FunctionCallStats {
                    count: 1,
                    total_time_ns: duration_ns,
                    min_time_ns: duration_ns,
                    max_time_ns: duration_ns,
                    error_count: if error { 1 } else { 0 },
                    last_call: now,
                }
            }
        };
        
        // Update stats map
        self.function_stats_map.update_cpu(&stats_key, &stats, cpu)?;
        
        // Update function call with return value
        if let Some(mut call_with_params) = self.function_params_map.lookup_cpu(&call_id, cpu)? {
            call_with_params.return_value = return_value;
            self.function_params_map.update_cpu(&call_id, &call_with_params, cpu)?;
        }
        
        Ok(())
    }
    
    /// Add child function to call graph
    pub fn add_child_function(
        &self,
        parent_function: &str,
        child_function: &str,
        cpu: usize,
    ) -> Result<(), MapError> {
        let parent_key = Self::generate_function_key(parent_function);
        let child_key = Self::generate_function_key(child_function);
        
        // Update parent node
        if let Some(mut parent_node) = self.call_graph_map.lookup_cpu(&parent_key, cpu)? {
            if !parent_node.children.contains(&child_function.to_string()) {
                parent_node.children.push(child_function.to_string());
                self.call_graph_map.update_cpu(&parent_key, &parent_node, cpu)?;
            }
        }
        
        // Update child node
        if let Some(mut child_node) = self.call_graph_map.lookup_cpu(&child_key, cpu)? {
            if !child_node.parents.contains(&parent_function.to_string()) {
                child_node.parents.push(parent_function.to_string());
                self.call_graph_map.update_cpu(&child_key, &child_node, cpu)?;
            }
        }
        
        Ok(())
    }
    
    /// Get function call info
    pub fn get_function_info(&self, function_name: &str, cpu: usize) -> Result<Option<FunctionCallInfo>, MapError> {
        let function_key = Self::generate_function_key(function_name);
        self.function_info_map.lookup_cpu(&function_key, cpu)
    }
    
    /// Get function call statistics
    pub fn get_function_stats(&self, function_name: &str, pid: u32, cpu: usize) -> Result<Option<FunctionCallStats>, MapError> {
        let stats_key = Self::generate_stats_key(function_name, pid);
        self.function_stats_map.lookup_cpu(&stats_key, cpu)
    }
    
    /// Get function call with parameters
    pub fn get_function_call(&self, call_id: u64, cpu: usize) -> Result<Option<FunctionCallWithParams>, MapError> {
        self.function_params_map.lookup_cpu(&call_id, cpu)
    }
    
    /// Get call graph node
    pub fn get_call_graph_node(&self, function_name: &str, cpu: usize) -> Result<Option<CallGraphNode>, MapError> {
        let function_key = Self::generate_function_key(function_name);
        self.call_graph_map.lookup_cpu(&function_key, cpu)
    }
    
    /// Get total function call statistics
    pub fn get_total_function_stats(&self, function_name: &str, pid: u32) -> Result<Option<FunctionCallStats>, MapError> {
        let stats_key = Self::generate_stats_key(function_name, pid);
        
        // Aggregate statistics across all CPUs
        self.function_stats_map.aggregate(
            &stats_key,
            |stats_list| {
                if stats_list.is_empty() {
                    None
                } else {
                    let mut total_stats = FunctionCallStats {
                        count: 0,
                        total_time_ns: 0,
                        min_time_ns: u64::MAX,
                        max_time_ns: 0,
                        error_count: 0,
                        last_call: 0,
                    };
                    
                    for stats in stats_list {
                        total_stats.count += stats.count;
                        total_stats.total_time_ns += stats.total_time_ns;
                        total_stats.min_time_ns = total_stats.min_time_ns.min(stats.min_time_ns);
                        total_stats.max_time_ns = total_stats.max_time_ns.max(stats.max_time_ns);
                        total_stats.error_count += stats.error_count;
                        total_stats.last_call = total_stats.last_call.max(stats.last_call);
                    }
                    
                    if total_stats.min_time_ns == u64::MAX {
                        total_stats.min_time_ns = 0;
                    }
                    
                    Some(total_stats)
                }
            },
        )
    }
    
    /// Export function call statistics to Elasticsearch
    pub fn export_function_stats(&self) -> Result<usize, MapError> {
        let mut exported = 0;
        
        // Iterate over all CPUs
        for cpu in 0..self.function_stats_map.num_cpus() {
            // Skip offline CPUs
            if !self.function_stats_map.is_cpu_online(cpu) {
                continue;
            }
            
            // TODO: Implement efficient iteration over function stats
            // For now, we'll just export a few known function stats
            for function_key in 0..100 {
                if let Some(stats) = self.function_stats_map.lookup_cpu(&function_key, cpu)? {
                    // Create export data
                    let export_data = serde_json::json!({
                        "function_key": function_key,
                        "count": stats.count,
                        "total_time_ns": stats.total_time_ns,
                        "min_time_ns": stats.min_time_ns,
                        "max_time_ns": stats.max_time_ns,
                        "avg_time_ns": if stats.count > 0 { stats.total_time_ns / stats.count } else { 0 },
                        "error_count": stats.error_count,
                        "error_rate": if stats.count > 0 { (stats.error_count as f64) / (stats.count as f64) } else { 0.0 },
                        "last_call": stats.last_call,
                        "cpu": cpu,
                    });
                    
                    // Log to Elasticsearch
                    if let Ok(()) = self.es_logger.log_event("uprobe_function_stats", export_data) {
                        exported += 1;
                    }
                }
            }
        }
        
        Ok(exported)
    }
    
    /// Export call graph to Elasticsearch
    pub fn export_call_graph(&self) -> Result<usize, MapError> {
        let mut exported = 0;
        
        // Iterate over all CPUs
        for cpu in 0..self.call_graph_map.num_cpus() {
            // Skip offline CPUs
            if !self.call_graph_map.is_cpu_online(cpu) {
                continue;
            }
            
            // TODO: Implement efficient iteration over call graph
            // For now, we'll just export a few known call graph nodes
            for function_key in 0..100 {
                if let Some(node) = self.call_graph_map.lookup_cpu(&function_key, cpu)? {
                    // Create export data
                    let export_data = serde_json::json!({
                        "function_name": node.function_name,
                        "call_count": node.call_count,
                        "children": node.children,
                        "parents": node.parents,
                        "cpu": cpu,
                    });
                    
                    // Log to Elasticsearch
                    if let Ok(()) = self.es_logger.log_event("uprobe_call_graph", export_data) {
                        exported += 1;
                    }
                }
            }
        }
        
        Ok(exported)
    }
    
    /// Get statistics for all maps
    pub fn get_stats(&self) -> (PerCpuMapStats, PerCpuMapStats, PerCpuMapStats, PerCpuMapStats) {
        (
            self.function_info_map.stats().clone(),
            self.function_stats_map.stats().clone(),
            self.function_params_map.stats().clone(),
            self.call_graph_map.stats().clone(),
        )
    }
}

/// Uprobe hook handler for function entry
pub fn handle_function_entry(
    hooker: &UprobeHooker,
    function_name: &str,
    binary_path: &str,
    pid: u32,
    tid: u32,
    uid: u32,
    comm: &str,
    parameters: Vec<FunctionParameter>,
) -> Result<u64, MapError> {
    // Get current CPU
    let cpu = 0; // In a real implementation, get the current CPU
    
    // Record function entry
    hooker.record_function_entry(
        function_name,
        binary_path,
        pid,
        tid,
        uid,
        comm,
        parameters,
        cpu,
    )
}

/// Uprobe hook handler for function exit
pub fn handle_function_exit(
    hooker: &UprobeHooker,
    function_name: &str,
    pid: u32,
    call_id: u64,
    duration_ns: u64,
    return_value: Option<FunctionParameter>,
    error: bool,
) -> Result<(), MapError> {
    // Get current CPU
    let cpu = 0; // In a real implementation, get the current CPU
    
    // Record function exit
    hooker.record_function_exit(
        function_name,
        pid,
        call_id,
        duration_ns,
        return_value,
        error,
        cpu,
    )
}

/// Example test function
#[cfg(test)]
mod tests {
    use super::*;
    use inspector_gadget::logging::ElasticsearchConfig;
    
    #[test]
    fn test_uprobe_hooker() -> Result<(), MapError> {
        // Create Elasticsearch logger
        let es_config = ElasticsearchConfig {
            url: "http://localhost:9200".to_string(),
            index: "uprobe_test_results".to_string(),
            username: None,
            password: None,
            batch_size: 10,
            flush_interval: 5,
            connect_timeout: 10,
            request_timeout: 30,
        };
        
        let es_logger = Arc::new(
            ElasticsearchLogger::new(es_config).expect("Failed to create Elasticsearch logger"),
        );
        
        // Create Uprobe Hooker
        let hooker = UprobeHooker::new(es_logger)?;
        
        // Test function entry
        let call_id = handle_function_entry(
            &hooker,
            "main",
            "/usr/bin/test_program",
            1000,
            1000,
            1000,
            "test_program",
            vec![
                FunctionParameter::Integer(42),
                FunctionParameter::String("hello".to_string()),
            ],
        )?;
        
        // Test nested function entry
        let nested_call_id = handle_function_entry(
            &hooker,
            "process_data",
            "/usr/bin/test_program",
            1000,
            1000,
            1000,
            "test_program",
            vec![FunctionParameter::Integer(42)],
        )?;
        
        // Add child function to call graph
        hooker.add_child_function("main", "process_data", 0)?;
        
        // Test nested function exit
        handle_function_exit(
            &hooker,
            "process_data",
            1000,
            nested_call_id,
            500000,
            Some(FunctionParameter::Boolean(true)),
            false,
        )?;
        
        // Test function exit
        handle_function_exit(
            &hooker,
            "main",
            1000,
            call_id,
            1000000,
            Some(FunctionParameter::Integer(0)),
            false,
        )?;
        
        // Verify function info
        let info = hooker.get_function_info("main", 0)?;
        assert!(info.is_some());
        let info = info.unwrap();
        assert_eq!(info.function_name, "main");
        assert_eq!(info.binary_path, "/usr/bin/test_program");
        assert_eq!(info.pid, 1000);
        
        // Verify function stats
        let stats = hooker.get_function_stats("main", 1000, 0)?;
        assert!(stats.is_some());
        let stats = stats.unwrap();
        assert_eq!(stats.count, 1);
        assert_eq!(stats.total_time_ns, 1000000);
        assert_eq!(stats.min_time_ns, 1000000);
        assert_eq!(stats.max_time_ns, 1000000);
        assert_eq!(stats.error_count, 0);
        
        // Verify function call with parameters
        let call = hooker.get_function_call(call_id, 0)?;
        assert!(call.is_some());
        let call = call.unwrap();
        assert_eq!(call.function_name, "main");
        assert_eq!(call.pid, 1000);
        assert_eq!(call.parameters.len(), 2);
        assert_eq!(call.return_value, Some(FunctionParameter::Integer(0)));
        
        // Verify call graph
        let node = hooker.get_call_graph_node("main", 0)?;
        assert!(node.is_some());
        let node = node.unwrap();
        assert_eq!(node.function_name, "main");
        assert_eq!(node.call_count, 1);
        assert_eq!(node.children, vec!["process_data".to_string()]);
        
        let child_node = hooker.get_call_graph_node("process_data", 0)?;
        assert!(child_node.is_some());
        let child_node = child_node.unwrap();
        assert_eq!(child_node.function_name, "process_data");
        assert_eq!(child_node.call_count, 1);
        assert_eq!(child_node.parents, vec!["main".to_string()]);
        
        // Get total function stats
        let total_stats = hooker.get_total_function_stats("main", 1000)?;
        assert!(total_stats.is_some());
        let total_stats = total_stats.unwrap();
        println!("Total stats for function main: {:?}", total_stats);
        
        // Export function stats
        let exported = hooker.export_function_stats()?;
        println!("Exported {} function stats", exported);
        
        // Export call graph
        let exported = hooker.export_call_graph()?;
        println!("Exported {} call graph nodes", exported);
        
        // Get statistics
        let (info_stats, stats_stats, params_stats, graph_stats) = hooker.get_stats();
        println!("Function info map stats: {:?}", info_stats);
        println!("Function stats map stats: {:?}", stats_stats);
        println!("Function params map stats: {:?}", params_stats);
        println!("Call graph map stats: {:?}", graph_stats);
        
        Ok(())
    }
} 