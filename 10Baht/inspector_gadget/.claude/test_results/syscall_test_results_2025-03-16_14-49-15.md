# SyscallInterceptor Test Results - 2025-03-16_14-49-15
Test run started at: Sun 16 Mar 14:49:15 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Kernel: 6.9.3-76060903-generic

---


## Command: nix-shell shell.nix --run "python3 tools/pimp/pimp.py status" (2025-03-16_14-49-15)
**Description**: Check the status of the pimp.py tool and Vagrant VMs

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
python3: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:49:28 CET 2025

---


## Command: nix-shell shell.nix --run "python3 tools/pimp/pimp.py setup" (2025-03-16_14-49-15)
**Description**: Set up the environment for testing

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
python3: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:49:41 CET 2025

---


## Command: nix-shell shell.nix --run "python3 tools/pimp/pimp.py pill lsm_default" (2025-03-16_14-49-15)
**Description**: Inject pill into the LSM VM

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
python3: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:50:07 CET 2025

---


## Command: nix-shell shell.nix --run "python3 tools/pimp/pimp.py ping-pill lsm_default" (2025-03-16_14-49-15)
**Description**: Ping the pill in the LSM VM

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
python3: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:50:20 CET 2025

---


## Command: nix-shell shell.nix --run "python3 tools/pimp/pimp.py heartbeat" (2025-03-16_14-49-15)
**Description**: Start heartbeat monitoring for pills

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
python3: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:50:33 CET 2025

---


## Command: nix-shell shell.nix --run "python3 tools/pimp/pimp.py heartbeat --stop" (2025-03-16_14-49-15)
**Description**: Stop heartbeat monitoring

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
python3: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:51:16 CET 2025

---


## Command: nix-shell shell.nix --run "python3 tools/pimp/pimp.py pill --remove lsm_default" (2025-03-16_14-49-15)
**Description**: Remove pill from the LSM VM

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
python3: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:51:30 CET 2025

---


## Command: nix-shell shell.nix --run "python3 tools/pimp/pimp.py cleanup" (2025-03-16_14-49-15)
**Description**: Clean up the environment after testing

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
python3: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:51:43 CET 2025

---


## Summary
Test run completed at: Sun 16 Mar 14:51:43 CET 2025

### Test Results
- Passed: 8
- Failed: 0
0
