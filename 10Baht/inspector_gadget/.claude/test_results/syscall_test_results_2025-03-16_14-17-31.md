# SyscallInterceptor Test Results - 2025-03-16_14-17-31
Test run started at: Sun 16 Mar 14:17:31 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Kernel: 6.9.3-76060903-generic

---


## Command: setup_python_env (2025-03-16_14-17-31)
**Description**: Setting up Python virtual environment with required packages

**Results**:
```
Setting up Python virtual environment...
Requirement already satisfied: rich in ./.venv/lib/python3.10/site-packages (13.9.4)
Requirement already satisfied: typer in ./.venv/lib/python3.10/site-packages (0.15.2)
Requirement already satisfied: pydantic in ./.venv/lib/python3.10/site-packages (2.10.6)
Requirement already satisfied: pyyaml in ./.venv/lib/python3.10/site-packages (6.0.2)
Collecting grpcio
  Downloading grpcio-1.71.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (5.9 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 5.9/5.9 MB 16.0 MB/s eta 0:00:00
Collecting grpcio-tools
  Downloading grpcio_tools-1.71.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.5 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.5/2.5 MB 25.0 MB/s eta 0:00:00
Requirement already satisfied: typing-extensions<5.0,>=4.0.0 in ./.venv/lib/python3.10/site-packages (from rich) (4.12.2)
Requirement already satisfied: markdown-it-py>=2.2.0 in ./.venv/lib/python3.10/site-packages (from rich) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in ./.venv/lib/python3.10/site-packages (from rich) (2.19.1)
Requirement already satisfied: click>=8.0.0 in ./.venv/lib/python3.10/site-packages (from typer) (8.1.8)
Requirement already satisfied: shellingham>=1.3.0 in ./.venv/lib/python3.10/site-packages (from typer) (1.5.4)
Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.10/site-packages (from pydantic) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.10/site-packages (from pydantic) (2.27.2)
Requirement already satisfied: setuptools in ./.venv/lib/python3.10/site-packages (from grpcio-tools) (59.6.0)
Collecting protobuf<6.0dev,>=5.26.1
  Downloading protobuf-5.29.3-cp38-abi3-manylinux2014_x86_64.whl (319 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 319.7/319.7 KB 14.1 MB/s eta 0:00:00
Requirement already satisfied: mdurl~=0.1 in ./.venv/lib/python3.10/site-packages (from markdown-it-py>=2.2.0->rich) (0.1.2)
Installing collected packages: protobuf, grpcio, grpcio-tools
Successfully installed grpcio-1.71.0 grpcio-tools-1.71.0 protobuf-5.29.3
Python virtual environment setup complete.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:03 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py status (2025-03-16_14-17-31)
**Description**: Check the status of the pimp.py tool and Vagrant VMs

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:04 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py setup (2025-03-16_14-17-31)
**Description**: Set up the environment for testing

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:05 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py inject --hooker hooker_pill (2025-03-16_14-17-31)
**Description**: Inject hooker_pills into Vagrant VMs

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:06 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test (2025-03-16_14-17-31)
**Description**: Run the SyscallInterceptor tests

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:06 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_high_throughput (2025-03-16_14-17-31)
**Description**: Run the high throughput test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:07 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_percpu_maps_integration --features percpu_maps (2025-03-16_14-17-31)
**Description**: Run the Per-CPU Maps integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:08 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_xdp_integration --features xdp (2025-03-16_14-17-31)
**Description**: Run the XDP integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:10 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_tracepoint_integration --features tracepoint (2025-03-16_14-17-31)
**Description**: Run the Tracepoint integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:11 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py cleanup (2025-03-16_14-17-31)
**Description**: Clean up the environment after testing

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 37, in <module>
    from src.reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 44, in <module>
    from reporter import Reporter
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/reporter.py", line 15, in <module>
    from jinja2 import Environment, FileSystemLoader
ModuleNotFoundError: No module named 'jinja2'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:12 CET 2025

---


## Summary
Test run completed at: Sun 16 Mar 14:18:12 CET 2025

### Test Results
- Passed: 10
- Failed: 0
0
