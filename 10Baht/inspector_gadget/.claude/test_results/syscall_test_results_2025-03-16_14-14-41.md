# SyscallInterceptor Test Results - 2025-03-16_14-14-41
Test run started at: Sun 16 Mar 14:14:41 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Kernel: 6.9.3-76060903-generic

---


## Command: ./run_hookerpill_tests.sh (2025-03-16_14-14-41)
**Description**: Run the hookerpill tests to test the SyscallInterceptor implementation

**Results**:
```
[0;34m=== Inspector Gadget HookerPill Test Runner ===[0m
[1;33mStep 1: Destroying all VMs via gRPC on port 40000...[0m
[1;33mRunning pimp cleanup with force flag...[0m
[1;33mAttempt 1/3: Cleaning up VMs[0m
Attempting to start nix-shell environment...
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at /home/<USER>/dev/inspector_gadget/tools/pimp/shell.nix:1:17:

            1| { pkgs ? import <nixpkgs> {} }:
             |                 ^
            2|
(use '--show-trace' to show detailed location information)
nix-shell failed, falling back to regular Python environment...
Running Pimp tool with Python...
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:14:42 CET 2025

---


## Summary
Test run completed at: Sun 16 Mar 14:14:42 CET 2025

### Test Results
- Passed: 1
- Failed: 0
0
