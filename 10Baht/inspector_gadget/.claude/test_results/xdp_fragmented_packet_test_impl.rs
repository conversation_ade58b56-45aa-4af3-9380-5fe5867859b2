use std::time::{Duration, Instant};
use std::thread;
use std::process::Command;
use std::fs::{self, File};
use std::io::{self, Read, Write};
use std::path::{Path, PathBuf};
use serde::{Serialize, Deserialize};
use serde_json::{json, Value};

// Test VM Management
struct TestVM {
    name: String,
    status: VMStatus,
    vagrant_dir: PathBuf,
    ip_address: Option<String>,
}

enum VMStatus {
    NotCreated,
    Running,
    Stopped,
    Error,
}

// Test configuration
#[derive(Serialize, Deserialize)]
struct XDPFragmentedPacketTestConfig {
    vm_name: String,
    packet_count: usize,
    fragment_sizes: Vec<usize>,
    fragment_overlap: bool,
    protocols: Vec<String>,
    max_fragments_per_packet: usize,
    capture_timeout_seconds: u64,
    expected_reassembly_rate: f64,
}

impl TestVM {
    fn new(vm_name: &str) -> Self {
        let vagrant_dir = PathBuf::from("/home/<USER>/cdev/inspector_gadget/test/vms")
            .join(vm_name);
        
        Self {
            name: vm_name.to_string(),
            status: VMStatus::NotCreated,
            vagrant_dir,
            ip_address: None,
        }
    }
    
    fn start(&mut self) -> io::Result<()> {
        println!("Starting VM: {}", self.name);
        
        // Change to vagrant directory
        std::env::set_current_dir(&self.vagrant_dir)?;
        
        // Run vagrant up
        let output = Command::new("vagrant")
            .arg("up")
            .output()?;
        
        if !output.status.success() {
            eprintln!("Failed to start VM: {}", String::from_utf8_lossy(&output.stderr));
            self.status = VMStatus::Error;
            return Err(io::Error::new(io::ErrorKind::Other, "Failed to start VM"));
        }
        
        // Get VM IP address
        let ssh_config = Command::new("vagrant")
            .args(["ssh-config"])
            .output()?;
        
        if ssh_config.status.success() {
            let config = String::from_utf8_lossy(&ssh_config.stdout);
            
            // Parse HostName from ssh-config
            for line in config.lines() {
                if line.trim().starts_with("HostName") {
                    if let Some(ip) = line.split_whitespace().nth(1) {
                        self.ip_address = Some(ip.to_string());
                        break;
                    }
                }
            }
        }
        
        self.status = VMStatus::Running;
        println!("VM started successfully. IP: {:?}", self.ip_address);
        
        Ok(())
    }
    
    fn stop(&mut self) -> io::Result<()> {
        println!("Stopping VM: {}", self.name);
        
        // Change to vagrant directory
        std::env::set_current_dir(&self.vagrant_dir)?;
        
        // Run vagrant halt
        let output = Command::new("vagrant")
            .arg("halt")
            .output()?;
        
        if !output.status.success() {
            eprintln!("Failed to stop VM: {}", String::from_utf8_lossy(&output.stderr));
            return Err(io::Error::new(io::ErrorKind::Other, "Failed to stop VM"));
        }
        
        self.status = VMStatus::Stopped;
        println!("VM stopped successfully");
        
        Ok(())
    }
    
    fn execute_command(&self, command: &str) -> io::Result<String> {
        println!("Executing command on VM: {}", command);
        
        // Change to vagrant directory
        std::env::set_current_dir(&self.vagrant_dir)?;
        
        // Run command via vagrant ssh
        let output = Command::new("vagrant")
            .args(["ssh", "-c", command])
            .output()?;
        
        if !output.status.success() {
            eprintln!("Command failed: {}", String::from_utf8_lossy(&output.stderr));
            return Err(io::Error::new(io::ErrorKind::Other, "Command execution failed"));
        }
        
        let stdout = String::from_utf8_lossy(&output.stdout).to_string();
        Ok(stdout)
    }
    
    fn upload_file(&self, remote_path: &str, local_content: &str) -> io::Result<()> {
        println!("Uploading file to VM: {}", remote_path);
        
        // Create temporary file
        let temp_file_path = std::env::temp_dir().join("temp_upload.txt");
        let mut temp_file = File::create(&temp_file_path)?;
        temp_file.write_all(local_content.as_bytes())?;
        temp_file.flush()?;
        
        // Change to vagrant directory
        std::env::set_current_dir(&self.vagrant_dir)?;
        
        // Use scp to copy file to VM
        let output = Command::new("vagrant")
            .args(["upload", temp_file_path.to_str().unwrap(), remote_path])
            .output()?;
        
        if !output.status.success() {
            eprintln!("File upload failed: {}", String::from_utf8_lossy(&output.stderr));
            return Err(io::Error::new(io::ErrorKind::Other, "File upload failed"));
        }
        
        // Clean up temp file
        fs::remove_file(temp_file_path)?;
        
        Ok(())
    }
    
    fn inject_pill(&self, pill_config: &HookerPillConfig) -> io::Result<()> {
        println!("Injecting hooker_pill with configuration:");
        println!("{:#?}", pill_config);
        
        // Serialize pill config to JSON
        let config_json = serde_json::to_string_pretty(pill_config)
            .map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
        
        // Upload configuration file to VM
        self.upload_file("/tmp/pill_config.json", &config_json)?;
        
        // Run injection command
        let injection_command = "sudo LD_PRELOAD=/usr/local/lib/libhooker_pill.so \
                               INSPECTOR_GADGET_CONFIG_FILE=/tmp/pill_config.json \
                               bash -c 'echo \"Pill injected\" > /tmp/pill_status.txt'";
        
        self.execute_command(injection_command)?;
        
        // Verify pill is running
        let status = self.execute_command("cat /tmp/pill_status.txt")?;
        if !status.contains("Pill injected") {
            return Err(io::Error::new(io::ErrorKind::Other, "Pill injection failed"));
        }
        
        Ok(())
    }
}

// Hooker Pill Configuration
#[derive(Debug, Serialize, Deserialize)]
struct HookerPillConfig {
    es_url: String,
    es_username: String,
    es_password: String,
    index_name: String,
    batch_size: usize,
    flush_interval_ms: usize,
    // Optional fields
    #[serde(skip_serializing_if = "Option::is_none")]
    ca_cert_path: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    client_cert_path: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    client_key_path: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    retry_max_attempts: Option<usize>,
    #[serde(skip_serializing_if = "Option::is_none")]
    retry_initial_backoff_ms: Option<usize>,
    #[serde(skip_serializing_if = "Option::is_none")]
    retry_max_backoff_ms: Option<usize>,
}

// Implementation of the XDP fragmented packet test
fn run_xdp_fragmented_packet_test() -> io::Result<TestResult> {
    println!("=== XDP Fragmented Packet Test (XDP-P-01) ===");
    
    // Test configuration
    let test_config = XDPFragmentedPacketTestConfig {
        vm_name: "ubuntu-22.04-xdp".to_string(),
        packet_count: 1000,
        fragment_sizes: vec![64, 128, 256, 512, 1024],
        fragment_overlap: true,
        protocols: vec!["icmp".to_string(), "tcp".to_string(), "udp".to_string()],
        max_fragments_per_packet: 8,
        capture_timeout_seconds: 120,
        expected_reassembly_rate: 0.95, // Expect 95% successful reassembly
    };
    
    // Start test VM
    let mut vm = TestVM::new(&test_config.vm_name);
    vm.start()?;
    
    // Ensure needed tools are installed
    vm.execute_command(
        "sudo apt-get update && \
         sudo apt-get install -y scapy python3-scapy tcpdump"
    )?;
    
    // Inject hooker_pill with XDP hooks
    let pill_config = HookerPillConfig {
        es_url: "http://elasticsearch:9200".to_string(),
        es_username: "elastic".to_string(),
        es_password: "changeme".to_string(),
        index_name: "test_xdp_fragmented".to_string(),
        batch_size: 1000,
        flush_interval_ms: 1000,
        ca_cert_path: None,
        client_cert_path: None,
        client_key_path: None,
        retry_max_attempts: Some(5),
        retry_initial_backoff_ms: Some(1000),
        retry_max_backoff_ms: Some(10000),
    };
    
    vm.inject_pill(&pill_config)?;
    
    // Create fragmented packet generator Python script
    let python_script = create_fragment_generator_script(&test_config);
    vm.upload_file("/tmp/fragment_generator.py", &python_script)?;
    
    // Start packet capture
    vm.execute_command("sudo tcpdump -i eth0 -w /tmp/capture.pcap &")?;
    thread::sleep(Duration::from_secs(2)); // Give tcpdump time to start
    
    // Run the fragment generator script
    println!("Generating fragmented packets...");
    let generation_output = vm.execute_command("sudo python3 /tmp/fragment_generator.py")?;
    println!("Generator output: {}", generation_output);
    
    // Wait for capture to complete
    thread::sleep(Duration::from_secs(10));
    
    // Stop packet capture
    vm.execute_command("sudo pkill tcpdump")?;
    thread::sleep(Duration::from_secs(2));
    
    // Analyze capture to verify fragment reassembly
    println!("Analyzing packet capture...");
    let analysis_script = create_fragment_analysis_script();
    vm.upload_file("/tmp/analyze_fragments.py", &analysis_script)?;
    
    let analysis_output = vm.execute_command("sudo python3 /tmp/analyze_fragments.py")?;
    println!("Analysis output: {}", analysis_output);
    
    // Parse analysis results
    let results = parse_analysis_results(&analysis_output)?;
    
    // Wait for Elasticsearch indexing to complete
    thread::sleep(Duration::from_secs(30));
    
    // Query Elasticsearch to verify XDP hook captured fragments
    let es_query_script = create_elasticsearch_query_script(&test_config);
    vm.upload_file("/tmp/query_elasticsearch.py", &es_query_script)?;
    
    let es_results = vm.execute_command("python3 /tmp/query_elasticsearch.py")?;
    println!("Elasticsearch query results: {}", es_results);
    
    // Parse Elasticsearch results
    let es_data = parse_elasticsearch_results(&es_results)?;
    
    // Calculate final metrics
    let reassembly_rate = results.reassembled_packets as f64 / results.total_fragments as f64 * 100.0;
    let es_capture_rate = es_data.captured_fragments as f64 / results.total_fragments as f64 * 100.0;
    
    // Determine test pass/fail
    let passed = reassembly_rate >= test_config.expected_reassembly_rate * 100.0 &&
                es_capture_rate >= 95.0;
    
    // Create test result
    let test_result = TestResult {
        test_id: "XDP-P-01".to_string(),
        test_name: "Fragmented Packet Handling".to_string(),
        status: if passed { "PASS" } else { "FAIL" }.to_string(),
        total_packets_generated: test_config.packet_count,
        total_fragments: results.total_fragments,
        reassembled_packets: results.reassembled_packets,
        reassembly_rate: reassembly_rate,
        captured_fragments: es_data.captured_fragments,
        capture_rate: es_capture_rate,
        duration_seconds: results.duration_seconds,
        metrics: json!({
            "by_protocol": results.protocol_metrics,
            "by_fragment_size": results.fragment_size_metrics,
            "fragment_counts": results.fragment_counts
        }),
    };
    
    // Clean up the VM
    vm.stop()?;
    
    println!("Test completed: {}", if passed { "PASSED" } else { "FAILED" });
    println!("Reassembly rate: {:.2}%", reassembly_rate);
    println!("XDP hook capture rate: {:.2}%", es_capture_rate);
    
    Ok(test_result)
}

// Create python script for generating fragmented packets
fn create_fragment_generator_script(config: &XDPFragmentedPacketTestConfig) -> String {
    format!(
        r#"#!/usr/bin/env python3
import sys
import time
import random
from scapy.all import *

# Configuration
PACKET_COUNT = {}
FRAGMENT_SIZES = {}
MAX_FRAGMENTS_PER_PACKET = {}
PROTOCOLS = {}
FRAGMENT_OVERLAP = {}

# Set up logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fragment_generator")

def generate_fragmented_packets():
    """Generate fragmented IP packets with various configurations."""
    total_fragments = 0
    packets_by_protocol = {{}}
    
    for protocol in PROTOCOLS:
        packets_by_protocol[protocol] = 0
    
    for i in range(PACKET_COUNT):
        # Select protocol
        protocol = random.choice(PROTOCOLS)
        packets_by_protocol[protocol] += 1
        
        # Base packet
        if protocol == "icmp":
            base = IP(dst="127.0.0.1")/ICMP()/("X" * 1400)
        elif protocol == "tcp":
            base = IP(dst="127.0.0.1")/TCP(sport=random.randint(1024, 65535), dport=80)/("X" * 1400)
        elif protocol == "udp":
            base = IP(dst="127.0.0.1")/UDP(sport=random.randint(1024, 65535), dport=53)/("X" * 1400)
        
        # Fragment the packet
        fragments = fragment(base, fragsize=random.choice(FRAGMENT_SIZES))
        
        # Limit fragments if needed
        fragments = fragments[:MAX_FRAGMENTS_PER_PACKET]
        total_fragments += len(fragments)
        
        # Send fragments
        for frag in fragments:
            send(frag, verbose=0)
            time.sleep(0.001)  # Small delay between fragments
        
        # Log progress
        if i % 100 == 0:
            logger.info(f"Generated {{i}} packets ({{total_fragments}} fragments)")
    
    logger.info(f"Completed. Total packets: {{PACKET_COUNT}}, Total fragments: {{total_fragments}}")
    logger.info(f"Packets by protocol: {{packets_by_protocol}}")
    
    # Write stats to file for later analysis
    with open("/tmp/fragment_stats.json", "w") as f:
        import json
        json.dump({{
            "total_packets": PACKET_COUNT,
            "total_fragments": total_fragments,
            "packets_by_protocol": packets_by_protocol,
            "timestamp": time.time()
        }}, f)

if __name__ == "__main__":
    start_time = time.time()
    generate_fragmented_packets()
    elapsed = time.time() - start_time
    print(f"Generated {{PACKET_COUNT}} fragmented packets in {{elapsed:.2f}} seconds")
"#,
        config.packet_count,
        config.fragment_sizes,
        config.max_fragments_per_packet,
        config.protocols,
        config.fragment_overlap
    )
}

// Create python script for analyzing captured fragments
fn create_fragment_analysis_script() -> String {
    r#"#!/usr/bin/env python3
import sys
import time
import json
from scapy.all import *

# Load the capture file
print("Loading capture file...")
packets = rdpcap("/tmp/capture.pcap")
print(f"Loaded {len(packets)} packets from capture")

# Load generation stats
with open("/tmp/fragment_stats.json", "r") as f:
    stats = json.load(f)

# Analyze fragments and reassembly
fragments = {}
reassembled = 0
fragment_sizes = {}
protocol_counts = {"icmp": 0, "tcp": 0, "udp": 0, "other": 0}
fragment_counts = {}

for packet in packets:
    if IP in packet and packet[IP].flags == 1 or packet[IP].frag > 0:
        # This is a fragment
        ip_id = packet[IP].id
        if ip_id not in fragments:
            fragments[ip_id] = []
        fragments[ip_id].append(packet)
        
        # Count by size
        pkt_size = len(packet)
        if pkt_size not in fragment_sizes:
            fragment_sizes[pkt_size] = 0
        fragment_sizes[pkt_size] += 1
        
        # Count protocols (fragment 0 has the protocol)
        if packet[IP].frag == 0:
            if ICMP in packet:
                protocol_counts["icmp"] += 1
            elif TCP in packet:
                protocol_counts["tcp"] += 1
            elif UDP in packet:
                protocol_counts["udp"] += 1
            else:
                protocol_counts["other"] += 1

# Count fragment groups by size
for ip_id, frags in fragments.items():
    frag_count = len(frags)
    if frag_count not in fragment_counts:
        fragment_counts[frag_count] = 0
    fragment_counts[frag_count] += 1

# Count reassembled packets
for ip_id, frags in fragments.items():
    # Simple check: if we have a fragment with offset 0 and one without MF flag, consider it complete
    has_first = any(pkt[IP].frag == 0 for pkt in frags)
    has_last = any(pkt[IP].flags == 0 for pkt in frags)
    if has_first and has_last:
        reassembled += 1

# Calculate metrics
total_fragments = sum(len(frags) for frags in fragments.values())
reassembly_rate = reassembled / len(fragments) * 100 if fragments else 0

# Output results
results = {
    "total_fragments": total_fragments,
    "unique_packets": len(fragments),
    "reassembled_packets": reassembled,
    "reassembly_rate": reassembly_rate,
    "protocol_metrics": protocol_counts,
    "fragment_size_metrics": fragment_sizes,
    "fragment_counts": fragment_counts,
    "duration_seconds": time.time() - stats["timestamp"],
    "expected_packets": stats["total_packets"]
}

print(json.dumps(results, indent=2))

# Save results for later use
with open("/tmp/fragment_analysis.json", "w") as f:
    json.dump(results, f)

print(f"Analysis complete. Reassembly rate: {reassembly_rate:.2f}%")
"#.to_string()
}

// Create python script for querying Elasticsearch
fn create_elasticsearch_query_script(config: &XDPFragmentedPacketTestConfig) -> String {
    format!(
        r#"#!/usr/bin/env python3
import sys
import json
import time
import requests
from datetime import datetime, timedelta

# Configuration
ES_URL = "http://elasticsearch:9200"
ES_INDEX = "test_xdp_fragmented"
ES_AUTH = ("elastic", "changeme")

# Calculate time range (from fragment generator stats)
with open("/tmp/fragment_stats.json", "r") as f:
    stats = json.load(f)
    
start_time = datetime.fromtimestamp(stats["timestamp"] - 60)  # 1 minute before
end_time = datetime.fromtimestamp(stats["timestamp"] + 300)   # 5 minutes after

# Query Elasticsearch for XDP hook events
query = {{
    "query": {{
        "bool": {{
            "must": [
                {{ "term": {{ "hook_type": "xdp" }} }},
                {{ "range": {{ 
                    "timestamp": {{
                        "gte": start_time.isoformat(),
                        "lte": end_time.isoformat()
                    }}
                }} }}
            ]
        }}
    }},
    "aggs": {{
        "protocol_counts": {{
            "terms": {{ "field": "protocol" }}
        }},
        "fragment_counts": {{
            "terms": {{ "field": "is_fragment" }}
        }}
    }},
    "size": 0
}}

# Execute query
response = requests.post(
    f"{{ES_URL}}/{{ES_INDEX}}/_search",
    auth=ES_AUTH,
    json=query,
    headers={{"Content-Type": "application/json"}}
)

if response.status_code != 200:
    print(f"Error querying Elasticsearch: {{response.text}}")
    sys.exit(1)

# Get fragment counts
results = response.json()
total_fragments_query = {{
    "query": {{
        "bool": {{
            "must": [
                {{ "term": {{ "hook_type": "xdp" }} }},
                {{ "term": {{ "is_fragment": True }} }},
                {{ "range": {{ 
                    "timestamp": {{
                        "gte": start_time.isoformat(),
                        "lte": end_time.isoformat()
                    }}
                }} }}
            ]
        }}
    }}
}}

fragment_resp = requests.post(
    f"{{ES_URL}}/{{ES_INDEX}}/_count",
    auth=ES_AUTH,
    json=total_fragments_query,
    headers={{"Content-Type": "application/json"}}
)

if fragment_resp.status_code != 200:
    print(f"Error counting fragments: {{fragment_resp.text}}")
    fragment_count = 0
else:
    fragment_count = fragment_resp.json().get("count", 0)

# Compile results
es_results = {{
    "total_events": results["hits"]["total"]["value"],
    "captured_fragments": fragment_count,
    "protocol_distribution": {{
        bucket["key"]: bucket["doc_count"] 
        for bucket in results["aggregations"]["protocol_counts"]["buckets"]
    }},
    "fragment_stats": {{
        bucket["key_as_string"]: bucket["doc_count"] 
        for bucket in results["aggregations"]["fragment_counts"]["buckets"]
    }}
}}

print(json.dumps(es_results, indent=2))

# Save results
with open("/tmp/es_results.json", "w") as f:
    json.dump(es_results, f)
"#
    )
}

// Parsing analysis results
fn parse_analysis_results(output: &str) -> io::Result<AnalysisResults> {
    // Extract JSON from the output
    let json_start = output.find('{').ok_or_else(|| io::Error::new(
        io::ErrorKind::InvalidData, 
        "Invalid analysis output: no JSON found"
    ))?;
    
    let json_str = &output[json_start..];
    
    match serde_json::from_str::<AnalysisResults>(json_str) {
        Ok(results) => Ok(results),
        Err(e) => {
            eprintln!("Error parsing analysis results: {}", e);
            eprintln!("JSON string: {}", json_str);
            Err(io::Error::new(io::ErrorKind::InvalidData, e))
        }
    }
}

// Parsing Elasticsearch results
fn parse_elasticsearch_results(output: &str) -> io::Result<ESResults> {
    // Extract JSON from the output
    let json_start = output.find('{').ok_or_else(|| io::Error::new(
        io::ErrorKind::InvalidData, 
        "Invalid Elasticsearch output: no JSON found"
    ))?;
    
    let json_str = &output[json_start..];
    
    match serde_json::from_str::<ESResults>(json_str) {
        Ok(results) => Ok(results),
        Err(e) => {
            eprintln!("Error parsing Elasticsearch results: {}", e);
            eprintln!("JSON string: {}", json_str);
            Err(io::Error::new(io::ErrorKind::InvalidData, e))
        }
    }
}

// Analysis results structure
#[derive(Debug, Serialize, Deserialize)]
struct AnalysisResults {
    total_fragments: usize,
    unique_packets: usize,
    reassembled_packets: usize,
    reassembly_rate: f64,
    protocol_metrics: HashMap<String, usize>,
    fragment_size_metrics: HashMap<String, usize>,
    fragment_counts: HashMap<String, usize>,
    duration_seconds: f64,
    expected_packets: usize,
}

// Elasticsearch results structure
#[derive(Debug, Serialize, Deserialize)]
struct ESResults {
    total_events: usize,
    captured_fragments: usize,
    protocol_distribution: HashMap<String, usize>,
    fragment_stats: HashMap<String, usize>,
}

// Test result structure
#[derive(Debug, Serialize)]
struct TestResult {
    test_id: String,
    test_name: String,
    status: String,
    total_packets_generated: usize,
    total_fragments: usize,
    reassembled_packets: usize,
    reassembly_rate: f64,
    captured_fragments: usize,
    capture_rate: f64,
    duration_seconds: f64,
    metrics: Value,
}

// Main function
fn main() -> io::Result<()> {
    // Run the test
    match run_xdp_fragmented_packet_test() {
        Ok(result) => {
            // Write test results to file
            let results_json = serde_json::to_string_pretty(&result)
                .map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
            
            let result_path = PathBuf::from("/home/<USER>/cdev/inspector_gadget/.claude/test_results")
                .join(format!("xdp_fragmented_test_results_{}.json", 
                             chrono::Local::now().format("%Y%m%d_%H%M%S")));
            
            let mut file = File::create(result_path)?;
            file.write_all(results_json.as_bytes())?;
            
            println!("Test results written to file");
            Ok(())
        },
        Err(e) => {
            eprintln!("Test failed: {}", e);
            Err(e)
        }
    }
} 