# Elasticsearch Integration Test Implementation
Date: 2025-03-28

## Overview

This document outlines the implementation details for comprehensive testing of the Elasticsearch integration within the PIMP-Hooker_Pill system. These tests focus on real-world scenarios with actual Elasticsearch instances, avoiding mocks to ensure genuine integration verification.

## Test Environment

- **Elasticsearch Deployment**: 
  - Primary: Single-node Elasticsearch 7.17.9 
  - Failover: 3-node Elasticsearch 8.6.2 cluster
  - High-load: 5-node Elasticsearch 8.6.2 cluster with dedicated master nodes
- **Kibana**: 
  - Version 7.17.9 (paired with primary)
  - Version 8.6.2 (paired with failover and high-load)
- **Test Data Volume**:
  - Base dataset: 50,000 events
  - Mid-volume: 5 million events
  - High-volume: 50+ million events
- **Test Duration**: 
  - Basic tests: 1-2 hours
  - Extended tests: 24-48 hours

## Test Implementation Details

### 1. Connection and Authentication Tests

#### ES-CONN-01: Basic Connection Test
**Implementation**:
```rust
fn test_basic_connection() {
    // Configure test VM
    let vm = TestVM::new("ubuntu-22.04");
    vm.start().expect("Failed to start VM");
    
    // Deploy hooker_pill with ES basic auth configuration
    let pill_config = HookerPillConfig {
        es_url: "http://elasticsearch:9200",
        es_username: "elastic",
        es_password: "changeme",
        index_name: "test_basic_conn",
        batch_size: 100,
        flush_interval_ms: 1000,
    };
    
    let pill = HookerPill::new(pill_config);
    vm.inject_pill(pill).expect("Failed to inject pill");
    
    // Generate test events
    vm.execute_command("./binary_evaluation_test normal 100")
        .expect("Failed to run test binary");
    
    // Wait for events to be processed
    thread::sleep(Duration::from_secs(5));
    
    // Verify connection and data in Elasticsearch
    let es_client = ElasticsearchClient::new(
        "http://elasticsearch:9200", 
        "elastic", 
        "changeme"
    );
    
    let count = es_client.count_documents("test_basic_conn")
        .expect("Failed to count documents");
    
    assert!(count > 0, "No documents found in Elasticsearch");
    println!("Found {} documents in Elasticsearch", count);
    
    // Cleanup
    vm.stop().expect("Failed to stop VM");
    es_client.delete_index("test_basic_conn").expect("Failed to delete index");
}
```

#### ES-CONN-02: TLS Authentication Test
**Implementation**:
```rust
fn test_tls_connection() {
    // Set up TLS certificates
    let cert_path = prepare_test_certificates();
    
    // Configure test VM
    let vm = TestVM::new("ubuntu-22.04");
    vm.start().expect("Failed to start VM");
    
    // Deploy hooker_pill with TLS configuration
    let pill_config = HookerPillConfig {
        es_url: "https://elasticsearch:9200",
        es_username: "elastic",
        es_password: "changeme",
        ca_cert_path: cert_path.join("ca.crt").to_str().unwrap(),
        client_cert_path: cert_path.join("client.crt").to_str().unwrap(),
        client_key_path: cert_path.join("client.key").to_str().unwrap(),
        index_name: "test_tls_conn",
        batch_size: 100,
        flush_interval_ms: 1000,
    };
    
    let pill = HookerPill::new(pill_config);
    vm.inject_pill(pill).expect("Failed to inject pill");
    
    // Generate test events
    vm.execute_command("./binary_evaluation_test normal 100")
        .expect("Failed to run test binary");
    
    // Wait for events to be processed
    thread::sleep(Duration::from_secs(5));
    
    // Verify connection and data in Elasticsearch
    let es_client = ElasticsearchClient::new_with_tls(
        "https://elasticsearch:9200", 
        "elastic", 
        "changeme",
        cert_path.join("ca.crt").to_str().unwrap(),
        cert_path.join("client.crt").to_str().unwrap(),
        cert_path.join("client.key").to_str().unwrap()
    );
    
    let count = es_client.count_documents("test_tls_conn")
        .expect("Failed to count documents");
    
    assert!(count > 0, "No documents found in Elasticsearch");
    println!("Found {} documents in Elasticsearch", count);
    
    // Cleanup
    vm.stop().expect("Failed to stop VM");
    es_client.delete_index("test_tls_conn").expect("Failed to delete index");
}
```

### 2. Data Integrity Tests

#### ES-DATA-01: Schema Validation Test
**Implementation**:
```rust
fn test_schema_validation() {
    // Configure test VM
    let vm = TestVM::new("ubuntu-22.04");
    vm.start().expect("Failed to start VM");
    
    // Deploy hooker_pill
    let pill_config = HookerPillConfig {
        es_url: "http://elasticsearch:9200",
        es_username: "elastic",
        es_password: "changeme",
        index_name: "test_schema_validation",
        batch_size: 100,
        flush_interval_ms: 1000,
    };
    
    let pill = HookerPill::new(pill_config);
    vm.inject_pill(pill).expect("Failed to inject pill");
    
    // Generate test events for all hook types
    vm.execute_command("./binary_evaluation_test normal 100")
        .expect("Failed to run test binary");
    
    // Wait for events to be processed
    thread::sleep(Duration::from_secs(5));
    
    // Verify schema compliance
    let es_client = ElasticsearchClient::new(
        "http://elasticsearch:9200", 
        "elastic", 
        "changeme"
    );
    
    // Test LSM hook schema
    let lsm_events = es_client.query(
        "test_schema_validation",
        json!({
            "query": {
                "term": {
                    "hook_type": "lsm"
                }
            },
            "size": 10
        })
    ).expect("Failed to query LSM events");
    
    for event in lsm_events {
        validate_lsm_schema(&event);
    }
    
    // Test XDP hook schema
    let xdp_events = es_client.query(
        "test_schema_validation",
        json!({
            "query": {
                "term": {
                    "hook_type": "xdp"
                }
            },
            "size": 10
        })
    ).expect("Failed to query XDP events");
    
    for event in xdp_events {
        validate_xdp_schema(&event);
    }
    
    // Test Tracepoint hook schema
    let tracepoint_events = es_client.query(
        "test_schema_validation",
        json!({
            "query": {
                "term": {
                    "hook_type": "tracepoint"
                }
            },
            "size": 10
        })
    ).expect("Failed to query Tracepoint events");
    
    for event in tracepoint_events {
        validate_tracepoint_schema(&event);
    }
    
    // Test Uprobe hook schema
    let uprobe_events = es_client.query(
        "test_schema_validation",
        json!({
            "query": {
                "term": {
                    "hook_type": "uprobe"
                }
            },
            "size": 10
        })
    ).expect("Failed to query Uprobe events");
    
    for event in uprobe_events {
        validate_uprobe_schema(&event);
    }
    
    // Cleanup
    vm.stop().expect("Failed to stop VM");
    es_client.delete_index("test_schema_validation").expect("Failed to delete index");
}

fn validate_lsm_schema(event: &Value) {
    assert!(event.get("timestamp").is_some(), "Missing timestamp field");
    assert!(event.get("process_id").is_some(), "Missing process_id field");
    assert!(event.get("thread_id").is_some(), "Missing thread_id field");
    assert!(event.get("hook_type").is_some(), "Missing hook_type field");
    assert!(event.get("operation").is_some(), "Missing operation field");
    // LSM-specific fields
    assert!(event.get("security_context").is_some(), "Missing security_context field");
    assert!(event.get("target_path").is_some(), "Missing target_path field");
    assert!(event.get("result").is_some(), "Missing result field");
}

// Similar validation functions for other hook types
```

#### ES-DATA-02: Data Volume Test
**Implementation**:
```rust
fn test_data_volume() {
    // Configure test VM
    let vm = TestVM::new("ubuntu-22.04");
    vm.start().expect("Failed to start VM");
    
    // Deploy hooker_pill with bulk configuration
    let pill_config = HookerPillConfig {
        es_url: "http://elasticsearch:9200",
        es_username: "elastic",
        es_password: "changeme",
        index_name: "test_data_volume",
        batch_size: 10000,  // Larger batch size for volume
        flush_interval_ms: 5000,
    };
    
    let pill = HookerPill::new(pill_config);
    vm.inject_pill(pill).expect("Failed to inject pill");
    
    // Generate high volume of test events
    let event_generator = HighVolumeEventGenerator::new(1_000_000); // 1M events
    vm.execute_command(&format!("./volume_test {}", event_generator.config_file()))
        .expect("Failed to run volume test");
    
    // Wait for events to be processed
    let start_time = Instant::now();
    let mut last_count = 0;
    let mut stable_count_duration = Duration::from_secs(0);
    
    // Monitor until all events are processed or timeout
    while stable_count_duration < Duration::from_secs(30) && start_time.elapsed() < Duration::from_secs(600) {
        thread::sleep(Duration::from_secs(5));
        
        let es_client = ElasticsearchClient::new(
            "http://elasticsearch:9200", 
            "elastic", 
            "changeme"
        );
        
        let count = es_client.count_documents("test_data_volume")
            .expect("Failed to count documents");
        
        println!("Current document count: {}", count);
        
        if count == last_count {
            stable_count_duration += Duration::from_secs(5);
        } else {
            stable_count_duration = Duration::from_secs(0);
            last_count = count;
        }
    }
    
    // Verify data volume
    let es_client = ElasticsearchClient::new(
        "http://elasticsearch:9200", 
        "elastic", 
        "changeme"
    );
    
    let final_count = es_client.count_documents("test_data_volume")
        .expect("Failed to count documents");
    
    let expected_count = 1_000_000;
    let threshold = expected_count * 0.95; // Allow for 5% loss
    
    assert!(
        final_count >= threshold as u64, 
        "Document count {} is below threshold {} (expected {})",
        final_count, threshold, expected_count
    );
    
    // Get processing metrics
    let processing_time = start_time.elapsed();
    let events_per_second = final_count as f64 / processing_time.as_secs_f64();
    
    println!("Processed {} events in {:?} ({:.2} events/second)",
        final_count, processing_time, events_per_second);
    
    // Cleanup
    vm.stop().expect("Failed to stop VM");
    es_client.delete_index("test_data_volume").expect("Failed to delete index");
}
```

### 3. Resilience Tests

#### ES-RES-01: Network Disruption Test
**Implementation**:
```rust
fn test_network_disruption() {
    // Configure test VM
    let vm = TestVM::new("ubuntu-22.04");
    vm.start().expect("Failed to start VM");
    
    // Deploy hooker_pill with retry configuration
    let pill_config = HookerPillConfig {
        es_url: "http://elasticsearch:9200",
        es_username: "elastic",
        es_password: "changeme",
        index_name: "test_network_disruption",
        batch_size: 1000,
        flush_interval_ms: 5000,
        retry_max_attempts: 10,
        retry_initial_backoff_ms: 1000,
        retry_max_backoff_ms: 30000,
    };
    
    let pill = HookerPill::new(pill_config);
    vm.inject_pill(pill).expect("Failed to inject pill");
    
    // Start event generation
    let event_generator = Thread::spawn(move || {
        for i in 0..10 {
            vm.execute_command("./binary_evaluation_test normal 1000")
                .expect("Failed to run test binary");
            thread::sleep(Duration::from_secs(10));
        }
    });
    
    // Wait for initial events
    thread::sleep(Duration::from_secs(15));
    
    // Introduce network disruption
    println!("Introducing network disruption...");
    let network_disruptor = Thread::spawn(move || {
        // Block Elasticsearch traffic for 2 minutes
        vm.execute_command("iptables -A OUTPUT -p tcp --dport 9200 -j DROP")
            .expect("Failed to block Elasticsearch traffic");
        
        thread::sleep(Duration::from_secs(120));
        
        // Remove block
        vm.execute_command("iptables -D OUTPUT -p tcp --dport 9200 -j DROP")
            .expect("Failed to unblock Elasticsearch traffic");
        
        println!("Network connectivity restored");
    });
    
    // Wait for all events to complete
    event_generator.join().expect("Event generator thread failed");
    network_disruptor.join().expect("Network disruptor thread failed");
    
    // Allow time for recovery and processing
    thread::sleep(Duration::from_secs(60));
    
    // Verify data recovery
    let es_client = ElasticsearchClient::new(
        "http://elasticsearch:9200", 
        "elastic", 
        "changeme"
    );
    
    let count = es_client.count_documents("test_network_disruption")
        .expect("Failed to count documents");
    
    // We expect at least 9000 events (10 batches of 1000, allowing for some loss)
    let expected_minimum = 9000;
    
    assert!(
        count >= expected_minimum, 
        "Document count {} is below minimum expected {} after network disruption",
        count, expected_minimum
    );
    
    println!("Recovered {} documents after network disruption", count);
    
    // Cleanup
    vm.stop().expect("Failed to stop VM");
    es_client.delete_index("test_network_disruption").expect("Failed to delete index");
}
```

#### ES-RES-02: Elasticsearch Restart Test
**Implementation**:
```rust
fn test_elasticsearch_restart() {
    // Configure test VM
    let vm = TestVM::new("ubuntu-22.04");
    vm.start().expect("Failed to start VM");
    
    // Deploy hooker_pill with retry configuration
    let pill_config = HookerPillConfig {
        es_url: "http://elasticsearch:9200",
        es_username: "elastic",
        es_password: "changeme",
        index_name: "test_es_restart",
        batch_size: 1000,
        flush_interval_ms: 5000,
        retry_max_attempts: 10,
        retry_initial_backoff_ms: 1000,
        retry_max_backoff_ms: 30000,
    };
    
    let pill = HookerPill::new(pill_config);
    vm.inject_pill(pill).expect("Failed to inject pill");
    
    // Start event generation
    let event_generator = Thread::spawn(move || {
        for i in 0..10 {
            vm.execute_command("./binary_evaluation_test normal 1000")
                .expect("Failed to run test binary");
            thread::sleep(Duration::from_secs(10));
        }
    });
    
    // Wait for initial events
    thread::sleep(Duration::from_secs(15));
    
    // Restart Elasticsearch
    println!("Restarting Elasticsearch...");
    docker_command("restart", "elasticsearch").expect("Failed to restart Elasticsearch");
    
    // Wait for Elasticsearch to come back (typically 30-60 seconds)
    let mut es_available = false;
    let start_wait = Instant::now();
    
    while !es_available && start_wait.elapsed() < Duration::from_secs(120) {
        thread::sleep(Duration::from_secs(5));
        
        // Try to connect to Elasticsearch
        match ElasticsearchClient::new(
            "http://elasticsearch:9200", 
            "elastic", 
            "changeme"
        ).health_check() {
            Ok(_) => es_available = true,
            Err(_) => println!("Elasticsearch not yet available..."),
        }
    }
    
    assert!(es_available, "Elasticsearch did not become available after restart");
    println!("Elasticsearch restarted successfully after {:?}", start_wait.elapsed());
    
    // Wait for event generation to complete
    event_generator.join().expect("Event generator thread failed");
    
    // Allow time for recovery and processing
    thread::sleep(Duration::from_secs(60));
    
    // Verify data recovery
    let es_client = ElasticsearchClient::new(
        "http://elasticsearch:9200", 
        "elastic", 
        "changeme"
    );
    
    let count = es_client.count_documents("test_es_restart")
        .expect("Failed to count documents");
    
    // We expect at least 9000 events (10 batches of 1000, allowing for some loss)
    let expected_minimum = 9000;
    
    assert!(
        count >= expected_minimum, 
        "Document count {} is below minimum expected {} after Elasticsearch restart",
        count, expected_minimum
    );
    
    println!("Recovered {} documents after Elasticsearch restart", count);
    
    // Cleanup
    vm.stop().expect("Failed to stop VM");
    es_client.delete_index("test_es_restart").expect("Failed to delete index");
}
```

### 4. Performance Tests

#### ES-PERF-01: Bulk Indexing Test
**Implementation**:
```rust
fn test_bulk_indexing_performance() {
    // Configure test VM with high-performance settings
    let vm = TestVM::new_with_resources("ubuntu-22.04", 8, 16384); // 8 cores, 16GB RAM
    vm.start().expect("Failed to start VM");
    
    // Deploy hooker_pill with optimized bulk settings
    let pill_config = HookerPillConfig {
        es_url: "http://elasticsearch:9200",
        es_username: "elastic",
        es_password: "changeme",
        index_name: "test_bulk_performance",
        batch_size: 50000,  // Large batch size for bulk testing
        flush_interval_ms: 10000,
        concurrent_connections: 4,
        compression_enabled: true,
        max_retry_wait_ms: 60000,
    };
    
    let pill = HookerPill::new(pill_config);
    vm.inject_pill(pill).expect("Failed to inject pill");
    
    // Generate a large volume of events with varied types
    println!("Generating 5 million events...");
    let start_time = Instant::now();
    
    vm.execute_command("./performance_test --event-count 5000000 --mix-types")
        .expect("Failed to run performance test");
    
    println!("Event generation completed in {:?}", start_time.elapsed());
    
    // Monitor indexing progress
    let index_monitor_start = Instant::now();
    let mut last_count = 0;
    let mut stable_count_duration = Duration::from_secs(0);
    
    while stable_count_duration < Duration::from_secs(60) && index_monitor_start.elapsed() < Duration::from_secs(1800) {
        thread::sleep(Duration::from_secs(10));
        
        let es_client = ElasticsearchClient::new(
            "http://elasticsearch:9200", 
            "elastic", 
            "changeme"
        );
        
        match es_client.count_documents("test_bulk_performance") {
            Ok(count) => {
                println!("Current document count: {} ({:.2}%)", 
                    count, 
                    (count as f64 / 5_000_000.0) * 100.0);
                
                if count == last_count {
                    stable_count_duration += Duration::from_secs(10);
                } else {
                    stable_count_duration = Duration::from_secs(0);
                    last_count = count;
                }
            },
            Err(e) => {
                println!("Error getting count: {}", e);
                stable_count_duration = Duration::from_secs(0);
            }
        }
    }
    
    // Calculate performance metrics
    let total_elapsed = index_monitor_start.elapsed();
    let es_client = ElasticsearchClient::new(
        "http://elasticsearch:9200", 
        "elastic", 
        "changeme"
    );
    
    let final_count = es_client.count_documents("test_bulk_performance")
        .expect("Failed to count documents");
    
    let throughput = final_count as f64 / total_elapsed.as_secs_f64();
    
    println!("Bulk Indexing Performance:");
    println!("  Total documents indexed: {}", final_count);
    println!("  Total time: {:?}", total_elapsed);
    println!("  Throughput: {:.2} events/second", throughput);
    
    // Verify minimum throughput expectations
    assert!(
        throughput >= 5000.0,
        "Throughput {:.2} events/second is below minimum expected 5000/second",
        throughput
    );
    
    // Verify document count
    let count_percentage = (final_count as f64 / 5_000_000.0) * 100.0;
    assert!(
        count_percentage >= 95.0,
        "Only {:.2}% of documents were successfully indexed (expected >=95%)",
        count_percentage
    );
    
    // Cleanup
    vm.stop().expect("Failed to stop VM");
    es_client.delete_index("test_bulk_performance").expect("Failed to delete index");
}
```

#### ES-PERF-02: Query Performance Test
**Implementation**:
```rust
fn test_query_performance() {
    // First load test data if not already present
    let es_client = ElasticsearchClient::new(
        "http://elasticsearch:9200", 
        "elastic", 
        "changeme"
    );
    
    let index_name = "test_query_performance";
    let has_data = es_client.index_exists(index_name).unwrap_or(false);
    
    if !has_data {
        println!("Loading test dataset for query performance testing...");
        // Load 1 million documents for query testing
        let loader = TestDataLoader::new(1_000_000);
        loader.load_to_elasticsearch(es_client.clone(), index_name)
            .expect("Failed to load test data");
        
        // Wait for indexing to complete
        thread::sleep(Duration::from_secs(60));
    }
    
    // Ensure index has expected document count
    let count = es_client.count_documents(index_name)
        .expect("Failed to count documents");
    
    assert!(
        count >= 1_000_000,
        "Index has insufficient documents for testing: {}/1,000,000",
        count
    );
    
    println!("Running query performance tests against {} documents", count);
    
    // Test 1: Simple term query
    let term_query = json!({
        "query": {
            "term": {
                "hook_type": "tracepoint"
            }
        }
    });
    
    let term_results = run_timed_query(&es_client, index_name, term_query, 10);
    println!("Term query average time: {:.2}ms", term_results.average_ms);
    assert!(
        term_results.average_ms < 100.0,
        "Term query average time {:.2}ms exceeds threshold of 100ms",
        term_results.average_ms
    );
    
    // Test 2: Range query
    let now = chrono::Utc::now();
    let one_day_ago = now - chrono::Duration::days(1);
    
    let range_query = json!({
        "query": {
            "range": {
                "timestamp": {
                    "gte": one_day_ago.to_rfc3339(),
                    "lte": now.to_rfc3339()
                }
            }
        }
    });
    
    let range_results = run_timed_query(&es_client, index_name, range_query, 10);
    println!("Range query average time: {:.2}ms", range_results.average_ms);
    assert!(
        range_results.average_ms < 200.0,
        "Range query average time {:.2}ms exceeds threshold of 200ms",
        range_results.average_ms
    );
    
    // Test 3: Complex boolean query
    let bool_query = json!({
        "query": {
            "bool": {
                "must": [
                    { "term": { "hook_type": "xdp" } }
                ],
                "filter": [
                    { "range": { "timestamp": { "gte": "now-1h" } } }
                ],
                "should": [
                    { "term": { "protocol": "TCP" } },
                    { "term": { "protocol": "UDP" } }
                ],
                "minimum_should_match": 1
            }
        }
    });
    
    let bool_results = run_timed_query(&es_client, index_name, bool_query, 10);
    println!("Boolean query average time: {:.2}ms", bool_results.average_ms);
    assert!(
        bool_results.average_ms < 300.0,
        "Boolean query average time {:.2}ms exceeds threshold of 300ms",
        bool_results.average_ms
    );
    
    // Test 4: Aggregation query
    let agg_query = json!({
        "size": 0,
        "aggs": {
            "hook_types": {
                "terms": {
                    "field": "hook_type",
                    "size": 10
                },
                "aggs": {
                    "hourly": {
                        "date_histogram": {
                            "field": "timestamp",
                            "calendar_interval": "hour"
                        }
                    }
                }
            }
        }
    });
    
    let agg_results = run_timed_query(&es_client, index_name, agg_query, 5);
    println!("Aggregation query average time: {:.2}ms", agg_results.average_ms);
    assert!(
        agg_results.average_ms < 1000.0,
        "Aggregation query average time {:.2}ms exceeds threshold of 1000ms",
        agg_results.average_ms
    );
    
    // No cleanup - keep the index for future performance tests
    println!("Query performance tests completed successfully");
}

struct QueryResult {
    average_ms: f64,
    min_ms: f64,
    max_ms: f64,
    times_ms: Vec<f64>,
}

fn run_timed_query(client: &ElasticsearchClient, index: &str, query: Value, iterations: usize) -> QueryResult {
    let mut times_ms = Vec::with_capacity(iterations);
    
    for i in 0..iterations {
        let start = Instant::now();
        let _result = client.query(index, query.clone())
            .expect("Query failed");
        let elapsed = start.elapsed();
        let ms = elapsed.as_secs_f64() * 1000.0;
        times_ms.push(ms);
    }
    
    let total: f64 = times_ms.iter().sum();
    let average = total / iterations as f64;
    let min = *times_ms.iter().min_by(|a, b| a.partial_cmp(b).unwrap()).unwrap();
    let max = *times_ms.iter().max_by(|a, b| a.partial_cmp(b).unwrap()).unwrap();
    
    QueryResult {
        average_ms: average,
        min_ms: min,
        max_ms: max,
        times_ms,
    }
}
```

### 5. Index Management Tests

#### ES-IDX-01: Index Rotation Test
**Implementation**:
```rust
fn test_index_rotation() {
    // Configure test VM
    let vm = TestVM::new("ubuntu-22.04");
    vm.start().expect("Failed to start VM");
    
    // Deploy hooker_pill with time-based index rotation
    let pill_config = HookerPillConfig {
        es_url: "http://elasticsearch:9200",
        es_username: "elastic",
        es_password: "changeme",
        index_prefix: "test_rotation",  // Will create time-based indices
        index_date_pattern: "yyyy.MM.dd", // Daily rotation
        index_rollover_threshold: 1000, // Rollover after 1000 documents
        batch_size: 100,
        flush_interval_ms: 1000,
    };
    
    let pill = HookerPill::new(pill_config);
    vm.inject_pill(pill).expect("Failed to inject pill");
    
    // Generate events over a simulated time period
    // This requires custom test code that generates events with timestamps
    // spanning multiple days
    let time_range_generator = TimeRangeEventGenerator::new(
        chrono::Duration::days(5), // 5 days of data
        10000 // 10k events per day
    );
    
    vm.upload_file("time_range_generator.json", time_range_generator.config_file())
        .expect("Failed to upload generator config");
    
    vm.execute_command("./time_range_test time_range_generator.json")
        .expect("Failed to run time range test");
    
    // Wait for events to be processed
    thread::sleep(Duration::from_secs(60));
    
    // Check for rotated indices
    let es_client = ElasticsearchClient::new(
        "http://elasticsearch:9200", 
        "elastic", 
        "changeme"
    );
    
    let indices = es_client.list_indices("test_rotation-*")
        .expect("Failed to list indices");
    
    println!("Found {} rotated indices", indices.len());
    
    // We expect at least 5 indices (one per day)
    assert!(
        indices.len() >= 5,
        "Expected at least 5 rotated indices, found {}",
        indices.len()
    );
    
    // Check document distribution
    for index in &indices {
        let count = es_client.count_documents(index)
            .expect(&format!("Failed to count documents in {}", index));
        
        println!("Index {}: {} documents", index, count);
    }
    
    // Test ILM policy application if configured
    let ilm_enabled = es_client.get_ilm_policy("test_rotation")
        .is_ok();
    
    if ilm_enabled {
        println!("ILM policy is configured correctly");
    } else {
        println!("Note: ILM policy not configured for test indices");
    }
    
    // Cleanup
    vm.stop().expect("Failed to stop VM");
    
    for index in indices {
        es_client.delete_index(&index).expect(&format!("Failed to delete index {}", index));
    }
}
```

## Additional Utilities

### Event Generators

```rust
// High volume event generator
struct HighVolumeEventGenerator {
    event_count: usize,
    config_path: PathBuf,
}

impl HighVolumeEventGenerator {
    fn new(event_count: usize) -> Self {
        let config = json!({
            "event_count": event_count,
            "burst_size": 10000,
            "burst_delay_ms": 500,
            "event_types": ["lsm", "xdp", "tracepoint", "uprobe"],
            "distribution": {
                "lsm": 0.2,
                "xdp": 0.3,
                "tracepoint": 0.4,
                "uprobe": 0.1
            }
        });
        
        let temp_dir = tempfile::tempdir().expect("Failed to create temp dir");
        let config_path = temp_dir.path().join("volume_config.json");
        
        std::fs::write(
            &config_path,
            serde_json::to_string_pretty(&config).expect("Failed to serialize config")
        ).expect("Failed to write config file");
        
        Self {
            event_count,
            config_path,
        }
    }
    
    fn config_file(&self) -> &str {
        self.config_path.to_str().unwrap()
    }
}

// Time range event generator
struct TimeRangeEventGenerator {
    duration: chrono::Duration,
    events_per_day: usize,
    config_path: PathBuf,
}

impl TimeRangeEventGenerator {
    fn new(duration: chrono::Duration, events_per_day: usize) -> Self {
        let end_time = chrono::Utc::now();
        let start_time = end_time - duration;
        
        let config = json!({
            "start_time": start_time.to_rfc3339(),
            "end_time": end_time.to_rfc3339(),
            "events_per_day": events_per_day,
            "event_types": ["lsm", "xdp", "tracepoint", "uprobe"],
            "distribution": {
                "lsm": 0.2,
                "xdp": 0.3,
                "tracepoint": 0.4,
                "uprobe": 0.1
            }
        });
        
        let temp_dir = tempfile::tempdir().expect("Failed to create temp dir");
        let config_path = temp_dir.path().join("timerange_config.json");
        
        std::fs::write(
            &config_path,
            serde_json::to_string_pretty(&config).expect("Failed to serialize config")
        ).expect("Failed to write config file");
        
        Self {
            duration,
            events_per_day,
            config_path,
        }
    }
    
    fn config_file(&self) -> &str {
        self.config_path.to_str().unwrap()
    }
}
```

## Test Execution Schedule

| Week | Day | Tests |
|------|-----|-------|
| Week 1 | Monday | ES-CONN-01, ES-CONN-02 |
| Week 1 | Tuesday | ES-DATA-01, ES-DATA-02 |
| Week 1 | Wednesday | ES-RES-01, ES-RES-02 |
| Week 1 | Thursday | ES-PERF-01 |
| Week 1 | Friday | ES-PERF-02, ES-IDX-01 |

## Dashboard Validation

For dashboard validation, we will:

1. Use the data generated in these tests to create standard dashboards
2. Create scripted validations that verify:
   - All visualizations display correctly
   - Filters function as expected
   - Metrics match expected values
   - Time-based navigation works

## Next Steps

1. Implement all test cases in actual code
2. Create test automation framework for running these tests in CI/CD pipeline
3. Set up comprehensive test reporting
4. Extend tests to cover additional edge cases as identified 