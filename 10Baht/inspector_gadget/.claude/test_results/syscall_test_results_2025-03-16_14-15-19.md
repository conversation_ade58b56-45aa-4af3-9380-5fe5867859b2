# SyscallInterceptor Test Results - 2025-03-16_14-15-19
Test run started at: Sun 16 Mar 14:15:19 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Kernel: 6.9.3-76060903-generic

---


## Command: setup_python_env (2025-03-16_14-15-19)
**Description**: Setting up Python virtual environment with required packages

**Results**:
```
Setting up Python virtual environment...
Collecting rich
  Downloading rich-13.9.4-py3-none-any.whl (242 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 242.4/242.4 KB 1.8 MB/s eta 0:00:00
Collecting typer
  Downloading typer-0.15.2-py3-none-any.whl (45 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 45.1/45.1 KB 2.2 MB/s eta 0:00:00
Collecting pydantic
  Downloading pydantic-2.10.6-py3-none-any.whl (431 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 431.7/431.7 KB 15.8 MB/s eta 0:00:00
Collecting markdown-it-py>=2.2.0
  Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 87.5/87.5 KB 5.1 MB/s eta 0:00:00
Collecting pygments<3.0.0,>=2.13.0
  Downloading pygments-2.19.1-py3-none-any.whl (1.2 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 20.9 MB/s eta 0:00:00
Collecting typing-extensions<5.0,>=4.0.0
  Downloading typing_extensions-4.12.2-py3-none-any.whl (37 kB)
Collecting click>=8.0.0
  Downloading click-8.1.8-py3-none-any.whl (98 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 98.2/98.2 KB 5.0 MB/s eta 0:00:00
Collecting shellingham>=1.3.0
  Downloading shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
Collecting annotated-types>=0.6.0
  Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Collecting pydantic-core==2.27.2
  Downloading pydantic_core-2.27.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 15.0 MB/s eta 0:00:00
Collecting mdurl~=0.1
  Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Installing collected packages: typing-extensions, shellingham, pygments, mdurl, click, annotated-types, pydantic-core, markdown-it-py, rich, pydantic, typer
Successfully installed annotated-types-0.7.0 click-8.1.8 markdown-it-py-3.0.0 mdurl-0.1.2 pydantic-2.10.6 pydantic-core-2.27.2 pygments-2.19.1 rich-13.9.4 shellingham-1.5.4 typer-0.15.2 typing-extensions-4.12.2
Python virtual environment setup complete.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:15:55 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py status (2025-03-16_14-15-19)
**Description**: Check the status of the pimp.py tool and Vagrant VMs

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:15:56 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py setup (2025-03-16_14-15-19)
**Description**: Set up the environment for testing

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:15:57 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py inject --hooker hooker_pill (2025-03-16_14-15-19)
**Description**: Inject hooker_pills into Vagrant VMs

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:15:57 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test (2025-03-16_14-15-19)
**Description**: Run the SyscallInterceptor tests

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:15:58 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_high_throughput (2025-03-16_14-15-19)
**Description**: Run the high throughput test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:15:58 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_percpu_maps_integration --features percpu_maps (2025-03-16_14-15-19)
**Description**: Run the Per-CPU Maps integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:15:59 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_xdp_integration --features xdp (2025-03-16_14-15-19)
**Description**: Run the XDP integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:15:59 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_tracepoint_integration --features tracepoint (2025-03-16_14-15-19)
**Description**: Run the Tracepoint integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:00 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py cleanup (2025-03-16_14-15-19)
**Description**: Clean up the environment after testing

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:01 CET 2025

---


## Summary
Test run completed at: Sun 16 Mar 14:16:01 CET 2025

### Test Results
- Passed: 10
- Failed: 0
0
