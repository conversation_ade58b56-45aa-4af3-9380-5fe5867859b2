# XDP and Uprobe Hooking Test Results (Nix-Only) - 20250316_171218

## Environment
- Date: Sun 16 Mar 17:12:18 CET 2025
- Kernel: 6.9.3-76060903-generic

## Checking Nix Environment
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
Nix shell is working
rustc 1.75.0 (82e1608df 2023-12-21)
cargo 1.75.0 (1d8b05cdd 2023-11-20)
```
✅ Success

## Checking eBPF Compilation Tools
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
/bin/sh: symbol lookup error: /nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib/libc.so.6: undefined symbol: __tunable_is_initialized, version GLIBC_PRIVATE
Required tools not found
```
✅ Success

## Checking Compilation with SKIP_EBPF_COMPILATION
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/xdp_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `xdp_percpu_hooker_test`
  * `integration-test` target `xdp_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/uprobe_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `uprobe_percpu_hooker_test`
  * `integration-test` target `uprobe_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/tracepoint_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `tracepoint_percpu_hooker_test`
  * `integration-test` target `tracepoint_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/lsm_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `lsm_percpu_hooker_test`
  * `integration-test` target `lsm_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/lrumap_pill_test.rs` found to be present in multiple build targets:
  * `bin` target `lrumap_pill_test`
  * `integration-test` target `lrumap_pill_test`
   Compiling inspector_gadget v0.2.0 (/home/<USER>/dev/inspector_gadget)
warning: inspector_gadget@0.2.0: Skipping eBPF compilation as requested by SKIP_EBPF_COMPILATION
error: missing #[error("...")] display attribute
  --> src/error.rs:18:5
   |
18 | /     /// I/O error
19 | |     Io(io::Error),
   | |_________________^

error: couldn't read /home/<USER>/dev/inspector_gadget/target/debug/build/inspector_gadget-6ed591acd145629d/out/fileless_detector.bpf.o: No such file or directory (os error 2)
  --> src/ebpf/programs/fileless_detector.rs:22:38
   |
22 |   const FILELESS_DETECTOR_BPF: &[u8] = include_bytes!(concat!(
   |  ______________________________________^
23 | |     env!("OUT_DIR"),
24 | |     "/fileless_detector.bpf.o"
25 | | ));
   | |__^
   |
   = note: this error originates in the macro `include_bytes` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0255]: the name `TraceEvent` is defined multiple times
   --> src/core/mod.rs:140:1
    |
9   |     EventType as ImportedEventType, EventData as ImportedEventData, TraceEvent, EventSeverity,
    |                                                                     ---------- previous import of the type `TraceEvent` here
...
140 | pub struct TraceEvent {
    | ^^^^^^^^^^^^^^^^^^^^^ `TraceEvent` redefined here
    |
    = note: `TraceEvent` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
    |
9   |     EventType as ImportedEventType, EventData as ImportedEventData, TraceEvent as OtherTraceEvent, EventSeverity,
    |                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

error[E0252]: the name `EventBuffer` is defined multiple times
  --> src/core/mod.rs:17:5
   |
10 |     EventBuffer, EventBufferConfig, BufferOverflowBehavior, EventBufferStats,
   |     ----------- previous import of the type `EventBuffer` here
...
17 |     EventBuffer, BufferStats,
   |     ^^^^^^^^^^^ `EventBuffer` reimported here
   |
   = note: `EventBuffer` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
17 |     EventBuffer as OtherEventBuffer, BufferStats,
   |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

error[E0252]: the name `EventProcessor` is defined multiple times
  --> src/core/mod.rs:25:5
   |
11 |     EventProcessor, EventExporter, EventCollectionSystem,
   |     -------------- previous import of the trait `EventProcessor` here
...
25 |     EventProcessor, PipelineProcessor, PipelineProcessorConfig,
   |     ^^^^^^^^^^^^^^ `EventProcessor` reimported here
   |
   = note: `EventProcessor` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
25 |     EventProcessor as OtherEventProcessor, PipelineProcessor, PipelineProcessorConfig,
   |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

error[E0252]: the name `EventExporter` is defined multiple times
  --> src/core/mod.rs:35:5
   |
11 |     EventProcessor, EventExporter, EventCollectionSystem,
   |                     ------------- previous import of the trait `EventExporter` here
...
35 |     EventExporter, ElasticsearchExporter, ElasticsearchConfig,
   |     ^^^^^^^^^^^^^ `EventExporter` reimported here
   |
   = note: `EventExporter` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
35 |     EventExporter as OtherEventExporter, ElasticsearchExporter, ElasticsearchConfig,
   |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

error[E0255]: the name `SyscallEvent` is defined multiple times
  --> src/core/events.rs:83:1
   |
18 | use crate::syscalls::SyscallEvent;
   |     ----------------------------- previous import of the type `SyscallEvent` here
...
83 | pub struct SyscallEvent {
   | ^^^^^^^^^^^^^^^^^^^^^^^ `SyscallEvent` redefined here
   |
   = note: `SyscallEvent` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
18 | use crate::syscalls::SyscallEvent as OtherSyscallEvent;
   |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

error[E0252]: the name `ElasticsearchConfig` is defined multiple times
  --> src/logging/mod.rs:13:46
   |
8  | pub use self::elasticsearch::{ElasticsearchConfig, ElasticsearchExporter};
   |                               ------------------- previous import of the type `ElasticsearchConfig` here
...
13 | pub use elasticsearch::{ElasticsearchLogger, ElasticsearchConfig}; 
   |                                              ^^^^^^^^^^^^^^^^^^^ `ElasticsearchConfig` reimported here
   |
   = note: `ElasticsearchConfig` must be defined only once in the type namespace of this module

error[E0432]: unresolved imports `mapping::get_syscall_category`, `mapping::get_syscall_name`, `mapping::get_syscalls_for_platform`, `mapping::init_syscall_registry`
  --> src/syscalls/common/mod.rs:17:18
   |
17 |     get_syscall, get_syscall_category, get_syscall_name, get_syscalls_by_category,
   |                  ^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^ no `get_syscall_name` in `syscalls::common::mapping`
   |                  |
   |                  no `get_syscall_category` in `syscalls::common::mapping`
18 |     get_syscalls_for_platform, init_syscall_registry, register_syscall,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^ no `init_syscall_registry` in `syscalls::common::mapping`
   |     |
   |     no `get_syscalls_for_platform` in `syscalls::common::mapping`
   |
help: a similar name exists in the module
   |
17 |     get_syscall, get_syscalls_by_category, get_syscall_name, get_syscalls_by_category,
   |                  ~~~~~~~~~~~~~~~~~~~~~~~~
help: a similar name exists in the module
   |
17 |     get_syscall, get_syscall_category, get_syscall_by_name, get_syscalls_by_category,
   |                                        ~~~~~~~~~~~~~~~~~~~
help: a similar name exists in the module
   |
18 |     get_syscall_count_for_platform, init_syscall_registry, register_syscall,
   |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

error[E0432]: unresolved import `tokio::sync::Condvar`
 --> src/core/buffer/circular.rs:9:26
  |
9 | use tokio::sync::{Mutex, Condvar};
  |                          ^^^^^^^ no `Condvar` in `sync`
  |
  = help: consider importing one of these items instead:
          parking_lot::Condvar
          std::sync::Condvar

error[E0432]: unresolved import `crate::core::tracer`
  --> src/platforms/windows/etw.rs:11:18
   |
11 | use crate::core::tracer::{SyscallFilter, TraceEvent, Tracer};
   |                  ^^^^^^ could not find `tracer` in `core`

error[E0432]: unresolved import `crate::error::PlatformError`
  --> src/platforms/windows/etw.rs:12:27
   |
12 | use crate::error::{Error, PlatformError, Result};
   |                           ^^^^^^^^^^^^^ no `PlatformError` in `error`

error[E0432]: unresolved import `regex`
  --> src/core/processor/filter.rs:11:5
   |
11 | use regex::Regex;
   |     ^^^^^ use of undeclared crate or module `regex`

error[E0432]: unresolved import `crate::core::events::Event`
  --> src/core/processor/parallel.rs:15:5
   |
15 | use crate::core::events::Event;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ no `Event` in `core::events`

error[E0433]: failed to resolve: use of undeclared crate or module `futures`
  --> src/core/processor/mod.rs:29:5
   |
29 | use futures::future::join_all;
   |     ^^^^^^^ use of undeclared crate or module `futures`

error[E0432]: unresolved import `crate::core::events::Event`
  --> src/core/processor/plugin.rs:12:5
   |
12 | use crate::core::events::Event;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ no `Event` in `core::events`

error[E0432]: unresolved import `crate::core::events::Event`
  --> src/core/processor/mod.rs:31:5
   |
31 | use crate::core::events::Event;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ no `Event` in `core::events`

error[E0433]: failed to resolve: use of undeclared crate or module `elasticsearch`
 --> src/core/exporter/elasticsearch.rs:7:5
  |
7 | use elasticsearch::{
  |     ^^^^^^^^^^^^^ use of undeclared crate or module `elasticsearch`

error[E0433]: failed to resolve: use of undeclared crate or module `elasticsearch`
  --> src/core/exporter/elasticsearch.rs:16:5
   |
16 | use elasticsearch::http::StatusCode;
   |     ^^^^^^^^^^^^^ use of undeclared crate or module `elasticsearch`

error[E0432]: unresolved import `elasticsearch`
 --> src/core/exporter/elasticsearch.rs:7:5
  |
7 | use elasticsearch::{
  |     ^^^^^^^^^^^^^ help: a similar path exists: `crate::elasticsearch`
  |
  = note: `use` statements changed in Rust 2018; read more at <https://doc.rust-lang.org/edition-guide/rust-2018/module-system/path-clarity.html>

error[E0432]: unresolved import `url`
  --> src/core/exporter/elasticsearch.rs:22:5
   |
22 | use url::Url;
   |     ^^^ use of undeclared crate or module `url`

error[E0432]: unresolved imports `crate::core::events::Event`, `crate::core::events::EventId`
  --> src/core/exporter/elasticsearch.rs:24:27
   |
24 | use crate::core::events::{Event, EventId};
   |                           ^^^^^  ^^^^^^^ no `EventId` in `core::events`
   |                           |
   |                           no `Event` in `core::events`

error[E0432]: unresolved import `crate::core::events::Event`
  --> src/core/exporter/file.rs:16:5
   |
16 | use crate::core::events::Event;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ no `Event` in `core::events`

error[E0432]: unresolved import `crate::core::events::Event`
  --> src/core/exporter/mod.rs:17:5
   |
17 | use crate::core::events::Event;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ no `Event` in `core::events`

error[E0432]: unresolved import `crate::core::events::Event`
  --> src/core/collector.rs:17:5
   |
17 | use crate::core::events::Event;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ no `Event` in `core::events`

error[E0432]: unresolved import `elasticsearch::ElasticsearchLogger`
  --> src/logging/mod.rs:13:25
   |
13 | pub use elasticsearch::{ElasticsearchLogger, ElasticsearchConfig}; 
   |                         ^^^^^^^^^^^^^^^^^^^
   |                         |
   |                         no `ElasticsearchLogger` in `logging::elasticsearch`
   |                         help: a similar name exists in the module: `ElasticsearchExporter`
   |
   = help: consider importing this struct through its public re-export instead:
           crate::ElasticsearchLogger

error[E0432]: unresolved import `crate::logging::elasticsearch::ElasticsearchLogger`
 --> src/ebpf/maps/percpu_elasticsearch.rs:3:5
  |
3 | use crate::logging::elasticsearch::ElasticsearchLogger;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ no `ElasticsearchLogger` in `logging::elasticsearch`
  |
help: a similar name exists in the module
  |
3 | use crate::logging::elasticsearch::ElasticsearchExporter;
  |                                    ~~~~~~~~~~~~~~~~~~~~~
help: consider importing one of these items instead
  |
3 | use crate::ElasticsearchLogger;
  |     ~~~~~~~~~~~~~~~~~~~~~~~~~~
3 | use crate::logging::ElasticsearchLogger;
  |     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

error[E0432]: unresolved imports `crate::hookers::lsm_hooker::LsmEvent`, `crate::hookers::lsm_hooker::LsmHookerConfig`
 --> src/hookers/lsm_percpu_hooker.rs:2:45
  |
2 | use crate::hookers::lsm_hooker::{LsmHooker, LsmEvent, LsmHookerConfig};
  |                                             ^^^^^^^^  ^^^^^^^^^^^^^^^ no `LsmHookerConfig` in `hookers::lsm_hooker`
  |                                             |
  |                                             no `LsmEvent` in `hookers::lsm_hooker`

error[E0432]: unresolved imports `crate::hookers::xdp_hooker::XdpEvent`, `crate::hookers::xdp_hooker::XdpHookerConfig`
 --> src/hookers/xdp_percpu_hooker.rs:2:45
  |
2 | use crate::hookers::xdp_hooker::{XdpHooker, XdpEvent, XdpHookerConfig};
  |                                             ^^^^^^^^  ^^^^^^^^^^^^^^^ no `XdpHookerConfig` in `hookers::xdp_hooker`
  |                                             |
  |                                             no `XdpEvent` in `hookers::xdp_hooker`

error[E0432]: unresolved imports `crate::hookers::tracepoint_hooker::TracepointEvent`, `crate::hookers::tracepoint_hooker::TracepointHookerConfig`
 --> src/hookers/tracepoint_percpu_hooker.rs:2:59
  |
2 | use crate::hookers::tracepoint_hooker::{TracepointHooker, TracepointEvent, TracepointHookerConfig};
  |                                                           ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
  |                                                           |                |
  |                                                           |                no `TracepointHookerConfig` in `hookers::tracepoint_hooker`
  |                                                           |                help: a similar name exists in the module: `TracepointHooker`
  |                                                           no `TracepointEvent` in `hookers::tracepoint_hooker`

error[E0432]: unresolved imports `crate::hookers::uprobe_hooker::UprobeEvent`, `crate::hookers::uprobe_hooker::UprobeHookerConfig`
 --> src/hookers/uprobe_percpu_hooker.rs:2:51
  |
2 | use crate::hookers::uprobe_hooker::{UprobeHooker, UprobeEvent, UprobeHookerConfig};
  |                                                   ^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^
  |                                                   |            |
  |                                                   |            no `UprobeHookerConfig` in `hookers::uprobe_hooker`
  |                                                   |            help: a similar name exists in the module: `UprobeHooker`
  |                                                   no `UprobeEvent` in `hookers::uprobe_hooker`

error[E0432]: unresolved imports `lsm_hooker::LsmEvent`, `lsm_hooker::LsmHookerConfig`
  --> src/hookers/mod.rs:15:33
   |
15 | pub use lsm_hooker::{LsmHooker, LsmEvent, LsmHookerConfig, LsmHookerStats};
   |                                 ^^^^^^^^  ^^^^^^^^^^^^^^^ no `LsmHookerConfig` in `hookers::lsm_hooker`
   |                                 |
   |                                 no `LsmEvent` in `hookers::lsm_hooker`

error[E0432]: unresolved imports `xdp_hooker::XdpEvent`, `xdp_hooker::XdpHookerConfig`
  --> src/hookers/mod.rs:17:33
   |
17 | pub use xdp_hooker::{XdpHooker, XdpEvent, XdpHookerConfig, XdpHookerStats};
   |                                 ^^^^^^^^  ^^^^^^^^^^^^^^^ no `XdpHookerConfig` in `hookers::xdp_hooker`
   |                                 |
   |                                 no `XdpEvent` in `hookers::xdp_hooker`

error[E0432]: unresolved imports `tracepoint_hooker::TracepointEvent`, `tracepoint_hooker::TracepointHookerConfig`
  --> src/hookers/mod.rs:19:47
   |
19 | pub use tracepoint_hooker::{TracepointHooker, TracepointEvent, TracepointHookerConfig, TracepointHookerStats};
   |                                               ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
   |                                               |                |
   |                                               |                no `TracepointHookerConfig` in `hookers::tracepoint_hooker`
   |                                               |                help: a similar name exists in the module: `TracepointHooker`
   |                                               no `TracepointEvent` in `hookers::tracepoint_hooker`

error[E0432]: unresolved imports `uprobe_hooker::UprobeEvent`, `uprobe_hooker::UprobeHookerConfig`
  --> src/hookers/mod.rs:21:39
   |
21 | pub use uprobe_hooker::{UprobeHooker, UprobeEvent, UprobeHookerConfig, UprobeHookerStats};
   |                                       ^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^
   |                                       |            |
   |                                       |            no `UprobeHookerConfig` in `hookers::uprobe_hooker`
   |                                       |            help: a similar name exists in the module: `UprobeHooker`
   |                                       no `UprobeEvent` in `hookers::uprobe_hooker`

error[E0106]: missing lifetime specifier
  --> src/binary/pe/mod.rs:20:9
   |
20 |     pe: PE,
   |         ^^ expected named lifetime parameter
   |
help: consider introducing a named lifetime parameter
   |
16 ~ pub struct PeBinary<'a> {
17 |     /// Path to the binary file
18 |     path: Option<PathBuf>,
19 |     /// Parsed PE data
20 ~     pe: PE<'a>,
   |

error[E0433]: failed to resolve: could not find `PlatformError` in `error`
   --> src/syscalls/linux/mod.rs:279:31
    |
279 |                 crate::error::PlatformError::Linux(
    |                               ^^^^^^^^^^^^^ could not find `PlatformError` in `error`

error[E0433]: failed to resolve: could not find `PlatformError` in `error`
   --> src/syscalls/linux/mod.rs:390:31
    |
390 |                 crate::error::PlatformError::Linux(
    |                               ^^^^^^^^^^^^^ could not find `PlatformError` in `error`

error[E0433]: failed to resolve: could not find `PlatformError` in `error`
   --> src/syscalls/linux/mod.rs:895:31
    |
895 |                 crate::error::PlatformError::Linux(
    |                               ^^^^^^^^^^^^^ could not find `PlatformError` in `error`

error[E0433]: failed to resolve: could not find `PlatformError` in `error`
   --> src/syscalls/linux/mod.rs:920:31
    |
920 |                 crate::error::PlatformError::Linux(
    |                               ^^^^^^^^^^^^^ could not find `PlatformError` in `error`

error[E0433]: failed to resolve: could not find `PlatformError` in `error`
   --> src/syscalls/linux/mod.rs:929:31
    |
929 |                 crate::error::PlatformError::Linux(
    |                               ^^^^^^^^^^^^^ could not find `PlatformError` in `error`

error[E0433]: failed to resolve: could not find `PlatformError` in `error`
   --> src/syscalls/linux/mod.rs:980:31
    |
980 |                 crate::error::PlatformError::Linux(
    |                               ^^^^^^^^^^^^^ could not find `PlatformError` in `error`

error[E0433]: failed to resolve: could not find `LinuxEbpfSyscallInterceptor` in `ebpf`
  --> src/syscalls/mod.rs:54:42
   |
54 |                 Ok(Box::new(linux::ebpf::LinuxEbpfSyscallInterceptor::new()?))
   |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find `LinuxEbpfSyscallInterceptor` in `ebpf`

error[E0433]: failed to resolve: could not find `PlatformError` in `error`
  --> src/syscalls/mod.rs:57:35
   |
57 |                     crate::error::PlatformError::Linux(
   |                                   ^^^^^^^^^^^^^ could not find `PlatformError` in `error`

error[E0407]: method `process` is not a member of trait `EventProcessor`
   --> src/core/processor/pipeline.rs:83:5
    |
83  | /     async fn process(&self, event: TraceEvent) -> Result<Option<TraceEvent>> {
84  | |         trace!("Processing event: {}", event.id);
85  | |         
86  | |         // Apply normalizer
...   |
127 | |         Ok(Some(event))
128 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process_batch` is not a member of trait `EventProcessor`
   --> src/core/processor/pipeline.rs:130:5
    |
130 | /     async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
131 | |         trace!("Processing batch of {} events", events.len());
132 | |         
133 | |         let mut result = Vec::with_capacity(events.len());
...   |
142 | |         Ok(result)
143 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process` is not a member of trait `EventProcessor`
   --> src/core/processor/normalizer.rs:122:5
    |
122 | /     async fn process(&self, mut event: TraceEvent) -> Result<Option<TraceEvent>> {
123 | |         self.normalize_syscall(&mut event)?;
124 | |         debug!("Normalized event: {}", event.id);
125 | |         Ok(Some(event))
126 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process_batch` is not a member of trait `EventProcessor`
   --> src/core/processor/normalizer.rs:128:5
    |
128 | /     async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
129 | |         let mut result = Vec::with_capacity(events.len());
130 | |         
131 | |         for mut event in events {
...   |
137 | |         Ok(result)
138 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process` is not a member of trait `EventProcessor`
   --> src/core/processor/enricher.rs:572:5
    |
572 | /     async fn process(&self, mut event: TraceEvent) -> Result<Option<TraceEvent>> {
573 | |         self.enrich_event(&mut event).await?;
574 | |         debug!("Enriched event: {}", event.id);
575 | |         Ok(Some(event))
576 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process_batch` is not a member of trait `EventProcessor`
   --> src/core/processor/enricher.rs:578:5
    |
578 | /     async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
579 | |         let mut result = Vec::with_capacity(events.len());
580 | |         
581 | |         for mut event in events {
...   |
587 | |         Ok(result)
588 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process` is not a member of trait `EventProcessor`
   --> src/core/processor/filter.rs:269:5
    |
269 | /     async fn process(&self, event: TraceEvent) -> Result<Option<TraceEvent>> {
270 | |         if self.matches(&event) {
271 | |             debug!("Event {} passed filter", event.id);
272 | |             Ok(Some(event))
...   |
276 | |         }
277 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process_batch` is not a member of trait `EventProcessor`
   --> src/core/processor/filter.rs:279:5
    |
279 | /     async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
280 | |         let mut result = Vec::with_capacity(events.len());
281 | |         
282 | |         for event in events {
...   |
288 | |         Ok(result)
289 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process` is not a member of trait `EventProcessor`
   --> src/core/processor/correlator.rs:441:5
    |
441 | /     async fn process(&self, event: TraceEvent) -> Result<Option<TraceEvent>> {
442 | |         // Correlate event
443 | |         let correlated_events = self.correlate_event(&event).await?;
444 | |         
...   |
459 | |         }
460 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0407]: method `process_batch` is not a member of trait `EventProcessor`
   --> src/core/processor/correlator.rs:462:5
    |
462 | /     async fn process_batch(&self, events: Vec<TraceEvent>) -> Result<Vec<TraceEvent>> {
463 | |         let mut result = Vec::with_capacity(events.len());
464 | |         
465 | |         for event in events {
...   |
478 | |         Ok(result)
479 | |     }
    | |_____^ not a member of trait `EventProcessor`

error[E0433]: failed to resolve: use of undeclared crate or module `elasticsearch`
   --> src/core/exporter/elasticsearch.rs:164:21
    |
164 |             .exists(elasticsearch::indices::IndicesExistsParts::Index(&[&self.config.index]))
    |                     ^^^^^^^^^^^^^ use of undeclared crate or module `elasticsearch`

error[E0433]: failed to resolve: use of undeclared crate or module `elasticsearch`
   --> src/core/exporter/elasticsearch.rs:175:25
    |
175 |                 .create(elasticsearch::indices::IndicesCreateParts::Index(&self.config.index));
    |                         ^^^^^^^^^^^^^ use of undeclared crate or module `elasticsearch`

error[E0412]: cannot find type `Duration` in this scope
   --> src/core/buffer/circular.rs:207:57
    |
207 |     pub fn next_batch(&self, max_count: usize, timeout: Duration) -> Vec<TraceEvent> {
    |                                                         ^^^^^^^^ not found in this scope
    |
help: consider importing one of these items
    |
8   + use chrono::Duration;
    |
8   + use core::time::Duration;
    |
8   + use crate::core::buffer::Duration;
    |
8   + use std::time::Duration;
    |
      and 1 other candidate

error[E0433]: failed to resolve: use of undeclared type `Instant`
   --> src/core/buffer/circular.rs:209:26
    |
209 |         let start_time = Instant::now();
    |                          ^^^^^^^ use of undeclared type `Instant`
    |
help: consider importing one of these items
    |
8   + use std::time::Instant;
    |
8   + use tokio::time::Instant;
    |

error[E0433]: failed to resolve: use of undeclared type `Duration`
   --> src/core/buffer/circular.rs:214:81
    |
214 |             let remaining = timeout.checked_sub(start_time.elapsed()).unwrap_or(Duration::from_secs(0));
    |                                                                                 ^^^^^^^^ use of undeclared type `Duration`
    |
help: consider importing one of these items
    |
8   + use chrono::Duration;
    |
8   + use core::time::Duration;
    |
8   + use crate::core::buffer::Duration;
    |
8   + use std::time::Duration;
    |
      and 1 other candidate

error[E0412]: cannot find type `Duration` in this scope
   --> src/core/buffer/circular.rs:414:60
    |
414 |     async fn next_batch(&self, max_events: usize, timeout: Duration) -> Result<Vec<TraceEvent>> {
    |                                                            ^^^^^^^^ not found in this scope
    |
help: consider importing one of these items
    |
8   + use chrono::Duration;
    |
8   + use core::time::Duration;
    |
8   + use crate::core::buffer::Duration;
    |
8   + use std::time::Duration;
    |
      and 1 other candidate

error[E0532]: expected tuple struct or tuple variant, found unit variant `EventType::Syscall`
  --> src/core/processor/normalizer.rs:61:16
   |
61 |         if let EventType::Syscall(syscall_name) = &event.event_type {
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
  ::: src/core/events.rs:47:5
   |
47 |     Syscall,
   |     ------- `EventType::Syscall` defined here
   |
help: use this syntax instead
   |
61 |         if let EventType::Syscall = &event.event_type {
   |                ~~~~~~~~~~~~~~~~~~
help: consider importing one of these items instead
   |
8  + use crate::EventData::Syscall;
   |
8  + use crate::analysis::EventData::Syscall;
   |
8  + use crate::core::ImportedEventData::Syscall;
   |
help: if you import `Syscall`, refer to it directly
   |
61 -         if let EventType::Syscall(syscall_name) = &event.event_type {
61 +         if let Syscall(syscall_name) = &event.event_type {
   |

error[E0412]: cannot find type `Receiver` in this scope
   --> src/ebpf/programs/fileless_detector.rs:166:28
    |
166 |     event_receiver: Option<Receiver<EbpfEvent>>,
    |                            ^^^^^^^^ not found in this scope
    |
help: consider importing one of these items
    |
8   + use core::ops::Receiver;
    |
8   + use crate::ebpf::Receiver;
    |
8   + use std::ops::Receiver;
    |
8   + use std::sync::mpsc::Receiver;
    |
      and 5 other candidates

warning: unused imports: `EventData`, `EventType`
  --> src/platforms/linux/mod.rs:10:39
   |
10 | use crate::core::{Tracer, TraceEvent, EventType, EventData, TracerStats};
   |                                       ^^^^^^^^^  ^^^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `SyscallEvent`, `Syscall`
  --> src/platforms/linux/mod.rs:14:23
   |
14 | use crate::syscalls::{Syscall, SyscallEvent};
   |                       ^^^^^^^  ^^^^^^^^^^^^

warning: unused import: `std::process::Command`
 --> src/platforms/linux/perf.rs:9:5
  |
9 | use std::process::Command;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Error`
  --> src/platforms/linux/perf.rs:10:28
   |
10 | use crate::error::{Result, Error};
   |                            ^^^^^

warning: unused imports: `PathBuf`, `Path`
 --> src/platforms/linux/proc.rs:8:17
  |
8 | use std::path::{Path, PathBuf};
  |                 ^^^^  ^^^^^^^

warning: unused imports: `Read`, `self`
  --> src/platforms/linux/proc.rs:10:15
   |
10 | use std::io::{self, Read};
   |               ^^^^  ^^^^

warning: unused imports: `EventData`, `EventType`
 --> src/platforms/windows/mod.rs:8:39
  |
8 | use crate::core::{Tracer, TraceEvent, EventType, EventData, TracerStats};
  |                                       ^^^^^^^^^  ^^^^^^^^^

warning: unused imports: `SyscallEvent`, `Syscall`
  --> src/platforms/windows/mod.rs:12:23
   |
12 | use crate::syscalls::{Syscall, SyscallEvent};
   |                       ^^^^^^^  ^^^^^^^^^^^^

warning: unused import: `Error`
 --> src/platforms/windows/process.rs:8:28
  |
8 | use crate::error::{Result, Error};
  |                            ^^^^^

warning: unused import: `Error`
 --> src/platforms/windows/registry.rs:8:28
  |
8 | use crate::error::{Result, Error};
  |                            ^^^^^

warning: unused imports: `Header`, `ProgramHeader`, `Sym`, `Symtab`
 --> src/binary/elf/mod.rs:9:24
  |
9 | use goblin::elf::{Elf, Header, ProgramHeader, SectionHeader, Symtab, Sym};
  |                        ^^^^^^  ^^^^^^^^^^^^^                 ^^^^^^  ^^^

warning: unused import: `options::ParseOptions`
 --> src/binary/pe/mod.rs:9:59
  |
9 | use goblin::pe::{PE, header, section_table::SectionTable, options::ParseOptions};
  |                                                           ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Read`
  --> src/binary/mod.rs:24:21
   |
24 | use std::io::{self, Read};
   |                     ^^^^

warning: unused import: `Mmap`
  --> src/binary/mod.rs:25:15
   |
25 | use memmap2::{Mmap, MmapOptions};
   |               ^^^^

warning: unused import: `std::sync::Arc`
  --> src/syscalls/common/filter.rs:10:5
   |
10 | use std::sync::Arc;
   |     ^^^^^^^^^^^^^^

warning: unused imports: `Duration`, `SystemTime`
 --> src/syscalls/analysis/mod.rs:9:17
  |
9 | use std::time::{Duration, SystemTime};
  |                 ^^^^^^^^  ^^^^^^^^^^

warning: unused imports: `debug`, `error`, `info`, `warn`
  --> src/syscalls/analysis/mod.rs:10:11
   |
10 | use log::{debug, error, info, warn};
   |           ^^^^^  ^^^^^  ^^^^  ^^^^

warning: unused imports: `SyscallParameter`, `Syscall`
  --> src/syscalls/analysis/mod.rs:13:31
   |
13 | use crate::syscalls::common::{Syscall, SyscallEvent, SyscallCategory, SyscallParameter, SyscallParameterValue};
   |                               ^^^^^^^                                 ^^^^^^^^^^^^^^^^

warning: unused imports: `ParameterDirection`, `SyscallParameterValue`
  --> src/syscalls/linux/mod.rs:19:51
   |
19 |     SyscallEvent, SyscallCategory, SyscallFilter, SyscallParameterValue,
   |                                                   ^^^^^^^^^^^^^^^^^^^^^
20 |     ParameterDirection, Syscall, init_syscall_registry, register_syscall,
   |     ^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug`, `warn`
  --> src/syscalls/linux/ebpf.rs:13:11
   |
13 | use log::{debug, error, info, warn};
   |           ^^^^^               ^^^^

warning: unused import: `get_syscall`
  --> src/syscalls/linux/ebpf.rs:19:5
   |
19 |     get_syscall, get_syscall_category, get_syscall_name,
   |     ^^^^^^^^^^^

warning: unused imports: `debug`, `error`, `warn`
 --> src/syscalls/linux/syscall_table.rs:8:11
  |
8 | use log::{debug, error, info, warn};
  |           ^^^^^  ^^^^^        ^^^^

warning: unused import: `std::fmt`
  --> src/syscalls/mod.rs:23:5
   |
23 | use std::fmt;
   |     ^^^^^^^^

warning: unused import: `TraceEvent`
 --> src/core/mod.rs:9:69
  |
9 |     EventType as ImportedEventType, EventData as ImportedEventData, TraceEvent, EventSeverity,
  |                                                                     ^^^^^^^^^^

warning: unused import: `EventBuffer`
  --> src/core/mod.rs:17:5
   |
17 |     EventBuffer, BufferStats,
   |     ^^^^^^^^^^^

warning: unused import: `EventProcessor`
  --> src/core/mod.rs:25:5
   |
25 |     EventProcessor, PipelineProcessor, PipelineProcessorConfig,
   |     ^^^^^^^^^^^^^^

warning: unused import: `EventExporter`
  --> src/core/mod.rs:35:5
   |
35 |     EventExporter, ElasticsearchExporter, ElasticsearchConfig,
   |     ^^^^^^^^^^^^^

warning: unused import: `std::fmt`
 --> src/core/events.rs:9:5
  |
9 | use std::fmt;
  |     ^^^^^^^^

warning: unused import: `Duration`
  --> src/core/events.rs:11:17
   |
11 | use std::time::{Duration, SystemTime, UNIX_EPOCH};
   |                 ^^^^^^^^

warning: unused import: `crate::syscalls::SyscallEvent`
  --> src/core/events.rs:18:5
   |
18 | use crate::syscalls::SyscallEvent;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src/core/buffer/circular.rs:8:5
  |
8 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `Error`
  --> src/core/buffer/circular.rs:13:20
   |
13 | use crate::error::{Error, Result};
   |                    ^^^^^

warning: unused import: `Arc`
 --> src/core/buffer/memory.rs:9:17
  |
9 | use std::sync::{Arc, Mutex, Condvar};
  |                 ^^^

warning: unused import: `std::time::Duration`
  --> src/core/buffer/mod.rs:16:5
   |
16 | use std::time::Duration;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src/core/processor/normalizer.rs:9:5
  |
9 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `trace`
  --> src/core/processor/enricher.rs:11:18
   |
11 | use log::{debug, trace};
   |                  ^^^^^

warning: unused import: `std::sync::Arc`
 --> src/core/processor/filter.rs:8:5
  |
8 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `Error`
  --> src/core/processor/filter.rs:14:20
   |
14 | use crate::error::{Error, Result};
   |                    ^^^^^

warning: unused import: `trace`
  --> src/core/processor/correlator.rs:12:18
   |
12 | use log::{debug, trace};
   |                  ^^^^^

warning: unused import: `uuid::Uuid`
  --> src/core/processor/correlator.rs:15:5
   |
15 | use uuid::Uuid;
   |     ^^^^^^^^^^

warning: unused import: `trace`
  --> src/core/processor/parallel.rs:10:18
   |
10 | use log::{debug, trace, warn};
   |                  ^^^^^

warning: unused imports: `debug`, `error`, `warn`
  --> src/core/processor/plugin.rs:10:11
   |
10 | use log::{debug, error, info, warn};
   |           ^^^^^  ^^^^^        ^^^^

warning: unused imports: `debug`, `warn`
 --> src/core/exporter/file.rs:8:11
  |
8 | use log::{debug, error, info, warn};
  |           ^^^^^               ^^^^

warning: unused import: `serde_json::json`
 --> src/core/exporter/file.rs:9:5
  |
9 | use serde_json::json;
  |     ^^^^^^^^^^^^^^^^

warning: unused import: `warn`
  --> src/core/collector.rs:11:31
   |
11 | use log::{debug, error, info, warn};
   |                               ^^^^

warning: unused import: `crate::error::Result`
 --> src/analysis/mod.rs:9:5
  |
9 | use crate::error::Result;
  |     ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/analysis/patterns.rs:7:5
  |
7 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `SyscallCategory`
 --> src/analysis/patterns.rs:9:32
  |
9 | use crate::syscalls::{Syscall, SyscallCategory};
  |                                ^^^^^^^^^^^^^^^

warning: unused imports: `SyscallCategory`, `Syscall`
  --> src/analysis/reports.rs:10:23
   |
10 | use crate::syscalls::{Syscall, SyscallCategory};
   |                       ^^^^^^^  ^^^^^^^^^^^^^^^

warning: unused import: `PatternMatcher`
  --> src/analysis/reports.rs:11:40
   |
11 | use super::patterns::{BehaviorPattern, PatternMatcher, Severity};
   |                                        ^^^^^^^^^^^^^^

warning: unused import: `Syscall`
 --> src/analysis/statistics.rs:9:23
  |
9 | use crate::syscalls::{Syscall, SyscallCategory};
  |                       ^^^^^^^

warning: unused import: `thiserror::Error`
  --> src/error.rs:10:5
   |
10 | use thiserror::Error;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `ElasticsearchConfig`
  --> src/logging/mod.rs:13:46
   |
13 | pub use elasticsearch::{ElasticsearchLogger, ElasticsearchConfig}; 
   |                                              ^^^^^^^^^^^^^^^^^^^

warning: unused imports: `SystemTime`, `UNIX_EPOCH`
  --> src/ebpf/mod.rs:12:27
   |
12 | use std::time::{Duration, SystemTime, UNIX_EPOCH};
   |                           ^^^^^^^^^^  ^^^^^^^^^^

warning: unused import: `warn`
  --> src/ebpf/mod.rs:13:31
   |
13 | use log::{debug, error, info, warn};
   |                               ^^^^

warning: unused imports: `Receiver`, `channel`
  --> src/ebpf/lsm/mod.rs:10:31
   |
10 | use std::sync::mpsc::{Sender, Receiver, channel};
   |                               ^^^^^^^^  ^^^^^^^

warning: unused imports: `debug`, `warn`
  --> src/ebpf/lsm/mod.rs:13:11
   |
13 | use log::{debug, error, info, warn};
   |           ^^^^^               ^^^^

warning: unused import: `ProgramType`
  --> src/ebpf/lsm/mod.rs:15:41
   |
15 | use crate::ebpf::{EbpfError, EbpfEvent, ProgramType};
   |                                         ^^^^^^^^^^^

warning: unused imports: `MapType`, `TypedMapBuilder`, `TypedMap`
  --> src/ebpf/lsm/mod.rs:16:30
   |
16 | use crate::ebpf::maps::{Map, MapType, MapBuilder, TypedMap, TypedMapBuilder};
   |                              ^^^^^^^              ^^^^^^^^  ^^^^^^^^^^^^^^^

warning: unused imports: `Receiver`, `channel`
  --> src/ebpf/xdp/mod.rs:10:31
   |
10 | use std::sync::mpsc::{Sender, Receiver, channel};
   |                               ^^^^^^^^  ^^^^^^^

warning: unused imports: `Ipv4Addr`, `Ipv6Addr`
  --> src/ebpf/xdp/mod.rs:13:24
   |
13 | use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
   |                        ^^^^^^^^  ^^^^^^^^

warning: unused imports: `debug`, `warn`
  --> src/ebpf/xdp/mod.rs:14:11
   |
14 | use log::{debug, error, info, warn};
   |           ^^^^^               ^^^^

warning: unused imports: `MapType`, `TypedMapBuilder`, `TypedMap`
  --> src/ebpf/xdp/mod.rs:17:30
   |
17 | use crate::ebpf::maps::{Map, MapType, MapBuilder, TypedMap, TypedMapBuilder};
   |                              ^^^^^^^              ^^^^^^^^  ^^^^^^^^^^^^^^^

warning: unused imports: `Receiver`, `channel`
  --> src/ebpf/tracepoints/mod.rs:10:31
   |
10 | use std::sync::mpsc::{Sender, Receiver, channel};
   |                               ^^^^^^^^  ^^^^^^^

warning: unused imports: `debug`, `warn`
  --> src/ebpf/tracepoints/mod.rs:14:11
   |
14 | use log::{debug, error, info, warn};
   |           ^^^^^               ^^^^

warning: unused imports: `MapType`, `TypedMapBuilder`, `TypedMap`
  --> src/ebpf/tracepoints/mod.rs:17:30
   |
17 | use crate::ebpf::maps::{Map, MapType, MapBuilder, TypedMap, TypedMapBuilder};
   |                              ^^^^^^^              ^^^^^^^^  ^^^^^^^^^^^^^^^

warning: unused imports: `Receiver`, `channel`
  --> src/ebpf/uprobes/mod.rs:10:31
   |
10 | use std::sync::mpsc::{Sender, Receiver, channel};
   |                               ^^^^^^^^  ^^^^^^^

warning: unused imports: `debug`, `warn`
  --> src/ebpf/uprobes/mod.rs:15:11
   |
15 | use log::{debug, error, info, warn};
   |           ^^^^^               ^^^^

warning: unused imports: `MapType`, `TypedMapBuilder`, `TypedMap`
  --> src/ebpf/uprobes/mod.rs:18:30
   |
18 | use crate::ebpf::maps::{Map, MapType, MapBuilder, TypedMap, TypedMapBuilder};
   |                              ^^^^^^^              ^^^^^^^^  ^^^^^^^^^^^^^^^

warning: unused imports: `debug`, `warn`
  --> src/ebpf/programs/mod.rs:13:11
   |
13 | use log::{debug, error, info, warn};
   |           ^^^^^               ^^^^

warning: unused import: `ProgramType`
  --> src/ebpf/programs/mod.rs:15:41
   |
15 | use crate::ebpf::{EbpfError, EbpfEvent, ProgramType};
   |                                         ^^^^^^^^^^^

warning: unused imports: `debug`, `error`, `warn`
  --> src/ebpf/programs/fileless.rs:11:11
   |
11 | use log::{debug, error, info, warn};
   |           ^^^^^  ^^^^^        ^^^^

warning: unused import: `std::path::Path`
 --> src/ebpf/programs/fileless_detector.rs:8:5
  |
8 | use std::path::Path;
  |     ^^^^^^^^^^^^^^^

warning: unused import: `Duration`
  --> src/ebpf/programs/fileless_detector.rs:13:17
   |
13 | use std::time::{Duration, SystemTime, UNIX_EPOCH};
   |                 ^^^^^^^^

warning: unused import: `warn`
  --> src/ebpf/programs/fileless_detector.rs:14:31
   |
14 | use log::{debug, error, info, warn};
   |                               ^^^^

warning: unused imports: `debug`, `warn`
  --> src/ebpf/loader.rs:15:11
   |
15 | use log::{debug, error, info, warn};
   |           ^^^^^               ^^^^

warning: unused imports: `MapFlags`, `Map`, `OpenObject`, `ProgramAttachType`, `ProgramType`, `Program`
  --> src/ebpf/loader.rs:20:5
   |
20 |     Map, MapFlags, Object, ObjectBuilder, OpenObject, Program, ProgramAttachType, ProgramType,
   |     ^^^  ^^^^^^^^                         ^^^^^^^^^^  ^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^

warning: unused import: `std::marker::PhantomData`
 --> src/ebpf/maps/mod.rs:9:5
  |
9 | use std::marker::PhantomData;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `Arc`, `Mutex`
  --> src/ebpf/maps/mod.rs:13:17
   |
13 | use std::sync::{Arc, Mutex};
   |                 ^^^  ^^^^^

warning: unused import: `std::collections::HashMap`
  --> src/ebpf/maps/mod.rs:14:5
   |
14 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug`, `error`, `info`, `warn`
  --> src/ebpf/maps/mod.rs:15:11
   |
15 | use log::{debug, error, info, warn};
   |           ^^^^^  ^^^^^  ^^^^  ^^^^

warning: unused imports: `Serialize`, `de::DeserializeOwned`
  --> src/ebpf/maps/mod.rs:16:13
   |
16 | use serde::{Serialize, de::DeserializeOwned};
   |             ^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `thiserror::Error`
  --> src/ebpf/maps/mod.rs:17:5
   |
17 | use thiserror::Error;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `MapIter`
 --> src/ebpf/maps/typed.rs:9:60
  |
9 | use super::{Map, MapConfig, MapType, MapError, MapBuilder, MapIter};
  |                                                            ^^^^^^^

warning: unused import: `std::mem`
 --> src/ebpf/maps/hash.rs:8:5
  |
8 | use std::mem;
  |     ^^^^^^^^

warning: unused import: `std::sync::Mutex`
  --> src/ebpf/maps/lrumap.rs:12:5
   |
12 | use std::sync::Mutex;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `ElasticsearchConfig`
  --> src/ebpf/maps/lrumap.rs:14:43
   |
14 | use crate::logging::{ElasticsearchLogger, ElasticsearchConfig};
   |                                           ^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src/ebpf/maps/lrumap.rs:16:5
   |
16 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AtomicU64`, `Ordering`
  --> src/ebpf/maps/percpumap.rs:10:25
   |
10 | use std::sync::atomic::{AtomicU64, Ordering};
   |                         ^^^^^^^^^  ^^^^^^^^

warning: unused imports: `debug`, `error`, `info`
  --> src/ebpf/maps/percpumap.rs:12:11
   |
12 | use log::{debug, warn, info, error};
   |           ^^^^^        ^^^^  ^^^^^

warning: unused import: `super::hash::HashFunction`
  --> src/ebpf/maps/percpumap.rs:16:5
   |
16 | use super::hash::HashFunction;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `MapType`
 --> src/ebpf/maps/typed_optimized_percpu_map.rs:7:22
  |
7 | use libbpf_rs::{Map, MapType};
  |                      ^^^^^^^

warning: unused import: `crate::ebpf::maps::MapError`
 --> src/ebpf/maps/typed_optimized_percpu_map.rs:9:5
  |
9 | use crate::ebpf::maps::MapError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `MapFlags`
   --> src/ebpf/maps/typed_optimized_percpu_map.rs:263:29
    |
263 |             use libbpf_rs::{MapFlags, MapType as LibbpfMapType};
    |                             ^^^^^^^^

warning: unused import: `std::io`
   --> src/ebpf/maps/typed_optimized_percpu_map.rs:295:17
    |
295 |             use std::io;
    |                 ^^^^^^^

warning: unused import: `std::ops::Add`
  --> src/ebpf/maps/percpu_elasticsearch.rs:10:5
   |
10 | use std::ops::Add;
   |     ^^^^^^^^^^^^^

warning: unused import: `crate::ebpf::maps::MapError`
  --> src/hookers/lsm_hooker.rs:10:5
   |
10 | use crate::ebpf::maps::MapError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug`, `error`, `warn`
 --> src/hookers/lsm_percpu_hooker.rs:7:11
  |
7 | use log::{debug, error, info, warn};
  |           ^^^^^  ^^^^^        ^^^^

warning: unused import: `std::collections::HashMap`
 --> src/hookers/xdp_hooker.rs:1:5
  |
1 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `Ipv4Addr`, `Ipv6Addr`
 --> src/hookers/xdp_hooker.rs:2:24
  |
2 | use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
  |                        ^^^^^^^^  ^^^^^^^^

warning: unused imports: `debug`, `info`, `warn`
 --> src/hookers/xdp_hooker.rs:8:11
  |
8 | use log::{debug, error, info, warn};
  |           ^^^^^         ^^^^  ^^^^

warning: unused import: `crate::ebpf::maps::MapError`
  --> src/hookers/xdp_hooker.rs:11:5
   |
11 | use crate::ebpf::maps::MapError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug`, `error`, `warn`
 --> src/hookers/xdp_percpu_hooker.rs:7:11
  |
7 | use log::{debug, error, info, warn};
  |           ^^^^^  ^^^^^        ^^^^

warning: unused import: `info`
 --> src/hookers/tracepoint_hooker.rs:7:25
  |
7 | use log::{debug, error, info, warn};
  |                         ^^^^

warning: unused import: `crate::ebpf::maps::MapError`
  --> src/hookers/tracepoint_hooker.rs:10:5
   |
10 | use crate::ebpf::maps::MapError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug`, `error`, `warn`
 --> src/hookers/tracepoint_percpu_hooker.rs:7:11
  |
7 | use log::{debug, error, info, warn};
  |           ^^^^^  ^^^^^        ^^^^

warning: unused import: `info`
 --> src/hookers/uprobe_hooker.rs:7:25
  |
7 | use log::{debug, error, info, warn};
  |                         ^^^^

warning: unused import: `crate::ebpf::maps::MapError`
  --> src/hookers/uprobe_hooker.rs:10:5
   |
10 | use crate::ebpf::maps::MapError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug`, `error`, `warn`
 --> src/hookers/uprobe_percpu_hooker.rs:7:11
  |
7 | use log::{debug, error, info, warn};
  |           ^^^^^  ^^^^^        ^^^^

warning: unused import: `Deserialize`
 --> src/elasticsearch/mod.rs:6:24
  |
6 | use serde::{Serialize, Deserialize};
  |                        ^^^^^^^^^^^

warning: unused imports: `info`, `warn`
 --> src/elasticsearch/mod.rs:8:25
  |
8 | use log::{debug, error, info, warn};
  |                         ^^^^  ^^^^

error[E0782]: trait objects must include the `dyn` keyword
  --> src/syscalls/common/filter.rs:41:22
   |
41 |     platform: Option<Platform>,
   |                      ^^^^^^^^
   |
help: add `dyn` keyword before this trait
   |
41 |     platform: Option<dyn Platform>,
   |                      +++

error[E0782]: trait objects must include the `dyn` keyword
  --> src/syscalls/common/mod.rs:33:19
   |
33 |     pub platform: Platform,
   |                   ^^^^^^^^
   |
help: add `dyn` keyword before this trait
   |
33 |     pub platform: dyn Platform,
   |                   +++

Some errors have detailed explanations: E0106, E0252, E0255, E0407, E0412, E0432, E0433, E0532, E0782.
For more information about an error, try `rustc --explain E0106`.
warning: `inspector_gadget` (lib) generated 111 warnings
The following warnings were emitted during compilation:

warning: inspector_gadget@0.2.0: Skipping eBPF compilation as requested by SKIP_EBPF_COMPILATION

error: could not compile `inspector_gadget` (lib) due to 64 previous errors; 111 warnings emitted
```
❌ Failed

