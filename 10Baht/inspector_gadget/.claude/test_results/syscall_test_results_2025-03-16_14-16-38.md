# SyscallInterceptor Test Results - 2025-03-16_14-16-38
Test run started at: Sun 16 Mar 14:16:38 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Kernel: 6.9.3-76060903-generic

---


## Command: setup_python_env (2025-03-16_14-16-38)
**Description**: Setting up Python virtual environment with required packages

**Results**:
```
Setting up Python virtual environment...
Requirement already satisfied: rich in ./.venv/lib/python3.10/site-packages (13.9.4)
Requirement already satisfied: typer in ./.venv/lib/python3.10/site-packages (0.15.2)
Requirement already satisfied: pydantic in ./.venv/lib/python3.10/site-packages (2.10.6)
Collecting pyyaml
  Downloading PyYAML-6.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (751 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 751.2/751.2 KB 6.1 MB/s eta 0:00:00
Requirement already satisfied: typing-extensions<5.0,>=4.0.0 in ./.venv/lib/python3.10/site-packages (from rich) (4.12.2)
Requirement already satisfied: markdown-it-py>=2.2.0 in ./.venv/lib/python3.10/site-packages (from rich) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in ./.venv/lib/python3.10/site-packages (from rich) (2.19.1)
Requirement already satisfied: click>=8.0.0 in ./.venv/lib/python3.10/site-packages (from typer) (8.1.8)
Requirement already satisfied: shellingham>=1.3.0 in ./.venv/lib/python3.10/site-packages (from typer) (1.5.4)
Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.10/site-packages (from pydantic) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.10/site-packages (from pydantic) (2.27.2)
Requirement already satisfied: mdurl~=0.1 in ./.venv/lib/python3.10/site-packages (from markdown-it-py>=2.2.0->rich) (0.1.2)
Installing collected packages: pyyaml
Successfully installed pyyaml-6.0.2
Python virtual environment setup complete.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:52 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py status (2025-03-16_14-16-38)
**Description**: Check the status of the pimp.py tool and Vagrant VMs

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:53 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py setup (2025-03-16_14-16-38)
**Description**: Set up the environment for testing

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:53 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py inject --hooker hooker_pill (2025-03-16_14-16-38)
**Description**: Inject hooker_pills into Vagrant VMs

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:54 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test (2025-03-16_14-16-38)
**Description**: Run the SyscallInterceptor tests

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:55 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_high_throughput (2025-03-16_14-16-38)
**Description**: Run the high throughput test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:55 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_percpu_maps_integration --features percpu_maps (2025-03-16_14-16-38)
**Description**: Run the Per-CPU Maps integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:56 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_xdp_integration --features xdp (2025-03-16_14-16-38)
**Description**: Run the XDP integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:57 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_tracepoint_integration --features tracepoint (2025-03-16_14-16-38)
**Description**: Run the Tracepoint integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:58 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py cleanup (2025-03-16_14-16-38)
**Description**: Clean up the environment after testing

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 35, in <module>
    from src.vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 42, in <module>
    from vagrant_manager import VagrantManager
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/src/vagrant_manager.py", line 10, in <module>
    import grpc
ModuleNotFoundError: No module named 'grpc'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:16:58 CET 2025

---


## Summary
Test run completed at: Sun 16 Mar 14:16:58 CET 2025

### Test Results
- Passed: 10
- Failed: 0
0
