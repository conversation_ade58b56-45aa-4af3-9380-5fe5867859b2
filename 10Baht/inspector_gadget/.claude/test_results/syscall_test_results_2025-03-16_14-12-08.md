# SyscallInterceptor Test Results - 2025-03-16_14-12-08
Test run started at: Sun 16 Mar 14:12:08 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Kernel: 6.9.3-76060903-generic

---


## Command: pimp.py status (2025-03-16_14-12-08)
**Description**: Check the status of the pimp.py tool and Vagrant VMs

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:09 CET 2025

---


## Command: pimp.py setup (2025-03-16_14-12-08)
**Description**: Set up the environment for testing

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:09 CET 2025

---


## Command: pimp.py inject --hooker hooker_pill (2025-03-16_14-12-08)
**Description**: Inject hooker_pills into Vagrant VMs

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:10 CET 2025

---


## Command: pimp.py run --test syscall_interceptor_test (2025-03-16_14-12-08)
**Description**: Run the SyscallInterceptor tests

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:11 CET 2025

---


## Command: pimp.py run --test syscall_interceptor_test::test_high_throughput (2025-03-16_14-12-08)
**Description**: Run the high throughput test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:12 CET 2025

---


## Command: pimp.py run --test syscall_interceptor_test::test_percpu_maps_integration --features percpu_maps (2025-03-16_14-12-08)
**Description**: Run the Per-CPU Maps integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:12 CET 2025

---


## Command: pimp.py run --test syscall_interceptor_test::test_xdp_integration --features xdp (2025-03-16_14-12-08)
**Description**: Run the XDP integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:13 CET 2025

---


## Command: pimp.py run --test syscall_interceptor_test::test_tracepoint_integration --features tracepoint (2025-03-16_14-12-08)
**Description**: Run the Tracepoint integration test

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:13 CET 2025

---


## Command: pimp.py cleanup (2025-03-16_14-12-08)
**Description**: Clean up the environment after testing

**Results**:
```
Traceback (most recent call last):
  File "/home/<USER>/dev/inspector_gadget/tools/pimp/pimp.py", line 13, in <module>
    from rich.console import Console
ModuleNotFoundError: No module named 'rich'
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:13:14 CET 2025

---


## Summary
Test run completed at: Sun 16 Mar 14:13:14 CET 2025

### Test Results
- Passed: 9
- Failed: 0
0
