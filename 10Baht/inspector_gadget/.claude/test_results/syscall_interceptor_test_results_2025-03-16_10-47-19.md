# SyscallInterceptor Test Results - 2025-03-16_10-47-19
Test run started at: Sun 16 Mar 10:47:19 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Rust: 
- Cargo: 
- Kernel: 6.9.3-76060903-generic

---

- Elasticsearch: Not available at http://localhost:9200

---


## Test: test_high_throughput (2025-03-16_10-47-19)
**Description**: Tests the SyscallInterceptor's ability to handle high throughput of syscalls

**Features**: 

**Environment**:
- ELASTICSEARCH_URL: http://localhost:9200
- ELASTICSEARCH_INDEX: syscall_events_test
- RUST_LOG: debug

**Results**:
```
./scripts/run_syscall_interceptor_tests.sh: line 36: cargo: command not found
✅ PASSED
```

**Completed at**: Sun 16 Mar 10:47:19 CET 2025

---


## Test: test_elasticsearch_integration (2025-03-16_10-47-19)
**Description**: Tests the SyscallInterceptor's integration with Elasticsearch

**Features**: elasticsearch

**Results**: SKIPPED - Elasticsearch not available

---


## Test: test_percpu_maps_integration (2025-03-16_10-47-19)
**Description**: Tests the SyscallInterceptor's integration with Per-CPU Maps

**Features**: percpu_maps

**Environment**:
- ELASTICSEARCH_URL: http://localhost:9200
- ELASTICSEARCH_INDEX: syscall_events_test
- RUST_LOG: debug

**Results**:
```
./scripts/run_syscall_interceptor_tests.sh: line 36: cargo: command not found
✅ PASSED
```

**Completed at**: Sun 16 Mar 10:47:19 CET 2025

---


## Test: test_percpu_maps_with_elasticsearch (2025-03-16_10-47-19)
**Description**: Tests the SyscallInterceptor's integration with Per-CPU Maps and Elasticsearch

**Features**: elasticsearch,percpu_maps

**Results**: SKIPPED - Elasticsearch not available

---


## Test: test_xdp_integration (2025-03-16_10-47-19)
**Description**: Tests the SyscallInterceptor's integration with XDP

**Features**: elasticsearch,xdp

**Environment**:
- ELASTICSEARCH_URL: http://localhost:9200
- ELASTICSEARCH_INDEX: syscall_events_test
- RUST_LOG: debug

**Results**:
```
./scripts/run_syscall_interceptor_tests.sh: line 36: cargo: command not found
✅ PASSED
```

**Completed at**: Sun 16 Mar 10:47:19 CET 2025

---


## Test: test_tracepoint_integration (2025-03-16_10-47-19)
**Description**: Tests the SyscallInterceptor's integration with kernel tracepoints

**Features**: elasticsearch,tracepoint

**Environment**:
- ELASTICSEARCH_URL: http://localhost:9200
- ELASTICSEARCH_INDEX: syscall_events_test
- RUST_LOG: debug

**Results**:
```
./scripts/run_syscall_interceptor_tests.sh: line 36: cargo: command not found
✅ PASSED
```

**Completed at**: Sun 16 Mar 10:47:19 CET 2025

---


## Summary
Test run completed at: Sun 16 Mar 10:47:19 CET 2025

