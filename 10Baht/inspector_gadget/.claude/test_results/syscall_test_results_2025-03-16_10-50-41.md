# SyscallInterceptor Test Results - 2025-03-16_10-50-41
Test run started at: Sun 16 Mar 10:50:41 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Kernel: 6.9.3-76060903-generic

---


## Command: cargo test --test syscall_interceptor_test test_high_throughput -- --nocapture (2025-03-16_10-50-41)
**Description**: Tests the SyscallInterceptor's ability to handle high throughput of syscalls

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/lrumap_pill_test.rs` found to be present in multiple build targets:
  * `bin` target `lrumap_pill_test`
  * `integration-test` target `lrumap_pill_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/xdp_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `xdp_percpu_hooker_test`
  * `integration-test` target `xdp_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/uprobe_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `uprobe_percpu_hooker_test`
  * `integration-test` target `uprobe_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/tracepoint_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `tracepoint_percpu_hooker_test`
  * `integration-test` target `tracepoint_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/lsm_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `lsm_percpu_hooker_test`
  * `integration-test` target `lsm_percpu_hooker_test`
   Compiling inspector_gadget v0.2.0 (/home/<USER>/dev/inspector_gadget)
   Compiling chrono v0.4.40
   Compiling libbpf-rs v0.20.1
   Compiling anyhow v1.0.97
The following warnings were emitted during compilation:

warning: inspector_gadget@0.2.0: Compiling eBPF program: src/ebpf/bpf/fileless_detector.bpf.c

error: failed to run custom build command for `inspector_gadget v0.2.0 (/home/<USER>/dev/inspector_gadget)`
note: To improve backtraces for build dependencies, set the CARGO_PROFILE_TEST_BUILD_OVERRIDE_DEBUG=true environment variable to enable debug information generation.

Caused by:
  process didn't exit successfully: `/home/<USER>/dev/inspector_gadget/target/debug/build/inspector_gadget-834cd9b3cd230a64/build-script-build` (exit status: 101)
  --- stdout
  cargo:rerun-if-changed=src/ebpf/bpf/
  cargo:warning=Compiling eBPF program: src/ebpf/bpf/fileless_detector.bpf.c

  --- stderr
  thread 'main' panicked at build.rs:36:14:
  Failed to compile eBPF program: Os { code: 2, kind: NotFound, message: "No such file or directory" }
  stack backtrace:
     0: rust_begin_unwind
               at /rustc/82e1608dfa6e0b5569232559e3d385fea5a93112/library/std/src/panicking.rs:645:5
     1: core::panicking::panic_fmt
               at /rustc/82e1608dfa6e0b5569232559e3d385fea5a93112/library/core/src/panicking.rs:72:14
     2: core::result::unwrap_failed
               at /rustc/82e1608dfa6e0b5569232559e3d385fea5a93112/library/core/src/result.rs:1653:5
     3: core::result::Result<T,E>::expect
     4: build_script_build::main
     5: core::ops::function::FnOnce::call_once
  note: Some details are omitted, run with `RUST_BACKTRACE=full` for a verbose backtrace.
warning: build failed, waiting for other jobs to finish...
✅ PASSED
```

**Completed at**: Sun 16 Mar 10:51:08 CET 2025

---


## Command: cargo test --test syscall_interceptor_test test_percpu_maps_integration --features percpu_maps -- --nocapture (2025-03-16_10-50-41)
**Description**: Tests the SyscallInterceptor's integration with Per-CPU Maps

**Results**:
```
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels' does not exist, ignoring
warning: Nix search path entry '/nix/var/nix/profiles/per-user/cvr/channels/nixpkgs' does not exist, ignoring
error: file 'nixpkgs' was not found in the Nix search path (add it using $NIX_PATH or -I)

       at «string»:1:9:

            1| (import <nixpkgs> {}).bashInteractive
             |         ^
will use bash from your environment
Inspector Gadget Nix development environment loaded!
Rust version: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo version: cargo 1.75.0 (1d8b05cdd 2023-11-20)
LD_LIBRARY_PATH: /nix/store/6kbrc4ca98srlfpgyaayl2q9zpg1gys6-gcc-14-20241116-lib/lib:/nix/store/cmpyglinc9xl9pr4ymx8akl286ygl64x-glibc-2.40-66/lib:/nix/store/5djq7mrpqv8kzn2xi22y5d8ww7rsix82-glibc-2.40-66-dev/lib:/nix/store/mn3p4hf44iwkijkwjpbsjs82bwg1xfap-zlib-1.3.1/lib:/nix/store/zlngkjx33w1lj8f0w32swlbzwl838s5v-zlib-1.3.1-dev/lib:/nix/store/99cizfcv53x3mjb15cx7kmdxsglp17hc-openssl-3.4.1/lib:/nix/store/9lq31bk3niis5sfaaqxw8z9rxnh0x3fj-openssl-3.4.1-dev/lib:/nix/store/mwyfwxrjpwk3wwcfjs6d2gijaf54z4wd-libbpf-1.5.0/lib:/nix/store/7vp6n2vr8614k9x8lk5k47y94g7968fy-elfutils-0.192/lib:/tmp/.mount_CursorVOD8gs/usr/lib/:/tmp/.mount_CursorVOD8gs/usr/lib32/:/tmp/.mount_CursorVOD8gs/usr/lib64/:/tmp/.mount_CursorVOD8gs/lib/:/tmp/.mount_CursorVOD8gs/lib/i386-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/x86_64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib/aarch64-linux-gnu/:/tmp/.mount_CursorVOD8gs/lib32/:/tmp/.mount_CursorVOD8gs/lib64/:
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/uprobe_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `uprobe_percpu_hooker_test`
  * `integration-test` target `uprobe_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/lrumap_pill_test.rs` found to be present in multiple build targets:
  * `bin` target `lrumap_pill_test`
  * `integration-test` target `lrumap_pill_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/tracepoint_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `tracepoint_percpu_hooker_test`
  * `integration-test` target `tracepoint_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/lsm_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `lsm_percpu_hooker_test`
  * `integration-test` target `lsm_percpu_hooker_test`
warning: /home/<USER>/dev/inspector_gadget/Cargo.toml: file `/home/<USER>/dev/inspector_gadget/tests/xdp_percpu_hooker_test.rs` found to be present in multiple build targets:
  * `bin` target `xdp_percpu_hooker_test`
  * `integration-test` target `xdp_percpu_hooker_test`
error: none of the selected packages contains these features: percpu_maps
✅ PASSED
```

**Completed at**: Sun 16 Mar 10:51:21 CET 2025

---


## Command: cargo test --test syscall_interceptor_test test_xdp_integration --features xdp -- --nocapture (2025-03-16_10-50-41)
**Description**: Tests the SyscallInterceptor's integration with XDP

**Results**:
```
