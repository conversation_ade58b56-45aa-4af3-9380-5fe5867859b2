# SyscallInterceptor Test Results - 2025-03-16_14-18-44
Test run started at: Sun 16 Mar 14:18:45 CET 2025

## System Information
- OS: Linux ryohei 6.9.3-76060903-generic #202405300957~1738770968~22.04~d5f7c84 SMP PREEMPT_DYNAMIC Wed F x86_64 x86_64 x86_64 GNU/Linux
- Kernel: 6.9.3-76060903-generic

---


## Command: setup_python_env (2025-03-16_14-18-44)
**Description**: Setting up Python virtual environment with required packages

**Results**:
```
Setting up Python virtual environment...
Requirement already satisfied: rich in ./.venv/lib/python3.10/site-packages (13.9.4)
Requirement already satisfied: typer in ./.venv/lib/python3.10/site-packages (0.15.2)
Requirement already satisfied: pydantic in ./.venv/lib/python3.10/site-packages (2.10.6)
Requirement already satisfied: pyyaml in ./.venv/lib/python3.10/site-packages (6.0.2)
Requirement already satisfied: grpcio in ./.venv/lib/python3.10/site-packages (1.71.0)
Requirement already satisfied: grpcio-tools in ./.venv/lib/python3.10/site-packages (1.71.0)
Collecting jinja2
  Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 134.9/134.9 KB 1.2 MB/s eta 0:00:00
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in ./.venv/lib/python3.10/site-packages (from rich) (2.19.1)
Requirement already satisfied: typing-extensions<5.0,>=4.0.0 in ./.venv/lib/python3.10/site-packages (from rich) (4.12.2)
Requirement already satisfied: markdown-it-py>=2.2.0 in ./.venv/lib/python3.10/site-packages (from rich) (3.0.0)
Requirement already satisfied: click>=8.0.0 in ./.venv/lib/python3.10/site-packages (from typer) (8.1.8)
Requirement already satisfied: shellingham>=1.3.0 in ./.venv/lib/python3.10/site-packages (from typer) (1.5.4)
Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.10/site-packages (from pydantic) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.10/site-packages (from pydantic) (2.27.2)
Requirement already satisfied: protobuf<6.0dev,>=5.26.1 in ./.venv/lib/python3.10/site-packages (from grpcio-tools) (5.29.3)
Requirement already satisfied: setuptools in ./.venv/lib/python3.10/site-packages (from grpcio-tools) (59.6.0)
Collecting MarkupSafe>=2.0
  Downloading MarkupSafe-3.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (20 kB)
Requirement already satisfied: mdurl~=0.1 in ./.venv/lib/python3.10/site-packages (from markdown-it-py>=2.2.0->rich) (0.1.2)
Installing collected packages: MarkupSafe, jinja2
Successfully installed MarkupSafe-3.0.2 jinja2-3.1.6
Python virtual environment setup complete.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:18:59 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py status (2025-03-16_14-18-44)
**Description**: Check the status of the pimp.py tool and Vagrant VMs

**Results**:
```

░▒▓███████▓▒░░▒▓█▓▒░▒▓██████████████▓▒░░▒▓███████▓▒░  
░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ 
░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ 
░▒▓███████▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓███████▓▒░  
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        

Performance & Integration Management Platform
Version 0.1.0

╭────────────────────╮
│ Checking VM Status │
╰────────────────────╯
⠋ Checking VM status
                  VM Status                   
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━┓
┃ VM Name                      ┃ Status      ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━┩
│ lsm_glitchykimsu             │ running     │
│ xdp_wigglyside               │ poweroff    │
│ tracepoint_glitchydarkhydrus │ not_created │
│ uprobe_fluffyta505           │ not_created │
└──────────────────────────────┴─────────────┘
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:00 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py setup (2025-03-16_14-18-44)
**Description**: Set up the environment for testing

**Results**:
```

░▒▓███████▓▒░░▒▓█▓▒░▒▓██████████████▓▒░░▒▓███████▓▒░  
░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ 
░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ 
░▒▓███████▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓███████▓▒░  
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        

Performance & Integration Management Platform
Version 0.1.0

╭─────────────────────╮
│ Setting up test VMs │
╰─────────────────────╯
VMs provisioned successfully
Provisioning VMs... ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100% 0:00:00
⠋ Provisioning VMs
            VM Setup Results            
┏━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━┓
┃ VM Name                ┃ Status      ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━┩
│ lsm_sleepyteamt        │ provisioned │
│ xdp_dramaticmuddywater │ provisioned │
│ tracepoint_clumsyteamt │ provisioned │
│ uprobe_chaoticapt39    │ provisioned │
└────────────────────────┴─────────────┘
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:02 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py inject --hooker hooker_pill (2025-03-16_14-18-44)
**Description**: Inject hooker_pills into Vagrant VMs

**Results**:
```
Usage: pimp.py [OPTIONS] COMMAND [ARGS]...
Try 'pimp.py -h' for help.

Error: No such command 'inject'.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:03 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test (2025-03-16_14-18-44)
**Description**: Run the SyscallInterceptor tests

**Results**:
```
Usage: pimp.py [OPTIONS] COMMAND [ARGS]...
Try 'pimp.py -h' for help.

Error: No such command 'run'.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:04 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_high_throughput (2025-03-16_14-18-44)
**Description**: Run the high throughput test

**Results**:
```
Usage: pimp.py [OPTIONS] COMMAND [ARGS]...
Try 'pimp.py -h' for help.

Error: No such command 'run'.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:05 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_percpu_maps_integration --features percpu_maps (2025-03-16_14-18-44)
**Description**: Run the Per-CPU Maps integration test

**Results**:
```
Usage: pimp.py [OPTIONS] COMMAND [ARGS]...
Try 'pimp.py -h' for help.

Error: No such command 'run'.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:06 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_xdp_integration --features xdp (2025-03-16_14-18-44)
**Description**: Run the XDP integration test

**Results**:
```
Usage: pimp.py [OPTIONS] COMMAND [ARGS]...
Try 'pimp.py -h' for help.

Error: No such command 'run'.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:07 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py run --test syscall_interceptor_test::test_tracepoint_integration --features tracepoint (2025-03-16_14-18-44)
**Description**: Run the Tracepoint integration test

**Results**:
```
Usage: pimp.py [OPTIONS] COMMAND [ARGS]...
Try 'pimp.py -h' for help.

Error: No such command 'run'.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:08 CET 2025

---


## Command: source .venv/bin/activate && python tools/pimp/pimp.py cleanup (2025-03-16_14-18-44)
**Description**: Clean up the environment after testing

**Results**:
```

░▒▓███████▓▒░░▒▓█▓▒░▒▓██████████████▓▒░░▒▓███████▓▒░  
░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ 
░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ 
░▒▓███████▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓███████▓▒░  
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        
░▒▓█▓▒░      ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        

Performance & Integration Management Platform
Version 0.1.0

╭───────────────────────╮
│ Cleaning up test VMs  │
╰───────────────────────╯
VMs cleaned up successfully
Cleaning up VMs... ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100% 0:00:00
⠋ Cleaning up VMs
                             VM Cleanup Results                             
┏━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━┓
┃ VM Name               ┃ Initial Status ┃ Final Status ┃ Result           ┃
┡━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━┩
│ lsm_b0uncyw1nnt1      │ running        │ running      │ No change needed │
│ xdp_n1nj@d@rkhydrus   │ poweroff       │ poweroff     │ Success          │
│ tracepoint_sleepykims │ not_created    │ not_created  │ Success          │
│ uprobe_discomuddyw    │ not_created    │ not_created  │ Success          │
└───────────────────────┴────────────────┴──────────────┴──────────────────┘
3/4 VMs successfully cleaned up.
Some VMs may still be running or in an inconsistent state.
✅ PASSED
```

**Completed at**: Sun 16 Mar 14:19:09 CET 2025

---


## Summary
Test run completed at: Sun 16 Mar 14:19:09 CET 2025

### Test Results
- Passed: 10
- Failed: 0
0
