# Version Increment Guidelines for Inspector Gadget

This document provides guidance on how to manage version numbers for the Inspector Gadget project, emphasizing a more conservative approach to version increments.

## Version Number Format

Inspector Gadget follows the [Semantic Versioning](https://semver.org/) format: `MAJOR.MINOR.PATCH`

## Conservative Version Increment Strategy

### Patch Version (0.0.X)

- **Increment for**: 
  - Bug fixes
  - Small performance improvements
  - Documentation updates
  - Non-functional changes
  - Individual feature implementations that don't change APIs

- **Example**: Updating from 0.0.1 to 0.0.2 after fixing a bug in the eBPF event collection

### Minor Version (0.X.0)

- **Increment only when**:
  - A complete set of related features is fully implemented
  - New functionality is added in a backward-compatible manner
  - A significant milestone in the roadmap is reached
  - Multiple related patch versions have accumulated (typically 5-10 patches)

- **Example**: Updating from 0.0.x to 0.1.0 only after the entire eBPF hooking system is complete, including all planned hooker types and threat detectors

- **Approval Required**: Minor version increments require explicit approval from the project lead

### Major Version (X.0.0)

- **Increment only when**:
  - Breaking API changes are introduced
  - Major architectural changes are implemented
  - The product reaches a significant maturity milestone
  - The project is considered production-ready

- **Example**: Updating from 0.x.x to 1.0.0 only when Inspector Gadget is considered stable and ready for production use

- **Approval Required**: Major version increments require team consensus and explicit approval from the project lead

## Development Workflow

1. Most work should be done under patch versions (0.0.X)
2. Features should be grouped into logical sets for minor version increments
3. Before proposing a minor version increment, ensure that:
   - All related features are complete
   - Tests are passing
   - Documentation is updated
   - The change represents a meaningful milestone

## Pre-release Versions

For features in development, use pre-release version suffixes:

- **Alpha**: `0.0.1-alpha.1` - Early development, unstable
- **Beta**: `0.0.1-beta.1` - Feature complete but not fully tested
- **RC**: `0.0.1-rc.1` - Release candidate, potentially shippable

## Version Increment Examples

### Appropriate Patch Increment (0.0.X)
- Implementing a single eBPF program type
- Adding a new threat detector
- Fixing bugs in existing functionality
- Improving performance of event collection
- Updating documentation

### Appropriate Minor Increment (0.X.0)
- Completing the entire eBPF hooking system
- Finishing all planned threat detectors
- Implementing the complete event collection system
- Adding a new major subsystem that doesn't break existing APIs

### Appropriate Major Increment (X.0.0)
- Changing core APIs in a backward-incompatible way
- Complete architectural redesign
- First production-ready release

## Requesting Version Increments

For minor version increments (0.X.0), create a proposal that includes:

1. Summary of completed features
2. Justification for the version increment
3. Test results and performance metrics
4. Documentation updates
5. Future roadmap

Submit this proposal to the project lead for review and approval.

## Current Version Strategy

- Start with version 0.0.1
- Increment patch versions for most feature work
- Reserve 0.1.0 for when the complete eBPF hooking system is implemented
- Reserve 1.0.0 for when Inspector Gadget is production-ready 