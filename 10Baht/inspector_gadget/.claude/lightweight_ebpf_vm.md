# Lightweight eBPF Testing VM

## Overview

This document proposes using a lightweight Alpine Linux-based virtual machine for testing eBPF components in the Inspector Gadget project, particularly the hooker pill components. The current testing environment uses Ubuntu VMs which are relatively large and resource-intensive.

## Proposed Solution

Create a lightweight VM using Alpine Linux with minimal required dependencies for eBPF testing.

### Vagrantfile Example

```ruby
Vagrant.configure("2") do |config|
  config.vm.box = "alpine/alpine64"
  config.vm.provider "virtualbox" do |vb|
    vb.memory = "1024"
    vb.cpus = 1
  end
  
  config.vm.provision "shell", inline: <<-SHELL
    # Install necessary dependencies for eBPF
    apk update
    apk add build-base linux-headers elfutils-dev clang llvm bcc bcc-dev

    # Create a test directory
    mkdir -p /opt/hooker_test
    
    # Copy test scripts
    cp /vagrant/tools/pimp/tests/*.sh /opt/hooker_test/
    chmod +x /opt/hooker_test/*.sh
  SHELL
end
```

## Requirements

For the lightweight VM to work correctly with eBPF testing, the kernel needs:

1. BPF JIT compilation enabled
2. Kprobes/uprobes support
3. Tracepoints support
4. XDP (Express Data Path) support if testing XDP hooks

## Benefits

1. **Reduced Resource Usage**: Alpine Linux uses significantly less disk space (~150MB) compared to Ubuntu (~1GB+)
2. **Faster Boot Times**: Lightweight VMs boot faster, reducing test cycle time
3. **Lower Memory Footprint**: Alpine can run with as little as 512MB RAM
4. **Simplified Environment**: Fewer unnecessary packages means less potential for conflicts

## Implementation Plan

1. Create a prototype lightweight VM using Alpine Linux
2. Verify eBPF functionality works correctly in this environment
3. Adapt existing test scripts to work with the Alpine environment
4. Update the pimp tool to support this new VM type
5. Add documentation on how to use the lightweight VM option

## Potential Challenges

1. Kernel version compatibility - ensure Alpine has a kernel with sufficient eBPF support
2. Library dependencies - may need to compile some tools from source
3. Script compatibility - some scripts may assume Ubuntu-specific paths or commands 