# Hooker Pill VM Testing Locations

This document provides an overview of the testing locations and components for the Hooker Pill VM injection framework.

## Directory Structure

```
tools/pimp/
├── pimp.py                 # Main PIMP tool for VM management
├── run.sh                  # Wrapper script for common operations
├── inject_pill_test.py     # Simple VM detection and pill injection script
├── hooker_pill_test.py     # Comprehensive end-to-end testing framework
├── run_pill_test.py        # Quick and reliable testing script
├── src/
│   ├── vagrant_manager.py  # Core VM management functionality
│   ├── test_runner.py      # Test execution engine
│   ├── reporter.py         # Test result reporting
│   └── tests/
│       ├── test_hooker_pill_lifecycle.py  # Pill lifecycle testing
│       ├── test_hooker_pill_manager.py    # Pill manager component testing
│       ├── test_vm_timed_integration.py   # VM integration with timing
│       └── test_timing_manager.py         # Test timing framework
└── config/
    └── default_config.yaml # Default VM configurations
```

## Testing Scripts

### 1. inject_pill_test.py

**Purpose**: Quick debugging and verification of VM detection and pill injection.

**Key Features**:
- Simple interface for validating pill injection
- Reports VM status and availability
- Tracks test type mapping
- Shows performance metrics

**Usage Example**:
```bash
python inject_pill_test.py --ping-count 3
python inject_pill_test.py --vm-name iptesting_lsm_jumpyapt29
```

### 2. run_pill_test.py

**Purpose**: Reliable script for pill injection, monitoring, and cleanup.

**Key Features**:
- Auto-detection of running VMs
- Support for specific VM names
- Automatic VM registration in internal dictionaries
- Comprehensive error handling
- Performance metrics and reporting

**Usage Example**:
```bash
python run_pill_test.py --test-type lsm --ping-count 5
python run_pill_test.py --remove-pill
```

### 3. hooker_pill_test.py

**Purpose**: Complete end-to-end testing framework for all hooker types.

**Key Features**:
- VM creation and provisioning
- Support for multiple test types
- Detailed test result reporting
- Resource cleanup
- CI/CD integration capabilities

**Usage Example**:
```bash
python hooker_pill_test.py --test-type all
python hooker_pill_test.py --test-type lsm,xdp --skip-cleanup
python hooker_pill_test.py --vm-name iptesting_lsm_jumpyapt29 --test-type lsm
```

## Core Components

### 1. VagrantManager (src/vagrant_manager.py)

The central component that handles VM management, pill injection, and monitoring. Key functionalities:

- VM creation and provisioning
- VM status monitoring
- Pill injection and verification
- Pill health monitoring
- Resource cleanup

### 2. Test Runner (src/test_runner.py)

Responsible for executing tests with proper isolation and reporting:

- Test case discovery and execution
- Test isolation
- Timing management
- Result collection

### 3. Reporter (src/reporter.py)

Generates formatted test reports:

- Console output
- JSON report generation
- Test summary statistics
- Performance metrics

## Testing Locations

### 1. Unit Tests

Located in `src/tests/`, these validate individual components:

- `test_hooker_pill_manager.py`: Tests the pill management functionality
- `test_hooker_pill_lifecycle.py`: Tests the complete pill lifecycle
- `test_vm_timed_integration.py`: Tests VM integration with timing optimization

### 2. Integration Tests

The main scripts themselves (`inject_pill_test.py`, `run_pill_test.py`, `hooker_pill_test.py`) serve as integration tests that validate the complete system.

### 3. VM Configurations

VM configurations are stored in `config/default_config.yaml` and define:

- VM resources (memory, CPU)
- Box images
- Network configuration
- Provisioning scripts

## Test Data Collection

Test results are collected in several formats:

1. **Console Output**: Real-time progress and results
2. **Log Files**: Detailed logging in `pimp.log`
3. **Test Results Directory**: Structured test results for analysis
4. **VM Logs**: Collected from VMs for debugging

## Known Limitations

1. VM naming inconsistency between status command and pill injection
2. Occasional timing issues with VM provisioning
3. Need for manual cleanup in some failure scenarios 