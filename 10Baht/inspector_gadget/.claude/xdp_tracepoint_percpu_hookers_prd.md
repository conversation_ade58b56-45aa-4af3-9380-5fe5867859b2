# Product Requirements Document: XDP and Tracepoint Hookers with Per-CPU Maps Integration

## Overview

This document outlines the requirements for integrating the optimized Per-CPU Map implementation with XDP (eXpress Data Path) and Tracepoint hookers in the Inspector Gadget project. This integration will significantly improve performance, reduce contention, and provide better monitoring capabilities for high-throughput network events (XDP) and kernel tracepoints.

## Background

The Inspector Gadget project currently has several hooking mechanisms for monitoring different aspects of the system:
- LSM (Linux Security Module) hookers for security events
- Uprobe hookers for user-space function instrumentation
- XDP hookers for network packet processing
- Tracepoint hookers for kernel tracepoints

The LSM and Uprobe hookers have already been integrated with Per-CPU Maps, resulting in improved performance and monitoring capabilities. This PRD focuses on extending this integration to XDP and Tracepoint hookers, which typically deal with high-frequency events where the performance benefits of Per-CPU Maps are especially valuable.

## Goals

1. Improve performance of XDP and Tracepoint hookers by reducing contention with Per-CPU Maps
2. Provide efficient aggregation of monitoring data across CPUs
3. Enable detailed per-CPU statistics for network events and kernel tracepoints
4. Integrate with the existing Elasticsearch monitoring solution
5. Maintain backward compatibility with existing APIs
6. Ensure proper error handling and recovery mechanisms

## Non-Goals

1. Modify the core functionality of XDP or Tracepoint hookers
2. Redesign the existing Map abstractions
3. Support for non-Linux platforms (as XDP is Linux-specific)
4. Real-time streaming of per-CPU data (batch processing is sufficient)

## Requirements

### XDP Hooker with Per-CPU Maps

#### Functional Requirements

1. Create an XdpPerCpuHooker struct that uses TypedOptimizedPerCpuMap for storing statistics
2. Implement methods for tracking network packet events:
   - Packets received
   - Packets dropped
   - Packets redirected
   - Packets passed to the network stack
3. Implement efficient aggregation of statistics across CPUs
4. Ensure thread-safety for concurrent event processing
5. Provide methods for querying statistics (both per-CPU and aggregated)
6. Maintain compatibility with the existing XDP hooker API

#### Performance Requirements

1. Per-CPU event processing should add less than 50ns overhead per event
2. Support for at least 10 million packets per second per CPU
3. Aggregation operations should complete in under 1ms
4. Resource usage should scale linearly with the number of CPUs

### Tracepoint Hooker with Per-CPU Maps

#### Functional Requirements

1. Create a TracepointPerCpuHooker struct that uses TypedOptimizedPerCpuMap for storing statistics
2. Support arbitrary tracepoint events with custom data structures
3. Implement efficient aggregation of statistics across CPUs
4. Ensure thread-safety for concurrent event processing
5. Provide methods for querying statistics (both per-CPU and aggregated)
6. Maintain compatibility with the existing Tracepoint hooker API

#### Performance Requirements

1. Per-CPU event processing should add less than 50ns overhead per event
2. Support for at least 5 million events per second per CPU
3. Aggregation operations should complete in under 1ms
4. Resource usage should scale linearly with the number of CPUs

### Monitoring Integration

1. Extend the Elasticsearch integration to support XDP and Tracepoint statistics
2. Create Kibana dashboard templates for XDP and Tracepoint monitoring
3. Implement time-series data collection for XDP and Tracepoint events
4. Ensure proper indexing and querying capabilities for the collected data

### Testing and Validation

1. Create unit tests for XDP and Tracepoint Per-CPU hookers
2. Implement benchmark tests to validate performance requirements
3. Create example applications demonstrating the integration
4. Develop stress tests for high-throughput scenarios

## User Experience

### XDP Hooker Example

```rust
use inspector_gadget::hookers::XdpPerCpuHooker;

// Create a new XDP Per-CPU hooker
let xdp_hooker = XdpPerCpuHooker::new("eth0")?;

// Start intercepting network packets
xdp_hooker.start_intercepting()?;

// Later, query statistics
let stats = xdp_hooker.get_statistics()?;
println!("Packets received: {}", stats.packets_received);
println!("Packets dropped: {}", stats.packets_dropped);
println!("Packets redirected: {}", stats.packets_redirected);
println!("Packets passed: {}", stats.packets_passed);

// Query per-CPU statistics
for (cpu_id, cpu_stats) in stats.per_cpu_stats {
    println!("CPU {}: Packets received: {}", cpu_id, cpu_stats.packets_received);
}

// Stop intercepting
xdp_hooker.stop_intercepting()?;
```

### Tracepoint Hooker Example

```rust
use inspector_gadget::hookers::TracepointPerCpuHooker;

// Create a new Tracepoint Per-CPU hooker
let tracepoint_hooker = TracepointPerCpuHooker::new("sched", "sched_switch")?;

// Start intercepting tracepoint events
tracepoint_hooker.start_intercepting()?;

// Later, query statistics
let stats = tracepoint_hooker.get_statistics()?;
println!("Events processed: {}", stats.events_processed);
println!("Events dropped: {}", stats.events_dropped);

// Query per-CPU statistics
for (cpu_id, cpu_stats) in stats.per_cpu_stats {
    println!("CPU {}: Events processed: {}", cpu_id, cpu_stats.events_processed);
}

// Stop intercepting
tracepoint_hooker.stop_intercepting()?;
```

## Technical Design

### XDP Per-CPU Hooker

The XdpPerCpuHooker will be implemented as follows:

1. A struct that wraps an XDP eBPF program and a TypedOptimizedPerCpuMap for statistics
2. The eBPF program will determine the current CPU and update the corresponding entry in the Per-CPU Map
3. The hooker will provide methods for starting, stopping, and querying statistics
4. Statistics will be aggregated across CPUs when requested

### Tracepoint Per-CPU Hooker

The TracepointPerCpuHooker will be implemented as follows:

1. A struct that wraps a Tracepoint eBPF program and a TypedOptimizedPerCpuMap for statistics
2. The eBPF program will determine the current CPU and update the corresponding entry in the Per-CPU Map
3. The hooker will provide methods for starting, stopping, and querying statistics
4. Statistics will be aggregated across CPUs when requested

### Monitoring Integration

The monitoring integration will extend the existing Elasticsearch integration:

1. Add XDP and Tracepoint statistics to the exported data
2. Create Kibana dashboard templates for XDP and Tracepoint monitoring
3. Implement time-series data collection for XDP and Tracepoint events

## Timeline

1. **Week 1**: Implement XdpPerCpuHooker and base functionality
2. **Week 2**: Implement TracepointPerCpuHooker and base functionality
3. **Week 3**: Extend Elasticsearch integration and create Kibana dashboards
4. **Week 4**: Implement unit tests, benchmarks, and example applications
5. **Week 5**: Performance optimization and stress testing
6. **Week 6**: Documentation and final polish

## Success Metrics

1. XDP and Tracepoint hookers with Per-CPU Maps integration are fully implemented
2. Performance benchmarks meet or exceed requirements
3. Unit tests provide >90% code coverage
4. Elasticsearch integration is complete and functional
5. Example applications demonstrate the usage and benefits
6. Documentation is comprehensive and accessible

## Dependencies

1. Existing Per-CPU Map implementation
2. Existing XDP and Tracepoint hookers
3. Existing Elasticsearch integration
4. eBPF and libbpf-rs libraries
5. Kernel support for XDP and Tracepoints

## Risks and Mitigations

| Risk | Mitigation |
| ---- | ---------- |
| XDP and Tracepoint performance degradation | Extensive benchmarking during development to ensure no regression |
| Compatibility issues with existing APIs | Maintain backward compatibility and provide migration guides if needed |
| High memory usage with many CPUs | Optimize memory usage and provide configuration options |
| Kernel version compatibility issues | Document minimum kernel version requirements and provide graceful fallbacks |

## Open Questions

1. Should we prioritize XDP or Tracepoint integration first?
2. Do we need to support custom aggregation functions for specific use cases?
3. Should we provide a unified API for all Per-CPU hookers?
4. How should we handle hookers that span multiple interfaces or tracepoints?

## Appendix

### Glossary

- **XDP**: eXpress Data Path, a high-performance networking framework in the Linux kernel
- **Tracepoint**: A static hook in the Linux kernel for tracing and debugging
- **Per-CPU Map**: A data structure that avoids contention by having per-CPU data areas
- **Hooker**: In Inspector Gadget, a component that intercepts and processes events

### References

1. Inspector Gadget documentation
2. Per-CPU Maps PRD and implementation
3. XDP documentation (https://github.com/xdp-project/xdp-tutorial)
4. Linux Tracepoints documentation 