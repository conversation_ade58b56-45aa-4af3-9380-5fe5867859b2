feat(pimp): Enhance Vagrant VM provisioning with improved resource management

Implement comprehensive improvements to Pimp.py for better Vagrant VM
provisioning with a focus on resource management, error handling, and
system monitoring. This enhancement significantly improves the reliability
and efficiency of VM-based testing infrastructure.

Key improvements:
- Add intelligent resource management with system auto-detection
- Implement VM batching for efficient parallel provisioning
- Create robust error handling with retry logic and fallbacks
- Add system health monitoring and preflight checks
- Enhance Vagrantfile generation with optimized VM settings
- Update shell.nix with improved dependencies and helper functions
- Create enhanced run.sh with comprehensive error handling
- Improve configuration file with resource management settings

This change resolves previous issues with VM provisioning failures,
resource exhaustion, and inconsistent VM management, making the testing
infrastructure more robust and reliable across different environments.

Related: #11
Documentation: .claude/pimp_vagrant_integration_prd.md 