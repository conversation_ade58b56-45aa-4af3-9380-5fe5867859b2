# Shell.nix Fixes and Configuration Updates

This document tracks changes and fixes made to the various shell.nix files in the Inspector Gadget project.

## Summary of Changes

### Date: 2025-03-22

1. Fixed hooker_pill/shell.nix
2. Fixed tools/pimp/shell.nix
3. Standardized dependency management across shell.nix files

## hooker_pill/shell.nix Fixes

The hooker_pill/shell.nix file was previously empty (just a space character), causing nix-shell to fail. The file has been updated with a proper configuration that includes:

- Rust toolchain with cargo and rustc
- Essential libraries like openssl and libbpf
- Development tools including gcc and clang
- Testing tools like curl and jq

This ensures that the hooker_pill component can be built and tested in isolation.

### Previous (broken) content:
```
 
```

### New content:
```nix
let
  # Use a specific nixpkgs commit for stability
  nixpkgs = fetchTarball {
    url = "https://github.com/NixOS/nixpkgs/archive/nixos-23.11.tar.gz";
    sha256 = "sha256:1f5d2g1p6nfwycpmrnnmc2xmcszp804adp16knjvdkj8nz36y1fg";
  };
  pkgs = import nixpkgs {};
in

pkgs.mkShell {
  name = "hooker-pill-env";
  
  buildInputs = with pkgs; [
    # Rust
    rustc
    cargo
    
    # Essential libraries and tools for hooker_pill
    openssl
    libbpf
    pkg-config
    gcc
    clang
    
    # Basic tools
    curl
    jq
  ];

  shellHook = ''
    # Set up environment variables
    export RUST_BACKTRACE=1
    
    echo "========================================================"
    echo "Hooker Pill Development Environment"
    echo "========================================================"
    echo "Rust: $(rustc --version)"
    echo "GCC: $(gcc --version | head -n 1)"
    echo "========================================================"
  '';
}
```

## tools/pimp/shell.nix Fixes

The tools/pimp/shell.nix file had issues with local NIX_STORE and NIX_STATE_DIR paths which caused errors when running nix-shell. These settings have been removed, and the file has been simplified.

### Key changes:

- Removed problematic NIX_STATE_DIR and NIX_STORE settings
- Removed local nix directory references
- Maintained all dependencies and Python environment setup

This allows the pimp tool to be properly built and used for testing the hooker_pill injections.

## Standardization

All shell.nix files now follow a more consistent pattern:

1. Main project shell.nix - Contains comprehensive development environment
2. Component-specific shell.nix files - Contain minimal necessary dependencies
3. Consistent versioning - Using same nixpkgs release across all files

## Testing the Fixes

The fixes have been tested by:

1. Running `nix-shell` in each directory
2. Building the components
3. Running basic tests to ensure functionality

## Recommendations

1. Keep shell.nix files minimal but complete
2. Avoid local Nix store configurations 
3. Standardize on common nixpkgs versions
4. Document dependency requirements for each component 