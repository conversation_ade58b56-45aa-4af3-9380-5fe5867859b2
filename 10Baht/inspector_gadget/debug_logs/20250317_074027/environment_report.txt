=== System Information ===
Date: Mon 17 Mar 07:40:30 CET 2025
Hostname: 
Kernel: 6.1.36
Architecture: x86_64
Distribution: NixOS 22.11 (<PERSON><PERSON><PERSON>)

=== Nix Environment ===
Nix Version: Not found

=== eBPF Requirements ===
Kernel Headers (Traditional): Not found
Kernel Headers (Nix): -r--r--r-- 1 <USER> <GROUP> 215 Jan  1  1970 /nix/store/ml8qqhdgdicz91pb265661nzy9nna2gx-linux-headers-6.5/include/linux/version.h
BPF Syscall: 4898 symbols

=== Compiler Tools ===
Clang: clang version 16.0.6
LLVM Objcopy: llvm-objcopy, compatible with GNU objcopy
GCC: gcc (GCC) 12.3.0
Rust: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo: cargo 1.75.0 (1d8b05cdd 2023-11-20)

=== Network Interfaces ===
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN mode DEFAULT group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
2: enp5s0: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc mq state DOWN mode DEFAULT group default qlen 1000
    link/ether 60:45:cb:9d:89:7f brd ff:ff:ff:ff:ff:ff
3: enp7s0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP mode DEFAULT group default qlen 1000
    link/ether 04:42:1a:57:cc:c9 brd ff:ff:ff:ff:ff:ff
5: docker0: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc noqueue state DOWN mode DEFAULT group default 
    link/ether 02:42:79:ee:7f:e3 brd ff:ff:ff:ff:ff:ff
6: br-6950d612034f: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc noqueue state DOWN mode DEFAULT group default 
    link/ether 02:42:fe:40:68:b3 brd ff:ff:ff:ff:ff:ff
7: br-b3a7ff57a6b7: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc noqueue state DOWN mode DEFAULT group default 
    link/ether 02:42:99:8f:d2:3c brd ff:ff:ff:ff:ff:ff
8: br-f75064dd7d83: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc noqueue state DOWN mode DEFAULT group default 
    link/ether 02:42:64:88:1e:4e brd ff:ff:ff:ff:ff:ff
9: wlp9s0f3u2: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc mq state DOWN mode DORMANT group default qlen 1000
    link/ether 22:10:81:13:5c:6a brd ff:ff:ff:ff:ff:ff permaddr 74:da:38:04:f3:f1

=== Elasticsearch ===
Elasticsearch URL: http://localhost:9200
Status: error

=== Permissions ===
Current user: cvr
Groups: users nogroup
CAP_SYS_ADMIN: Not found

=== Resource Limits ===
real-time non-blocking time  (microseconds, -R) unlimited
core file size              (blocks, -c) unlimited
data seg size               (kbytes, -d) unlimited
scheduling priority                 (-e) 0
file size                   (blocks, -f) unlimited
pending signals                     (-i) 128011
max locked memory           (kbytes, -l) 8192
max memory size             (kbytes, -m) unlimited
open files                          (-n) 524288
pipe size                (512 bytes, -p) 8
POSIX message queues         (bytes, -q) 819200
real-time priority                  (-r) 0
stack size                  (kbytes, -s) 8192
cpu time                   (seconds, -t) unlimited
max user processes                  (-u) 128011
virtual memory              (kbytes, -v) unlimited
file locks                          (-x) unlimited

=== Python Environment ===
Python Version: Not found
Pip Version: Not found
Python Path: Not found

Python Modules:
Could not list Python modules
