=== System Information ===
Date: Mon 17 Mar 07:32:51 CET 2025
Hostname: 
Kernel: 6.1.36
Architecture: x86_64
Distribution: NixOS 22.11 (<PERSON><PERSON><PERSON>)

=== Nix Environment ===
Nix Version: Not found

=== eBPF Requirements ===
Kernel Headers: Not found
BPF Syscall: 4898 symbols

=== Compiler Tools ===
Clang: clang version 16.0.6
LLVM Objcopy: llvm-objcopy, compatible with GNU objcopy
GCC: gcc (GCC) 12.3.0
Rust: rustc 1.75.0 (82e1608df 2023-12-21)
Cargo: Not found

=== Network Interfaces ===
Could not list network interfaces

=== Elasticsearch ===
Elasticsearch URL: http://localhost:9200
Status: error

=== Permissions ===
Current user: cvr
Groups: users nogroup
CAP_SYS_ADMIN: Not found

=== Resource Limits ===
real-time non-blocking time  (microseconds, -R) unlimited
core file size              (blocks, -c) unlimited
data seg size               (kbytes, -d) unlimited
scheduling priority                 (-e) 0
file size                   (blocks, -f) unlimited
pending signals                     (-i) 128011
max locked memory           (kbytes, -l) 8192
max memory size             (kbytes, -m) unlimited
open files                          (-n) 524288
pipe size                (512 bytes, -p) 8
POSIX message queues         (bytes, -q) 819200
real-time priority                  (-r) 0
stack size                  (kbytes, -s) 8192
cpu time                   (seconds, -t) unlimited
max user processes                  (-u) 128011
virtual memory              (kbytes, -v) unlimited
file locks                          (-x) unlimited

=== Python Environment ===
Python Version: Not found
Pip Version: Not found
Python Path: Not found

Python Modules:
Could not list Python modules
