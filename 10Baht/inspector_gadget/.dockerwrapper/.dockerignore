# Git
.git
.gitignore
.github

# Docker
.dockerwrapper
.docker
docker-compose*.yml
Dockerfile*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.coverage.*
coverage.xml
*.cover

# Virtual Environment
.env
.venv
venv/
ENV/

# IDE
.idea
.vscode
*.swp
*.swo
.DS_Store

# Node
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Project specific
logs/
*.log
.cursor/
.claude/
docs/ 